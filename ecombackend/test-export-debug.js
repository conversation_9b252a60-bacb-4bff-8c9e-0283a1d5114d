const axios = require('axios');

async function testExportDebug() {
  console.log('🔍 Testing Export Generation Debug');
  console.log('==================================');

  try {
    // First, get the export template details
    console.log('\n1. Getting export template details...');
    const templateResponse = await axios.get('http://localhost:3000/api/universal-export/1');
    console.log('✅ Template found:', {
      id: templateResponse.data.data.id,
      name: templateResponse.data.data.name,
      format: templateResponse.data.data.format,
      fieldMappings: templateResponse.data.data.fieldMappings.length,
      products: templateResponse.data.data.products.length
    });

    // Test the export generation
    console.log('\n2. Testing export generation...');
    const exportResponse = await axios.get(
      'http://localhost:3000/api/universal-export/export/sample-csv-export-12345678',
      {
        responseType: 'stream',
        validateStatus: () => true // Accept any status code
      }
    );

    console.log('📊 Response details:');
    console.log('Status:', exportResponse.status);
    console.log('Headers:', exportResponse.headers);

    // Read the response data
    let responseData = '';
    exportResponse.data.on('data', chunk => {
      responseData += chunk;
    });

    exportResponse.data.on('end', () => {
      console.log('\n📄 Response content:');
      console.log(responseData.substring(0, 500)); // First 500 chars
      
      if (responseData.includes('Product ID')) {
        console.log('✅ Export generation successful - CSV headers found!');
      } else if (responseData.includes('isSuccess')) {
        console.log('❌ Export generation failed - JSON error response');
        try {
          const errorData = JSON.parse(responseData);
          console.log('Error details:', errorData);
        } catch (e) {
          console.log('Could not parse error response');
        }
      } else {
        console.log('⚠️ Unexpected response format');
      }
    });

    exportResponse.data.on('error', (error) => {
      console.error('❌ Stream error:', error);
    });

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.log('Response status:', error.response.status);
      console.log('Response data:', error.response.data);
    }
  }
}

testExportDebug();
