{"name": "pantoneclo", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:dev:poll": "nest start --watch --watchOptions.poll=1000", "start:debug": "nest start --debug --watch", "start:prod": "node dist/src/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "typeorm": "ts-node ./node_modules/typeorm/cli", "typeorm:create-migration": "npm run typeorm migration:create -- ./migrations/%npm_config_migration_name%", "typeorm:generate-migration": "npx typeorm-ts-node-commonjs migration:generate -d ./typeorm.config.ts ./migrations/%npm_config_name%", "typeorm:run-migrations": "npx typeorm-ts-node-commonjs migration:run -- -d ./typeorm.config.ts", "typeorm:revert-migrations": "npm run typeorm migration:revert -- -d ./typeorm.config.ts", "run-specific-migration": "bash run-migration.sh %MigrationFileName%", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@aws-sdk/client-s3": "^3.744.0", "@aws-sdk/s3-request-presigner": "^3.637.0", "@nestjs-modules/mailer": "^2.0.2", "@nestjs/axios": "^3.0.2", "@nestjs/cache-manager": "^3.0.0", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.2.2", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^10.2.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^10.3.3", "@nestjs/schedule": "^5.0.1", "@nestjs/swagger": "^7.1.17", "@nestjs/typeorm": "^10.0.1", "@sentry/nestjs": "^8.55.0", "@sentry/profiling-node": "^9.0.1", "@types/sharp": "^0.32.0", "axios": "^1.7.2", "basic-auth": "^2.0.1", "bcrypt": "^5.1.1", "cache-manager": "^6.4.0", "cache-manager-fs-hash": "^2.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "compression": "^1.7.4", "cookie-parser": "^1.4.7", "csurf": "^1.2.2", "dotenv": "^16.3.1", "ejs": "^3.1.10", "exceljs": "^4.4.0", "express-rate-limit": "^7.4.0", "form-data": "^4.0.2", "handlebars": "^4.7.8", "helmet": "^7.1.0", "html-pdf": "^3.0.1", "i18next": "^23.16.2", "i18next-fs-backend": "^2.3.2", "i18next-http-middleware": "^3.6.0", "iconv-lite": "^0.6.3", "mjml": "^4.15.3", "multer": "^1.4.5-lts.1", "multer-s3": "^3.0.1", "nodemailer": "^6.9.16", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "pdfkit": "^0.15.2", "pg": "^8.11.3", "puppeteer": "^22.8.2", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "sharp": "^0.33.2", "slugify": "^1.6.6", "socket.io": "^4.7.4", "stripe": "^15.7.0", "swagger-stats": "^0.99.7", "tmp": "^0.2.3", "typeorm": "^0.3.20", "xml2js": "^0.6.2"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.1.3", "@nestjs/testing": "^10.0.0", "@types/compression": "^1.7.5", "@types/csurf": "^1.11.5", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/multer": "^1.4.11", "@types/node": "^20.3.1", "@types/supertest": "^2.0.12", "@types/tmp": "^0.2.6", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "packageManager": "pnpm@10.12.1+sha512.f0dda8580f0ee9481c5c79a1d927b9164f2c478e90992ad268bbb2465a736984391d6333d2c327913578b2804af33474ca554ba29c04a8b13060a717675ae3ac"}