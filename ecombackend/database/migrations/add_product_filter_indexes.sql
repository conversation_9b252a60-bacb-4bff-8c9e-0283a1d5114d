-- Enhanced Product Filtering Performance Indexes
-- Run this migration to improve filter query performance

-- 1. JSONB GIN indexes for variant filtering
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_product_meta_variants_size 
ON product_meta USING GIN ((variants->'size'));

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_product_meta_variants_color 
ON product_meta USING GIN ((variants->'variantDetails'));

-- 2. Composite indexes for common filter combinations
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_product_meta_country_active 
ON product_meta (country_id, is_active) 
WHERE is_active = true AND deleted_at IS NULL;

-- 3. Product category composite index
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_product_category_composite 
ON product_category (category_id, product_id);

-- 4. Product brand and publish status
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_product_brand_publish_active 
ON product (brand_id, is_publish, is_active) 
WHERE is_publish = true AND is_active = true AND deleted_at IS NULL;

-- 5. Product sort order and ID for default ordering
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_product_sort_order_id 
ON product (sort_order ASC, id DESC) 
WHERE is_publish = true AND is_active = true AND deleted_at IS NULL;

-- 6. Product meta pricing for sorting
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_product_meta_pricing 
ON product_meta (unit_price, discount_price, country_id) 
WHERE is_active = true AND deleted_at IS NULL;

-- 7. Specific JSONB path indexes for better performance
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_product_meta_size_ids 
ON product_meta USING GIN (((variants->'size'->>'id')::int));

-- 8. Color extraction index (more complex but faster)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_product_meta_color_ids 
ON product_meta USING GIN ((
    SELECT array_agg(DISTINCT (detail->'color'->>'id')::int)
    FROM jsonb_array_elements(variants) AS variant,
         jsonb_array_elements(variant->'variantDetails') AS detail
    WHERE detail->'color'->>'id' IS NOT NULL
));

-- 9. Category hierarchy index
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_category_parent_id 
ON category (parent_id) 
WHERE parent_id IS NOT NULL;

-- 10. Product localization for multi-language support
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_product_localization_lang_product 
ON product_localization (language_id, product_id) 
WHERE deleted_at IS NULL;

-- Performance monitoring queries
-- Use these to check index usage:

-- Check index usage:
-- SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read, idx_tup_fetch 
-- FROM pg_stat_user_indexes 
-- WHERE indexname LIKE 'idx_product%' 
-- ORDER BY idx_scan DESC;

-- Check query performance:
-- EXPLAIN (ANALYZE, BUFFERS) 
-- SELECT p.id FROM product p 
-- INNER JOIN product_meta pm ON pm.product_id = p.id 
-- WHERE pm.country_id = 1 
-- AND p.brand_id = 1 
-- AND pm.variants @> '[{"size": {"id": 1}}]';
