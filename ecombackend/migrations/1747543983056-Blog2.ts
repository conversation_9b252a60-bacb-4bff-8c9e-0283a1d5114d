import { MigrationInterface, QueryRunner } from "typeorm";

export class Blog21747543983056 implements MigrationInterface {
    name = 'Blog21747543983056'

    public async up(queryRunner: QueryRunner): Promise<void> {
        
        await queryRunner.query(`CREATE TABLE "blog_categories" ("created_by" integer, "updated_by" integer, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "remarks" character varying, "name" character varying NOT NULL, "description" character varying(255), "slug" character varying NOT NULL DEFAULT 'name', "parent_id" integer, "sort_order" integer, "image_gallery_id" integer, "locale" jsonb, CONSTRAINT "UQ_903a6ea496e83ba9bec10af5835" UNIQUE ("slug"), CONSTRAINT "PK_1056d6faca26b9957f5d26e6572" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_903a6ea496e83ba9bec10af583" ON "blog_categories" ("slug") `);
        await queryRunner.query(`CREATE INDEX "IDX_ee34e04bfb55942ce65ae758b7" ON "blog_categories" ("parent_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_a51b35f289de4c99deadfb5e47" ON "blog_categories" ("image_gallery_id") `);
        await queryRunner.query(`CREATE TABLE "blog_post_products" ("created_by" integer, "updated_by" integer, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "remarks" character varying, "post_id" integer NOT NULL, "product_id" integer NOT NULL, "position" smallint NOT NULL DEFAULT '0', "postId" integer, CONSTRAINT "PK_e0d823543562c950beb6dd2cc9d" PRIMARY KEY ("id", "post_id", "product_id"))`);
        await queryRunner.query(`CREATE TABLE "blog_comments" ("created_by" integer, "updated_by" integer, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "remarks" character varying, "user_id" character varying, "author_name" character varying, "author_email" character varying, "content" character varying NOT NULL, "status" character varying(20) NOT NULL DEFAULT 'pending', "post_id" integer, "parent_id" integer, CONSTRAINT "PK_b478aaeecf38441a25739aa9610" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "blog_posts" ("created_by" integer, "updated_by" integer, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "remarks" character varying, "author_id" integer, "secondary_author_ids" character varying, "title" character varying NOT NULL, "slug" character varying NOT NULL, "excerpt" character varying, "content" character varying NOT NULL, "featured_image_url" character varying, "status" character varying NOT NULL DEFAULT 'draft', "published_at" TIMESTAMP WITH TIME ZONE, "meta_title" character varying(100), "meta_description" character varying(200), "country_id" integer, CONSTRAINT "UQ_5b2818a2c45c3edb9991b1c7a51" UNIQUE ("slug"), CONSTRAINT "PK_dd2add25eac93daefc93da9d387" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "blog_tags" ("created_by" integer, "updated_by" integer, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "remarks" character varying, "name" character varying NOT NULL, "slug" character varying NOT NULL, CONSTRAINT "UQ_afc33ebb304bb6ee9dc0a26c5d9" UNIQUE ("slug"), CONSTRAINT "PK_8880485f371f1892310811845c8" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "blog_post_tags" ("created_by" integer, "updated_by" integer, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "remarks" character varying, "post_id" integer NOT NULL, "tag_id" integer NOT NULL, "position" integer NOT NULL DEFAULT '0', "postId" integer, CONSTRAINT "PK_a6b87899f483670c1c8c7b98c02" PRIMARY KEY ("id", "post_id", "tag_id"))`);
        
        await queryRunner.query(`CREATE TABLE "blog_post_category" ("blog_post_id" integer NOT NULL, "blog_category_id" integer NOT NULL, CONSTRAINT "PK_735303a3928717baf038e2fe8ab" PRIMARY KEY ("blog_post_id", "blog_category_id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_74d14a71cadca3579f78d3bc94" ON "blog_post_category" ("blog_post_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_c9027abf1f7ac1800d0f092076" ON "blog_post_category" ("blog_category_id") `);
        
        await queryRunner.query(`ALTER TABLE "blog_post_tags" DROP COLUMN "created_by"`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" DROP COLUMN "updated_by"`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" DROP COLUMN "created_at"`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" DROP COLUMN "updated_at"`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" DROP COLUMN "deleted_at"`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" DROP CONSTRAINT "PK_a6b87899f483670c1c8c7b98c02"`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" ADD CONSTRAINT "PK_faca06ca8a7e90dc368e206da30" PRIMARY KEY ("post_id", "tag_id")`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" DROP COLUMN "id"`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" DROP COLUMN "is_active"`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" DROP COLUMN "remarks"`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" DROP COLUMN "position"`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" DROP COLUMN "postId"`);
        
        await queryRunner.query(`ALTER TABLE "blog_post_tags" ADD "created_by" integer`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" ADD "updated_by" integer`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" ADD "created_at" TIMESTAMP NOT NULL DEFAULT now()`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" ADD "updated_at" TIMESTAMP NOT NULL DEFAULT now()`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" ADD "deleted_at" TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" ADD "id" SERIAL NOT NULL`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" DROP CONSTRAINT "PK_faca06ca8a7e90dc368e206da30"`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" ADD CONSTRAINT "PK_a6b87899f483670c1c8c7b98c02" PRIMARY KEY ("post_id", "tag_id", "id")`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" ADD "is_active" boolean NOT NULL DEFAULT true`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" ADD "remarks" character varying`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" ADD "position" integer NOT NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" ADD "postId" integer`);
        
        await queryRunner.query(`ALTER TABLE "blog_post_tags" DROP CONSTRAINT "PK_a6b87899f483670c1c8c7b98c02"`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" ADD CONSTRAINT "PK_faca06ca8a7e90dc368e206da30" PRIMARY KEY ("post_id", "tag_id")`);
        await queryRunner.query(`CREATE INDEX "IDX_1de71966ca7ba3f4d0225db125" ON "blog_post_tags" ("post_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_92c39b2147baa669d0a77344bd" ON "blog_post_tags" ("tag_id") `);
        
        await queryRunner.query(`ALTER TABLE "blog_categories" ADD CONSTRAINT "FK_ee34e04bfb55942ce65ae758b79" FOREIGN KEY ("parent_id") REFERENCES "blog_categories"("id") ON DELETE SET NULL ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "blog_categories" ADD CONSTRAINT "FK_a51b35f289de4c99deadfb5e476" FOREIGN KEY ("image_gallery_id") REFERENCES "image_gallery"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "blog_post_products" ADD CONSTRAINT "FK_4cd8d8f29bdc11c385857b2211f" FOREIGN KEY ("postId") REFERENCES "blog_posts"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "blog_comments" ADD CONSTRAINT "FK_4e0b8959256b08ceb3d001f616b" FOREIGN KEY ("post_id") REFERENCES "blog_posts"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "blog_comments" ADD CONSTRAINT "FK_e681eead24fde355111a223fc6d" FOREIGN KEY ("parent_id") REFERENCES "blog_comments"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" ADD CONSTRAINT "FK_ad4d63c771dcd9365fe4fe5a463" FOREIGN KEY ("postId") REFERENCES "blog_posts"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        
        await queryRunner.query(`ALTER TABLE "blog_post_category" ADD CONSTRAINT "FK_74d14a71cadca3579f78d3bc947" FOREIGN KEY ("blog_post_id") REFERENCES "blog_posts"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "blog_post_category" ADD CONSTRAINT "FK_c9027abf1f7ac1800d0f092076c" FOREIGN KEY ("blog_category_id") REFERENCES "blog_categories"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" ADD CONSTRAINT "FK_1de71966ca7ba3f4d0225db1255" FOREIGN KEY ("post_id") REFERENCES "blog_posts"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" ADD CONSTRAINT "FK_92c39b2147baa669d0a77344bd9" FOREIGN KEY ("tag_id") REFERENCES "blog_tags"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "blog_post_tags" DROP CONSTRAINT "FK_92c39b2147baa669d0a77344bd9"`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" DROP CONSTRAINT "FK_1de71966ca7ba3f4d0225db1255"`);
        await queryRunner.query(`ALTER TABLE "blog_post_category" DROP CONSTRAINT "FK_c9027abf1f7ac1800d0f092076c"`);
        await queryRunner.query(`ALTER TABLE "blog_post_category" DROP CONSTRAINT "FK_74d14a71cadca3579f78d3bc947"`);
        await queryRunner.query(`ALTER TABLE "wheel_countries_country" DROP CONSTRAINT "FK_8df1dbe50606dd5f7665f03dbc1"`);
        await queryRunner.query(`ALTER TABLE "wheel_countries_country" DROP CONSTRAINT "FK_fd001f9b08dcfc57e02663462d0"`);
        await queryRunner.query(`ALTER TABLE "product_collection_pivot" DROP CONSTRAINT "FK_10274b8658544a015eb12603fa7"`);
        await queryRunner.query(`ALTER TABLE "country_courier" DROP CONSTRAINT "FK_af69aba49ef221b590372c7caf0"`);
        await queryRunner.query(`ALTER TABLE "country_courier" DROP CONSTRAINT "FK_6a53a80d5917eef5045a719a78c"`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" DROP CONSTRAINT "FK_ad4d63c771dcd9365fe4fe5a463"`);
        await queryRunner.query(`ALTER TABLE "blog_comments" DROP CONSTRAINT "FK_e681eead24fde355111a223fc6d"`);
        await queryRunner.query(`ALTER TABLE "blog_comments" DROP CONSTRAINT "FK_4e0b8959256b08ceb3d001f616b"`);
        await queryRunner.query(`ALTER TABLE "blog_post_products" DROP CONSTRAINT "FK_4cd8d8f29bdc11c385857b2211f"`);
        await queryRunner.query(`ALTER TABLE "blog_categories" DROP CONSTRAINT "FK_a51b35f289de4c99deadfb5e476"`);
        await queryRunner.query(`ALTER TABLE "blog_categories" DROP CONSTRAINT "FK_ee34e04bfb55942ce65ae758b79"`);
        await queryRunner.query(`ALTER TABLE "cart" DROP CONSTRAINT "FK_64b5ab228f2f151dc0c6e5ef7d6"`);
        await queryRunner.query(`ALTER TABLE "wheel_item" DROP CONSTRAINT "FK_7bb4210caa6de770006e9f3c6fe"`);
        await queryRunner.query(`ALTER TABLE "wheel_item" DROP CONSTRAINT "FK_af6f2abd212099d7b37134c8115"`);
        await queryRunner.query(`ALTER TABLE "seo_meta" DROP CONSTRAINT "FK_7fa670a01361654f51e0c5ccf3b"`);
        await queryRunner.query(`ALTER TABLE "size_chart_value" DROP CONSTRAINT "FK_eb13127f7b1727df82fbb5ab9a5"`);
        await queryRunner.query(`ALTER TABLE "size_chart_value" DROP CONSTRAINT "FK_f2a8009210c1fb90908a9311d90"`);
        await queryRunner.query(`ALTER TABLE "size_chart_value" DROP CONSTRAINT "FK_6ce3f87e117dded97de1cc6cfbd"`);
        await queryRunner.query(`ALTER TABLE "size_chart" DROP CONSTRAINT "FK_78010ddee9ed28e94656e3277f4"`);
        await queryRunner.query(`ALTER TABLE "product" DROP CONSTRAINT "FK_c20e1b4756acdc10f33dabc892c"`);
        await queryRunner.query(`ALTER TABLE "suggested_products" DROP CONSTRAINT "FK_6f0bd4092ee7b26b3f8c642b6d6"`);
        await queryRunner.query(`ALTER TABLE "suggested_products" DROP CONSTRAINT "FK_01a78c77da883a72fdae50c4284"`);
        await queryRunner.query(`ALTER TABLE "product_collection_pivot" DROP CONSTRAINT "FK_bc5206b280d552e093232f3eddd"`);
        await queryRunner.query(`ALTER TABLE "product_feed" DROP CONSTRAINT "FK_aa3f66004f07df10e56a6b8f460"`);
        await queryRunner.query(`ALTER TABLE "product_meta" DROP CONSTRAINT "FK_715cfb5a4f92e661f5b94a1cace"`);
        await queryRunner.query(`ALTER TABLE "category" DROP CONSTRAINT "FK_1117b4fcb3cd4abb4383e1c2743"`);
        await queryRunner.query(`ALTER TABLE "product_localization" DROP CONSTRAINT "UQ_dbc50143c6b36589b511ad0563b"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_92c39b2147baa669d0a77344bd"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_1de71966ca7ba3f4d0225db125"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_bc5206b280d552e093232f3edd"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_10274b8658544a015eb12603fa"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_af69aba49ef221b590372c7caf"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_6a53a80d5917eef5045a719a78"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_b6b5e81eddccdb215607015ad4"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_c20e1b4756acdc10f33dabc892"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_8cfaf4a1e80806d58e3dbe6922"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_3f4cfd3849111765f78554ea97"`);
        await queryRunner.query(`ALTER TABLE "product_collection_pivot" DROP CONSTRAINT "PK_2c48c2d823182ab367482c8a144"`);
        await queryRunner.query(`ALTER TABLE "product_collection_pivot" ADD CONSTRAINT "PK_10274b8658544a015eb12603fa7" PRIMARY KEY ("collection_id")`);
        await queryRunner.query(`ALTER TABLE "slider_country" ALTER COLUMN "sort_order" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "slider_media" DROP COLUMN "button_link"`);
        await queryRunner.query(`ALTER TABLE "slider_media" ADD "button_link" character varying`);
        await queryRunner.query(`ALTER TABLE "slider_media" DROP COLUMN "button_text"`);
        await queryRunner.query(`ALTER TABLE "slider_media" ADD "button_text" character varying`);
        await queryRunner.query(`ALTER TABLE "slider" ALTER COLUMN "usages_type" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "seller" DROP COLUMN "country_id"`);
        await queryRunner.query(`ALTER TABLE "seller" ADD "country_id" integer NOT NULL`);
        await queryRunner.query(`ALTER TABLE "seller" ALTER COLUMN "phone" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "product_order" ALTER COLUMN "is_draft" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "product_order" DROP CONSTRAINT "UQ_6d4e0758065b4d1fa0122de9310"`);
        await queryRunner.query(`ALTER TABLE "product_order" DROP COLUMN "uuid"`);
        await queryRunner.query(`ALTER TABLE "product_order" ADD "uuid" character varying(50)`);
        await queryRunner.query(`ALTER TABLE "courier_services" DROP COLUMN "cod_charge"`);
        await queryRunner.query(`ALTER TABLE "courier_services" ADD "cod_charge" numeric(10,2) NOT NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "courier_services" DROP COLUMN "price_outside_city"`);
        await queryRunner.query(`ALTER TABLE "courier_services" ADD "price_outside_city" numeric(10,2) NOT NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "courier_services" DROP COLUMN "price_inside_city"`);
        await queryRunner.query(`ALTER TABLE "courier_services" ADD "price_inside_city" numeric(10,2) NOT NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "size_chart_value" ALTER COLUMN "chart_images" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "size_chart_value" DROP COLUMN "note"`);
        await queryRunner.query(`ALTER TABLE "size_chart_value" ADD "note" jsonb`);
        await queryRunner.query(`ALTER TABLE "size_chart" ALTER COLUMN "chart_images" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "size_chart" DROP COLUMN "note"`);
        await queryRunner.query(`ALTER TABLE "size_chart" ADD "note" jsonb`);
        await queryRunner.query(`ALTER TABLE "product_localization" ALTER COLUMN "language_id" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "product_collection_pivot" DROP CONSTRAINT "PK_10274b8658544a015eb12603fa7"`);
        await queryRunner.query(`ALTER TABLE "product_collection_pivot" ADD CONSTRAINT "PK_2c48c2d823182ab367482c8a144" PRIMARY KEY ("collection_id", "product_id")`);
        await queryRunner.query(`ALTER TABLE "product_collection_pivot" ALTER COLUMN "product_id" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "product_collection_pivot" ADD CONSTRAINT "FK_bc5206b280d552e093232f3eddd" FOREIGN KEY ("product_id") REFERENCES "product"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "product_feed" DROP COLUMN "createdBy"`);
        await queryRunner.query(`ALTER TABLE "product_feed" ADD "createdBy" character varying`);
        await queryRunner.query(`ALTER TABLE "product_meta" ALTER COLUMN "product_id" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "product_meta" ADD CONSTRAINT "FK_715cfb5a4f92e661f5b94a1cace" FOREIGN KEY ("product_id") REFERENCES "product"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "country" DROP COLUMN "languages_id"`);
        await queryRunner.query(`ALTER TABLE "country" ADD "languages_id" integer array`);
        await queryRunner.query(`ALTER TABLE "courier_services" ALTER COLUMN "code" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" DROP CONSTRAINT "PK_faca06ca8a7e90dc368e206da30"`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" ADD CONSTRAINT "PK_a6b87899f483670c1c8c7b98c02" PRIMARY KEY ("post_id", "tag_id", "id")`);
        await queryRunner.query(`ALTER TABLE "product_category" DROP CONSTRAINT "PK_c14c8e52460c8062f62e7e8f416"`);
        await queryRunner.query(`ALTER TABLE "product_collection_pivot" DROP CONSTRAINT "PK_2c48c2d823182ab367482c8a144"`);
        await queryRunner.query(`ALTER TABLE "product_feed_items" DROP CONSTRAINT "PK_ca2b40fdb5849350381d1a94c55"`);
        await queryRunner.query(`ALTER TABLE "country_courier" DROP CONSTRAINT "PK_485a49aa48938634bef4acba7b9"`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" DROP COLUMN "postId"`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" DROP COLUMN "position"`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" DROP COLUMN "remarks"`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" DROP COLUMN "is_active"`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" DROP CONSTRAINT "PK_a6b87899f483670c1c8c7b98c02"`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" ADD CONSTRAINT "PK_faca06ca8a7e90dc368e206da30" PRIMARY KEY ("post_id", "tag_id")`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" DROP COLUMN "id"`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" DROP COLUMN "deleted_at"`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" DROP COLUMN "updated_at"`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" DROP COLUMN "created_at"`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" DROP COLUMN "updated_by"`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" DROP COLUMN "created_by"`);
        await queryRunner.query(`ALTER TABLE "courier_services" DROP COLUMN "remarks"`);
        await queryRunner.query(`ALTER TABLE "courier_services" DROP COLUMN "deleted_at"`);
        await queryRunner.query(`ALTER TABLE "courier_services" DROP COLUMN "updated_at"`);
        await queryRunner.query(`ALTER TABLE "courier_services" DROP COLUMN "created_at"`);
        await queryRunner.query(`ALTER TABLE "courier_services" DROP COLUMN "updated_by"`);
        await queryRunner.query(`ALTER TABLE "courier_services" DROP COLUMN "created_by"`);
        await queryRunner.query(`ALTER TABLE "product_collection_pivot" DROP COLUMN "sort_order"`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" ADD "postId" integer`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" ADD "position" integer NOT NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" ADD "remarks" character varying`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" ADD "is_active" boolean NOT NULL DEFAULT true`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" ADD "id" SERIAL NOT NULL`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" DROP CONSTRAINT "PK_faca06ca8a7e90dc368e206da30"`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" ADD CONSTRAINT "PK_a6b87899f483670c1c8c7b98c02" PRIMARY KEY ("id", "post_id", "tag_id")`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" ADD "deleted_at" TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" ADD "updated_at" TIMESTAMP NOT NULL DEFAULT now()`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" ADD "created_at" TIMESTAMP NOT NULL DEFAULT now()`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" ADD "updated_by" integer`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" ADD "created_by" integer`);
        await queryRunner.query(`ALTER TABLE "product_collection_pivot" ADD "sort_order" integer`);
        await queryRunner.query(`ALTER TABLE "country_courier" ADD "id" SERIAL NOT NULL`);
        await queryRunner.query(`ALTER TABLE "country_courier" ADD CONSTRAINT "country_courier_pkey" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "cart" ADD "shipping_discount_amount" numeric(10,2) DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "cart" ADD "shipping_discount_id" bigint`);
        await queryRunner.query(`ALTER TABLE "cart" ADD "order_discount_amount" numeric(10,2) DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "cart" ADD "order_discount_id" bigint`);
        await queryRunner.query(`ALTER TABLE "cart_item" ADD "discount_amount" numeric(10,2)`);
        await queryRunner.query(`ALTER TABLE "cart_item" ADD "discount_id" integer`);
        await queryRunner.query(`ALTER TABLE "news_subscription" ADD "user_id" integer`);
        await queryRunner.query(`ALTER TABLE "product_refund_type" ADD "sku" character varying`);
        await queryRunner.query(`ALTER TABLE "size_chart_value" ADD "measurement" jsonb`);
        await queryRunner.query(`ALTER TABLE "size_chart" ADD "measurement" jsonb`);
        await queryRunner.query(`ALTER TABLE "product" ADD "category_id" integer`);
        await queryRunner.query(`ALTER TABLE "courier_services" ADD "remarks" character varying`);
        await queryRunner.query(`ALTER TABLE "courier_services" ADD "deleted_at" TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "courier_services" ADD "updated_at" TIMESTAMP NOT NULL DEFAULT now()`);
        await queryRunner.query(`ALTER TABLE "courier_services" ADD "created_at" TIMESTAMP NOT NULL DEFAULT now()`);
        await queryRunner.query(`ALTER TABLE "courier_services" ADD "updated_by" integer`);
        await queryRunner.query(`ALTER TABLE "courier_services" ADD "created_by" integer`);
        await queryRunner.query(`DROP INDEX "public"."IDX_c9027abf1f7ac1800d0f092076"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_74d14a71cadca3579f78d3bc94"`);
        await queryRunner.query(`DROP TABLE "blog_post_category"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_8df1dbe50606dd5f7665f03dbc"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_fd001f9b08dcfc57e02663462d"`);
        await queryRunner.query(`DROP TABLE "wheel_countries_country"`);
        await queryRunner.query(`DROP TABLE "blog_post_tags"`);
        await queryRunner.query(`DROP TABLE "blog_tags"`);
        await queryRunner.query(`DROP TABLE "blog_posts"`);
        await queryRunner.query(`DROP TABLE "blog_comments"`);
        await queryRunner.query(`DROP TABLE "blog_post_products"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_a51b35f289de4c99deadfb5e47"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_ee34e04bfb55942ce65ae758b7"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_903a6ea496e83ba9bec10af583"`);
        await queryRunner.query(`DROP TABLE "blog_categories"`);
        await queryRunner.query(`DROP TABLE "wheel_item"`);
        await queryRunner.query(`DROP TABLE "wheel"`);
        await queryRunner.query(`ALTER TABLE "product_localization" ADD CONSTRAINT "UQ_93cb0579ab34174d798a7508d69" UNIQUE ("product_id", "language_id")`);
        await queryRunner.query(`ALTER TABLE "suggested_products" ADD CONSTRAINT "suggested_products_product_id_suggested_product_id_key" UNIQUE ("product_id", "suggested_product_id")`);
        await queryRunner.query(`CREATE INDEX "IDX_c33199b2errrdd98817b45fe7" ON "cart_item" ("discount_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_5a26e726d93e4121d86d8c1067" ON "product_localization" ("language_id") `);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_8cfaf4a1e80806d58e3dbe6922" ON "product" ("slug") `);
        await queryRunner.query(`CREATE INDEX "IDX_0dce9bc93c2d2c399982d04bef" ON "product" ("category_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_bc5206b280d552e093232f3edd" ON "product_collection_pivot" ("product_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_10274b8658544a015eb12603fa" ON "product_collection_pivot" ("collection_id") `);
        await queryRunner.query(`ALTER TABLE "country_courier" ADD CONSTRAINT "FK_country" FOREIGN KEY ("country_id") REFERENCES "country"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "country_courier" ADD CONSTRAINT "FK_courier" FOREIGN KEY ("courier_id") REFERENCES "courier_services"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "cart" ADD CONSTRAINT "cart_order_discount_id_fkey" FOREIGN KEY ("order_discount_id") REFERENCES "discount"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "cart" ADD CONSTRAINT "cart_shipping_discount_id_fkey" FOREIGN KEY ("shipping_discount_id") REFERENCES "discount"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "seller" ADD CONSTRAINT "fk_country" FOREIGN KEY ("country_id") REFERENCES "country"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "product_localization" ADD CONSTRAINT "FK_5a26e726d93e4121d86d8c1067a" FOREIGN KEY ("language_id") REFERENCES "country"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "product" ADD CONSTRAINT "FK_0dce9bc93c2d2c399982d04bef1" FOREIGN KEY ("category_id") REFERENCES "category"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "suggested_products" ADD CONSTRAINT "suggested_products_product_id_fkey" FOREIGN KEY ("product_id") REFERENCES "product"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "suggested_products" ADD CONSTRAINT "suggested_products_suggested_product_id_fkey" FOREIGN KEY ("suggested_product_id") REFERENCES "product"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "product_collection_pivot" ADD CONSTRAINT "FK_10274b8658544a015eb12603fa7" FOREIGN KEY ("collection_id") REFERENCES "product_collections"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "category" ADD CONSTRAINT "FK_1117b4fcb3cd4abb4383e1c2743" FOREIGN KEY ("parent_id") REFERENCES "category"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
