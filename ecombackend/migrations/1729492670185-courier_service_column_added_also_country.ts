import { MigrationInterface, QueryRunner } from 'typeorm';

export class CourierServiceColumnAddedAlsoCountry1729492670185
  implements MigrationInterface
{
  name = 'CourierServiceColumnAddedAlsoCountry1729492670185';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Step 1: Drop the country_courier table if it exists
    await queryRunner.query(`DROP TABLE IF EXISTS "country_courier"`);
    await queryRunner.query(`DROP TABLE IF EXISTS "courier"`);
    await queryRunner.query(`DROP TABLE IF EXISTS "country_courier_relation"`);

    // Step 2: Create a new country_courier_relation table
    await queryRunner.query(`
            CREATE TABLE "country_courier" (
                "id" SERIAL PRIMARY KEY,
                "country_id" INT NOT NULL,
                "courier_id" INT NOT NULL,
                CONSTRAINT "FK_country" FOREIGN KEY ("country_id") REFERENCES "country"("id") ON DELETE CASCADE ON UPDATE NO ACTION,
                CONSTRAINT "FK_courier" FOREIGN KEY ("courier_id") REFERENCES "courier_services"("id") ON DELETE CASCADE ON UPDATE NO ACTION
            )
        `);

    // Step 3: Add new columns to courier_services
    await queryRunner.query(
      `ALTER TABLE "courier_services" ADD "apiKey" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "courier_services" ADD "price_inside_city" numeric(10,2) NOT NULL DEFAULT '0'`,
    );
    await queryRunner.query(
      `ALTER TABLE "courier_services" ADD "price_outside_city" numeric(10,2) NOT NULL DEFAULT '0'`,
    );
    await queryRunner.query(
      `ALTER TABLE "courier_services" ADD "cod_charge" numeric(10,2) NOT NULL DEFAULT '0'`,
    );
    await queryRunner.query(
      `ALTER TABLE "courier_services" ADD "additional" jsonb DEFAULT '{}'`,
    );
    await queryRunner.query(
      `ALTER TABLE "courier_services" ADD "icon" character varying`,
    );

    await queryRunner.query(
      `ALTER TABLE "country" ADD "gtm_code" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "country" ADD "gtm_auth" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "country" ADD "language_id" integer NOT NULL DEFAULT 1`,
    );

    await queryRunner.query(
      `ALTER TABLE "country" ADD "domain" character varying`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Step 1: Drop the country_courier_relation table
    await queryRunner.query(`DROP TABLE IF EXISTS "country_courier"`);
    await queryRunner.query(`DROP TABLE IF EXISTS "country_courier_relation"`);

    // Step 2: Drop the added columns in courier_services
    await queryRunner.query(
      `ALTER TABLE "courier_services" DROP COLUMN "icon"`,
    );
    await queryRunner.query(
      `ALTER TABLE "courier_services" DROP COLUMN "additional"`,
    );
    await queryRunner.query(
      `ALTER TABLE "courier_services" DROP COLUMN "cod_charge"`,
    );
    await queryRunner.query(
      `ALTER TABLE "courier_services" DROP COLUMN "price_outside_city"`,
    );
    await queryRunner.query(
      `ALTER TABLE "courier_services" DROP COLUMN "price_inside_city"`,
    );
    await queryRunner.query(
      `ALTER TABLE "courier_services" DROP COLUMN "apiKey"`,
    );

    await queryRunner.query(`ALTER TABLE "country" DROP COLUMN "gtm_code"`);
    await queryRunner.query(`ALTER TABLE "country" DROP COLUMN "gtm_auth"`);
    await queryRunner.query(`ALTER TABLE "country" DROP COLUMN "language_id"`);
    await queryRunner.query(`ALTER TABLE "country" DROP COLUMN "domain"`);
  }
}
