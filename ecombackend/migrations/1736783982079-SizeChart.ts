import { MigrationInterface, QueryRunner } from 'typeorm';

export class SizeChart1736783982079 implements MigrationInterface {
  name = 'SizeChart1736783982079';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "size_chart" ("id" SERIAL NOT NULL, "name" character varying NOT NULL, "values" jsonb NOT NULL, "image_id" integer, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_afd60357dbee3fd9101effa5200" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "size_chart_value" ("id" SERIAL NOT NULL, "size_chart_id" integer NOT NULL, "countryId" integer NOT NULL, "name" character varying NOT NULL, "values" jsonb, "image_id" integer, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_be8d3b6c310e0a4ff73e5de259e" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_2947af3ec38f583b309a3769ca" ON "size_chart_value" ("countryId", "size_chart_id") `,
    );

    await queryRunner.query(
      `ALTER TABLE "product" ADD "size_chart_id" integer`,
    );

    await queryRunner.query(`ALTER TABLE "size_chart" ADD COLUMN "note" TEXT`);

    await queryRunner.query(
      `ALTER TABLE "size_chart_value" ADD COLUMN "note" TEXT`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "product" DROP COLUMN "size_chart_id"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_2947af3ec38f583b309a3769ca"`,
    );
    await queryRunner.query(`DROP TABLE "size_chart_value"`);
    await queryRunner.query(`DROP TABLE "size_chart"`);
  }
}
