import { MigrationInterface, QueryRunner } from "typeorm";

export class AddedCountryCodeInAddress1723035810375 implements MigrationInterface {
    name = 'AddedCountryCodeInAddress1723035810375'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "address" ADD "country_code" integer`);
        await queryRunner.query(`ALTER TABLE "address_order" ADD "country_code" integer`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "address_order" DROP COLUMN "country_code"`);
        await queryRunner.query(`ALTER TABLE "address" DROP COLUMN "country_code"`);
    }

}
