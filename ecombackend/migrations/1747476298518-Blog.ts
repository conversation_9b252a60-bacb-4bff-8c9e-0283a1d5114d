import { MigrationInterface, QueryRunner } from "typeorm";

export class Blog1747476298518 implements MigrationInterface {
    name = 'Blog1747476298518'

    public async up(queryRunner: QueryRunner): Promise<void> {
        
        await queryRunner.query(`CREATE TABLE "blog_categories" ("created_by" integer, "updated_by" integer, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "remarks" character varying, "name" character varying(100) NOT NULL, "slug" character varying(100) NOT NULL, "description" text, "parentId" integer, CONSTRAINT "UQ_903a6ea496e83ba9bec10af5835" UNIQUE ("slug"), CONSTRAINT "PK_1056d6faca26b9957f5d26e6572" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "blog_post_products" ("created_by" integer, "updated_by" integer, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "remarks" character varying, "post_id" integer NOT NULL, "product_id" integer NOT NULL, "position" smallint NOT NULL DEFAULT '0', "postId" integer, CONSTRAINT "PK_e0d823543562c950beb6dd2cc9d" PRIMARY KEY ("id", "post_id", "product_id"))`);
        await queryRunner.query(`CREATE TABLE "blog_comments" ("created_by" integer, "updated_by" integer, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "remarks" character varying, "user_id" character varying, "author_name" character varying, "author_email" character varying, "content" character varying NOT NULL, "status" character varying(20) NOT NULL DEFAULT 'pending', "post_id" integer, "parent_id" integer, CONSTRAINT "PK_b478aaeecf38441a25739aa9610" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "blog_posts" ("created_by" integer, "updated_by" integer, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "remarks" character varying, "author_id" integer, "secondary_author_ids" character varying, "title" character varying NOT NULL, "slug" character varying NOT NULL, "excerpt" character varying, "content" character varying NOT NULL, "featured_image_url" character varying, "status" character varying NOT NULL DEFAULT 'draft', "published_at" TIMESTAMP WITH TIME ZONE, "meta_title" character varying(100), "meta_description" character varying(200), "category_id" integer, CONSTRAINT "UQ_5b2818a2c45c3edb9991b1c7a51" UNIQUE ("slug"), CONSTRAINT "PK_dd2add25eac93daefc93da9d387" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "blog_tags" ("created_by" integer, "updated_by" integer, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "remarks" character varying, "name" character varying NOT NULL, "slug" character varying NOT NULL, CONSTRAINT "UQ_afc33ebb304bb6ee9dc0a26c5d9" UNIQUE ("slug"), CONSTRAINT "PK_8880485f371f1892310811845c8" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "blog_post_tags" ("created_by" integer, "updated_by" integer, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "remarks" character varying, "post_id" integer NOT NULL, "tag_id" integer NOT NULL, "position" integer NOT NULL DEFAULT '0', "postId" integer, CONSTRAINT "PK_a6b87899f483670c1c8c7b98c02" PRIMARY KEY ("id", "post_id", "tag_id"))`);
        
        await queryRunner.query(`ALTER TABLE "blog_post_tags" DROP COLUMN "created_by"`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" DROP COLUMN "updated_by"`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" DROP COLUMN "created_at"`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" DROP COLUMN "updated_at"`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" DROP COLUMN "deleted_at"`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" DROP CONSTRAINT "PK_a6b87899f483670c1c8c7b98c02"`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" ADD CONSTRAINT "PK_faca06ca8a7e90dc368e206da30" PRIMARY KEY ("post_id", "tag_id")`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" DROP COLUMN "id"`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" DROP COLUMN "is_active"`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" DROP COLUMN "remarks"`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" DROP COLUMN "position"`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" DROP COLUMN "postId"`);
        
        await queryRunner.query(`ALTER TABLE "blog_post_tags" ADD "created_by" integer`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" ADD "updated_by" integer`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" ADD "created_at" TIMESTAMP NOT NULL DEFAULT now()`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" ADD "updated_at" TIMESTAMP NOT NULL DEFAULT now()`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" ADD "deleted_at" TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" ADD "id" SERIAL NOT NULL`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" DROP CONSTRAINT "PK_faca06ca8a7e90dc368e206da30"`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" ADD CONSTRAINT "PK_a6b87899f483670c1c8c7b98c02" PRIMARY KEY ("post_id", "tag_id", "id")`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" ADD "is_active" boolean NOT NULL DEFAULT true`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" ADD "remarks" character varying`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" ADD "position" integer NOT NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" ADD "postId" integer`);
        
        await queryRunner.query(`ALTER TABLE "blog_post_tags" DROP CONSTRAINT "PK_a6b87899f483670c1c8c7b98c02"`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" ADD CONSTRAINT "PK_faca06ca8a7e90dc368e206da30" PRIMARY KEY ("post_id", "tag_id")`);
        
        await queryRunner.query(`CREATE INDEX "IDX_1de71966ca7ba3f4d0225db125" ON "blog_post_tags" ("post_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_92c39b2147baa669d0a77344bd" ON "blog_post_tags" ("tag_id") `);
        
        await queryRunner.query(`ALTER TABLE "blog_categories" ADD CONSTRAINT "FK_b8f73bf6f544ac8846f2a0a2eca" FOREIGN KEY ("parentId") REFERENCES "blog_categories"("id") ON DELETE SET NULL ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "blog_post_products" ADD CONSTRAINT "FK_4cd8d8f29bdc11c385857b2211f" FOREIGN KEY ("postId") REFERENCES "blog_posts"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "blog_comments" ADD CONSTRAINT "FK_4e0b8959256b08ceb3d001f616b" FOREIGN KEY ("post_id") REFERENCES "blog_posts"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "blog_comments" ADD CONSTRAINT "FK_e681eead24fde355111a223fc6d" FOREIGN KEY ("parent_id") REFERENCES "blog_comments"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "blog_posts" ADD CONSTRAINT "FK_2829ec24ec5af8ca797f76817ab" FOREIGN KEY ("category_id") REFERENCES "blog_categories"("id") ON DELETE SET NULL ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" ADD CONSTRAINT "FK_ad4d63c771dcd9365fe4fe5a463" FOREIGN KEY ("postId") REFERENCES "blog_posts"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        
        await queryRunner.query(`ALTER TABLE "blog_post_tags" ADD CONSTRAINT "FK_1de71966ca7ba3f4d0225db1255" FOREIGN KEY ("post_id") REFERENCES "blog_posts"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" ADD CONSTRAINT "FK_92c39b2147baa669d0a77344bd9" FOREIGN KEY ("tag_id") REFERENCES "blog_tags"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "blog_post_tags" DROP CONSTRAINT "FK_92c39b2147baa669d0a77344bd9"`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" DROP CONSTRAINT "FK_1de71966ca7ba3f4d0225db1255"`);
        
        await queryRunner.query(`ALTER TABLE "blog_post_tags" DROP CONSTRAINT "FK_ad4d63c771dcd9365fe4fe5a463"`);
        await queryRunner.query(`ALTER TABLE "blog_posts" DROP CONSTRAINT "FK_2829ec24ec5af8ca797f76817ab"`);
        await queryRunner.query(`ALTER TABLE "blog_comments" DROP CONSTRAINT "FK_e681eead24fde355111a223fc6d"`);
        await queryRunner.query(`ALTER TABLE "blog_comments" DROP CONSTRAINT "FK_4e0b8959256b08ceb3d001f616b"`);
        await queryRunner.query(`ALTER TABLE "blog_post_products" DROP CONSTRAINT "FK_4cd8d8f29bdc11c385857b2211f"`);
        await queryRunner.query(`ALTER TABLE "blog_categories" DROP CONSTRAINT "FK_b8f73bf6f544ac8846f2a0a2eca"`);
        
        await queryRunner.query(`ALTER TABLE "blog_post_tags" DROP CONSTRAINT "PK_faca06ca8a7e90dc368e206da30"`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" ADD CONSTRAINT "PK_a6b87899f483670c1c8c7b98c02" PRIMARY KEY ("post_id", "tag_id", "id")`);
        
        await queryRunner.query(`ALTER TABLE "blog_post_tags" DROP COLUMN "postId"`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" DROP COLUMN "position"`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" DROP COLUMN "remarks"`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" DROP COLUMN "is_active"`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" DROP CONSTRAINT "PK_a6b87899f483670c1c8c7b98c02"`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" ADD CONSTRAINT "PK_faca06ca8a7e90dc368e206da30" PRIMARY KEY ("post_id", "tag_id")`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" DROP COLUMN "id"`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" DROP COLUMN "deleted_at"`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" DROP COLUMN "updated_at"`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" DROP COLUMN "created_at"`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" DROP COLUMN "updated_by"`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" DROP COLUMN "created_by"`);
        
        await queryRunner.query(`ALTER TABLE "blog_post_tags" ADD "postId" integer`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" ADD "position" integer NOT NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" ADD "remarks" character varying`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" ADD "is_active" boolean NOT NULL DEFAULT true`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" ADD "id" SERIAL NOT NULL`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" DROP CONSTRAINT "PK_faca06ca8a7e90dc368e206da30"`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" ADD CONSTRAINT "PK_a6b87899f483670c1c8c7b98c02" PRIMARY KEY ("id", "post_id", "tag_id")`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" ADD "deleted_at" TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" ADD "updated_at" TIMESTAMP NOT NULL DEFAULT now()`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" ADD "created_at" TIMESTAMP NOT NULL DEFAULT now()`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" ADD "updated_by" integer`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" ADD "created_by" integer`);
        
        await queryRunner.query(`DROP TABLE "blog_post_tags"`);
        await queryRunner.query(`DROP TABLE "blog_tags"`);
        await queryRunner.query(`DROP TABLE "blog_posts"`);
        await queryRunner.query(`DROP TABLE "blog_comments"`);
        await queryRunner.query(`DROP TABLE "blog_post_products"`);
        await queryRunner.query(`DROP TABLE "blog_categories"`);
        
    }

}
