import { MigrationInterface, QueryRunner } from "typeorm";

export class BlogPostCategoryTagAuthorLocalization1747731524232 implements MigrationInterface {
    name = 'BlogPostCategoryTagAuthorLocalization1747731524232'

    public async up(queryRunner: QueryRunner): Promise<void> {

        await queryRunner.query(`CREATE TABLE "blog_categories" ("created_by" integer, "updated_by" integer, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "remarks" character varying, "name" character varying NOT NULL, "description" character varying(255), "slug" character varying NOT NULL, "parent_id" integer, "sort_order" integer, "image_gallery_id" integer, "locale" jsonb, CONSTRAINT "UQ_903a6ea496e83ba9bec10af5835" UNIQUE ("slug"), CONSTRAINT "PK_1056d6faca26b9957f5d26e6572" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_903a6ea496e83ba9bec10af583" ON "blog_categories" ("slug") `);
        await queryRunner.query(`CREATE INDEX "IDX_ee34e04bfb55942ce65ae758b7" ON "blog_categories" ("parent_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_a51b35f289de4c99deadfb5e47" ON "blog_categories" ("image_gallery_id") `);
        await queryRunner.query(`CREATE TABLE "blog_post_products" ("created_by" integer, "updated_by" integer, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "remarks" character varying, "post_id" integer NOT NULL, "product_id" integer NOT NULL, "position" smallint NOT NULL DEFAULT '0', "postId" integer, CONSTRAINT "PK_e0d823543562c950beb6dd2cc9d" PRIMARY KEY ("id", "post_id", "product_id"))`);
        await queryRunner.query(`CREATE TABLE "blog_comments" ("created_by" integer, "updated_by" integer, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "remarks" character varying, "user_id" character varying, "author_name" character varying, "author_email" character varying, "content" character varying NOT NULL, "status" character varying(20) NOT NULL DEFAULT 'pending', "post_id" integer, "parent_id" integer, CONSTRAINT "PK_b478aaeecf38441a25739aa9610" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "blog_authors" ("created_by" integer, "updated_by" integer, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "remarks" character varying, "name" character varying NOT NULL, "description" character varying(255), "email" character varying NOT NULL, "image_gallery_id" integer, CONSTRAINT "UQ_fb4fd20f5452d17ae397ea4c0b1" UNIQUE ("email"), CONSTRAINT "PK_1b3376ea6752f667f5be151df3f" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_353afdd9f6d040bf6023360a7b" ON "blog_authors" ("image_gallery_id") `);
        await queryRunner.query(`CREATE TABLE "blog_post_localization" ("created_by" integer, "updated_by" integer, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "remarks" character varying, "language_id" integer NOT NULL, "title" character varying, "excerpt" character varying, "content" character varying, "featured_image_url" character varying, "status" character varying NOT NULL DEFAULT 'draft', "published_at" TIMESTAMP WITH TIME ZONE, "meta_title" character varying(100), "meta_description" character varying(200), "primary_author" integer, "meta_info" jsonb, "blog_post_id" integer, CONSTRAINT "UQ_a284b585a90d9dd5c0a3d9a29c8" UNIQUE ("blog_post_id", "language_id"), CONSTRAINT "PK_5e293ab9329ac876e3dc9ab1cea" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_9ad4b81f63c9a2bede81cdcfae" ON "blog_post_localization" ("blog_post_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_c98fa70d9abf23efb2c7ea5f7b" ON "blog_post_localization" ("language_id") `);
        await queryRunner.query(`CREATE TABLE "blog_posts" ("created_by" integer, "updated_by" integer, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "remarks" character varying, "title" character varying NOT NULL, "slug" character varying NOT NULL, "excerpt" character varying, "content" character varying NOT NULL, "featured_image_url" character varying, "status" character varying NOT NULL DEFAULT 'draft', "published_at" TIMESTAMP WITH TIME ZONE, "meta_title" character varying(100), "meta_description" character varying(200), "primary_author" integer, "country_id" integer, "meta_info" jsonb, CONSTRAINT "UQ_5b2818a2c45c3edb9991b1c7a51" UNIQUE ("slug"), CONSTRAINT "PK_dd2add25eac93daefc93da9d387" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "blog_tags" ("created_by" integer, "updated_by" integer, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "remarks" character varying, "name" character varying NOT NULL, "description" character varying(255), "slug" character varying NOT NULL, "locale" jsonb, CONSTRAINT "UQ_afc33ebb304bb6ee9dc0a26c5d9" UNIQUE ("slug"), CONSTRAINT "PK_8880485f371f1892310811845c8" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_afc33ebb304bb6ee9dc0a26c5d" ON "blog_tags" ("slug") `);
        await queryRunner.query(`CREATE TABLE "wheel" ("created_by" integer, "updated_by" integer, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "remarks" character varying, "title" character varying, "description" character varying, "locale" jsonb, CONSTRAINT "PK_87bf9475f18a2419cf0a424120d" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "wheel_item" ("created_by" integer, "updated_by" integer, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "remarks" character varying, "text" character varying NOT NULL, "description" character varying NOT NULL, "color" character varying NOT NULL, "value" integer, "weight" integer NOT NULL DEFAULT '1', "locale" jsonb, "wheelId" integer, "couponId" integer, CONSTRAINT "PK_21c853a090c09ec486cacc3dc05" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "blog_post_locale_authors" ("post_id" integer NOT NULL, "author_id" integer NOT NULL, CONSTRAINT "PK_f88d2babd0c51645599610b5d89" PRIMARY KEY ("post_id", "author_id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_8514fd7dad82a089589dd728b6" ON "blog_post_locale_authors" ("post_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_b991fc221311c22eb4322e34e9" ON "blog_post_locale_authors" ("author_id") `);
        await queryRunner.query(`CREATE TABLE "blog_post_category" ("blog_post_id" integer NOT NULL, "blog_category_id" integer NOT NULL, CONSTRAINT "PK_735303a3928717baf038e2fe8ab" PRIMARY KEY ("blog_post_id", "blog_category_id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_74d14a71cadca3579f78d3bc94" ON "blog_post_category" ("blog_post_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_c9027abf1f7ac1800d0f092076" ON "blog_post_category" ("blog_category_id") `);
        await queryRunner.query(`CREATE TABLE "blog_post_tags" ("post_id" integer NOT NULL, "tag_id" integer NOT NULL, CONSTRAINT "PK_faca06ca8a7e90dc368e206da30" PRIMARY KEY ("post_id", "tag_id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_1de71966ca7ba3f4d0225db125" ON "blog_post_tags" ("post_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_92c39b2147baa669d0a77344bd" ON "blog_post_tags" ("tag_id") `);
        await queryRunner.query(`CREATE TABLE "blog_post_authors" ("post_id" integer NOT NULL, "author_id" integer NOT NULL, CONSTRAINT "PK_ea5cbdd9111db7badfe1256b2e3" PRIMARY KEY ("post_id", "author_id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_6cbd9ae7f26020145a8ce46d81" ON "blog_post_authors" ("post_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_995a8f9bc957eb12fd24b5a23a" ON "blog_post_authors" ("author_id") `);

        await queryRunner.query(`ALTER TABLE "blog_categories" ADD CONSTRAINT "FK_ee34e04bfb55942ce65ae758b79" FOREIGN KEY ("parent_id") REFERENCES "blog_categories"("id") ON DELETE SET NULL ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "blog_categories" ADD CONSTRAINT "FK_a51b35f289de4c99deadfb5e476" FOREIGN KEY ("image_gallery_id") REFERENCES "image_gallery"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "blog_post_products" ADD CONSTRAINT "FK_4cd8d8f29bdc11c385857b2211f" FOREIGN KEY ("postId") REFERENCES "blog_posts"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "blog_comments" ADD CONSTRAINT "FK_4e0b8959256b08ceb3d001f616b" FOREIGN KEY ("post_id") REFERENCES "blog_posts"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "blog_comments" ADD CONSTRAINT "FK_e681eead24fde355111a223fc6d" FOREIGN KEY ("parent_id") REFERENCES "blog_comments"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "blog_authors" ADD CONSTRAINT "FK_353afdd9f6d040bf6023360a7bc" FOREIGN KEY ("image_gallery_id") REFERENCES "image_gallery"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "blog_post_localization" ADD CONSTRAINT "FK_9ad4b81f63c9a2bede81cdcfae3" FOREIGN KEY ("blog_post_id") REFERENCES "blog_posts"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "blog_post_localization" ADD CONSTRAINT "FK_5e73d4f87d651ed89b8009317a6" FOREIGN KEY ("primary_author") REFERENCES "blog_authors"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "blog_posts" ADD CONSTRAINT "FK_4d910e27b947e6904160c9859aa" FOREIGN KEY ("primary_author") REFERENCES "blog_authors"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);

        await queryRunner.query(`ALTER TABLE "blog_post_locale_authors" ADD CONSTRAINT "FK_8514fd7dad82a089589dd728b63" FOREIGN KEY ("post_id") REFERENCES "blog_post_localization"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "blog_post_locale_authors" ADD CONSTRAINT "FK_b991fc221311c22eb4322e34e90" FOREIGN KEY ("author_id") REFERENCES "blog_authors"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "blog_post_category" ADD CONSTRAINT "FK_74d14a71cadca3579f78d3bc947" FOREIGN KEY ("blog_post_id") REFERENCES "blog_posts"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "blog_post_category" ADD CONSTRAINT "FK_c9027abf1f7ac1800d0f092076c" FOREIGN KEY ("blog_category_id") REFERENCES "blog_categories"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" ADD CONSTRAINT "FK_1de71966ca7ba3f4d0225db1255" FOREIGN KEY ("post_id") REFERENCES "blog_posts"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" ADD CONSTRAINT "FK_92c39b2147baa669d0a77344bd9" FOREIGN KEY ("tag_id") REFERENCES "blog_tags"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "blog_post_authors" ADD CONSTRAINT "FK_6cbd9ae7f26020145a8ce46d81e" FOREIGN KEY ("post_id") REFERENCES "blog_posts"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "blog_post_authors" ADD CONSTRAINT "FK_995a8f9bc957eb12fd24b5a23af" FOREIGN KEY ("author_id") REFERENCES "blog_authors"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {

        await queryRunner.query(`ALTER TABLE "blog_post_authors" DROP CONSTRAINT "FK_995a8f9bc957eb12fd24b5a23af"`);
        await queryRunner.query(`ALTER TABLE "blog_post_authors" DROP CONSTRAINT "FK_6cbd9ae7f26020145a8ce46d81e"`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" DROP CONSTRAINT "FK_92c39b2147baa669d0a77344bd9"`);
        await queryRunner.query(`ALTER TABLE "blog_post_tags" DROP CONSTRAINT "FK_1de71966ca7ba3f4d0225db1255"`);
        await queryRunner.query(`ALTER TABLE "blog_post_category" DROP CONSTRAINT "FK_c9027abf1f7ac1800d0f092076c"`);
        await queryRunner.query(`ALTER TABLE "blog_post_category" DROP CONSTRAINT "FK_74d14a71cadca3579f78d3bc947"`);
        await queryRunner.query(`ALTER TABLE "blog_post_locale_authors" DROP CONSTRAINT "FK_b991fc221311c22eb4322e34e90"`);
        await queryRunner.query(`ALTER TABLE "blog_post_locale_authors" DROP CONSTRAINT "FK_8514fd7dad82a089589dd728b63"`);

        await queryRunner.query(`ALTER TABLE "blog_posts" DROP CONSTRAINT "FK_4d910e27b947e6904160c9859aa"`);
        await queryRunner.query(`ALTER TABLE "blog_post_localization" DROP CONSTRAINT "FK_5e73d4f87d651ed89b8009317a6"`);
        await queryRunner.query(`ALTER TABLE "blog_post_localization" DROP CONSTRAINT "FK_9ad4b81f63c9a2bede81cdcfae3"`);
        await queryRunner.query(`ALTER TABLE "blog_authors" DROP CONSTRAINT "FK_353afdd9f6d040bf6023360a7bc"`);
        await queryRunner.query(`ALTER TABLE "blog_comments" DROP CONSTRAINT "FK_e681eead24fde355111a223fc6d"`);
        await queryRunner.query(`ALTER TABLE "blog_comments" DROP CONSTRAINT "FK_4e0b8959256b08ceb3d001f616b"`);
        await queryRunner.query(`ALTER TABLE "blog_post_products" DROP CONSTRAINT "FK_4cd8d8f29bdc11c385857b2211f"`);
        await queryRunner.query(`ALTER TABLE "blog_categories" DROP CONSTRAINT "FK_a51b35f289de4c99deadfb5e476"`);
        await queryRunner.query(`ALTER TABLE "blog_categories" DROP CONSTRAINT "FK_ee34e04bfb55942ce65ae758b79"`);

        await queryRunner.query(`DROP INDEX "public"."IDX_995a8f9bc957eb12fd24b5a23a"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_6cbd9ae7f26020145a8ce46d81"`);
        await queryRunner.query(`DROP TABLE "blog_post_authors"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_92c39b2147baa669d0a77344bd"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_1de71966ca7ba3f4d0225db125"`);
        await queryRunner.query(`DROP TABLE "blog_post_tags"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_c9027abf1f7ac1800d0f092076"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_74d14a71cadca3579f78d3bc94"`);
        await queryRunner.query(`DROP TABLE "blog_post_category"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_b991fc221311c22eb4322e34e9"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_8514fd7dad82a089589dd728b6"`);
        await queryRunner.query(`DROP TABLE "blog_post_locale_authors"`);

        await queryRunner.query(`DROP INDEX "public"."IDX_afc33ebb304bb6ee9dc0a26c5d"`);
        await queryRunner.query(`DROP TABLE "blog_tags"`);
        await queryRunner.query(`DROP TABLE "blog_posts"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_c98fa70d9abf23efb2c7ea5f7b"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_9ad4b81f63c9a2bede81cdcfae"`);
        await queryRunner.query(`DROP TABLE "blog_post_localization"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_353afdd9f6d040bf6023360a7b"`);
        await queryRunner.query(`DROP TABLE "blog_authors"`);
        await queryRunner.query(`DROP TABLE "blog_comments"`);
        await queryRunner.query(`DROP TABLE "blog_post_products"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_a51b35f289de4c99deadfb5e47"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_ee34e04bfb55942ce65ae758b7"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_903a6ea496e83ba9bec10af583"`);
        await queryRunner.query(`DROP TABLE "blog_categories"`);

    }

}
