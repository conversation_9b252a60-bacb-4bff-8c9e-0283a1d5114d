import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateCourierHistoryTable1725392899201 implements MigrationInterface {
    name = 'CreateCourierHistoryTable1725392899201'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "courier_history" ("created_by" integer, "updated_by" integer, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "remarks" character varying, "serviceProvider" character varying NOT NULL, "requestType" character varying NOT NULL, "requestData" text, "responseData" text, CONSTRAINT "PK_388a7744cd9c87c190ce99f2714" PRIMARY KEY ("id"))`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE "courier_history"`);
    }

}
