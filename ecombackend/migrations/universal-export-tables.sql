-- Universal Export Tables Migration
-- Created: 2025-07-24

-- Create universal_export table
CREATE TABLE IF NOT EXISTS universal_export (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL UNIQUE,
    country_id INTEGER NOT NULL,
    delimiter VARCHAR(5) NOT NULL DEFAULT ';',
    image_limit INTEGER NOT NULL DEFAULT 1 CHECK (image_limit >= 1 AND image_limit <= 10),
    include_variants BOOLEAN NOT NULL DEFAULT true,
    include_inactive BOOLEAN NOT NULL DEFAULT false,
    description TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER NOT NULL,
    
    -- Foreign key constraints
    CONSTRAINT fk_universal_export_country FOREIGN KEY (country_id) REFERENCES country(id) ON DELETE CASCADE,
    CONSTRAINT fk_universal_export_created_by <PERSON>OREIG<PERSON> KEY (created_by) <PERSON><PERSON><PERSON>ENCES "user"(id) ON DELETE CASCADE
);

-- <PERSON>reate field_mapping table
CREATE TABLE IF NOT EXISTS field_mapping (
    id SERIAL PRIMARY KEY,
    universal_export_id INTEGER NOT NULL,
    source_field VARCHAR(255) NOT NULL,
    target_field VARCHAR(255) NOT NULL,
    field_type VARCHAR(20) NOT NULL DEFAULT 'string' CHECK (field_type IN ('string', 'number', 'boolean', 'date', 'image', 'array')),
    is_required BOOLEAN NOT NULL DEFAULT false,
    default_value TEXT,
    sort_order INTEGER NOT NULL DEFAULT 0,
    format_pattern VARCHAR(255),
    description TEXT,
    
    -- Foreign key constraints
    CONSTRAINT fk_field_mapping_universal_export FOREIGN KEY (universal_export_id) REFERENCES universal_export(id) ON DELETE CASCADE
);

-- Create universal_export_products junction table
CREATE TABLE IF NOT EXISTS universal_export_products (
    universal_export_id INTEGER NOT NULL,
    product_id INTEGER NOT NULL,
    
    PRIMARY KEY (universal_export_id, product_id),
    
    -- Foreign key constraints
    CONSTRAINT fk_universal_export_products_export FOREIGN KEY (universal_export_id) REFERENCES universal_export(id) ON DELETE CASCADE,
    CONSTRAINT fk_universal_export_products_product FOREIGN KEY (product_id) REFERENCES product(id) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_universal_export_slug ON universal_export(slug);
CREATE INDEX IF NOT EXISTS idx_universal_export_country_id ON universal_export(country_id);
CREATE INDEX IF NOT EXISTS idx_universal_export_created_by ON universal_export(created_by);
CREATE INDEX IF NOT EXISTS idx_universal_export_created_at ON universal_export(created_at);

CREATE INDEX IF NOT EXISTS idx_field_mapping_universal_export_id ON field_mapping(universal_export_id);
CREATE INDEX IF NOT EXISTS idx_field_mapping_sort_order ON field_mapping(universal_export_id, sort_order);

CREATE INDEX IF NOT EXISTS idx_universal_export_products_export_id ON universal_export_products(universal_export_id);
CREATE INDEX IF NOT EXISTS idx_universal_export_products_product_id ON universal_export_products(product_id);

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_universal_export_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_universal_export_updated_at
    BEFORE UPDATE ON universal_export
    FOR EACH ROW
    EXECUTE FUNCTION update_universal_export_updated_at();

-- Insert sample data for testing
INSERT INTO universal_export (name, slug, country_id, delimiter, image_limit, include_variants, include_inactive, description, created_by)
VALUES 
    ('Sample CSV Export', 'sample-csv-export-12345678', 1, ';', 2, true, false, 'Sample CSV export template for testing', 1),
    ('Sample XLSX Export', 'sample-xlsx-export-87654321', 1, ',', 3, true, true, 'Sample XLSX export template for testing', 1),
    ('Sample XML Export', 'sample-xml-export-11223344', 1, ';', 1, false, false, 'Sample XML export template for testing', 1)
ON CONFLICT (slug) DO NOTHING;

-- Clean up any existing field mappings for the sample export to avoid duplicates
DELETE FROM field_mapping WHERE universal_export_id = (SELECT id FROM universal_export WHERE slug = 'sample-csv-export-12345678');

-- Insert sample field mappings
INSERT INTO field_mapping (universal_export_id, source_field, target_field, field_type, is_required, default_value, sort_order, description)
SELECT 
    ue.id,
    'id',
    'Product ID',
    'number',
    true,
    NULL,
    0,
    'Product unique identifier'
FROM universal_export ue WHERE ue.slug = 'sample-csv-export-12345678'
ON CONFLICT DO NOTHING;

INSERT INTO field_mapping (universal_export_id, source_field, target_field, field_type, is_required, default_value, sort_order, description)
SELECT 
    ue.id,
    'name',
    'Product Name',
    'string',
    true,
    NULL,
    1,
    'Product display name'
FROM universal_export ue WHERE ue.slug = 'sample-csv-export-12345678'
ON CONFLICT DO NOTHING;

INSERT INTO field_mapping (universal_export_id, source_field, target_field, field_type, is_required, default_value, sort_order, description)
SELECT 
    ue.id,
    'sku',
    'SKU',
    'string',
    true,
    NULL,
    2,
    'Product SKU code'
FROM universal_export ue WHERE ue.slug = 'sample-csv-export-12345678'
ON CONFLICT DO NOTHING;

-- Feeds-aligned pricing structure
INSERT INTO field_mapping (universal_export_id, source_field, target_field, field_type, is_required, default_value, sort_order, description)
SELECT
    ue.id,
    'price',
    'Price',
    'number',
    false,
    '0.00',
    3,
    'Product price (original price)'
FROM universal_export ue WHERE ue.slug = 'sample-csv-export-12345678'
ON CONFLICT DO NOTHING;

INSERT INTO field_mapping (universal_export_id, source_field, target_field, field_type, is_required, default_value, sort_order, description)
SELECT
    ue.id,
    'salePrice',
    'Sale Price',
    'number',
    false,
    '0.00',
    4,
    'Product sale price (discounted price)'
FROM universal_export ue WHERE ue.slug = 'sample-csv-export-12345678'
ON CONFLICT DO NOTHING;

INSERT INTO field_mapping (universal_export_id, source_field, target_field, field_type, is_required, default_value, sort_order, description)
SELECT
    ue.id,
    'brand',
    'Brand Name',
    'string',
    false,
    'Unknown',
    5,
    'Product brand'
FROM universal_export ue WHERE ue.slug = 'sample-csv-export-12345678'
ON CONFLICT DO NOTHING;

INSERT INTO field_mapping (universal_export_id, source_field, target_field, field_type, is_required, default_value, sort_order, description)
SELECT
    ue.id,
    'images',
    'Images',
    'image',
    false,
    NULL,
    6,
    'Product images'
FROM universal_export ue WHERE ue.slug = 'sample-csv-export-12345678'
ON CONFLICT DO NOTHING;

-- Add additional commonly used fields
INSERT INTO field_mapping (universal_export_id, source_field, target_field, field_type, is_required, default_value, sort_order, description)
SELECT
    ue.id,
    'title',
    'Title',
    'string',
    false,
    NULL,
    7,
    'Product title'
FROM universal_export ue WHERE ue.slug = 'sample-csv-export-12345678'
ON CONFLICT DO NOTHING;

INSERT INTO field_mapping (universal_export_id, source_field, target_field, field_type, is_required, default_value, sort_order, description)
SELECT
    ue.id,
    'description',
    'Description',
    'string',
    false,
    NULL,
    8,
    'Product description'
FROM universal_export ue WHERE ue.slug = 'sample-csv-export-12345678'
ON CONFLICT DO NOTHING;

INSERT INTO field_mapping (universal_export_id, source_field, target_field, field_type, is_required, default_value, sort_order, description)
SELECT
    ue.id,
    'link',
    'Link',
    'string',
    false,
    NULL,
    9,
    'Product link'
FROM universal_export ue WHERE ue.slug = 'sample-csv-export-12345678'
ON CONFLICT DO NOTHING;

INSERT INTO field_mapping (universal_export_id, source_field, target_field, field_type, is_required, default_value, sort_order, description)
SELECT
    ue.id,
    'currency',
    'Currency',
    'string',
    false,
    'BDT',
    10,
    'Product currency'
FROM universal_export ue WHERE ue.slug = 'sample-csv-export-12345678'
ON CONFLICT DO NOTHING;

INSERT INTO field_mapping (universal_export_id, source_field, target_field, field_type, is_required, default_value, sort_order, description)
SELECT
    ue.id,
    'quantity',
    'Quantity',
    'number',
    false,
    '0',
    11,
    'Product quantity'
FROM universal_export ue WHERE ue.slug = 'sample-csv-export-12345678'
ON CONFLICT DO NOTHING;

INSERT INTO field_mapping (universal_export_id, source_field, target_field, field_type, is_required, default_value, sort_order, description)
SELECT
    ue.id,
    'availability',
    'Availability',
    'string',
    false,
    'in stock',
    12,
    'Product availability'
FROM universal_export ue WHERE ue.slug = 'sample-csv-export-12345678'
ON CONFLICT DO NOTHING;

INSERT INTO field_mapping (universal_export_id, source_field, target_field, field_type, is_required, default_value, sort_order, description)
SELECT
    ue.id,
    'status',
    'Status',
    'string',
    false,
    'active',
    13,
    'Product status'
FROM universal_export ue WHERE ue.slug = 'sample-csv-export-12345678'
ON CONFLICT DO NOTHING;

INSERT INTO field_mapping (universal_export_id, source_field, target_field, field_type, is_required, default_value, sort_order, description)
SELECT
    ue.id,
    'condition',
    'Condition',
    'string',
    false,
    'new',
    14,
    'Product condition'
FROM universal_export ue WHERE ue.slug = 'sample-csv-export-12345678'
ON CONFLICT DO NOTHING;

INSERT INTO field_mapping (universal_export_id, source_field, target_field, field_type, is_required, default_value, sort_order, description)
SELECT
    ue.id,
    'imageLink',
    'Image Link',
    'image',
    false,
    NULL,
    15,
    'Primary product image'
FROM universal_export ue WHERE ue.slug = 'sample-csv-export-12345678'
ON CONFLICT DO NOTHING;

INSERT INTO field_mapping (universal_export_id, source_field, target_field, field_type, is_required, default_value, sort_order, description)
SELECT
    ue.id,
    'additionalImageLink',
    'Additional Images',
    'image',
    false,
    NULL,
    16,
    'Additional product images'
FROM universal_export ue WHERE ue.slug = 'sample-csv-export-12345678'
ON CONFLICT DO NOTHING;

INSERT INTO field_mapping (universal_export_id, source_field, target_field, field_type, is_required, default_value, sort_order, description)
SELECT
    ue.id,
    'isActive',
    'Is Active',
    'boolean',
    false,
    'true',
    17,
    'Product active status'
FROM universal_export ue WHERE ue.slug = 'sample-csv-export-12345678'
ON CONFLICT DO NOTHING;

INSERT INTO field_mapping (universal_export_id, source_field, target_field, field_type, is_required, default_value, sort_order, description)
SELECT
    ue.id,
    'createdAt',
    'Created At',
    'date',
    false,
    NULL,
    18,
    'Product creation date'
FROM universal_export ue WHERE ue.slug = 'sample-csv-export-12345678'
ON CONFLICT DO NOTHING;

INSERT INTO field_mapping (universal_export_id, source_field, target_field, field_type, is_required, default_value, sort_order, description)
SELECT
    ue.id,
    'updatedAt',
    'Updated At',
    'date',
    false,
    NULL,
    19,
    'Product update date'
FROM universal_export ue WHERE ue.slug = 'sample-csv-export-12345678'
ON CONFLICT DO NOTHING;

INSERT INTO field_mapping (universal_export_id, source_field, target_field, field_type, is_required, default_value, sort_order, description)
SELECT
    ue.id,
    'categories',
    'Categories',
    'array',
    false,
    NULL,
    20,
    'Product categories'
FROM universal_export ue WHERE ue.slug = 'sample-csv-export-12345678'
ON CONFLICT DO NOTHING;

-- Add some sample products to the export (assuming products with IDs 1-5 exist)
INSERT INTO universal_export_products (universal_export_id, product_id)
SELECT ue.id, p.id
FROM universal_export ue
CROSS JOIN (SELECT id FROM product LIMIT 5) p
WHERE ue.slug = 'sample-csv-export-12345678'
ON CONFLICT DO NOTHING;

-- Display created tables info
SELECT 
    'universal_export' as table_name,
    COUNT(*) as record_count
FROM universal_export
UNION ALL
SELECT 
    'field_mapping' as table_name,
    COUNT(*) as record_count
FROM field_mapping
UNION ALL
SELECT 
    'universal_export_products' as table_name,
    COUNT(*) as record_count
FROM universal_export_products;

COMMIT;
