import { MigrationInterface, QueryRunner } from "typeorm";

export class Cart1739073630313 implements MigrationInterface {
    name = 'Cart1739073630313'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "cart_item" ("created_by" integer, "updated_by" integer, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "remarks" character varying, "cartUUID" character varying NOT NULL, "name" character varying NOT NULL, "slug" character varying NOT NULL, "unit_price" numeric(10,2), "discount_price" numeric(10,2), "quantity" integer NOT NULL, "stock" integer NOT NULL, "is_multi_variant" boolean NOT NULL DEFAULT false, "sku" character varying, "currency" character varying NOT NULL, "brand" character varying NOT NULL, "color_variant" jsonb, "size_variant" jsonb, "categories_arr" json NOT NULL, "featured_image" jsonb, "product_id" integer, CONSTRAINT "PK_bd94725aa84f8cf37632bcde997" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "cart" ("created_by" integer, "updated_by" integer, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "remarks" character varying, "status" character varying NOT NULL DEFAULT 'active', "user_id" integer, "cartUUID" character varying NOT NULL, "is_modal_open" boolean NOT NULL DEFAULT false, "active_language_id" integer NOT NULL DEFAULT '1', "country_id" integer NOT NULL, "coupon_id" integer, "is_billing_shipping_same" boolean, "subtotal" numeric(10,2), "discount" numeric(10,2), "tax" numeric(10,2), "total" numeric(10,2), "total_after_discount" numeric(10,2), "shipping_charge" numeric(10,2), "cod_charge" numeric(10,2), "courier_service_id" integer, "courier_id" integer, "currency_id" integer, "subscriber_id" integer, "has_shipping_address" boolean NOT NULL DEFAULT false, "shipping_address" jsonb NOT NULL DEFAULT '{}', "billing_address" jsonb NOT NULL DEFAULT '{}', "delivery_type" character varying, "payment_method" character varying, "countries" jsonb NOT NULL DEFAULT '[]', "billing_states" jsonb NOT NULL DEFAULT '[]', "billing_cities" jsonb NOT NULL DEFAULT '[]', "shipping_states" jsonb NOT NULL DEFAULT '[]', "shipping_cities" jsonb NOT NULL DEFAULT '[]', CONSTRAINT "UQ_845ca68ceaf9239e3c19d900d07" UNIQUE ("cartUUID"), CONSTRAINT "PK_c524ec48751b9b5bcfbf6e59be7" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "cart_item" ADD CONSTRAINT "FK_67a2e8406e01ffa24ff9026944e" FOREIGN KEY ("product_id") REFERENCES "product"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "cart_item" ADD CONSTRAINT "FK_5853e56b16a9b97f9623cebe667" FOREIGN KEY ("cartUUID") REFERENCES "cart"("cartUUID") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "cart" ADD CONSTRAINT "FK_447b7e48ad18b8dbe8c791c5b8c" FOREIGN KEY ("coupon_id") REFERENCES "coupon"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "cart" DROP CONSTRAINT "FK_447b7e48ad18b8dbe8c791c5b8c"`);
        await queryRunner.query(`ALTER TABLE "cart_item" DROP CONSTRAINT "FK_5853e56b16a9b97f9623cebe667"`);
        await queryRunner.query(`ALTER TABLE "cart_item" DROP CONSTRAINT "FK_67a2e8406e01ffa24ff9026944e"`);
        await queryRunner.query(`DROP TABLE "cart"`);
        await queryRunner.query(`DROP TABLE "cart_item"`);
    }

}
