CREATE TABLE "fortunate_wheel" ("created_by" integer, "updated_by" integer, "max_retry" integer NOT NULL DEFAULT 1, "background_image" character varying, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "title" character varying NOT NULL, "remarks" character varying, "country_id" integer NOT NULL, "slices" jsonb NOT NULL, CONSTRAINT "PK_ee86b23b86460a6c11581d65944" PRIMARY KEY ("id"));
CREATE TABLE "fortunate_wheel_usage" ("created_by" integer, "updated_by" integer, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "usage_count" integer NOT NULL DEFAULT 0, "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "remarks" character varying, "wheel_id" integer NOT NULL, "user_phone" character varying, "user_email" character varying, CONSTRAINT "PK_3949aefddf4a8f57031d1957f7f" PRIMARY KEY ("id"));
ALTER TABLE "fortunate_wheel_usage" ADD CONSTRAINT "FK_e5aff194b87400504f3dd87a819" FOREIGN KEY ("wheel_id") REFERENCES "fortunate_wheel"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
CREATE UNIQUE INDEX user_email_1747989989662_index ON "fortunate_wheel_usage" USING btree ("wheel_id", "user_email");
CREATE UNIQUE INDEX user_phone_1747990072578_index ON "fortunate_wheel_usage" USING btree ("wheel_id", "user_phone");