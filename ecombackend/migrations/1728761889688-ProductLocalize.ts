import { MigrationInterface, QueryRunner } from 'typeorm';

export class ProductLocalize1728761889688 implements MigrationInterface {
  name = 'ProductLocalize1728761889688';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "product_localization" ("created_by" integer, "updated_by" integer, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "remarks" character varying, "name" character varying, "description" character varying, "material_care" character varying, "shipping_return" character varying, "wow_factors" character varying, "tag_line1" character varying, "tag_line2" character varying, "tag_line2_type" character varying, "product_tags" text array, "product_id" integer, "language_id" integer, "meta_title" character varying, "meta_description" text, CONSTRAINT "UQ_93cb0579ab34174d798a7508d69" UNIQUE ("product_id", "language_id"), CONSTRAINT "PK_610c2368e2537458741abaf4940" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_c33199b2d82c2dd98817b45fe7" ON "product_localization" ("product_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_5a26e726d93e4121d86d8c1067" ON "product_localization" ("language_id") `,
    );
    await queryRunner.query(`ALTER TABLE "seo_meta" ADD "locale" jsonb`);
    await queryRunner.query(
      `ALTER TABLE "product_localization" ADD CONSTRAINT "FK_c33199b2d82c2dd98817b45fe72" FOREIGN KEY ("product_id") REFERENCES "product"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "product_localization" ADD CONSTRAINT "FK_5a26e726d93e4121d86d8c1067a" FOREIGN KEY ("language_id") REFERENCES "country"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "product_localization" DROP CONSTRAINT "FK_5a26e726d93e4121d86d8c1067a"`,
    );
    await queryRunner.query(
      `ALTER TABLE "product_localization" DROP CONSTRAINT "FK_c33199b2d82c2dd98817b45fe72"`,
    );
    await queryRunner.query(`ALTER TABLE "seo_meta" DROP COLUMN "locale"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_5a26e726d93e4121d86d8c1067"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_c33199b2d82c2dd98817b45fe7"`,
    );
    await queryRunner.query(`DROP TABLE "product_localization"`);
  }
}
