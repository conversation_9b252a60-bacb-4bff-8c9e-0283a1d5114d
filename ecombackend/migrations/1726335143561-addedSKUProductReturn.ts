import { MigrationInterface, QueryRunner } from "typeorm";

export class AddedSKUProductReturn1726335143561 implements MigrationInterface {
    name = 'AddedSKUProductReturn1726335143561'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "product_refund" ADD "sku" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "product_refund" DROP COLUMN "sku"`);
    }

}
