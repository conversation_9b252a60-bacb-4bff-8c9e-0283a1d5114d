import { MigrationInterface, QueryRunner } from "typeorm";

export class  $npmConfigMigrationName1725805030475 implements MigrationInterface {
    name = ' $npmConfigMigrationName1725805030475'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "product_order_details" ADD "sku" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "product_order_details" DROP COLUMN "sku"`);
    }

}
