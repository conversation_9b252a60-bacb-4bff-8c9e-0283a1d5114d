import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddSocialLinkCountryTable1729666647418
  implements MigrationInterface
{
  name = 'AddSocialLinkCountryTable1729666647418';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "country" ADD "x_link" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "country" ADD "yt_link" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "country" ADD "fb_link" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "country" ADD "insta_link" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "country" ADD "tiktok_link" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "country" ADD "linkedin_link" character varying`,
    );
    // product_refund_type

    await queryRunner.query(
      `ALTER TABLE "product_refund_type" ADD "sku" character varying`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "country" DROP COLUMN "linkedin_link"`,
    );
    await queryRunner.query(`ALTER TABLE "country" DROP COLUMN "tiktok_link"`);
    await queryRunner.query(`ALTER TABLE "country" DROP COLUMN "insta_link"`);
    await queryRunner.query(`ALTER TABLE "country" DROP COLUMN "fb_link"`);
    await queryRunner.query(`ALTER TABLE "country" DROP COLUMN "yt_link"`);
    await queryRunner.query(`ALTER TABLE "country" DROP COLUMN "x_link"`);
    await queryRunner.query(`ALTER TABLE "country" DROP COLUMN "x_link"`);
    // product_refund_type
    await queryRunner.query(
      `ALTER TABLE "product_refund_type" DROP COLUMN "sku"`,
    );
  }
}
