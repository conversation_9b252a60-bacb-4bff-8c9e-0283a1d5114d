# Affiliate System Migration Files

This directory contains migration files for the affiliate system. The affiliate system allows users to become affiliates and earn commissions from referrals and discount code usage.

## Migration Files Overview

### 1. TypeORM Migration Files

#### `1748900000000-update-affiliate-schema.ts`
- **Purpose**: Updates existing affiliate tables to match current entity definitions
- **Use Case**: When affiliate tables already exist but need schema updates
- **What it does**:
  - Adds missing columns to existing tables
  - Updates enum types with new values
  - Creates missing indexes and foreign key constraints
  - Makes nullable fields that should be nullable

#### How to run TypeORM migrations:
```bash
# Run all pending migrations
npm run typeorm:run-migrations

# Or using the specific script
npx typeorm-ts-node-commonjs migration:run -- -d ./typeorm.config.ts
```

### 2. SQL Migration Files

#### `affiliate-schema-update.sql`
- **Purpose**: SQL version of the schema update migration
- **Use Case**: Direct database updates or when TypeORM migrations are not preferred
- **Features**: Uses conditional logic to avoid errors if columns/constraints already exist

#### `affiliate-complete-schema.sql`
- **Purpose**: Complete affiliate system schema creation from scratch
- **Use Case**: Fresh installations or when affiliate tables don't exist
- **What it creates**:
  - All affiliate system tables with proper structure
  - All enum types
  - Foreign key constraints
  - Performance indexes
  - Table and column comments

#### How to run SQL migrations:
```bash
# Connect to your PostgreSQL database and run:
psql -h localhost -U your_username -d your_database -f migrations/affiliate-schema-update.sql

# Or for complete schema creation:
psql -h localhost -U your_username -d your_database -f migrations/affiliate-complete-schema.sql
```

## Database Tables Created/Updated

### 1. `affiliate_profiles`
Main table storing affiliate user information and commission settings.

**Key columns:**
- `user_id` - Links to the user table
- `affiliate_code` - Unique code for the affiliate
- `status` - pending, active, suspended, rejected
- `commission_type` - percentage or fixed
- `commission_rate` - Default commission rate
- `country_id` - Affiliate's country
- `payment_details` - JSON field for payment information

### 2. `affiliate_discounts`
Links affiliates to specific discount codes with custom commission rates.

**Key columns:**
- `affiliate_id` - Links to affiliate_profiles
- `discount_id` - Links to discount table
- `status` - active, inactive, expired, pending, rejected
- `commission_type` - Override commission type for this discount
- `commission_rate` - Override commission rate for this discount

### 3. `affiliate_clicks`
Tracks clicks on affiliate links for analytics and attribution.

**Key columns:**
- `affiliate_id` - Links to affiliate_profiles
- `affiliate_discount_id` - Links to specific discount campaign
- `session_id` - User session tracking
- `source` - direct_link, coupon_code, social_media, etc.
- `converted` - Whether the click resulted in a conversion

### 4. `affiliate_commissions`
Records commission earnings for affiliates.

**Key columns:**
- `affiliate_id` - Links to affiliate_profiles
- `affiliate_discount_id` - Links to affiliate_discounts
- `order_id` - Links to product_order
- `status` - pending, approved, paid, cancelled, refunded
- `commission_amount` - Calculated commission amount
- `source` - order, discount_usage, referral, bonus

### 5. `affiliate_conversions`
Tracks successful conversions from affiliate traffic.

**Key columns:**
- `affiliate_id` - Links to affiliate_profiles
- `affiliate_discount_id` - Links to affiliate_discounts
- `type` - purchase, signup, lead, download
- `conversion_value` - Value of the conversion
- `commission_earned` - Commission earned from this conversion

### 6. `affiliate_discount_usages`
Records usage of affiliate discount codes.

**Key columns:**
- `affiliate_discount_id` - Links to affiliate_discounts
- `discount_usage_id` - Links to discount_usage (nullable)
- `order_id` - Links to product_order
- `commission_amount` - Commission earned from this usage

## Migration Strategy

### For Existing Systems
1. **Check current state**: Verify which affiliate tables already exist
2. **Run update migration**: Use `1748900000000-update-affiliate-schema.ts` or `affiliate-schema-update.sql`
3. **Verify schema**: Check that all columns and constraints are properly created

### For New Systems
1. **Run complete schema**: Use `affiliate-complete-schema.sql` to create everything from scratch
2. **Verify creation**: Check that all tables, indexes, and constraints are created

### Rollback Strategy
- TypeORM migrations include `down()` methods for rollback
- SQL migrations are designed to be idempotent (safe to run multiple times)
- Always backup your database before running migrations in production

## Performance Considerations

The migrations create the following indexes for optimal performance:
- Primary key indexes (automatic)
- Foreign key indexes for all relationships
- Status field indexes for filtering
- Session and tracking field indexes for analytics queries
- UUID and invoice number indexes for lookups

## Troubleshooting

### Common Issues:
1. **Enum type conflicts**: If enum types already exist with different values, you may need to manually update them
2. **Foreign key constraints**: Ensure referenced tables (user, country, discount, product_order) exist before running migrations
3. **Column conflicts**: If columns exist with different data types, manual intervention may be required

### Verification Queries:
```sql
-- Check if all affiliate tables exist
SELECT table_name FROM information_schema.tables 
WHERE table_name LIKE 'affiliate_%' AND table_schema = 'public';

-- Check enum types
SELECT typname FROM pg_type WHERE typname LIKE '%affiliate%';

-- Check foreign key constraints
SELECT constraint_name, table_name FROM information_schema.table_constraints 
WHERE constraint_type = 'FOREIGN KEY' AND table_name LIKE 'affiliate_%';
```

## Support

If you encounter issues with these migrations:
1. Check the database logs for specific error messages
2. Verify that all prerequisite tables exist
3. Ensure you have proper database permissions
4. Consider running migrations in a test environment first
