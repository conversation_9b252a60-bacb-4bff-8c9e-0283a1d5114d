import { MigrationInterface, QueryRunner } from 'typeorm';

export class RemoveAffiliateC<PERSON>1748400000000 implements MigrationInterface {
  name = 'RemoveAffiliateCode1748400000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Remove the unique constraint on affiliate_code first
    await queryRunner.query(`
      ALTER TABLE "affiliate_profiles" 
      DROP CONSTRAINT IF EXISTS "UQ_affiliate_profiles_affiliate_code"
    `);

    // Drop the index on affiliate_code if it exists
    await queryRunner.query(`
      DROP INDEX IF EXISTS "IDX_affiliate_profiles_affiliate_code"
    `);

    // Remove the affiliate_code column
    await queryRunner.query(`
      ALTER TABLE "affiliate_profiles" 
      DROP COLUMN IF EXISTS "affiliate_code"
    `);

    // Make discount_id NOT NULL since it's now required
    await queryRunner.query(`
      ALTER TABLE "affiliate_profiles" 
      ALTER COLUMN "discount_id" SET NOT NULL
    `);

    // Add unique constraint on discount_id to ensure one affiliate per discount
    await queryRunner.query(`
      ALTER TABLE "affiliate_profiles" 
      ADD CONSTRAINT "UQ_affiliate_profiles_discount_id" UNIQUE ("discount_id")
    `);

    // Add foreign key constraint to discount table if not exists
    await queryRunner.query(`
      ALTER TABLE "affiliate_profiles" 
      ADD CONSTRAINT "FK_affiliate_profiles_discount_id" 
      FOREIGN KEY ("discount_id") REFERENCES "discounts"("id") 
      ON DELETE CASCADE ON UPDATE NO ACTION
    `);

    // Create index on discount_id for better performance
    await queryRunner.query(`
      CREATE INDEX "IDX_affiliate_profiles_discount_id" 
      ON "affiliate_profiles" ("discount_id")
    `);

    // Create index on status for better performance
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_affiliate_profiles_status" 
      ON "affiliate_profiles" ("status")
    `);

    // Create composite index for common queries
    await queryRunner.query(`
      CREATE INDEX "IDX_affiliate_profiles_discount_status" 
      ON "affiliate_profiles" ("discount_id", "status")
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop the indexes
    await queryRunner.query(`
      DROP INDEX IF EXISTS "IDX_affiliate_profiles_discount_status"
    `);
    
    await queryRunner.query(`
      DROP INDEX IF EXISTS "IDX_affiliate_profiles_status"
    `);
    
    await queryRunner.query(`
      DROP INDEX IF EXISTS "IDX_affiliate_profiles_discount_id"
    `);

    // Drop foreign key constraint
    await queryRunner.query(`
      ALTER TABLE "affiliate_profiles" 
      DROP CONSTRAINT IF EXISTS "FK_affiliate_profiles_discount_id"
    `);

    // Drop unique constraint on discount_id
    await queryRunner.query(`
      ALTER TABLE "affiliate_profiles" 
      DROP CONSTRAINT IF EXISTS "UQ_affiliate_profiles_discount_id"
    `);

    // Make discount_id nullable again
    await queryRunner.query(`
      ALTER TABLE "affiliate_profiles" 
      ALTER COLUMN "discount_id" DROP NOT NULL
    `);

    // Add back the affiliate_code column
    await queryRunner.query(`
      ALTER TABLE "affiliate_profiles" 
      ADD COLUMN "affiliate_code" character varying(50)
    `);

    // Create unique constraint on affiliate_code
    await queryRunner.query(`
      ALTER TABLE "affiliate_profiles" 
      ADD CONSTRAINT "UQ_affiliate_profiles_affiliate_code" UNIQUE ("affiliate_code")
    `);

    // Create index on affiliate_code
    await queryRunner.query(`
      CREATE INDEX "IDX_affiliate_profiles_affiliate_code" 
      ON "affiliate_profiles" ("affiliate_code")
    `);
  }
}
