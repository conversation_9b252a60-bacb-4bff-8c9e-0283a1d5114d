import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddCodeColumnInCourierService1723153077026
  implements MigrationInterface
{
  name = 'AddCodeColumnInCourierService1723153077026';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "courier_services" ADD "code" character varying NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "courier_services" ADD CONSTRAINT "UQ_995c87cff497b8c9c04b63b359b" UNIQUE ("code")`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "courier_services" DROP CONSTRAINT "UQ_995c87cff497b8c9c04b63b359b"`,
    );
    await queryRunner.query(
      `ALTER TABLE "courier_services" DROP COLUMN "code"`,
    );
  }
}
