import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateAffiliateSchema1748900000000 implements MigrationInterface {
  name = 'UpdateAffiliateSchema1748900000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add missing columns to affiliate_profiles table
    const profileColumns = await queryRunner.query(`
      SELECT column_name FROM information_schema.columns 
      WHERE table_name = 'affiliate_profiles' AND table_schema = 'public'
    `);
    
    const existingProfileColumns = profileColumns.map(row => row.column_name);

    if (!existingProfileColumns.includes('country_id')) {
      await queryRunner.query(`ALTER TABLE "affiliate_profiles" ADD "country_id" integer`);
    }

    if (!existingProfileColumns.includes('notes')) {
      await queryRunner.query(`ALTER TABLE "affiliate_profiles" ADD "notes" character varying`);
    }

    // Add missing columns to affiliate_commissions table
    const commissionColumns = await queryRunner.query(`
      SELECT column_name FROM information_schema.columns 
      WHERE table_name = 'affiliate_commissions' AND table_schema = 'public'
    `);
    
    const existingCommissionColumns = commissionColumns.map(row => row.column_name);

    if (!existingCommissionColumns.includes('country_id')) {
      await queryRunner.query(`ALTER TABLE "affiliate_commissions" ADD "country_id" character varying`);
    }

    if (!existingCommissionColumns.includes('uuid')) {
      await queryRunner.query(`ALTER TABLE "affiliate_commissions" ADD "uuid" character varying`);
    }

    if (!existingCommissionColumns.includes('invoice_no')) {
      await queryRunner.query(`ALTER TABLE "affiliate_commissions" ADD "invoice_no" character varying`);
    }

    if (!existingCommissionColumns.includes('commission_type')) {
      await queryRunner.query(`ALTER TABLE "affiliate_commissions" ADD "commission_type" character varying`);
    }

    // Update affiliate_discounts status enum to include missing values
    await queryRunner.query(`
      ALTER TYPE "public"."affiliate_discounts_status_enum" 
      ADD VALUE IF NOT EXISTS 'pending'
    `);
    
    await queryRunner.query(`
      ALTER TYPE "public"."affiliate_discounts_status_enum" 
      ADD VALUE IF NOT EXISTS 'rejected'
    `);

    // Add commission settings to affiliate_discounts
    const discountColumns = await queryRunner.query(`
      SELECT column_name FROM information_schema.columns 
      WHERE table_name = 'affiliate_discounts' AND table_schema = 'public'
    `);
    
    const existingDiscountColumns = discountColumns.map(row => row.column_name);

    if (!existingDiscountColumns.includes('commission_type')) {
      await queryRunner.query(`
        ALTER TABLE "affiliate_discounts" 
        ADD "commission_type" character varying
      `);
    }

    if (!existingDiscountColumns.includes('commission_rate')) {
      await queryRunner.query(`
        ALTER TABLE "affiliate_discounts" 
        ADD "commission_rate" numeric(5,2)
      `);
    }

    // Add foreign key constraint for affiliate_profiles.country_id if it doesn't exist
    const profileConstraints = await queryRunner.query(`
      SELECT constraint_name FROM information_schema.table_constraints 
      WHERE table_name = 'affiliate_profiles' 
      AND constraint_name = 'FK_affiliate_profiles_country_id'
    `);

    if (profileConstraints.length === 0) {
      await queryRunner.query(`
        ALTER TABLE "affiliate_profiles" 
        ADD CONSTRAINT "FK_affiliate_profiles_country_id" 
        FOREIGN KEY ("country_id") REFERENCES "country"("id") ON DELETE SET NULL
      `);
    }

    // Make discount_usage_id nullable in affiliate_discount_usages
    await queryRunner.query(`
      ALTER TABLE "affiliate_discount_usages" 
      ALTER COLUMN "discount_usage_id" DROP NOT NULL
    `);

    // Create indexes for better performance if they don't exist
    const indexes = [
      { table: 'affiliate_profiles', column: 'country_id', name: 'IDX_affiliate_profiles_country_id' },
      { table: 'affiliate_commissions', column: 'uuid', name: 'IDX_affiliate_commissions_uuid' },
      { table: 'affiliate_commissions', column: 'invoice_no', name: 'IDX_affiliate_commissions_invoice_no' },
      { table: 'affiliate_discounts', column: 'commission_type', name: 'IDX_affiliate_discounts_commission_type' }
    ];

    for (const index of indexes) {
      const existingIndex = await queryRunner.query(`
        SELECT indexname FROM pg_indexes 
        WHERE tablename = '${index.table}' AND indexname = '${index.name}'
      `);
      
      if (existingIndex.length === 0) {
        await queryRunner.query(`
          CREATE INDEX "${index.name}" ON "${index.table}" ("${index.column}")
        `);
      }
    }

    // Add comments to new columns
    await queryRunner.query(`
      COMMENT ON COLUMN "affiliate_profiles"."country_id" IS 
      'Country where the affiliate is located'
    `);

    await queryRunner.query(`
      COMMENT ON COLUMN "affiliate_profiles"."notes" IS 
      'Internal notes about the affiliate'
    `);

    await queryRunner.query(`
      COMMENT ON COLUMN "affiliate_commissions"."country_id" IS 
      'Country code for the commission transaction'
    `);

    await queryRunner.query(`
      COMMENT ON COLUMN "affiliate_commissions"."uuid" IS 
      'Unique identifier for the commission'
    `);

    await queryRunner.query(`
      COMMENT ON COLUMN "affiliate_commissions"."invoice_no" IS 
      'Invoice number associated with the commission'
    `);

    await queryRunner.query(`
      COMMENT ON COLUMN "affiliate_discounts"."commission_type" IS 
      'Override commission type for this specific discount'
    `);

    await queryRunner.query(`
      COMMENT ON COLUMN "affiliate_discounts"."commission_rate" IS 
      'Override commission rate for this specific discount'
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove foreign key constraint
    await queryRunner.query(`
      ALTER TABLE "affiliate_profiles" 
      DROP CONSTRAINT IF EXISTS "FK_affiliate_profiles_country_id"
    `);

    // Remove indexes
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_affiliate_profiles_country_id"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_affiliate_commissions_uuid"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_affiliate_commissions_invoice_no"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_affiliate_discounts_commission_type"`);

    // Remove added columns
    await queryRunner.query(`ALTER TABLE "affiliate_profiles" DROP COLUMN IF EXISTS "country_id"`);
    await queryRunner.query(`ALTER TABLE "affiliate_profiles" DROP COLUMN IF EXISTS "notes"`);
    await queryRunner.query(`ALTER TABLE "affiliate_commissions" DROP COLUMN IF EXISTS "country_id"`);
    await queryRunner.query(`ALTER TABLE "affiliate_commissions" DROP COLUMN IF EXISTS "uuid"`);
    await queryRunner.query(`ALTER TABLE "affiliate_commissions" DROP COLUMN IF EXISTS "invoice_no"`);
    await queryRunner.query(`ALTER TABLE "affiliate_commissions" DROP COLUMN IF EXISTS "commission_type"`);
    await queryRunner.query(`ALTER TABLE "affiliate_discounts" DROP COLUMN IF EXISTS "commission_type"`);
    await queryRunner.query(`ALTER TABLE "affiliate_discounts" DROP COLUMN IF EXISTS "commission_rate"`);

    // Make discount_usage_id NOT NULL again
    await queryRunner.query(`
      ALTER TABLE "affiliate_discount_usages" 
      ALTER COLUMN "discount_usage_id" SET NOT NULL
    `);
  }
}
