import { MigrationInterface, QueryRunner } from "typeorm";

export class AddedSetupModule1720799384128 implements MigrationInterface {
    name = 'AddedSetupModule1720799384128'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "setup" ("created_by" integer, "updated_by" integer, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "remarks" character varying, "key" character varying NOT NULL, "value" character varying NOT NULL, CONSTRAINT "UQ_906c39ee0b73b841532a3ed997e" UNIQUE ("key"), CONSTRAINT "PK_4a8b5a3c999d9548c34af3b1516" PRIMARY KEY ("id"))`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE "setup"`);
    }

}
