-- =====================================================
-- Affiliate System Schema Update Migration
-- =====================================================
-- This SQL script updates the affiliate system tables to match
-- the current entity definitions in the codebase.
-- 
-- Run this script to bring your database schema in sync with
-- the TypeORM entities.
-- =====================================================

-- Add missing columns to affiliate_profiles table
DO $$
BEGIN
    -- Add country_id column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'affiliate_profiles' 
        AND column_name = 'country_id'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE "affiliate_profiles" ADD COLUMN "country_id" integer;
        COMMENT ON COLUMN "affiliate_profiles"."country_id" IS 'Country where the affiliate is located';
    END IF;

    -- Add notes column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'affiliate_profiles' 
        AND column_name = 'notes'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE "affiliate_profiles" ADD COLUMN "notes" character varying;
        COMMENT ON COLUMN "affiliate_profiles"."notes" IS 'Internal notes about the affiliate';
    END IF;
END $$;

-- Add missing columns to affiliate_commissions table
DO $$
BEGIN
    -- Add country_id column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'affiliate_commissions' 
        AND column_name = 'country_id'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE "affiliate_commissions" ADD COLUMN "country_id" character varying;
        COMMENT ON COLUMN "affiliate_commissions"."country_id" IS 'Country code for the commission transaction';
    END IF;

    -- Add uuid column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'affiliate_commissions' 
        AND column_name = 'uuid'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE "affiliate_commissions" ADD COLUMN "uuid" character varying;
        COMMENT ON COLUMN "affiliate_commissions"."uuid" IS 'Unique identifier for the commission';
    END IF;

    -- Add invoice_no column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'affiliate_commissions' 
        AND column_name = 'invoice_no'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE "affiliate_commissions" ADD COLUMN "invoice_no" character varying;
        COMMENT ON COLUMN "affiliate_commissions"."invoice_no" IS 'Invoice number associated with the commission';
    END IF;

    -- Add commission_type column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'affiliate_commissions' 
        AND column_name = 'commission_type'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE "affiliate_commissions" ADD COLUMN "commission_type" character varying;
    END IF;
END $$;

-- Update affiliate_discounts status enum to include missing values
DO $$
BEGIN
    -- Add 'pending' to enum if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM pg_enum 
        WHERE enumlabel = 'pending' 
        AND enumtypid = (
            SELECT oid FROM pg_type WHERE typname = 'affiliate_discounts_status_enum'
        )
    ) THEN
        ALTER TYPE "public"."affiliate_discounts_status_enum" ADD VALUE 'pending';
    END IF;

    -- Add 'rejected' to enum if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM pg_enum 
        WHERE enumlabel = 'rejected' 
        AND enumtypid = (
            SELECT oid FROM pg_type WHERE typname = 'affiliate_discounts_status_enum'
        )
    ) THEN
        ALTER TYPE "public"."affiliate_discounts_status_enum" ADD VALUE 'rejected';
    END IF;
END $$;

-- Add commission settings to affiliate_discounts table
DO $$
BEGIN
    -- Add commission_type column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'affiliate_discounts' 
        AND column_name = 'commission_type'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE "affiliate_discounts" ADD COLUMN "commission_type" character varying;
        COMMENT ON COLUMN "affiliate_discounts"."commission_type" IS 'Override commission type for this specific discount';
    END IF;

    -- Add commission_rate column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'affiliate_discounts' 
        AND column_name = 'commission_rate'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE "affiliate_discounts" ADD COLUMN "commission_rate" numeric(5,2);
        COMMENT ON COLUMN "affiliate_discounts"."commission_rate" IS 'Override commission rate for this specific discount';
    END IF;
END $$;

-- Add foreign key constraint for affiliate_profiles.country_id
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE table_name = 'affiliate_profiles' 
        AND constraint_name = 'FK_affiliate_profiles_country_id'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE "affiliate_profiles" 
        ADD CONSTRAINT "FK_affiliate_profiles_country_id" 
        FOREIGN KEY ("country_id") REFERENCES "country"("id") ON DELETE SET NULL;
    END IF;
END $$;

-- Make discount_usage_id nullable in affiliate_discount_usages
ALTER TABLE "affiliate_discount_usages" 
ALTER COLUMN "discount_usage_id" DROP NOT NULL;

-- Create indexes for better performance
DO $$
BEGIN
    -- Index for affiliate_profiles.country_id
    IF NOT EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE tablename = 'affiliate_profiles' 
        AND indexname = 'IDX_affiliate_profiles_country_id'
    ) THEN
        CREATE INDEX "IDX_affiliate_profiles_country_id" ON "affiliate_profiles" ("country_id");
    END IF;

    -- Index for affiliate_commissions.uuid
    IF NOT EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE tablename = 'affiliate_commissions' 
        AND indexname = 'IDX_affiliate_commissions_uuid'
    ) THEN
        CREATE INDEX "IDX_affiliate_commissions_uuid" ON "affiliate_commissions" ("uuid");
    END IF;

    -- Index for affiliate_commissions.invoice_no
    IF NOT EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE tablename = 'affiliate_commissions' 
        AND indexname = 'IDX_affiliate_commissions_invoice_no'
    ) THEN
        CREATE INDEX "IDX_affiliate_commissions_invoice_no" ON "affiliate_commissions" ("invoice_no");
    END IF;

    -- Index for affiliate_discounts.commission_type
    IF NOT EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE tablename = 'affiliate_discounts' 
        AND indexname = 'IDX_affiliate_discounts_commission_type'
    ) THEN
        CREATE INDEX "IDX_affiliate_discounts_commission_type" ON "affiliate_discounts" ("commission_type");
    END IF;
END $$;

-- =====================================================
-- Migration completed successfully
-- =====================================================
-- The affiliate system schema has been updated to match
-- the current entity definitions.
-- 
-- Tables updated:
-- - affiliate_profiles: Added country_id, notes columns
-- - affiliate_commissions: Added country_id, uuid, invoice_no, commission_type columns
-- - affiliate_discounts: Added commission_type, commission_rate columns, updated status enum
-- - affiliate_discount_usages: Made discount_usage_id nullable
-- 
-- New indexes created for better performance.
-- Foreign key constraints added where appropriate.
-- =====================================================
