import { MigrationInterface, QueryRunner } from 'typeorm';

export class MakeDiscountUsageIdNullable1748700000000 implements MigrationInterface {
  name = 'MakeDiscountUsageIdNullable1748700000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Make discount_usage_id column nullable in affiliate_discount_usages table
    await queryRunner.query(`
      ALTER TABLE "affiliate_discount_usages" 
      ALTER COLUMN "discount_usage_id" DROP NOT NULL
    `);

    // Add comment to explain why this field can be null
    await queryRunner.query(`
      COMMENT ON COLUMN "affiliate_discount_usages"."discount_usage_id" IS 
      'Reference to discount_usage record. Can be null if the affiliate commission is created without a corresponding discount usage (e.g., manual commission or special cases).'
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove the comment
    await queryRunner.query(`
      COMMENT ON COLUMN "affiliate_discount_usages"."discount_usage_id" IS NULL
    `);

    // Note: We cannot safely make the column NOT NULL again without ensuring all records have values
    // This would require data migration to populate null values first
    await queryRunner.query(`
      -- WARNING: This rollback may fail if there are NULL values in discount_usage_id
      -- You may need to populate NULL values before running this migration
      ALTER TABLE "affiliate_discount_usages" 
      ALTER COLUMN "discount_usage_id" SET NOT NULL
    `);
  }
}
