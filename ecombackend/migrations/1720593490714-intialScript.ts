import { MigrationInterface, QueryRunner } from "typeorm";

export class IntialScript1720593490714 implements MigrationInterface {
    name = 'IntialScript1720593490714'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "subscription_popup" RENAME COLUMN "image_url" TO "image_gallery_id"`);
        await queryRunner.query(`CREATE TABLE "setup_home" ("created_by" integer, "updated_by" integer, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "remarks" character varying, "image_gallery_id" integer, "title" character varying, "sub_title" character varying, "redirect_link" character varying, "type" character varying NOT NULL, "country_id" integer, CONSTRAINT "PK_555730ebcec1e72a9ea1cdc243a" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "setup_home_partner_instagram" ("created_by" integer, "updated_by" integer, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "remarks" character varying, "image_gallery_id" integer, "title" character varying, "sub_title" character varying, "redirect_link" character varying, "type" character varying NOT NULL, "country_id" integer, CONSTRAINT "PK_463b5d4eeab76b5ec49df5971b9" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "product_tags" ("created_by" integer, "updated_by" integer, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "remarks" character varying, "tag_name" character varying NOT NULL, "slug" character varying NOT NULL, CONSTRAINT "UQ_999acf4b93e7fff390d9d3e5471" UNIQUE ("slug"), CONSTRAINT "PK_e96bca3cd7a592009f2c9dc6f3e" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "product_tags_meta" ("created_by" integer, "updated_by" integer, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "remarks" character varying, "product_id" integer NOT NULL, "product_tag_id" integer NOT NULL, CONSTRAINT "PK_f2c4e9773bf746e7945d2adf455" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "faq" ("created_by" integer, "updated_by" integer, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "remarks" character varying, "question" character varying NOT NULL, "answer" character varying NOT NULL, CONSTRAINT "PK_d6f5a52b1a96dd8d0591f9fbc47" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "product" ADD "tag_line2_type" character varying`);
        await queryRunner.query(`ALTER TABLE "product" ADD "limited_edition" boolean NOT NULL DEFAULT false`);
        await queryRunner.query(`ALTER TABLE "product" ADD "just_arrived" boolean NOT NULL DEFAULT false`);
        await queryRunner.query(`ALTER TABLE "product" ADD "most_popular" boolean NOT NULL DEFAULT false`);
        await queryRunner.query(`ALTER TABLE "product" ADD "customer_favorite" boolean NOT NULL DEFAULT false`);
        await queryRunner.query(`ALTER TABLE "product" ADD "best_value" boolean NOT NULL DEFAULT false`);
        await queryRunner.query(`ALTER TABLE "product" ADD "special_offer" boolean NOT NULL DEFAULT false`);
        await queryRunner.query(`ALTER TABLE "product" ADD "product_details" text`);
        await queryRunner.query(`ALTER TABLE "subscription_popup" DROP COLUMN "image_gallery_id"`);
        await queryRunner.query(`ALTER TABLE "subscription_popup" ADD "image_gallery_id" integer`);
        await queryRunner.query(`ALTER TABLE "setup_home" ADD CONSTRAINT "FK_a096d0e16ed7a153b00c25e0156" FOREIGN KEY ("image_gallery_id") REFERENCES "image_gallery"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "setup_home" ADD CONSTRAINT "FK_2a69acb650b7e26d00e5aa7aa12" FOREIGN KEY ("country_id") REFERENCES "country"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "setup_home_partner_instagram" ADD CONSTRAINT "FK_688f9bba164844ac23979ddeb9e" FOREIGN KEY ("image_gallery_id") REFERENCES "image_gallery"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "setup_home_partner_instagram" ADD CONSTRAINT "FK_a5c9da21be5cab086fc3e6dee9e" FOREIGN KEY ("country_id") REFERENCES "country"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "subscription_popup" ADD CONSTRAINT "FK_93057ba2adca2ede32142fce204" FOREIGN KEY ("image_gallery_id") REFERENCES "image_gallery"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "subscription_popup" DROP CONSTRAINT "FK_93057ba2adca2ede32142fce204"`);
        await queryRunner.query(`ALTER TABLE "setup_home_partner_instagram" DROP CONSTRAINT "FK_a5c9da21be5cab086fc3e6dee9e"`);
        await queryRunner.query(`ALTER TABLE "setup_home_partner_instagram" DROP CONSTRAINT "FK_688f9bba164844ac23979ddeb9e"`);
        await queryRunner.query(`ALTER TABLE "setup_home" DROP CONSTRAINT "FK_2a69acb650b7e26d00e5aa7aa12"`);
        await queryRunner.query(`ALTER TABLE "setup_home" DROP CONSTRAINT "FK_a096d0e16ed7a153b00c25e0156"`);
        await queryRunner.query(`ALTER TABLE "subscription_popup" DROP COLUMN "image_gallery_id"`);
        await queryRunner.query(`ALTER TABLE "subscription_popup" ADD "image_gallery_id" character varying`);
        await queryRunner.query(`ALTER TABLE "product" DROP COLUMN "product_details"`);
        await queryRunner.query(`ALTER TABLE "product" DROP COLUMN "special_offer"`);
        await queryRunner.query(`ALTER TABLE "product" DROP COLUMN "best_value"`);
        await queryRunner.query(`ALTER TABLE "product" DROP COLUMN "customer_favorite"`);
        await queryRunner.query(`ALTER TABLE "product" DROP COLUMN "most_popular"`);
        await queryRunner.query(`ALTER TABLE "product" DROP COLUMN "just_arrived"`);
        await queryRunner.query(`ALTER TABLE "product" DROP COLUMN "limited_edition"`);
        await queryRunner.query(`ALTER TABLE "product" DROP COLUMN "tag_line2_type"`);
        await queryRunner.query(`DROP TABLE "faq"`);
        await queryRunner.query(`DROP TABLE "product_tags_meta"`);
        await queryRunner.query(`DROP TABLE "product_tags"`);
        await queryRunner.query(`DROP TABLE "setup_home_partner_instagram"`);
        await queryRunner.query(`DROP TABLE "setup_home"`);
        await queryRunner.query(`ALTER TABLE "subscription_popup" RENAME COLUMN "image_gallery_id" TO "image_url"`);
    }

}
