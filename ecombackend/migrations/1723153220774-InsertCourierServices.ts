import { MigrationInterface, QueryRunner } from 'typeorm';

export class InsertCourierServices1723153220774 implements MigrationInterface {
  name = 'InsertCourierServices1723153220774';
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `INSERT INTO "courier_services" (name, code) VALUES ('MyGLS', 'mygls')`,
    );
    await queryRunner.query(
      `INSERT INTO courier_services (name, code) VALUES ('RedX', 'redx')`,
    );
    
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `DELETE FROM courier_services WHERE code = 'mygls'`,
    );
    await queryRunner.query(`DELETE FROM courier_services WHERE code = 'redx'`);
  }
}
