import { MigrationInterface, QueryRunner } from "typeorm";

export class CouponUsage1742116513909 implements MigrationInterface {
    name = 'CouponUsage1742116513909'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "coupon_usage" ("created_by" integer, "updated_by" integer, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "remarks" character varying, "coupon_id" integer NOT NULL, "user_email" character varying NOT NULL, "used_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_5727b2e426ee3e63c3f5e200e61" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "coupon_usage" ADD CONSTRAINT "FK_2a59fd09f9a61cfa1e422be2445" FOREIGN KEY ("coupon_id") REFERENCES "coupon"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "coupon_usage" DROP CONSTRAINT "FK_2a59fd09f9a61cfa1e422be2445"`);
        await queryRunner.query(`DROP TABLE "coupon_usage"`);
    }

}
