import { MigrationInterface, QueryRunner } from "typeorm";

export class CategoryProductRelated1742789109601 implements MigrationInterface {
    name = 'CategoryProductRelated1742789109601'

    public async up(queryRunner: QueryRunner): Promise<void> {

        await queryRunner.query(`CREATE TABLE "category_product_related" ("created_by" integer, "updated_by" integer, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "remarks" character varying, "sort_order" integer, "category_id" integer NOT NULL, "category_product_related_id" integer NOT NULL, CONSTRAINT "PK_441b90443d7e0ef6a2b72ae9b8b" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "category_product_related" ADD CONSTRAINT "FK_0577ae0bf576f7b253230071a59" FOREIGN KEY ("category_id") REFERENCES "category"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "category_product_related" ADD CONSTRAINT "FK_b50c6e0972304b2f49035748c1f" FOREIGN KEY ("category_product_related_id") REFERENCES "product"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);

    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "category_product_related" DROP CONSTRAINT "FK_b50c6e0972304b2f49035748c1f"`);
        await queryRunner.query(`ALTER TABLE "category_product_related" DROP CONSTRAINT "FK_0577ae0bf576f7b253230071a59"`);

        await queryRunner.query(`DROP TABLE "category_product_related"`);
    }

}
