import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddCommissionToAffiliateDiscounts1748600000000 implements MigrationInterface {
  name = 'AddCommissionToAffiliateDiscounts1748600000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add commission_type and commission_rate columns to affiliate_discounts table
    await queryRunner.query(`
      ALTER TABLE "affiliate_discounts" 
      ADD COLUMN "commission_type" character varying,
      ADD COLUMN "commission_rate" decimal(5,2)
    `);

    // Add check constraint for commission_type enum
    await queryRunner.query(`
      ALTER TABLE "affiliate_discounts" 
      ADD CONSTRAINT "CHK_affiliate_discounts_commission_type" 
      CHECK ("commission_type" IS NULL OR "commission_type" IN ('percentage', 'fixed'))
    `);

    // Add check constraint for commission_rate
    await queryRunner.query(`
      ALTER TABLE "affiliate_discounts" 
      ADD CONSTRAINT "CHK_affiliate_discounts_commission_rate" 
      CHECK ("commission_rate" IS NULL OR "commission_rate" >= 0)
    `);

    // Add index for commission_type for better query performance
    await queryRunner.query(`
      CREATE INDEX "IDX_affiliate_discounts_commission_type" 
      ON "affiliate_discounts" ("commission_type")
    `);

    // Add comment to explain the priority system
    await queryRunner.query(`
      COMMENT ON COLUMN "affiliate_discounts"."commission_type" IS 
      'Commission type for this specific discount assignment. Overrides affiliate profile commission_type when set.'
    `);

    await queryRunner.query(`
      COMMENT ON COLUMN "affiliate_discounts"."commission_rate" IS 
      'Commission rate for this specific discount assignment. Overrides affiliate profile commission_rate when set.'
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop the index
    await queryRunner.query(`
      DROP INDEX IF EXISTS "IDX_affiliate_discounts_commission_type"
    `);

    // Drop the check constraints
    await queryRunner.query(`
      ALTER TABLE "affiliate_discounts" 
      DROP CONSTRAINT IF EXISTS "CHK_affiliate_discounts_commission_rate"
    `);

    await queryRunner.query(`
      ALTER TABLE "affiliate_discounts" 
      DROP CONSTRAINT IF EXISTS "CHK_affiliate_discounts_commission_type"
    `);

    // Drop the columns
    await queryRunner.query(`
      ALTER TABLE "affiliate_discounts" 
      DROP COLUMN IF EXISTS "commission_rate",
      DROP COLUMN IF EXISTS "commission_type"
    `);
  }
}
