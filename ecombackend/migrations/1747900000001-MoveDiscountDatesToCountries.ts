import { MigrationInterface, QueryRunner } from 'typeorm';

export class MoveDiscountDatesToCountries1747900000001 implements MigrationInterface {
  name = 'MoveDiscountDatesToCountries1747900000001';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add start_at and end_at columns to discount_countries table
    await queryRunner.query(`
      ALTER TABLE "discount_countries" 
      ADD COLUMN "start_at" bigint NOT NULL DEFAULT 0
    `);

    await queryRunner.query(`
      ALTER TABLE "discount_countries" 
      ADD COLUMN "end_at" bigint
    `);

    // Copy data from discount table to discount_countries table
    await queryRunner.query(`
      UPDATE "discount_countries" 
      SET "start_at" = d."start_at", "end_at" = d."end_at"
      FROM "discount" d 
      WHERE "discount_countries"."discount_id" = d."id"
    `);

    // Remove the default value constraint after data migration
    await queryRunner.query(`
      ALTER TABLE "discount_countries" 
      ALTER COLUMN "start_at" DROP DEFAULT
    `);

    // Remove start_at and end_at columns from discount table
    await queryRunner.query(`
      ALTER TABLE "discount" 
      DROP COLUMN "start_at"
    `);

    await queryRunner.query(`
      ALTER TABLE "discount" 
      DROP COLUMN "end_at"
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Add start_at and end_at columns back to discount table
    await queryRunner.query(`
      ALTER TABLE "discount" 
      ADD COLUMN "start_at" bigint NOT NULL DEFAULT 0
    `);

    await queryRunner.query(`
      ALTER TABLE "discount" 
      ADD COLUMN "end_at" bigint
    `);

    // Copy data back from discount_countries to discount table
    // Note: This assumes all countries for a discount have the same dates
    await queryRunner.query(`
      UPDATE "discount" 
      SET "start_at" = dc."start_at", "end_at" = dc."end_at"
      FROM "discount_countries" dc 
      WHERE "discount"."id" = dc."discount_id"
      AND dc."discount_id" IN (
        SELECT "discount_id" 
        FROM "discount_countries" 
        GROUP BY "discount_id" 
        HAVING COUNT(*) = 1
      )
    `);

    // For discounts with multiple countries, use the earliest start_at and latest end_at
    await queryRunner.query(`
      UPDATE "discount" 
      SET "start_at" = subq."min_start_at", "end_at" = subq."max_end_at"
      FROM (
        SELECT 
          "discount_id",
          MIN("start_at") as "min_start_at",
          MAX("end_at") as "max_end_at"
        FROM "discount_countries" 
        GROUP BY "discount_id"
        HAVING COUNT(*) > 1
      ) subq
      WHERE "discount"."id" = subq."discount_id"
    `);

    // Remove the default value constraint after data migration
    await queryRunner.query(`
      ALTER TABLE "discount" 
      ALTER COLUMN "start_at" DROP DEFAULT
    `);

    // Remove start_at and end_at columns from discount_countries table
    await queryRunner.query(`
      ALTER TABLE "discount_countries" 
      DROP COLUMN "start_at"
    `);

    await queryRunner.query(`
      ALTER TABLE "discount_countries" 
      DROP COLUMN "end_at"
    `);
  }
}
