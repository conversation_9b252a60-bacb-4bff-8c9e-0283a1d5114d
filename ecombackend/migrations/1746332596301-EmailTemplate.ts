import { MigrationInterface, QueryRunner } from "typeorm";

export class EmailTemplate1746332596301 implements MigrationInterface {
    name = 'EmailTemplate1746332596301'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "email_templates" ("created_by" integer, "updated_by" integer, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "remarks" character varying, "name" text NOT NULL, "subject" text NOT NULL, "builder_content" jsonb NOT NULL, "html_content" character varying NOT NULL, "dynamicVariable" jsonb, CONSTRAINT "PK_06c564c515d8cdb40b6f3bfbbb4" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "email_translations" ("created_by" integer, "updated_by" integer, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "remarks" character varying, "locale" character varying(2) NOT NULL, "subject" text NOT NULL, "html_content" character varying NOT NULL, "template_id" integer, "country_id" integer, CONSTRAINT "PK_348fb9da414bae160f0ed576c03" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."email_campaigns_status_enum" AS ENUM('draft', 'scheduled', 'sent', 'cancelled')`);
        await queryRunner.query(`CREATE TABLE "email_campaigns" ("created_by" integer, "updated_by" integer, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "remarks" character varying, "name" character varying NOT NULL, "scheduledAt" TIMESTAMP NOT NULL, "status" "public"."email_campaigns_status_enum" NOT NULL DEFAULT 'draft', "targetGroup" jsonb NOT NULL, "template_id" integer, CONSTRAINT "PK_72bad329795785308e66d562350" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."email_send_history_status_enum" AS ENUM('pending', 'sent', 'failed')`);
        await queryRunner.query(`CREATE TABLE "email_send_history" ("created_by" integer, "updated_by" integer, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "remarks" character varying, "recipientEmail" character varying NOT NULL, "locale" character varying(2) NOT NULL, "status" "public"."email_send_history_status_enum" NOT NULL DEFAULT 'pending', "errorMessage" character varying, "sentAt" TIMESTAMP, "opened" boolean NOT NULL DEFAULT false, "clicked" boolean NOT NULL DEFAULT false, "openedAt" TIMESTAMP, "clickedAt" TIMESTAMP, "bounced" boolean NOT NULL DEFAULT false, "deliveredAt" TIMESTAMP, "complaint" boolean NOT NULL DEFAULT false, "messageBody" character varying, "campaign_id" integer, "country_id" integer, CONSTRAINT "PK_c2cb1dba185b05480e3ac3439e8" PRIMARY KEY ("id"))`);
        
        await queryRunner.query(`ALTER TABLE "email_translations" ADD CONSTRAINT "FK_8e9453ae5fa52b47241c563e54a" FOREIGN KEY ("template_id") REFERENCES "email_templates"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "email_translations" ADD CONSTRAINT "FK_5c127c2559dd4c1d2203f084857" FOREIGN KEY ("country_id") REFERENCES "country"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "email_campaigns" ADD CONSTRAINT "FK_b1271c13007a99e591b54807f32" FOREIGN KEY ("template_id") REFERENCES "email_templates"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "email_send_history" ADD CONSTRAINT "FK_40533dfd2971d1a8268b6449926" FOREIGN KEY ("campaign_id") REFERENCES "email_campaigns"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "email_send_history" ADD CONSTRAINT "FK_b3ba46b4aa765e39f6f08c390c0" FOREIGN KEY ("country_id") REFERENCES "country"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "email_send_history" DROP CONSTRAINT "FK_b3ba46b4aa765e39f6f08c390c0"`);
        await queryRunner.query(`ALTER TABLE "email_send_history" DROP CONSTRAINT "FK_40533dfd2971d1a8268b6449926"`);
        await queryRunner.query(`ALTER TABLE "email_campaigns" DROP CONSTRAINT "FK_b1271c13007a99e591b54807f32"`);
        await queryRunner.query(`ALTER TABLE "email_translations" DROP CONSTRAINT "FK_5c127c2559dd4c1d2203f084857"`);
        await queryRunner.query(`ALTER TABLE "email_translations" DROP CONSTRAINT "FK_8e9453ae5fa52b47241c563e54a"`);
        
        await queryRunner.query(`DROP TABLE "email_send_history"`);
        await queryRunner.query(`DROP TYPE "public"."email_send_history_status_enum"`);
        await queryRunner.query(`DROP TABLE "email_campaigns"`);
        await queryRunner.query(`DROP TYPE "public"."email_campaigns_status_enum"`);
        await queryRunner.query(`DROP TABLE "email_translations"`);
        await queryRunner.query(`DROP TABLE "email_templates"`);
    }

}
