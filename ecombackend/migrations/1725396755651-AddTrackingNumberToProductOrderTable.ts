import { MigrationInterface, QueryRunner } from "typeorm";

export class AddTrackingNumberToProductOrderTable1725396755651 implements MigrationInterface {
    name = 'AddTrackingNumberToProductOrderTable1725396755651'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "product_order" ADD "tracking_number" character varying`);
        await queryRunner.query(`ALTER TABLE "product_order" ADD "is_draft" boolean NOT NULL DEFAULT false`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "product_order" DROP COLUMN "tracking_number"`);
        await queryRunner.query(`ALTER TABLE "product_order" DROP COLUMN "is_draft"`);
    }

}
