import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateProductFeedAndFeedItems1729104935877
  implements MigrationInterface
{
  name = 'CreateProductFeedAndFeedItems1729104935877';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "product_feed" ("id" SERIAL NOT NULL, "slug" character varying NOT NULL, "countryId" integer NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "createdBy" character varying NOT NULL, CONSTRAINT "PK_8324ca6f37bccf60d5808a6a383" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "product_feed_items" ("product_feed_id" integer NOT NULL, "product_id" integer NOT NULL, CONSTRAINT "PK_ca2b40fdb5849350381d1a94c55" PRIMARY KEY ("product_feed_id", "product_id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_cf9064c2a836deb17f6b2bdb1d" ON "product_feed_items" ("product_feed_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_4ce2daee170467e757d22a1288" ON "product_feed_items" ("product_id") `,
    );
    await queryRunner.query(
      `ALTER TABLE "product_feed_items" ADD CONSTRAINT "FK_cf9064c2a836deb17f6b2bdb1d2" FOREIGN KEY ("product_feed_id") REFERENCES "product_feed"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "product_feed_items" ADD CONSTRAINT "FK_4ce2daee170467e757d22a12886" FOREIGN KEY ("product_id") REFERENCES "product"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );

    await queryRunner.query(
      `ALTER TABLE "product_feed" ADD "name" character varying NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "product_feed" ADD "delimiter" character varying NOT NULL`,
    );

    await queryRunner.query(
      `ALTER TABLE "product_feed" ADD "username" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "product_feed" ADD "password" character varying`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "product_feed_items" DROP CONSTRAINT "FK_4ce2daee170467e757d22a12886"`,
    );
    await queryRunner.query(
      `ALTER TABLE "product_feed_items" DROP CONSTRAINT "FK_cf9064c2a836deb17f6b2bdb1d2"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_4ce2daee170467e757d22a1288"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_cf9064c2a836deb17f6b2bdb1d"`,
    );
    await queryRunner.query(`DROP TABLE "product_feed_items"`);
    await queryRunner.query(`DROP TABLE "product_feed"`);
  }
}
