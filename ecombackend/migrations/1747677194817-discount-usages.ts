import { MigrationInterface, QueryRunner } from 'typeorm';

export class DiscountUsages1747677194817 implements MigrationInterface {
  name = 'DiscountUsages1747677194817';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "discount_usage" ("created_by" integer, "updated_by" integer, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "remarks" character varying, "user_email" character varying, "discount_id" integer, "order_id" integer, CONSTRAINT "PK_cd98b1243514dbd073a07096067" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `ALTER TABLE "discount_usage" ADD CONSTRAINT "FK_4892be6edb6f49f7dad1ce08f08" FOREIGN KEY ("discount_id") REFERENCES "discount"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );

    // await queryRunner.query(`ALTER TABLE "discount" DROP COLUMN "amount"`);
    // await queryRunner.query(`ALTER TABLE "discount" DROP COLUMN "min_amount"`);
    // await queryRunner.query(
    //   `ALTER TABLE "discount" DROP COLUMN "min_quantity"`,
    // );
    // await queryRunner.query(
    //   `ALTER TABLE "discount_countries" ADD "amount" numeric(10,2) NOT NULL`,
    // );
    // await queryRunner.query(
    //   `ALTER TABLE "discount_countries" ADD "min_amount" numeric(10,2)`,
    // );
    // await queryRunner.query(
    //   `ALTER TABLE "discount_countries" ADD "min_quantity" integer`,
    // );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE "discount_usage"`);
  }
}
