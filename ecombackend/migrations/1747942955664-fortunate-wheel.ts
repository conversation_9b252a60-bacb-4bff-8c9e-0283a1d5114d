import { MigrationInterface, QueryRunner } from 'typeorm';

export class FortunateWheel1747942955664 implements MigrationInterface {
  name = 'FortunateWheel1747942955664';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "fortunate_wheel" ("created_by" integer, "updated_by" integer, "max_retry" integer NOT NULL DEFAULT 1, "background_image" character varying, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "title" character varying NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "remarks" character varying, "country_id" integer NOT NULL, "slices" jsonb NOT NULL, CONSTRAINT "PK_ee86b23b86460a6c11581d65944" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "fortunate_wheel_usage" ("created_by" integer, "updated_by" integer, "usage_count" integer NOT NULL DEFAULT 0, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "remarks" character varying, "wheel_id" integer NOT NULL, "user_phone" character varying, "user_email" character varying, CONSTRAINT "PK_3949aefddf4a8f57031d1957f7f" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `ALTER TABLE "fortunate_wheel_usage" ADD CONSTRAINT "FK_e5aff194b87400504f3dd87a819" FOREIGN KEY ("wheel_id") REFERENCES "fortunate_wheel"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );

    await queryRunner.query(
      `CREATE UNIQUE INDEX user_email_1747989989662_index ON "fortunate_wheel_usage" USING btree ("wheel_id", "user_email")`,
    );

    await queryRunner.query(
      `CREATE UNIQUE INDEX user_phone_1747990072578_index ON "fortunate_wheel_usage" USING btree ("wheel_id", "user_phone")`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "fortunate_wheel_usage" DROP CONSTRAINT "FK_e5aff194b87400504f3dd87a819"`,
    );

    await queryRunner.query(`DROP TABLE "fortunate_wheel_usage"`);
    await queryRunner.query(`DROP TABLE "fortunate_wheel"`);
  }
}
