import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateCartOtpTable1747900000000 implements MigrationInterface {
  name = 'CreateCartOtpTable1747900000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create cart_otp table
    await queryRunner.query(`
      CREATE TABLE "cart_otp" (
        "created_by" integer,
        "updated_by" integer,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        "deleted_at" TIMESTAMP,
        "id" SERIAL NOT NULL,
        "is_active" boolean NOT NULL DEFAULT true,
        "remarks" character varying,
        "cart_id" integer NOT NULL,
        "otp_phone" character varying(20) NOT NULL,
        "otp_code" character varying(6) NOT NULL,
        "otp_expired_at" TIMESTAMP NOT NULL,
        "otp_verified_at" TIMESTAMP,
        "otp_retry_count" integer NOT NULL DEFAULT 0,
        "status" character varying(20) NOT NULL DEFAULT 'pending',
        "next_retry_at" TIMESTAMP,
        "verification_attempts" integer NOT NULL DEFAULT 0,
        "max_verification_attempts" integer NOT NULL DEFAULT 5,
        CONSTRAINT "PK_cart_otp_id" PRIMARY KEY ("id")
      )
    `);

    // Create indexes for performance
    await queryRunner.query(`CREATE INDEX "IDX_CART_OTP_CART_ID" ON "cart_otp" ("cart_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_CART_OTP_PHONE" ON "cart_otp" ("otp_phone")`);
    await queryRunner.query(`CREATE INDEX "IDX_CART_OTP_CART_STATUS" ON "cart_otp" ("cart_id", "status")`);
    await queryRunner.query(`CREATE INDEX "IDX_CART_OTP_EXPIRED_AT" ON "cart_otp" ("otp_expired_at")`);

    // Add foreign key constraint to cart table
    await queryRunner.query(`
      ALTER TABLE "cart_otp"
      ADD CONSTRAINT "FK_CART_OTP_CART_ID"
      FOREIGN KEY ("cart_id")
      REFERENCES "cart"("id")
      ON DELETE CASCADE
      ON UPDATE CASCADE
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop foreign key constraint
    await queryRunner.query(`ALTER TABLE "cart_otp" DROP CONSTRAINT "FK_CART_OTP_CART_ID"`);

    // Drop indexes
    await queryRunner.query(`DROP INDEX "IDX_CART_OTP_EXPIRED_AT"`);
    await queryRunner.query(`DROP INDEX "IDX_CART_OTP_CART_STATUS"`);
    await queryRunner.query(`DROP INDEX "IDX_CART_OTP_PHONE"`);
    await queryRunner.query(`DROP INDEX "IDX_CART_OTP_CART_ID"`);

    // Drop table
    await queryRunner.query(`DROP TABLE "cart_otp"`);
  }
}
