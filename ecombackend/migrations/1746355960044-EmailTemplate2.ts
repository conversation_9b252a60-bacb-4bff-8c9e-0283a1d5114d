import { MigrationInterface, QueryRunner } from "typeorm";

export class EmailTemplate21746355960044 implements MigrationInterface {
    name = 'EmailTemplate21746355960044'

    public async up(queryRunner: QueryRunner): Promise<void> {
        
        await queryRunner.query(`CREATE TABLE "translation_keys" ("created_by" integer, "updated_by" integer, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "remarks" character varying, "key" character varying NOT NULL, "base_text" text NOT NULL, "section" character varying, "template_id" integer, CONSTRAINT "PK_b9fb6087506f44b1a451c8a5991" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "translation_values" ("created_by" integer, "updated_by" integer, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "remarks" character varying, "locale" character varying(2) NOT NULL, "value" text NOT NULL, "translation_key_id" integer, CONSTRAINT "PK_59703dde55bc8caeefeffe4fa92" PRIMARY KEY ("id"))`);
        
        await queryRunner.query(`ALTER TABLE "translation_keys" ADD CONSTRAINT "FK_74fdf64d62cd7b5823f8b7abcd4" FOREIGN KEY ("template_id") REFERENCES "email_templates"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "translation_values" ADD CONSTRAINT "FK_e64aae81c971abb2cebb4bab382" FOREIGN KEY ("translation_key_id") REFERENCES "translation_keys"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "product_collection_pivot" DROP CONSTRAINT "FK_10274b8658544a015eb12603fa7"`);
        await queryRunner.query(`ALTER TABLE "country_courier" DROP CONSTRAINT "FK_af69aba49ef221b590372c7caf0"`);
        await queryRunner.query(`ALTER TABLE "country_courier" DROP CONSTRAINT "FK_6a53a80d5917eef5045a719a78c"`);
        await queryRunner.query(`ALTER TABLE "cart" DROP CONSTRAINT "FK_64b5ab228f2f151dc0c6e5ef7d6"`);
        await queryRunner.query(`ALTER TABLE "translation_values" DROP CONSTRAINT "FK_e64aae81c971abb2cebb4bab382"`);
        await queryRunner.query(`ALTER TABLE "translation_keys" DROP CONSTRAINT "FK_74fdf64d62cd7b5823f8b7abcd4"`);
        await queryRunner.query(`ALTER TABLE "seo_meta" DROP CONSTRAINT "FK_7fa670a01361654f51e0c5ccf3b"`);
        await queryRunner.query(`ALTER TABLE "suggested_products" DROP CONSTRAINT "FK_6f0bd4092ee7b26b3f8c642b6d6"`);
        await queryRunner.query(`ALTER TABLE "suggested_products" DROP CONSTRAINT "FK_01a78c77da883a72fdae50c4284"`);
        await queryRunner.query(`ALTER TABLE "product" DROP CONSTRAINT "FK_c20e1b4756acdc10f33dabc892c"`);
        await queryRunner.query(`ALTER TABLE "size_chart_value" DROP CONSTRAINT "FK_eb13127f7b1727df82fbb5ab9a5"`);
        await queryRunner.query(`ALTER TABLE "size_chart_value" DROP CONSTRAINT "FK_f2a8009210c1fb90908a9311d90"`);
        await queryRunner.query(`ALTER TABLE "size_chart_value" DROP CONSTRAINT "FK_6ce3f87e117dded97de1cc6cfbd"`);
        await queryRunner.query(`ALTER TABLE "size_chart" DROP CONSTRAINT "FK_78010ddee9ed28e94656e3277f4"`);
        await queryRunner.query(`ALTER TABLE "product_collection_pivot" DROP CONSTRAINT "FK_bc5206b280d552e093232f3eddd"`);
        await queryRunner.query(`ALTER TABLE "product_feed" DROP CONSTRAINT "FK_aa3f66004f07df10e56a6b8f460"`);
        await queryRunner.query(`ALTER TABLE "product_meta" DROP CONSTRAINT "FK_715cfb5a4f92e661f5b94a1cace"`);
        await queryRunner.query(`ALTER TABLE "category" DROP CONSTRAINT "FK_1117b4fcb3cd4abb4383e1c2743"`);
        await queryRunner.query(`ALTER TABLE "product_localization" DROP CONSTRAINT "UQ_dbc50143c6b36589b511ad0563b"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_bc5206b280d552e093232f3edd"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_10274b8658544a015eb12603fa"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_af69aba49ef221b590372c7caf"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_6a53a80d5917eef5045a719a78"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_b6b5e81eddccdb215607015ad4"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_c20e1b4756acdc10f33dabc892"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_8cfaf4a1e80806d58e3dbe6922"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_3f4cfd3849111765f78554ea97"`);
        await queryRunner.query(`ALTER TABLE "product_collection_pivot" DROP CONSTRAINT "PK_2c48c2d823182ab367482c8a144"`);
        await queryRunner.query(`ALTER TABLE "product_collection_pivot" ADD CONSTRAINT "PK_10274b8658544a015eb12603fa7" PRIMARY KEY ("collection_id")`);
        await queryRunner.query(`ALTER TABLE "slider_country" ALTER COLUMN "sort_order" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "slider_media" DROP COLUMN "button_link"`);
        await queryRunner.query(`ALTER TABLE "slider_media" ADD "button_link" character varying`);
        await queryRunner.query(`ALTER TABLE "slider_media" DROP COLUMN "button_text"`);
        await queryRunner.query(`ALTER TABLE "slider_media" ADD "button_text" character varying`);
        await queryRunner.query(`ALTER TABLE "slider" ALTER COLUMN "usages_type" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "seller" DROP COLUMN "country_id"`);
        await queryRunner.query(`ALTER TABLE "seller" ADD "country_id" integer NOT NULL`);
        await queryRunner.query(`ALTER TABLE "seller" ALTER COLUMN "phone" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "product_order" ALTER COLUMN "is_draft" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "product_order" DROP CONSTRAINT "UQ_6d4e0758065b4d1fa0122de9310"`);
        await queryRunner.query(`ALTER TABLE "product_order" DROP COLUMN "uuid"`);
        await queryRunner.query(`ALTER TABLE "product_order" ADD "uuid" character varying(50)`);
        await queryRunner.query(`ALTER TABLE "courier_services" DROP COLUMN "cod_charge"`);
        await queryRunner.query(`ALTER TABLE "courier_services" ADD "cod_charge" numeric(10,2) NOT NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "courier_services" DROP COLUMN "price_outside_city"`);
        await queryRunner.query(`ALTER TABLE "courier_services" ADD "price_outside_city" numeric(10,2) NOT NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "courier_services" DROP COLUMN "price_inside_city"`);
        await queryRunner.query(`ALTER TABLE "courier_services" ADD "price_inside_city" numeric(10,2) NOT NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "product_localization" ALTER COLUMN "language_id" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "size_chart_value" ALTER COLUMN "chart_images" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "size_chart" ALTER COLUMN "chart_images" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "product_collection_pivot" DROP CONSTRAINT "PK_10274b8658544a015eb12603fa7"`);
        await queryRunner.query(`ALTER TABLE "product_collection_pivot" ADD CONSTRAINT "PK_2c48c2d823182ab367482c8a144" PRIMARY KEY ("product_id", "collection_id")`);
        await queryRunner.query(`ALTER TABLE "product_collection_pivot" ALTER COLUMN "product_id" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "product_collection_pivot" ADD CONSTRAINT "FK_bc5206b280d552e093232f3eddd" FOREIGN KEY ("product_id") REFERENCES "product"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "product_feed" DROP COLUMN "createdBy"`);
        await queryRunner.query(`ALTER TABLE "product_feed" ADD "createdBy" character varying`);
        await queryRunner.query(`ALTER TABLE "product_meta" ALTER COLUMN "product_id" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "product_meta" ADD CONSTRAINT "FK_715cfb5a4f92e661f5b94a1cace" FOREIGN KEY ("product_id") REFERENCES "product"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "country" DROP COLUMN "languages_id"`);
        await queryRunner.query(`ALTER TABLE "country" ADD "languages_id" integer array`);
        await queryRunner.query(`ALTER TABLE "courier_services" ALTER COLUMN "code" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "product_category" DROP CONSTRAINT "PK_c14c8e52460c8062f62e7e8f416"`);
        await queryRunner.query(`ALTER TABLE "product_collection_pivot" DROP CONSTRAINT "PK_2c48c2d823182ab367482c8a144"`);
        await queryRunner.query(`ALTER TABLE "product_feed_items" DROP CONSTRAINT "PK_ca2b40fdb5849350381d1a94c55"`);
        await queryRunner.query(`ALTER TABLE "country_courier" DROP CONSTRAINT "PK_485a49aa48938634bef4acba7b9"`);
        await queryRunner.query(`ALTER TABLE "courier_services" DROP COLUMN "remarks"`);
        await queryRunner.query(`ALTER TABLE "courier_services" DROP COLUMN "deleted_at"`);
        await queryRunner.query(`ALTER TABLE "courier_services" DROP COLUMN "updated_at"`);
        await queryRunner.query(`ALTER TABLE "courier_services" DROP COLUMN "created_at"`);
        await queryRunner.query(`ALTER TABLE "courier_services" DROP COLUMN "updated_by"`);
        await queryRunner.query(`ALTER TABLE "courier_services" DROP COLUMN "created_by"`);
        await queryRunner.query(`ALTER TABLE "product_collection_pivot" DROP COLUMN "sort_order"`);
        await queryRunner.query(`ALTER TABLE "product_collection_pivot" ADD "sort_order" integer`);
        await queryRunner.query(`ALTER TABLE "country_courier" ADD "id" SERIAL NOT NULL`);
        await queryRunner.query(`ALTER TABLE "country_courier" ADD CONSTRAINT "country_courier_pkey" PRIMARY KEY ("id")`);
        await queryRunner.query(`ALTER TABLE "news_subscription" ADD "user_id" integer`);
        await queryRunner.query(`ALTER TABLE "product_refund_type" ADD "sku" character varying`);
        await queryRunner.query(`ALTER TABLE "product" ADD "category_id" integer`);
        await queryRunner.query(`ALTER TABLE "size_chart_value" ADD "measurement" jsonb`);
        await queryRunner.query(`ALTER TABLE "size_chart" ADD "measurement" jsonb`);
        await queryRunner.query(`ALTER TABLE "courier_services" ADD "updated_at" TIMESTAMP NOT NULL DEFAULT now()`);
        await queryRunner.query(`ALTER TABLE "courier_services" ADD "created_by" integer`);
        await queryRunner.query(`ALTER TABLE "courier_services" ADD "created_at" TIMESTAMP NOT NULL DEFAULT now()`);
        await queryRunner.query(`ALTER TABLE "courier_services" ADD "remarks" character varying`);
        await queryRunner.query(`ALTER TABLE "courier_services" ADD "deleted_at" TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "courier_services" ADD "updated_by" integer`);
        await queryRunner.query(`DROP TABLE "translation_values"`);
        await queryRunner.query(`DROP TABLE "translation_keys"`);
        await queryRunner.query(`ALTER TABLE "suggested_products" ADD CONSTRAINT "suggested_products_product_id_suggested_product_id_key" UNIQUE ("product_id", "suggested_product_id")`);
        await queryRunner.query(`ALTER TABLE "product_localization" ADD CONSTRAINT "UQ_93cb0579ab34174d798a7508d69" UNIQUE ("product_id", "language_id")`);
        await queryRunner.query(`CREATE INDEX "IDX_5a26e726d93e4121d86d8c1067" ON "product_localization" ("language_id") `);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_8cfaf4a1e80806d58e3dbe6922" ON "product" ("slug") `);
        await queryRunner.query(`CREATE INDEX "IDX_0dce9bc93c2d2c399982d04bef" ON "product" ("category_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_bc5206b280d552e093232f3edd" ON "product_collection_pivot" ("product_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_10274b8658544a015eb12603fa" ON "product_collection_pivot" ("collection_id") `);
        await queryRunner.query(`ALTER TABLE "country_courier" ADD CONSTRAINT "FK_country" FOREIGN KEY ("country_id") REFERENCES "country"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "country_courier" ADD CONSTRAINT "FK_courier" FOREIGN KEY ("courier_id") REFERENCES "courier_services"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "seller" ADD CONSTRAINT "fk_country" FOREIGN KEY ("country_id") REFERENCES "country"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "suggested_products" ADD CONSTRAINT "suggested_products_product_id_fkey" FOREIGN KEY ("product_id") REFERENCES "product"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "suggested_products" ADD CONSTRAINT "suggested_products_suggested_product_id_fkey" FOREIGN KEY ("suggested_product_id") REFERENCES "product"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "product_localization" ADD CONSTRAINT "FK_5a26e726d93e4121d86d8c1067a" FOREIGN KEY ("language_id") REFERENCES "country"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "product" ADD CONSTRAINT "FK_0dce9bc93c2d2c399982d04bef1" FOREIGN KEY ("category_id") REFERENCES "category"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "product_collection_pivot" ADD CONSTRAINT "FK_10274b8658544a015eb12603fa7" FOREIGN KEY ("collection_id") REFERENCES "product_collections"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "category" ADD CONSTRAINT "FK_1117b4fcb3cd4abb4383e1c2743" FOREIGN KEY ("parent_id") REFERENCES "category"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
