import { MigrationInterface, QueryRunner } from "typeorm";

export class Media<PERSON>lider1744891427333 implements MigrationInterface {
    name = 'MediaSlider1744891427333'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "slider" ("created_by" integer, "updated_by" integer, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "remarks" character varying, "name" character varying, "description" text, "type" character varying NOT NULL, CONSTRAINT "PK_ae59f1b572454f8251212e2d3dc" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "slider_media" ("created_by" integer, "updated_by" integer, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "remarks" character varying, "title" character varying, "sub_title" character varying, "description" text, "sub_description" text, "type" character varying NOT NULL, "usages_type" character varying NOT NULL, "sort_order" integer NOT NULL DEFAULT '0', "media_custom_url" text, "global_style_css" jsonb, "slider_id" integer, "media_id" integer, CONSTRAINT "PK_50cb6e625d1eced50a5be6b4524" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "slider_country" ("created_by" integer, "updated_by" integer, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "remarks" character varying, "locale" jsonb, "country_id" integer, "slider_media_id" integer, CONSTRAINT "PK_a85a40c6c77571131b5fdd8f190" PRIMARY KEY ("id"))`);

        await queryRunner.query(`ALTER TABLE "slider_media" ADD CONSTRAINT "FK_24f188aa7f6198f60a7a6311d0e" FOREIGN KEY ("slider_id") REFERENCES "slider"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "slider_media" ADD CONSTRAINT "FK_f100628cda254ab27bb1b6f8a0b" FOREIGN KEY ("media_id") REFERENCES "image_gallery"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "slider_country" ADD CONSTRAINT "FK_9be2f13b09ba08721e384bd4025" FOREIGN KEY ("country_id") REFERENCES "country"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "slider_country" ADD CONSTRAINT "FK_09ad0dee8e8a4ed39abdf966ee0" FOREIGN KEY ("slider_media_id") REFERENCES "slider_media"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "slider_country" DROP CONSTRAINT "FK_09ad0dee8e8a4ed39abdf966ee0"`);
        await queryRunner.query(`ALTER TABLE "slider_country" DROP CONSTRAINT "FK_9be2f13b09ba08721e384bd4025"`);
        await queryRunner.query(`ALTER TABLE "slider_media" DROP CONSTRAINT "FK_f100628cda254ab27bb1b6f8a0b"`);
        await queryRunner.query(`ALTER TABLE "slider_media" DROP CONSTRAINT "FK_24f188aa7f6198f60a7a6311d0e"`);

        await queryRunner.query(`DROP TABLE "slider_country"`);
        await queryRunner.query(`DROP TABLE "slider_media"`);
        await queryRunner.query(`DROP TABLE "slider"`);
    }

}
