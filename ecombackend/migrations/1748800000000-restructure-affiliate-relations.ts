import { MigrationInterface, QueryRunner } from 'typeorm';

export class RestructureAffiliateRelations1748800000000 implements MigrationInterface {
  name = 'RestructureAffiliateRelations1748800000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add affiliate_discount_id to affiliate_commissions
    await queryRunner.query(`
      ALTER TABLE "affiliate_commissions" 
      ADD COLUMN "affiliate_discount_id" integer
    `);

    // Add affiliate_discount_id to affiliate_clicks
    await queryRunner.query(`
      ALTER TABLE "affiliate_clicks" 
      ADD COLUMN "affiliate_discount_id" integer
    `);

    // Add affiliate_discount_id to affiliate_conversions
    await queryRunner.query(`
      ALTER TABLE "affiliate_conversions" 
      ADD COLUMN "affiliate_discount_id" integer NOT NULL
    `);

    // Create foreign key constraints
    await queryRunner.query(`
      ALTER TABLE "affiliate_commissions" 
      ADD CONSTRAINT "FK_affiliate_commissions_affiliate_discount" 
      FOREIGN KEY ("affiliate_discount_id") REFERENCES "affiliate_discounts"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "affiliate_clicks" 
      ADD CONSTRAINT "FK_affiliate_clicks_affiliate_discount" 
      FOREIGN KEY ("affiliate_discount_id") REFERENCES "affiliate_discounts"("id") ON DELETE SET NULL
    `);

    await queryRunner.query(`
      ALTER TABLE "affiliate_conversions" 
      ADD CONSTRAINT "FK_affiliate_conversions_affiliate_discount" 
      FOREIGN KEY ("affiliate_discount_id") REFERENCES "affiliate_discounts"("id") ON DELETE CASCADE
    `);

    // Create indexes for better performance
    await queryRunner.query(`
      CREATE INDEX "IDX_affiliate_commissions_affiliate_discount_id" 
      ON "affiliate_commissions" ("affiliate_discount_id")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_affiliate_clicks_affiliate_discount_id" 
      ON "affiliate_clicks" ("affiliate_discount_id")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_affiliate_conversions_affiliate_discount_id" 
      ON "affiliate_conversions" ("affiliate_discount_id")
    `);

    // Update existing records to link through affiliate_discount
    // This assumes that existing records have discount_id that can be matched to affiliate_discounts
    await queryRunner.query(`
      UPDATE "affiliate_commissions" 
      SET "affiliate_discount_id" = (
        SELECT ad.id 
        FROM "affiliate_discounts" ad 
        WHERE ad.affiliate_id = "affiliate_commissions".affiliate_id 
        AND ad.discount_id = "affiliate_commissions".discount_id
        LIMIT 1
      )
      WHERE "affiliate_discount_id" IS NULL 
      AND "discount_id" IS NOT NULL
    `);

    // For clicks without specific discount, link to the first active affiliate discount
    await queryRunner.query(`
      UPDATE "affiliate_clicks" 
      SET "affiliate_discount_id" = (
        SELECT ad.id 
        FROM "affiliate_discounts" ad 
        WHERE ad.affiliate_id = "affiliate_clicks".affiliate_id 
        AND ad.status = 'active'
        ORDER BY ad.created_at ASC
        LIMIT 1
      )
      WHERE "affiliate_discount_id" IS NULL
    `);

    // For conversions, link through existing order data
    await queryRunner.query(`
      UPDATE "affiliate_conversions" 
      SET "affiliate_discount_id" = (
        SELECT ac.affiliate_discount_id 
        FROM "affiliate_commissions" ac 
        WHERE ac.order_id = "affiliate_conversions".order_id
        AND ac.affiliate_id = "affiliate_conversions".affiliate_id
        LIMIT 1
      )
      WHERE "affiliate_discount_id" IS NULL 
      AND "order_id" IS NOT NULL
    `);

    // For remaining conversions without order, link to first active affiliate discount
    await queryRunner.query(`
      UPDATE "affiliate_conversions" 
      SET "affiliate_discount_id" = (
        SELECT ad.id 
        FROM "affiliate_discounts" ad 
        WHERE ad.affiliate_id = "affiliate_conversions".affiliate_id 
        AND ad.status = 'active'
        ORDER BY ad.created_at ASC
        LIMIT 1
      )
      WHERE "affiliate_discount_id" IS NULL
    `);

    // Add comments to explain the new structure
    await queryRunner.query(`
      COMMENT ON COLUMN "affiliate_commissions"."affiliate_discount_id" IS 
      'Links commission to specific affiliate discount campaign'
    `);

    await queryRunner.query(`
      COMMENT ON COLUMN "affiliate_clicks"."affiliate_discount_id" IS 
      'Links click to specific affiliate discount campaign (nullable for general affiliate links)'
    `);

    await queryRunner.query(`
      COMMENT ON COLUMN "affiliate_conversions"."affiliate_discount_id" IS 
      'Links conversion to specific affiliate discount campaign'
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove foreign key constraints
    await queryRunner.query(`
      ALTER TABLE "affiliate_commissions" 
      DROP CONSTRAINT "FK_affiliate_commissions_affiliate_discount"
    `);

    await queryRunner.query(`
      ALTER TABLE "affiliate_clicks" 
      DROP CONSTRAINT "FK_affiliate_clicks_affiliate_discount"
    `);

    await queryRunner.query(`
      ALTER TABLE "affiliate_conversions" 
      DROP CONSTRAINT "FK_affiliate_conversions_affiliate_discount"
    `);

    // Remove indexes
    await queryRunner.query(`DROP INDEX "IDX_affiliate_commissions_affiliate_discount_id"`);
    await queryRunner.query(`DROP INDEX "IDX_affiliate_clicks_affiliate_discount_id"`);
    await queryRunner.query(`DROP INDEX "IDX_affiliate_conversions_affiliate_discount_id"`);

    // Remove columns
    await queryRunner.query(`ALTER TABLE "affiliate_commissions" DROP COLUMN "affiliate_discount_id"`);
    await queryRunner.query(`ALTER TABLE "affiliate_clicks" DROP COLUMN "affiliate_discount_id"`);
    await queryRunner.query(`ALTER TABLE "affiliate_conversions" DROP COLUMN "affiliate_discount_id"`);
  }
}
