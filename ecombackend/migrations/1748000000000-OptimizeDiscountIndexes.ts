import { MigrationInterface, QueryRunner } from "typeorm";

export class OptimizeDiscountIndexes1748000000000 implements MigrationInterface {
    name = 'OptimizeDiscountIndexes1748000000000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Add indexes for discount table to optimize getDiscountGoals and getApplicableDiscounts queries
        
        // Composite index for active automatic discounts
        await queryRunner.query(
            `CREATE INDEX "IDX_discount_active_automatic" ON "discount" ("is_active", "is_automatic") WHERE "is_active" = true`
        );

        // Index for discount type filtering
        await queryRunner.query(
            `CREATE INDEX "IDX_discount_type" ON "discount" ("discount_type")`
        );

        // Index for coupon lookups
        await queryRunner.query(
            `CREATE INDEX "IDX_discount_coupon" ON "discount" ("coupon") WHERE "coupon" IS NOT NULL`
        );

        // Index for usage limits
        await queryRunner.query(
            `CREATE INDEX "IDX_discount_usage_limits" ON "discount" ("max_use", "used_count") WHERE "max_use" IS NOT NULL`
        );

        // Add indexes for discount_countries table to optimize country and date filtering
        
        // Composite index for country and date range filtering
        await queryRunner.query(
            `CREATE INDEX "IDX_discount_countries_country_dates" ON "discount_countries" ("country_id", "start_at", "end_at")`
        );

        // Index for minimum amount filtering
        await queryRunner.query(
            `CREATE INDEX "IDX_discount_countries_min_amount" ON "discount_countries" ("min_amount") WHERE "min_amount" IS NOT NULL`
        );

        // Index for minimum quantity filtering
        await queryRunner.query(
            `CREATE INDEX "IDX_discount_countries_min_quantity" ON "discount_countries" ("min_quantity") WHERE "min_quantity" IS NOT NULL`
        );

        // Add indexes for cart_item table to optimize cart-related queries
        
        // Index for cart UUID lookups (if not already exists)
        await queryRunner.query(
            `CREATE INDEX IF NOT EXISTS "IDX_cart_item_cart_uuid" ON "cart_item" ("cartUUID")`
        );

        // Index for product lookups in cart items
        await queryRunner.query(
            `CREATE INDEX IF NOT EXISTS "IDX_cart_item_product_id" ON "cart_item" ("product_id")`
        );

        // Index for discount lookups in cart items
        await queryRunner.query(
            `CREATE INDEX IF NOT EXISTS "IDX_cart_item_discount_id" ON "cart_item" ("discount_id") WHERE "discount_id" IS NOT NULL`
        );

        // Add indexes for product table to optimize product fetching
        
        // Composite index for active published products
        await queryRunner.query(
            `CREATE INDEX "IDX_product_active_published" ON "product" ("is_active", "is_publish", "deleted_at") WHERE "is_active" = true AND "is_publish" = true AND "deleted_at" IS NULL`
        );

        // Add indexes for product_meta table to optimize country-specific product queries
        
        // Composite index for country and active products
        await queryRunner.query(
            `CREATE INDEX "IDX_product_meta_country_active" ON "product_meta" ("country_id", "is_active", "deleted_at") WHERE "is_active" = true AND "deleted_at" IS NULL`
        );

        // Index for product_id in product_meta
        await queryRunner.query(
            `CREATE INDEX IF NOT EXISTS "IDX_product_meta_product_id" ON "product_meta" ("product_id")`
        );

        // Add indexes for product_localization table
        
        // Composite index for product and language
        await queryRunner.query(
            `CREATE INDEX IF NOT EXISTS "IDX_product_localization_product_language" ON "product_localization" ("product_id", "language_id")`
        );

        // Add indexes for image_gallery table to optimize image fetching
        
        // Index for image gallery ID (if not already exists)
        await queryRunner.query(
            `CREATE INDEX IF NOT EXISTS "IDX_image_gallery_id" ON "image_gallery" ("id")`
        );

        // Add indexes for cart table to optimize cart lookups
        
        // Index for cart UUID (if not already exists)
        await queryRunner.query(
            `CREATE INDEX IF NOT EXISTS "IDX_cart_uuid" ON "cart" ("cartUUID")`
        );

        // Index for country in cart
        await queryRunner.query(
            `CREATE INDEX IF NOT EXISTS "IDX_cart_country_id" ON "cart" ("country_id")`
        );

        // Index for user in cart
        await queryRunner.query(
            `CREATE INDEX IF NOT EXISTS "IDX_cart_user_id" ON "cart" ("user_id") WHERE "user_id" IS NOT NULL`
        );

        // Add indexes for discount usage tracking
        
        // Index for discount usage by discount
        await queryRunner.query(
            `CREATE INDEX IF NOT EXISTS "IDX_discount_usage_discount_id" ON "discount_usage" ("discount_id")`
        );

        // Index for discount usage by order
        await queryRunner.query(
            `CREATE INDEX IF NOT EXISTS "IDX_discount_usage_order_id" ON "discount_usage" ("order_id")`
        );

        // Index for discount usage by email
        await queryRunner.query(
            `CREATE INDEX IF NOT EXISTS "IDX_discount_usage_user_email" ON "discount_usage" ("user_email")`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop all the indexes created in the up method
        
        await queryRunner.query(`DROP INDEX IF EXISTS "IDX_discount_active_automatic"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "IDX_discount_type"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "IDX_discount_coupon"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "IDX_discount_usage_limits"`);
        
        await queryRunner.query(`DROP INDEX IF EXISTS "IDX_discount_countries_country_dates"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "IDX_discount_countries_min_amount"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "IDX_discount_countries_min_quantity"`);
        
        await queryRunner.query(`DROP INDEX IF EXISTS "IDX_cart_item_cart_uuid"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "IDX_cart_item_product_id"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "IDX_cart_item_discount_id"`);
        
        await queryRunner.query(`DROP INDEX IF EXISTS "IDX_product_active_published"`);
        
        await queryRunner.query(`DROP INDEX IF EXISTS "IDX_product_meta_country_active"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "IDX_product_meta_product_id"`);
        
        await queryRunner.query(`DROP INDEX IF EXISTS "IDX_product_localization_product_language"`);
        
        await queryRunner.query(`DROP INDEX IF EXISTS "IDX_image_gallery_id"`);
        
        await queryRunner.query(`DROP INDEX IF EXISTS "IDX_cart_uuid"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "IDX_cart_country_id"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "IDX_cart_user_id"`);
        
        await queryRunner.query(`DROP INDEX IF EXISTS "IDX_discount_usage_discount_id"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "IDX_discount_usage_order_id"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "IDX_discount_usage_user_email"`);
    }
}
