import { MigrationInterface, QueryRunner } from 'typeorm';

export class AttributeLocale1728536340384 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "attribute_value" ADD "locale" jsonb`);
    await queryRunner.query(`ALTER TABLE "category" ADD "locale" jsonb`);
    await queryRunner.query(`ALTER TABLE "product_tags" ADD "locale" jsonb`);
    await queryRunner.query(`ALTER TABLE "quick_links" ADD "locale" jsonb`);
    await queryRunner.query(`ALTER TABLE "faq" ADD "locale" jsonb`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "attribute_value" DROP COLUMN "locale"`,
    );
    await queryRunner.query(`ALTER TABLE "category" DROP COLUMN "locale"`);
    await queryRunner.query(`ALTER TABLE "product_tags" DROP COLUMN "locale"`);
    await queryRunner.query(`ALTER TABLE "quick_links" DROP COLUMN "locale"`);
    await queryRunner.query(`ALTER TABLE "faq" DROP COLUMN "locale"`);
  }
}
