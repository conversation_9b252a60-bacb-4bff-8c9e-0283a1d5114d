import { MigrationInterface, QueryRunner } from 'typeorm';

export class ProductCollection1729864764290 implements MigrationInterface {
  name = 'ProductCollection1729864764290';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "product_collections" ("id" SERIAL NOT NULL, "title" character varying NOT NULL, "slug" character varying NOT NULL, "full_content" text, "left_half_content" text, "right_half_content" text, "is_active" boolean NOT NULL DEFAULT false, "countryId" integer NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "meta_title" character varying, "meta_description" text, "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "createdBy" integer, "meta_image_id" integer, CONSTRAINT "PK_32bc9e0d29551e755a4df9a5f4e" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_b6e5d43167356236927d5ca64b" ON "product_collections" ("slug") `,
    );
    await queryRunner.query(
      `CREATE TABLE "product_collection_pivot" ("collection_id" integer NOT NULL, "product_id" integer NOT NULL, CONSTRAINT "PK_2c48c2d823182ab367482c8a144" PRIMARY KEY ("collection_id", "product_id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_10274b8658544a015eb12603fa" ON "product_collection_pivot" ("collection_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_bc5206b280d552e093232f3edd" ON "product_collection_pivot" ("product_id") `,
    );
    await queryRunner.query(
      `ALTER TABLE "product_collections" ADD CONSTRAINT "FK_a114964a13e6b8e93eb2fdcc4ed" FOREIGN KEY ("meta_image_id") REFERENCES "image_gallery"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "product_collections" ADD CONSTRAINT "FK_c911d35808aefc1b64c63802296" FOREIGN KEY ("countryId") REFERENCES "country"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "product_collection_pivot" ADD CONSTRAINT "FK_10274b8658544a015eb12603fa7" FOREIGN KEY ("collection_id") REFERENCES "product_collections"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "product_collection_pivot" ADD CONSTRAINT "FK_bc5206b280d552e093232f3eddd" FOREIGN KEY ("product_id") REFERENCES "product"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "address" ADD "address_id" character varying`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "product_collection_pivot" DROP CONSTRAINT "FK_bc5206b280d552e093232f3eddd"`,
    );
    await queryRunner.query(
      `ALTER TABLE "product_collection_pivot" DROP CONSTRAINT "FK_10274b8658544a015eb12603fa7"`,
    );
    await queryRunner.query(
      `ALTER TABLE "product_collections" DROP CONSTRAINT "FK_c911d35808aefc1b64c63802296"`,
    );
    await queryRunner.query(
      `ALTER TABLE "product_collections" DROP CONSTRAINT "FK_a114964a13e6b8e93eb2fdcc4ed"`,
    );
    await queryRunner.query(`ALTER TABLE "address" DROP COLUMN "address_id"`);

    await queryRunner.query(
      `DROP INDEX "public"."IDX_bc5206b280d552e093232f3edd"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_10274b8658544a015eb12603fa"`,
    );
    await queryRunner.query(`DROP TABLE "product_collection_pivot"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_b6e5d43167356236927d5ca64b"`,
    );
    await queryRunner.query(`DROP TABLE "product_collections"`);
  }
}
