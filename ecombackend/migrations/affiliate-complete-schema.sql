-- =====================================================
-- Complete Affiliate System Schema Creation
-- =====================================================
-- This SQL script creates the complete affiliate system
-- from scratch, including all tables, enums, constraints,
-- and indexes as defined in the TypeORM entities.
-- 
-- Use this script if you need to create the affiliate
-- system tables from scratch.
-- =====================================================

-- Create enums for affiliate system
DO $$
BEGIN
    -- Affiliate profile status enum
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'affiliate_profiles_status_enum') THEN
        CREATE TYPE "public"."affiliate_profiles_status_enum" AS ENUM('pending', 'active', 'suspended', 'rejected');
    END IF;

    -- Commission type enum
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'affiliate_profiles_commission_type_enum') THEN
        CREATE TYPE "public"."affiliate_profiles_commission_type_enum" AS ENUM('percentage', 'fixed');
    END IF;

    -- Affiliate discount status enum
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'affiliate_discounts_status_enum') THEN
        CREATE TYPE "public"."affiliate_discounts_status_enum" AS ENUM('active', 'inactive', 'expired', 'pending', 'rejected');
    END IF;

    -- Click source enum
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'affiliate_clicks_source_enum') THEN
        CREATE TYPE "public"."affiliate_clicks_source_enum" AS ENUM('direct_link', 'coupon_code', 'social_media', 'email', 'website', 'other');
    END IF;

    -- Commission status enum
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'affiliate_commissions_status_enum') THEN
        CREATE TYPE "public"."affiliate_commissions_status_enum" AS ENUM('pending', 'approved', 'paid', 'cancelled', 'refunded');
    END IF;

    -- Commission source enum
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'affiliate_commissions_source_enum') THEN
        CREATE TYPE "public"."affiliate_commissions_source_enum" AS ENUM('order', 'discount_usage', 'referral', 'bonus');
    END IF;

    -- Conversion type enum
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'affiliate_conversions_type_enum') THEN
        CREATE TYPE "public"."affiliate_conversions_type_enum" AS ENUM('purchase', 'signup', 'lead', 'download');
    END IF;
END $$;

-- Create affiliate_profiles table
CREATE TABLE IF NOT EXISTS "affiliate_profiles" (
    "created_by" integer,
    "updated_by" integer,
    "created_at" TIMESTAMP NOT NULL DEFAULT now(),
    "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
    "deleted_at" TIMESTAMP,
    "id" SERIAL NOT NULL,
    "is_active" boolean NOT NULL DEFAULT true,
    "remarks" character varying,
    "user_id" integer NOT NULL,
    "affiliate_code" character varying NOT NULL,
    "country_id" integer,
    "status" "public"."affiliate_profiles_status_enum" NOT NULL DEFAULT 'pending',
    "commission_type" "public"."affiliate_profiles_commission_type_enum" NOT NULL DEFAULT 'percentage',
    "commission_rate" numeric(5,2) NOT NULL DEFAULT '0',
    "total_earnings" numeric(10,2) NOT NULL DEFAULT '0',
    "pending_earnings" numeric(10,2) NOT NULL DEFAULT '0',
    "paid_earnings" numeric(10,2) NOT NULL DEFAULT '0',
    "total_clicks" integer NOT NULL DEFAULT '0',
    "total_conversions" integer NOT NULL DEFAULT '0',
    "total_orders" integer NOT NULL DEFAULT '0',
    "conversion_rate" numeric(5,2) NOT NULL DEFAULT '0',
    "payment_method" character varying,
    "payment_details" jsonb,
    "bio" text,
    "website_url" character varying,
    "social_media" jsonb,
    "approved_at" TIMESTAMP,
    "rejected_at" TIMESTAMP,
    "rejection_reason" character varying,
    "notes" character varying,
    CONSTRAINT "UQ_affiliate_profiles_user_id" UNIQUE ("user_id"),
    CONSTRAINT "UQ_affiliate_profiles_affiliate_code" UNIQUE ("affiliate_code"),
    CONSTRAINT "PK_affiliate_profiles" PRIMARY KEY ("id")
);

-- Create affiliate_discounts table
CREATE TABLE IF NOT EXISTS "affiliate_discounts" (
    "created_by" integer,
    "updated_by" integer,
    "created_at" TIMESTAMP NOT NULL DEFAULT now(),
    "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
    "deleted_at" TIMESTAMP,
    "id" SERIAL NOT NULL,
    "is_active" boolean NOT NULL DEFAULT true,
    "remarks" character varying,
    "affiliate_id" integer NOT NULL,
    "discount_id" integer NOT NULL,
    "status" "public"."affiliate_discounts_status_enum" NOT NULL DEFAULT 'active',
    "usage_count" integer NOT NULL DEFAULT '0',
    "max_usage" integer,
    "total_commission_earned" numeric(10,2) NOT NULL DEFAULT '0',
    "start_date" TIMESTAMP,
    "end_date" TIMESTAMP,
    "notes" text,
    "commission_type" character varying,
    "commission_rate" numeric(5,2),
    CONSTRAINT "PK_affiliate_discounts" PRIMARY KEY ("id")
);

-- Create affiliate_clicks table
CREATE TABLE IF NOT EXISTS "affiliate_clicks" (
    "created_by" integer,
    "updated_by" integer,
    "created_at" TIMESTAMP NOT NULL DEFAULT now(),
    "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
    "deleted_at" TIMESTAMP,
    "id" SERIAL NOT NULL,
    "is_active" boolean NOT NULL DEFAULT true,
    "remarks" character varying,
    "affiliate_id" integer NOT NULL,
    "affiliate_discount_id" integer,
    "session_id" character varying,
    "ip_address" character varying,
    "user_agent" text,
    "referrer_url" text,
    "landing_page" text,
    "source" "public"."affiliate_clicks_source_enum" NOT NULL DEFAULT 'direct_link',
    "country_id" integer,
    "city" character varying,
    "device_type" character varying,
    "browser" character varying,
    "os" character varying,
    "utm_source" character varying,
    "utm_medium" character varying,
    "utm_campaign" character varying,
    "utm_content" character varying,
    "utm_term" character varying,
    "converted" boolean NOT NULL DEFAULT false,
    "conversion_date" TIMESTAMP,
    "order_id" integer,
    "commission_earned" numeric(10,2) NOT NULL DEFAULT '0',
    CONSTRAINT "PK_affiliate_clicks" PRIMARY KEY ("id")
);

-- Create affiliate_commissions table
CREATE TABLE IF NOT EXISTS "affiliate_commissions" (
    "created_by" integer,
    "updated_by" integer,
    "created_at" TIMESTAMP NOT NULL DEFAULT now(),
    "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
    "deleted_at" TIMESTAMP,
    "id" SERIAL NOT NULL,
    "is_active" boolean NOT NULL DEFAULT true,
    "remarks" character varying,
    "affiliate_id" integer NOT NULL,
    "affiliate_discount_id" integer NOT NULL,
    "order_id" integer,
    "discount_id" integer,
    "click_id" integer,
    "source" "public"."affiliate_commissions_source_enum" NOT NULL DEFAULT 'order',
    "status" "public"."affiliate_commissions_status_enum" NOT NULL DEFAULT 'pending',
    "order_amount" numeric(10,2) NOT NULL DEFAULT '0',
    "commission_rate" numeric(5,2) NOT NULL DEFAULT '0',
    "commission_amount" numeric(10,2) NOT NULL DEFAULT '0',
    "currency_code" character varying NOT NULL DEFAULT 'USD',
    "customer_email" character varying,
    "customer_phone" character varying,
    "coupon_code" character varying,
    "discount_amount" numeric(10,2),
    "approved_at" TIMESTAMP,
    "paid_at" TIMESTAMP,
    "payment_reference" character varying,
    "notes" text,
    "refund_reason" character varying,
    "refunded_at" TIMESTAMP,
    "country_id" character varying,
    "uuid" character varying,
    "invoice_no" character varying,
    "commission_type" character varying,
    CONSTRAINT "PK_affiliate_commissions" PRIMARY KEY ("id")
);

-- Create affiliate_conversions table
CREATE TABLE IF NOT EXISTS "affiliate_conversions" (
    "created_by" integer,
    "updated_by" integer,
    "created_at" TIMESTAMP NOT NULL DEFAULT now(),
    "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
    "deleted_at" TIMESTAMP,
    "id" SERIAL NOT NULL,
    "is_active" boolean NOT NULL DEFAULT true,
    "remarks" character varying,
    "affiliate_id" integer NOT NULL,
    "affiliate_discount_id" integer,
    "click_id" integer,
    "order_id" integer,
    "type" "public"."affiliate_conversions_type_enum" NOT NULL DEFAULT 'purchase',
    "conversion_value" numeric(10,2) NOT NULL DEFAULT '0',
    "customer_email" character varying,
    "customer_phone" character varying,
    "session_id" character varying,
    "ip_address" character varying,
    "conversion_page" text,
    "time_to_conversion" integer,
    "coupon_used" character varying,
    "discount_applied" numeric(10,2),
    "commission_earned" numeric(10,2) NOT NULL DEFAULT '0',
    "conversion_data" jsonb,
    CONSTRAINT "PK_affiliate_conversions" PRIMARY KEY ("id")
);

-- Create affiliate_discount_usages table
CREATE TABLE IF NOT EXISTS "affiliate_discount_usages" (
    "created_by" integer,
    "updated_by" integer,
    "created_at" TIMESTAMP NOT NULL DEFAULT now(),
    "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
    "deleted_at" TIMESTAMP,
    "id" SERIAL NOT NULL,
    "is_active" boolean NOT NULL DEFAULT true,
    "remarks" character varying,
    "affiliate_discount_id" integer NOT NULL,
    "discount_usage_id" integer,
    "order_id" integer NOT NULL,
    "customer_email" character varying NOT NULL,
    "customer_phone" character varying,
    "order_amount" numeric(10,2) NOT NULL DEFAULT '0',
    "discount_amount" numeric(10,2) NOT NULL DEFAULT '0',
    "commission_rate" numeric(5,2) NOT NULL DEFAULT '0',
    "commission_amount" numeric(10,2) NOT NULL DEFAULT '0',
    "session_id" character varying,
    "ip_address" character varying,
    "user_agent" text,
    "referrer_url" text,
    "utm_source" character varying,
    "utm_medium" character varying,
    "utm_campaign" character varying,
    CONSTRAINT "PK_affiliate_discount_usages" PRIMARY KEY ("id")
);

-- Add foreign key constraints
DO $$
BEGIN
    -- affiliate_profiles foreign keys
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'FK_affiliate_profiles_user_id'
    ) THEN
        ALTER TABLE "affiliate_profiles"
        ADD CONSTRAINT "FK_affiliate_profiles_user_id"
        FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE CASCADE;
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'FK_affiliate_profiles_country_id'
    ) THEN
        ALTER TABLE "affiliate_profiles"
        ADD CONSTRAINT "FK_affiliate_profiles_country_id"
        FOREIGN KEY ("country_id") REFERENCES "country"("id") ON DELETE SET NULL;
    END IF;

    -- affiliate_discounts foreign keys
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'FK_affiliate_discounts_affiliate_id'
    ) THEN
        ALTER TABLE "affiliate_discounts"
        ADD CONSTRAINT "FK_affiliate_discounts_affiliate_id"
        FOREIGN KEY ("affiliate_id") REFERENCES "affiliate_profiles"("id") ON DELETE CASCADE;
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'FK_affiliate_discounts_discount_id'
    ) THEN
        ALTER TABLE "affiliate_discounts"
        ADD CONSTRAINT "FK_affiliate_discounts_discount_id"
        FOREIGN KEY ("discount_id") REFERENCES "discount"("id") ON DELETE CASCADE;
    END IF;

    -- affiliate_clicks foreign keys
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'FK_affiliate_clicks_affiliate_id'
    ) THEN
        ALTER TABLE "affiliate_clicks"
        ADD CONSTRAINT "FK_affiliate_clicks_affiliate_id"
        FOREIGN KEY ("affiliate_id") REFERENCES "affiliate_profiles"("id") ON DELETE CASCADE;
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'FK_affiliate_clicks_affiliate_discount_id'
    ) THEN
        ALTER TABLE "affiliate_clicks"
        ADD CONSTRAINT "FK_affiliate_clicks_affiliate_discount_id"
        FOREIGN KEY ("affiliate_discount_id") REFERENCES "affiliate_discounts"("id") ON DELETE SET NULL;
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'FK_affiliate_clicks_country_id'
    ) THEN
        ALTER TABLE "affiliate_clicks"
        ADD CONSTRAINT "FK_affiliate_clicks_country_id"
        FOREIGN KEY ("country_id") REFERENCES "country"("id") ON DELETE SET NULL;
    END IF;

    -- affiliate_commissions foreign keys
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'FK_affiliate_commissions_affiliate_id'
    ) THEN
        ALTER TABLE "affiliate_commissions"
        ADD CONSTRAINT "FK_affiliate_commissions_affiliate_id"
        FOREIGN KEY ("affiliate_id") REFERENCES "affiliate_profiles"("id") ON DELETE CASCADE;
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'FK_affiliate_commissions_affiliate_discount_id'
    ) THEN
        ALTER TABLE "affiliate_commissions"
        ADD CONSTRAINT "FK_affiliate_commissions_affiliate_discount_id"
        FOREIGN KEY ("affiliate_discount_id") REFERENCES "affiliate_discounts"("id") ON DELETE CASCADE;
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'FK_affiliate_commissions_order_id'
    ) THEN
        ALTER TABLE "affiliate_commissions"
        ADD CONSTRAINT "FK_affiliate_commissions_order_id"
        FOREIGN KEY ("order_id") REFERENCES "product_order"("id") ON DELETE SET NULL;
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'FK_affiliate_commissions_discount_id'
    ) THEN
        ALTER TABLE "affiliate_commissions"
        ADD CONSTRAINT "FK_affiliate_commissions_discount_id"
        FOREIGN KEY ("discount_id") REFERENCES "discount"("id") ON DELETE SET NULL;
    END IF;

    -- affiliate_conversions foreign keys
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'FK_affiliate_conversions_affiliate_id'
    ) THEN
        ALTER TABLE "affiliate_conversions"
        ADD CONSTRAINT "FK_affiliate_conversions_affiliate_id"
        FOREIGN KEY ("affiliate_id") REFERENCES "affiliate_profiles"("id") ON DELETE CASCADE;
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'FK_affiliate_conversions_affiliate_discount_id'
    ) THEN
        ALTER TABLE "affiliate_conversions"
        ADD CONSTRAINT "FK_affiliate_conversions_affiliate_discount_id"
        FOREIGN KEY ("affiliate_discount_id") REFERENCES "affiliate_discounts"("id") ON DELETE SET NULL;
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'FK_affiliate_conversions_click_id'
    ) THEN
        ALTER TABLE "affiliate_conversions"
        ADD CONSTRAINT "FK_affiliate_conversions_click_id"
        FOREIGN KEY ("click_id") REFERENCES "affiliate_clicks"("id") ON DELETE SET NULL;
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'FK_affiliate_conversions_order_id'
    ) THEN
        ALTER TABLE "affiliate_conversions"
        ADD CONSTRAINT "FK_affiliate_conversions_order_id"
        FOREIGN KEY ("order_id") REFERENCES "product_order"("id") ON DELETE SET NULL;
    END IF;

    -- affiliate_discount_usages foreign keys
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'FK_affiliate_discount_usages_affiliate_discount_id'
    ) THEN
        ALTER TABLE "affiliate_discount_usages"
        ADD CONSTRAINT "FK_affiliate_discount_usages_affiliate_discount_id"
        FOREIGN KEY ("affiliate_discount_id") REFERENCES "affiliate_discounts"("id") ON DELETE CASCADE;
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'FK_affiliate_discount_usages_discount_usage_id'
    ) THEN
        ALTER TABLE "affiliate_discount_usages"
        ADD CONSTRAINT "FK_affiliate_discount_usages_discount_usage_id"
        FOREIGN KEY ("discount_usage_id") REFERENCES "discount_usage"("id") ON DELETE SET NULL;
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'FK_affiliate_discount_usages_order_id'
    ) THEN
        ALTER TABLE "affiliate_discount_usages"
        ADD CONSTRAINT "FK_affiliate_discount_usages_order_id"
        FOREIGN KEY ("order_id") REFERENCES "product_order"("id") ON DELETE CASCADE;
    END IF;
END $$;

-- Create indexes for better performance
DO $$
BEGIN
    -- affiliate_profiles indexes
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'IDX_affiliate_profiles_user_id') THEN
        CREATE INDEX "IDX_affiliate_profiles_user_id" ON "affiliate_profiles" ("user_id");
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'IDX_affiliate_profiles_affiliate_code') THEN
        CREATE INDEX "IDX_affiliate_profiles_affiliate_code" ON "affiliate_profiles" ("affiliate_code");
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'IDX_affiliate_profiles_status') THEN
        CREATE INDEX "IDX_affiliate_profiles_status" ON "affiliate_profiles" ("status");
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'IDX_affiliate_profiles_country_id') THEN
        CREATE INDEX "IDX_affiliate_profiles_country_id" ON "affiliate_profiles" ("country_id");
    END IF;

    -- affiliate_discounts indexes
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'IDX_affiliate_discounts_affiliate_id') THEN
        CREATE INDEX "IDX_affiliate_discounts_affiliate_id" ON "affiliate_discounts" ("affiliate_id");
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'IDX_affiliate_discounts_discount_id') THEN
        CREATE INDEX "IDX_affiliate_discounts_discount_id" ON "affiliate_discounts" ("discount_id");
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'IDX_affiliate_discounts_status') THEN
        CREATE INDEX "IDX_affiliate_discounts_status" ON "affiliate_discounts" ("status");
    END IF;

    -- affiliate_clicks indexes
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'IDX_affiliate_clicks_affiliate_id') THEN
        CREATE INDEX "IDX_affiliate_clicks_affiliate_id" ON "affiliate_clicks" ("affiliate_id");
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'IDX_affiliate_clicks_affiliate_discount_id') THEN
        CREATE INDEX "IDX_affiliate_clicks_affiliate_discount_id" ON "affiliate_clicks" ("affiliate_discount_id");
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'IDX_affiliate_clicks_session_id') THEN
        CREATE INDEX "IDX_affiliate_clicks_session_id" ON "affiliate_clicks" ("session_id");
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'IDX_affiliate_clicks_converted') THEN
        CREATE INDEX "IDX_affiliate_clicks_converted" ON "affiliate_clicks" ("converted");
    END IF;

    -- affiliate_commissions indexes
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'IDX_affiliate_commissions_affiliate_id') THEN
        CREATE INDEX "IDX_affiliate_commissions_affiliate_id" ON "affiliate_commissions" ("affiliate_id");
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'IDX_affiliate_commissions_affiliate_discount_id') THEN
        CREATE INDEX "IDX_affiliate_commissions_affiliate_discount_id" ON "affiliate_commissions" ("affiliate_discount_id");
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'IDX_affiliate_commissions_order_id') THEN
        CREATE INDEX "IDX_affiliate_commissions_order_id" ON "affiliate_commissions" ("order_id");
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'IDX_affiliate_commissions_status') THEN
        CREATE INDEX "IDX_affiliate_commissions_status" ON "affiliate_commissions" ("status");
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'IDX_affiliate_commissions_uuid') THEN
        CREATE INDEX "IDX_affiliate_commissions_uuid" ON "affiliate_commissions" ("uuid");
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'IDX_affiliate_commissions_invoice_no') THEN
        CREATE INDEX "IDX_affiliate_commissions_invoice_no" ON "affiliate_commissions" ("invoice_no");
    END IF;

    -- affiliate_conversions indexes
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'IDX_affiliate_conversions_affiliate_id') THEN
        CREATE INDEX "IDX_affiliate_conversions_affiliate_id" ON "affiliate_conversions" ("affiliate_id");
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'IDX_affiliate_conversions_affiliate_discount_id') THEN
        CREATE INDEX "IDX_affiliate_conversions_affiliate_discount_id" ON "affiliate_conversions" ("affiliate_discount_id");
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'IDX_affiliate_conversions_order_id') THEN
        CREATE INDEX "IDX_affiliate_conversions_order_id" ON "affiliate_conversions" ("order_id");
    END IF;

    -- affiliate_discount_usages indexes
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'IDX_affiliate_discount_usages_affiliate_discount_id') THEN
        CREATE INDEX "IDX_affiliate_discount_usages_affiliate_discount_id" ON "affiliate_discount_usages" ("affiliate_discount_id");
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'IDX_affiliate_discount_usages_order_id') THEN
        CREATE INDEX "IDX_affiliate_discount_usages_order_id" ON "affiliate_discount_usages" ("order_id");
    END IF;
END $$;

-- Add table and column comments
COMMENT ON TABLE "affiliate_profiles" IS 'Stores affiliate user profiles and their commission settings';
COMMENT ON TABLE "affiliate_discounts" IS 'Links affiliates to specific discount codes with custom commission rates';
COMMENT ON TABLE "affiliate_clicks" IS 'Tracks clicks on affiliate links for analytics and attribution';
COMMENT ON TABLE "affiliate_commissions" IS 'Records commission earnings for affiliates from orders and other sources';
COMMENT ON TABLE "affiliate_conversions" IS 'Tracks successful conversions from affiliate traffic';
COMMENT ON TABLE "affiliate_discount_usages" IS 'Records usage of affiliate discount codes';

-- =====================================================
-- Complete Affiliate System Schema Created Successfully
-- =====================================================
-- All affiliate system tables have been created with:
-- - Proper data types and constraints
-- - Foreign key relationships
-- - Performance indexes
-- - Enum types for status fields
-- - Comments for documentation
--
-- Tables created:
-- 1. affiliate_profiles - Main affiliate user profiles
-- 2. affiliate_discounts - Affiliate discount code assignments
-- 3. affiliate_clicks - Click tracking and analytics
-- 4. affiliate_commissions - Commission calculations and payments
-- 5. affiliate_conversions - Conversion tracking
-- 6. affiliate_discount_usages - Discount usage tracking
-- =====================================================
