import { MigrationInterface, QueryRunner } from "typeorm";

export class AddedCountryCodeUser1723749300530 implements MigrationInterface {
    name = 'AddedCountryCodeUser1723749300530'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "user" ADD "country_code" integer`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "country_code"`);
    }

}
