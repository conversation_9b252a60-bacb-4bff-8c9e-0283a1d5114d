CREATE TYPE "public"."discount_amount_type_enum" AS ENUM('fixed', 'percentage');
CREATE TYPE "public"."discount_applies_to_enum" AS ENUM('all', 'segment', 'specific');
CREATE TYPE "public"."discount_discount_type_enum" AS ENUM('moneyOffProduct', 'buyXgetY', 'moneyOffOrder', 'shipping');

CREATE TABLE "discount" ("id" SERIAL NOT NULL, "amount_type" "public"."discount_amount_type_enum" NOT NULL DEFAULT 'fixed', "applies_to" "public"."discount_applies_to_enum" NOT NULL DEFAULT 'all', "discount_type" "public"."discount_discount_type_enum" NOT NULL, "eligibility_type" integer NOT NULL DEFAULT '0', "end_at" bigint, "is_active" boolean NOT NULL DEFAULT false, "is_automatic" boolean NOT NULL DEFAULT true, "max_customer_use" integer, "max_use" integer, "start_at" bigint NOT NULL, "used_count" integer NOT NULL DEFAULT '0', "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "coupon" character varying, "title" character varying NOT NULL, "buy_x_min_qty" integer, "buy_y_min_qty" integer, CONSTRAINT "PK_d05d8712e429673e459e7f1cddb" PRIMARY KEY ("id"));

CREATE TABLE "discount_product_pivot" ("discount_id" integer NOT NULL, "product_id" integer NOT NULL, CONSTRAINT "PK_a7662717ecebaabd6c5abb624bf" PRIMARY KEY ("discount_id", "product_id"));

CREATE INDEX "IDX_781cdaf0c1cac2c6a126d6128b" ON "discount_product_pivot" ("discount_id");

CREATE INDEX "IDX_cdfa7c28f7dfc0b68201367238" ON "discount_product_pivot" ("product_id");

CREATE TABLE "discount_countries" ("discount_id" integer NOT NULL, "country_id" integer NOT NULL, "amount" numeric(10,2) NOT NULL, "min_amount" numeric(10,2), "min_quantity" integer, CONSTRAINT "PK_99a300f2773bfb5807b8f81045e" PRIMARY KEY ("discount_id", "country_id"));

CREATE INDEX "IDX_5589fa11f66580f0d10dd00664" ON "discount_countries" ("discount_id");
CREATE INDEX "IDX_e4f369d8e89c66dc5390d46e95" ON "discount_countries" ("country_id");
CREATE TABLE "discount_buy_products" ("discount_id" integer NOT NULL, "product_id" integer NOT NULL, CONSTRAINT "PK_6a8313b85be8cc9a6dd47f6002d" PRIMARY KEY ("discount_id", "product_id"));
CREATE INDEX "IDX_24b5fae2dd570c7dfd821865d3" ON "discount_buy_products" ("discount_id");
CREATE INDEX "IDX_e05e8ee8aadb3d178502a53306" ON "discount_buy_products" ("product_id");
CREATE TABLE "discount_get_products" ("discount_id" integer NOT NULL, "product_id" integer NOT NULL, CONSTRAINT "PK_051e1c2362b6f72848ffd57133b" PRIMARY KEY ("discount_id", "product_id"));
CREATE INDEX "IDX_f47a1a752367fbff9e1d4aee01" ON "discount_get_products" ("discount_id");
CREATE INDEX "IDX_d1587525a7ff328623a809837f" ON "discount_get_products" ("product_id");
ALTER TABLE "discount_product_pivot" ADD CONSTRAINT "FK_781cdaf0c1cac2c6a126d6128bb" FOREIGN KEY ("discount_id") REFERENCES "discount"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "discount_product_pivot" ADD CONSTRAINT "FK_cdfa7c28f7dfc0b68201367238b" FOREIGN KEY ("product_id") REFERENCES "product"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "discount_countries" ADD CONSTRAINT "FK_5589fa11f66580f0d10dd00664a" FOREIGN KEY ("discount_id") REFERENCES "discount"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "discount_countries" ADD CONSTRAINT "FK_e4f369d8e89c66dc5390d46e95a" FOREIGN KEY ("country_id") REFERENCES "country"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "discount_buy_products" ADD CONSTRAINT "FK_24b5fae2dd570c7dfd821865d3c" FOREIGN KEY ("discount_id") REFERENCES "discount"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "discount_buy_products" ADD CONSTRAINT "FK_e05e8ee8aadb3d178502a533067" FOREIGN KEY ("product_id") REFERENCES "product"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "discount_get_products" ADD CONSTRAINT "FK_f47a1a752367fbff9e1d4aee016" FOREIGN KEY ("discount_id") REFERENCES "discount"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "discount_get_products" ADD CONSTRAINT "FK_d1587525a7ff328623a809837f1" FOREIGN KEY ("product_id") REFERENCES "product"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "cart_item" ADD "discount_id" integer;
ALTER TABLE "cart_item" ADD "discount_amount" numeric(10,2);
ALTER TABLE cart ADD COLUMN order_discount_id BIGINT REFERENCES discount(id);
ALTER TABLE cart ADD COLUMN order_discount_amount NUMERIC(10, 2) DEFAULT 0;
ALTER TABLE cart ADD COLUMN shipping_discount_id BIGINT REFERENCES discount(id);
ALTER TABLE cart ADD COLUMN shipping_discount_amount NUMERIC(10, 2) DEFAULT 0;

CREATE INDEX "IDX_c33199b2errrdd98817b45fe7" ON "cart_item" ("discount_id");


CREATE TABLE "discount_usage" ("created_by" integer, "updated_by" integer, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "remarks" character varying, "user_email" character varying, "discount_id" integer NOT NULL, "order_id" integer NOT NULL, CONSTRAINT "PK_cd98b1243514dbd073a07096067" PRIMARY KEY ("id"));
ALTER TABLE "discount_usage" ADD CONSTRAINT "FK_4892be6edb6f49f7dad1ce08f08" FOREIGN KEY ("discount_id") REFERENCES "discount"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
