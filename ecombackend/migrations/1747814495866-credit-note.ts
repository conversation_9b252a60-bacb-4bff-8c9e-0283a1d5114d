import { MigrationInterface, QueryRunner } from "typeorm";

export class BlogPostCategoryTagAuthorLocalization1747814495816 implements MigrationInterface {
    name = 'BlogPostCategoryTagAuthorLocalization1747814495816'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "product_order_details" ADD COLUMN "credit_qty" INTEGER`);
        await queryRunner.query(`ALTER TABLE "product_order" ADD COLUMN "credited_order_id" INTEGER`);
    } 

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "product_order_details" DROP COLUMN "credit_qty"`);
        await queryRunner.query(`ALTER TABLE "product_order" DROP COLUMN "credited_order_id"`);
    }

}
