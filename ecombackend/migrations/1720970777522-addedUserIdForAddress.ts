import { MigrationInterface, QueryRunner } from "typeorm";

export class AddedUserIdForAddress1720970777522 implements MigrationInterface {
    name = 'AddedUserIdForAddress1720970777522'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "address_order" DROP CONSTRAINT "FK_7ea5c92e1ffba232cfd2174f338"`);
        await queryRunner.query(`ALTER TABLE "address" ADD "user_id" integer`);
        await queryRunner.query(`ALTER TABLE "user" ADD CONSTRAINT "FK_5c80e24caacfe9c6158b1fcafa4" FOREIGN KEY ("shipping_address_id") REFERENCES "address_order"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "user" DROP CONSTRAINT "FK_5c80e24caacfe9c6158b1fcafa4"`);
        await queryRunner.query(`ALTER TABLE "address" DROP COLUMN "user_id"`);
        await queryRunner.query(`ALTER TABLE "address_order" ADD CONSTRAINT "FK_7ea5c92e1ffba232cfd2174f338" FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
