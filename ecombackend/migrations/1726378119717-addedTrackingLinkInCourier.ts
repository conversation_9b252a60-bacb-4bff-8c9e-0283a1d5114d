import { MigrationInterface, QueryRunner } from "typeorm";

export class AddedTrackingLinkInCourier1726378119717 implements MigrationInterface {
    name = 'AddedTrackingLinkInCourier1726378119717'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "courier_services" ADD "tracking_link" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "courier_services" DROP COLUMN "tracking_link"`);
    }

}
