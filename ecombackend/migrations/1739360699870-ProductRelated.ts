import { MigrationInterface, QueryRunner } from "typeorm";

export class ProductRelated1739360699870 implements MigrationInterface {
    name = 'ProductRelated1739360699870'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "product_related" ("created_by" integer, "updated_by" integer, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "remarks" character varying, "sort_order" integer, "product_id" integer NOT NULL, "product_related_id" integer NOT NULL, CONSTRAINT "PK_633afff8b31d5abe4b34835a6af" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "product_related" ADD CONSTRAINT "FK_251aa48e91e87fa4e6f3eda7244" FOREIGN KEY ("product_id") REFERENCES "product"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "product_related" ADD CONSTRAINT "FK_60968950dab8271ca183f8ce2ce" FOREIGN KEY ("product_related_id") REFERENCES "product"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "product_related" DROP CONSTRAINT "FK_60968950dab8271ca183f8ce2ce"`);
        await queryRunner.query(`ALTER TABLE "product_related" DROP CONSTRAINT "FK_251aa48e91e87fa4e6f3eda7244"`);
        await queryRunner.query(`DROP TABLE "product_related"`);
    }

}
