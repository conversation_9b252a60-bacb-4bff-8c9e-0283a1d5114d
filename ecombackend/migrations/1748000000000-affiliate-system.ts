import { MigrationInterface, QueryRunner } from 'typeorm';

export class AffiliateSystem1748000000000 implements MigrationInterface {
  name = 'AffiliateSystem1748000000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create affiliate_profiles table
    await queryRunner.query(`
      CREATE TYPE "public"."affiliate_profiles_status_enum" AS ENUM('pending', 'active', 'suspended', 'rejected')
    `);
    await queryRunner.query(`
      CREATE TYPE "public"."affiliate_profiles_commission_type_enum" AS ENUM('percentage', 'fixed')
    `);
    await queryRunner.query(`
      CREATE TABLE "affiliate_profiles" (
        "created_by" integer,
        "updated_by" integer,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        "deleted_at" TIMESTAMP,
        "id" SERIAL NOT NULL,
        "is_active" boolean NOT NULL DEFAULT true,
        "remarks" character varying,
        "user_id" integer NOT NULL,
        "affiliate_code" character varying(50) NOT NULL,
        "status" "public"."affiliate_profiles_status_enum" NOT NULL DEFAULT 'pending',
        "commission_type" "public"."affiliate_profiles_commission_type_enum" NOT NULL DEFAULT 'percentage',
        "commission_rate" numeric(5,2) NOT NULL DEFAULT '0',
        "total_earnings" numeric(10,2) NOT NULL DEFAULT '0',
        "pending_earnings" numeric(10,2) NOT NULL DEFAULT '0',
        "paid_earnings" numeric(10,2) NOT NULL DEFAULT '0',
        "total_clicks" integer NOT NULL DEFAULT '0',
        "total_conversions" integer NOT NULL DEFAULT '0',
        "total_orders" integer NOT NULL DEFAULT '0',
        "conversion_rate" numeric(5,2) NOT NULL DEFAULT '0',
        "payment_method" character varying,
        "payment_details" jsonb,
        "bio" text,
        "website_url" character varying,
        "social_media" jsonb,
        "approved_at" TIMESTAMP,
        "rejected_at" TIMESTAMP,
        "rejection_reason" character varying,
        CONSTRAINT "UQ_affiliate_profiles_user_id" UNIQUE ("user_id"),
        CONSTRAINT "UQ_affiliate_profiles_affiliate_code" UNIQUE ("affiliate_code"),
        CONSTRAINT "PK_affiliate_profiles" PRIMARY KEY ("id")
      )
    `);

    // Create affiliate_discounts table
    await queryRunner.query(`
      CREATE TYPE "public"."affiliate_discounts_status_enum" AS ENUM('active', 'inactive', 'expired')
    `);
    await queryRunner.query(`
      CREATE TABLE "affiliate_discounts" (
        "created_by" integer,
        "updated_by" integer,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        "deleted_at" TIMESTAMP,
        "id" SERIAL NOT NULL,
        "is_active" boolean NOT NULL DEFAULT true,
        "remarks" character varying,
        "affiliate_id" integer NOT NULL,
        "discount_id" integer NOT NULL,
        "status" "public"."affiliate_discounts_status_enum" NOT NULL DEFAULT 'active',
        "usage_count" integer NOT NULL DEFAULT '0',
        "max_usage" integer,
        "total_commission_earned" numeric(10,2) NOT NULL DEFAULT '0',
        "start_date" TIMESTAMP,
        "end_date" TIMESTAMP,
        "notes" text,
        CONSTRAINT "PK_affiliate_discounts" PRIMARY KEY ("id")
      )
    `);

    // Create affiliate_clicks table
    await queryRunner.query(`
      CREATE TYPE "public"."affiliate_clicks_source_enum" AS ENUM('direct_link', 'coupon_code', 'social_media', 'email', 'website', 'other')
    `);
    await queryRunner.query(`
      CREATE TABLE "affiliate_clicks" (
        "created_by" integer,
        "updated_by" integer,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        "deleted_at" TIMESTAMP,
        "id" SERIAL NOT NULL,
        "is_active" boolean NOT NULL DEFAULT true,
        "remarks" character varying,
        "affiliate_id" integer NOT NULL,
        "session_id" character varying,
        "ip_address" character varying,
        "user_agent" text,
        "referrer_url" text,
        "landing_page" text,
        "source" "public"."affiliate_clicks_source_enum" NOT NULL DEFAULT 'direct_link',
        "country_id" integer,
        "city" character varying,
        "device_type" character varying,
        "browser" character varying,
        "os" character varying,
        "utm_source" character varying,
        "utm_medium" character varying,
        "utm_campaign" character varying,
        "utm_content" character varying,
        "utm_term" character varying,
        "converted" boolean NOT NULL DEFAULT false,
        "conversion_date" TIMESTAMP,
        "order_id" integer,
        "commission_earned" numeric(10,2) NOT NULL DEFAULT '0',
        CONSTRAINT "PK_affiliate_clicks" PRIMARY KEY ("id")
      )
    `);

    // Create affiliate_commissions table
    await queryRunner.query(`
      CREATE TYPE "public"."affiliate_commissions_status_enum" AS ENUM('pending', 'approved', 'paid', 'cancelled', 'refunded')
    `);
    await queryRunner.query(`
      CREATE TYPE "public"."affiliate_commissions_source_enum" AS ENUM('order', 'discount_usage', 'referral', 'bonus')
    `);
    await queryRunner.query(`
      CREATE TABLE "affiliate_commissions" (
        "created_by" integer,
        "updated_by" integer,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        "deleted_at" TIMESTAMP,
        "id" SERIAL NOT NULL,
        "is_active" boolean NOT NULL DEFAULT true,
        "remarks" character varying,
        "affiliate_id" integer NOT NULL,
        "order_id" integer,
        "discount_id" integer,
        "click_id" integer,
        "source" "public"."affiliate_commissions_source_enum" NOT NULL DEFAULT 'order',
        "status" "public"."affiliate_commissions_status_enum" NOT NULL DEFAULT 'pending',
        "order_amount" numeric(10,2) NOT NULL DEFAULT '0',
        "commission_rate" numeric(5,2) NOT NULL DEFAULT '0',
        "commission_amount" numeric(10,2) NOT NULL DEFAULT '0',
        "currency_code" character varying NOT NULL DEFAULT 'USD',
        "customer_email" character varying,
        "customer_phone" character varying,
        "coupon_code" character varying,
        "discount_amount" numeric(10,2),
        "approved_at" TIMESTAMP,
        "paid_at" TIMESTAMP,
        "payment_reference" character varying,
        "notes" text,
        "refund_reason" character varying,
        "refunded_at" TIMESTAMP,
        CONSTRAINT "PK_affiliate_commissions" PRIMARY KEY ("id")
      )
    `);

    // Create affiliate_conversions table
    await queryRunner.query(`
      CREATE TYPE "public"."affiliate_conversions_type_enum" AS ENUM('purchase', 'signup', 'lead', 'download')
    `);
    await queryRunner.query(`
      CREATE TABLE "affiliate_conversions" (
        "created_by" integer,
        "updated_by" integer,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        "deleted_at" TIMESTAMP,
        "id" SERIAL NOT NULL,
        "is_active" boolean NOT NULL DEFAULT true,
        "remarks" character varying,
        "affiliate_id" integer NOT NULL,
        "click_id" integer,
        "order_id" integer,
        "type" "public"."affiliate_conversions_type_enum" NOT NULL DEFAULT 'purchase',
        "conversion_value" numeric(10,2) NOT NULL DEFAULT '0',
        "customer_email" character varying,
        "customer_phone" character varying,
        "session_id" character varying,
        "ip_address" character varying,
        "conversion_page" text,
        "time_to_conversion" integer,
        "coupon_used" character varying,
        "discount_applied" numeric(10,2),
        "commission_earned" numeric(10,2) NOT NULL DEFAULT '0',
        "conversion_data" jsonb,
        CONSTRAINT "PK_affiliate_conversions" PRIMARY KEY ("id")
      )
    `);

    // Create affiliate_discount_usages table
    await queryRunner.query(`
      CREATE TABLE "affiliate_discount_usages" (
        "created_by" integer,
        "updated_by" integer,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        "deleted_at" TIMESTAMP,
        "id" SERIAL NOT NULL,
        "is_active" boolean NOT NULL DEFAULT true,
        "remarks" character varying,
        "affiliate_discount_id" integer NOT NULL,
        "discount_usage_id" integer NOT NULL,
        "order_id" integer NOT NULL,
        "customer_email" character varying NOT NULL,
        "customer_phone" character varying,
        "order_amount" numeric(10,2) NOT NULL DEFAULT '0',
        "discount_amount" numeric(10,2) NOT NULL DEFAULT '0',
        "commission_rate" numeric(5,2) NOT NULL DEFAULT '0',
        "commission_amount" numeric(10,2) NOT NULL DEFAULT '0',
        "session_id" character varying,
        "ip_address" character varying,
        "user_agent" text,
        "referrer_url" text,
        "utm_source" character varying,
        "utm_medium" character varying,
        "utm_campaign" character varying,
        CONSTRAINT "PK_affiliate_discount_usages" PRIMARY KEY ("id")
      )
    `);

    // Add foreign key constraints
    await queryRunner.query(`
      ALTER TABLE "affiliate_profiles" 
      ADD CONSTRAINT "FK_affiliate_profiles_user_id" 
      FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "affiliate_discounts" 
      ADD CONSTRAINT "FK_affiliate_discounts_affiliate_id" 
      FOREIGN KEY ("affiliate_id") REFERENCES "affiliate_profiles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "affiliate_discounts" 
      ADD CONSTRAINT "FK_affiliate_discounts_discount_id" 
      FOREIGN KEY ("discount_id") REFERENCES "discount"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "affiliate_clicks" 
      ADD CONSTRAINT "FK_affiliate_clicks_affiliate_id" 
      FOREIGN KEY ("affiliate_id") REFERENCES "affiliate_profiles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "affiliate_clicks" 
      ADD CONSTRAINT "FK_affiliate_clicks_country_id" 
      FOREIGN KEY ("country_id") REFERENCES "country"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "affiliate_commissions" 
      ADD CONSTRAINT "FK_affiliate_commissions_affiliate_id" 
      FOREIGN KEY ("affiliate_id") REFERENCES "affiliate_profiles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "affiliate_commissions" 
      ADD CONSTRAINT "FK_affiliate_commissions_order_id" 
      FOREIGN KEY ("order_id") REFERENCES "product_order"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "affiliate_commissions" 
      ADD CONSTRAINT "FK_affiliate_commissions_discount_id" 
      FOREIGN KEY ("discount_id") REFERENCES "discount"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "affiliate_conversions" 
      ADD CONSTRAINT "FK_affiliate_conversions_affiliate_id" 
      FOREIGN KEY ("affiliate_id") REFERENCES "affiliate_profiles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "affiliate_conversions" 
      ADD CONSTRAINT "FK_affiliate_conversions_click_id" 
      FOREIGN KEY ("click_id") REFERENCES "affiliate_clicks"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "affiliate_conversions" 
      ADD CONSTRAINT "FK_affiliate_conversions_order_id" 
      FOREIGN KEY ("order_id") REFERENCES "product_order"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "affiliate_discount_usages" 
      ADD CONSTRAINT "FK_affiliate_discount_usages_affiliate_discount_id" 
      FOREIGN KEY ("affiliate_discount_id") REFERENCES "affiliate_discounts"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "affiliate_discount_usages" 
      ADD CONSTRAINT "FK_affiliate_discount_usages_discount_usage_id" 
      FOREIGN KEY ("discount_usage_id") REFERENCES "discount_usage"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "affiliate_discount_usages" 
      ADD CONSTRAINT "FK_affiliate_discount_usages_order_id" 
      FOREIGN KEY ("order_id") REFERENCES "product_order"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
    `);

    // Create indexes for better performance
    await queryRunner.query(`CREATE INDEX "IDX_affiliate_profiles_user_id" ON "affiliate_profiles" ("user_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_affiliate_profiles_affiliate_code" ON "affiliate_profiles" ("affiliate_code")`);
    await queryRunner.query(`CREATE INDEX "IDX_affiliate_profiles_status" ON "affiliate_profiles" ("status")`);
    
    await queryRunner.query(`CREATE INDEX "IDX_affiliate_discounts_affiliate_id" ON "affiliate_discounts" ("affiliate_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_affiliate_discounts_discount_id" ON "affiliate_discounts" ("discount_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_affiliate_discounts_status" ON "affiliate_discounts" ("status")`);
    
    await queryRunner.query(`CREATE INDEX "IDX_affiliate_clicks_affiliate_id" ON "affiliate_clicks" ("affiliate_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_affiliate_clicks_session_id" ON "affiliate_clicks" ("session_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_affiliate_clicks_converted" ON "affiliate_clicks" ("converted")`);
    
    await queryRunner.query(`CREATE INDEX "IDX_affiliate_commissions_affiliate_id" ON "affiliate_commissions" ("affiliate_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_affiliate_commissions_order_id" ON "affiliate_commissions" ("order_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_affiliate_commissions_status" ON "affiliate_commissions" ("status")`);
    
    await queryRunner.query(`CREATE INDEX "IDX_affiliate_conversions_affiliate_id" ON "affiliate_conversions" ("affiliate_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_affiliate_conversions_order_id" ON "affiliate_conversions" ("order_id")`);
    
    await queryRunner.query(`CREATE INDEX "IDX_affiliate_discount_usages_affiliate_discount_id" ON "affiliate_discount_usages" ("affiliate_discount_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_affiliate_discount_usages_order_id" ON "affiliate_discount_usages" ("order_id")`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop foreign key constraints
    await queryRunner.query(`ALTER TABLE "affiliate_discount_usages" DROP CONSTRAINT "FK_affiliate_discount_usages_order_id"`);
    await queryRunner.query(`ALTER TABLE "affiliate_discount_usages" DROP CONSTRAINT "FK_affiliate_discount_usages_discount_usage_id"`);
    await queryRunner.query(`ALTER TABLE "affiliate_discount_usages" DROP CONSTRAINT "FK_affiliate_discount_usages_affiliate_discount_id"`);
    await queryRunner.query(`ALTER TABLE "affiliate_conversions" DROP CONSTRAINT "FK_affiliate_conversions_order_id"`);
    await queryRunner.query(`ALTER TABLE "affiliate_conversions" DROP CONSTRAINT "FK_affiliate_conversions_click_id"`);
    await queryRunner.query(`ALTER TABLE "affiliate_conversions" DROP CONSTRAINT "FK_affiliate_conversions_affiliate_id"`);
    await queryRunner.query(`ALTER TABLE "affiliate_commissions" DROP CONSTRAINT "FK_affiliate_commissions_discount_id"`);
    await queryRunner.query(`ALTER TABLE "affiliate_commissions" DROP CONSTRAINT "FK_affiliate_commissions_order_id"`);
    await queryRunner.query(`ALTER TABLE "affiliate_commissions" DROP CONSTRAINT "FK_affiliate_commissions_affiliate_id"`);
    await queryRunner.query(`ALTER TABLE "affiliate_clicks" DROP CONSTRAINT "FK_affiliate_clicks_country_id"`);
    await queryRunner.query(`ALTER TABLE "affiliate_clicks" DROP CONSTRAINT "FK_affiliate_clicks_affiliate_id"`);
    await queryRunner.query(`ALTER TABLE "affiliate_discounts" DROP CONSTRAINT "FK_affiliate_discounts_discount_id"`);
    await queryRunner.query(`ALTER TABLE "affiliate_discounts" DROP CONSTRAINT "FK_affiliate_discounts_affiliate_id"`);
    await queryRunner.query(`ALTER TABLE "affiliate_profiles" DROP CONSTRAINT "FK_affiliate_profiles_user_id"`);

    // Drop tables
    await queryRunner.query(`DROP TABLE "affiliate_discount_usages"`);
    await queryRunner.query(`DROP TABLE "affiliate_conversions"`);
    await queryRunner.query(`DROP TABLE "affiliate_commissions"`);
    await queryRunner.query(`DROP TABLE "affiliate_clicks"`);
    await queryRunner.query(`DROP TABLE "affiliate_discounts"`);
    await queryRunner.query(`DROP TABLE "affiliate_profiles"`);

    // Drop enums
    await queryRunner.query(`DROP TYPE "public"."affiliate_conversions_type_enum"`);
    await queryRunner.query(`DROP TYPE "public"."affiliate_commissions_source_enum"`);
    await queryRunner.query(`DROP TYPE "public"."affiliate_commissions_status_enum"`);
    await queryRunner.query(`DROP TYPE "public"."affiliate_clicks_source_enum"`);
    await queryRunner.query(`DROP TYPE "public"."affiliate_discounts_status_enum"`);
    await queryRunner.query(`DROP TYPE "public"."affiliate_profiles_commission_type_enum"`);
    await queryRunner.query(`DROP TYPE "public"."affiliate_profiles_status_enum"`);
  }
}
