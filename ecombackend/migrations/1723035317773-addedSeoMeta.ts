import { MigrationInterface, QueryRunner } from "typeorm";

export class AddedSeoMeta1723035317773 implements MigrationInterface {
    name = 'AddedSeoMeta1723035317773'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "setup_home_partner_instagram" DROP CONSTRAINT "FK_a5c9da21be5cab086fc3e6dee9e"`);
        await queryRunner.query(`CREATE TABLE "seo_meta" ("created_by" integer, "updated_by" integer, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "id" SERIAL NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "remarks" character varying, "meta_title" character varying, "meta_description" character varying, "keywords" character varying, "gallery_image_id" integer, "meta_type" character varying, "reference_id" integer, "image_gallery_id" integer, CONSTRAINT "PK_2a849d7e3165b26dec3e1471b9b" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "seo_meta" ADD CONSTRAINT "FK_7fa670a01361654f51e0c5ccf3b" FOREIGN KEY ("image_gallery_id") REFERENCES "image_gallery"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "seo_meta" DROP CONSTRAINT "FK_7fa670a01361654f51e0c5ccf3b"`);
        await queryRunner.query(`DROP TABLE "seo_meta"`);
        await queryRunner.query(`ALTER TABLE "setup_home_partner_instagram" ADD CONSTRAINT "FK_a5c9da21be5cab086fc3e6dee9e" FOREIGN KEY ("country_id") REFERENCES "country"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
