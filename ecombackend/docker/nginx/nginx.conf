events {}

http {
    upstream nestjs_backend {
        server app:3000;
        server app:3001;
        server app:3002;
        # server app:3003;
    }

    server {
        listen 80;

        location / {
            proxy_pass http://nestjs_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}
