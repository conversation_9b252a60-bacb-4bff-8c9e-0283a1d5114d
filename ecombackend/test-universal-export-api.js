const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';

// Mock JWT token for testing (you might need to get a real one)
const MOCK_TOKEN = 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c';

const headers = {
  'Content-Type': 'application/json',
  'Authorization': MOCK_TOKEN
};

async function testUniversalExportAPI() {
  console.log('🧪 Testing Universal Export API');
  console.log('================================');

  try {
    // Test 1: Get source fields
    console.log('\n1. Testing GET /universal-export/source-fields');
    try {
      const response = await axios.get(`${BASE_URL}/universal-export/source-fields`, { headers });
      console.log('✅ Source fields:', response.data);
    } catch (error) {
      console.log('❌ Source fields error:', error.response?.data || error.message);
    }

    // Test 2: Get all export templates
    console.log('\n2. Testing GET /universal-export');
    try {
      const response = await axios.get(`${BASE_URL}/universal-export?page=1&take=10`, { headers });
      console.log('✅ Export templates:', response.data);
    } catch (error) {
      console.log('❌ Export templates error:', error.response?.data || error.message);
    }

    // Test 3: Get specific export template
    console.log('\n3. Testing GET /universal-export/1');
    try {
      const response = await axios.get(`${BASE_URL}/universal-export/1`, { headers });
      console.log('✅ Specific export template:', response.data);
    } catch (error) {
      console.log('❌ Specific export template error:', error.response?.data || error.message);
    }

    // Test 4: Create new export template
    console.log('\n4. Testing POST /universal-export');
    const newExportData = {
      name: 'Test API Export',
      countryId: 1,
      format: 'csv',
      delimiter: ',',
      imageLimit: 2,
      includeVariants: true,
      includeInactive: false,
      description: 'Test export created via API',
      fieldMappings: [
        {
          sourceField: 'id',
          targetField: 'Product_ID',
          fieldType: 'number',
          isRequired: true,
          sortOrder: 0
        },
        {
          sourceField: 'name',
          targetField: 'Product_Name',
          fieldType: 'string',
          isRequired: true,
          sortOrder: 1
        },
        {
          sourceField: 'unitPrice',
          targetField: 'Price',
          fieldType: 'number',
          isRequired: false,
          defaultValue: '0.00',
          sortOrder: 2
        }
      ],
      productsId: [1, 2, 3]
    };

    try {
      const response = await axios.post(`${BASE_URL}/universal-export`, newExportData, { headers });
      console.log('✅ Created export template:', response.data);
    } catch (error) {
      console.log('❌ Create export template error:', error.response?.data || error.message);
    }

    // Test 5: Test export generation (this might fail due to missing products)
    console.log('\n5. Testing GET /universal-export/export/sample-csv-export-12345678');
    try {
      const response = await axios.get(`${BASE_URL}/universal-export/export/sample-csv-export-12345678`, { 
        headers,
        responseType: 'stream'
      });
      console.log('✅ Export generation successful, content-type:', response.headers['content-type']);
      
      // Save a small sample of the response
      let data = '';
      response.data.on('data', chunk => {
        data += chunk;
        if (data.length > 500) { // Only capture first 500 chars
          response.data.destroy();
        }
      });
      
      response.data.on('end', () => {
        console.log('📄 Export sample (first 500 chars):', data.substring(0, 500));
      });
      
    } catch (error) {
      console.log('❌ Export generation error:', error.response?.data || error.message);
    }

  } catch (error) {
    console.error('❌ General error:', error.message);
  }
}

// Run the tests
testUniversalExportAPI().then(() => {
  console.log('\n🏁 API testing completed');
}).catch(error => {
  console.error('❌ Test suite failed:', error.message);
});
