const { Client } = require('pg');
const fastcsv = require('fast-csv');
const fs = require('fs');

async function testDirectExport() {
  console.log('🔍 Testing Direct Export Generation');
  console.log('===================================');

  // Database connection
  const client = new Client({
    host: 'localhost',
    user: 'postgres',
    password: '',
    database: 'playground',
    port: 5432,
  });

  try {
    await client.connect();
    console.log('✅ Connected to database');

    // Get export template
    console.log('\n1. Getting export template...');
    const templateQuery = `
      SELECT ue.*, fm.source_field, fm.target_field, fm.field_type, fm.sort_order
      FROM universal_export ue
      LEFT JOIN field_mapping fm ON ue.id = fm.universal_export_id
      WHERE ue.slug = $1
      ORDER BY fm.sort_order
    `;
    
    const templateResult = await client.query(templateQuery, ['sample-csv-export-12345678']);
    
    if (templateResult.rows.length === 0) {
      throw new Error('Export template not found');
    }

    const template = templateResult.rows[0];
    const fieldMappings = templateResult.rows.map(row => ({
      sourceField: row.source_field,
      targetField: row.target_field,
      fieldType: row.field_type,
      sortOrder: row.sort_order
    })).filter(fm => fm.sourceField); // Remove null mappings

    console.log('✅ Template found:', template.name);
    console.log('📋 Field mappings:', fieldMappings.length);

    // Get products
    console.log('\n2. Getting products...');
    const productsQuery = `
      SELECT p.id, p.name, p.sku, p.slug, p.description, p.is_active, 
             p.created_at, p.updated_at, b.name as brand_name
      FROM product p
      INNER JOIN universal_export_products uep ON p.id = uep.product_id
      LEFT JOIN brand b ON p.brand_id = b.id
      WHERE uep.universal_export_id = $1
        AND p.is_publish = true
        AND p.deleted_at IS NULL
      ORDER BY p.id
      LIMIT 10
    `;

    const productsResult = await client.query(productsQuery, [template.id]);
    console.log('✅ Found products:', productsResult.rows.length);

    if (productsResult.rows.length === 0) {
      console.log('⚠️ No products found for this export template');
      return;
    }

    // Generate CSV
    console.log('\n3. Generating CSV...');
    const csvData = [];
    
    // Add headers
    const headers = fieldMappings
      .sort((a, b) => a.sortOrder - b.sortOrder)
      .map(fm => fm.targetField);
    
    csvData.push(headers);

    // Add data rows
    productsResult.rows.forEach(product => {
      const row = [];
      fieldMappings
        .sort((a, b) => a.sortOrder - b.sortOrder)
        .forEach(fm => {
          let value = '';
          switch (fm.sourceField) {
            case 'id':
              value = product.id;
              break;
            case 'name':
              value = product.name || '';
              break;
            case 'sku':
              value = product.sku || '';
              break;
            case 'unitPrice':
              value = '0.00'; // Default since we don't have price data
              break;
            case 'brand':
              value = product.brand_name || 'Unknown';
              break;
            case 'images':
              value = ''; // Default empty for now
              break;
            default:
              value = '';
          }
          row.push(value);
        });
      csvData.push(row);
    });

    // Write CSV file
    const csvContent = csvData.map(row => row.join(';')).join('\n');
    fs.writeFileSync('test-direct-export.csv', csvContent);
    
    console.log('✅ CSV generated successfully!');
    console.log('📄 First few lines:');
    console.log(csvContent.split('\n').slice(0, 5).join('\n'));

    console.log('\n📊 Summary:');
    console.log(`- Template: ${template.name}`);
    console.log(`- Format: ${template.format}`);
    console.log(`- Field mappings: ${fieldMappings.length}`);
    console.log(`- Products exported: ${productsResult.rows.length}`);
    console.log(`- CSV file: test-direct-export.csv`);

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await client.end();
  }
}

testDirectExport();
