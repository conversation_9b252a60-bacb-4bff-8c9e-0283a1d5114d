name: Deploy to stage Server

on:
  push:
    branches:
      - staging

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v2

      - name: List files in repository to check for deploy.sh
        run: |
          ls -alh $GITHUB_WORKSPACE

      - name: Check if PA<PERSON><PERSON><PERSON><PERSON><PERSON> secret is set
        id: check_secret
        run: |
          if [[ -z "${{ secrets.PANTONECLO_STAGE }}" ]]; then
            echo "PANTONECLO secret is not set. Skipping deployment."
            exit 0  # Exit without failure, skipping the deploy steps
          fi
          echo "PANTONECLO secret is set. Proceeding with deployment."

      - name: Set up SSH
        if: steps.check_secret.outputs.result != 'skipped'
        env:
          SSH_PRIVATE_KEY: ${{ secrets.PANTONECLO_STAGE }}
          PANTONECLO_HOST: ${{ vars.PANTONECLO_STAGE_HOST}}
          PANTONECLO_USERNAME: ${{ vars.PANTONECLO_STAGE_USERNAME}}
        run: |
          mkdir -p ~/.ssh
          echo "$SSH_PRIVATE_KEY" | tr -d '\r' > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          # Add the server to the known hosts to prevent SSH prompt
          ssh-keyscan -H "$PANTONECLO_HOST" >> ~/.ssh/known_hosts

      - name: Debug SSH Connection
        if: steps.check_secret.outputs.result != 'skipped'
        env:
          PANTONECLO_HOST: ${{ vars.PANTONECLO_STAGE_HOST}}
          PANTONECLO_USERNAME: ${{ vars.PANTONECLO_STAGE_USERNAME}}
        run: |
          ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no "$PANTONECLO_USERNAME@$PANTONECLO_HOST" 'echo "SSH connection successful"'

      - name: Deploy Application
        if: steps.check_secret.outputs.result != 'skipped'
        env:
          PANTONECLO_HOST: ${{ vars.PANTONECLO_STAGE_HOST}}
          PANTONECLO_USERNAME: ${{ vars.PANTONECLO_STAGE_USERNAME}}
        run: |

          ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no "$PANTONECLO_USERNAME@$PANTONECLO_HOST" << 'EOF'

            cd /root/pantoneclo/ecombackend
            
            git reset --hard
            git clean -df
            git reset --hard .
            
            GIT_SSH_COMMAND="ssh -i ~/.ssh/pantoneclo/id_rsa" git pull

            docker compose build && docker compose up -d
          EOF
