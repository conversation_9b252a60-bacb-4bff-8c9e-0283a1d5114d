// Test script to verify commission calculation in validateCommissionCalculation method
const axios = require('axios');

async function testCommissionValidation() {
  const baseURL = 'http://localhost:4000/api';
  
  // Test data
  const testData = {
    cartId: 2510,
    couponCode: 'SKPC2025'
  };

  console.log('🧪 Testing Commission Validation...\n');

  try {
    // Test 1: Preview Cart Commission (existing method)
    console.log('1️⃣ Testing previewCartCommission:');
    try {
      const previewResponse = await axios.post(`${baseURL}/affiliate/preview-commission`, testData);
      console.log('✅ Preview Commission Response:');
      console.log(JSON.stringify(previewResponse.data, null, 2));
    } catch (error) {
      console.log('❌ Preview Commission Error:', error.response?.data || error.message);
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // Test 2: Validate Commission Calculation (updated method)
    console.log('2️⃣ Testing validateCommissionCalculation:');
    try {
      const validateResponse = await axios.post(`${baseURL}/affiliate/validate-commission`, testData);
      console.log('✅ Validate Commission Response:');
      console.log(JSON.stringify(validateResponse.data, null, 2));
    } catch (error) {
      console.log('❌ Validate Commission Error:', error.response?.data || error.message);
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // Test 3: Compare commission calculations
    console.log('3️⃣ Comparing Commission Calculations:');
    try {
      const [previewRes, validateRes] = await Promise.all([
        axios.post(`${baseURL}/affiliate/preview-commission`, testData),
        axios.post(`${baseURL}/affiliate/validate-commission`, testData)
      ]);

      const previewCommission = previewRes.data.data?.estimatedCommission;
      const validateCommission = validateRes.data.data?.commission?.estimatedCommission;

      console.log(`Preview Commission: ${previewCommission}`);
      console.log(`Validate Commission: ${validateCommission}`);
      
      if (previewCommission && validateCommission) {
        const difference = Math.abs(previewCommission - validateCommission);
        console.log(`Difference: ${difference}`);
        console.log(difference < 0.01 ? '✅ Calculations match!' : '❌ Calculations differ!');
      }
    } catch (error) {
      console.log('❌ Comparison Error:', error.message);
    }

  } catch (error) {
    console.error('❌ Test Error:', error.message);
  }
}

// Run the test
testCommissionValidation();
