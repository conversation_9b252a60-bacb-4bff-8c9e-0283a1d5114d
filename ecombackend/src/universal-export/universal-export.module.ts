import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UniversalExportController } from './universal-export.controller';
import { UniversalExportService } from './universal-export.service';

import { UniversalExportProductItemRepository } from './repositories/universal-export-product-item.repository';
import { UniversalExport } from '../domain/entities/universal-export.entity';
import { FieldMapping } from '../domain/entities/field-mapping.entity';
import { Product } from '../domain/entities/product.entity';
import { CsvExportService } from './strategy/csv-export.service';
import { XlsxExportService } from './strategy/xlsx-export.service';
import { XmlExportService } from './strategy/xml-export.service';
import { SkroutzExportService } from './strategy/skroutz-export.service';


@Module({
  imports: [
    TypeOrmModule.forFeature([UniversalExport, FieldMapping, Product])
  ],
  controllers: [UniversalExportController],
  providers: [
    UniversalExportService,
    UniversalExportProductItemRepository,
    CsvExportService,
    XlsxExportService,
    XmlExportService,
    SkroutzExportService,
  ],
  exports: [UniversalExportService],
})
export class UniversalExportModule {}
