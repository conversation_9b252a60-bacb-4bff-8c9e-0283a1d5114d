import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { has } from 'lodash';
import { UniversalExport } from '../../domain/entities/universal-export.entity';
import { Product } from '../../domain/entities/product.entity';
import { formatCategories } from 'src/product-feed/utils/helper';

@Injectable()
export class UniversalExportProductItemRepository extends Repository<Product> {
  constructor(
    dataSource: DataSource,
    @InjectRepository(UniversalExport)
    private universalExportRepository: Repository<UniversalExport>,
  ) {
    super(
      Product,
      dataSource.createEntityManager(),
      dataSource.createQueryRunner(),
    );
  }

  /**
   * Get enhanced product data for universal export with proper joins and localization
   */
  async getEnhancedExportProducts(
    universalExport: UniversalExport,
    chunkSize: number = 100,
    offset: number = 0,
  ) {
    console.log('🚀 Getting enhanced export products for:', universalExport.name);

    // Get the export with country information
    const exportWithCountry = await this.universalExportRepository.findOne({
      where: { id: universalExport.id },
      relations: ['country', 'country.currency'],
    });

    if (!exportWithCountry?.country) {
      throw new Error('Export country not found');
    }

    const country = exportWithCountry.country;
    const languageId = country.languageId;
    const baseUrl = `https://${country.domain}`;

    // Get products with comprehensive joins
    const products = await this.findProductsWithFullData(
      universalExport,
      country.id,
      languageId,
      chunkSize,
      offset,
    );

    console.log('📦 Found products:', products.length);

    if (!products.length) {
      return [];
    }

    // Get attribute localizations
    const attributesId = this.extractAttributeIds(products);
    const attrValues = attributesId.length
      ? await this.getProductAttributeLocalize(attributesId, languageId)
      : {};

    // For now, we'll use basic image processing without attributeImages
    const imageUrls = {};
    const productImageMap = {};

    // Process and flatten products
    const processedProducts = this.processProductsWithVariants(
      products,
      languageId,
      baseUrl,
      attrValues,
      imageUrls,
      productImageMap,
      country,
    );

    console.log('✅ Processed products:', processedProducts.length);
    return processedProducts;
  }

  /**
   * Find products with comprehensive data joins
   */
  private async findProductsWithFullData(
    universalExport: UniversalExport,
    countryId: number,
    languageId: number,
    chunkSize: number,
    offset: number,
  ): Promise<Product[]> {
    const query = this.createQueryBuilder('product')
      .innerJoin(
        'universal_export_products',
        'uep',
        'uep.product_id = product.id AND uep.universal_export_id = :exportId',
        { exportId: universalExport.id }
      )
      .leftJoinAndSelect('product.productMeta', 'productMeta', 'productMeta.countryId = :countryId', { countryId })
      .leftJoinAndSelect('productMeta.country', 'country')
      .leftJoinAndSelect('country.currency', 'currency')
      .leftJoinAndSelect(
        'product.localizations',
        'localization',
        'localization.languageId = :languageId AND localization.product = product.id',
        { languageId }
      )
      .leftJoinAndSelect('product.featuredImage', 'featuredImage')
      .leftJoinAndSelect('product.hoverImage', 'hoverImage')
      .leftJoinAndSelect('product.brand', 'brand')
      .leftJoinAndSelect('product.categories', 'category')
      .leftJoinAndSelect('category.parent', 'parentCategory')
      .where('product.isPublish = :isPublish', { isPublish: true })
      .andWhere('product.deletedAt IS NULL')
      .andWhere('product.isActive = :isActive', { isActive: true })
      .andWhere('productMeta.isActive = :metaIsActive', { metaIsActive: true })
      .andWhere('productMeta.deletedAt IS NULL')
      .orderBy('product.sortOrder', 'ASC')
      .skip(offset)
      .take(chunkSize);

    return await query.getMany();
  }

  /**
   * Extract attribute IDs from products for localization
   */
  private extractAttributeIds(products: Product[]): number[] {
    return [
      ...new Set(
        products.flatMap((product) => {
          return product.selectedAttributes?.flatMap(
            (attribute) => attribute.values || [],
          ) || [];
        }),
      ),
    ];
  }

  /**
   * Get localized attribute values
   */
  private async getProductAttributeLocalize(
    attributesId: number[],
    languageId: number,
  ): Promise<Record<number, string>> {
    const localizedNamesMap: Record<number, string> = {};

    if (!attributesId || !attributesId.length) return localizedNamesMap;

    const filteredIds = attributesId.filter(
      (item) => item !== null && item !== undefined,
    );

    if (!filteredIds.length) return localizedNamesMap;

    const query = `
      SELECT locale->>'${languageId}' as locale_name, id
      FROM attribute_value
      WHERE id IN (${filteredIds.join(', ')})
      AND locale->>'${languageId}' IS NOT NULL
    `;

    const result = await this.manager.query(query);

    result.forEach((row) => {
      localizedNamesMap[row.id] = row.locale_name;
    });

    return localizedNamesMap;
  }

  /**
   * Process product images and create mapping
   */
  private async processProductImages(products: Product[]) {
    const images = [];
    const productImageMap = {};

    products.forEach((product) => {
      const _productImage = {};
      product?.attributeImages?.forEach((item) => {
        if (item.images && item.images.length > 0) {
          const selectImages = [item.images[0]];
          images.push(item.images[0]);
          if (item.images.length > 1) {
            selectImages.push(item.images[1]);
            images.push(item.images[1]);
          }
          _productImage[item.attributeValueId] = selectImages;
        }
      });

      if (Object.keys(_productImage).length) {
        productImageMap[product.id] = _productImage;
      }
    });

    const imageUrls = await this.getImageUrls([...new Set(images)]);

    return { imageUrls, productImageMap };
  }

  /**
   * Get image URLs from database
   */
  private async getImageUrls(imagesId: number[]): Promise<Record<number, string>> {
    const images: Record<number, string> = {};

    if (!imagesId || !imagesId.length) return images;

    const query = `
      SELECT original_image_url, id
      FROM image_gallery
      WHERE id IN (${imagesId.join(', ')})
    `;

    const result = await this.manager.query(query);

    result.forEach((row) => {
      images[row.id] = row.original_image_url;
    });

    return images;
  }

  /**
   * Process products with variants and create flattened structure
   */
  private processProductsWithVariants(
    products: Product[],
    languageId: number,
    baseUrl: string,
    attrValues: Record<number, string>,
    imageUrls: Record<number, string>,
    productImageMap: any,
    country: any,
  ) {
    return products.flatMap((product) => {
      const localization = product.localizations?.find(
        (localize) => localize.languageId == languageId,
      );

      const _categories = product.categories;
      const categories = _categories
        ? formatCategories(_categories, languageId)
        : [];

      const categoriesEn = _categories
        ? formatCategories(_categories)
        : [];

      const productImagesId = productImageMap[product.id];

      let image1 = product.featuredImage?.originalImageUrl;
      let image2 = product.hoverImage?.originalImageUrl;

      // Handle multi-variant products
      if (product.isMultiVariantProduct && product.productMeta?.variants) {
        return product.productMeta.variants.flatMap((variant) => {
          return variant.variantDetails.map((detail) => {
            const itemSKU = detail.sku;
            
            // Reset images for each variant
            image1 = product.featuredImage?.originalImageUrl;
            image2 = product.hoverImage?.originalImageUrl;

            // Process color (simplified without attributeImages)
            const colorStr = detail.color?.name || '';
            const variantImage1 = image1;
            const variantImage2 = image2;

            // Process size
            const sizeStr = this.processVariantSize(variant.size, attrValues);

            return this.createProductData(
              product,
              localization,
              itemSKU,
              sizeStr,
              colorStr,
              detail.quantity ? Number(detail.quantity) : 0,
              detail.unitPrice ? Number(detail.unitPrice) : 0,
              detail.discountPrice ? Number(detail.discountPrice) : 0,
              [variantImage1, variantImage2],
              detail.isActive,
              baseUrl,
              categories,
              categoriesEn,
              country,
            );
          });
        });
      } else {
        // Handle single variant products
        return this.createProductData(
          product,
          localization,
          product.sku,
          '',
          '',
          product.productMeta?.quantity || 0,
          product.productMeta?.unitPrice ? Number(product.productMeta.unitPrice) : 0,
          product.productMeta?.discountPrice ? Number(product.productMeta.discountPrice) : 0,
          [image1, image2],
          product.productMeta?.isActive || false,
          baseUrl,
          categories,
          categoriesEn,
          country,
        );
      }
    });
  }

  /**
   * Process variant color and get appropriate images
   */
  private processVariantColor(
    color: any,
    productImagesId: any,
    imageUrls: Record<number, string>,
    attrValues: Record<number, string>,
    defaultImage1: string,
    defaultImage2: string,
  ) {
    let colorStr = '';
    let variantImage1 = defaultImage1;
    let variantImage2 = defaultImage2;

    if (color?.id) {
      const colorId = color.id;

      // Get color-specific images
      if (has(productImagesId, colorId)) {
        const imagesId = productImagesId[colorId];

        if (imagesId.length) {
          const firstImageId = imagesId[0];
          const secondImageId = imagesId[1];

          if (firstImageId && has(imageUrls, firstImageId)) {
            variantImage1 = imageUrls[firstImageId];
          }

          if (secondImageId && has(imageUrls, secondImageId)) {
            variantImage2 = imageUrls[secondImageId];
          }
        }
      }

      // Get localized color name
      const _colorStr = has(attrValues, colorId)
        ? attrValues[colorId]
        : color.name;

      colorStr = _colorStr || color.name;
    }

    return { colorStr, variantImage1, variantImage2 };
  }

  /**
   * Process variant size
   */
  private processVariantSize(size: any, attrValues: Record<number, string>): string {
    if (!size?.id) return '';

    const sizeId = size.id;
    const _sizeStr = has(attrValues, sizeId)
      ? attrValues[sizeId]
      : size.name;

    return _sizeStr || size.name;
  }

  /**
   * Create standardized product data object
   */
  private createProductData(
    product: Product,
    localization: any,
    sku: string,
    size: string,
    color: string,
    quantity: number,
    unitPrice: number,
    discountPrice: number,
    images: string[],
    isActive: boolean,
    baseUrl: string,
    categories: string[],
    categoriesEn: string[],
    country: any,
  ) {
    const skuParam = sku !== product.sku ? `?sku=${sku}` : '';
    
    return {
      id: product.id,
      name: localization?.name || product.name,
      description: localization?.description || product.description,
      slug: product.slug,
      originalSku: product.sku,
      sku: sku,
      metaSku: sku, // For compatibility
      size: size,
      color: color,
      quantity: quantity,
      unitPrice: unitPrice,
      discountPrice: discountPrice,
      price: unitPrice,
      salePrice: discountPrice || unitPrice,
      images: images.filter(Boolean),
      isActive: isActive,
      isPublish: product.isPublish,
      link: `${baseUrl}/product/${product.slug}${skuParam}`,
      shipping: country.shippingCharge || '',
      brand: product.brand?.name || '',
      categories: categories,
      categoriesEn: categoriesEn,
      rawCategories: categories.join(','),
      currency: country.currency?.currency || '',
      createdAt: product.createdAt,
      updatedAt: product.updatedAt,
      // Additional fields for enhanced exports
      ean: sku, // Use SKU as EAN for now
      gtin: sku, // Use SKU as GTIN for now
      hsCode: '',
      countryOfOrigin: country.code || 'BD',
      weight:  '',
      availability: quantity > 0 ? 'in stock' : 'out of stock',
      condition: 'new',
      gender: '',
      ageGroup: 'adult',
      material: '',
      pattern: '',
    };
  }
}
