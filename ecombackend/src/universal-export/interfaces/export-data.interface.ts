/**
 * Standardized export data object containing all possible export properties
 * This is the single data bucket used by all export strategies
 */
export interface ExportDataObject {
  // Core product information
  id: number;
  sku: string;
  name: string;
  title: string;
  description: string;
  link: string;
  
  // Brand information
  brand: string;
  
  // Pricing information
  unitPrice: number;
  price: number;
  discountPrice: number;
  salePrice: number;
  currency: string;
  
  // Inventory information
  quantity: number;
  availability: string;
  status: string;
  condition: string;
  
  // Image information
  images: string;
  imageLink: string;
  additionalImageLink: string;
  
  // Category information
  categories: string;
  googleProductCategory: string;
  fbProductCategory: string;
  
  // Product attributes
  color: string;
  size: string;
  gender: string;
  ageGroup: string;
  material: string;
  pattern: string;
  
  // Additional identifiers
  itemGroupId: string;
  gtin: string;
  ean: string;
  hsCode: string;
  countryOfOrigin: string;
  
  // Shipping information
  shipping: string;
  shippingWeight: string;
  
  // System information
  isActive: string;
  createdAt: string;
  updatedAt: string;
}
