import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  Res,
  StreamableFile,
  BadRequestException,
} from '@nestjs/common';
import { Response } from 'express';
import { UniversalExportService } from './universal-export.service';
import { CreateUniversalExportDto } from './dto/create-universal-export.dto';
import { UpdateUniversalExportDto } from './dto/update-universal-export.dto';
import { ExportRequestDto } from './dto/export-request.dto';
import { PageOptionsDto } from '../common/pagination/page-options.dto';
import { ServiceResponse } from '../common/utils/service-response';
import { ExportFormat } from '../domain/entities/universal-export.entity';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

@ApiTags('Universal Export')
@Controller('universal-export')
export class UniversalExportController {
  constructor(private readonly universalExportService: UniversalExportService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new export template' })
  @ApiResponse({ status: 201, description: 'Export template created successfully' })
  async create(
    @Body() createUniversalExportDto: CreateUniversalExportDto,
    // TODO: Get user ID from JWT token
    // @Request() req: any,
  ) {
    const createdBy = 1; // TODO: Replace with actual user ID from JWT
    return this.universalExportService.create(createUniversalExportDto, createdBy);
  }

  @Get()
  @ApiOperation({ summary: 'Get all export templates with pagination' })
  @ApiResponse({ status: 200, description: 'Export templates retrieved successfully' })
  async findAll(
    @Query() params: { globalSearch?: string },
    @Query() pageOptionsDto: PageOptionsDto,
  ) {
    return this.universalExportService.findAllDataByPagination(params, pageOptionsDto);
  }

  @Get('source-fields')
  @ApiOperation({ summary: 'Get available source fields for mapping' })
  @ApiResponse({ status: 200, description: 'Source fields retrieved successfully' })
  getSourceFields() {
    const fields = this.universalExportService.getAvailableSourceFields();
    return new ServiceResponse(fields, 'Source fields retrieved successfully');
  }

  @Get('test-export/:slug')
  @ApiOperation({ summary: 'Test export generation (debug)' })
  async testExport(@Param('slug') slug: string) {
    try {
      console.log('🔍 Test Export - Starting for slug:', slug);

      // Test finding the template
      const template = await this.universalExportService.findBySlug(slug);
      console.log('✅ Template found:', template.name);

      // Test completed - repository integration working

      return new ServiceResponse({
        template: {
          id: template.id,
          name: template.name,
          fieldMappings: template.fieldMappings.length
        },
        message: 'Test completed successfully'
      }, 'Test export debug completed');
    } catch (error) {
      console.error('❌ Test Export Error:', error);
      return new ServiceResponse(null, `Test failed: ${error.message}`);
    }
  }

  @Get('data/:slug.:extension')
  @ApiOperation({ summary: 'Stream export file' })
  @ApiResponse({ status: 200, description: 'Export file streamed successfully' })
  async streamExport(
    @Param('slug') slug: string,
    @Param('extension') extension: string,
    @Query() filters: ExportRequestDto,
    @Res() res: Response,
  ): Promise<void> {
    // Validate the file extension
    if (!['csv', 'xlsx', 'xml', 'skroutz'].includes(extension)) {
      throw new BadRequestException(
        'Unsupported file format. Only csv, xlsx, xml, or skroutz is allowed.',
      );
    }

    res.setTimeout(350000); // 5.5 minutes

    // Convert extension to ExportFormat enum
    const requestedFormat = extension.toLowerCase() as ExportFormat;
    const stream = await this.universalExportService.generateExport(slug, filters, requestedFormat);

    const contentType = this.getContentType(extension);
    res.setHeader('Content-Type', contentType);
    res.setHeader(
      'Content-Disposition',
      `attachment; filename=export-${slug}.${extension}`,
    );

    stream.pipe(res);
  }



  @Get('export/:slug')
  @ApiOperation({ summary: 'Generate and download export file (legacy)' })
  @ApiResponse({ status: 200, description: 'Export file generated successfully' })
  async generateExportLegacy(
    @Param('slug') slug: string,
    @Query() filters: ExportRequestDto,
    @Res() res: Response,
  ): Promise<void> {
    try {
      console.log('🔍 Export Controller - Starting export for slug:', slug);

      // Get the export template
      const exportTemplate = await this.universalExportService.findBySlug(slug);
      console.log('✅ Template found:', exportTemplate.name);

      res.setTimeout(350000); // 5.5 minutes

      console.log('🚀 Generating export stream...');
      // Default to CSV format for legacy endpoint
      const stream = await this.universalExportService.generateExport(slug, filters, ExportFormat.CSV);
      console.log('✅ Stream generated successfully');

      const contentType = this.getContentType('csv');
      res.setHeader('Content-Type', contentType);
      res.setHeader(
        'Content-Disposition',
        `attachment; filename=export-${slug}.csv`,
      );

      console.log('📤 Piping stream to response');
      stream.pipe(res);

      stream.on('error', (error) => {
        console.error('❌ Stream error:', error);
        if (!res.headersSent) {
          res.status(500).json({
            isSuccess: false,
            message: 'Export generation failed',
            error: error.message
          });
        }
      });

    } catch (error) {
      console.error('❌ Export Controller Error:', error);
      if (!res.headersSent) {
        res.status(500).json({
          isSuccess: false,
          message: 'Export generation failed',
          error: error.message
        });
      }
    }
  }

  @Get(':id/skroutz-excel')
  @ApiOperation({ summary: 'Download Skroutz Excel file for export' })
  @ApiResponse({ status: 200, description: 'Skroutz Excel file downloaded successfully' })
  async downloadSkroutzExcel(
    @Param('id') id: number,
    @Res() res: Response,
  ): Promise<void> {
    try {
      const response = await this.universalExportService.findOne(id);
      if (!response.data) {
        throw new BadRequestException('Export not found');
      }

      const universalExport = response.data;
      res.setTimeout(350000); // 5.5 minutes

      // Generate Skroutz Excel using the new strategy
      const stream = await this.universalExportService.generateExport(
        universalExport.slug,
        {},
        ExportFormat.SKROUTZ
      );

      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader(
        'Content-Disposition',
        `attachment; filename=skroutz-export-${universalExport.slug}.xlsx`,
      );

      stream.pipe(res);
    } catch (error) {
      console.error('Structured Excel Export Error:', error);
      throw error;
    }
  }

  @Get(':id/download-excel')
  @ApiOperation({ summary: 'Get download link for structured Excel' })
  async getExcelDownloadLink(@Param('id') id: number): Promise<any> {
    const response = await this.universalExportService.findOne(id);
    if (!response.data) {
      throw new BadRequestException('Export not found');
    }

    const universalExport = response.data;
    return {
      message: 'Excel download link',
      exportId: id,
      exportName: universalExport.name,
      downloadUrl: `/api/universal-export/${id}/skroutz-excel`,
      format: 'skroutz'
    };
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a single export template' })
  @ApiResponse({ status: 200, description: 'Export template found' })
  async findOne(@Param('id') id: string) {
    return this.universalExportService.findOne(+id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update an export template' })
  @ApiResponse({ status: 200, description: 'Export template updated successfully' })
  async update(
    @Param('id') id: string,
    @Body() updateUniversalExportDto: UpdateUniversalExportDto,
  ) {
    return this.universalExportService.update(+id, updateUniversalExportDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete an export template' })
  @ApiResponse({ status: 200, description: 'Export template deleted successfully' })
  async remove(@Param('id') id: string) {
    return this.universalExportService.remove(+id);
  }

  @Get(':id/products')
  @ApiOperation({ summary: 'Get products associated with export template' })
  @ApiResponse({ status: 200, description: 'Products retrieved successfully' })
  async getExportProducts(
    @Param('id') id: string,
    @Query() params: { globalSearch?: string },
    @Query() pageOptionsDto: PageOptionsDto,
  ) {
    return this.universalExportService.getExportProducts(
      +id,
      pageOptionsDto,
      params.globalSearch,
    );
  }

  @Post(':id/products')
  @ApiOperation({ summary: 'Add products to export template' })
  @ApiResponse({ status: 200, description: 'Products added successfully' })
  async addProducts(
    @Param('id') id: string,
    @Body() body: { productIds: number[] },
  ) {
    return this.universalExportService.addProducts(+id, body.productIds);
  }

  @Delete(':id/products/:productId')
  @ApiOperation({ summary: 'Remove product from export template' })
  @ApiResponse({ status: 200, description: 'Product removed successfully' })
  async removeProduct(
    @Param('id') id: string,
    @Param('productId') productId: string,
  ) {
    return this.universalExportService.removeProduct(+id, +productId);
  }

  @Delete(':id/products')
  @ApiOperation({ summary: 'Remove multiple products from export template' })
  @ApiResponse({ status: 200, description: 'Products removed successfully' })
  async removeProducts(
    @Param('id') id: string,
    @Body() body: { productIds: number[] },
  ) {
    return this.universalExportService.removeProducts(+id, body.productIds);
  }

  private getContentType(format: string): string {
    switch (format) {
      case 'csv':
        return 'text/csv';
      case 'xlsx':
        return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      case 'xml':
        return 'application/xml';
      default:
        return 'application/octet-stream';
    }
  }
}
