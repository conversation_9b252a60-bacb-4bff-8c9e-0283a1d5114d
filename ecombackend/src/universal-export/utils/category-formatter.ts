import { Category } from '../../domain/entities/category.entity';
import { has } from 'lodash';

/**
 * Format categories with hierarchy (Parent > Child)
 */
export function formatCategories(
  categories: Category[],
  languageId?: number,
): string[] {
  return categories.map((category) => {
    const _categoryName = getLocalizeName(
      languageId,
      category.locale,
      category.name,
    );

    const hierarchy = category.parent
      ? `${getLocalizeName(
          languageId,
          category.parent.locale,
          category.parent.name,
        )} > ${_categoryName}`
      : _categoryName;
    return hierarchy;
  });
}

/**
 * Format child categories array with hierarchy
 */
export function formatChildCategoriesArr(
  categories: any[],
  languageId?: number,
): string[] {
  return categories.reduce((acc, category) => {
    const _categoryName = getLocalizeName(
      languageId,
      category.locale,
      category.name,
    );

    // Check if there are child categories and format them
    if (category.children && category.children.length > 0) {
      category.children.forEach((child) => {
        const childName = getLocalizeName(languageId, child.locale, child.name);
        acc.push(`${_categoryName} > ${childName}`);
      });
    } else {
      // If no children, just add the parent category
      acc.push(_categoryName);
    }

    return acc;
  }, []);
}

/**
 * Get localized value from locale object
 */
export const getLocalizeValue = (
  languageId?: number,
  locale?: any,
  value?: any,
) => {
  if (!locale || !languageId) return value;

  const _localeTitle = has(locale, languageId) ? locale[languageId] : undefined;

  return _localeTitle || value;
};

/**
 * Get localized name from locale object
 */
export const getLocalizeName = (
  languageId?: number,
  locale?: any,
  value?: any,
) => {
  if (!locale || !languageId) return value;

  const _localeTitle = has(locale, languageId)
    ? locale[languageId]?.name
    : undefined;

  return _localeTitle || value;
};

/**
 * Get localized description from locale object
 */
export const getLocalizeDescription = (
  languageId?: number,
  locale?: any,
  value?: any,
) => {
  if (!locale || !languageId) return value;

  const _localeTitle = has(locale, languageId)
    ? locale[languageId]?.description
    : undefined;

  return _localeTitle || value;
};

/**
 * Format categories for Skroutz (pipe-separated)
 */
export function formatCategoriesForSkroutz(
  categories: string[],
): string {
  return categories
    .join('|')
    .split(' > ')
    .map(cat => cat.trim())
    .join('|');
}

/**
 * Parse category string and clean it
 */
export function parseCategoryString(categoryStr: string): string[] {
  if (!categoryStr) return [];
  
  return categoryStr
    .split(',')
    .map(cat => cat.trim())
    .filter(Boolean);
}
