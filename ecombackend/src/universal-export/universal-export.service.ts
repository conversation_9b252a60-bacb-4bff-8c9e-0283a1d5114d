import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ILike, Repository, In } from 'typeorm';
import { UniversalExport, ExportFormat } from '../domain/entities/universal-export.entity';
import { FieldMapping } from '../domain/entities/field-mapping.entity';
import { Product } from '../domain/entities/product.entity';
import { Readable } from 'stream';
import { CreateUniversalExportDto } from './dto/create-universal-export.dto';
import { UpdateUniversalExportDto } from './dto/update-universal-export.dto';
import { ExportRequestDto } from './dto/export-request.dto';
import { ServiceResponse } from '../common/utils/service-response';
import { PageOptionsDto } from '../common/pagination/page-options.dto';
import { PageMetaDto } from '../common/pagination/page-meta.dto';
import { PageDto } from '../common/pagination/page.dto';
import { v4 as uuidv4 } from 'uuid';


import { ExportStrategy } from './strategy/export-strategy.interface';
import { CsvExportService } from './strategy/csv-export.service';
import { XlsxExportService } from './strategy/xlsx-export.service';
import { XmlExportService } from './strategy/xml-export.service';
import { SkroutzExportService } from './strategy/skroutz-export.service';

@Injectable()
export class UniversalExportService {
  private exportStrategies: Map<ExportFormat, ExportStrategy>;

  constructor(
    @InjectRepository(UniversalExport)
    private universalExportRepository: Repository<UniversalExport>,
    @InjectRepository(FieldMapping)
    private fieldMappingRepository: Repository<FieldMapping>,
    @InjectRepository(Product)
    private productRepository: Repository<Product>,
    private csvExportService: CsvExportService,
    private xlsxExportService: XlsxExportService,
    private xmlExportService: XmlExportService,
    private skroutzExportService: SkroutzExportService,
  ) {
    // Register export strategies
    this.exportStrategies = new Map<ExportFormat, ExportStrategy>([
      [ExportFormat.CSV, this.csvExportService],
      [ExportFormat.XLSX, this.xlsxExportService],
      [ExportFormat.XML, this.xmlExportService],
      [ExportFormat.SKROUTZ, this.skroutzExportService],
    ]);
  }

  /**
   * Generate export stream
   */
  async generateExport(
    slug: string,
    filters?: ExportRequestDto,
    requestedFormat?: ExportFormat,
  ): Promise<Readable> {
    const universalExport = await this.universalExportRepository.findOne({
      where: { slug },
      relations: ['country', 'fieldMappings'],
    });

    if (!universalExport) {
      throw new NotFoundException(`Export template with slug ${slug} not found`);
    }

    // Validate that country is set
    if (!universalExport.countryId) {
      throw new BadRequestException('Export template must have a country assigned');
    }

    // Use requested format (required since templates no longer have default format)
    const format = requestedFormat || ExportFormat.CSV; // Default to CSV if not specified
    const strategy = this.exportStrategies.get(format);
    if (!strategy) {
      throw new BadRequestException('Unsupported export format');
    }

    return strategy.export(universalExport);
  }

  /**
   * Create new export template
   */
  async create(
    createDto: CreateUniversalExportDto,
    createdBy: number,
  ): Promise<ServiceResponse> {
    const slug = this.generateSlug(createDto.name);
    
    // Check if slug already exists
    const existingExport = await this.universalExportRepository.findOne({
      where: { slug },
    });

    if (existingExport) {
      throw new BadRequestException('Export template with this name already exists');
    }

    const universalExport = this.universalExportRepository.create({
      ...createDto,
      slug,
      createdBy,
    });

    const savedExport = await this.universalExportRepository.save(universalExport);

    // Create field mappings with proper data sanitization
    if (createDto.fieldMappings && createDto.fieldMappings.length > 0) {
      const fieldMappings = createDto.fieldMappings.map((mapping, index) =>
        this.fieldMappingRepository.create({
          sourceField: String(mapping.sourceField),
          targetField: String(mapping.targetField),
          fieldType: mapping.fieldType,
          isRequired: Boolean(mapping.isRequired),
          defaultValue: mapping.defaultValue ? String(mapping.defaultValue) : null,
          sortOrder: mapping.sortOrder !== undefined ? Number(mapping.sortOrder) : index,
          formatPattern: mapping.formatPattern ? String(mapping.formatPattern) : null,
          description: mapping.description ? String(mapping.description) : null,
          universalExportId: Number(savedExport.id),
        })
      );

      await this.fieldMappingRepository.save(fieldMappings);
    }

    // Associate products
    if (createDto.productsId && createDto.productsId.length > 0) {
      const products = await this.productRepository.findBy({
        id: In(createDto.productsId)
      });
      savedExport.products = products;
      await this.universalExportRepository.save(savedExport);
    }

    return new ServiceResponse(savedExport, 'Export template created successfully');
  }

  /**
   * Update export template
   */
  async update(
    id: number,
    updateDto: UpdateUniversalExportDto,
  ): Promise<ServiceResponse> {
    const universalExport = await this.universalExportRepository.findOne({
      where: { id },
      relations: ['fieldMappings'],
    });

    if (!universalExport) {
      throw new NotFoundException('Export template not found');
    }

    // Use transaction to ensure data consistency
    return await this.universalExportRepository.manager.transaction(async manager => {
      // Update basic fields (exclude fieldMappings to handle separately)
      const { fieldMappings, ...basicFields } = updateDto;
      Object.assign(universalExport, basicFields);

      // Generate new slug if name changed
      if (updateDto.name && updateDto.name !== universalExport.name) {
        universalExport.slug = this.generateSlug(updateDto.name);
      }

      // Save the export first
      const savedExport = await manager.save(UniversalExport, universalExport);

      // Update field mappings
      if (fieldMappings && fieldMappings.length > 0) {
        // Remove existing mappings
        await manager.delete(FieldMapping, { universalExportId: id });

        // Create new mappings with proper data sanitization
        const newFieldMappings = fieldMappings.map((mapping, index) =>
          manager.create(FieldMapping, {
            sourceField: String(mapping.sourceField),
            targetField: String(mapping.targetField),
            fieldType: mapping.fieldType,
            isRequired: Boolean(mapping.isRequired),
            defaultValue: mapping.defaultValue ? String(mapping.defaultValue) : null,
            sortOrder: mapping.sortOrder !== undefined ? Number(mapping.sortOrder) : index,
            formatPattern: mapping.formatPattern ? String(mapping.formatPattern) : null,
            description: mapping.description ? String(mapping.description) : null,
            universalExportId: Number(id),
          })
        );

        await manager.save(FieldMapping, newFieldMappings);
      }

      // Return the updated export with field mappings
      const finalExport = await manager.findOne(UniversalExport, {
        where: { id },
        relations: ['fieldMappings'],
      });

      return new ServiceResponse(finalExport, 'Export template updated successfully');
    });
  }

  /**
   * Get all export templates with pagination
   */
  async findAllDataByPagination(
    params: { globalSearch?: string },
    pageOptionsDto: PageOptionsDto,
  ): Promise<ServiceResponse> {
    const whereCondition = params?.globalSearch
      ? { name: ILike(`%${params.globalSearch}%`) }
      : {};

    // Use find with relations to get proper countryId
    const [list, itemCount] = await this.universalExportRepository.findAndCount({
      where: whereCondition,
      relations: ['country'],
      skip: pageOptionsDto.skip,
      take: pageOptionsDto.take,
      order: { id: 'DESC' },
    });

    // Add product count and fix countryId manually
    for (const item of list) {
      // Use raw SQL query to avoid any TypeORM entity relation issues
      const productCountResult = await this.universalExportRepository.query(
        'SELECT COUNT(*) as count FROM universal_export_products WHERE universal_export_id = $1',
        [item.id]
      );
      const productCount = productCountResult[0]?.count || 0;


      (item as any).productCount = parseInt(productCount) || 0;

      // Fix countryId from country relation
      if (item.country) {
        (item as any).countryId = item.country.id;
      }
    }

    const pageMetaDto = new PageMetaDto({ itemCount, pageOptionsDto });

    // Transform the list to ensure countryId is included and add export URLs
    const transformedList = list.map(item => ({
      ...item,
      countryId: item.country?.id || 1, // Hardcode to 1 for now since all records have Bangladesh
      exportUrls: {
        csv: `/api/universal-export/data/${item.slug}.csv`,
        xlsx: `/api/universal-export/data/${item.slug}.xlsx`,
        xml: `/api/universal-export/data/${item.slug}.xml`,
        skroutz: `/api/universal-export/data/${item.slug}.skroutz`,
        skroutzExcel: `/api/universal-export/${item.id}/skroutz-excel`
      }
    }));

    return new ServiceResponse(
      { items: transformedList, pagination: pageMetaDto },
      'Export templates retrieved successfully',
    );
  }

  /**
   * Get single export template
   */
  async findOne(id: number): Promise<ServiceResponse> {
    const universalExport = await this.universalExportRepository.findOne({
      where: { id },
      relations: ['country', 'fieldMappings', 'products'],
    });

    if (!universalExport) {
      throw new NotFoundException('Export template not found');
    }

    return new ServiceResponse(universalExport, 'Export template found');
  }

  /**
   * Get export template by slug
   */
  async findBySlug(slug: string): Promise<UniversalExport> {
    const universalExport = await this.universalExportRepository.findOne({
      where: { slug },
      relations: ['country', 'fieldMappings'],
    });

    if (!universalExport) {
      throw new NotFoundException(`Export template with slug ${slug} not found`);
    }

    return universalExport;
  }

  /**
   * Delete export template
   */
  async remove(id: number): Promise<ServiceResponse> {
    const universalExport = await this.universalExportRepository.findOne({
      where: { id },
    });

    if (!universalExport) {
      throw new NotFoundException('Export template not found');
    }

    await this.universalExportRepository.remove(universalExport);

    return new ServiceResponse(null, 'Export template deleted successfully');
  }

  /**
   * Get products associated with export template
   */
  async getExportProducts(
    id: number,
    pageOptionsDto: PageOptionsDto,
    globalSearch?: string,
  ): Promise<ServiceResponse> {
    try {
      const universalExport = await this.universalExportRepository.findOne({
        where: { id },
        relations: ['products', 'products.brand', 'products.featuredImage', 'products.productMeta'],
      });

      if (!universalExport) {
        throw new NotFoundException('Export template not found');
      }

      let query = this.productRepository
        .createQueryBuilder('product')
        .innerJoin('universal_export_products', 'uep', 'uep.product_id = product.id')
        .leftJoinAndSelect('product.brand', 'brand')
        .leftJoinAndSelect('product.featuredImage', 'featuredImage')
        .leftJoinAndSelect('product.productMeta', 'productMeta')
        .where('uep.universal_export_id = :exportId', { exportId: id })
        .andWhere('product.deleted_at IS NULL');

      if (globalSearch) {
        query = query.andWhere(
          '(product.name ILIKE :search OR product.sku ILIKE :search)',
          { search: `%${globalSearch}%` }
        );
      }

      const [products, itemCount] = await query
        .orderBy('product.id', 'DESC')
        .skip(pageOptionsDto.skip)
        .take(pageOptionsDto.take)
        .getManyAndCount();

      const pageMetaDto = new PageMetaDto({ itemCount, pageOptionsDto });
      return new ServiceResponse(
        new PageDto(products, pageMetaDto),
        'Products retrieved successfully'
      );
    } catch (error) {
      console.error('Error in getExportProducts:', error);
      throw error;
    }
  }

  /**
   * Add products to export template
   */
  async addProducts(id: number, productIds: number[]): Promise<ServiceResponse> {
    const universalExport = await this.universalExportRepository.findOne({
      where: { id },
      relations: ['products'],
    });

    if (!universalExport) {
      throw new NotFoundException('Export template not found');
    }

    const products = await this.productRepository.findBy({
      id: In(productIds),
    });

    if (products.length !== productIds.length) {
      throw new BadRequestException('Some products not found');
    }

    // Get existing product IDs to avoid duplicates
    const existingProductIds = universalExport.products.map(p => p.id);
    const newProducts = products.filter(p => !existingProductIds.includes(p.id));

    if (newProducts.length === 0) {
      return new ServiceResponse(null, 'No new products to add');
    }

    universalExport.products = [...universalExport.products, ...newProducts];
    await this.universalExportRepository.save(universalExport);

    return new ServiceResponse(
      { addedCount: newProducts.length },
      `${newProducts.length} products added successfully`
    );
  }

  /**
   * Remove product from export template
   */
  async removeProduct(id: number, productId: number): Promise<ServiceResponse> {
    const universalExport = await this.universalExportRepository.findOne({
      where: { id },
      relations: ['products'],
    });

    if (!universalExport) {
      throw new NotFoundException('Export template not found');
    }

    universalExport.products = universalExport.products.filter(p => p.id !== productId);
    await this.universalExportRepository.save(universalExport);

    return new ServiceResponse(null, 'Product removed successfully');
  }

  /**
   * Remove multiple products from export template
   */
  async removeProducts(id: number, productIds: number[]): Promise<ServiceResponse> {
    const universalExport = await this.universalExportRepository.findOne({
      where: { id },
      relations: ['products'],
    });

    if (!universalExport) {
      throw new NotFoundException('Export template not found');
    }

    universalExport.products = universalExport.products.filter(
      p => !productIds.includes(p.id)
    );
    await this.universalExportRepository.save(universalExport);

    return new ServiceResponse(
      { removedCount: productIds.length },
      `${productIds.length} products removed successfully`
    );
  }

  /**
   * Get available source fields for mapping (based on feed fields)
   */
  getAvailableSourceFields(): string[] {
    return [
      // Basic product info
      'id',
      'sku',
      'name',
      'title', // alias for name
      'description',
      'link',
      'brand',

      // Pricing (feeds-aligned structure)
      'price', // original price (was unitPrice)
      'salePrice', // discounted price (was discountPrice)
      'currency',

      // Inventory
      'quantity',
      'availability',
      'status',
      'condition',

      // Images
      'images',
      'imageLink', // first image
      'additionalImageLink', // additional images

      // Categories
      'categories',
      'googleProductCategory',
      'fbProductCategory',

      // Product attributes
      'color',
      'size',
      'gender',
      'ageGroup',
      'material',
      'pattern',

      // Additional fields
      'itemGroupId',
      'gtin',
      'shipping',
      'shippingWeight',
      'isActive',
      'createdAt',
      'updatedAt',
    ];
  }

  private generateSlug(name: string): string {
    const baseSlug = name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');
    
    return `${baseSlug}-${uuidv4().substring(0, 8)}`;
  }
}
