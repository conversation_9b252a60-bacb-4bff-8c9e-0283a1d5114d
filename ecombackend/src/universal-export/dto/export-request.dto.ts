import {
  <PERSON><PERSON>ption<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>y,
  <PERSON><PERSON><PERSON>ber,
} from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';

export class ExportRequestDto {
  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  globalSearch?: string;

  @ApiPropertyOptional({ type: [Number] })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  productIds?: number[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  categoryFilter?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  brandFilter?: string;
}
