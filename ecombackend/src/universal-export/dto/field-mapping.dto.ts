import {
  IsString,
  IsEnum,
  IsBoolean,
  IsO<PERSON>al,
  IsN<PERSON>ber,
  IsNotEmpty,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { FieldType } from '../../domain/entities/field-mapping.entity';

export class FieldMappingDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  sourceField: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  targetField: string;

  @ApiProperty({ enum: FieldType })
  @IsEnum(FieldType)
  fieldType: FieldType;

  @ApiPropertyOptional()
  @IsOptional()
  @IsBoolean()
  isRequired?: boolean;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  defaultValue?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  sortOrder?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  formatPattern?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  description?: string;
}
