import { Injectable } from '@nestjs/common';
import { Readable, PassThrough } from 'stream';
import { UniversalExport } from '../../domain/entities/universal-export.entity';
import { ExportStrategy } from './export-strategy.interface';
import { UniversalExportProductItemRepository } from '../repositories/universal-export-product-item.repository';
import { ExportDataObject } from '../interfaces/export-data.interface';

@Injectable()
export class XmlExportService implements ExportStrategy {
  constructor(
    private productItemRepo: UniversalExportProductItemRepository,
  ) {}

  async export(universalExport: UniversalExport): Promise<Readable> {
    const stream = new PassThrough();
    
    // Write XML header
    stream.write('<?xml version="1.0" encoding="UTF-8"?>\n');
    stream.write('<products>\n');

    // Process products in chunks to handle large datasets efficiently
    const batchSize = 75;
    let offset = 0;
    let hasMore = true;
    let totalProcessed = 0;

    while (hasMore) {
      const exportProducts = await this.productItemRepo.getEnhancedExportProducts(
        universalExport,
        batchSize,
        offset,
      );

      if (exportProducts.length > 0) {
        console.log(`📊 Processing XML chunk: ${exportProducts.length} products (offset: ${offset})`);

        // Process each product in the chunk
        exportProducts.forEach((product) => {
          const transformedProduct = this.transformToExportDataObject(product);
          const productXml = this.mapProductToXml(transformedProduct, universalExport);
          stream.write(productXml);
        });

        totalProcessed += exportProducts.length;
        offset += batchSize;
      } else {
        hasMore = false;
      }
    }

    console.log(`✅ XML Export completed. Total products: ${totalProcessed}`);

    // Close XML
    stream.write('</products>\n');
    stream.end();

    return stream;
  }

  /**
   * Transform enhanced product data to ExportDataObject format
   */
  private transformToExportDataObject(product: any): ExportDataObject {
    const images = Array.isArray(product.images) ? product.images.join(',') : (product.images || '');
    const categories = Array.isArray(product.categories) ? product.categories.join(',') : (product.rawCategories || product.categories || '');

    return {
      // Core product information
      id: product.id,
      sku: product.sku || '',
      name: product.name || '',
      title: product.name || '',
      description: product.description || '',
      link: product.link || '',

      // Brand information
      brand: product.brand || '',

      // Pricing information
      unitPrice: product.unitPrice || product.price || 0,
      price: product.price || product.unitPrice || 0,
      discountPrice: product.discountPrice || product.salePrice || 0,
      salePrice: product.salePrice || product.discountPrice || product.price || 0,
      currency: product.currency || '',

      // Inventory information
      quantity: product.quantity || 0,
      availability: product.availability || (product.quantity > 0 ? 'in stock' : 'out of stock'),
      status: product.isActive ? 'active' : 'inactive',
      condition: product.condition || 'new',

      // Image information
      images: images,
      imageLink: Array.isArray(product.images) ? (product.images[0] || '') : (product.images || ''),
      additionalImageLink: Array.isArray(product.images) ? (product.images.slice(1).join(',') || '') : '',

      // Category information
      categories: categories,
      googleProductCategory: '',
      fbProductCategory: '',

      // Product attributes
      color: product.color || '',
      size: product.size || '',
      gender: product.gender || '',
      ageGroup: product.ageGroup || 'adult',
      material: product.material || '',
      pattern: product.pattern || '',

      // Additional identifiers
      itemGroupId: product.originalSku || product.sku || '',
      gtin: product.gtin || product.sku || '',
      ean: product.ean || product.sku || '',
      hsCode: product.hsCode || '',
      countryOfOrigin: product.countryOfOrigin || '',

      // Shipping information
      shipping: product.shipping || '',
      shippingWeight: product.weight || '',

      // System information
      isActive: product.isActive ? 'true' : 'false',
      createdAt: product.createdAt ? new Date(product.createdAt).toISOString() : '',
      updatedAt: product.updatedAt ? new Date(product.updatedAt).toISOString() : '',
    };
  }

  /**
   * Map standardized export data object to XML
   * Simple field mapping - no complex processing needed
   */
  private mapProductToXml(product: ExportDataObject, universalExport: UniversalExport): string {
    let xml = '  <product>\n';

    universalExport.fieldMappings
      .sort((a, b) => a.sortOrder - b.sortOrder)
      .forEach(mapping => {
        // Direct field access from standardized data object
        let value = product[mapping.sourceField as keyof ExportDataObject];

        // Apply default value if needed
        if ((value === null || value === undefined || value === '') && mapping.defaultValue) {
          value = mapping.defaultValue;
        }

        // Apply format pattern if specified
        if (value && mapping.formatPattern) {
          value = this.applyFormatPattern(value, mapping.formatPattern);
        }

        // Handle image fields with limit
        if (mapping.fieldType === 'image' && typeof value === 'string') {
          const images = value.split(',').filter(Boolean).slice(0, universalExport.imageLimit);
          xml += `    <${mapping.targetField}>\n`;
          images.forEach((image, index) => {
            xml += `      <image${index + 1}>${this.escapeXml(image.trim())}</image${index + 1}>\n`;
          });
          xml += `    </${mapping.targetField}>\n`;
        } else {
          const escapedValue = this.escapeXml(String(value || ''));
          xml += `    <${mapping.targetField}>${escapedValue}</${mapping.targetField}>\n`;
        }
      });

    xml += '  </product>\n';
    return xml;
  }

  private applyFormatPattern(value: any, pattern: string): string {
    if (!pattern) return String(value);

    // Simple format patterns
    switch (pattern.toLowerCase()) {
      case 'uppercase':
        return String(value).toUpperCase();
      case 'lowercase':
        return String(value).toLowerCase();
      case 'currency':
        return `${value} BDT`;
      default:
        return String(value);
    }
  }

  private escapeXml(text: string): string {
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;');
  }
}
