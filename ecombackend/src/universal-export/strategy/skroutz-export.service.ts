import { Injectable } from '@nestjs/common';
import { ExportStrategy } from './export-strategy.interface';
import { UniversalExport } from '../../domain/entities/universal-export.entity';
import { UniversalExportProductItemRepository } from '../repositories/universal-export-product-item.repository';
import { Readable } from 'stream';
import * as ExcelJS from 'exceljs';
import { formatCategoriesForSkroutz } from '../utils/category-formatter';

@Injectable()
export class SkroutzExportService implements ExportStrategy {
  constructor(
    private productItemRepo: UniversalExportProductItemRepository,
  ) {}

  async export(universalExport: UniversalExport): Promise<Readable> {
    console.log('🏪 Starting Skroutz export for:', universalExport.name);

    // We'll process products in chunks, but first set up the workbook structure

    // Create workbook and worksheet
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('List Your Products Here');

    // Define Skroutz-specific columns based on the template (34 columns)
    const columns = [
      // Product related information (9 columns)
      { header: 'Style Key', key: 'styleKey', width: 20 },
      { header: 'Product Name', key: 'productName', width: 30 },
      { header: 'Brand', key: 'brand', width: 20 },
      { header: 'Category', key: 'category', width: 30 },
      { header: 'Material (1430) (Multiselect)', key: 'material', width: 25 },
      { header: 'Sleeve length (1432)', key: 'sleeveLength', width: 20 },
      { header: 'Neckline (1435)', key: 'neckline', width: 20 },
      { header: 'Style fit (1712)', key: 'styleFit', width: 20 },
      { header: 'Multipack (1400)', key: 'multipack', width: 15 },

      // Color related information (12 columns)
      { header: 'Color', key: 'color', width: 15 },
      { header: 'Product Description (de)', key: 'descriptionDe', width: 40 },
      { header: 'Product Description (en)', key: 'descriptionEn', width: 40 },
      { header: 'Image 1', key: 'image1', width: 50 },
      { header: 'Image 2', key: 'image2', width: 50 },
      { header: 'Image 3', key: 'image3', width: 50 },
      { header: 'Image 4', key: 'image4', width: 50 },
      { header: 'Image 5', key: 'image5', width: 50 },
      { header: 'Image 6', key: 'image6', width: 50 },
      { header: 'Pattern (1429)', key: 'pattern', width: 20 },
      { header: 'Contains non-textile parts of animal origin (1469)', key: 'animalOrigin', width: 35 },
      { header: 'Material Composition (1797) (Textile)', key: 'materialComposition', width: 30 },

      // Size related information (8 columns)
      { header: 'SKU', key: 'sku', width: 20 },
      { header: 'EAN', key: 'ean', width: 15 },
      { header: 'HS Code', key: 'hsCode', width: 15 },
      { header: 'Country Of Origin', key: 'countryOfOrigin', width: 20 },
      { header: 'Weight (in grams)', key: 'weight', width: 18 },
      { header: 'Size', key: 'size', width: 10 },
      { header: 'Stock', key: 'stock', width: 10 },
      { header: 'Type of size (1345)', key: 'sizeType', width: 20 },

      // Price related information (3 columns)
      { header: 'Country', key: 'country', width: 15 },
      { header: 'Sale Price (Including VAT)', key: 'salePrice', width: 25 },
      { header: 'Retail Price (Including VAT)', key: 'retailPrice', width: 25 },

      // Errors (2 columns)
      { header: 'Has Error', key: 'hasError', width: 12 },
      { header: 'General Error', key: 'generalError', width: 30 },
    ];

    worksheet.columns = columns;

    // Style the header row
    worksheet.getRow(1).font = { bold: true };
    worksheet.getRow(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE6E6FA' }
    };

    // Process products in chunks to handle large datasets efficiently
    const batchSize = 100;
    let offset = 0;
    let hasMore = true;
    let totalProcessed = 0;

    while (hasMore) {
      const exportProducts = await this.productItemRepo.getEnhancedExportProducts(
        universalExport,
        batchSize,
        offset,
      );

      if (exportProducts.length > 0) {
        console.log(`🏪 Processing Skroutz chunk: ${exportProducts.length} products (offset: ${offset})`);

        // Add data rows from this chunk
        exportProducts.forEach((product, index) => {
      console.log('🔍 Processing product:', product.name, 'SKU:', product.sku);

      // Parse images array safely
      const images = Array.isArray(product.images) ? product.images :
                    (product.images ? String(product.images).split(',').map((img: string) => img.trim()) : []);

      // Format categories for Skroutz
      let categoryStr = Array.isArray(product.categories) ? product.categories.join('|') : product.categories;
        categoryStr = categoryStr.split(',')
            .map((cat: string) => cat.trim())
            .join('|')
            .split('>')
            .map((cat: string) => cat.trim())
            .join('|');

      const row = worksheet.addRow({
        // Product related information
        styleKey: product.originalSku || product.sku || `STYLE_${product.id}`,
        productName: product.name || 'N/A',
        brand: product.brand || 'N/A',
        category: categoryStr || '',
        material: '', // Default material
        sleeveLength: 'N/A',
        neckline: 'N/A',
        styleFit: '',
        multipack: '1-pack',

        // Color related information
        color: product.color || 'N/A',
        descriptionDe: product.description || 'N/A',
        descriptionEn: product.description || 'N/A',
        image1: images[0] || '',
        image2: images[1] || '',
        image3: images[2] || '',
        image4: images[3] || '',
        image5: images[4] || '',
        image6: images[5] || '',
        pattern: product.pattern || 'N/A',
        animalOrigin: 'No',
        materialComposition: product.material || 'Cotton=100',

        // Size related information
        sku: product.sku || '',
        ean: product.ean || product.sku || '',
        hsCode: product.hsCode || '',
        countryOfOrigin: product.countryOfOrigin || '',
        weight: product.weight || '',
        size: product.size || '',
        stock: product.quantity || 0,
        sizeType: 'Normal sizes',

        // Price related information
        country: product.countryOfOrigin || '',
        salePrice: product.salePrice || product.unitPrice || product.price || 0,
        retailPrice: product.unitPrice || product.price || 0,

        // Errors
        hasError: 'No',
        generalError: ''
      });

          // Alternate row colors for better readability
          if ((totalProcessed + index) % 2 === 1) {
            row.fill = {
              type: 'pattern',
              pattern: 'solid',
              fgColor: { argb: 'FFF8F8FF' }
            };
          }
        });

        totalProcessed += exportProducts.length;
        offset += batchSize;
      } else {
        hasMore = false;
      }
    }

    console.log(`✅ Skroutz Export completed. Total products: ${totalProcessed}`);

    // Auto-fit columns
    worksheet.columns.forEach(column => {
      if (column.width && column.width < 15) {
        column.width = 15;
      }
    });

    // Add borders to all cells
    worksheet.eachRow((row, rowNumber) => {
      row.eachCell((cell) => {
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        };
      });
    });

    // Generate buffer and convert to readable stream
    const buffer = await workbook.xlsx.writeBuffer();
    const stream = new Readable();
    stream.push(buffer);
    stream.push(null); // End the stream
    return stream;
  }

  getContentType(): string {
    return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
  }

  getFileExtension(): string {
    return 'xlsx';
  }
}
