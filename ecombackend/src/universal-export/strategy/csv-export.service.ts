import { Injectable } from '@nestjs/common';
import { Readable, PassThrough } from 'stream';
import * as fastcsv from 'fast-csv';
import { UniversalExport } from '../../domain/entities/universal-export.entity';
import { ExportStrategy } from './export-strategy.interface';
import { UniversalExportProductItemRepository } from '../repositories/universal-export-product-item.repository';
import { ExportDataObject } from '../interfaces/export-data.interface';

@Injectable()
export class CsvExportService implements ExportStrategy {
  constructor(
    private productItemRepo: UniversalExportProductItemRepository,
  ) {}

  async export(universalExport: UniversalExport): Promise<Readable> {
    try {
      console.log('🔍 CSV Export Debug - Starting export for:', universalExport.name);

      const stream = new PassThrough();

      // Get headers based on field mappings
      const headers = universalExport.fieldMappings
        .sort((a, b) => a.sortOrder - b.sortOrder)
        .map(mapping => mapping.targetField);

      console.log('📋 Headers:', headers);

      // Configure the CSV stream with the specified delimiter
      const csvStream = fastcsv.format({
        headers: headers,
        delimiter: universalExport.delimiter || ';',
      });
      csvStream.pipe(stream);

      // Process products in chunks to handle large datasets efficiently
      const batchSize = 50;
      let offset = 0;
      let hasMore = true;
      let totalProcessed = 0;

      while (hasMore) {
        const exportProducts = await this.productItemRepo.getEnhancedExportProducts(
          universalExport,
          batchSize,
          offset,
        );

        if (exportProducts.length > 0) {
          console.log(`📊 Processing chunk: ${exportProducts.length} products (offset: ${offset})`);

          // Process each product in the chunk
          exportProducts.forEach((product) => {
            const transformedProduct = this.transformToExportDataObject(product);
            const row = this.mapProductToRow(transformedProduct, universalExport);
            csvStream.write(row);
          });

          totalProcessed += exportProducts.length;
          offset += batchSize;
        } else {
          hasMore = false;
        }
      }

      console.log(`✅ CSV Export completed. Total products: ${totalProcessed}`);
      csvStream.end();
      return stream;
    } catch (error) {
      console.error('❌ CSV Export Error:', error);
      throw error;
    }
  }

  /**
   * Transform enhanced product data to ExportDataObject format
   */
  private transformToExportDataObject(product: any): ExportDataObject {
    const images = Array.isArray(product.images) ? product.images.join(',') : (product.images || '');
    const categories = Array.isArray(product.categories) ? product.categories.join(',') : (product.rawCategories || product.categories || '');

    return {
      // Core product information
      id: product.id,
      sku: product.sku || '',
      name: product.name || '',
      title: product.name || '',
      description: product.description || '',
      link: product.link || '',

      // Brand information
      brand: product.brand || '',

      // Pricing information
      unitPrice: product.unitPrice || product.price || 0,
      price: product.price || product.unitPrice || 0,
      discountPrice: product.discountPrice || product.salePrice || 0,
      salePrice: product.salePrice || product.discountPrice || product.price || 0,
      currency: product.currency || '',

      // Inventory information
      quantity: product.quantity || 0,
      availability: product.availability || (product.quantity > 0 ? 'in stock' : 'out of stock'),
      status: product.isActive ? 'active' : 'inactive',
      condition: product.condition || 'new',

      // Image information
      images: images,
      imageLink: Array.isArray(product.images) ? (product.images[0] || '') : (product.images || ''),
      additionalImageLink: Array.isArray(product.images) ? (product.images.slice(1).join(',') || '') : '',

      // Category information
      categories: categories,
      googleProductCategory: '',
      fbProductCategory: '',

      // Product attributes
      color: product.color || '',
      size: product.size || '',
      gender: product.gender || '',
      ageGroup: product.ageGroup || 'adult',
      material: product.material || '',
      pattern: product.pattern || '',

      // Additional identifiers
      itemGroupId: product.originalSku || product.sku || '',
      gtin: product.gtin || product.sku || '',
      ean: product.ean || product.sku || '',
      hsCode: product.hsCode || '',
      countryOfOrigin: product.countryOfOrigin || '',

      // Shipping information
      shipping: product.shipping || '',
      shippingWeight: product.weight || '',

      // System information
      isActive: product.isActive ? 'true' : 'false',
      createdAt: product.createdAt ? new Date(product.createdAt).toISOString() : '',
      updatedAt: product.updatedAt ? new Date(product.updatedAt).toISOString() : '',
    };
  }

  /**
   * Map standardized export data object to CSV row
   * Simple field mapping - no complex processing needed
   */
  private mapProductToRow(product: ExportDataObject, universalExport: UniversalExport): any {
    const row = {};

    universalExport.fieldMappings
      .sort((a, b) => a.sortOrder - b.sortOrder)
      .forEach(mapping => {
        // Direct field access from standardized data object
        let value = product[mapping.sourceField as keyof ExportDataObject];

        // Apply default value if needed
        if ((value === null || value === undefined || value === '') && mapping.defaultValue) {
          value = mapping.defaultValue;
        }

        // Apply format pattern if specified
        if (value && mapping.formatPattern) {
          value = this.applyFormatPattern(value, mapping.formatPattern);
        }

        // Handle image fields with limit
        if (mapping.fieldType === 'image' && typeof value === 'string') {
          const images = value.split(',').filter(Boolean);
          value = images.slice(0, universalExport.imageLimit).join(',');
        }

        // Convert to string for CSV
        row[mapping.targetField] = value !== null && value !== undefined ? String(value) : '';
      });

    return row;
  }

  private applyFormatPattern(value: any, pattern: string): string {
    if (!pattern) return String(value);

    // Simple format patterns
    switch (pattern.toLowerCase()) {
      case 'uppercase':
        return String(value).toUpperCase();
      case 'lowercase':
        return String(value).toLowerCase();
      case 'currency':
        return `${value} BDT`;
      default:
        return String(value);
    }
  }
}
