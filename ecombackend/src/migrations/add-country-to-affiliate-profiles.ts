import { MigrationInterface, QueryRunner, TableColumn, TableForeignKey } from 'typeorm';

export class AddCountryToAffiliateProfiles1703000000000 implements MigrationInterface {
  name = 'AddCountryToAffiliateProfiles1703000000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add country_id column to affiliate_profiles table
    await queryRunner.addColumn(
      'affiliate_profiles',
      new TableColumn({
        name: 'country_id',
        type: 'int',
        isNullable: true,
      })
    );

    // Add foreign key constraint
    await queryRunner.createForeignKey(
      'affiliate_profiles',
      new TableForeignKey({
        columnNames: ['country_id'],
        referencedTableName: 'countries',
        referencedColumnNames: ['id'],
        onDelete: 'SET NULL',
        onUpdate: 'CASCADE',
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop foreign key constraint
    const table = await queryRunner.getTable('affiliate_profiles');
    const foreignKey = table?.foreignKeys.find(fk => fk.columnNames.indexOf('country_id') !== -1);
    if (foreignKey) {
      await queryRunner.dropForeignKey('affiliate_profiles', foreignKey);
    }

    // Drop country_id column
    await queryRunner.dropColumn('affiliate_profiles', 'country_id');
  }
}
