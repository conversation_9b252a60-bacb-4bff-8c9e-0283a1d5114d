import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddMinMaxPriceToProductMeta1733800000000 implements MigrationInterface {
  name = 'AddMinMaxPriceToProductMeta1733800000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE "product_meta"
      ADD COLUMN "min_price" numeric(10,2) DEFAULT 0,
      ADD COLUMN "max_price" numeric(10,2) DEFAULT 0,
      ADD COLUMN "discount_min_percentage" numeric(5,2) DEFAULT 0,
      ADD COLUMN "discount_max_percentage" numeric(5,2) DEFAULT 0
    `);

    // Create indexes for better sorting performance
    await queryRunner.query(`
      CREATE INDEX "IDX_product_meta_min_price" ON "product_meta" ("min_price")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_product_meta_max_price" ON "product_meta" ("max_price")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_product_meta_discount_min_percentage" ON "product_meta" ("discount_min_percentage")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_product_meta_discount_max_percentage" ON "product_meta" ("discount_max_percentage")
    `);

    // Update existing rows with calculated min/max prices and discount percentages
    await queryRunner.query(`
      UPDATE product_meta
      SET
        min_price = CASE
          WHEN variants IS NOT NULL AND jsonb_array_length(variants) > 0 THEN
            (SELECT MIN((variant_detail->>'discountPrice')::numeric)
             FROM jsonb_array_elements(variants) AS variant,
                  jsonb_array_elements(variant->'variantDetails') AS variant_detail
             WHERE (variant_detail->>'discountPrice')::numeric > 0)
          ELSE discount_price
        END,
        max_price = CASE
          WHEN variants IS NOT NULL AND jsonb_array_length(variants) > 0 THEN
            (SELECT MAX((variant_detail->>'discountPrice')::numeric)
             FROM jsonb_array_elements(variants) AS variant,
                  jsonb_array_elements(variant->'variantDetails') AS variant_detail
             WHERE (variant_detail->>'discountPrice')::numeric > 0)
          ELSE discount_price
        END,
        discount_min_percentage = CASE
          WHEN variants IS NOT NULL AND jsonb_array_length(variants) > 0 THEN
            (SELECT MIN(
              CASE
                WHEN (variant_detail->>'unitPrice')::numeric > 0
                     AND (variant_detail->>'discountPrice')::numeric > 0
                     AND (variant_detail->>'discountPrice')::numeric < (variant_detail->>'unitPrice')::numeric
                THEN (((variant_detail->>'unitPrice')::numeric - (variant_detail->>'discountPrice')::numeric) / (variant_detail->>'unitPrice')::numeric) * 100
                ELSE 0
              END
             )
             FROM jsonb_array_elements(variants) AS variant,
                  jsonb_array_elements(variant->'variantDetails') AS variant_detail
             WHERE (variant_detail->>'discountPrice')::numeric > 0)
          WHEN unit_price > 0 AND discount_price > 0 AND discount_price < unit_price
          THEN ((unit_price - discount_price) / unit_price) * 100
          ELSE 0
        END,
        discount_max_percentage = CASE
          WHEN variants IS NOT NULL AND jsonb_array_length(variants) > 0 THEN
            (SELECT MAX(
              CASE
                WHEN (variant_detail->>'unitPrice')::numeric > 0
                     AND (variant_detail->>'discountPrice')::numeric > 0
                     AND (variant_detail->>'discountPrice')::numeric < (variant_detail->>'unitPrice')::numeric
                THEN (((variant_detail->>'unitPrice')::numeric - (variant_detail->>'discountPrice')::numeric) / (variant_detail->>'unitPrice')::numeric) * 100
                ELSE 0
              END
             )
             FROM jsonb_array_elements(variants) AS variant,
                  jsonb_array_elements(variant->'variantDetails') AS variant_detail
             WHERE (variant_detail->>'discountPrice')::numeric > 0)
          WHEN unit_price > 0 AND discount_price > 0 AND discount_price < unit_price
          THEN ((unit_price - discount_price) / unit_price) * 100
          ELSE 0
        END
      WHERE is_active = true
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "IDX_product_meta_discount_max_percentage"`);
    await queryRunner.query(`DROP INDEX "IDX_product_meta_discount_min_percentage"`);
    await queryRunner.query(`DROP INDEX "IDX_product_meta_max_price"`);
    await queryRunner.query(`DROP INDEX "IDX_product_meta_min_price"`);
    await queryRunner.query(`ALTER TABLE "product_meta" DROP COLUMN "discount_max_percentage"`);
    await queryRunner.query(`ALTER TABLE "product_meta" DROP COLUMN "discount_min_percentage"`);
    await queryRunner.query(`ALTER TABLE "product_meta" DROP COLUMN "max_price"`);
    await queryRunner.query(`ALTER TABLE "product_meta" DROP COLUMN "min_price"`);
  }
}
