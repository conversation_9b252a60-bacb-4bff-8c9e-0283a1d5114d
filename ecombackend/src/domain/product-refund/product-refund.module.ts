import { MiddlewareConsumer, Module, RequestMethod } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtTokenVerifierMiddleware } from 'src/common/middlewares/jwt-token-verifier.middleware';
import { ProductRefundRepository } from './product-refund.repository';
import { ProductRefundService } from './product-refund.service';
import { ProductRefundController } from './product-refund.controller';
import { ProductRefund } from '../entities/product-refund.entity';
import { ProductRefundTypeRepository } from './product-refund-type.repository';
import { InvoiceService } from 'src/common/services/invoice.service';
import { MailService } from 'src/common/services/mail.service';
import { ExcelService } from 'src/common/services/excel.service';
import { HttpModule } from '@nestjs/axios';
import { ProductOrderService } from '../product-order/product-order.service';
import { ProductOrderRepository } from '../product-order/product-order.repository';
import { ProductService } from '../product/product.service';
import { ImageGalleryRepository } from '../image/image-gallery.repository';
import { PdfService } from 'src/node-pdf/pdf.service';
import { ExternalEnventoryService } from 'src/common/services/external/manage-inventory.service';
import { AddressRepository } from '../address/address.repository';
import { ProductOrderDetailsRepository } from '../product-order/product-order-details.repository';
import { ProductRepository } from '../product/product.repository';
import { ProductMetaRepository } from '../product/product-meta.repository';
import { AttributeValueRepository } from '../attribute-value/attribute-value.repository';
import { ProductTagsService } from '../product-tags/product-tags.service';
import { SeoMetaService } from '../seo-meta/seo-meta.service';
import { ProductLocalization } from '../entities/product.entity';
import { ProductRelationRepository } from '../product/product-relation.repository';
import { CacheModule } from '@nestjs/cache-manager';
import * as fsStore from 'cache-manager-fs-hash';
import { CountryRepository } from '../country/country.repository';
import { CategoryRepository } from '../category/category.repository';
import { ProductTagsRepository } from '../product-tags/product-tags.repository';
import { ProductTagsMetaRepository } from '../product-tags/product-tags-meta.repository';
import { SeoMetaRepository } from '../seo-meta/seo-meta.repository';
import { AffiliateIntegrationService } from '../affiliate/services/affiliate-integration.service';
import { AffiliateOrderIntegrationService } from '../affiliate/services/affiliate-order-integration.service';
import { AffiliateTrackingService } from '../affiliate/services/affiliate-tracking.service';
import { AffiliateService } from '../affiliate/services/affiliate.service';
import { AffiliateProfile } from '../affiliate/entities/affiliate-profile.entity';
import { AffiliateCommission } from '../affiliate/entities/affiliate-commission.entity';
import { Cart } from '../cart/entities/cart.entity';
import { ProductOrder } from '../entities/product-order.entity';
import { AffiliateDiscountCommissionService } from '../affiliate/services/affiliate-discount-commission.service';
import { AffiliateDiscount } from '../affiliate/entities/affiliate-discount.entity';
import { AffiliateClick } from '../affiliate/entities/affiliate-click.entity';
import { Discount } from '../entities/discount.entity';
import { User } from '../entities/user.entity';
import { AffiliateConversion } from '../affiliate/entities/affiliate-conversion.entity';
import { UTMTrackingService } from '../affiliate/services/utm-tracking.service';
import { ConversionTrackingService } from '../affiliate/services/conversion-tracking.service';
import { AffiliateDiscountUsage } from '../affiliate/entities/affiliate-discount-usage.entity';
import { DiscountUsage } from '../entities/discount-usage.entity';
import { AddressShippingRepository } from '../address-shipping/address-shipping.repository';
import { AddressBillingRepository } from '../address-billing/address-billing.repository';

// import { JwtTokenVerifierMiddleware } from '../../common/middlewares/jwt-token-verifier.middleware';
// import { TokenManagementModule } from '../../common/providers/token-management/token-management.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      ProductRefund, 
      ProductLocalization, 
      AffiliateProfile,
      AffiliateCommission,
      Cart,
      ProductOrder,
      AffiliateDiscount,
      AffiliateClick,
      Discount,
      User,
      AffiliateConversion,
      AffiliateDiscountUsage,
      DiscountUsage,
    ]),
    HttpModule,
    CacheModule.register({
      store: fsStore, // File system cache store
      options: {
        path: './cache', // Ensure this directory exists
        ttl: 3600000, // 1 hour
        maxsize: **********, // 1GB
        zip: true, // Compress cache
        preventfill: false, // Ensure cache is preloaded on start
      },
    }),
  ],
  providers: [
    ProductRefundService,
    ProductRefundRepository,
    ProductRefundTypeRepository,
    InvoiceService,
    MailService,
    ExcelService,
    ProductOrderService,
    ProductOrderRepository,
    ProductService,
    ImageGalleryRepository,
    PdfService,
    ExternalEnventoryService,
    // AddressRepository,
    ProductOrderDetailsRepository,
    ProductRepository,
    ProductMetaRepository,
    AttributeValueRepository,
    ProductTagsService,
    SeoMetaService,
    ProductRelationRepository,
    CountryRepository,
    CategoryRepository,
    ProductTagsRepository,
    ProductTagsMetaRepository,
    SeoMetaRepository,
    AffiliateIntegrationService,
    AffiliateOrderIntegrationService,
    AffiliateTrackingService,
    AffiliateService,
    AffiliateDiscountCommissionService,
    UTMTrackingService,
    ConversionTrackingService,
    AddressShippingRepository,
    AddressBillingRepository,
  ],
  controllers: [ProductRefundController],
  exports: [ProductRefundService],
})
export class ProductRefundModule {
  configure(consumer: MiddlewareConsumer) {
    // consumer.apply(JwtTokenVerifierMiddleware).forRoutes({ path: 'ProductRefund', method: RequestMethod.POST });
    // consumer
    //   .apply(JwtTokenVerifierMiddleware)
    //   .forRoutes({ path: 'productRefund', method: RequestMethod.POST });
    consumer
      .apply(JwtTokenVerifierMiddleware)
      .forRoutes({ path: 'productRefund/findAllUserDataByPagination', method: RequestMethod.GET });
    consumer
      .apply(JwtTokenVerifierMiddleware)
      .forRoutes({ path: 'productRefund/:id', method: RequestMethod.PUT });
    consumer.apply(JwtTokenVerifierMiddleware).forRoutes({
      path: 'productRefund/delete/:id',
      method: RequestMethod.DELETE,
    });
    consumer.apply(JwtTokenVerifierMiddleware).forRoutes({
      path: 'productRefund/restore/:id',
      method: RequestMethod.POST,
    });
    consumer.apply(JwtTokenVerifierMiddleware).forRoutes({
      path: 'productRefund/activeOrInactive',
      method: RequestMethod.POST,
    });
    consumer.apply(JwtTokenVerifierMiddleware).forRoutes({
      path: 'productRefund/report/productReturnList',
      method: RequestMethod.GET,
    });
    consumer.apply(JwtTokenVerifierMiddleware).forRoutes({
      path: 'productRefund/report/excelDownload/productReturnList',
      method: RequestMethod.GET,
    });
  }
}
