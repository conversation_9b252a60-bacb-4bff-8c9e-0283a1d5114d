import { <PERSON>du<PERSON> } from '@nestjs/common';
import { CartService } from './cart.service';
import { CartController } from './cart.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Cart } from './entities/cart.entity';
import { CartItem } from './entities/cart.-item.entity';
import { CartOtp } from './entities/cart-otp.entity';
import { CartRepository } from './cart.repository';
import { CartItemRepository } from './cart-item.repository';
import { CartOtpRepository } from './cart-otp.repository';
import { ProductMetaRepository } from '../product/product-meta.repository';
import { ProductOrderRepository } from '../product-order/product-order.repository';
import { MailerService } from '@nestjs-modules/mailer';
import { MailService } from 'src/common/services/mail.service';
import { ProductOrderDetailsRepository } from '../product-order/product-order-details.repository';
import { CountryRepository } from '../country/country.repository';
import { ImageGalleryRepository } from '../image/image-gallery.repository';
import { InvoiceService } from 'src/common/services/invoice.service';
import { ExcelService } from 'src/common/services/excel.service';
import { PdfService } from 'src/node-pdf/pdf.service';
import { ProductOrderService } from '../product-order/product-order.service';
import { ProductOrder } from '../entities/product-order.entity';
import { ProductOrderModule } from '../product-order/product-order.module';
import { ProductService } from '../product/product.service';
import { HttpModule, HttpService } from '@nestjs/axios';
import { ProductRepository } from '../product/product.repository';
import { AttributeValueRepository } from '../attribute-value/attribute-value.repository';
import { ProductTagsService } from '../product-tags/product-tags.service';
import { SeoMetaService } from '../seo-meta/seo-meta.service';
import { ProductModule } from '../product/product.module';
import { Product, ProductLocalization } from '../entities/product.entity';
import { ProductRelationRepository } from '../product/product-relation.repository';
import { CategoryRepository } from '../category/category.repository';
import { ProductTagsRepository } from '../product-tags/product-tags.repository';
import { ProductTagsMetaRepository } from '../product-tags/product-tags-meta.repository';
import { SeoMetaRepository } from '../seo-meta/seo-meta.repository';
import { CacheModule } from '@nestjs/cache-manager';
import * as fsStore from 'cache-manager-fs-hash';
import { ExternalEnventoryService } from 'src/common/services/external/manage-inventory.service';
import { UserRepository } from '../user/user.repository';
import { Discount } from '../entities/discount.entity';
import { DiscountsService } from 'src/discounts/discounts.service';
import { Country } from '../entities/country.entity';
import { DiscountCountry } from '../entities/discount-country.entity';
import { DiscountUsage } from '../entities/discount-usage.entity';
import {
  FortunateWheel,
  FortunateWheelUsage,
} from '../entities/fortunate-wheel.entity';
import { SmsService } from '../../common/services/sms.service';
import { AffiliateModule } from '../affiliate/affiliate.module';
import { AddressShippingRepository } from '../address-shipping/address-shipping.repository';
import { AddressBillingRepository } from '../address-billing/address-billing.repository';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Cart,
      CartItem,
      Discount,
      DiscountCountry,
      DiscountUsage,
      FortunateWheel,
      FortunateWheelUsage,
      ProductLocalization,
      ProductRelationRepository,
      Product,
      Country,
    ]),
    HttpModule,
    AffiliateModule,
    CacheModule.register({
      store: fsStore, // File system cache store
      options: {
        path: './cache', // Ensure this directory exists
        ttl: 3600000, // 1 hour
        maxsize: **********, // 1GB
        zip: true, // Compress cache
        preventfill: false, // Ensure cache is preloaded on start
      },
    }),
  ],
  controllers: [CartController],
  providers: [
    CartService,
    CartRepository,
    CartItemRepository,
    CartOtpRepository,
    SmsService,
    ProductMetaRepository,
    ProductOrderService,
    ProductOrderRepository,
    ProductOrderDetailsRepository,
    // AddressOrderRepository,
    // AddressRepository,
    MailService,
    CountryRepository,
    ImageGalleryRepository,
    InvoiceService,
    ExcelService,
    PdfService,
    ProductService,
    ProductRepository,
    AttributeValueRepository,
    ProductTagsService,
    SeoMetaService,
    CategoryRepository,
    ProductTagsRepository,
    ProductTagsMetaRepository,
    SeoMetaRepository,
    // ProductOrderDetailsRepository,
    ExternalEnventoryService,
    UserRepository,
    DiscountsService,
    AddressShippingRepository,
    AddressBillingRepository,
  ],
  exports: [CartService, CartRepository, CartItemRepository, CartOtpRepository, SmsService],
})
export class CartModule { }
