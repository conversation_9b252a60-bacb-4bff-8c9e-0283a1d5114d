import { <PERSON><PERSON><PERSON>, <PERSON>NotE<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { IsCustomEmail } from 'src/common/decorators/email-format.decorator';

export class LoginDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  // @IsCustomEmail()
  email: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  password: string;

  @ApiProperty()
  @IsDefined()
  @IsNumber()
  userTypeId: number;
}
