import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Inject,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import { Product } from 'src/domain/entities/product.entity';
import { CategoryProductRelated } from './entity/category-product-related.entity';
import { ProductSortingEnum } from 'src/common/enums/product-sorting';
import { PageOptionsDto } from 'src/common/pagination/page-options.dto';
import { ServiceResponse } from 'src/common/utils/service-response';
import { CategoryProductRelatedRepository } from './category-product-related.repository';
import { ProductListResponseDto } from '../product/productResponseDto/product-list-response.dto';
import { ImageGallery } from '../entities/image-gallery.entity';
import { sortPriceFromList } from 'src/common/utils/common-functions';
import { ProductRelationRepository } from '../product/product-relation.repository';
import { CreateProductRelatedDto } from './dto/create-category-product-related.dto';
import { Category } from '../entities/category.entity';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';

@Injectable()
export class CategoryProductRelatedService {
  constructor(
    private readonly categoryProductRelatedRepository: CategoryProductRelatedRepository,
    private readonly productRelationRepository: ProductRelationRepository,
    @InjectRepository(Product) private readonly productRepository: Repository<Product>,
    @InjectRepository(Category) private readonly categoryRepository: Repository<Category>,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
  ) { }

  /**
   * Add related for a product
   */
  async addOrUpdateRelated(categoryId: number, body: CreateProductRelatedDto): Promise<any> {
    try {
      console.log('body:::', body);

      // Check if the main product exists
      const category = await this.categoryRepository.findOne({ where: { id: categoryId } });
      if (!category) {
        throw new NotFoundException(`Category with id ${categoryId} not found`);
      }

      // Extract product-related IDs from request body
      const categoryProductRelatedIds = body.categoryProductRelatedIds.map((item) => item.categoryProductRelated);

      // Fetch all suggested products
      const relatedProducts = await this.productRepository.findBy({ id: In(categoryProductRelatedIds) })  // .findByIds(categoryProductRelatedIds);

      if (relatedProducts.length !== categoryProductRelatedIds.length) {
        throw new BadRequestException('Some suggested products do not exist');
      }

      // Fetch existing related products for this product
      const existingRelateds: any = await this.categoryProductRelatedRepository.find({
        where: { category: { id: categoryId } },
        relations: ['categoryProductRelated']
      });

      // Map existing related products for quick lookup
      const existingRelatedMap = new Map(
        existingRelateds.map((rel) => [`${rel.categoryProductRelated.id}`, rel])
      );

      // Prepare entities for bulk insert or update
      const relatedEntities = body.categoryProductRelatedIds.map((item) => {
        if (existingRelatedMap.has(`${item.categoryProductRelated}`)) {
          // Update existing entry
          const existingRelation: any = existingRelatedMap.get(`${item.categoryProductRelated}`);
          return this.categoryProductRelatedRepository.merge(existingRelation, {
            sortOrder: item.sortOrder ?? existingRelation.sortOrder,
            updatedBy: body.createdBy
          });
        } else {
          // Create a new entry
          return this.categoryProductRelatedRepository.create({
            category: { id: categoryId },
            categoryProductRelated: { id: item.categoryProductRelated },
            sortOrder: item.sortOrder ?? 1,
            createdBy: body.createdBy
          });
        }
      });

      // Save all (both updates and new inserts)
      const savedData = await this.categoryProductRelatedRepository.save(relatedEntities);

      if (savedData) {
        return new ServiceResponse(
          savedData,
          'All product related saved successfully',
        );
      } else {
        return new ServiceResponse(
          null,
          'Product Related saved successfully',
          null,
        );
      }
    } catch (error) {
      console.log('error:::', error);
    }
  }

  /**
   * Get all related for a product
   */
  async getRelateds(productId: number): Promise<Product> {
    const product = await this.productRepository.findOne({
      where: { id: productId },
      // relations: ['related', 'related.categoryProductRelated'],
    });
    if (!product)
      throw new NotFoundException(`Product with id ${productId} not found`);

    return product;
  }


  /**
   * Update related for a product
   */
  async updateRelateds(
    categoryId: number,
    body: CreateProductRelatedDto,
  ): Promise<Product> {
    const product = await this.productRepository.findOne({
      where: { id: categoryId },
    });
    if (!product)
      throw new NotFoundException(`Product with id ${categoryId} not found`);

    if (!body?.categoryProductRelatedIds?.length) {
      await this.categoryProductRelatedRepository.delete({
        category: { id: categoryId },
      });

      return product;
    }

    // Fetch suggested products
    const categoryProductRelateds = await this.productRepository.find({
      where: body?.categoryProductRelatedIds.map((data) => ({ id: data?.categoryProductRelated })),
    });

    if (categoryProductRelateds.length !== body?.categoryProductRelatedIds.length) {
      throw new BadRequestException('Some suggested products do not exist');
    }

    // Remove existing related
    await this.categoryProductRelatedRepository.delete({
      category: { id: categoryId },
    });

    // Add new related
    const relatedToSave = categoryProductRelateds.map((categoryProductRelated) =>
      this.categoryProductRelatedRepository.create({ category: { id: categoryId }, categoryProductRelated }),
    );

    await this.categoryProductRelatedRepository.save(relatedToSave);

    // Return updated product with related
    return this.productRepository.findOne({
      where: { id: categoryId },
      // relations: ['related', 'related.categoryProductRelated'],
    });
  }

  /**
   * Remove a single suggestion
   */
  async removeRelated(
    productId: number,
    categoryProductRelatedId: number,
  ): Promise<ServiceResponse> {
    const categoryProductRelated = await this.categoryProductRelatedRepository.findOne({
      where: {
        category: productId,
        categoryProductRelated: categoryProductRelatedId,
      },
    });

    if (!categoryProductRelated) throw new NotFoundException('Related not found');

    const data = await this.categoryProductRelatedRepository.delete({
      category: productId,
      categoryProductRelated: categoryProductRelatedId
    });

    if (data) {
      return new ServiceResponse(
        data,
        'Product related deleted successfully',
      );
    } else {
      return new ServiceResponse(
        null,
        'Product Related deleted successfully',
        null,
      );
    }
  }

  async findByCategory(categoryId: number, name?: string): Promise<Product[]> {
    return this.categoryProductRelatedRepository.findByCategory(categoryId);
  }



  async getRelatedProductsById(
    countryId: number,
    categoryId: number,
    params: {
      languageId?: number;
      orderBy?: ProductSortingEnum;
      brandId?: number;
      size?: string;
      color?: string;
    },
    pageOptionsDto: PageOptionsDto,
  ): Promise<ServiceResponse> {
    let result = null;
    if (countryId) {
      const languageId = params.languageId || 1;

      // ✅ Include all filter parameters in cache key to avoid returning wrong cached results
      const filterParams = {
        brandId: params?.brandId,
        size: params?.size,
        color: params?.color,
        orderBy: params?.orderBy
      };
      const cacheKey = `category_wise_products_${categoryId}_${params?.languageId}_${JSON.stringify(pageOptionsDto)}_${JSON.stringify(filterParams)}`;

      // Try retrieving from cache only if no filters are applied
      // Skip cache for filtered requests to ensure accurate results
      const hasFilters = params?.brandId || params?.size || params?.color;
      const cachedProducts: ProductListResponseDto | any = hasFilters ? null : await this.cacheManager.get(cacheKey);

      if (cachedProducts) {
        // console.log('2. Returning from cache');
        return new ServiceResponse(
          cachedProducts?.productList,
          'All product found successfully.',
          cachedProducts.meta,
        );
      }

      result = await this.categoryProductRelatedRepository.getRelatedProductsById(
        countryId,
        categoryId,
        pageOptionsDto,
        languageId,
        params
      );

      if (result?.itemList && result?.itemList.length == 0) {
        return new ServiceResponse([], 'Product not found');
      }

      const productsId = result?.itemList.map((item) => item?.categoryProductRelated?.id);

      const relationdata = productsId
        ? await this.productRelationRepository.attachCategoriesToProducts(
          countryId,
          productsId,
          languageId,
        )
        : {};


      const productList: ProductListResponseDto[] = [];
      for (const item of result?.itemList) {
        const product = item.categoryProductRelated;
        // console.log('result product:::', product);
        const newProduct: ProductListResponseDto = new ProductListResponseDto();
        newProduct.id = product.id;
        newProduct.name = product?.localizations && product?.localizations[0]?.name || product.name;

        newProduct.countryId = countryId;

        newProduct.featuredImage = new ImageGallery();
        newProduct.hoverImage = new ImageGallery();

        newProduct.brand_name = relationdata[product.id]['brand'] || '';
        newProduct.categoriesArr = relationdata[product.id]['categories'] || '';
        newProduct.sku = relationdata[product.id]['sku'] || product?.parent_sku;
        newProduct.currency = relationdata[product.id]['currency'];

        newProduct.featuredImage.id = product.featuredImage.id;
        newProduct.featuredImage.name = product.featuredImage.name;
        newProduct.featuredImage.imageUrl = product.featuredImage.imageUrl;
        newProduct.featuredImage.originalImageUrl = product.featuredImage.originalImageUrl;
        newProduct.featuredImage.imageGalleryUrls = product.featuredImage.imageGalleryUrls;

        newProduct.hoverImage.id = product.hoverImage.id;
        newProduct.hoverImage.name = product.hoverImage.name;
        newProduct.hoverImage.imageUrl = product.hoverImage.imageUrl;
        newProduct.hoverImage.originalImageUrl = product.hoverImage.originalImageUrl;
        newProduct.hoverImage.imageGalleryUrls = product.hoverImage.imageGalleryUrls;

        newProduct.isMultiVariantProduct = product?.isMultiVariantProduct;
        newProduct.slug = product.slug;

        // Deprecated
        newProduct.categoryId = 0; //product.category_id;
        newProduct.categorySlug = '--'; //product.cat_slug;
        newProduct.catParentName = '--'; //product.cat_parent_name;
        // Deprecated end

        newProduct.limitedEdition = product.limitedEdition;
        newProduct.justArrived = product.justArrived;
        newProduct.mostPopular = product.mostPopular;
        newProduct.customerFavorite = product.customerFavorite;
        newProduct.bestValue = product.bestValue;
        newProduct.specialOffer = product.specialOffer;

        newProduct.quantity = product.quantity;
        if (!product?.isMultiVariantProduct) {
          // let productPrice: ProductPriceResponseDto =
          //   new ProductPriceResponseDto();
          newProduct.discountPrice = product?.productMeta?.discountPrice;
          newProduct.unitPrice = product?.productMeta?.unitPrice;
        } else {
          const price: number[] = sortPriceFromList(product?.productMeta?.variants);
          if (price.length > 0) {
            price[0]
              ? (newProduct.unitPrice = price[0])
              : (newProduct.unitPrice = 0);
          }
          if (price.length > 1) {
            price[1]
              ? (newProduct.discountPrice = price[1])
              : (newProduct.discountPrice = 0);
          }
        }
        productList.push(newProduct);
      }

      // Store result in cache only for unfiltered requests
      if (!hasFilters) {
        try {
          await this.cacheManager.set(cacheKey, { productList, meta: result.meta }, (3600000)); // Cache for 1 hour
        } catch (error) {
          console.log('cache set error:::', error);
        }
      }

      return new ServiceResponse(
        productList,
        'All product found successfully',
        result.meta,
      );
    } else {
      return new ServiceResponse(
        null,
        'Please provide country id and try again',
        null,
      );
    }
  }

  async searchProductSuggestion(categoryId: number, name?: string): Promise<Product[]> {
    return this.categoryProductRelatedRepository.searchProductSuggestion(categoryId, name);
  }

  async categoryWiseProducts(categoryId: number, countryId: number, languageId?: number): Promise<any> {
    return this.categoryProductRelatedRepository.categoryWiseProducts(categoryId, countryId, languageId);
  }
}
