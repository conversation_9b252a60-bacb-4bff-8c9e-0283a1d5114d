import { Injectable } from '@nestjs/common';
import { Repository, DataSource, IsNull, ILike, QueryRunner, Brackets } from 'typeorm';
import { PageOptionsDto } from 'src/common/pagination/page-options.dto';
import { PageDto } from 'src/common/pagination/page.dto';
import { PageMetaDto } from 'src/common/pagination/page-meta.dto';
import { CategoryProductRelated } from './entity/category-product-related.entity';
import { Product, ProductLocalization } from '../entities/product.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { ProductMeta } from '../entities/product-meta.entity';
import { ProductTagsMeta } from '../entities/product-tags-meta.entity';
import { Category } from '../entities/category.entity';
import { ProductSortingEnum } from 'src/common/enums/product-sorting';

@Injectable()
export class CategoryProductRelatedRepository extends Repository<CategoryProductRelated> {

  constructor(
    private dataSource: DataSource,
    @InjectRepository(Product) private readonly productRepository: Repository<Product>,
    @InjectRepository(ProductTagsMeta) private readonly productTagsMetaRepository: Repository<ProductTagsMeta>,
    @InjectRepository(Category) private readonly categoryRepository: Repository<Category>,
  ) {
    super(
      CategoryProductRelated,
      dataSource.createEntityManager(),
      dataSource.createQueryRunner(),
    );
  }

  // public async getRelatedProductsById(
  //   countryId: number,
  //   categoryId: number,
  //   pageOptionsDto: PageOptionsDto,
  //   languageId?: number,
  //   params?: any
  // ): Promise<PageDto<any>> {
  //   let brandId = null;
  //   let sizes = [];
  //   let colors = [];
  //   let orderByQuery = '';
  //   if (params?.brandId) {
  //     brandId = params?.brandId;
  //   }

  //   languageId = languageId || 1;

  //   if (!categoryId) {
  //     categoryId = null;
  //   }

  //   if (params?.size) {
  //     sizes = params?.size.split(',').map((item) => Number(item.trim()));
  //   }

  //   if (params?.color) {
  //     colors = params?.color.split(',').map((item) => Number(item.trim()));
  //   }

  //   // if(params?.orderBy== ProductSortingEnum.Best_Sellers){
  //   //   orderBy = `, `;
  //   // }else if(params?.orderBy== ProductSortingEnum.New_Arrivals){
  //   //   orderBy = orderBy + ProductSortingEnum.New_Arrivals;
  //   // }else
  //   if (params?.orderBy == ProductSortingEnum.Low_To_High) {
  //     orderByQuery = `ORDER BY pm.unit_price ASC`;
  //   } else if (params?.orderBy == ProductSortingEnum.High_To_Low) {
  //     orderByQuery = `ORDER BY pm.unit_price DESC`;
  //   } else {
  //     orderByQuery = `ORDER BY p.sort_order, p.id DESC`;
  //   }

  //   let queryRunner: QueryRunner = this.dataSource.createQueryRunner();
  //   await queryRunner.connect();
  //   await queryRunner.startTransaction(); // ✅ Begin transaction

  //   try {
  //     languageId = languageId || 1;

  //     const query = queryRunner.manager
  //       .createQueryBuilder('category_product_related', 'cpr')
  //       .innerJoinAndSelect('cpr.category', 'category')
  //       .innerJoinAndSelect('cpr.categoryProductRelated', 'relatedProduct')
  //       .innerJoinAndSelect('relatedProduct.featuredImage', 'featuredImage')
  //       .innerJoinAndSelect('relatedProduct.hoverImage', 'hoverImage')
  //       .leftJoinAndSelect(
  //         'relatedProduct.productMeta',
  //         'productMeta',
  //         'productMeta.countryId = :countryId AND productMeta.isActive = true AND productMeta.product_id = relatedProduct.id',
  //         { countryId }
  //       )
  //       .leftJoinAndSelect(
  //         'relatedProduct.localizations',
  //         'localizations',
  //         'localizations.language_id = :languageId',
  //         { languageId }
  //       )
  //       .where('category.id = :categoryId', { categoryId })
  //       .andWhere('productMeta.country_id = :countryId', { countryId })
  //       .andWhere('relatedProduct.is_publish = :isPublish', { isPublish: true })
  //       .andWhere('relatedProduct.deleted_at IS NULL')
  //       .andWhere('relatedProduct.isActive = :isActive', { isActive: true })
  //       .andWhere('productMeta.isActive = :metaIsActive', { metaIsActive: true })
  //       .andWhere('productMeta.deleted_at IS NULL');

  //     const dataWuthPagination = await query
  //       .skip(pageOptionsDto.skip)
  //       .take(pageOptionsDto.take)
  //       .orderBy('cpr.sortOrder', 'ASC')
  //       .getMany();

  //     const dataWuthCount = await query
  //       .orderBy('cpr.sortOrder', 'ASC')
  //       .getCount()

  //     const pageMetaDto = new PageMetaDto({ itemCount: dataWuthCount, pageOptionsDto });
  //     await queryRunner.commitTransaction(); // ✅ Commit transaction
  //     return new PageDto(dataWuthPagination, pageMetaDto);
  //   }
  //   catch (error) {
  //     console.log('getRelatedProductsById error', error);
  //     await queryRunner.rollbackTransaction();
  //     throw error;
  //   }
  //   finally {
  //     await queryRunner.release();
  //   }
  // }

  public async getRelatedProductsById(
    countryId: number,
    categoryId: number,
    pageOptionsDto: PageOptionsDto,
    languageId?: number,
    params?: any,
  ): Promise<PageDto<any>> {
    let brandId = null;
    let sizes: number[] = [];
    let colors: number[] = [];
    let orderByQuery = '';

    // ✅ Extract params
    if (params?.brandId) {
      brandId = Number(params.brandId);
    }

    if (params?.size) {
      sizes = params.size.split(',').map((item) => Number(item.trim()));
    }

    if (params?.color) {
      colors = params.color.split(',').map((item) => Number(item.trim()));
    }

    // ✅ Fallback
    languageId = languageId || 1;
    if (!categoryId) {
      categoryId = null;
    }

    // ✅ OrderBy SQL
    if (params?.orderBy == ProductSortingEnum.Low_To_High) {
      orderByQuery = `pm.unit_price ASC`;
    } else if (params?.orderBy == ProductSortingEnum.High_To_Low) {
      orderByQuery = `pm.unit_price DESC`;
    } else {
      orderByQuery = `cpr.sort_order ASC`; // default
    }

    let queryRunner: QueryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction(); // ✅ Begin transaction

    try {
      languageId = languageId || 1;

      const query = queryRunner.manager
        .createQueryBuilder('category_product_related', 'cpr')
        .innerJoinAndSelect('cpr.category', 'category')
        .innerJoinAndSelect('cpr.categoryProductRelated', 'relatedProduct')
        .innerJoinAndSelect('relatedProduct.featuredImage', 'featuredImage')
        .innerJoinAndSelect('relatedProduct.hoverImage', 'hoverImage')
        .leftJoinAndSelect(
          'relatedProduct.productMeta',
          'productMeta',
          'productMeta.countryId = :countryId AND productMeta.isActive = true AND productMeta.product_id = relatedProduct.id',
          { countryId },
        )
        .leftJoinAndSelect(
          'relatedProduct.localizations',
          'localizations',
          'localizations.language_id = :languageId',
          { languageId },
        )
        .where('category.id = :categoryId', { categoryId })
        .andWhere('productMeta.country_id = :countryId', { countryId })
        .andWhere('relatedProduct.is_publish = :isPublish', { isPublish: true })
        .andWhere('relatedProduct.deleted_at IS NULL')
        .andWhere('relatedProduct.isActive = :isActive', { isActive: true })
        .andWhere('productMeta.isActive = :metaIsActive', { metaIsActive: true })
        .andWhere('productMeta.deleted_at IS NULL');

      // ✅ Add brand filter if needed
      if (brandId) {
        query.andWhere('relatedProduct.brand_id = :brandId', { brandId });
      }

      // ✅ Determine filters
      const sizesArray = sizes.length ? sizes : null;
      const colorsArray = colors.length ? colors : null;

      // ✅ Apply size filter if provided
      if (sizesArray && sizesArray.length > 0) {
        query.andWhere(`
          relatedProduct.id IN (
            SELECT DISTINCT product_id
            FROM (
              SELECT product_id, (jsonb_array_elements(variants)->'size'->>'id')::int AS size_id
              FROM product_meta
              WHERE is_active = true AND country_id = :countryId
            ) AS size_filter
            WHERE size_id = ANY(:sizesArray::int[])
          )
        `, { countryId, sizesArray });
      }

      // ✅ Apply color filter if provided
      if (colorsArray && colorsArray.length > 0) {
        query.andWhere(`
          relatedProduct.id IN (
            SELECT DISTINCT product_id
            FROM (
              SELECT product_id, (jsonb_array_elements(details)->'color'->>'id')::int AS color_id
              FROM (
                SELECT DISTINCT product_id, jsonb_array_elements(variants)->'variantDetails' AS details
                FROM product_meta
                WHERE is_active = true AND country_id = :countryId
              ) AS sub
            ) AS color_filter
            WHERE color_id = ANY(:colorsArray::int[])
          )
        `, { countryId, colorsArray });
      }


      // ✅ Order by your dynamic query
      query.orderBy(orderByQuery);

      // ✅ Apply pagination
      const dataWithPagination = await query
        .skip(pageOptionsDto.skip)
        .take(pageOptionsDto.take)
        .orderBy('cpr.sortOrder', 'ASC')
        .cache(3600000) // 1h cache
        .getMany();

      const dataWithCount = await query.getCount();

      const pageMetaDto = new PageMetaDto({ itemCount: dataWithCount, pageOptionsDto });

      await queryRunner.commitTransaction();

      return new PageDto(dataWithPagination, pageMetaDto);
    } catch (error) {
      console.error('getRelatedProductsById error:', error);
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }


  async findByCategory(categoryId: number, name?: string): Promise<any[]> {
    let queryRunner: QueryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction(); // ✅ Begin transaction

    try {
      const data = await queryRunner.manager
        .createQueryBuilder('category_product_related', 'cpr')
        .innerJoinAndSelect('cpr.category', 'category')
        .innerJoinAndSelect('cpr.categoryProductRelated', 'relatedProduct')
        .innerJoinAndSelect('relatedProduct.featuredImage', 'featuredImage')
        .innerJoinAndSelect('relatedProduct.hoverImage', 'hoverImage')
        .select([
          'cpr.id',
          'category.id',
          'category.name',
          'relatedProduct.name',
          'relatedProduct.slug',
          'relatedProduct.id',
          'featuredImage',
        ])
        .where('category.id = :categoryId', { categoryId })
        .orderBy('cpr.sortOrder', 'ASC')
        .getMany();

      await queryRunner.commitTransaction(); // ✅ Commit transaction
      return data
    }
    catch (error) {
      console.log('error', error);
      await queryRunner.rollbackTransaction();
      throw error;
    }
    finally {
      console.log('release start');
      await queryRunner.release();
      console.log('release end');
    }
  }


  async searchProductSuggestion(categoryId: number, name: string): Promise<Product[]> {
    const escapedName = name?.replace(/'/g, "''");
    let query = '';
    if (name == 'ALL') {
      query = ` SELECT p.id, p.name as name, ig.image_url as image
      FROM product p
      LEFT JOIN image_gallery ig on ig.id = p.featured_image_id 
      GROUP BY p.id, ig.id
      Limit 10`;
    } else {
      query = `
      SELECT p.id, p.name as name, ig.image_url as image
      FROM product p
      LEFT JOIN image_gallery ig on ig.id = p.featured_image_id 
      where p.name ILIKE '%${escapedName}%'
      GROUP BY p.id, ig.id
      `;
    }

    return this.productRepository.manager.query(query);
  }

  public async categoryWiseProducts(
    categoryId: number,
    countryId: number,
    languageId?: number
  ): Promise<any> {
    languageId = languageId || 1;

    const query = `WITH main_products AS (
          SELECT DISTINCT p.id
          FROM product p
          INNER JOIN product_meta pm ON pm.product_id = p.id
          WHERE p.is_publish = true 
          AND pm.deleted_at IS NULL 
          AND p.deleted_at IS NULL 
          AND p.is_active = true 
          AND pm.is_active = true
      ),
      tagged_products AS (
          SELECT DISTINCT ptm2.product_id
          FROM product_tags_meta ptm1
          INNER JOIN product_tags_meta ptm2 ON ptm1.product_tag_id = ptm2.product_tag_id
          WHERE ptm1.product_id IN (SELECT id FROM main_products) AND ptm2.deleted_at IS NULL 
          AND ptm2.is_active = true
      )
      SELECT p.id, COALESCE(localization.name, p.name) AS name,
    	igf.image_url as image
      FROM product p 
      JOIN image_gallery igf ON igf.id = p.featured_image_id 
      JOIN image_gallery igh ON igh.id = p.hover_image_id
      LEFT JOIN product_localization localization ON (localization.product_id = p.id AND localization.language_id = $2) 
      WHERE (p.id IN (SELECT id FROM main_products)
        OR p.id IN (SELECT product_id FROM tagged_products))

        AND EXISTS (
              SELECT 1
              FROM product_category pc
              WHERE pc.product_id = p.id
              AND (pc.category_id = $1 OR pc.category_id IN (
                  SELECT c2.id
                  FROM category c2
                  WHERE c2.parent_id = $1
              ))
          )
      ORDER BY p.sort_order, p.id desc
    `;

    // Create QueryRunner for transaction
    const queryRunner: QueryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Execute queries inside transaction
      const data = await this.productRepository.manager.query(query, [categoryId, languageId])

      // Commit transaction
      await queryRunner.commitTransaction();

      return data
    } catch (error) {
      console.log('error', error);
      await queryRunner.rollbackTransaction();
      throw error;
    }
    finally {
      await queryRunner.release();
    }
  }
}
