import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { UniversalExport } from './universal-export.entity';

export enum FieldType {
  STRING = 'string',
  NUMBER = 'number',
  BOOLEAN = 'boolean',
  DATE = 'date',
  IMAGE = 'image',
  ARRAY = 'array',
}

@Entity()
export class FieldMapping {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'universal_export_id' })
  universalExportId: number;

  @Column({ name: 'source_field' })
  sourceField: string;

  @Column({ name: 'target_field' })
  targetField: string;

  @Column({
    name: 'field_type',
    type: 'enum',
    enum: FieldType,
    default: FieldType.STRING,
  })
  fieldType: FieldType;

  @Column({ name: 'is_required', default: false })
  isRequired: boolean;

  @Column({ name: 'default_value', nullable: true })
  defaultValue: string;

  @Column({ name: 'sort_order', default: 0 })
  sortOrder: number;

  @Column({ name: 'format_pattern', nullable: true })
  formatPattern: string;

  @Column({ nullable: true })
  description: string;

  @ManyToOne(() => UniversalExport, (universalExport) => universalExport.fieldMappings, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'universal_export_id' })
  universalExport: UniversalExport;
}
