import { <PERSON><PERSON><PERSON>, Column, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>um<PERSON> } from 'typeorm';
import { AbstractEntity } from './abstract.entity';
import { ISubscriptionPopup } from '../../common/interfaces/subscription-popup.interface';
import { Country } from './country.entity';
import { ImageGallery } from './image-gallery.entity';


@Entity()
export class SubscriptionPopup extends AbstractEntity implements ISubscriptionPopup {
  @Column({ name: 'name', nullable: true, default: null })
  title: string;

  @Column({ name: 'details', nullable: true, default: null })
  details: string;

  @Column({ name: 'image_gallery_id', nullable: true, default: null })
  imageGalleryId: number;

  @Column({ name: 'country_id', nullable: true, default: null })
  countryId: number;

  @ManyToOne(() => Country, { eager: true })
  @JoinColumn({ name: 'country_id', referencedColumnName: 'id' })
  country: Country;

  @ManyToOne(() => ImageGallery, { eager: true })
  @JoinC<PERSON>umn({ name: 'image_gallery_id', referencedColumnName: 'id' })
  imageGallery: ImageGallery;

  @Column({ name: 'type', nullable: true, default: null })
  type: string;

}
