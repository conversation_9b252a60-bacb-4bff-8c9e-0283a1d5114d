import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  ManyToMany,
  JoinTable,
  JoinColumn,
  ManyToOne,
  OneToMany,
} from 'typeorm';
import { Product } from './product.entity';
import { Country } from './country.entity';
import { FieldMapping } from './field-mapping.entity';

export enum ExportFormat {
  CSV = 'csv',
  XLSX = 'xlsx',
  XML = 'xml',
  SKROUTZ = 'skroutz',
}

@Entity()
export class UniversalExport {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @Column()
  slug: string;

  @Column({ name: 'country_id' })
  countryId: number;

  @Column({
    type: 'enum',
    enum: ExportFormat,
    default: ExportFormat.CSV,
  })
  format: ExportFormat;

  @Column({ default: ';' })
  delimiter: string;

  @Column({ name: 'image_limit', default: 1 })
  imageLimit: number;

  @Column({ name: 'include_variants', default: true })
  includeVariants: boolean;

  @Column({ name: 'include_inactive', default: false })
  includeInactive: boolean;

  @Column({ nullable: true })
  description: string;

  @Column({ name: 'created_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @Column({
    name: 'updated_at',
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt: Date;

  @Column({ name: 'created_by' })
  createdBy: number;

  @OneToMany(() => FieldMapping, (fieldMapping) => fieldMapping.universalExport, {
    cascade: true,
    eager: true,
  })
  fieldMappings: FieldMapping[];

  @ManyToMany(() => Product, (product) => product.universalExports)
  @JoinTable({
    name: 'universal_export_products',
    joinColumn: {
      name: 'universal_export_id',
      referencedColumnName: 'id',
    },
    inverseJoinColumn: {
      name: 'product_id',
      referencedColumnName: 'id',
    },
  })
  products: Product[];

  @ManyToOne(() => Country)
  @JoinColumn({ name: 'country_id', referencedColumnName: 'id' })
  country: Country;
}
