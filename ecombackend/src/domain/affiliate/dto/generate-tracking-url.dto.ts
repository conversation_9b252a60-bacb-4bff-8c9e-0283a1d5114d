import { ApiProperty } from '@nestjs/swagger';

export class GenerateTrackingUrlDto {
    @ApiProperty({ type: String, description: 'Base URL' })
    baseUrl: string;

    @ApiProperty({ type: String, description: 'Affiliate code' })
    affiliateCode: string;

    @ApiProperty({ type: String, description: 'UTM source', required: false })
    utmSource?: string;

    @ApiProperty({ type: String, description: 'UTM medium', required: false })
    utmMedium?: string;

    @ApiProperty({ type: String, description: 'UTM campaign', required: false })
    utmCampaign?: string;

    @ApiProperty({ type: String, description: 'UTM content', required: false })
    utmContent?: string;

    @ApiProperty({ type: String, description: 'UTM term', required: false })
    utmTerm?: string;

    @ApiProperty({ type: Object, description: 'Additional parameters', required: false })
    additionalParams?: Record<string, string>;
}