import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsEnum, IsNumber } from 'class-validator';
import { ClickSource } from '../entities/affiliate-click.entity';

export class TrackClickDto {
  @ApiProperty({
    description: 'Affiliate tracking identifier (can be affiliate code or coupon code)',
    example: 'AFF123456 or SUMMER20',
  })
  @IsString()
  affiliateCode: string; // Can be either affiliate code or coupon code

  @ApiPropertyOptional({
    description: 'Specific coupon code if different from affiliate code',
    example: 'SUMMER20',
  })
  @IsString()
  @IsOptional()
  couponCode?: string;

  @ApiPropertyOptional({
    description: 'Session ID',
    example: 'sess_1234567890',
  })
  @IsString()
  @IsOptional()
  sessionId?: string;

  @ApiPropertyOptional({
    description: 'Landing page URL',
    example: '/products/summer-collection',
  })
  @IsString()
  @IsOptional()
  landingPage?: string;

  @ApiPropertyOptional({
    description: 'Referrer URL',
    example: 'https://instagram.com/johndoe',
  })
  @IsString()
  @IsOptional()
  referrerUrl?: string;

  @ApiProperty({
    description: 'Click source',
    enum: ClickSource,
    example: ClickSource.SOCIAL_MEDIA,
  })
  @IsEnum(ClickSource)
  source: ClickSource;

  @ApiPropertyOptional({
    description: 'Country ID',
    example: 1,
  })
  @IsNumber()
  @IsOptional()
  countryId?: number;

  @ApiPropertyOptional({
    description: 'UTM Source',
    example: 'instagram',
  })
  @IsString()
  @IsOptional()
  utmSource?: string;

  @ApiPropertyOptional({
    description: 'UTM Medium',
    example: 'social',
  })
  @IsString()
  @IsOptional()
  utmMedium?: string;

  @ApiPropertyOptional({
    description: 'UTM Campaign',
    example: 'summer_sale_2024',
  })
  @IsString()
  @IsOptional()
  utmCampaign?: string;

  @ApiPropertyOptional({
    description: 'UTM Content',
    example: 'story_post',
  })
  @IsString()
  @IsOptional()
  utmContent?: string;

  @ApiPropertyOptional({
    description: 'UTM Term',
    example: 'summer_fashion',
  })
  @IsString()
  @IsOptional()
  utmTerm?: string;
}
