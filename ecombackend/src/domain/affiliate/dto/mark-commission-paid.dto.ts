import { IsOptional, IsString } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';

export class MarkCommissionPaidDto {
  @ApiPropertyOptional({
    description: 'Optional notes for the payment',
    example: 'Paid via PayPal on 2024-01-20'
  })
  @IsOptional()
  @IsString()
  notes?: string;

  @ApiPropertyOptional({
    description: 'Transaction ID from payment processor',
    example: 'TXN_123456789'
  })
  @IsOptional()
  @IsString()
  transactionId?: string;
}
