import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsString, IsDateString } from 'class-validator';

export class AssignDiscountToAffiliateDto {
  @ApiProperty({
    description: 'Affiliate ID',
    example: 1,
  })
  @IsNumber()
  affiliateId: number;

  @ApiProperty({
    description: 'Discount ID to assign',
    example: 5,
  })
  @IsNumber()
  discountId: number;

  @ApiPropertyOptional({
    description: 'Maximum usage limit for this affiliate (null for unlimited)',
    example: 100,
  })
  @IsNumber()
  @IsOptional()
  maxUsage?: number;

  @ApiPropertyOptional({
    description: 'Start date for the assignment',
    example: '2024-01-01T00:00:00Z',
  })
  @IsDateString()
  @IsOptional()
  startDate?: string;

  @ApiPropertyOptional({
    description: 'End date for the assignment',
    example: '2024-12-31T23:59:59Z',
  })
  @IsDateString()
  @IsOptional()
  endDate?: string;

  @ApiPropertyOptional({
    description: 'Notes about this assignment',
    example: 'Special holiday promotion for top affiliate',
  })
  @IsString()
  @IsOptional()
  notes?: string;
}
