import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsEnum, IsNumber, IsEmail } from 'class-validator';
import { ConversionType } from '../entities/affiliate-conversion.entity';

export class TrackConversionDto {
  @ApiProperty({
    description: 'Affiliate tracking identifier (affiliate code or coupon code)',
    example: 'AFF123456 or SUMMER20',
  })
  @IsString()
  affiliateCode: string;

  @ApiPropertyOptional({
    description: 'Session ID to link with original click',
    example: 'sess_1234567890',
  })
  @IsString()
  @IsOptional()
  sessionId?: string;

  @ApiPropertyOptional({
    description: 'Order ID for purchase conversions',
    example: 12345,
  })
  @IsNumber()
  @IsOptional()
  orderId?: number;

  @ApiProperty({
    description: 'Type of conversion',
    enum: ConversionType,
    example: ConversionType.PURCHASE,
  })
  @IsEnum(ConversionType)
  type: ConversionType;

  @ApiProperty({
    description: 'Monetary value of the conversion',
    example: 99.99,
  })
  @IsNumber()
  conversionValue: number;

  @ApiPropertyOptional({
    description: 'Customer email address',
    example: '<EMAIL>',
  })
  @IsEmail()
  @IsOptional()
  customerEmail?: string;

  @ApiPropertyOptional({
    description: 'Customer phone number',
    example: '+1234567890',
  })
  @IsString()
  @IsOptional()
  customerPhone?: string;

  @ApiPropertyOptional({
    description: 'Page where conversion occurred',
    example: '/checkout/success',
  })
  @IsString()
  @IsOptional()
  conversionPage?: string;

  @ApiPropertyOptional({
    description: 'Coupon code used in the conversion',
    example: 'SUMMER20',
  })
  @IsString()
  @IsOptional()
  couponUsed?: string;

  @ApiPropertyOptional({
    description: 'Discount amount applied',
    example: 19.99,
  })
  @IsNumber()
  @IsOptional()
  discountApplied?: number;

  @ApiPropertyOptional({
    description: 'Commission earned from this conversion',
    example: 9.99,
  })
  @IsNumber()
  @IsOptional()
  commissionEarned?: number;
}
