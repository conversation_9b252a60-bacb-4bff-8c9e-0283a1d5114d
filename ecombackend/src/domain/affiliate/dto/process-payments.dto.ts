import { <PERSON><PERSON><PERSON>y, Is<PERSON>ptional, IsString, IsNumber, ArrayMinSize } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class ProcessPaymentsDto {
  @ApiProperty({
    description: 'Array of commission IDs to process for payment',
    example: [1, 2, 3],
    type: [Number]
  })
  @IsArray()
  @ArrayMinSize(1)
  @IsNumber({}, { each: true })
  commissionIds: number[];

  @ApiPropertyOptional({
    description: 'Optional notes for the payment batch',
    example: 'Monthly payment batch processed via PayPal'
  })
  @IsOptional()
  @IsString()
  notes?: string;

  @ApiPropertyOptional({
    description: 'Payment method used',
    example: 'PayPal'
  })
  @IsOptional()
  @IsString()
  paymentMethod?: string;
}
