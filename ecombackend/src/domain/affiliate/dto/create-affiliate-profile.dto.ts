import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsString, IsN<PERSON>ber, IsOptional, IsUrl, IsObject, Min, Max } from 'class-validator';
import { CommissionType } from '../entities/affiliate-profile.entity';

export class CreateAffiliateProfileDto {
  @ApiProperty({
    description: 'User ID who wants to become an affiliate',
    example: 123,
  })
  @IsNumber()
  userId: number;

  @ApiPropertyOptional({
    description: 'Unique affiliate code (auto-generated if not provided)',
    example: 'AFF123456',
  })
  @IsString()
  @IsOptional()
  affiliateCode?: string;

  @ApiPropertyOptional({
    description: 'Country ID for the affiliate (optional)',
    example: 1,
  })
  @IsOptional()
  @IsNumber()
  countryId?: number;

  @ApiProperty({
    description: 'Commission type',
    enum: CommissionType,
    example: CommissionType.PERCENTAGE,
  })
  @IsEnum(CommissionType)
  commissionType: CommissionType;

  @ApiProperty({
    description: 'Commission rate (percentage 0-100 or fixed amount)',
    example: 10.5,
  })
  @IsNumber()
  @Min(0)
  @Max(100)
  commissionRate: number;

  @ApiPropertyOptional({
    description: 'Payment method preference',
    example: 'bank_transfer',
  })
  @IsString()
  @IsOptional()
  paymentMethod?: string;

  @ApiPropertyOptional({
    description: 'Payment details (bank account, PayPal email, etc.)',
    example: {
      bankName: 'Chase Bank',
      accountNumber: '**********',
      routingNumber: '*********',
      accountHolderName: 'John Doe'
    },
  })
  @IsObject()
  @IsOptional()
  paymentDetails?: any;

  @ApiPropertyOptional({
    description: 'Affiliate bio/description',
    example: 'Fashion influencer with 50K followers on Instagram',
  })
  @IsString()
  @IsOptional()
  bio?: string;

  @ApiPropertyOptional({
    description: 'Website URL',
    example: 'https://johndoe.com',
  })
  // @IsUrl()
  @IsOptional()
  websiteUrl?: string;

  @ApiPropertyOptional({
    description: 'Social media profiles',
    example: {
      instagram: '@johndoe',
      facebook: 'john.doe.page',
      youtube: 'johndoe_channel'
    },
  })
  @IsObject()
  @IsOptional()
  socialMedia?: {
    instagram?: string;
    facebook?: string;
    twitter?: string;
    youtube?: string;
    tiktok?: string;
  };
}
