import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Min } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { AffiliateStatus, CommissionType } from '../entities/affiliate-profile.entity';

export class UpdateAffiliateDto {
  @ApiPropertyOptional({
    description: 'Unique affiliate code',
    example: 'AFF123456'
  })
  @IsOptional()
  @IsString()
  affiliateCode?: string;

  @ApiPropertyOptional({
    description: 'Country ID for the affiliate',
    example: 1
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  countryId?: number;

  @ApiPropertyOptional({
    description: 'Commission type',
    enum: CommissionType,
    example: CommissionType.PERCENTAGE
  })
  @IsOptional()
  @IsEnum(CommissionType)
  commissionType?: CommissionType;

  @ApiPropertyOptional({
    description: 'Commission rate',
    example: 10.5
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  commissionRate?: number;

  @ApiPropertyOptional({
    description: 'Payment method',
    example: 'PayPal'
  })
  @IsOptional()
  @IsString()
  paymentMethod?: string;

  @ApiPropertyOptional({
    description: 'Affiliate bio',
    example: 'Experienced affiliate marketer with focus on fashion'
  })
  @IsOptional()
  @IsString()
  bio?: string;

  @ApiPropertyOptional({
    description: 'Affiliate status',
    enum: AffiliateStatus,
    example: AffiliateStatus.ACTIVE
  })
  @IsOptional()
  @IsEnum(AffiliateStatus)
  status?: AffiliateStatus;
}
