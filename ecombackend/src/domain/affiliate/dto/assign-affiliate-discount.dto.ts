import { <PERSON>N<PERSON><PERSON>, IsOptional, IsString, IsEnum, IsDateString, Min } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { AffiliateDiscountStatus } from '../entities/affiliate-discount.entity';
import { CommissionType } from '../entities/affiliate-profile.entity';

export class AssignAffiliateDiscountDto {
  @ApiProperty({
    description: 'Affiliate ID to assign discount to',
    example: 1
  })
  @IsNumber()
  @Min(1)
  affiliateId: number;

  @ApiProperty({
    description: 'Discount ID to assign',
    example: 5
  })
  @IsNumber()
  @Min(1)
  discountId: number;

  // @ApiPropertyOptional({
  //   description: 'Status of the affiliate discount',
  //   enum: AffiliateDiscountStatus,
  //   example: AffiliateDiscountStatus.ACTIVE
  // })
  // @IsOptional()
  // @IsEnum(AffiliateDiscountStatus)
  // status?: AffiliateDiscountStatus;

  @ApiPropertyOptional({
    description: 'Maximum usage count for this discount (null for unlimited)',
    example: 100
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  maxUsage?: number;

  @ApiPropertyOptional({
    description: 'Start date for the discount assignment',
    example: '2024-01-01T00:00:00Z'
  })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiPropertyOptional({
    description: 'End date for the discount assignment',
    example: '2024-12-31T23:59:59Z'
  })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiPropertyOptional({
    description: 'Notes about this discount assignment',
    example: 'Special promotion for Q1 2024'
  })
  @IsOptional()
  @IsString()
  notes?: string;

  @ApiPropertyOptional({
    description: 'Commission type for this specific discount (overrides affiliate profile)',
    enum: CommissionType,
    example: CommissionType.PERCENTAGE
  })
  @IsOptional()
  @IsEnum(CommissionType)
  commissionType?: CommissionType;

  @ApiPropertyOptional({
    description: 'Commission rate for this specific discount (overrides affiliate profile)',
    example: 15.0
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  commissionRate?: number;
}

export class UpdateAffiliateDiscountDto {
  @ApiPropertyOptional({
    description: 'Status of the affiliate discount',
    enum: AffiliateDiscountStatus,
    example: AffiliateDiscountStatus.ACTIVE
  })
  @IsOptional()
  @IsEnum(AffiliateDiscountStatus)
  status?: AffiliateDiscountStatus;

  @ApiPropertyOptional({
    description: 'Maximum usage count for this discount (null for unlimited)',
    example: 100
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  maxUsage?: number;

  @ApiPropertyOptional({
    description: 'Start date for the discount assignment',
    example: '2024-01-01T00:00:00Z'
  })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiPropertyOptional({
    description: 'End date for the discount assignment',
    example: '2024-12-31T23:59:59Z'
  })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiPropertyOptional({
    description: 'Notes about this discount assignment',
    example: 'Updated promotion terms'
  })
  @IsOptional()
  @IsString()
  notes?: string;

  @ApiPropertyOptional({
    description: 'Commission type for this specific discount (overrides affiliate profile)',
    enum: CommissionType,
    example: CommissionType.PERCENTAGE
  })
  @IsOptional()
  @IsEnum(CommissionType)
  commissionType?: CommissionType;

  @ApiPropertyOptional({
    description: 'Commission rate for this specific discount (overrides affiliate profile)',
    example: 15.0
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  commissionRate?: number;
}
