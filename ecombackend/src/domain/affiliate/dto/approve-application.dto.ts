import { <PERSON><PERSON><PERSON><PERSON>, IsOptional, IsString, Min } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class ApproveApplicationDto {
  // @ApiProperty({
  //   description: 'Discount ID to assign to the affiliate',
  //   example: 1
  // })
  // @IsNumber()
  // @Min(1)
  // discountId: number;

  // @ApiProperty({
  //   description: 'Commission rate for the affiliate',
  //   example: 10.5
  // })
  
  @IsNumber()
  @Min(0)
  commissionRate: number;

  @ApiPropertyOptional({
    description: 'Optional notes for the approval',
    example: 'Approved based on good application and social media presence'
  })
  @IsOptional()
  @IsString()
  notes?: string;
}
