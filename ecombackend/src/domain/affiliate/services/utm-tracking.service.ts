import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, EntityManager } from 'typeorm';
import { AffiliateClick, ClickSource } from '../entities/affiliate-click.entity';
import { AffiliateConversion, ConversionType } from '../entities/affiliate-conversion.entity';
import { AffiliateProfile, AffiliateStatus } from '../entities/affiliate-profile.entity';
import { ProductOrder } from '../../entities/product-order.entity';
import { ServiceResponse } from 'src/common/utils/service-response';

export interface UTMParameters {
  utmSource?: string;
  utmMedium?: string;
  utmCampaign?: string;
  utmContent?: string;
  utmTerm?: string;
}

export interface ClickTrackingData {
  affiliateId: number;
  sessionId?: string;
  ipAddress?: string;
  userAgent?: string;
  referrerUrl?: string;
  landingPage?: string;
  source: ClickSource;
  countryId?: number;
  city?: string;
  deviceType?: string;
  browser?: string;
  os?: string;
  utm: UTMParameters;
}

export interface ConversionTrackingData {
  affiliateId: number;
  affiliateDiscountId?: number;
  clickId?: number;
  orderId?: number;
  type: ConversionType;
  conversionValue: number;
  customerEmail?: string;
  customerPhone?: string;
  sessionId?: string;
  ipAddress?: string;
  conversionPage?: string;
  couponUsed?: string;
  discountApplied?: number;
  commissionEarned?: number;
  conversionData?: any;
}

@Injectable()
export class UTMTrackingService {
  private readonly logger = new Logger(UTMTrackingService.name);

  constructor(
    @InjectRepository(AffiliateClick)
    private clickRepo: Repository<AffiliateClick>,

    @InjectRepository(AffiliateConversion)
    private conversionRepo: Repository<AffiliateConversion>,

    @InjectRepository(AffiliateProfile)
    private affiliateProfileRepo: Repository<AffiliateProfile>,
  ) { }

  /**
   * Track affiliate click with UTM parameters
   */
  async trackClick(data: ClickTrackingData): Promise<ServiceResponse> {
    try {
      const click = this.clickRepo.create({
        affiliateId: data.affiliateId,
        sessionId: data.sessionId,
        ipAddress: data.ipAddress,
        userAgent: data.userAgent,
        referrerUrl: data.referrerUrl,
        landingPage: data.landingPage,
        source: data.source,
        countryId: data.countryId,
        city: data.city,
        deviceType: data.deviceType,
        browser: data.browser,
        os: data.os,
        utmSource: data.utm.utmSource,
        utmMedium: data.utm.utmMedium,
        utmCampaign: data.utm.utmCampaign,
        utmContent: data.utm.utmContent,
        utmTerm: data.utm.utmTerm,
        converted: false,
      });

      const savedClick = await this.clickRepo.save(click);

      this.logger.log(`Click tracked for affiliate ${data.affiliateId}: ${savedClick.id}`);

      return new ServiceResponse(savedClick, 'Click tracked successfully');
    } catch (error) {
      this.logger.error(`Error tracking click: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Track conversion and link to original click
   */
  async trackConversion(data: ConversionTrackingData, em?: EntityManager): Promise<ServiceResponse> {
    console.log('trackConversion start data:::', data);

    const entityManager = em || this.conversionRepo.manager;

    try {
      // Find the original click if sessionId is provided
      let originalClick: AffiliateClick | null = null;

      if (data.sessionId) {
        originalClick = await entityManager.findOne(AffiliateClick, {
          where: {
            affiliateId: data.affiliateId,
            sessionId: data.sessionId,
            converted: false
          },
          order: { createdAt: 'DESC' }
        });
      }

      // Calculate time to conversion
      let timeToConversion: number | null = null;
      if (originalClick) {
        const clickTime = new Date(originalClick.createdAt).getTime();
        const conversionTime = new Date().getTime();
        timeToConversion = Math.round((conversionTime - clickTime) / (1000 * 60)); // Minutes
      }

      // Validate required data
      if (!data.affiliateId) {
        throw new Error('affiliateId is required for conversion tracking');
      }
      if (!data.conversionValue || data.conversionValue < 0) {
        throw new Error('conversionValue must be a positive number');
      }

      // Create conversion record
      const conversion = entityManager.create(AffiliateConversion, {
        affiliateId: data.affiliateId,
        affiliateDiscountId: data.affiliateDiscountId,
        clickId: originalClick?.id || data.clickId,
        orderId: data.orderId,
        type: data.type,
        conversionValue: data.conversionValue,
        customerEmail: data.customerEmail,
        customerPhone: data.customerPhone,
        sessionId: data.sessionId,
        ipAddress: data.ipAddress,
        conversionPage: data.conversionPage,
        timeToConversion,
        couponUsed: data.couponUsed,
        discountApplied: data.discountApplied,
        commissionEarned: data.commissionEarned || 0,
        conversionData: data.conversionData,
      });

      console.log('::::::::::::::::::::::::::::::::::::');
      
      this.logger.log(`📋 Conversion entity created:`, {
        affiliateId: conversion.affiliateId,
        affiliateDiscountId: conversion.affiliateDiscountId,
        orderId: conversion.orderId,
        type: conversion.type,
        conversionValue: conversion.conversionValue,
        commissionEarned: conversion.commissionEarned
      });

      this.logger.log(`💾 Saving conversion for affiliate ${data.affiliateId}...`);
      const savedConversion = await entityManager.save(AffiliateConversion, conversion);
      this.logger.log(`✅ Conversion saved with ID: ${savedConversion.id}`);

      // Mark the original click as converted
      if (originalClick) {
        this.logger.log(`🔄 Updating original click ${originalClick.id} as converted...`);
        await entityManager.update(AffiliateClick, originalClick.id, {
          converted: true,
          conversionDate: new Date(),
          orderId: data.orderId,
          commissionEarned: data.commissionEarned || 0,
        });
        this.logger.log(`✅ Original click ${originalClick.id} marked as converted`);
      } else {
        this.logger.log(`ℹ️ No original click found to update for conversion ${savedConversion.id}`);
      }

      this.logger.log(`🎉 Conversion tracking completed for affiliate ${data.affiliateId}: ${savedConversion.id}`);

      return new ServiceResponse(savedConversion, 'Conversion tracked successfully');
    } catch (error) {
      this.logger.error(`Error tracking conversion: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get UTM performance analytics
   */
  async getUTMAnalytics(affiliateId: number, dateFrom?: Date, dateTo?: Date): Promise<ServiceResponse> {
    try {
      const queryBuilder = this.clickRepo
        .createQueryBuilder('click')
        .leftJoin('click.affiliate', 'affiliate')
        .where('click.affiliateId = :affiliateId', { affiliateId });

      if (dateFrom) {
        queryBuilder.andWhere('click.createdAt >= :dateFrom', { dateFrom });
      }

      if (dateTo) {
        queryBuilder.andWhere('click.createdAt <= :dateTo', { dateTo });
      }

      // UTM Source Analytics
      const utmSourceStats = await queryBuilder
        .clone()
        .select('click.utmSource', 'source')
        .addSelect('COUNT(*)', 'clicks')
        .addSelect('SUM(CASE WHEN click.converted = true THEN 1 ELSE 0 END)', 'conversions')
        .addSelect('SUM(click.commissionEarned)', 'totalCommission')
        .groupBy('click.utmSource')
        .getRawMany();

      // UTM Medium Analytics
      const utmMediumStats = await queryBuilder
        .clone()
        .select('click.utmMedium', 'medium')
        .addSelect('COUNT(*)', 'clicks')
        .addSelect('SUM(CASE WHEN click.converted = true THEN 1 ELSE 0 END)', 'conversions')
        .addSelect('SUM(click.commissionEarned)', 'totalCommission')
        .groupBy('click.utmMedium')
        .getRawMany();

      // UTM Campaign Analytics
      const utmCampaignStats = await queryBuilder
        .clone()
        .select('click.utmCampaign', 'campaign')
        .addSelect('COUNT(*)', 'clicks')
        .addSelect('SUM(CASE WHEN click.converted = true THEN 1 ELSE 0 END)', 'conversions')
        .addSelect('SUM(click.commissionEarned)', 'totalCommission')
        .groupBy('click.utmCampaign')
        .getRawMany();

      const analytics = {
        utmSource: utmSourceStats.map(stat => ({
          source: stat.source || 'direct',
          clicks: parseInt(stat.clicks),
          conversions: parseInt(stat.conversions),
          conversionRate: stat.clicks > 0 ? ((stat.conversions / stat.clicks) * 100).toFixed(2) : '0.00',
          totalCommission: parseFloat(stat.totalCommission) || 0,
        })),
        utmMedium: utmMediumStats.map(stat => ({
          medium: stat.medium || 'direct',
          clicks: parseInt(stat.clicks),
          conversions: parseInt(stat.conversions),
          conversionRate: stat.clicks > 0 ? ((stat.conversions / stat.clicks) * 100).toFixed(2) : '0.00',
          totalCommission: parseFloat(stat.totalCommission) || 0,
        })),
        utmCampaign: utmCampaignStats.map(stat => ({
          campaign: stat.campaign || 'no-campaign',
          clicks: parseInt(stat.clicks),
          conversions: parseInt(stat.conversions),
          conversionRate: stat.clicks > 0 ? ((stat.conversions / stat.clicks) * 100).toFixed(2) : '0.00',
          totalCommission: parseFloat(stat.totalCommission) || 0,
        })),
      };

      return new ServiceResponse(analytics, 'UTM analytics retrieved successfully');
    } catch (error) {
      this.logger.error(`Error getting UTM analytics: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Parse UTM parameters from URL
   */
  parseUTMFromUrl(url: string): UTMParameters {
    try {
      const urlObj = new URL(url);
      const params = urlObj.searchParams;

      return {
        utmSource: params.get('utm_source') || undefined,
        utmMedium: params.get('utm_medium') || undefined,
        utmCampaign: params.get('utm_campaign') || undefined,
        utmContent: params.get('utm_content') || undefined,
        utmTerm: params.get('utm_term') || undefined,
      };
    } catch (error) {
      this.logger.warn(`Error parsing UTM from URL: ${url}`, error.message);
      return {};
    }
  }

  /**
   * Generate affiliate tracking URL with UTM parameters
   */
  async generateTrackingUrl(
    baseUrl: string,
    affiliateCode: string,
    utm: UTMParameters,
    additionalParams?: Record<string, string>
  ): Promise<ServiceResponse> {
    try {
      const affiliateProfile = await this.affiliateProfileRepo.findOne({
        where: {
          affiliateCode,
          status: AffiliateStatus.ACTIVE
        },
        relations: ['affiliateDiscounts', 'affiliateDiscounts.discount']
      });

      console.log(' generateAffiliateLink affiliate:::', affiliateProfile?.id);

      if (!affiliateProfile) {
        return new ServiceResponse(null, 'Affiliate not found');
      }

      const url = new URL(baseUrl);

      // Add affiliate code
      url.searchParams.set('ref', affiliateProfile?.affiliateCode);

      // Add UTM parameters
      if (utm.utmSource) url.searchParams.set('utm_source', utm.utmSource);
      if (utm.utmMedium) url.searchParams.set('utm_medium', utm.utmMedium);
      if (utm.utmCampaign) url.searchParams.set('utm_campaign', utm.utmCampaign);
      if (utm.utmContent) url.searchParams.set('utm_content', utm.utmContent);
      if (utm.utmTerm) url.searchParams.set('utm_term', utm.utmTerm);

      // Add additional parameters
      if (additionalParams) {
        Object.entries(additionalParams).forEach(([key, value]) => {
          url.searchParams.set(key, value);
        });
      }

      return new ServiceResponse({ trackingUrl: url.toString() }, 'Tracking URL generated successfully');
    } catch (error) {
      this.logger.error(`Error generating tracking URL: ${error.message}`, error.stack);
      return new ServiceResponse(null, 'Failed to generate tracking URL');
    }
  }
}
