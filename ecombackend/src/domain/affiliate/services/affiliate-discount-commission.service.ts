import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IsNull } from 'typeorm';
import { AffiliateProfile, AffiliateStatus, CommissionType } from '../entities/affiliate-profile.entity';
import { AffiliateDiscount, AffiliateDiscountStatus } from '../entities/affiliate-discount.entity';
import { AffiliateDiscountUsage } from '../entities/affiliate-discount-usage.entity';
import { AffiliateCommission, CommissionStatus, CommissionSource } from '../entities/affiliate-commission.entity';
import { Cart } from '../../cart/entities/cart.entity';
import { ProductOrder } from '../../entities/product-order.entity';
import { Discount, DiscountType, AmountType } from '../../entities/discount.entity';
import { DiscountUsage } from '../../entities/discount-usage.entity';
import { ServiceResponse } from 'src/common/utils/service-response';
import { Country } from 'src/domain/entities/country.entity';
import { Currency } from 'src/domain/entities/currency.entity';

export interface CommissionCalculationResult {
  success: boolean;
  commissionAmount: number;
  commissionRate: number;
  commissionType: CommissionType;
  orderAmount: number;
  orderSubtotal?: number;
  discountAmount: number;
  commissionableAmount: number;
  affiliateId: number;
  discountId: number;
  message?: string;
  country: Country;
  currency: Currency;
  uuid: string;
  invoiceNo: string;
}

export interface CartCommissionPreview {
  estimatedCommission: number;
  commissionRate: number;
  commissionType: CommissionType;
  cartSubtotal: number;
  cartTotal: number;
  discountAmount: number;
  commissionableAmount: number;
  affiliateId: number;
  couponCode: string;
}

@Injectable()
export class AffiliateDiscountCommissionService {
  private readonly logger = new Logger(AffiliateDiscountCommissionService.name);

  constructor(
    @InjectRepository(AffiliateProfile)
    private affiliateRepo: Repository<AffiliateProfile>,

    @InjectRepository(AffiliateDiscount)
    private affiliateDiscountRepo: Repository<AffiliateDiscount>,

    @InjectRepository(AffiliateDiscountUsage)
    private affiliateDiscountUsageRepo: Repository<AffiliateDiscountUsage>,

    @InjectRepository(AffiliateCommission)
    private commissionRepo: Repository<AffiliateCommission>,

    @InjectRepository(Cart)
    private cartRepo: Repository<Cart>,

    @InjectRepository(ProductOrder)
    private orderRepo: Repository<ProductOrder>,

    @InjectRepository(Discount)
    private discountRepo: Repository<Discount>,

    @InjectRepository(DiscountUsage)
    private discountUsageRepo: Repository<DiscountUsage>,
  ) { }

  /**
   * Calculate commission for order based on discount usage
   */
  async calculateOrderCommission(
    orderId: number,
    transactionalEM?: EntityManager
  ): Promise<CommissionCalculationResult> {
    const em = transactionalEM || this.orderRepo.manager;

    try {
      // Get order with discount information
      const order = await em.findOne(ProductOrder, {
        where: { id: orderId },
        relations: ['coupons', 'productOrderDetails', 'user']
      });

      if (!order) {
        return {
          success: false,
          commissionAmount: 0,
          commissionRate: 0,
          commissionType: CommissionType.PERCENTAGE,
          orderAmount: 0,
          discountAmount: 0,
          commissionableAmount: 0,
          affiliateId: 0,
          discountId: 0,
          message: 'Order not found',
          country: null,
          currency: null,
          uuid: null,
          invoiceNo: null
        };
      }

      // Check if order used a discount coupon
      if (!order.coupons) {
        return {
          success: false,
          commissionAmount: 0,
          commissionRate: 0,
          commissionType: CommissionType.PERCENTAGE,
          orderAmount: order.amount,
          discountAmount: 0,
          commissionableAmount: order.amount,
          affiliateId: 0,
          discountId: 0,
          message: 'No discount coupon used',
          country: null,
          currency: null,
          uuid: null,
          invoiceNo: null
        };
      }

      // Find affiliate associated with the discount through affiliate_discounts table
      const affiliateDiscount = await em.findOne(AffiliateDiscount, {
        where: {
          discountId: order.coupons.id,
          status: AffiliateDiscountStatus.ACTIVE,
          deletedAt: IsNull()
        },
        relations: ['affiliate', 'discount']
      });

      const affiliate = affiliateDiscount?.affiliate;

      if (!affiliate) {
        return {
          success: false,
          commissionAmount: 0,
          commissionRate: 0,
          commissionType: CommissionType.PERCENTAGE,
          orderAmount: order.amount,
          discountAmount: order.discount || 0,
          commissionableAmount: order.amount - (order.discount || 0),
          affiliateId: 0,
          discountId: order.coupons.id,
          message: 'No affiliate found for this discount',
          country: null,
          currency: null,
          uuid: null,
          invoiceNo: null
        };
      }

      // Check if commission already exists
      const existingCommission = await em.findOne(AffiliateCommission, {
        where: {
          orderId: order.id,
          affiliateId: affiliate.id
        }
      });

      if (existingCommission) {
        return {
          success: false,
          commissionAmount: existingCommission.commissionAmount,
          commissionRate: affiliate.commissionRate,
          commissionType: affiliate.commissionType,
          orderAmount: order.amount,
          discountAmount: order.discount || 0,
          commissionableAmount: order.amount - (order.discount || 0),
          affiliateId: affiliate.id,
          discountId: order.coupons.id,
          message: 'Commission already exists for this order',
          country: null,
          currency: null,
          uuid: null,
          invoiceNo: null
        };
      }

      // Calculate commission based on affiliate settings
      const orderAmount = order.amount; // Total order amount (includes shipping, tax, etc.)
      const discountAmount = order.discount || 0;

      // Calculate order subtotal (product value only) from order details
      const orderSubtotal = order.productOrderDetails?.reduce((sum, detail) => {
        const unitPrice = Number(detail.discountPrice || detail.unitPrice || 0);
        const quantity = Number(detail.quantity || 0);
        return sum + (unitPrice * quantity);
      }, 0) || 0;

      // Commission calculation strategy (as per requirement):
      // Commission should be calculated from cart.total & productDiscount.amount
      // For orders, use order.amount (which comes from cart.total)
      const commissionableAmount = orderAmount;

      let commissionAmount = 0;

      // Determine commission rate and type (AffiliateDiscount takes priority)
      const effectiveCommissionType = affiliateDiscount?.commissionType || affiliate?.commissionType;
      const effectiveCommissionRate = affiliateDiscount?.commissionRate || affiliate?.commissionRate;

      if (effectiveCommissionType === CommissionType.PERCENTAGE) {
        commissionAmount = (commissionableAmount * effectiveCommissionRate) / 100;
      } else {
        // Fixed commission type
        commissionAmount = effectiveCommissionRate;
      }

      // Round to 2 decimal places
      commissionAmount = Math.round(commissionAmount * 100) / 100;

      return {
        success: true,
        commissionAmount,
        commissionRate: effectiveCommissionRate,
        commissionType: effectiveCommissionType,
        orderAmount,
        orderSubtotal,
        discountAmount,
        commissionableAmount,
        affiliateId: affiliate.id,
        discountId: order.coupons.id,
        message: 'Commission calculated successfully',
        country: order?.country,
        currency: order?.currency,
        uuid: order?.uuid,
        invoiceNo: order?.invoiceNo
      };

    } catch (error) {
      this.logger.error(`Error calculating order commission: ${error.message}`, error.stack);
      return {
        success: false,
        commissionAmount: 0,
        commissionRate: 0,
        commissionType: CommissionType.PERCENTAGE,
        orderAmount: 0,
        discountAmount: 0,
        commissionableAmount: 0,
        affiliateId: 0,
        discountId: 0,
        message: `Error: ${error.message}`,
        country: null,
        currency: null,
        uuid: null,
        invoiceNo: null
      };
    }
  }

  /**
   * Preview commission for cart with discount applied
   */
  async previewCartCommission(
    cartId: number,
    couponCode: string
  ): Promise<CartCommissionPreview | null> {
    try {
      // Get cart details
      const cart = await this.cartRepo.findOne({
        where: { id: cartId },
        // relations: ['cart', 'cart.product']
        relations: ['cart', 'cart.productId', 'cart.discount'],
      });

      if (!cart) {
        this.logger.warn(`Cart not found: ${cartId}`);
        return null;
      }

      // Find discount by coupon code
      const discount = await this.discountRepo.findOne({
        where: { coupon: couponCode, is_active: true },
        relations: ['countries', 'products', 'buy_products', 'get_products'],
      });

      if (!discount) {
        this.logger.warn(`Discount not found for coupon: ${couponCode}`);
        return null;
      }

      // Find affiliate associated with this discount through affiliate_discounts table
      const affiliateDiscount = await this.affiliateDiscountRepo.findOne({
        where: {
          discountId: discount.id,
          status: AffiliateDiscountStatus.ACTIVE
        },
        relations: ['affiliate']
      });

      const affiliate = affiliateDiscount?.affiliate;

      if (!affiliate) {
        this.logger.warn(`No affiliate found for discount: ${discount.id}`);
        return null;
      }

      // Use the actual discount amount from the cart instead of recalculating
      // The cart already has the correct discount applied through the main discount service
      const discountAmount = Number(cart.discount || 0);

      // Calculate the original cart subtotal using original unit prices (before any discounts)
      const originalCartSubtotal = cart.cart?.reduce((sum, item) => {
        const originalPrice = Number(item.unitPrice || 0);
        const quantity = Number(item.quantity || 0);
        return sum + (originalPrice * quantity);
      }, 0) || 0;

      const cartSubtotalAfterDiscount = Number(cart.subtotal || 0);
      const cartTotal = Number(cart.total || 0);

      // Commission calculation strategy (as per requirement):
      // Commission should be calculated from cart.total & productDiscount.amount
      // Use cart.total as the base for commission calculation
      const commissionableAmount = cartTotal;

      let estimatedCommission = 0;

      // Determine commission rate and type (AffiliateDiscount takes priority)
      const effectiveCommissionType = affiliateDiscount?.commissionType || affiliate?.commissionType;
      const effectiveCommissionRate = affiliateDiscount?.commissionRate || affiliate?.commissionRate;

      if (effectiveCommissionType === CommissionType.PERCENTAGE) {
        estimatedCommission = (commissionableAmount * effectiveCommissionRate) / 100;
      } else {
        estimatedCommission = effectiveCommissionRate;
      }

      // Round to 2 decimal places
      estimatedCommission = Math.round(estimatedCommission * 100) / 100;

      return {
        estimatedCommission,
        commissionRate: effectiveCommissionRate,
        commissionType: effectiveCommissionType,
        cartSubtotal: cartSubtotalAfterDiscount,
        cartTotal,
        discountAmount,
        commissionableAmount,
        affiliateId: affiliate.id,
        couponCode
      };

    } catch (error) {
      this.logger.error(`Error previewing cart commission: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Create commission record and affiliate discount usage tracking after successful order
   */
  async createCommissionRecord(
    orderId: number,
    calculationResult: CommissionCalculationResult,
    transactionalEM?: EntityManager
  ): Promise<AffiliateCommission | null> {
    if (!calculationResult.success || calculationResult.commissionAmount <= 0) {
      return null;
    }

    const em = transactionalEM || this.commissionRepo.manager;

    try {
      this.logger.log(`Creating commission record for order ${orderId}, affiliate ${calculationResult.affiliateId}, discount ${calculationResult.discountId}`);

      // Get order details for tracking
      const order = await em.findOne(ProductOrder, {
        where: { id: orderId },
        relations: ['user', 'coupons']
      });

      if (!order) {
        this.logger.error(`❌ Order not found for commission creation: ${orderId}`);
        return null;
      }

      this.logger.log(`✅ Order found: ${order.id}, coupon: ${order.coupons?.coupon}, user: ${order.user?.email}`);

      // Find the affiliate discount relationship
      console.log('calculationResult.affiliateId:::', calculationResult.discountId);
      console.log('calculationResult.affiliateId:::', calculationResult.affiliateId);
      
      const affiliateDiscount = await em.findOne(AffiliateDiscount, {
        where: {
          discountId: calculationResult.discountId,
          affiliateId: calculationResult.affiliateId,
          status: AffiliateDiscountStatus.ACTIVE
        }
      });

      console.log('affiliateDiscount:::', affiliateDiscount);
      

      if (!affiliateDiscount) {
        this.logger.error(`❌ Affiliate discount relationship not found for affiliate: ${calculationResult.affiliateId}, discount: ${calculationResult.discountId}`);

        // Let's check if the relationship exists but with different status
        const anyAffiliateDiscount = await em.findOne(AffiliateDiscount, {
          where: {
            discountId: calculationResult.discountId,
            affiliateId: calculationResult.affiliateId
          }
        });

        if (anyAffiliateDiscount) {
          this.logger.error(`❌ Affiliate discount exists but status is: ${anyAffiliateDiscount.status} (expected: ${AffiliateDiscountStatus.ACTIVE})`);
        } else {
          this.logger.error(`❌ No affiliate discount relationship exists at all for affiliate: ${calculationResult.affiliateId}, discount: ${calculationResult.discountId}`);
        }

        return null;
      }

      this.logger.log(`✅ Affiliate discount relationship found: ${affiliateDiscount.id}, status: ${affiliateDiscount.status}`);

      // Find the discount usage record
      const discountUsage = await em.findOne(DiscountUsage, {
        where: {
          orderId: orderId,
          discountId: calculationResult.discountId
        }
      });

      if (discountUsage) {
        this.logger.log(`✅ Discount usage found: ${discountUsage.id}`);
      } else {
        this.logger.warn(`⚠️ No discount usage record found for order ${orderId}, discount ${calculationResult.discountId}`);
      }

      // Create commission record
      this.logger.log(`Creating commission record with data:`, {
        affiliateId: calculationResult.affiliateId,
        affiliateDiscountId: affiliateDiscount.id,
        orderId: orderId,
        discountId: calculationResult.discountId,
        commissionAmount: calculationResult.commissionAmount,
        orderAmount: calculationResult.orderAmount
      });

      const commission = em.create(AffiliateCommission, {
        affiliateId: calculationResult.affiliateId,
        affiliateDiscountId: affiliateDiscount.id,
        orderId: orderId,
        discountId: calculationResult.discountId,
        source: CommissionSource.ORDER,
        status: CommissionStatus.PENDING,
        orderAmount: calculationResult.orderAmount,
        discountAmount: calculationResult.discountAmount,
        commissionRate: calculationResult.commissionRate,
        commissionAmount: calculationResult.commissionAmount,
        currencyCode: calculationResult.currency?.currencySymbol,
        customerEmail: order.user?.email,
        customerPhone: order.user?.phone,
        couponCode: order.coupons?.coupon,
        country: calculationResult.country,
        uuid: calculationResult.uuid,
        invoiceNo: calculationResult.invoiceNo,
        commissionType: calculationResult.commissionType,
      });

      const savedCommission = await em.save(commission);
      this.logger.log(`✅ Commission record saved with ID: ${savedCommission.id}`);

      // Create affiliate discount usage tracking record
      this.logger.log(`Creating affiliate discount usage tracking record...`);
      let usageRecord = null;
      try {
        usageRecord = await this.createAffiliateDiscountUsage(
          affiliateDiscount,
          order,
          discountUsage,
          calculationResult,
          em
        );

        if (usageRecord) {
          this.logger.log(`✅ Affiliate discount usage record created with ID: ${usageRecord.id}`);
        } else {
          this.logger.error(`❌ Failed to create affiliate discount usage record - but continuing with commission creation`);
        }
      } catch (usageError) {
        this.logger.error(`❌ Error creating affiliate discount usage record:`, {
          error: usageError.message,
          stack: usageError.stack
        });
        // Don't throw error - continue with commission creation
      }

      // Update affiliate discount statistics
      this.logger.log(`Updating affiliate discount statistics for discount ID: ${affiliateDiscount.id}`);
      try {
        await this.updateAffiliateDiscountStatistics(affiliateDiscount.id, calculationResult, em);
        this.logger.log(`✅ Affiliate discount statistics updated successfully`);
      } catch (statsError) {
        this.logger.error(`❌ Error updating affiliate discount statistics:`, {
          error: statsError.message,
          stack: statsError.stack
        });
        // Don't throw error - commission creation is still successful
      }

      this.logger.log(`✅ Commission creation completed: commission ID ${savedCommission.id} for affiliate: ${calculationResult.affiliateId}`);

      return savedCommission;

    } catch (error) {
      this.logger.error(`Error creating commission record: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Create affiliate discount usage tracking record
   */
  private async createAffiliateDiscountUsage(
    affiliateDiscount: AffiliateDiscount,
    order: ProductOrder,
    discountUsage: DiscountUsage | null,
    calculationResult: CommissionCalculationResult,
    em: EntityManager
  ): Promise<AffiliateDiscountUsage | null> {
    try {
      this.logger.log(`Creating affiliate discount usage record:`, {
        affiliateDiscountId: affiliateDiscount.id,
        orderId: order.id,
        discountUsageId: discountUsage?.id || 'null (no discount usage found)',
        customerEmail: order.user?.email,
        commissionAmount: calculationResult.commissionAmount
      });

      const affiliateDiscountUsage = em.create(AffiliateDiscountUsage, {
        affiliateDiscountId: affiliateDiscount.id,
        discountUsageId: discountUsage?.id || null, // Explicitly set to null if no discount usage
        orderId: order.id,
        customerEmail: order.user?.email || '',
        customerPhone: order.user?.phone || null,
        orderAmount: Number(calculationResult.orderAmount),
        discountAmount: Number(calculationResult.discountAmount),
        commissionRate: Number(calculationResult.commissionRate),
        commissionAmount: Number(calculationResult.commissionAmount),
        sessionId: null, // Will be populated from tracking data if available
        ipAddress: null, // Will be populated from tracking data if available
        userAgent: null, // Will be populated from tracking data if available
        referrerUrl: null, // Will be populated from tracking data if available
        utmSource: null, // Will be populated from tracking data if available
        utmMedium: null, // Will be populated from tracking data if available
        utmCampaign: null, // Will be populated from tracking data if available
      });

      this.logger.log(`Saving affiliate discount usage with data:`, {
        affiliateDiscountId: affiliateDiscountUsage.affiliateDiscountId,
        discountUsageId: affiliateDiscountUsage.discountUsageId,
        orderId: affiliateDiscountUsage.orderId,
        orderAmount: affiliateDiscountUsage.orderAmount,
        commissionAmount: affiliateDiscountUsage.commissionAmount
      });

      const savedUsage = await em.save(affiliateDiscountUsage);

      this.logger.log(`✅ Affiliate discount usage created successfully: ${savedUsage.id} for affiliate discount: ${affiliateDiscount.id}`);

      return savedUsage;

    } catch (error) {
      this.logger.error(`❌ Error creating affiliate discount usage:`, {
        error: error.message,
        stack: error.stack,
        affiliateDiscountId: affiliateDiscount.id,
        orderId: order.id,
        discountUsageId: discountUsage?.id,
        calculationResult: {
          affiliateId: calculationResult.affiliateId,
          commissionAmount: calculationResult.commissionAmount,
          orderAmount: calculationResult.orderAmount
        }
      });
      return null;
    }
  }

  /**
   * Update affiliate discount statistics based on usage
   */
  private async updateAffiliateDiscountStatistics(
    affiliateDiscountId: number,
    calculationResult: CommissionCalculationResult,
    em: EntityManager
  ): Promise<void> {
    try {
      this.logger.log(`Updating statistics for affiliate discount ${affiliateDiscountId}`);

      // Get current affiliate discount data
      const affiliateDiscount = await em.findOne(AffiliateDiscount, {
        where: { id: affiliateDiscountId }
      });

      if (!affiliateDiscount) {
        this.logger.error(`❌ Affiliate discount not found for statistics update: ${affiliateDiscountId}`);
        return;
      }

      this.logger.log(`Current affiliate discount stats:`, {
        id: affiliateDiscount.id,
        currentUsageCount: affiliateDiscount.usageCount || 0,
        currentTotalCommissionEarned: affiliateDiscount.totalCommissionEarned || 0,
        newCommissionAmount: calculationResult.commissionAmount
      });

      // Calculate new statistics
      const currentUsageCount = Number(affiliateDiscount.usageCount || 0);
      const currentTotalCommissionEarned = Number(affiliateDiscount.totalCommissionEarned || 0);
      const newCommissionAmount = Number(calculationResult.commissionAmount);

      const newUsageCount = currentUsageCount + 1;
      const newTotalCommissionEarned = currentTotalCommissionEarned + newCommissionAmount;

      this.logger.log(`Calculated new statistics:`, {
        newUsageCount,
        newTotalCommissionEarned: newTotalCommissionEarned.toFixed(2)
      });

      // Update affiliate discount statistics
      const updateResult = await em.update(AffiliateDiscount, { id: affiliateDiscountId }, {
        usageCount: newUsageCount,
        totalCommissionEarned: Number(newTotalCommissionEarned.toFixed(2)),
        updatedAt: new Date()
      });

      this.logger.log(`✅ Affiliate discount statistics update result:`, {
        affiliateDiscountId,
        affected: updateResult.affected,
        newUsageCount,
        newTotalCommissionEarned: newTotalCommissionEarned.toFixed(2)
      });

      // Verify the update by fetching the record again
      const updatedDiscount = await em.findOne(AffiliateDiscount, {
        where: { id: affiliateDiscountId },
        select: ['id', 'usageCount', 'totalCommissionEarned']
      });

      if (updatedDiscount) {
        this.logger.log(`✅ Verification - Updated affiliate discount stats:`, {
          id: updatedDiscount.id,
          usageCount: updatedDiscount.usageCount,
          totalCommissionEarned: updatedDiscount.totalCommissionEarned
        });
      } else {
        this.logger.error(`❌ Could not verify affiliate discount update for ID: ${affiliateDiscountId}`);
      }

    } catch (error) {
      this.logger.error(`❌ Error updating affiliate discount statistics for ID ${affiliateDiscountId}:`, {
        error: error.message,
        stack: error.stack,
        calculationResult: {
          affiliateId: calculationResult.affiliateId,
          commissionAmount: calculationResult.commissionAmount,
          discountId: calculationResult.discountId
        }
      });
    }
  }

  /**
   * Calculate discount amount using the same logic as the main discount service
   */
  private async calculateDiscountAmount(cart: Cart, discount: Discount): Promise<number> {
    try {
      // Get the country-specific discount amount using the same logic as DiscountService
      console.log('calculateDiscountAmount discount:::', discount?.coupon);
      const countryDiscount = discount.countries?.find(
        (item) => item.countryId == cart.countryId,
      );

      console.log('countryDiscount:::', countryDiscount);

      if (!countryDiscount) {
        return 0;
      }

      const cartSubtotal = Number(cart.subtotal || 0);
      const cartTotal = Number(cart.total || 0);
      const cartQuantity = this.getCartTotalQty(cart);

      // Check minimum requirements
      const minAmount = Number(countryDiscount.min_amount || 0);
      const minQuantity = Number(countryDiscount.min_quantity || 0);

      if (minAmount > 0 && cartTotal < minAmount) {
        return 0;
      }

      if (minQuantity > 0 && cartQuantity < minQuantity) {
        return 0;
      }

      const discountAmountValue = Number(countryDiscount.amount || 0);
      console.log('discountAmountValue:::', discountAmountValue);
      console.log('discount.discount_type:::', discount.discount_type);

      let discountAmount = 0;

      // Handle different discount types based on the main discount service logic
      // switch (discount.discount_type) {
      //   case DiscountType.ORDER_DISCOUNT:
      //     discountAmount = this.calculatedDiscountAmount(
      //       cartTotal,
      //       discountAmountValue,
      //       discount,
      //     );
      //     break;

      //   case DiscountType.PRODUCT_DISCOUNT:
      //     // For product discounts, calculate based on applicable products
      //     discountAmount = await this.calculateProductDiscountAmount(
      //       cart,
      //       discount,
      //       discountAmountValue,
      //     );
      //     break;

      //   case DiscountType.BY_X_GET_Y:
      //     // For BXGY, calculate the value of free products
      //     discountAmount = await this.calculateBXGYDiscountAmount(
      //       cart,
      //       discount,
      //       discountAmountValue,
      //     );
      //     break;

      //   case DiscountType.SHIPPING_DISCOUNT:
      //     // For shipping discounts, use shipping charge
      //     const shippingCharge = Number(cart.shippingCharge || 0);
      //     discountAmount = this.calculatedDiscountAmount(
      //       shippingCharge,
      //       discountAmountValue,
      //       discount,
      //     );
      //     break;

      //   default:
      //     discountAmount = 0;
      // }

      discountAmount = this.calculatedDiscountAmount(
        cartTotal,
        discountAmountValue,
        discount,
      )

      console.log('discountAmount:::', discountAmount);

      // Ensure discount doesn't exceed cart total
      discountAmount = Math.min(discountAmount, cartTotal);

      // Round to 2 decimal places
      discountAmount = Math.round(discountAmount * 100) / 100;

      return discountAmount;

    } catch (error) {
      this.logger.error(`Error calculating discount amount: ${error.message}`, error.stack);
      return 0;
    }
  }

  /**
   * Calculate discount amount using the same logic as DiscountService
   */
  private calculatedDiscountAmount(
    amount: number,
    discountAmount: number,
    discount: Discount | any,
  ): number {

    console.log('amount:::', amount);
    console.log('discountAmount:::', discountAmount);
    console.log('calculatedDiscountAmount discount id:::', discount?.id);

    const _discountAmount = Number(discountAmount);

    if (discount.amount_type === AmountType.PERCENTAGE) {
      return amount * (_discountAmount / 100);
    }
    else if (discount.amount_type === AmountType.FIXED) {
      return _discountAmount;
    }

    return _discountAmount;
  }

  /**
   * Get cart total quantity
   */
  private getCartTotalQty(cart: Cart): number {
    return cart.cart?.reduce((total, item) => total + Number(item.quantity || 0), 0) || 0;
  }

  /**
   * Calculate product-specific discount amount
   */
  private async calculateProductDiscountAmount(
    cart: Cart,
    discount: Discount,
    discountAmountValue: number
  ): Promise<number> {
    const _amount = this.getCountryAmount(cart, discount);
    if (!_amount) return 0;

    let totalDiscount = 0;

    for (const item of cart.cart || []) {
      const appliesToProduct =
        discount.applies_to === 'all' ||
        discount.products?.some((p) => p.id === item.productId?.id);
      console.log('appliesToProduct:::', appliesToProduct);

      if (appliesToProduct) {
        const _unitPrice = Number(item.purchasePrice || 0);
        const _qty = Number(item.quantity || 0);
        const itemTotal = _unitPrice * _qty;

        const itemDiscount = this.calculatedDiscountAmount(
          _unitPrice,
          discountAmountValue,
          discount,
        );

        // Apply discount per unit and multiply by quantity
        const totalItemDiscount = itemDiscount * _qty;

        totalDiscount += Math.min(totalItemDiscount, itemTotal);
      }
    }

    return totalDiscount;
  }

  getCountryAmount(cart: Cart, discount: Discount) {
    const country = discount.countries.find(
      (item) => item.countryId == cart.countryId,
    );

    const _min_amount = Number(country?.min_amount || 0);
    const _amount = Number(country?.amount || 0);
    const _min_quantity = Number(country?.min_quantity || 0);

    if (this.getCartTotalQty(cart) < _min_amount) 0;

    if (this.getCartSubtotal(cart) < _min_quantity) 0;

    return _amount;
  }

  getCartSubtotal(cart: Cart) {
    return Number(cart.subtotal || 0);
  }


  /**
   * Calculate Buy X Get Y discount amount
   */
  private async calculateBXGYDiscountAmount(
    cart: Cart,
    discount: Discount,
    discountAmountValue: number,
  ): Promise<number> {
    const buyXMinQty = Number(discount.buy_x_min_qty || 1);
    const buyYMinQty = Number(discount.buy_y_min_qty || 1);

    // Find items that qualify for "buy" products
    const buyItems = cart.cart?.filter(
      (item) => discount.buy_products?.some((p) => p.id === item.productId?.id),
    ) || [];

    // Find items that qualify for "get" products
    const getItems = cart.cart?.filter(
      (item) => discount.get_products?.some((p) => p.id === item.productId?.id),
    ) || [];

    if (buyItems.length === 0 || getItems.length === 0) {
      return 0;
    }

    // Calculate total buy quantity
    const totalBuyQty = buyItems.reduce(
      (sum, item) => sum + Number(item.quantity || 0),
      0,
    );

    if (totalBuyQty < buyXMinQty) {
      return 0;
    }

    // Calculate how many free items customer can get
    const eligibleSets = Math.floor(totalBuyQty / buyXMinQty);
    const maxFreeQty = eligibleSets * buyYMinQty;

    let totalDiscount = 0;
    let remainingFreeQty = maxFreeQty;

    for (const item of getItems) {
      if (remainingFreeQty <= 0) break;

      const itemQty = Number(item.quantity || 0);
      const freeQtyForThisItem = Math.min(itemQty, remainingFreeQty);

      if (freeQtyForThisItem > 0) {
        const unitPrice = Number(item.purchasePrice || 0);

        // For BXGY, the discount amount is typically 100% (free items)
        // But we respect the discount configuration
        let itemDiscount: number;
        if (discount.amount_type === AmountType.PERCENTAGE) {
          itemDiscount = (unitPrice * freeQtyForThisItem * discountAmountValue) / 100;
        } else {
          // For fixed amount, apply the discount amount per free item
          itemDiscount = Math.min(discountAmountValue * freeQtyForThisItem, unitPrice * freeQtyForThisItem);
        }

        totalDiscount += itemDiscount;
        remainingFreeQty -= freeQtyForThisItem;
      }
    }

    return totalDiscount;
  }

  /**
   * Get affiliate by coupon code
   */
  async getAffiliateByCouponCode(couponCode: string): Promise<{ affiliate: AffiliateProfile, discount: any } | null> {
    try {
      // Find affiliate through affiliate_discounts table
      const affiliateDiscount = await this.affiliateDiscountRepo.findOne({
        where: {
          discount: {
            coupon: couponCode,
            is_active: true
          },
          status: AffiliateDiscountStatus.ACTIVE
        },
        relations: ['affiliate', 'discount']
      });

      if (!affiliateDiscount || !affiliateDiscount.affiliate) {
        return null;
      }

      return {
        affiliate: affiliateDiscount.affiliate,
        discount: affiliateDiscount.discount
      };
    } catch (error) {
      this.logger.error(`Error getting affiliate by coupon code: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Validate commission calculation by comparing with actual cart discount
   * This method can be used for testing and validation purposes
   */
  async validateCommissionCalculation(
    cartId: number,
    couponCode: string,
    actualDiscountAmount?: number
  ): Promise<ServiceResponse> {
    try {
      const cart = await this.cartRepo.findOne({
        where: { id: cartId },
        relations: ['cart', 'cart.productId', 'cart.discount'],
      });

      if (!cart) {
        return new ServiceResponse(
          {
            isValid: false,
            calculatedDiscount: 0,
            message: 'Cart not found'
          }, 'Cart not found', 404);
      }

      const discount = await this.discountRepo.findOne({
        where: { coupon: couponCode, is_active: true },
        relations: ['countries', 'products', 'buy_products', 'get_products']
      });

      if (!discount) {
        return new ServiceResponse(
          {
            isValid: false,
            calculatedDiscount: 0,
            message: 'Discount not found'
          }, 'Discount not found', 404);
      }

      // Use the actual discount amount from the cart
      // The cart.discount field contains the total discount applied by the main discount service
      const calculatedDiscount = Number(cart.discount || 0);

      // For debugging, also calculate component discounts
      const cartLevelDiscount = Number(cart.discount || 0);
      const productLevelDiscount = cart.cart?.reduce((sum, item) => {
        const originalPrice = Number(item.unitPrice || 0);
        const discountedPrice = Number(item.discountPrice || item.unitPrice || 0);
        const quantity = Number(item.quantity || 0);
        const itemDiscount = (originalPrice - discountedPrice) * quantity;
        return sum + itemDiscount;
      }, 0) || 0;

      console.log('Product-level discount:::', productLevelDiscount);
      console.log('Cart-level discount:::', cartLevelDiscount);
      console.log('Total calculated discount:::', calculatedDiscount);

      if (actualDiscountAmount !== undefined) {
        const difference = Math.abs(calculatedDiscount - actualDiscountAmount);
        const tolerance = 5.00; // Increased tolerance for different calculation methods

        // Calculate commission for validation using the same logic as previewCartCommission
        const affiliateDiscount = await this.affiliateDiscountRepo.findOne({
          where: {
            discountId: discount.id,
            status: AffiliateDiscountStatus.ACTIVE
          },
          relations: ['affiliate', 'affiliate.user']
        });

        const affiliate = affiliateDiscount?.affiliate;
        let commissionData = null;

        if (affiliate) {
          const cartTotal = Number(cart.total || 0);
          const commissionableAmount = cartTotal;

          // Determine commission rate and type (AffiliateDiscount takes priority)
          const effectiveCommissionType = affiliateDiscount?.commissionType || affiliate?.commissionType;
          const effectiveCommissionRate = affiliateDiscount?.commissionRate || affiliate?.commissionRate;

          let estimatedCommission = 0;
          if (effectiveCommissionType === CommissionType.PERCENTAGE) {
            estimatedCommission = (commissionableAmount * effectiveCommissionRate) / 100;
          } else {
            estimatedCommission = effectiveCommissionRate;
          }

          // Round to 2 decimal places
          estimatedCommission = Math.round(estimatedCommission * 100) / 100;

          commissionData = {
            affiliateId: affiliate.id,
            affiliateName: affiliate.user?.firstName + ' ' + affiliate.user?.lastName,
            commissionType: effectiveCommissionType,
            commissionRate: effectiveCommissionRate,
            commissionableAmount,
            estimatedCommission,
            commissionSource: affiliateDiscount?.commissionRate ? 'discount_specific' : 'affiliate_profile'
          };
        }

        // Also calculate original cart subtotal for reference
        const originalCartSubtotal = cart.cart?.reduce((sum, item) => {
          const originalPrice = Number(item.unitPrice || 0);
          const quantity = Number(item.quantity || 0);
          return sum + (originalPrice * quantity);
        }, 0) || 0;

        return new ServiceResponse(
          {
            isValid: difference <= tolerance,
            calculatedDiscount,
            actualDiscount: actualDiscountAmount,
            difference,
            originalCartSubtotal,
            cartSubtotalAfterDiscount: Number(cart.subtotal || 0),
            cartTotal: Number(cart.total || 0),
            productLevelDiscount,
            cartLevelDiscount,
            commission: commissionData,
            message: difference <= tolerance
              ? 'Calculation is within acceptable range'
              : `Calculation differs by ${difference.toFixed(2)} - this may be due to different calculation methods`
          }, 'Validation result');
      }

      // Calculate commission even when actualDiscountAmount is not provided
      const affiliateDiscount = await this.affiliateDiscountRepo.findOne({
        where: {
          discountId: discount.id,
          status: AffiliateDiscountStatus.ACTIVE
        },
        relations: ['affiliate', 'affiliate.user']
      });

      const affiliate = affiliateDiscount?.affiliate;
      let commissionData = null;

      if (affiliate) {
        const cartTotal = Number(cart.total || 0);
        const commissionableAmount = cartTotal;

        // Determine commission rate and type (AffiliateDiscount takes priority)
        const effectiveCommissionType = affiliateDiscount?.commissionType || affiliate?.commissionType;
        const effectiveCommissionRate = affiliateDiscount?.commissionRate || affiliate?.commissionRate;

        let estimatedCommission = 0;
        if (effectiveCommissionType === CommissionType.PERCENTAGE) {
          estimatedCommission = (commissionableAmount * effectiveCommissionRate) / 100;
        } else {
          estimatedCommission = effectiveCommissionRate;
        }

        // Round to 2 decimal places
        estimatedCommission = Math.round(estimatedCommission * 100) / 100;

        commissionData = {
          affiliateId: affiliate.id,
          affiliateName: affiliate.user?.firstName + ' ' + affiliate.user?.lastName,
          commissionType: effectiveCommissionType,
          commissionRate: effectiveCommissionRate,
          commissionableAmount,
          estimatedCommission,
          commissionSource: affiliateDiscount?.commissionRate ? 'discount_specific' : 'affiliate_profile'
        };
      }

      // Calculate original cart subtotal for reference
      const originalCartSubtotal = cart.cart?.reduce((sum, item) => {
        const originalPrice = Number(item.unitPrice || 0);
        const quantity = Number(item.quantity || 0);
        return sum + (originalPrice * quantity);
      }, 0) || 0;

      return new ServiceResponse(
        {
          isValid: true,
          calculatedDiscount,
          originalCartSubtotal,
          cartSubtotalAfterDiscount: Number(cart.subtotal || 0),
          cartTotal: Number(cart.total || 0),
          productLevelDiscount,
          cartLevelDiscount,
          commission: commissionData,
          message: 'Discount and commission calculated successfully'
        }, 'Validation result');

    } catch (error) {
      this.logger.error(`Error validating commission calculation: ${error.message}`, error.stack);
      return new ServiceResponse(
        {
          isValid: false,
          calculatedDiscount: 0,
          message: `Validation error: ${error.message}`
        }, 'Validation error');
    }
  }
}
