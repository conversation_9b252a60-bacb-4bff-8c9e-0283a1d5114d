import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, EntityManager } from 'typeorm';
import { AffiliateClick } from '../entities/affiliate-click.entity';
import { AffiliateConversion, ConversionType } from '../entities/affiliate-conversion.entity';
import { AffiliateProfile, AffiliateStatus } from '../entities/affiliate-profile.entity';
import { AffiliateCommission } from '../entities/affiliate-commission.entity';
import { ProductOrder } from '../../entities/product-order.entity';
import { UTMTrackingService, ConversionTrackingData } from './utm-tracking.service';
import { ServiceResponse } from 'src/common/utils/service-response';
import { AffiliateDiscount } from '../entities/affiliate-discount.entity';

export interface OrderConversionData {
  orderId: number;
  affiliateCode?: string;
  sessionId?: string;
  ipAddress?: string;
  userAgent?: string;
  conversionPage?: string;
}

export interface DirectConversionData {
  affiliateCode: string;
  sessionId?: string;
  orderId?: number;
  type: ConversionType;
  conversionValue: number;
  customerEmail?: string;
  customerPhone?: string;
  conversionPage?: string;
  couponUsed?: string;
  discountApplied?: number;
  commissionEarned?: number;
  ipAddress?: string;
  userAgent?: string;
}

@Injectable()
export class ConversionTrackingService {
  private readonly logger = new Logger(ConversionTrackingService.name);

  constructor(
    @InjectRepository(AffiliateClick)
    private clickRepo: Repository<AffiliateClick>,
    
    @InjectRepository(AffiliateConversion)
    private conversionRepo: Repository<AffiliateConversion>,
    
    @InjectRepository(AffiliateProfile)
    private affiliateRepo: Repository<AffiliateProfile>,
    
    @InjectRepository(AffiliateCommission)
    private commissionRepo: Repository<AffiliateCommission>,
    
    @InjectRepository(ProductOrder)
    private orderRepo: Repository<ProductOrder>,
    
    private utmTrackingService: UTMTrackingService,
  ) {}

  /**
   * Track order conversion - called when an order is placed
   */
  async trackOrderConversion(data: OrderConversionData, em?: EntityManager): Promise<ServiceResponse> {
    const entityManager = em || this.conversionRepo.manager;
    
    try {
      // Get order details
      const order = await entityManager.findOne(ProductOrder, {
        where: { id: data.orderId },
        relations: ['user', 'coupons', 'productOrderDetails']
      });

      if (!order) {
        throw new Error('Order not found');
      }

      // Find affiliate by coupon code or session
      let affiliate: AffiliateProfile | null = null;
      let affiliateDiscount: any = null;
      let originalClick: AffiliateClick | null = null;
      let couponUsed: string | null = null;

      // Try to find affiliate by coupon code first
      if (data.affiliateCode || order.coupons?.coupon) {
        const couponCode = data.affiliateCode || order.coupons?.coupon;
        couponUsed = couponCode;

        // Find affiliate through affiliate_discounts table
        affiliateDiscount = await entityManager
          .createQueryBuilder(AffiliateDiscount, 'ad')
          .leftJoinAndSelect('ad.affiliate', 'affiliate')
          .leftJoinAndSelect('ad.discount', 'discount')
          .where('discount.coupon = :coupon', { coupon: couponCode })
          .andWhere('discount.is_active = true')
          .andWhere('ad.status = :status', { status: 'active' })
          .getOne();

        affiliate = affiliateDiscount?.affiliate || null;
      }

      // If no affiliate found by coupon, try by session
      if (!affiliate && data.sessionId) {
        originalClick = await entityManager.findOne(AffiliateClick, {
          where: { 
            sessionId: data.sessionId,
            converted: false
          },
          relations: ['affiliate'],
          order: { createdAt: 'DESC' }
        });
        
        affiliate = originalClick?.affiliate || null;
      }

      if (!affiliate) {
        this.logger.warn(`No affiliate found for order ${data.orderId} with coupon: ${couponUsed}`);
        return new ServiceResponse(null, 'No affiliate found for this order');
      }

      this.logger.log(`✅ Affiliate found for order ${data.orderId}: ${affiliate.id} (${affiliate.user?.email})`);
      this.logger.log(`📋 Affiliate discount: ${affiliateDiscount?.id}, Coupon: ${couponUsed}`);

      // Calculate discount applied
      // const discountApplied = order.coupons?.discount_amount || 0;
      const discountApplied = order.discount || 0;
      
      // Get commission amount from existing commission record
      const commission = await entityManager.findOne(AffiliateCommission, {
        where: { orderId: data.orderId, affiliateId: affiliate.id }
      });

      const commissionEarned = commission?.commissionAmount || 0;

      // Create conversion tracking data
      const conversionData: ConversionTrackingData = {
        affiliateId: affiliate.id,
        affiliateDiscountId: affiliateDiscount?.id,
        clickId: originalClick?.id,
        orderId: data.orderId,
        type: ConversionType.PURCHASE,
        conversionValue: order.amount,
        customerEmail: order.user?.email,
        customerPhone: order.user?.phone,
        sessionId: data.sessionId,
        ipAddress: data.ipAddress,
        conversionPage: data.conversionPage,
        couponUsed,
        discountApplied,
        commissionEarned,
        conversionData: {
          orderTotal: order.amount,
          orderItems: order.productOrderDetails?.length || 0,
          paymentMethod: order.paymentMethod,
          orderStatus: order.deliveryStatus,
          userAgent: data.userAgent,
        }
      };

      // Track the conversion using UTM tracking service
      this.logger.log(`🔄 Tracking conversion for order ${data.orderId}, affiliate ${affiliate.id}`);
      this.logger.log(`📊 Conversion data:`, {
        affiliateId: conversionData.affiliateId,
        affiliateDiscountId: conversionData.affiliateDiscountId,
        orderId: conversionData.orderId,
        conversionValue: conversionData.conversionValue,
        commissionEarned: conversionData.commissionEarned
      });

      const result = await this.utmTrackingService.trackConversion(conversionData, entityManager);

      if (result.data) {
        this.logger.log(`✅ Order conversion tracked successfully for affiliate ${affiliate.id}, order ${data.orderId}, conversion ID: ${result.data.id}`);
      } else {
        this.logger.warn(`⚠️ Order conversion tracking returned no data for order ${data.orderId}: ${result.msg}`);
      }

      return result;
    } catch (error) {
      this.logger.error(`Error tracking order conversion: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Track direct conversion (not tied to an order)
   */
  async trackConversion(data: DirectConversionData, em?: EntityManager): Promise<ServiceResponse> {
    console.log('track conversion::::', data);
    
    const entityManager = em || this.conversionRepo.manager;

    try {
      // Find affiliate by affiliate code or coupon code
      let affiliate: AffiliateProfile | null = null;
      let affiliateDiscount: any = null;
      let originalClick: AffiliateClick | null = null;

      // Try to find affiliate by affiliate code first
      affiliate = await entityManager.findOne(AffiliateProfile, {
        where: {
          affiliateCode: data.affiliateCode,
          status: AffiliateStatus.ACTIVE
        }
      });

      // If not found by affiliate code, try by coupon code
      if (!affiliate) {
        affiliateDiscount = await entityManager
          .createQueryBuilder('affiliate_discount', 'ad')
          .leftJoinAndSelect('ad.affiliate', 'affiliate')
          .leftJoinAndSelect('ad.discount', 'discount')
          .where('discount.coupon = :coupon', { coupon: data.affiliateCode })
          .andWhere('discount.is_active = true')
          .andWhere('ad.status = :status', { status: 'active' })
          .getOne();

        affiliate = affiliateDiscount?.affiliate || null;
      }

      // If still no affiliate found and sessionId provided, try by session
      if (!affiliate && data.sessionId) {
        originalClick = await entityManager.findOne(AffiliateClick, {
          where: {
            sessionId: data.sessionId,
            converted: false
          },
          relations: ['affiliate'],
          order: { createdAt: 'DESC' }
        });

        affiliate = originalClick?.affiliate || null;
      }

      if (!affiliate) {
        this.logger.warn(`No affiliate found for conversion with code: ${data.affiliateCode}`);
        return new ServiceResponse(null, 'No affiliate found for this conversion');
      }

      // Create conversion tracking data
      const conversionData: ConversionTrackingData = {
        affiliateId: affiliate.id,
        affiliateDiscountId: affiliateDiscount?.id,
        clickId: originalClick?.id,
        orderId: data.orderId,
        type: data.type,
        conversionValue: data.conversionValue,
        customerEmail: data.customerEmail,
        customerPhone: data.customerPhone,
        sessionId: data.sessionId,
        ipAddress: data.ipAddress,
        conversionPage: data.conversionPage,
        couponUsed: data.couponUsed,
        discountApplied: data.discountApplied,
        commissionEarned: data.commissionEarned || 0,
        conversionData: {
          type: data.type,
          userAgent: data.userAgent,
          directTracking: true,
        }
      };

      // Track the conversion using UTM tracking service
      const result = await this.utmTrackingService.trackConversion(conversionData, entityManager);

      this.logger.log(`Direct conversion tracked for affiliate ${affiliate.id}, type: ${data.type}`);

      return result;
    } catch (error) {
      this.logger.error(`Error tracking direct conversion: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Track signup conversion
   */
  async trackSignupConversion(
    userId: number, 
    sessionId?: string, 
    ipAddress?: string,
    em?: EntityManager
  ): Promise<ServiceResponse> {
    const entityManager = em || this.conversionRepo.manager;
    
    try {
      // Find the original click by session
      const originalClick = await entityManager.findOne(AffiliateClick, {
        where: { 
          sessionId,
          converted: false
        },
        relations: ['affiliate'],
        order: { createdAt: 'DESC' }
      });

      if (!originalClick) {
        return new ServiceResponse(null, 'No affiliate click found for this signup');
      }

      const conversionData: ConversionTrackingData = {
        affiliateId: originalClick.affiliateId,
        clickId: originalClick.id,
        type: ConversionType.SIGNUP,
        conversionValue: 0, // Signups typically have no direct value
        sessionId,
        ipAddress,
        conversionData: {
          userId,
          conversionType: 'user_registration'
        }
      };

      const result = await this.utmTrackingService.trackConversion(conversionData, entityManager);
      
      this.logger.log(`Signup conversion tracked for affiliate ${originalClick.affiliateId}, user ${userId}`);
      
      return result;
    } catch (error) {
      this.logger.error(`Error tracking signup conversion: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get conversion funnel analytics
   */
  async getConversionFunnel(affiliateId: number, dateFrom?: Date, dateTo?: Date): Promise<ServiceResponse> {
    try {
      const baseQuery = this.clickRepo
        .createQueryBuilder('click')
        .where('click.affiliateId = :affiliateId', { affiliateId });

      if (dateFrom) {
        baseQuery.andWhere('click.createdAt >= :dateFrom', { dateFrom });
      }
      
      if (dateTo) {
        baseQuery.andWhere('click.createdAt <= :dateTo', { dateTo });
      }

      // Get total clicks
      const totalClicks = await baseQuery.getCount();

      // Get conversions by type
      const conversionQuery = this.conversionRepo
        .createQueryBuilder('conversion')
        .where('conversion.affiliateId = :affiliateId', { affiliateId });

      if (dateFrom) {
        conversionQuery.andWhere('conversion.createdAt >= :dateFrom', { dateFrom });
      }
      
      if (dateTo) {
        conversionQuery.andWhere('conversion.createdAt <= :dateTo', { dateTo });
      }

      const conversionsByType = await conversionQuery
        .select('conversion.type', 'type')
        .addSelect('COUNT(*)', 'count')
        .addSelect('SUM(conversion.conversionValue)', 'totalValue')
        .addSelect('SUM(conversion.commissionEarned)', 'totalCommission')
        .groupBy('conversion.type')
        .getRawMany();

      // Calculate funnel metrics
      const signups = conversionsByType.find(c => c.type === ConversionType.SIGNUP)?.count || 0;
      const purchases = conversionsByType.find(c => c.type === ConversionType.PURCHASE)?.count || 0;
      const leads = conversionsByType.find(c => c.type === ConversionType.LEAD)?.count || 0;

      const clickToSignupRate = totalClicks > 0 ? (signups / totalClicks) * 100 : 0;
      const clickToPurchaseRate = totalClicks > 0 ? (purchases / totalClicks) * 100 : 0;
      const signupToPurchaseRate = signups > 0 ? (purchases / signups) * 100 : 0;

      const funnel = {
        totalClicks,
        totalConversions: conversionsByType.reduce((sum, c) => sum + parseInt(c.count), 0),
        conversionsByType: conversionsByType.map(c => ({
          type: c.type,
          count: parseInt(c.count),
          totalValue: parseFloat(c.totalValue) || 0,
          totalCommission: parseFloat(c.totalCommission) || 0,
          conversionRate: totalClicks > 0 ? ((parseInt(c.count) / totalClicks) * 100).toFixed(2) : '0.00'
        })),
        funnelMetrics: {
          clickToSignupRate: clickToSignupRate.toFixed(2),
          clickToPurchaseRate: clickToPurchaseRate.toFixed(2),
          signupToPurchaseRate: signupToPurchaseRate.toFixed(2),
        }
      };

      return new ServiceResponse(funnel, 'Conversion funnel retrieved successfully');
    } catch (error) {
      this.logger.error(`Error getting conversion funnel: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get conversion timeline
   */
  async getConversionTimeline(affiliateId: number, days: number = 30): Promise<ServiceResponse> {
    try {
      const dateFrom = new Date();
      dateFrom.setDate(dateFrom.getDate() - days);

      const conversions = await this.conversionRepo
        .createQueryBuilder('conversion')
        .select('DATE(conversion.createdAt)', 'date')
        .addSelect('conversion.type', 'type')
        .addSelect('COUNT(*)', 'count')
        .addSelect('SUM(conversion.conversionValue)', 'totalValue')
        .where('conversion.affiliateId = :affiliateId', { affiliateId })
        .andWhere('conversion.createdAt >= :dateFrom', { dateFrom })
        .groupBy('DATE(conversion.createdAt), conversion.type')
        .orderBy('DATE(conversion.createdAt)', 'ASC')
        .getRawMany();

      const timeline = conversions.map(c => ({
        date: c.date,
        type: c.type,
        count: parseInt(c.count),
        totalValue: parseFloat(c.totalValue) || 0,
      }));

      return new ServiceResponse(timeline, 'Conversion timeline retrieved successfully');
    } catch (error) {
      this.logger.error(`Error getting conversion timeline: ${error.message}`, error.stack);
      throw error;
    }
  }
}
