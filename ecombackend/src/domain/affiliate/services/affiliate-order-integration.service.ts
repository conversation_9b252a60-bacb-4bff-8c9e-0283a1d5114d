import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, EntityManager } from 'typeorm';
import { AffiliateProfile } from '../entities/affiliate-profile.entity';
import { AffiliateDiscount } from '../entities/affiliate-discount.entity';
import { AffiliateDiscountUsage } from '../entities/affiliate-discount-usage.entity';
import { AffiliateCommission, CommissionStatus } from '../entities/affiliate-commission.entity';
import { AffiliateClick } from '../entities/affiliate-click.entity';
import { AffiliateDiscountCommissionService } from './affiliate-discount-commission.service';
import { ConversionTrackingService } from './conversion-tracking.service';
import { Cart } from '../../cart/entities/cart.entity';
import { ProductOrder } from '../../entities/product-order.entity';
import { ServiceResponse } from '../../../common/utils/service-response';

@Injectable()
export class AffiliateOrderIntegrationService {
  private readonly logger = new Logger(AffiliateOrderIntegrationService.name);

  constructor(
    @InjectRepository(AffiliateProfile)
    private affiliateRepo: Repository<AffiliateProfile>,

    @InjectRepository(AffiliateDiscount)
    private affiliateDiscountRepo: Repository<AffiliateDiscount>,

    @InjectRepository(AffiliateDiscountUsage)
    private affiliateDiscountUsageRepo: Repository<AffiliateDiscountUsage>,

    @InjectRepository(AffiliateCommission)
    private affiliateCommissionRepo: Repository<AffiliateCommission>,

    @InjectRepository(Cart)
    private cartRepo: Repository<Cart>,

    @InjectRepository(ProductOrder)
    private orderRepo: Repository<ProductOrder>,

    private readonly affiliateDiscountCommissionService: AffiliateDiscountCommissionService,
    private readonly conversionTrackingService: ConversionTrackingService,
  ) {}

  /**
   * Apply affiliate discount to cart and preview commission
   * This should be called when a coupon is applied to a cart
   */
  async applyAffiliateDiscountToCart(
    cartId: number,
    couponCode: string,
    sessionData?: any
  ): Promise<ServiceResponse> {
    try {
      // Get cart
      const cart = await this.cartRepo.findOne({
        where: { id: cartId },
        // relations: ['cart', 'cart.product']
        relations: ['cart', 'cart.productId', 'cart.discount'],
      });

      if (!cart) {
        return new ServiceResponse(null, 'Cart not found');
      }

      // Check if this is an affiliate discount
      const result = await this.affiliateDiscountCommissionService.getAffiliateByCouponCode(couponCode);

      if (!result) {
        return new ServiceResponse(null, 'Not an affiliate discount');
      }

      const { affiliate, discount } = result;

      // Preview commission for this cart
      const commissionPreview = await this.affiliateDiscountCommissionService.previewCartCommission(
        cartId,
        couponCode
      );

      if (!commissionPreview) {
        return new ServiceResponse(null, 'Unable to calculate commission preview');
      }

      // Store affiliate tracking data in session/cart for later use
      const trackingData = {
        affiliateId: affiliate.id,
        couponCode,
        discountId: discount.id,
        sessionData,
        appliedAt: new Date(),
      };

      return new ServiceResponse({
        isAffiliateDiscount: true,
        affiliate: {
          id: affiliate.id,
          userId: affiliate.userId,
          commissionRate: affiliate.commissionRate,
          commissionType: affiliate.commissionType,
        },
        commissionPreview,
        trackingData,
      }, 'Affiliate discount applied successfully');

    } catch (error) {
      this.logger.error(`Error applying affiliate discount to cart: ${error.message}`, error.stack);
      return new ServiceResponse(null, `Error: ${error.message}`);
    }
  }

  /**
   * Process order completion and create commission
   * This should be called after an order is successfully completed
   */
  async processOrderCompletion(
    orderId: number,
    transactionalEM?: EntityManager
  ): Promise<ServiceResponse> {
    const em = transactionalEM || this.orderRepo.manager;

    try {
      // Calculate commission for the order
      const calculationResult = await this.affiliateDiscountCommissionService.calculateOrderCommission(
        orderId,
        em
      );

      if (!calculationResult.success) {
        return new ServiceResponse(null, calculationResult.message || 'Commission calculation failed');
      }

      // Create commission record if eligible
      if (calculationResult.commissionAmount > 0) {
        const commission = await this.affiliateDiscountCommissionService.createCommissionRecord(
          orderId,
          calculationResult,
          em
        );

        if (commission) {
          // Update affiliate statistics
          try {
            await this.updateAffiliateStatistics(calculationResult.affiliateId, calculationResult, em);
            this.logger.log(`✅ Affiliate statistics updated successfully for affiliate ${calculationResult.affiliateId}`);
          } catch (statsError) {
            this.logger.error(`❌ Error updating affiliate statistics (but commission was created successfully):`, {
              error: statsError.message,
              stack: statsError.stack,
              affiliateId: calculationResult.affiliateId,
              commissionId: commission.id
            });
            // Don't throw error - commission creation was successful
          }

          this.logger.log(`✅ Commission created for order ${orderId}: ${calculationResult.commissionAmount}`);

          // Track conversion for this order
          try {
            await this.trackOrderConversion(orderId, calculationResult, em);
            this.logger.log(`✅ Order conversion tracked successfully for order ${orderId}`);
          } catch (conversionError) {
            this.logger.error(`❌ Error tracking order conversion (but commission was created successfully):`, {
              error: conversionError.message,
              stack: conversionError.stack,
              orderId,
              commissionId: commission.id
            });
            // Don't throw error - commission creation was successful
          }

          return new ServiceResponse({
            commission,
            calculationResult,
          }, 'Commission created successfully');
        }
      }

      return new ServiceResponse(null, 'No commission created - amount too low or other criteria not met');

    } catch (error) {
      this.logger.error(`Error processing order completion: ${error.message}`, error.stack);
      return new ServiceResponse(null, `Error: ${error.message}`);
    }
  }

  /**
   * Get commission preview for cart with specific coupon
   */
  async getCartCommissionPreview(
    cartId: number,
    couponCode: string
  ): Promise<ServiceResponse> {
    try {
      const commissionPreview = await this.affiliateDiscountCommissionService.previewCartCommission(
        cartId,
        couponCode
      );

      if (!commissionPreview) {
        return new ServiceResponse(null, 'Unable to calculate commission preview');
      }

      return new ServiceResponse(commissionPreview, 'Commission preview calculated successfully');

    } catch (error) {
      this.logger.error(`Error getting cart commission preview: ${error.message}`, error.stack);
      return new ServiceResponse(null, `Error: ${error.message}`);
    }
  }

  /**
   * Validate if a coupon code belongs to an affiliate
   */
  async validateAffiliateCoupon(couponCode: string): Promise<ServiceResponse> {
    try {
      const result = await this.affiliateDiscountCommissionService.getAffiliateByCouponCode(couponCode);

      if (!result) {
        return new ServiceResponse(
          { isAffiliateDiscount: false },
          'Not an affiliate discount'
        );
      }

      const { affiliate, discount } = result;

      return new ServiceResponse({
        isAffiliateDiscount: true,
        affiliate: {
          id: affiliate.id,
          userId: affiliate.userId,
          commissionRate: affiliate.commissionRate,
          commissionType: affiliate.commissionType,
          status: affiliate.status,
        },
        discount: {
          id: discount.id,
          coupon: discount.coupon,
          discountType: discount.discount_type,
          amountType: discount.amount_type,
          title: discount.title,
          isActive: discount.is_active,
        },
      }, 'Valid affiliate discount');

    } catch (error) {
      this.logger.error(`Error validating affiliate coupon: ${error.message}`, error.stack);
      return new ServiceResponse(null, `Error: ${error.message}`);
    }
  }

  /**
   * Get affiliate commission history
   */
  async getAffiliateCommissions(
    affiliateId: number,
    status?: CommissionStatus,
    limit: number = 50,
    offset: number = 0
  ): Promise<ServiceResponse> {
    try {
      const queryBuilder = this.affiliateCommissionRepo
        .createQueryBuilder('commission')
        .leftJoinAndSelect('commission.order', 'order')
        .leftJoinAndSelect('commission.discount', 'discount')
        .where('commission.affiliateId = :affiliateId', { affiliateId })
        .orderBy('commission.createdAt', 'DESC')
        .limit(limit)
        .offset(offset);

      if (status) {
        queryBuilder.andWhere('commission.status = :status', { status });
      }

      const [commissions, total] = await queryBuilder.getManyAndCount();

      return new ServiceResponse({
        commissions,
        total,
        limit,
        offset,
      }, 'Commissions retrieved successfully');

    } catch (error) {
      this.logger.error(`Error getting affiliate commissions: ${error.message}`, error.stack);
      return new ServiceResponse(null, `Error: ${error.message}`);
    }
  }

  /**
   * Get affiliate earnings summary
   */
  async getAffiliateEarningsSummary(affiliateId: number): Promise<ServiceResponse> {
    try {
      const affiliate = await this.affiliateRepo.findOne({
        where: { id: affiliateId },
        relations: ['country', 'affiliateDiscounts', 'affiliateDiscounts.discount']
      });

      if (!affiliate) {
        return new ServiceResponse(null, 'Affiliate not found');
      }

      // Get commission statistics
      const commissionStats = await this.affiliateCommissionRepo
        .createQueryBuilder('commission')
        .select([
          'COUNT(*) as totalCommissions',
          'SUM(CASE WHEN status = :pending THEN commission_amount ELSE 0 END) as pendingEarnings',
          'SUM(CASE WHEN status = :approved THEN commission_amount ELSE 0 END) as approvedEarnings',
          'SUM(CASE WHEN status = :paid THEN commission_amount ELSE 0 END) as paidEarnings',
          'SUM(commission_amount) as totalEarnings',
          'AVG(commission_amount) as averageCommission',
        ])
        .where('commission.affiliateId = :affiliateId', { affiliateId })
        .setParameters({
          pending: CommissionStatus.PENDING,
          approved: CommissionStatus.APPROVED,
          paid: CommissionStatus.PAID,
        })
        .getRawOne();

      // Get recent performance
      const recentCommissions = await this.affiliateCommissionRepo.find({
        where: { affiliateId },
        order: { createdAt: 'DESC' },
        take: 10,
        relations: ['order']
      });

      return new ServiceResponse({
        affiliate: {
          id: affiliate.id,
          userId: affiliate.userId,
          commissionRate: affiliate.commissionRate,
          commissionType: affiliate.commissionType,
          status: affiliate.status,
          country: affiliate.country ? {
            id: affiliate.country.id,
            name: affiliate.country.name,
            code: affiliate.country.code
          } : null,
          couponCodes: affiliate.affiliateDiscounts?.map(ad => ad.discount?.coupon).filter(Boolean) || [],
        },
        earnings: {
          totalCommissions: parseInt(commissionStats.totalCommissions) || 0,
          pendingEarnings: parseFloat(commissionStats.pendingEarnings) || 0,
          approvedEarnings: parseFloat(commissionStats.approvedEarnings) || 0,
          paidEarnings: parseFloat(commissionStats.paidEarnings) || 0,
          totalEarnings: parseFloat(commissionStats.totalEarnings) || 0,
          averageCommission: parseFloat(commissionStats.averageCommission) || 0,
        },
        recentCommissions,
      }, 'Earnings summary retrieved successfully');

    } catch (error) {
      this.logger.error(`Error getting affiliate earnings summary: ${error.message}`, error.stack);
      return new ServiceResponse(null, `Error: ${error.message}`);
    }
  }

  /**
   * Update affiliate statistics after commission creation
   * Properly calculates and updates all affiliate statistics with existing data
   */
  private async updateAffiliateStatistics(
    affiliateId: number,
    calculationResult: any,
    em: EntityManager
  ): Promise<void> {
    try {
      this.logger.log(`Updating affiliate statistics for affiliate ${affiliateId}`);

      // Get current affiliate data with proper decimal handling
      const affiliate = await em.findOne(AffiliateProfile, { where: { id: affiliateId } });
      if (!affiliate) {
        this.logger.error(`❌ Affiliate not found: ${affiliateId}`);
        return;
      }

      this.logger.log(`Current affiliate stats:`, {
        id: affiliate.id,
        totalEarnings: affiliate.totalEarnings || 0,
        pendingEarnings: affiliate.pendingEarnings || 0,
        totalOrders: affiliate.totalOrders || 0,
        totalClicks: affiliate.totalClicks || 0,
        conversionRate: affiliate.conversionRate || 0
      });

      // Get comprehensive statistics from AffiliateDiscountUsage for more accurate tracking
      const usageStats = await em
        .createQueryBuilder(AffiliateDiscountUsage, 'usage')
        .leftJoin('usage.affiliateDiscount', 'affiliateDiscount')
        .leftJoin(AffiliateCommission, 'commission', 'commission.orderId = usage.orderId AND commission.affiliateId = affiliateDiscount.affiliateId')
        .select([
          'COUNT(usage.id) as totalOrders',
          'SUM(usage.order_amount) as totalOrderValue',
          'SUM(usage.discount_amount) as totalDiscountAmount',
          'SUM(usage.commission_amount) as totalCommissionFromUsage',
          'AVG(usage.commission_amount) as averageCommission',
          'AVG(usage.order_amount) as averageOrderValue',
          'COUNT(DISTINCT usage.customer_email) as uniqueCustomers'
        ])
        .where('affiliateDiscount.affiliateId = :affiliateId', { affiliateId })
        .getRawOne();

      // Get commission statistics by status for earnings breakdown
      const commissionStats = await em
        .createQueryBuilder(AffiliateCommission, 'commission')
        .select([
          'COUNT(*) as totalCommissions',
          'SUM(CASE WHEN status = :pending THEN commission_amount ELSE 0 END) as pendingEarnings',
          'SUM(CASE WHEN status = :approved THEN commission_amount ELSE 0 END) as approvedEarnings',
          'SUM(CASE WHEN status = :paid THEN commission_amount ELSE 0 END) as paidEarnings',
          'SUM(commission_amount) as totalEarnings'
        ])
        .where('commission.affiliateId = :affiliateId', { affiliateId })
        .setParameters({
          pending: CommissionStatus.PENDING,
          approved: CommissionStatus.APPROVED,
          paid: CommissionStatus.PAID,
        })
        .getRawOne();

      // Calculate values using AffiliateDiscountUsage data for accuracy
      const currentTotalEarnings = Number(affiliate.totalEarnings || 0);
      const currentPendingEarnings = Number(affiliate.pendingEarnings || 0);
      const currentTotalOrders = Number(affiliate.totalOrders || 0);
      const currentTotalClicks = Number(affiliate.totalClicks || 0);

      // Use usage statistics for more accurate order tracking
      const usageTotalOrders = Number(usageStats.totalOrders || 0);
      const usageTotalOrderValue = Number(usageStats.totalOrderValue || 0);
      const usageTotalCommission = Number(usageStats.totalCommissionFromUsage || 0);
      const usageAverageCommission = Number(usageStats.averageCommission || 0);
      const usageAverageOrderValue = Number(usageStats.averageOrderValue || 0);
      const usageUniqueCustomers = Number(usageStats.uniqueCustomers || 0);

      // Use commission stats for earnings breakdown by status
      const dbTotalEarnings = Number(commissionStats.totalEarnings || 0);
      const dbPendingEarnings = Number(commissionStats.pendingEarnings || 0);
      const dbTotalCommissions = Number(commissionStats.totalCommissions || 0);

      // Calculate new values prioritizing usage data for order counts
      const newTotalOrders = Math.max(currentTotalOrders + 1, usageTotalOrders, dbTotalCommissions);
      const newTotalEarnings = Math.max(currentTotalEarnings + Number(calculationResult.commissionAmount), dbTotalEarnings);
      const newPendingEarnings = Math.max(currentPendingEarnings + Number(calculationResult.commissionAmount), dbPendingEarnings);

      // Calculate conversion rate with proper handling of zero clicks
      const newConversionRate = currentTotalClicks > 0 ?
        Number(((newTotalOrders / currentTotalClicks) * 100).toFixed(2)) : 0;

      // Use usage statistics for more accurate averages
      const newAverageOrderValue = usageTotalOrders > 0 ?
        Number((usageTotalOrderValue / usageTotalOrders).toFixed(2)) : 0;

      const newAverageCommission = newTotalOrders > 0 ?
        Number((newTotalEarnings / newTotalOrders).toFixed(2)) : 0;

      this.logger.log(`Calculated new affiliate statistics:`, {
        newTotalOrders,
        newTotalEarnings: newTotalEarnings.toFixed(2),
        newPendingEarnings: newPendingEarnings.toFixed(2),
        newConversionRate,
        newAverageOrderValue,
        newAverageCommission,
        usageBasedOrders: usageTotalOrders,
        commissionBasedOrders: dbTotalCommissions
      });

      // Update all statistics in a single query to avoid race conditions
      const updateResult = await em.update(AffiliateProfile, { id: affiliateId }, {
        totalEarnings: Number(newTotalEarnings.toFixed(2)),
        pendingEarnings: Number(newPendingEarnings.toFixed(2)),
        totalOrders: newTotalOrders,
        conversionRate: newConversionRate,
        // Update additional calculated fields if they exist in the entity
        ...(affiliate.hasOwnProperty('averageOrderValue') && { averageOrderValue: newAverageOrderValue }),
        ...(affiliate.hasOwnProperty('averageCommission') && { averageCommission: newAverageCommission }),
        updatedAt: new Date()
      });

      this.logger.log(`✅ Affiliate profile update result:`, {
        affiliateId,
        affected: updateResult.affected,
        newTotalOrders,
        newTotalEarnings: newTotalEarnings.toFixed(2),
        newPendingEarnings: newPendingEarnings.toFixed(2)
      });

      this.logger.log(`Updated affiliate ${affiliateId} statistics:`, {
        totalOrders: newTotalOrders,
        totalEarnings: newTotalEarnings.toFixed(2),
        pendingEarnings: newPendingEarnings.toFixed(2),
        conversionRate: `${newConversionRate}%`,
        averageCommission: newAverageCommission.toFixed(2),
        averageOrderValue: newAverageOrderValue.toFixed(2),
        totalClicks: currentTotalClicks,
        uniqueCustomers: usageUniqueCustomers,
        usageBasedOrders: usageTotalOrders,
        commissionBasedOrders: dbTotalCommissions,
        commissionAmount: Number(calculationResult.commissionAmount).toFixed(2)
      });

    } catch (error) {
      this.logger.error(`Error updating affiliate statistics for affiliate ${affiliateId}:`, {
        error: error.message,
        stack: error.stack,
        calculationResult: calculationResult
      });

      // Don't throw error to avoid breaking the commission creation process
      // Just log the error and continue
    }
  }

  /**
   * Recalculate all affiliate statistics from existing commission data
   * Useful for data consistency and fixing any discrepancies
   */
  async recalculateAffiliateStatistics(
    affiliateId: number,
    em?: EntityManager
  ): Promise<ServiceResponse> {
    const entityManager = em || this.affiliateRepo.manager;

    try {
      // Get affiliate
      const affiliate = await entityManager.findOne(AffiliateProfile, {
        where: { id: affiliateId },
        // relations: ['affiliateClicks', 'affiliateCommissions']
      });

      if (!affiliate) {
        return new ServiceResponse(null, 'Affiliate not found', 404);
      }

      // Get comprehensive statistics from AffiliateDiscountUsage for accurate tracking
      const usageStats = await entityManager
        .createQueryBuilder(AffiliateDiscountUsage, 'usage')
        .leftJoin('usage.affiliateDiscount', 'affiliateDiscount')
        .select([
          'COUNT(usage.id) as totalOrders',
          'SUM(usage.order_amount) as totalOrderValue',
          'SUM(usage.discount_amount) as totalDiscountAmount',
          'SUM(usage.commission_amount) as totalCommissionFromUsage',
          'AVG(usage.commission_amount) as averageCommission',
          'AVG(usage.order_amount) as averageOrderValue',
          'COUNT(DISTINCT usage.customer_email) as uniqueCustomers',
          'MIN(usage.created_at) as firstOrderDate',
          'MAX(usage.created_at) as lastOrderDate'
        ])
        .where('affiliateDiscount.affiliateId = :affiliateId', { affiliateId })
        .getRawOne();

      // Get commission statistics by status for earnings breakdown (through affiliate discounts)
      const commissionStats = await entityManager
        .createQueryBuilder(AffiliateCommission, 'commission')
        .leftJoin('commission.affiliateDiscount', 'affiliateDiscount')
        .select([
          'COUNT(*) as totalCommissions',
          'SUM(CASE WHEN commission.status = :pending THEN commission.commission_amount ELSE 0 END) as pendingEarnings',
          'SUM(CASE WHEN commission.status = :approved THEN commission.commission_amount ELSE 0 END) as approvedEarnings',
          'SUM(CASE WHEN commission.status = :paid THEN commission.commission_amount ELSE 0 END) as paidEarnings',
          'SUM(commission.commission_amount) as totalEarnings',
          'MIN(commission.created_at) as firstCommissionDate',
          'MAX(commission.created_at) as lastCommissionDate'
        ])
        .where('affiliateDiscount.affiliateId = :affiliateId', { affiliateId })
        .setParameters({
          pending: CommissionStatus.PENDING,
          approved: CommissionStatus.APPROVED,
          paid: CommissionStatus.PAID,
        })
        .getRawOne();

      // Get click statistics (if click tracking is implemented)
      const clickStats = await entityManager
        .createQueryBuilder(AffiliateClick, 'click')
        .select([
          'COUNT(*) as totalClicks',
          'COUNT(DISTINCT click.sessionId) as uniqueVisitors',
          'MIN(click.createdAt) as firstClickDate',
          'MAX(click.createdAt) as lastClickDate'
        ])
        .where('click.affiliateId = :affiliateId', { affiliateId })
        .getRawOne();

      // Calculate accurate statistics using usage data as primary source
      const totalOrdersFromUsage = Number(usageStats.totalOrders || 0);
      const totalOrderValueFromUsage = Number(usageStats.totalOrderValue || 0);
      const totalDiscountAmount = Number(usageStats.totalDiscountAmount || 0);
      const totalCommissionFromUsage = Number(usageStats.totalCommissionFromUsage || 0);
      const uniqueCustomers = Number(usageStats.uniqueCustomers || 0);

      // Use commission stats for status-based earnings
      const totalCommissions = Number(commissionStats.totalCommissions || 0);
      const totalEarnings = Number(commissionStats.totalEarnings || 0);
      const pendingEarnings = Number(commissionStats.pendingEarnings || 0);
      const approvedEarnings = Number(commissionStats.approvedEarnings || 0);
      const paidEarnings = Number(commissionStats.paidEarnings || 0);

      // Use the higher count between usage and commission records for accuracy
      const finalTotalOrders = Math.max(totalOrdersFromUsage, totalCommissions);
      const finalTotalOrderValue = totalOrderValueFromUsage || 0;

      const totalClicks = Number(clickStats?.totalClicks || affiliate.totalClicks || 0);
      const uniqueVisitors = Number(clickStats?.uniqueVisitors || 0);

      // Calculate derived statistics
      const conversionRate = totalClicks > 0 ?
        Number(((finalTotalOrders / totalClicks) * 100).toFixed(2)) : 0;

      const averageOrderValue = finalTotalOrders > 0 ?
        Number((finalTotalOrderValue / finalTotalOrders).toFixed(2)) : 0;

      const averageCommission = finalTotalOrders > 0 ?
        Number((totalEarnings / finalTotalOrders).toFixed(2)) : 0;

      // Update affiliate profile with recalculated statistics
      await entityManager.update(AffiliateProfile, { id: affiliateId }, {
        totalOrders: finalTotalOrders,
        totalEarnings: Number(totalEarnings.toFixed(2)),
        pendingEarnings: Number(pendingEarnings.toFixed(2)),
        totalClicks: totalClicks,
        conversionRate: conversionRate,
        // Update additional fields if they exist in the entity
        ...(affiliate.hasOwnProperty('approvedEarnings') && { approvedEarnings: Number(approvedEarnings.toFixed(2)) }),
        ...(affiliate.hasOwnProperty('paidEarnings') && { paidEarnings: Number(paidEarnings.toFixed(2)) }),
        ...(affiliate.hasOwnProperty('averageOrderValue') && { averageOrderValue }),
        ...(affiliate.hasOwnProperty('averageCommission') && { averageCommission }),
        ...(affiliate.hasOwnProperty('uniqueVisitors') && { uniqueVisitors }),
        ...(affiliate.hasOwnProperty('uniqueCustomers') && { uniqueCustomers }),
        updatedAt: new Date()
      });

      const recalculatedStats = {
        affiliateId,
        totalOrders: finalTotalOrders,
        totalCommissions,
        totalEarnings: Number(totalEarnings.toFixed(2)),
        pendingEarnings: Number(pendingEarnings.toFixed(2)),
        approvedEarnings: Number(approvedEarnings.toFixed(2)),
        paidEarnings: Number(paidEarnings.toFixed(2)),
        totalClicks,
        uniqueVisitors,
        uniqueCustomers,
        conversionRate,
        averageOrderValue,
        averageCommission,
        totalOrderValue: Number(finalTotalOrderValue.toFixed(2)),
        totalDiscountAmount: Number(totalDiscountAmount.toFixed(2)),
        totalCommissionFromUsage: Number(totalCommissionFromUsage.toFixed(2)),
        firstCommissionDate: commissionStats.firstCommissionDate,
        lastCommissionDate: commissionStats.lastCommissionDate,
        firstOrderDate: usageStats.firstOrderDate,
        lastOrderDate: usageStats.lastOrderDate,
        firstClickDate: clickStats?.firstClickDate,
        lastClickDate: clickStats?.lastClickDate
      };

      this.logger.log(`Recalculated affiliate ${affiliateId} statistics:`, recalculatedStats);

      return new ServiceResponse(recalculatedStats, 'Affiliate statistics recalculated successfully');

    } catch (error) {
      this.logger.error(`Error recalculating affiliate statistics for affiliate ${affiliateId}:`, {
        error: error.message,
        stack: error.stack
      });

      return new ServiceResponse(null, `Error recalculating statistics: ${error.message}`);
    }
  }

  /**
   * Recalculate statistics for all affiliates
   * Useful for bulk data consistency operations
   */
  async recalculateAllAffiliateStatistics(): Promise<ServiceResponse> {
    try {
      const affiliates = await this.affiliateRepo.find({
        select: ['id'],
        where: { isActive: true }
      });

      const results = [];
      let successCount = 0;
      let errorCount = 0;

      for (const affiliate of affiliates) {
        try {
          const result = await this.recalculateAffiliateStatistics(affiliate.id);
          if (result.data) {
            successCount++;
          } else {
            errorCount++;
          }
          results.push({
            affiliateId: affiliate.id,
            success: !!result.data,
            message: result.msg || 'Unknown error'
          });
        } catch (error) {
          errorCount++;
          results.push({
            affiliateId: affiliate.id,
            success: false,
            message: error.message
          });
        }
      }

      this.logger.log(`Bulk recalculation completed: ${successCount} success, ${errorCount} errors`);

      return new ServiceResponse({
        totalProcessed: affiliates.length,
        successCount,
        errorCount,
        results
      }, `Bulk recalculation completed: ${successCount}/${affiliates.length} successful`);

    } catch (error) {
      this.logger.error(`Error in bulk affiliate statistics recalculation:`, {
        error: error.message,
        stack: error.stack
      });

      return new ServiceResponse(null, `Error in bulk recalculation: ${error.message}`);
    }
  }

  /**
   * Get detailed affiliate statistics based on AffiliateDiscountUsage
   */
  async getAffiliateUsageStatistics(affiliateId: number): Promise<ServiceResponse> {
    try {
      const affiliate = await this.affiliateRepo.findOne({
        where: { id: affiliateId },
        relations: ['affiliateDiscounts', 'affiliateDiscounts.discount']
      });

      if (!affiliate) {
        return new ServiceResponse(null, 'Affiliate not found', 404);
      }

      // Get comprehensive usage statistics
      const usageStats = await this.affiliateDiscountUsageRepo
        .createQueryBuilder('usage')
        .leftJoin('usage.affiliateDiscount', 'affiliateDiscount')
        .leftJoin('usage.order', 'order')
        .select([
          'COUNT(usage.id) as totalUsages',
          'SUM(usage.order_amount) as totalOrderValue',
          'SUM(usage.discount_amount) as totalDiscountAmount',
          'SUM(usage.commission_amount) as totalCommissionEarned',
          'AVG(usage.order_amount) as averageOrderValue',
          'AVG(usage.commission_amount) as averageCommission',
          'AVG(usage.commission_rate) as averageCommissionRate',
          'COUNT(DISTINCT usage.customer_email) as uniqueCustomers',
          'COUNT(DISTINCT DATE(usage.created_at)) as activeDays',
          'MIN(usage.created_at) as firstUsage',
          'MAX(usage.created_at) as lastUsage'
        ])
        .where('affiliateDiscount.affiliateId = :affiliateId', { affiliateId })
        .getRawOne();

      // Get usage breakdown by discount
      const discountBreakdown = await this.affiliateDiscountUsageRepo
        .createQueryBuilder('usage')
        .leftJoin('usage.affiliateDiscount', 'affiliateDiscount')
        .leftJoin('affiliateDiscount.discount', 'discount')
        .select([
          'discount.id as discountId',
          'discount.coupon as couponCode',
          'discount.title as discountTitle',
          'COUNT(usage.id) as usageCount',
          'SUM(usage.order_amount) as totalOrderValue',
          'SUM(usage.commission_amount) as totalCommission',
          'AVG(usage.commission_rate) as averageCommissionRate',
          'MIN(usage.created_at) as firstUsed',
          'MAX(usage.created_at) as lastUsed'
        ])
        .where('affiliateDiscount.affiliateId = :affiliateId', { affiliateId })
        .groupBy('discount.id, discount.coupon, discount.title')
        .orderBy('COUNT(usage.id)', 'DESC')
        .getRawMany();

      // Get recent usage activity
      const recentUsages = await this.affiliateDiscountUsageRepo.find({
        where: {
          affiliateDiscount: {
            affiliateId: affiliateId
          }
        },
        relations: ['affiliateDiscount', 'affiliateDiscount.discount', 'order'],
        order: { createdAt: 'DESC' },
        take: 10
      });

      // Get customer statistics
      const customerStats = await this.affiliateDiscountUsageRepo
        .createQueryBuilder('usage')
        .leftJoin('usage.affiliateDiscount', 'affiliateDiscount')
        .select([
          'usage.customer_email as customerEmail',
          'COUNT(usage.id) as orderCount',
          'SUM(usage.order_amount) as totalSpent',
          'SUM(usage.commission_amount) as totalCommissionGenerated',
          'MIN(usage.created_at) as firstOrder',
          'MAX(usage.created_at) as lastOrder'
        ])
        .where('affiliateDiscount.affiliateId = :affiliateId', { affiliateId })
        .groupBy('usage.customer_email')
        .orderBy('SUM(usage.commission_amount)', 'DESC')
        .limit(20)
        .getRawMany();

      return new ServiceResponse({
        affiliate: {
          id: affiliate.id,
          userId: affiliate.userId,
          commissionRate: affiliate.commissionRate,
          commissionType: affiliate.commissionType,
          status: affiliate.status,
          totalEarnings: affiliate.totalEarnings,
          totalOrders: affiliate.totalOrders,
          totalClicks: affiliate.totalClicks,
          conversionRate: affiliate.conversionRate
        },
        usageStatistics: {
          totalUsages: parseInt(usageStats.totalUsages) || 0,
          totalOrderValue: parseFloat(usageStats.totalOrderValue) || 0,
          totalDiscountAmount: parseFloat(usageStats.totalDiscountAmount) || 0,
          totalCommissionEarned: parseFloat(usageStats.totalCommissionEarned) || 0,
          averageOrderValue: parseFloat(usageStats.averageOrderValue) || 0,
          averageCommission: parseFloat(usageStats.averageCommission) || 0,
          averageCommissionRate: parseFloat(usageStats.averageCommissionRate) || 0,
          uniqueCustomers: parseInt(usageStats.uniqueCustomers) || 0,
          activeDays: parseInt(usageStats.activeDays) || 0,
          firstUsage: usageStats.firstUsage,
          lastUsage: usageStats.lastUsage
        },
        discountBreakdown: discountBreakdown.map(item => ({
          discountId: item.discountId,
          couponCode: item.couponCode,
          discountTitle: item.discountTitle,
          usageCount: parseInt(item.usageCount),
          totalOrderValue: parseFloat(item.totalOrderValue),
          totalCommission: parseFloat(item.totalCommission),
          averageCommissionRate: parseFloat(item.averageCommissionRate),
          firstUsed: item.firstUsed,
          lastUsed: item.lastUsed
        })),
        topCustomers: customerStats.map(customer => ({
          customerEmail: customer.customerEmail,
          orderCount: parseInt(customer.orderCount),
          totalSpent: parseFloat(customer.totalSpent),
          totalCommissionGenerated: parseFloat(customer.totalCommissionGenerated),
          firstOrder: customer.firstOrder,
          lastOrder: customer.lastOrder
        })),
        recentActivity: recentUsages.map(usage => ({
          id: usage.id,
          orderId: usage.orderId,
          customerEmail: usage.customerEmail,
          orderAmount: usage.orderAmount,
          discountAmount: usage.discountAmount,
          commissionAmount: usage.commissionAmount,
          commissionRate: usage.commissionRate,
          couponCode: usage.affiliateDiscount?.discount?.coupon,
          createdAt: usage.createdAt
        }))
      }, 'Affiliate usage statistics retrieved successfully');

    } catch (error) {
      this.logger.error(`Error getting affiliate usage statistics for affiliate ${affiliateId}:`, {
        error: error.message,
        stack: error.stack
      });

      return new ServiceResponse(null, `Error getting usage statistics: ${error.message}`);
    }
  }

  /**
   * Track order conversion for affiliate analytics
   */
  private async trackOrderConversion(
    orderId: number,
    calculationResult: any,
    em: EntityManager
  ): Promise<void> {
    try {
      // Get order details
      const order = await em.findOne(ProductOrder, {
        where: { id: orderId },
        relations: ['user', 'coupons']
      });

      if (!order) {
        throw new Error(`Order ${orderId} not found`);
      }

      // Validate calculation result
      if (!calculationResult.affiliateId) {
        throw new Error('calculationResult.affiliateId is required');
      }
      if (!calculationResult.discountId) {
        throw new Error('calculationResult.discountId is required');
      }

      this.logger.log(`📋 Tracking conversion for order ${orderId}:`, {
        affiliateId: calculationResult.affiliateId,
        discountId: calculationResult.discountId,
        commissionAmount: calculationResult.commissionAmount,
        orderAmount: order.amount
      });

      // Get affiliate discount information
      const affiliateDiscount = await em.findOne(AffiliateDiscount, {
        where: {
          affiliateId: calculationResult.affiliateId,
          discountId: calculationResult.discountId
        },
        relations: ['discount']
      });

      if (!affiliateDiscount) {
        throw new Error(`Affiliate discount not found for affiliateId: ${calculationResult.affiliateId}, discountId: ${calculationResult.discountId}`);
      }

      // Prepare conversion tracking data
      const conversionData = {
        orderId: orderId,
        affiliateCode: affiliateDiscount?.discount?.coupon || order.coupons?.coupon,
        sessionId: null, // Could be enhanced to track session from cart/order
        ipAddress: null, // Could be enhanced to store IP from order creation
        userAgent: null, // Could be enhanced to store user agent from order creation
        conversionPage: '/checkout/success', // Default conversion page
      };

      // Track the order conversion
      const conversionResult = await this.conversionTrackingService.trackOrderConversion(
        conversionData,
        em
      );

      if (conversionResult.data) {
        this.logger.log(`✅ Order conversion tracked: ${conversionResult.msg}`);
      } else {
        this.logger.warn(`⚠️ Order conversion tracking returned no data: ${conversionResult.msg}`);
      }

    } catch (error) {
      this.logger.error(`❌ Error in trackOrderConversion: ${error.message}`, error.stack);
      throw error;
    }
  }
}
