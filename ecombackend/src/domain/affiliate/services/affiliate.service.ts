import { Injectable, BadRequestException, NotFoundException, Logger } from '@nestjs/common';
import { InjectDataSource, InjectRepository } from '@nestjs/typeorm';
import { Repository, EntityManager, In, DataSource } from 'typeorm';
import { AffiliateProfile, AffiliateStatus } from '../entities/affiliate-profile.entity';
import { AffiliateDiscount, AffiliateDiscountStatus } from '../entities/affiliate-discount.entity';
import { AffiliateClick } from '../entities/affiliate-click.entity';
import { AffiliateConversion } from '../entities/affiliate-conversion.entity';
import { AffiliateCommission, CommissionStatus, CommissionSource } from '../entities/affiliate-commission.entity';
import { AffiliateDiscountUsage } from '../entities/affiliate-discount-usage.entity';
import { CreateAffiliateProfileDto } from '../dto/create-affiliate-profile.dto';
import { AssignDiscountToAffiliateDto } from '../dto/assign-discount.dto';
import { AssignAffiliateDiscountDto, UpdateAffiliateDiscountDto } from '../dto/assign-affiliate-discount.dto';
import { TrackClickDto } from '../dto/track-click.dto';
import { UTMTrackingService } from './utm-tracking.service';
import { ConversionTrackingService } from './conversion-tracking.service';
import { ProductOrder } from '../../entities/product-order.entity';
import { Discount } from '../../entities/discount.entity';
import { User } from '../../entities/user.entity';
import { ServiceResponse } from 'src/common/utils/service-response';
import { PageOptionsDto } from 'src/common/pagination/page-options.dto';
import { PageMetaDto } from 'src/common/pagination/page-meta.dto';
import { PageDto } from 'src/common/pagination/page.dto';
import { ApproveApplicationDto } from '../dto/approve-application.dto';

@Injectable()
export class AffiliateService {
  private readonly logger = new Logger(AffiliateService.name);
  constructor(
    @InjectDataSource() private readonly dataSource: DataSource,
    @InjectRepository(AffiliateProfile)
    private affiliateProfileRepo: Repository<AffiliateProfile>,

    @InjectRepository(AffiliateDiscount)
    private affiliateDiscountRepo: Repository<AffiliateDiscount>,

    @InjectRepository(AffiliateClick)
    private affiliateClickRepo: Repository<AffiliateClick>,

    @InjectRepository(AffiliateConversion)
    private affiliateConversionRepo: Repository<AffiliateConversion>,

    @InjectRepository(AffiliateCommission)
    private affiliateCommissionRepo: Repository<AffiliateCommission>,

    @InjectRepository(AffiliateDiscountUsage)
    private affiliateDiscountUsageRepo: Repository<AffiliateDiscountUsage>,

    @InjectRepository(Discount)
    private discountRepo: Repository<Discount>,

    @InjectRepository(User)
    private userRepo: Repository<User>,

    private utmTrackingService: UTMTrackingService,
    private conversionTrackingService: ConversionTrackingService,
  ) { }

  /**
   * Generate a unique affiliate code
   */
  private async generateUniqueAffiliateCode(userId: number, maxAttempts: number = 10): Promise<string> {
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      // Generate code based on user ID and random string
      const randomString = Math.random().toString(36).substring(2, 8).toUpperCase();
      const affiliateCode = `AFF${userId}${randomString}`;

      // Check if code already exists
      const existingAffiliate = await this.affiliateProfileRepo.findOne({
        where: { affiliateCode }
      });

      if (!existingAffiliate) {
        return affiliateCode;
      }

      this.logger.warn(`Affiliate code ${affiliateCode} already exists, attempt ${attempt}/${maxAttempts}`);
    }

    // Fallback: use timestamp if all attempts failed
    const timestamp = Date.now().toString().slice(-6);
    const fallbackCode = `AFF${userId}${timestamp}`;

    this.logger.warn(`Using fallback affiliate code: ${fallbackCode}`);
    return fallbackCode;
  }

  /**
   * Get affiliate by affiliate code
   */
  async getAffiliateByCode(affiliateCode: string): Promise<ServiceResponse> {
    try {
      const affiliate = await this.affiliateProfileRepo.findOne({
        where: { affiliateCode },
        relations: [
          'user',
          'country',
          'affiliateDiscounts',
          'affiliateDiscounts.discount',
          'affiliateDiscounts.discount.countries'
        ]
      });

      if (!affiliate) {
        throw new NotFoundException('Affiliate not found');
      }

      return new ServiceResponse(affiliate, 'Affiliate retrieved successfully');
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(`Error retrieving affiliate: ${error.message}`);
    }
  }

  /**
   * Check if affiliate code is available
   */
  async checkAffiliateCodeAvailability(affiliateCode: string): Promise<ServiceResponse> {
    try {
      const existingAffiliate = await this.affiliateProfileRepo.findOne({
        where: { affiliateCode }
      });

      const isAvailable = !existingAffiliate;

      return new ServiceResponse(
        {
          affiliateCode,
          isAvailable,
          message: isAvailable ? 'Affiliate code is available' : 'Affiliate code is already taken'
        },
        'Code availability checked successfully'
      );
    } catch (error) {
      throw new BadRequestException(`Error checking affiliate code availability: ${error.message}`);
    }
  }

  /**
   * Get all affiliates with filtering and pagination
   */
  async getAllAffiliates(
    filters?: {
      status?: string;
      search?: string;
      countryId?: number;
      commissionType?: string;
    },
    pageOptionsDto?: PageOptionsDto
  ): Promise<ServiceResponse> {
    try {
      const queryBuilder = this.affiliateProfileRepo
        .createQueryBuilder('affiliate')
        .leftJoin('affiliate.user', 'user')
        .addSelect(['user.id', 'user.firstName', 'user.lastName', 'user.email', 'user.phone'])
        .leftJoin('affiliate.country', 'country')
        .addSelect(['country.id', 'country.name', 'country.code', 'country.currencyId', 'country.currency'])
        .leftJoin('country.currency', 'currency')
        .addSelect(['currency.id', 'currency.currency', 'currency.currency_symbol'])
        .leftJoin('affiliate.affiliateDiscounts', 'affiliateDiscount')
        .addSelect(['affiliateDiscount.id', 'affiliateDiscount.status'])
        .leftJoin('affiliateDiscount.discount', 'discount')
        .addSelect(['discount.id', 'discount.title', 'discount.coupon', 'discount.discount_type', 'discount.amount_type', 'discount.is_active', 'discount.is_automatic'])

      // Apply filters
      if (filters?.status) {
        queryBuilder.andWhere('affiliate.status = :status', { status: filters.status });
      }

      if (filters?.countryId) {
        queryBuilder.andWhere('affiliate.countryId = :countryId', { countryId: filters.countryId });
      }

      if (filters?.commissionType) {
        queryBuilder.andWhere('affiliate.commissionType = :commissionType', { commissionType: filters.commissionType });
      }

      if (filters?.search) {
        queryBuilder.andWhere(
          '(user.firstName LIKE :search OR user.lastName LIKE :search OR user.email LIKE :search OR discount.coupon LIKE :search OR country.name LIKE :search)',
          { search: `%${filters.search}%` }
        );
      }

      // Apply pagination
      const page = pageOptionsDto?.page || 1;
      const limit = pageOptionsDto?.take || 10;
      const skip = (page - 1) * limit;

      queryBuilder.skip(skip).take(limit);
      queryBuilder.orderBy('affiliate.createdAt', 'DESC');

      const [affiliates, total] = await queryBuilder.getManyAndCount();

      const pageMetaDto = new PageMetaDto({ itemCount: total, pageOptionsDto });

      const result = new PageDto(affiliates, pageMetaDto);

      return new ServiceResponse(
        result.itemList,
        'All affiliates found successfully',
        result.meta,
      );
    } catch (error) {
      throw new BadRequestException(`Error retrieving affiliates: ${error.message}`);
    }
  }

  async getAffiliateByUserId(userId: number): Promise<ServiceResponse> {
    try {
      // Get affiliate with essential relations only (optimized)
      const affiliate = await this.affiliateProfileRepo.findOne({
        where: { userId },
        relations: [
          'user',
          'country',
          'country.currency',
          'affiliateDiscounts',
          'affiliateDiscounts.discount',
          'affiliateDiscounts.discount.countries'
        ]
      });

      if (!affiliate) {
        console.log(`❌ [getAffiliateById] Affiliate not found for User ID: ${userId}`);
        throw new NotFoundException('Affiliate not found');
      }

      return new ServiceResponse(affiliate, 'Affiliate retrieved successfully');
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(`Error retrieving affiliate: ${error.message}`);
    }
  }

  /**
   * Get affiliate by ID with comprehensive relations and optimized statistics
   */
  async getAffiliateById(id: number): Promise<ServiceResponse> {
    try {
      console.log(`🚀 [getAffiliateById] Starting comprehensive affiliate retrieval for ID: ${id}`);

      // Get affiliate with essential relations only (optimized)
      const affiliate = await this.affiliateProfileRepo.findOne({
        where: { id },
        relations: [
          'user',
          'country',
          'country.currency',
          'affiliateDiscounts',
          'affiliateDiscounts.discount',
          'affiliateDiscounts.discount.countries'
        ]
      });

      if (!affiliate) {
        console.log(`❌ [getAffiliateById] Affiliate not found for ID: ${id}`);
        throw new NotFoundException('Affiliate not found');
      }

      console.log(`✅ [getAffiliateById] Affiliate found: ${affiliate.user?.email || 'No email'}, Status: ${affiliate.status}`);
      console.log(`📊 [getAffiliateById] Affiliate has ${affiliate.affiliateDiscounts?.length || 0} discounts`);

      // Log discount details
      if (affiliate.affiliateDiscounts && affiliate.affiliateDiscounts.length > 0) {
        affiliate.affiliateDiscounts.forEach((discount, index) => {
          console.log(`  📋 Discount ${index + 1}: ID=${discount.id}, Status=${discount.status}, Coupon=${discount.discount?.coupon || 'N/A'}`);
        });
      }

      // Execute all statistics queries in parallel for optimal performance
      console.log(`⚡ [getAffiliateById] Starting parallel statistics calculation for affiliate ${id}`);
      const startTime = Date.now();

      const [
        usageStatistics,
        commissionStatistics,
        clickStatistics,
        conversionStatistics,
        discountBreakdown,
        recentActivity,
        timeBasedStats,
        topCustomers
      ] = await Promise.all([
        this.calculateUsageStatistics(id),
        this.calculateCommissionStatistics(id),
        this.calculateClickStatistics(id),
        this.calculateConversionStatistics(id),
        this.calculateDiscountBreakdown(id),
        this.getRecentActivityData(id),
        this.calculateTimeBasedStatistics(id),
        this.getTopCustomersData(id)
      ]);

      const executionTime = Date.now() - startTime;
      console.log(`⚡ [getAffiliateById] Parallel statistics calculation completed in ${executionTime}ms`);

      // Log summary of statistics results
      console.log(`📊 [getAffiliateById] Statistics Summary for affiliate ${id}:`);
      console.log(`  - Usage: ${usageStatistics.totalUsages} usages, $${usageStatistics.totalOrderValue} revenue`);
      console.log(`  - Commissions: ${commissionStatistics.totalCommissions} commissions, $${commissionStatistics.totalEarnings} earnings`);
      console.log(`  - Clicks: ${clickStatistics.totalClicks} clicks, ${clickStatistics.uniqueVisitors} unique visitors`);
      console.log(`  - Conversions: ${conversionStatistics.totalConversions} conversions`);
      console.log(`  - Discount Performance: ${discountBreakdown.length} discount campaigns`);
      console.log(`  - Recent Activity: ${recentActivity.recentUsages?.length || 0} recent usages`);
      console.log(`  - Time Analytics: ${timeBasedStats.dailyStats?.length || 0} daily stats, ${timeBasedStats.monthlyStats?.length || 0} monthly stats`);
      console.log(`  - Top Customers: ${topCustomers.length} customers`);

      if (usageStatistics.totalUsages === 0 && commissionStatistics.totalCommissions === 0) {
        console.log(`⚠️ [getAffiliateById] WARNING: No usage or commission data found for affiliate ${id}`);
      }

      // Calculate comprehensive performance metrics
      const performanceMetrics = this.calculatePerformanceMetrics({
        usageStatistics,
        commissionStatistics,
        clickStatistics,
        conversionStatistics
      });

      // Build comprehensive affiliate response
      const optimizedAffiliateData = {
        // Basic affiliate information
        profile: {
          id: affiliate.id,
          userId: affiliate.userId,
          status: affiliate.status,
          commissionType: affiliate.commissionType,
          commissionRate: affiliate.commissionRate,
          paymentMethod: affiliate.paymentMethod,
          bio: affiliate.bio,
          createdAt: affiliate.createdAt,
          updatedAt: affiliate.updatedAt
        },

        // User information
        user: affiliate.user ? {
          id: affiliate.user.id,
          email: affiliate.user.email,
          firstName: affiliate.user.firstName,
          lastName: affiliate.user.lastName,
          phone: affiliate.user.phone
        } : null,

        // Country and currency information
        country: affiliate.country ? {
          id: affiliate.country.id,
          name: affiliate.country.name,
          code: affiliate.country.code,
          currency: affiliate.country.currency ? {
            currency: affiliate.country.currency.currency,
            currencySymbol: affiliate.country.currency.currencySymbol
          } : null
        } : null,

        // Affiliate discounts with enhanced data
        discounts: affiliate.affiliateDiscounts?.map(discount => ({
          id: discount.id,
          status: discount.status,
          usageCount: discount.usageCount || 0,
          totalCommissionEarned: discount.totalCommissionEarned || 0,
          maxUsage: discount.maxUsage,
          startDate: discount.startDate,
          endDate: discount.endDate,
          commissionType: discount.commissionType,
          commissionRate: discount.commissionRate,
          discount: discount.discount ? {
            id: discount.discount.id,
            title: discount.discount.title,
            coupon: discount.discount.coupon,
            discountType: discount.discount.discount_type,
            amountType: discount.discount.amount_type,
            isActive: discount.discount.is_active,
            isAutomatic: discount.discount.is_automatic,
            maxUse: discount.discount.max_use,
            usedCount: discount.discount.used_count,
            countries: discount.discount.countries?.map(country => ({
              countryId: country.countryId,
              amount: country.amount,
              minAmount: country.min_amount,
              minQuantity: country.min_quantity
            })) || []
          } : null
        })) || [],

        // Comprehensive statistics
        statistics: {
          // Core metrics
          overview: {
            totalUsages: usageStatistics.totalUsages || 0,
            totalOrderValue: usageStatistics.totalOrderValue || 0,
            totalCommissionEarned: usageStatistics.totalCommissionEarned || 0,
            uniqueCustomers: usageStatistics.uniqueCustomers || 0,
            totalClicks: Number(clickStatistics.totalClicks) || 0,
            totalConversions: Number(conversionStatistics.totalConversions) || 0,
            activeDays: usageStatistics.activeDays || 0
          },

          // Performance metrics
          performance: performanceMetrics,

          // Commission breakdown
          commissions: {
            total: commissionStatistics.totalCommissions || 0,
            totalEarnings: commissionStatistics.totalEarnings || 0,
            pendingEarnings: commissionStatistics.pendingEarnings || 0,
            approvedEarnings: commissionStatistics.approvedEarnings || 0,
            paidEarnings: commissionStatistics.paidEarnings || 0,
            firstCommissionDate: commissionStatistics.firstCommissionDate,
            lastCommissionDate: commissionStatistics.lastCommissionDate
          },

          // Time-based analytics
          timeAnalytics: timeBasedStats,

          // Discount performance breakdown
          discountPerformance: discountBreakdown
        },

        // Recent activity and top performers
        analytics: {
          recentActivity,
          topCustomers
        }
      };

      return new ServiceResponse(optimizedAffiliateData, 'Affiliate retrieved successfully with comprehensive statistics');
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(`Error retrieving affiliate: ${error.message}`);
    }
  }

  /**
   * Calculate usage statistics for an affiliate
   */
  private async calculateUsageStatistics(affiliateId: number) {
    try {
      console.log(`🔍 [calculateUsageStatistics] Starting calculation for affiliate ID: ${affiliateId}`);

      // First, let's check if the affiliate exists
      const affiliateExists = await this.affiliateProfileRepo.findOne({
        where: { id: affiliateId }
      });

      if (!affiliateExists) {
        console.log(`❌ [calculateUsageStatistics] Affiliate ${affiliateId} not found`);
        return this.getEmptyUsageStats();
      }

      console.log(`✅ [calculateUsageStatistics] Affiliate ${affiliateId} exists: ${affiliateExists.user?.email || 'No email'}`);

      // Check if affiliate has any discounts
      const affiliateDiscounts = await this.affiliateDiscountRepo.find({
        where: { affiliateId: affiliateId },
        relations: ['discount']
      });

      console.log(`📊 [calculateUsageStatistics] Found ${affiliateDiscounts.length} affiliate discounts for affiliate ${affiliateId}`);

      if (affiliateDiscounts.length === 0) {
        console.log(`⚠️ [calculateUsageStatistics] No affiliate discounts found for affiliate ${affiliateId}`);
        return this.getEmptyUsageStats();
      }

      // Log discount details
      affiliateDiscounts.forEach((ad, index) => {
        console.log(`  📋 Discount ${index + 1}: ID=${ad.id}, Status=${ad.status}, DiscountID=${ad.discountId}, Coupon=${ad.discount?.coupon || 'N/A'}`);
      });

      // Check if there are any usage records at all
      const totalUsageCount = await this.affiliateDiscountUsageRepo.count();
      console.log(`📈 [calculateUsageStatistics] Total usage records in database: ${totalUsageCount}`);

      // Check usage records for this affiliate's discounts
      const affiliateDiscountIds = affiliateDiscounts.map(ad => ad.id);
      console.log('affiliateDiscountIds:::', affiliateDiscountIds);

      let usageCountForAffiliate = 0;

      if (affiliateDiscountIds.length > 0) {
        usageCountForAffiliate = await this.affiliateDiscountUsageRepo
          .createQueryBuilder('usage')
          .where('usage.affiliateDiscountId IN (:...ids)', { ids: affiliateDiscountIds })
          .getCount();
      }

      console.log(`📊 [calculateUsageStatistics] Usage records for affiliate ${affiliateId}: ${usageCountForAffiliate}`);

      if (usageCountForAffiliate === 0) {
        console.log(`⚠️ [calculateUsageStatistics] No usage records found for affiliate ${affiliateId}`);
        return this.getEmptyUsageStats();
      }

      // Build and log the query
      const queryBuilder = this.affiliateDiscountUsageRepo
        .createQueryBuilder('usage')
        .leftJoin('usage.affiliateDiscount', 'affiliateDiscount')
        .select([
          'COUNT(usage.id) as "totalUsages"',
          'SUM(usage.order_amount) as "totalOrderValue"',
          'SUM(usage.discount_amount) as "totalDiscountAmount"',
          'SUM(usage.commission_amount) as "totalCommissionEarned"',
          'AVG(usage.order_amount) as "averageOrderValue"',
          'AVG(usage.commission_amount) as "averageCommission"',
          'AVG(usage.commission_rate) as "averageCommissionRate"',
          'COUNT(DISTINCT usage.customer_email) as "uniqueCustomers"',
          'COUNT(DISTINCT DATE(usage.created_at)) as "activeDays"',
          'MIN(usage.created_at) as "firstUsage"',
          'MAX(usage.created_at) as "lastUsage"'
        ])
        .where('affiliateDiscount.affiliateId = :affiliateId', { affiliateId });

      // Log the generated SQL
      const sql = queryBuilder.getSql();
      console.log(`🔍 [calculateUsageStatistics] Generated SQL: ${sql}`);
      console.log(`🔍 [calculateUsageStatistics] Parameters: affiliateId=${affiliateId}`);

      // Execute the query
      const result = await queryBuilder.getRawOne();

      console.log(`📊 [calculateUsageStatistics] Raw query result:`, result);

      // Validate and log the result
      if (!result) {
        console.log(`❌ [calculateUsageStatistics] Query returned null/undefined result`);
        return this.getEmptyUsageStats();
      }

      const processedResult = {
        totalUsages: parseInt(result.totalUsages) || 0,
        totalOrderValue: parseFloat(result.totalOrderValue) || 0,
        totalDiscountAmount: parseFloat(result.totalDiscountAmount) || 0,
        totalCommissionEarned: parseFloat(result.totalCommissionEarned) || 0,
        averageOrderValue: parseFloat(result.averageOrderValue) || 0,
        averageCommission: parseFloat(result.averageCommission) || 0,
        averageCommissionRate: parseFloat(result.averageCommissionRate) || 0,
        uniqueCustomers: parseInt(result.uniqueCustomers) || 0,
        activeDays: parseInt(result.activeDays) || 0,
        firstUsage: result.firstUsage,
        lastUsage: result.lastUsage
      };

      console.log(`✅ [calculateUsageStatistics] Processed result for affiliate ${affiliateId}:`, processedResult);

      return processedResult;

    } catch (error) {
      console.error(`❌ [calculateUsageStatistics] Error for affiliate ${affiliateId}:`, {
        error: error.message,
        stack: error.stack
      });

      return this.getEmptyUsageStats();
    }
  }

  /**
   * Get empty usage statistics structure
   */
  private getEmptyUsageStats() {
    return {
      totalUsages: 0,
      totalOrderValue: 0,
      totalDiscountAmount: 0,
      totalCommissionEarned: 0,
      averageOrderValue: 0,
      averageCommission: 0,
      averageCommissionRate: 0,
      uniqueCustomers: 0,
      activeDays: 0,
      firstUsage: null,
      lastUsage: null
    };
  }

  /**
   * Calculate commission statistics for an affiliate
   */
  private async calculateCommissionStatistics(affiliateId: number) {
    console.log('--------------------------------------------------------');

    try {
      console.log(`🔍 [calculateCommissionStatistics] Starting calculation for affiliate ID: ${affiliateId}`);

      // Check if there are any commission records at all
      const totalCommissionCount = await this.affiliateCommissionRepo.count();
      console.log(`📈 [calculateCommissionStatistics] Total commission records in database: ${totalCommissionCount}`);

      // Check commission records for this affiliate (both old and new structure)
      const directCommissionCount = await this.affiliateCommissionRepo.count({
        where: { affiliateId: affiliateId }
      });
      console.log(`📊 [calculateCommissionStatistics] Direct commission records for affiliate ${affiliateId}: ${directCommissionCount}`);

      // // Check commission records through affiliate discounts (new structure)
      // const throughDiscountCount1 = await this.affiliateCommissionRepo
      //   .createQueryBuilder('commission')
      //   .leftJoin('commission.affiliateDiscount', 'affiliateDiscount')
      //   .where('affiliateDiscount.affiliateId = :affiliateId', { affiliateId })
      //   .getMany();

      // console.log('throughDiscountCount1:::', throughDiscountCount1);


      const throughDiscountCount = await this.affiliateCommissionRepo
        .createQueryBuilder('commission')
        .leftJoin('commission.affiliateDiscount', 'affiliateDiscount')
        .where('affiliateDiscount.affiliateId = :affiliateId', { affiliateId })
        .getCount();

      console.log(`📊 [calculateCommissionStatistics] Commission records through discounts for affiliate ${affiliateId}: ${throughDiscountCount}`);

      if (directCommissionCount === 0 && throughDiscountCount === 0) {
        console.log(`⚠️ [calculateCommissionStatistics] No commission records found for affiliate ${affiliateId}`);
        return this.getEmptyCommissionStats();
      }

      // Build query - try both old and new structure
      let queryBuilder = this.affiliateCommissionRepo.createQueryBuilder('commission');

      if (throughDiscountCount > 0) {
        // Use new structure (through affiliate discounts)
        console.log(`🔄 [calculateCommissionStatistics] Using new structure (through affiliate discounts)`);
        queryBuilder = queryBuilder
          .leftJoin('commission.affiliateDiscount', 'affiliateDiscount')
          .where('affiliateDiscount.affiliateId = :affiliateId', { affiliateId });
      } else {
        // Use old structure (direct affiliate relationship)
        console.log(`🔄 [calculateCommissionStatistics] Using old structure (direct affiliate relationship)`);
        queryBuilder = queryBuilder
          .where('commission.affiliateId = :affiliateId', { affiliateId });
      }

      queryBuilder.select([
        'COUNT(*) as "totalCommissions"',
        'SUM(CASE WHEN commission.status = :pending THEN commission.commission_amount ELSE 0 END) as "pendingEarnings"',
        'SUM(CASE WHEN commission.status = :approved THEN commission.commission_amount ELSE 0 END) as "approvedEarnings"',
        'SUM(CASE WHEN commission.status = :paid THEN commission.commission_amount ELSE 0 END) as "paidEarnings"',
        'SUM(commission.commission_amount) as "totalEarnings"',
        'AVG(commission.commission_amount) as "averageCommission"',
        'MIN(commission.created_at) as "firstCommissionDate"',
        'MAX(commission.created_at) as "lastCommissionDate"'
      ])
        .setParameters({
          pending: 'pending',
          approved: 'approved',
          paid: 'paid',
        });

      // Log the generated SQL
      const sql = queryBuilder.getSql();
      console.log(`🔍 [calculateCommissionStatistics] Generated SQL: ${sql}`);

      const result = await queryBuilder.getRawOne();
      console.log(`📊 [calculateCommissionStatistics] Raw query result:`, result);

      if (!result) {
        console.log(`❌ [calculateCommissionStatistics] Query returned null/undefined result`);
        return this.getEmptyCommissionStats();
      }

      const processedResult = {
        totalCommissions: parseInt(result.totalCommissions) || 0,
        pendingEarnings: parseFloat(result.pendingEarnings) || 0,
        approvedEarnings: parseFloat(result.approvedEarnings) || 0,
        paidEarnings: parseFloat(result.paidEarnings) || 0,
        totalEarnings: parseFloat(result.totalEarnings) || 0,
        averageCommission: parseFloat(result.averageCommission) || 0,
        firstCommissionDate: result.firstCommissionDate,
        lastCommissionDate: result.lastCommissionDate
      };

      console.log(`✅ [calculateCommissionStatistics] Processed result for affiliate ${affiliateId}:`, processedResult);
      console.log('--------------------------------------------------------');
      return processedResult;

    } catch (error) {
      console.error(`❌ [calculateCommissionStatistics] Error for affiliate ${affiliateId}:`, {
        error: error.message,
        stack: error.stack
      });

      return this.getEmptyCommissionStats();
    }
  }

  /**
   * Get empty commission statistics structure
   */
  private getEmptyCommissionStats() {
    return {
      totalCommissions: 0,
      pendingEarnings: 0,
      approvedEarnings: 0,
      paidEarnings: 0,
      totalEarnings: 0,
      averageCommission: 0,
      firstCommissionDate: null,
      lastCommissionDate: null
    };
  }

  /**
   * Calculate click statistics for an affiliate
   */
  private async calculateClickStatistics(affiliateId: number) {
    return await this.affiliateClickRepo
      .createQueryBuilder('click')
      .leftJoin('click.affiliateDiscount', 'affiliateDiscount')
      .select([
        'COUNT(*) as "totalClicks"',
        'COUNT(DISTINCT click.ip_address) as "uniqueVisitors"',
        'COUNT(DISTINCT DATE(click.created_at)) as "activeDays"',
        'MIN(click.created_at) as "firstClickDate"',
        'MAX(click.created_at) as "lastClickDate"'
      ])
      .where('(affiliateDiscount.affiliateId = :affiliateId OR (click.affiliateId = :affiliateId AND click.affiliateDiscountId IS NULL))', { affiliateId })
      .getRawOne();
  }

  /**
   * Calculate conversion statistics for an affiliate
   */
  private async calculateConversionStatistics(affiliateId: number) {
    return await this.affiliateConversionRepo
      .createQueryBuilder('conversion')
      .leftJoin('conversion.affiliateDiscount', 'affiliateDiscount')
      .select([
        'COUNT(*) as "totalConversions"',
        'SUM(conversion.conversion_value) as "totalConversionValue"',
        'AVG(conversion.conversion_value) as "averageConversionValue"',
        'MIN(conversion.created_at) as "firstConversionDate"',
        'MAX(conversion.created_at) as "lastConversionDate"'
      ])
      .where('affiliateDiscount.affiliateId = :affiliateId', { affiliateId })
      .getRawOne();
  }

  /**
   * Calculate discount breakdown performance
   */
  private async calculateDiscountBreakdown(affiliateId: number) {
    return await this.affiliateDiscountUsageRepo
      .createQueryBuilder('usage')
      .leftJoin('usage.affiliateDiscount', 'affiliateDiscount')
      .leftJoin('affiliateDiscount.discount', 'discount')
      .select([
        'discount.id as "discountId"',
        'discount.coupon as couponCode',
        'discount.title as "discountTitle"',
        // 'COUNT(usage.id) as "usageCount"',
        'SUM(usage.order_amount) as "totalOrderValue"',
        'SUM(usage.commission_amount) as "totalCommission"',
        'AVG(usage.commission_rate) as "averageCommissionRate"',
        'COUNT(DISTINCT usage.customer_email) as "uniqueCustomers"',
        'MIN(usage.created_at) as "firstUsed"',
        'MAX(usage.created_at) as "lastUsed"'
      ])
      .where('affiliateDiscount.affiliateId = :affiliateId', { affiliateId })
      .groupBy('discount.id, discount.coupon, discount.title')
      // .orderBy('usageCount', 'DESC')
      .getRawMany();
  }

  /**
   * Get recent activity data
   */
  private async getRecentActivityData(affiliateId: number) {
    const [recentUsages, recentCommissions, recentClicks] = await Promise.all([
      // Recent usages
      this.affiliateDiscountUsageRepo.find({
        where: {
          affiliateDiscount: {
            affiliateId: affiliateId
          }
        },
        relations: ['affiliateDiscount', 'affiliateDiscount.discount'],
        order: { createdAt: 'DESC' },
        take: 10
      }),

      // Recent commissions
      this.affiliateCommissionRepo
        .createQueryBuilder('commission')
        .leftJoin('commission.affiliateDiscount', 'affiliateDiscount')
        .leftJoinAndSelect('commission.order', 'order')
        .where('affiliateDiscount.affiliateId = :affiliateId', { affiliateId })
        .orderBy('commission.createdAt', 'DESC')
        .limit(10)
        .getMany(),

      // Recent clicks
      this.affiliateClickRepo
        .createQueryBuilder('click')
        .leftJoin('click.affiliateDiscount', 'affiliateDiscount')
        .leftJoinAndSelect('click.country', 'country')
        .where('(affiliateDiscount.affiliateId = :affiliateId OR (click.affiliateId = :affiliateId AND click.affiliateDiscountId IS NULL))', { affiliateId })
        .orderBy('click.createdAt', 'DESC')
        .limit(10)
        .getMany()
    ]);

    return {
      recentUsages: recentUsages.map(usage => ({
        id: usage.id,
        orderId: usage.orderId,
        customerEmail: usage.customerEmail,
        orderAmount: usage.orderAmount,
        discountAmount: usage.discountAmount,
        commissionAmount: usage.commissionAmount,
        commissionRate: usage.commissionRate,
        couponCode: usage.affiliateDiscount?.discount?.coupon,
        createdAt: usage.createdAt
      })),
      recentCommissions: recentCommissions.map(commission => ({
        id: commission.id,
        // orderId: commission.orderId,
        invoiceNo: commission.invoiceNo,
        commissionAmount: commission.commissionAmount,
        currencyCode: commission.currencyCode,
        status: commission.status,
        orderAmount: commission.orderAmount,
        couponCode: commission.couponCode,
        createdAt: commission.createdAt
      })),
      recentClicks: recentClicks.map(click => ({
        id: click.id,
        ipAddress: click.ipAddress,
        userAgent: click.userAgent,
        referrerUrl: click.referrerUrl,
        utmSource: click.utmSource,
        utmMedium: click.utmMedium,
        utmCampaign: click.utmCampaign,
        countryName: click.country?.name,
        createdAt: click.createdAt
      }))
    };
  }

  /**
   * Calculate time-based statistics
   */
  private async calculateTimeBasedStatistics(affiliateId: number) {
    try {
      const [dailyStats, monthlyStats] = await Promise.all([
        // Daily statistics for last 30 days - simplified query
        this.affiliateDiscountUsageRepo
          .createQueryBuilder('usage')
          .leftJoinAndSelect('usage.affiliateDiscount', 'affiliateDiscount')
          // .select([
          //   'usage.created_at',
          //   'usage.order_amount',
          //   'usage.commission_amount',
          //   'usage.customer_email'
          // ])
          .where('affiliateDiscount.affiliateId = :affiliateId', { affiliateId })
          .andWhere('usage.created_at >= :thirtyDaysAgo', {
            thirtyDaysAgo: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
          })
          .orderBy('usage.created_at', 'DESC')
          .getMany(),

        // Monthly statistics for last 12 months - simplified query
        this.affiliateDiscountUsageRepo
          .createQueryBuilder('usage')
          .leftJoinAndSelect('usage.affiliateDiscount', 'affiliateDiscount')
          // .select([
          //   'usage.created_at',
          //   'usage.order_amount',
          //   'usage.commission_amount',
          //   'usage.customer_email'
          // ])
          .where('affiliateDiscount.affiliateId = :affiliateId', { affiliateId })
          .andWhere('usage.created_at >= :twelveMonthsAgo', {
            twelveMonthsAgo: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000)
          })
          .orderBy('usage.created_at', 'DESC')
          .getMany()
      ]);

      console.log('dailyStats:::', dailyStats?.length);
      console.log('monthlyStats:::', monthlyStats?.length);
      // Process daily stats in JavaScript to avoid SQL syntax issues
      const dailyStatsMap = new Map();
      dailyStats.forEach(usage => {
        const date = usage.createdAt.toISOString().split('T')[0];
        if (!dailyStatsMap.has(date)) {
          dailyStatsMap.set(date, {
            date,
            usages: 0,
            revenue: 0,
            commission: 0,
            uniqueCustomers: new Set()
          });
        }
        const dayData = dailyStatsMap.get(date);
        dayData.usages++;
        dayData.revenue += Number(usage.orderAmount);
        dayData.commission += Number(usage.commissionAmount);
        dayData.uniqueCustomers.add(usage.customerEmail);
      });

      // Process monthly stats in JavaScript
      const monthlyStatsMap = new Map();
      monthlyStats.forEach(usage => {
        const date = new Date(usage.createdAt);
        const key = `${date.getFullYear()}-${date.getMonth() + 1}`;
        if (!monthlyStatsMap.has(key)) {
          monthlyStatsMap.set(key, {
            year: date.getFullYear(),
            month: date.getMonth() + 1,
            usages: 0,
            revenue: 0,
            commission: 0,
            uniqueCustomers: new Set()
          });
        }
        const monthData = monthlyStatsMap.get(key);
        monthData.usages++;
        monthData.revenue += Number(usage.orderAmount);
        monthData.commission += Number(usage.commissionAmount);
        monthData.uniqueCustomers.add(usage.customerEmail);
      });

      return {
        dailyStats: Array.from(dailyStatsMap.values()).map(stat => ({
          date: stat.date,
          usages: stat.usages,
          revenue: Number(stat.revenue.toFixed(2)),
          commission: Number(stat.commission.toFixed(2)),
          uniqueCustomers: stat.uniqueCustomers.size
        })).sort((a, b) => b.date.localeCompare(a.date)),

        monthlyStats: Array.from(monthlyStatsMap.values()).map(stat => ({
          year: stat.year,
          month: stat.month,
          usages: stat.usages,
          revenue: Number(stat.revenue.toFixed(2)),
          commission: Number(stat.commission.toFixed(2)),
          uniqueCustomers: stat.uniqueCustomers.size
        })).sort((a, b) => {
          if (a.year !== b.year) return b.year - a.year;
          return b.month - a.month;
        })
      };
    } catch (error) {
      console.error('Error calculating time-based statistics:', error);
      return {
        dailyStats: [],
        monthlyStats: []
      };
    }
  }

  /**
   * Get top customers data
   */
  private async getTopCustomersData(affiliateId: number) {
    return await this.affiliateDiscountUsageRepo
      .createQueryBuilder('usage')
      .leftJoin('usage.affiliateDiscount', 'affiliateDiscount')
      .select([
        'usage.customer_email as "customerEmail"',
        'COUNT(usage.id) as "orderCount"',
        'SUM(usage.order_amount) as "totalSpent"',
        'SUM(usage.commission_amount) as "totalCommissionGenerated"',
        'AVG(usage.order_amount) as "averageOrderValue"',
        'MIN(usage.created_at) as "firstOrder"',
        'MAX(usage.created_at) as "lastOrder"'
      ])
      .where('affiliateDiscount.affiliateId = :affiliateId', { affiliateId })
      .groupBy('usage.customer_email')
      .orderBy('"totalCommissionGenerated"', 'DESC')
      .limit(20)
      .getRawMany()
      .then(customers => customers.map(customer => ({
        customerEmail: customer.customerEmail,
        orderCount: parseInt(customer.orderCount),
        totalSpent: parseFloat(customer.totalSpent),
        totalCommissionGenerated: parseFloat(customer.totalCommissionGenerated),
        averageOrderValue: parseFloat(customer.averageOrderValue),
        firstOrder: customer.firstOrder,
        lastOrder: customer.lastOrder
      })));
  }

  /**
   * Calculate performance metrics
   */
  private calculatePerformanceMetrics(stats: any) {
    const totalUsages = parseInt(stats.usageStatistics.totalUsages) || 0;
    const totalOrderValue = parseFloat(stats.usageStatistics.totalOrderValue) || 0;
    const totalCommissionEarned = parseFloat(stats.usageStatistics.totalCommissionEarned) || 0;
    const uniqueCustomers = parseInt(stats.usageStatistics.uniqueCustomers) || 0;
    const totalClicks = parseInt(stats.clickStatistics.totalClicks) || 0;
    const totalConversions = parseInt(stats.conversionStatistics.totalConversions) || 0;

    return {
      conversionRate: totalClicks > 0 ? Number(((totalUsages / totalClicks) * 100).toFixed(2)) : 0,
      clickToConversionRate: totalClicks > 0 ? Number(((totalConversions / totalClicks) * 100).toFixed(2)) : 0,
      customerAcquisitionCost: uniqueCustomers > 0 ? Number((totalCommissionEarned / uniqueCustomers).toFixed(2)) : 0,
      revenuePerClick: totalClicks > 0 ? Number((totalOrderValue / totalClicks).toFixed(2)) : 0,
      commissionPerClick: totalClicks > 0 ? Number((totalCommissionEarned / totalClicks).toFixed(2)) : 0,
      averageOrderValue: totalUsages > 0 ? Number((totalOrderValue / totalUsages).toFixed(2)) : 0,
      averageCommission: totalUsages > 0 ? Number((totalCommissionEarned / totalUsages).toFixed(2)) : 0,
      returnOnAdSpend: totalCommissionEarned > 0 ? Number((totalOrderValue / totalCommissionEarned).toFixed(2)) : 0,
      customerLifetimeValue: uniqueCustomers > 0 ? Number((totalOrderValue / uniqueCustomers).toFixed(2)) : 0,
      repeatCustomerRate: totalUsages > 0 && uniqueCustomers > 0 ?
        Number((((totalUsages - uniqueCustomers) / totalUsages) * 100).toFixed(2)) : 0
    };
  }

  /**
   * Get affiliate by ID with basic relations (lightweight version)
   */
  async getAffiliateByIdBasic(id: number): Promise<ServiceResponse> {
    try {
      const affiliate = await this.affiliateProfileRepo.findOne({
        where: { id },
        relations: [
          'user',
          'country',
          'country.currency',
          'affiliateDiscounts',
          'affiliateDiscounts.discount'
        ]
      });

      if (!affiliate) {
        throw new NotFoundException('Affiliate not found');
      }

      return new ServiceResponse(affiliate, 'Affiliate retrieved successfully');
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(`Error retrieving affiliate: ${error.message}`);
    }
  }

  /**
   * Get statistics for a specific affiliate discount
   */
  async getAffiliateDiscountStatistics(affiliateId: number, affiliateDiscountId: number): Promise<ServiceResponse> {
    try {
      // Verify the affiliate discount belongs to the affiliate
      const affiliateDiscount = await this.affiliateDiscountRepo.findOne({
        where: {
          id: affiliateDiscountId,
          affiliateId: affiliateId
        },
        relations: ['affiliate', 'discount', 'discount.countries']
      });

      if (!affiliateDiscount) {
        throw new NotFoundException('Affiliate discount not found');
      }

      // Get usage statistics for this specific discount
      const usageStats = await this.affiliateDiscountUsageRepo
        .createQueryBuilder('usage')
        .select([
          'COUNT(usage.id) as totalUsages',
          'SUM(usage.order_amount) as totalOrderValue',
          'SUM(usage.discount_amount) as totalDiscountAmount',
          'SUM(usage.commission_amount) as totalCommissionEarned',
          'AVG(usage.order_amount) as averageOrderValue',
          'AVG(usage.commission_amount) as averageCommission',
          'COUNT(DISTINCT usage.customer_email) as uniqueCustomers',
          'MIN(usage.created_at) as firstUsage',
          'MAX(usage.created_at) as lastUsage'
        ])
        .where('usage.affiliateDiscountId = :affiliateDiscountId', { affiliateDiscountId })
        .getRawOne();

      // Get commission statistics for this discount
      const commissionStats = await this.affiliateCommissionRepo
        .createQueryBuilder('commission')
        .select([
          'COUNT(*) as totalCommissions',
          'SUM(CASE WHEN status = :pending THEN commission_amount ELSE 0 END) as pendingEarnings',
          'SUM(CASE WHEN status = :approved THEN commission_amount ELSE 0 END) as approvedEarnings',
          'SUM(CASE WHEN status = :paid THEN commission_amount ELSE 0 END) as paidEarnings',
          'SUM(commission_amount) as totalEarnings'
        ])
        .where('commission.affiliateDiscountId = :affiliateDiscountId', { affiliateDiscountId })
        .setParameters({
          pending: 'pending',
          approved: 'approved',
          paid: 'paid',
        })
        .getRawOne();

      // Get click statistics for this discount
      const clickStats = await this.affiliateClickRepo
        .createQueryBuilder('click')
        .select([
          'COUNT(*) as totalClicks',
          'COUNT(DISTINCT click.ip_address) as uniqueVisitors'
        ])
        .where('click.affiliateDiscountId = :affiliateDiscountId', { affiliateDiscountId })
        .getRawOne();

      // Calculate performance metrics
      const totalUsages = parseInt(usageStats.totalUsages) || 0;
      const totalClicks = parseInt(clickStats.totalClicks) || 0;
      const conversionRate = totalClicks > 0 ? Number(((totalUsages / totalClicks) * 100).toFixed(2)) : 0;

      const discountStatistics = {
        affiliateDiscount,
        statistics: {
          totalUsages,
          totalOrderValue: parseFloat(usageStats.totalOrderValue) || 0,
          totalDiscountAmount: parseFloat(usageStats.totalDiscountAmount) || 0,
          totalCommissionEarned: parseFloat(usageStats.totalCommissionEarned) || 0,
          averageOrderValue: parseFloat(usageStats.averageOrderValue) || 0,
          averageCommission: parseFloat(usageStats.averageCommission) || 0,
          uniqueCustomers: parseInt(usageStats.uniqueCustomers) || 0,
          firstUsage: usageStats.firstUsage,
          lastUsage: usageStats.lastUsage,
          totalCommissions: parseInt(commissionStats.totalCommissions) || 0,
          totalEarnings: parseFloat(commissionStats.totalEarnings) || 0,
          pendingEarnings: parseFloat(commissionStats.pendingEarnings) || 0,
          approvedEarnings: parseFloat(commissionStats.approvedEarnings) || 0,
          paidEarnings: parseFloat(commissionStats.paidEarnings) || 0,
          totalClicks,
          uniqueVisitors: parseInt(clickStats.uniqueVisitors) || 0,
          conversionRate
        }
      };

      return new ServiceResponse(discountStatistics, 'Affiliate discount statistics retrieved successfully');
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(`Error retrieving affiliate discount statistics: ${error.message}`);
    }
  }

  /**
   * Update affiliate profile
   */
  async updateAffiliateProfile(id: number, updateData: Partial<AffiliateProfile>): Promise<ServiceResponse> {
    try {
      const affiliate = await this.affiliateProfileRepo.findOne({
        where: { id },
        relations: ['user', 'country', 'affiliateDiscounts', 'affiliateDiscounts.discount']
      });

      if (!affiliate) {
        throw new NotFoundException('Affiliate not found');
      }

      // Check if affiliateCode is being updated and validate uniqueness
      if (updateData.affiliateCode && updateData.affiliateCode !== affiliate.affiliateCode) {
        const existingAffiliate = await this.affiliateProfileRepo.findOne({
          where: { affiliateCode: updateData.affiliateCode }
        });

        if (existingAffiliate) {
          throw new BadRequestException('Affiliate code already exists');
        }
      }

      // Update allowed fields
      const allowedFields = ['affiliateCode', 'countryId', 'commissionType', 'commissionRate', 'paymentMethod', 'bio', 'websiteUrl', 'socialMedia', 'paymentDetails'];
      const filteredData = {};

      allowedFields.forEach(field => {
        if (updateData[field] !== undefined) {
          filteredData[field] = updateData[field];
        }
      });

      await this.affiliateProfileRepo.update(id, filteredData);

      const updatedAffiliate = await this.affiliateProfileRepo.findOne({
        where: { id },
        relations: ['user', 'affiliateDiscounts', 'affiliateDiscounts.discount']
      });

      return new ServiceResponse(updatedAffiliate, 'Affiliate updated successfully');
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(`Error updating affiliate: ${error.message}`);
    }
  }

  /**
   * Delete affiliate profile
   */
  async softDeleteAffiliateProfile(id: number): Promise<ServiceResponse> {
    try {
      const affiliate = await this.affiliateProfileRepo.findOne({
        where: { id }
      });

      if (!affiliate) {
        throw new NotFoundException('Affiliate not found');
      }

      // // Check if affiliate has pending commissions
      // const pendingCommissions = await this.affiliateCommissionRepo.count({
      //   where: {
      //     affiliateId: id,
      //     status: CommissionStatus.PENDING
      //   }
      // });

      // if (pendingCommissions > 0) {
      //   throw new BadRequestException('Cannot delete affiliate with pending commissions');
      // }

      await this.affiliateProfileRepo.softDelete(affiliate);

      return new ServiceResponse(null, 'Affiliate deleted successfully');
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(`Error deleting affiliate: ${error.message}`);
    }
  }

  /**
   * Create a new affiliate profile
   */
  // async createAffiliateProfile(dto: CreateAffiliateProfileDto): Promise<ServiceResponse> {
  //   // Check if user exists and has affiliate user type
  //   const user = await this.userRepo.findOne({
  //     where: { id: dto?.userId },
  //     relations: ['userType']
  //   });

  //   if (!user) {
  //     throw new NotFoundException('User not found');
  //   }

  //   // Check if user already has an affiliate profile
  //   const existingProfile = await this.affiliateProfileRepo.findOne({
  //     where: { userId: dto?.userId }
  //   });

  //   if (existingProfile) {
  //     throw new BadRequestException('User already has an affiliate profile');
  //   }

  //   // Create affiliate profile without discount assignment
  //   // Discounts will be assigned separately after approval
  //   const affiliateProfile = this.affiliateProfileRepo.create({
  //     userId: dto.userId,
  //     countryId: dto.countryId,
  //     commissionType: dto.commissionType,
  //     commissionRate: dto.commissionRate,
  //     paymentMethod: dto.paymentMethod,
  //     paymentDetails: dto.paymentDetails,
  //     bio: dto.bio,
  //     websiteUrl: dto.websiteUrl,
  //     socialMedia: dto.socialMedia,
  //     status: AffiliateStatus.PENDING,
  //   });


  //   // return await this.affiliateProfileRepo.save(affiliateProfile);
  //   const result = await this.affiliateProfileRepo.save(affiliateProfile);

  //   const updatedUser = await this.userRepo.update(user.id, { userType: { id: 5 } });
  //   const updatedUserResult = await this.userRepo.findOne({
  //     where: { id: user.id },
  //     relations: ['userType']
  //   });
  //   // console.log('updatedUser:::', updatedUser);

  //   result.user = updatedUserResult

  //   if (!result) {
  //     return new ServiceResponse(
  //       result,
  //       'Failed to saved Affiliate profile! Please try again',
  //     );
  //   }

  //   return new ServiceResponse(result, 'Affiliate profile created successfully');
  // }


  async createAffiliateProfile(dto: CreateAffiliateProfileDto): Promise<ServiceResponse> {
    console.log('dto:::', dto);

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Step 1: Find user
      const user = await queryRunner.manager.findOne(User, {
        where: { id: dto?.userId },
        relations: ['userType']
      });

      if (!user) {
        throw new NotFoundException('User not found');
      }

      // Step 2: Check for existing affiliate profile
      const existingProfile = await queryRunner.manager.findOne(AffiliateProfile, {
        where: { userId: dto?.userId }
      });

      if (existingProfile) {
        throw new BadRequestException('User already has an affiliate profile');
      }

      // Step 3: Generate unique affiliate code
      const affiliateCode = dto.affiliateCode || await this.generateUniqueAffiliateCode(dto.userId);

      // Step 4: Create affiliate profile
      const affiliateProfile = queryRunner.manager.create(AffiliateProfile, {
        userId: dto.userId,
        affiliateCode,
        countryId: dto.countryId,
        commissionType: dto.commissionType,
        commissionRate: dto.commissionRate,
        paymentMethod: dto.paymentMethod,
        paymentDetails: dto.paymentDetails,
        bio: dto.bio,
        websiteUrl: dto.websiteUrl,
        socialMedia: dto.socialMedia,
        status: AffiliateStatus.PENDING,
      });

      const savedProfile = await queryRunner.manager.save(AffiliateProfile, affiliateProfile);

      // Step 5: Update user type to Affiliate (assuming id 5 is Affiliate)
      await queryRunner.manager.update(User, user.id, { userType: { id: 5 } });

      const updatedUser = await queryRunner.manager.findOne(User, {
        where: { id: user.id },
        relations: ['userType']
      });

      // Step 6: Commit transaction
      await queryRunner.commitTransaction();

      savedProfile.user = updatedUser;

      return new ServiceResponse(savedProfile, 'Affiliate profile created successfully');
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw new BadRequestException('Transaction failed: ' + error.message);
    } finally {
      await queryRunner.release();
    }
  }


  /**
   * Assign a discount to an affiliate
   */
  async assignDiscountToAffiliate(dto: AssignDiscountToAffiliateDto | AssignAffiliateDiscountDto): Promise<ServiceResponse> {
    // Verify affiliate exists and is active
    const affiliate = await this.affiliateProfileRepo.findOne({
      where: { id: dto.affiliateId, status: AffiliateStatus.ACTIVE }
    });

    console.log('assignDiscountToAffiliate affiliate:::', affiliate?.id);

    if (!affiliate) {
      // throw new NotFoundException('Active affiliate not found');
      return new ServiceResponse(
        null,
        'Active affiliate not found',
      );
    }

    // Verify discount exists
    const discount = await this.discountRepo.findOne({
      // where: { id: dto.discountId, isActive: true }
      where: { id: dto.discountId, is_active: true }
    });

    console.log('assignDiscountToAffiliate discount:::', discount?.id);

    if (!discount) {
      // throw new NotFoundException('Active discount not found');
      return new ServiceResponse(
        null,
        'Active discount not found',
      );
    }

    // Check if assignment already exists
    const existingAssignment = await this.affiliateDiscountRepo.findOne({
      where: {
        // affiliateId: dto?.affiliateId,
        discountId: dto?.discountId
      }
    });

    console.log('existingAssignment:::', existingAssignment?.id);

    if (existingAssignment) {
      // throw new BadRequestException('Discount already assigned to this affiliate');
      return new ServiceResponse(
        null,
        'Discount already assigned',
      );
    }

    const affiliateDiscount = this.affiliateDiscountRepo.create({
      ...dto,
      status: AffiliateDiscountStatus.PENDING,
      startDate: dto?.startDate ? new Date(dto?.startDate) : new Date(),
      endDate: dto?.endDate ? new Date(dto?.endDate) : null,
    });

    console.log('affiliateDiscount:::', affiliateDiscount?.id);

    const result = await this.affiliateDiscountRepo.save(affiliateDiscount);
    if (!result) {
      return new ServiceResponse(
        result,
        'Failed to assign discount to affiliate! Please try again',
      );
    }

    console.log('result:::', result);

    return new ServiceResponse(result, 'Discount assigned to affiliate successfully');
  }

  /**
   * Track affiliate click
   */
  async trackClick(dto: TrackClickDto, request: any): Promise<ServiceResponse> {
    let affiliate = null;
    let affiliateDiscount = null;

    // First, try to find affiliate by affiliate code directly
    affiliate = await this.affiliateProfileRepo.findOne({
      where: {
        affiliateCode: dto?.affiliateCode,
        status: AffiliateStatus.ACTIVE
      },
      relations: ['user']
    });

    // If not found by affiliate code, try to find by coupon code
    if (!affiliate) {
      const couponToSearch = dto?.couponCode || dto?.affiliateCode;
      affiliateDiscount = await this.affiliateDiscountRepo.findOne({
        where: {
          discount: {
            coupon: couponToSearch,
            is_active: true
          },
          status: AffiliateDiscountStatus.ACTIVE
        },
        relations: ['affiliate', 'affiliate.user', 'discount']
      });

      affiliate = affiliateDiscount?.affiliate;
    }

    if (!affiliate) {
      // throw new NotFoundException('Active affiliate not found for the provided code');
      return new ServiceResponse(null, 'Active affiliate not found for the provided code')
    }

    // Extract request information
    const ipAddress = request.ip || request.connection.remoteAddress;
    const userAgent = request.headers['user-agent'];

    // Use UTM tracking service for comprehensive tracking
    const trackingData = {
      affiliateId: affiliate.id,
      sessionId: dto?.sessionId,
      ipAddress,
      userAgent,
      referrerUrl: dto?.referrerUrl,
      landingPage: dto?.landingPage,
      source: dto?.source,
      countryId: dto?.countryId,
      utm: {
        utmSource: dto?.utmSource,
        utmMedium: dto?.utmMedium,
        utmCampaign: dto?.utmCampaign,
        utmContent: dto?.utmContent,
        utmTerm: dto?.utmTerm,
      }
    };

    // console.log('teack click trackingData:::', trackingData);


    const result = await this.utmTrackingService.trackClick(trackingData);

    // console.log('teack click result:::', result);


    if (!result.data) {
      throw new BadRequestException('Failed to save click');
    }

    const savedClick = result.data;

    // Update affiliate stats
    await this.updateAffiliateStats(affiliate.id);

    return new ServiceResponse(savedClick, 'Click tracked successfully');
  }



  /**
   * Get affiliate profile by code
   */
  async getAffiliateProfile(couponCode: string): Promise<ServiceResponse> {
    // Find affiliate by discount coupon code through affiliate_discounts table
    const affiliateDiscount = await this.affiliateDiscountRepo.findOne({
      where: {
        discount: {
          coupon: couponCode,
          is_active: true
        },
        status: AffiliateDiscountStatus.ACTIVE
      },
      relations: ['affiliate', 'affiliate.user', 'discount']
    });

    const affiliate = affiliateDiscount?.affiliate;

    if (!affiliate) {
      return new ServiceResponse(null, 'Affiliate not found');
    }

    return new ServiceResponse(affiliate, 'Affiliate profile retrieved successfully');
  }


  /**
   * Process order commission (called when order is placed)
   */
  async processOrderCommission(
    orderId: number,
    affiliateCode?: string,
    sessionId?: string,
    transactionalEM?: EntityManager
  ): Promise<AffiliateCommission | null> {
    const em = transactionalEM || this.affiliateCommissionRepo.manager;

    // Find order
    const order = await em.findOne(ProductOrder, {
      where: { id: orderId },
      relations: ['user', 'coupons']
    });

    if (!order) {
      throw new NotFoundException('Order not found');
    }

    let affiliate: AffiliateProfile | null = null;
    let click: AffiliateClick | null = null;

    // Try to find affiliate by coupon code first
    if (affiliateCode) {
      const affiliateDiscount = await em.findOne(AffiliateDiscount, {
        where: {
          discount: {
            coupon: affiliateCode,
            is_active: true
          },
          status: AffiliateDiscountStatus.ACTIVE
        },
        relations: ['affiliate', 'discount']
      });
      affiliate = affiliateDiscount?.affiliate;
    }

    // If no affiliate code, try to find by session/click tracking
    if (!affiliate && sessionId) {
      click = await em.findOne(AffiliateClick, {
        where: { sessionId, converted: false },
        relations: ['affiliate'],
        order: { createdAt: 'DESC' }
      });

      if (click) {
        affiliate = click.affiliate;
      }
    }

    if (!affiliate) {
      return null; // No affiliate attribution
    }

    // Calculate commission
    const commissionAmount = this.calculateCommission(
      order.amount,
      affiliate.commissionRate,
      affiliate.commissionType
    );

    // Create commission record
    const commission = em.create(AffiliateCommission, {
      affiliateId: affiliate.id,
      orderId: order.id,
      clickId: click?.id,
      source: CommissionSource.ORDER,
      status: CommissionStatus.PENDING,
      orderAmount: order.amount,
      commissionRate: affiliate.commissionRate,
      commissionAmount,
      customerEmail: order.user?.email,
      customerPhone: order.user?.phone,
      couponCode: order.coupons?.coupon,
    });

    const savedCommission = await em.save(commission);

    // Update click as converted
    if (click) {
      await em.update(AffiliateClick, click.id, {
        converted: true,
        conversionDate: new Date(),
        orderId: order.id,
        commissionEarned: commissionAmount
      });
    }

    // Track conversion
    try {
      await this.conversionTrackingService.trackOrderConversion({
        orderId: order.id,
        affiliateCode,
        sessionId,
        ipAddress: null, // Will be extracted from request context if available
        conversionPage: '/checkout/success'
      }, em);
    } catch (conversionError) {
      this.logger.warn(`Failed to track conversion for order ${order.id}: ${conversionError.message}`);
    }

    // Update affiliate stats
    await this.updateAffiliateStats(affiliate.id, em);

    return savedCommission;
  }



  /**
   * Calculate commission amount
   */
  private calculateCommission(orderAmount: number, rate: number, type: string): number {
    if (type === 'percentage') {
      return (orderAmount * rate) / 100;
    } else {
      return rate; // Fixed amount
    }
  }

  /**
   * Update affiliate statistics
   */
  private async updateAffiliateStats(affiliateId: number, em?: EntityManager): Promise<void> {
    const entityManager = em || this.affiliateProfileRepo.manager;

    // Get click count
    const clickCount = await entityManager.count(AffiliateClick, {
      where: { affiliateId }
    });

    // Get conversion count from affiliate_conversions table
    const conversionCount = await entityManager.count(AffiliateConversion, {
      where: { affiliateId }
    });

    // Get order count
    const orderCount = await entityManager.count(AffiliateCommission, {
      where: { affiliateId, source: CommissionSource.ORDER }
    });

    // Get earnings
    const earningsResult = await entityManager
      .createQueryBuilder(AffiliateCommission, 'commission')
      .select('SUM(commission.commissionAmount)', 'total')
      .addSelect('SUM(CASE WHEN commission.status = :pending THEN commission.commissionAmount ELSE 0 END)', 'pending')
      .addSelect('SUM(CASE WHEN commission.status = :paid THEN commission.commissionAmount ELSE 0 END)', 'paid')
      .where('commission.affiliateId = :affiliateId', { affiliateId })
      .setParameters({
        pending: CommissionStatus.PENDING,
        paid: CommissionStatus.PAID
      })
      .getRawOne();

    const conversionRate = clickCount > 0 ? (conversionCount / clickCount) * 100 : 0;

    await entityManager.update(AffiliateProfile, affiliateId, {
      totalClicks: clickCount,
      totalConversions: conversionCount,
      totalOrders: orderCount,
      totalEarnings: parseFloat(earningsResult.total) || 0,
      pendingEarnings: parseFloat(earningsResult.pending) || 0,
      paidEarnings: parseFloat(earningsResult.paid) || 0,
      conversionRate: parseFloat(conversionRate.toFixed(2)),
    });
  }

  /**
   * Get affiliate applications with filtering and pagination
   */
  async getApplications(
    filters?: {
      status?: AffiliateStatus;
    },
    pageOptionsDto?: PageOptionsDto,
  ): Promise<ServiceResponse> {
    // console.log('getApplications:::', filters);
    try {
      // For now, we'll treat affiliate profiles with 'pending' status as applications
      const queryBuilder = this.affiliateProfileRepo
        .createQueryBuilder('affiliate')
        .leftJoinAndSelect('affiliate.user', 'user')
        .leftJoinAndSelect('affiliate.country', 'country')
        .leftJoinAndSelect('affiliate.affiliateDiscounts', 'affiliateDiscount')
        .leftJoinAndSelect('affiliateDiscount.discount', 'discount');

      // Filter by status (default to pending for applications)
      const status = filters?.status || 'pending';
      queryBuilder.where('affiliate.status = :status', { status });

      // Apply pagination
      const page = pageOptionsDto?.page || 1;
      const limit = pageOptionsDto?.take || 10;
      const skip = (page - 1) * limit;

      queryBuilder.skip(skip).take(limit);
      queryBuilder.orderBy('affiliate.createdAt', 'DESC');

      const [applications, total] = await queryBuilder.getManyAndCount();

      const pageMetaDto = new PageMetaDto({ itemCount: total, pageOptionsDto });

      const result = new PageDto(applications, pageMetaDto);

      return new ServiceResponse(
        result.itemList,
        'All applications found successfully',
        result.meta,
      );
    } catch (error) {
      throw new BadRequestException(`Error retrieving applications: ${error.message}`);
    }
  }

  /**
   * Approve affiliate application
   */
  async approveApplication(
    id: number,
    approvalData: ApproveApplicationDto
  ): Promise<ServiceResponse> {
    try {
      const affiliate = await this.affiliateProfileRepo.findOne({
        where: { id },
        relations: ['user']
      });

      if (!affiliate) {
        // throw new NotFoundException('Application not found');
        return new ServiceResponse(null, 'Application not found');
      }

      if (affiliate.status !== AffiliateStatus.PENDING) {
        // throw new BadRequestException('Application is not in pending status');
        return new ServiceResponse(null, 'Application is not in pending status');
      }

      // Verify discount exists
      // const discount = await this.discountRepo.findOne({
      //   where: { id: approvalData.discountId }
      // });

      // if (!discount) {
      //   throw new NotFoundException('Discount not found');
      // }

      // Update affiliate profile status and commission rate
      await this.affiliateProfileRepo.update(id, {
        status: AffiliateStatus.ACTIVE,
        commissionRate: approvalData.commissionRate,
        notes: approvalData?.notes,
        updatedAt: new Date()
      });

      // // Create affiliate discount assignment
      // const affiliateDiscount = this.affiliateDiscountRepo.create({
      //   affiliateId: id,
      //   // discountId: approvalData.discountId,
      //   status: AffiliateDiscountStatus.ACTIVE,
      //   maxUsage: null, // Unlimited by default
      //   startDate: new Date(),
      //   endDate: null, // No end date by default
      //   notes: approvalData.notes || 'Approved application'
      // });

      // await this.affiliateDiscountRepo.save(affiliateDiscount);

      const updatedAffiliate = await this.affiliateProfileRepo.findOne({
        where: { id },
        relations: ['user', 'country', 'affiliateDiscounts', 'affiliateDiscounts.discount']
      });

      return new ServiceResponse(updatedAffiliate, 'Application approved successfully');
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }
      // throw new BadRequestException(`Error approving application: ${error.message}`);
      return new ServiceResponse(null, `Error approving application: ${error.message}`);
    }
  }

  /**
   * Reject affiliate application
   */
  async rejectApplication(id: number, reason: string): Promise<ServiceResponse> {
    try {
      const affiliate = await this.affiliateProfileRepo.findOne({
        where: { id },
        relations: ['user']
      });

      if (!affiliate) {
        throw new NotFoundException('Application not found');
      }

      if (affiliate.status !== AffiliateStatus.PENDING) {
        throw new BadRequestException('Application is not in pending status');
      }

      // Update affiliate profile
      await this.affiliateProfileRepo.update(id, {
        status: AffiliateStatus.REJECTED,
        updatedAt: new Date()
      });

      const updatedAffiliate = await this.affiliateProfileRepo.findOne({
        where: { id },
        relations: ['user']
      });

      return new ServiceResponse(
        { affiliate: updatedAffiliate, rejectionReason: reason },
        'Application rejected successfully'
      );
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(`Error rejecting application: ${error.message}`);
    }
  }

  async approveDiscountApplication(
    id: number,
    approvalData: { notes?: string }
  ): Promise<ServiceResponse> {
    try {
      const affiliateDiscount = await this.affiliateDiscountRepo.findOne({
        where: { id },
        relations: ['affiliate', 'discount']
      });

      if (!affiliateDiscount) {
        // throw new NotFoundException('Application not found');
        return new ServiceResponse(null, 'Application not found');
      }

      // if (affiliateDiscount.status !== AffiliateDiscountStatus.INACTIVE) {
      //   // throw new BadRequestException('Application is not in pending status');
      //   return new ServiceResponse(null, 'Application is not in pending status');
      // }

      // Update affiliate discount
      await this.affiliateDiscountRepo.update(id, {
        status: AffiliateDiscountStatus.ACTIVE,
        notes: approvalData.notes || 'Approved application'
      });

      const updatedAffiliateDiscount = await this.affiliateDiscountRepo.findOne({
        where: { id },
        relations: ['affiliate', 'discount']
      });

      return new ServiceResponse(updatedAffiliateDiscount, 'Application approved successfully');
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        return new ServiceResponse(null, error.message);
      }
      return new ServiceResponse(null, `Error approving application: ${error.message}`);
    }
  }

  /**
   * Reject affiliate discount application
   *
   * @param {number} id The ID of the affiliate discount application
   * @param {string} reason The reason for rejecting the application
   *
   * @returns {Promise<ServiceResponse>} The response contains the updated affiliate
   * discount application and the rejection reason
   */
  async rejectDiscountApplication(id: number, reason: string): Promise<ServiceResponse> {
    try {
      const affiliateDiscount = await this.affiliateDiscountRepo.findOne({
        where: { id },
        relations: ['affiliate', 'discount']
      });

      if (!affiliateDiscount) {
        // throw new NotFoundException('Application not found');
        return new ServiceResponse(null, 'Application not found');
      }

      // Update affiliate discount
      await this.affiliateDiscountRepo.update(id, {
        status: AffiliateDiscountStatus.REJECTED,
        notes: reason,
      });

      const updatedAffiliateDiscount = await this.affiliateDiscountRepo.findOne({
        where: { id },
        relations: ['affiliate', 'discount']
      });

      return new ServiceResponse(
        { affiliateDiscount: updatedAffiliateDiscount, rejectionReason: reason },
        'Application rejected successfully'
      );
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        return new ServiceResponse(null, error.message);
      }
      return new ServiceResponse(null, `Error rejecting application: ${error.message}`);
    }
  }

  /**
   * Get commissions with filtering and pagination
   */
  async getCommissions(
    filters?: {
      status?: string;
      affiliateId?: number;
      dateFrom?: string;
      dateTo?: string;
    },
    pageOptionsDto?: PageOptionsDto,
  ): Promise<ServiceResponse> {
    const queryBuilder = this.affiliateCommissionRepo.createQueryBuilder('commission')
      .leftJoinAndSelect('commission.affiliate', 'affiliate')
      .leftJoinAndSelect('affiliate.user', 'user')
      .leftJoinAndSelect('affiliate.affiliateDiscounts', 'affiliateDiscounts')
      .leftJoinAndSelect('affiliateDiscounts.discount', 'discount');

    // Apply filters
    if (filters?.status) {
      queryBuilder.andWhere('commission.status = :status', { status: filters.status });
    }

    if (filters?.affiliateId) {
      queryBuilder.andWhere('commission.affiliateId = :affiliateId', { affiliateId: filters.affiliateId });
    }

    if (filters?.dateFrom) {
      queryBuilder.andWhere('commission.createdAt >= :dateFrom', { dateFrom: filters.dateFrom });
    }

    if (filters?.dateTo) {
      queryBuilder.andWhere('commission.createdAt <= :dateTo', { dateTo: filters.dateTo });
    }

    // Apply pagination
    const page = pageOptionsDto?.page || 1;
    const limit = pageOptionsDto?.take || 10;
    const skip = (page - 1) * limit;

    queryBuilder.skip(skip).take(limit);
    queryBuilder.orderBy('commission.createdAt', 'DESC');

    const [list, itemCount] = await queryBuilder.getManyAndCount();

    const pageMetaDto = new PageMetaDto({ itemCount, pageOptionsDto });

    const result = new PageDto(list, pageMetaDto);

    if (!result?.itemList?.length) {
      return new ServiceResponse(result, 'Commissions not found');
    }

    return new ServiceResponse(
      result.itemList,
      'All Couriery found successfully',
      result.meta,
    );
  }


  /**
   * Get commissions with filtering and pagination
   */
  async getAffiliateCommissions(
    filters?: {
      status?: string;
      affiliateId?: number;
      dateFrom?: string;
      dateTo?: string;
    },
    pageOptionsDto?: PageOptionsDto,
  ): Promise<ServiceResponse> {
    const queryBuilder = this.affiliateCommissionRepo.createQueryBuilder('commission')
    // .leftJoinAndSelect('commission.affiliate', 'affiliate')
    // .leftJoinAndSelect('affiliate.user', 'user')
    // .leftJoinAndSelect('affiliate.affiliateDiscounts', 'affiliateDiscounts')
    // .leftJoinAndSelect('affiliateDiscounts.discount', 'discount');

    // Apply filters
    if (filters?.status) {
      queryBuilder.andWhere('commission.status = :status', { status: filters.status });
    }

    if (filters?.affiliateId) {
      queryBuilder.andWhere('commission.affiliateId = :affiliateId', { affiliateId: filters.affiliateId });
    }

    if (filters?.dateFrom) {
      queryBuilder.andWhere('commission.createdAt >= :dateFrom', { dateFrom: filters.dateFrom });
    }

    if (filters?.dateTo) {
      queryBuilder.andWhere('commission.createdAt <= :dateTo', { dateTo: filters.dateTo });
    }

    // Apply pagination
    const page = pageOptionsDto?.page || 1;
    const limit = pageOptionsDto?.take || 10;
    const skip = (page - 1) * limit;

    queryBuilder.skip(skip).take(limit);
    queryBuilder.orderBy('commission.createdAt', 'DESC');

    const [list, itemCount] = await queryBuilder.getManyAndCount();

    const pageMetaDto = new PageMetaDto({ itemCount, pageOptionsDto });

    const result = new PageDto(list, pageMetaDto);

    if (!result?.itemList?.length) {
      return new ServiceResponse(result, 'Commissions not found');
    }

    return new ServiceResponse(
      result.itemList,
      'All Couriery found successfully',
      result.meta,
    );
  }

  /**
   * Approve commission
   */
  async approveCommission(id: number): Promise<ServiceResponse> {
    try {
      const commission = await this.affiliateCommissionRepo.findOne({
        where: { id },
        relations: ['affiliate']
      });

      if (!commission) {
        throw new NotFoundException('Commission not found');
      }

      if (commission.status !== CommissionStatus.PENDING) {
        throw new BadRequestException('Commission is not in pending status');
      }

      await this.affiliateCommissionRepo.update(id, {
        status: CommissionStatus.APPROVED,
        updatedAt: new Date()
      });

      const updatedCommission = await this.affiliateCommissionRepo.findOne({
        where: { id },
        relations: ['affiliate', 'affiliate.user']
      });

      return new ServiceResponse(updatedCommission, 'Commission approved successfully');
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(`Error approving commission: ${error.message}`);
    }
  }

  async rejectCommission(id: number, reason: string): Promise<ServiceResponse> {
    try {
      const commission = await this.affiliateCommissionRepo.findOne({
        where: { id },
        relations: ['affiliate']
      });

      if (!commission) {
        throw new NotFoundException('Commission not found');
      }

      if (commission.status !== CommissionStatus.PENDING) {
        throw new BadRequestException('Commission is not in pending status');
      }

      await this.affiliateCommissionRepo.update(id, {
        status: CommissionStatus.CANCELLED,
        notes: reason,
        updatedAt: new Date()
      });

      const updatedCommission = await this.affiliateCommissionRepo.findOne({
        where: { id },
        relations: ['affiliate', 'affiliate.user']
      });

      return new ServiceResponse(updatedCommission, 'Commission rejected successfully');
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(`Error rejecting commission: ${error.message}`);
    }
  }

  /**
   * Mark commission as paid
   */
  async markCommissionAsPaid(
    id: number,
    paymentData?: { notes?: string; transactionId?: string }
  ): Promise<ServiceResponse> {
    try {
      const commission = await this.affiliateCommissionRepo.findOne({
        where: { id },
        relations: ['affiliate']
      });

      if (!commission) {
        throw new NotFoundException('Commission not found');
      }

      if (commission.status !== CommissionStatus.APPROVED) {
        throw new BadRequestException('Commission must be approved before payment');
      }

      await this.affiliateCommissionRepo.update(id, {
        status: CommissionStatus.PAID,
        notes: paymentData?.notes,
        updatedAt: new Date()
      });

      // Update affiliate earnings
      await this.affiliateProfileRepo.increment(
        { id: commission.affiliateId },
        'totalEarnings',
        commission.commissionAmount
      );

      const updatedCommission = await this.affiliateCommissionRepo.findOne({
        where: { id },
        relations: ['affiliate', 'affiliate.user']
      });

      return new ServiceResponse(updatedCommission, 'Commission marked as paid successfully');
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(`Error marking commission as paid: ${error.message}`);
    }
  }

  /**
   * Process multiple commission payments
   */
  async processMultiplePayments(paymentData: {
    commissionIds: number[];
    notes?: string;
    paymentMethod?: string;
  }): Promise<ServiceResponse> {
    try {
      const commissions = await this.affiliateCommissionRepo.find({
        where: { id: In(paymentData.commissionIds) },
        relations: ['affiliate']
      });

      if (commissions.length !== paymentData.commissionIds.length) {
        throw new BadRequestException('Some commissions not found');
      }

      // Verify all commissions are approved
      const unapprovedCommissions = commissions.filter(c => c.status !== CommissionStatus.APPROVED);
      if (unapprovedCommissions.length > 0) {
        throw new BadRequestException('All commissions must be approved before payment');
      }

      // Process payments in transaction
      const totalAmount = commissions.reduce((sum, c) => sum + c.commissionAmount, 0);

      await this.affiliateCommissionRepo.update(
        { id: In(paymentData.commissionIds) },
        {
          status: CommissionStatus.PAID,
          notes: paymentData.notes,
          updatedAt: new Date()
        }
      );

      // Update affiliate earnings for each affiliate
      const affiliateEarnings = commissions.reduce((acc, commission) => {
        if (!acc[commission.affiliateId]) {
          acc[commission.affiliateId] = 0;
        }
        acc[commission.affiliateId] += commission.commissionAmount;
        return acc;
      }, {});

      for (const [affiliateId, earnings] of Object.entries(affiliateEarnings)) {
        // Move earnings from pending to paid
        await this.affiliateProfileRepo.decrement(
          { id: parseInt(affiliateId) },
          'pendingEarnings',
          earnings as number
        );
        await this.affiliateProfileRepo.increment(
          { id: parseInt(affiliateId) },
          'paidEarnings',
          earnings as number
        );
      }

      return new ServiceResponse(
        {
          processedCount: commissions.length,
          totalAmount,
          commissionIds: paymentData.commissionIds
        },
        'Payments processed successfully'
      );
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(`Error processing payments: ${error.message}`);
    }
  }

  /**
   * Get performance reports
   */
  async getPerformanceReports(filters?: {
    affiliateId?: number;
    dateFrom?: string;
    dateTo?: string;
    // groupBy?: string;
  }): Promise<ServiceResponse> {
    try {
      const queryBuilder = this.affiliateProfileRepo
        .createQueryBuilder('affiliate')
        .leftJoinAndSelect('affiliate.user', 'user')
        .leftJoinAndSelect('affiliate.country', 'country')
        .leftJoinAndSelect('affiliate.affiliateDiscounts', 'affiliateDiscounts')
        .leftJoinAndSelect('affiliateDiscounts.discount', 'discount')
        .leftJoinAndSelect('affiliate.commissions', 'commissions')
        .leftJoinAndSelect('affiliate.clicks', 'clicks')
        .leftJoinAndSelect('affiliate.conversions', 'conversions');

      // Apply filters
      if (filters?.affiliateId) {
        queryBuilder.andWhere('affiliate.id = :affiliateId', { affiliateId: filters.affiliateId });
      }

      if (filters?.dateFrom) {
        queryBuilder.andWhere('commissions.createdAt >= :dateFrom', { dateFrom: filters.dateFrom });
      }

      if (filters?.dateTo) {
        queryBuilder.andWhere('commissions.createdAt <= :dateTo', { dateTo: filters.dateTo });
      }

      queryBuilder.andWhere('affiliate.status = :status', { status: AffiliateStatus.ACTIVE });

      const affiliates = await queryBuilder.getMany();

      // Calculate performance metrics
      const performanceData = affiliates.map(affiliate => {
        const totalClicks = affiliate.clicks?.length || 0;
        const totalConversions = affiliate.conversions?.length || 0;
        const totalCommissions = affiliate.commissions?.reduce((sum, c) => sum + c.commissionAmount, 0) || 0;
        const totalOrders = affiliate.commissions?.length || 0;
        const conversionRate = totalClicks > 0 ? (totalConversions / totalClicks) * 100 : 0;
        const averageOrderValue = totalOrders > 0 ? totalCommissions / totalOrders : 0;

        return {
          id: affiliate.id,
          name: `${affiliate.user?.firstName} ${affiliate.user?.lastName}`,
          email: affiliate.user?.email,
          country: affiliate.country ? {
            id: affiliate.country.id,
            name: affiliate.country.name,
            code: affiliate.country.code
          } : null,
          couponCodes: affiliate.affiliateDiscounts?.map(ad => ad.discount?.coupon).filter(Boolean) || [],
          totalClicks,
          totalConversions,
          totalOrders,
          totalCommissions,
          conversionRate,
          averageOrderValue,
          status: affiliate.status,
          joinDate: affiliate.createdAt
        };
      });

      return new ServiceResponse(performanceData, 'Performance reports retrieved successfully');
    } catch (error) {
      throw new BadRequestException(`Error retrieving performance reports: ${error.message}`);
    }
  }



  /**
   * Update affiliate discount assignment
   */
  async updateAffiliateDiscount(id: number, dto: UpdateAffiliateDiscountDto): Promise<ServiceResponse> {
    try {
      const affiliateDiscount = await this.affiliateDiscountRepo.findOne({
        where: { id },
        relations: ['affiliate', 'discount']
      });

      if (!affiliateDiscount) {
        throw new NotFoundException('Affiliate discount assignment not found');
      }

      // Update the assignment
      await this.affiliateDiscountRepo.update(id, {
        status: dto.status,
        maxUsage: dto.maxUsage,
        startDate: dto.startDate ? new Date(dto.startDate) : undefined,
        endDate: dto.endDate ? new Date(dto.endDate) : undefined,
        notes: dto.notes,
        commissionType: dto.commissionType,
        commissionRate: dto.commissionRate,
        updatedAt: new Date()
      });

      const updatedAssignment = await this.affiliateDiscountRepo.findOne({
        where: { id },
        relations: ['affiliate', 'discount']
      });

      return new ServiceResponse(updatedAssignment, 'Affiliate discount assignment updated successfully');
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(`Error updating affiliate discount assignment: ${error.message}`);
    }
  }

  /**
   * Remove discount from affiliate
   */
  async removeDiscountFromAffiliate(id: number): Promise<ServiceResponse> {
    try {
      const affiliateDiscount = await this.affiliateDiscountRepo.findOne({
        where: { id },
        relations: ['affiliate', 'discount']
      });

      if (!affiliateDiscount) {
        throw new NotFoundException('Affiliate discount assignment not found');
      }

      await this.affiliateDiscountRepo.softDelete(affiliateDiscount?.id);

      return new ServiceResponse(null, 'Discount removed from affiliate successfully');
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(`Error removing discount from affiliate: ${error.message}`);
    }
  }

  /**
   * Get affiliate discounts
   */
  async getAffiliateDiscounts(affiliateId: number): Promise<ServiceResponse> {
    try {
      const affiliateDiscounts = await this.affiliateDiscountRepo.find({
        where: { affiliateId },
        relations: ['discount'],
        order: { createdAt: 'DESC' }
      });

      return new ServiceResponse(affiliateDiscounts, 'Affiliate discounts retrieved successfully');
    } catch (error) {
      throw new BadRequestException(`Error retrieving affiliate discounts: ${error.message}`);
    }
  }


  /**
   * Generate affiliate link
   */
  async generateAffiliateLink(
    targetUrl: string,
    affiliateCode: string,
    utmParams?: any
  ): Promise<ServiceResponse> {
    // Find affiliate by discount coupon code through affiliate_discounts table
    const affiliateProfile = await this.affiliateProfileRepo.findOne({
      where: {
        affiliateCode,
        status: AffiliateStatus.ACTIVE
      },
      relations: ['affiliateDiscounts', 'affiliateDiscounts.discount']
    });

    console.log(' generateAffiliateLink affiliate:::', affiliateProfile?.id);

    if (!affiliateProfile) {
      return new ServiceResponse(null, 'Affiliate not found');
    }

    const url = new URL(targetUrl);
    url.searchParams.set('ref', affiliateProfile?.affiliateCode);

    if (utmParams) {
      Object.keys(utmParams).forEach(key => {
        if (utmParams[key]) {
          url.searchParams.set(key, utmParams[key]);
        }
      });
    }
    console.log('url:::', url.toString());

    // return url.toString();
    return new ServiceResponse(url.toString(), 'Affiliate link generated successfully');
  }

}
