import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, EntityManager } from 'typeorm';
import { AffiliateProfile, AffiliateStatus } from '../entities/affiliate-profile.entity';
import { AffiliateDiscount, AffiliateDiscountStatus } from '../entities/affiliate-discount.entity';
import { AffiliateCommission } from '../entities/affiliate-commission.entity';
import { ServiceResponse } from 'src/common/utils/service-response';

@Injectable()
export class AffiliateTrackingService {
  private readonly logger = new Logger(AffiliateTrackingService.name);

  constructor(
    @InjectRepository(AffiliateProfile)
    private affiliateProfileRepo: Repository<AffiliateProfile>,

    @InjectRepository(AffiliateDiscount)
    private affiliateDiscountRepo: Repository<AffiliateDiscount>,

    @InjectRepository(AffiliateCommission)
    private affiliateCommissionRepo: Repository<AffiliateCommission>,
  ) { }

  /**
   * Track discount usage and create affiliate commission
   * Simplified version - use AffiliateDiscountIntegrationService for full functionality
   */
  async trackDiscountUsage(
    discountUsageId: number,
    orderId: number,
    sessionData?: any,
    transactionalEM?: EntityManager
  ): Promise<AffiliateCommission | null> {
    // This method is simplified for now
    // Use AffiliateDiscountIntegrationService.processOrderWithAffiliateCommission() instead
    this.logger.log(`Tracking discount usage: ${discountUsageId} for order: ${orderId}`);
    return null;
  }

  /**
   * Get affiliate by coupon code (updated for direct discount-affiliate relationship)
   */
  async getAffiliateByDiscountCode(couponCode: string): Promise<ServiceResponse> {
    const affiliate = await this.affiliateProfileRepo.findOne({
      where: {
        status: AffiliateStatus.ACTIVE,
        affiliateDiscounts: {
          discount: {
            coupon: couponCode,
            is_active: true
          },
          status: AffiliateDiscountStatus.ACTIVE
        }
      },
      relations: ['country', 'affiliateDiscounts', 'affiliateDiscounts.discount']
    });

    console.log('getAffiliateByDiscountCode affiliate:::', affiliate?.id);

    return new ServiceResponse(
      affiliate,
      affiliate ? 'Affiliate found successfully' : 'No affiliate found for this coupon code'
    );
  }

  async getAffiliateDashboardStats(affiliateId: number): Promise<ServiceResponse> {
    const discounts = await this.getAffiliateDiscounts(affiliateId);
    const stats = await this.getAffiliateDiscountStats(affiliateId);

    return new ServiceResponse(
      { discounts, stats },
      'Dashboard data retrieved successfully'
    );
  }

  /**
   * Get affiliate discount assignments for a specific affiliate
   */
  async getAffiliateDiscounts(affiliateId: number): Promise<ServiceResponse> {
    const result = await this.affiliateDiscountRepo.find({
      where: { affiliateId },
      relations: ['discount'],
      order: { createdAt: 'DESC' }
    })

    return new ServiceResponse(
      result,
      'Discount assignments retrieved successfully'
    );
  }

  /**
   * Get affiliate discount statistics
   */
  async getAffiliateDiscountStats(affiliateId: number, discountId?: number): Promise<ServiceResponse> {
    const queryBuilder = this.affiliateDiscountRepo
      .createQueryBuilder('ad')
      .leftJoinAndSelect('ad.affiliate', 'affiliate')
      .leftJoinAndSelect('ad.discount', 'discount')
      .where('ad.affiliateId = :affiliateId', { affiliateId });

    if (discountId) {
      queryBuilder.andWhere('ad.discountId = :discountId', { discountId });
    }

    const usages = await queryBuilder.getMany();

    const stats = {
      totalAssignments: usages.length,
      activeAssignments: usages.filter(u => u.status === AffiliateDiscountStatus.ACTIVE).length,
      totalUsageCount: usages.reduce((sum, u) => sum + (u.usageCount || 0), 0),
      totalCommissionEarned: usages.reduce((sum, u) => sum + Number(u.totalCommissionEarned || 0), 0),
      averageCommissionPerUsage: 0,
    };

    if (stats.totalUsageCount > 0) {
      stats.averageCommissionPerUsage = stats.totalCommissionEarned / stats.totalUsageCount;
    }

    return new ServiceResponse(
      stats,
      'Statistics retrieved successfully'
    );
  }

  /**
   * Validate affiliate discount assignment
   */
  async validateAffiliateDiscount(affiliateId: number, discountId: number): Promise<ServiceResponse> {
    const assignment = await this.affiliateDiscountRepo.findOne({
      where: {
        affiliateId,
        discountId,
        status: AffiliateDiscountStatus.ACTIVE
      }
    });

    console.log('assignment:::', assignment);

    if (!assignment) {
      return new ServiceResponse({ isValid: false }, 'Affiliate discount assignment is invalid');
    }

    // Check date validity
    const now = new Date();
    if (assignment.startDate && assignment.startDate > now) {
      return new ServiceResponse({ isValid: false }, 'Affiliate discount assignment is not yet active');
    }
    if (assignment.endDate && assignment.endDate < now) {
      return new ServiceResponse({ isValid: false }, 'Affiliate discount assignment has expired');
    }

    // Check usage limits
    if (assignment.maxUsage && assignment.usageCount >= assignment.maxUsage) {
      return new ServiceResponse({ isValid: false }, 'Affiliate discount assignment usage limit has been reached');
    }

    return new ServiceResponse({ isValid: true }, 'Affiliate discount assignment is valid');
  }
}