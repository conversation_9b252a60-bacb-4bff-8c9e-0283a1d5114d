import { Injectable, Logger } from '@nestjs/common';
import { AffiliateTrackingService } from './affiliate-tracking.service';
import { AffiliateService } from './affiliate.service';
import { EntityManager } from 'typeorm';

/**
 * Integration service to hook affiliate tracking into existing discount and order flows
 * This service should be called from your existing DiscountService and ProductOrderService
 */
@Injectable()
export class AffiliateIntegrationService {
  private readonly logger = new Logger(AffiliateIntegrationService.name);

  constructor(
    private readonly affiliateTrackingService: AffiliateTrackingService,
    private readonly affiliateService: AffiliateService,
  ) {}

  /**
   * Hook this into your existing discount application logic
   * Call this method when a discount is applied to an order
   */
  async onDiscountApplied(
    discountUsageId: number,
    orderId: number,
    sessionData?: {
      sessionId?: string;
      ipAddress?: string;
      userAgent?: string;
      referrerUrl?: string;
      utmSource?: string;
      utmMedium?: string;
      utmCampaign?: string;
    },
    transactionalEM?: EntityManager
  ): Promise<void> {
    try {
      await this.affiliateTrackingService.trackDiscountUsage(
        discountUsageId,
        orderId,
        sessionData,
        transactionalEM
      );
    } catch (error) {
      this.logger.error(`Failed to track affiliate discount usage: ${error.message}`, error.stack);
      // Don't throw error to avoid breaking the main discount flow
    }
  }

  /**
   * Hook this into your existing order creation logic
   * Call this method when an order is successfully created
   */
  async onOrderCreated(
    orderId: number,
    sessionData?: {
      affiliateCode?: string;
      sessionId?: string;
    },
    transactionalEM?: EntityManager
  ): Promise<void> {
    try {
      await this.affiliateService.processOrderCommission(
        orderId,
        sessionData?.affiliateCode,
        sessionData?.sessionId,
        transactionalEM
      );
    } catch (error) {
      this.logger.error(`Failed to process affiliate order commission: ${error.message}`, error.stack);
      // Don't throw error to avoid breaking the main order flow
    }
  }

  /**
   * Check if a coupon code belongs to an affiliate
   * Use this in your cart/checkout flow to identify affiliate attribution
   */
  async getAffiliateFromCoupon(couponCode: string) {
    try {
      return await this.affiliateTrackingService.getAffiliateByDiscountCode(couponCode);
    } catch (error) {
      this.logger.error(`Failed to get affiliate from coupon: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Validate if an affiliate can use a specific discount
   */
  async validateAffiliateDiscount(affiliateId: number, discountId: number): Promise<boolean> {
    try {
      const result = await this.affiliateTrackingService.validateAffiliateDiscount(affiliateId, discountId);

      return result.data?.isValid;
    } catch (error) {
      this.logger.error(`Failed to validate affiliate discount: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * Extract session data from request for tracking
   */
  extractSessionData(request: any): any {
    return {
      sessionId: request.session?.id || request.headers['x-session-id'],
      ipAddress: request.ip || request.connection?.remoteAddress,
      userAgent: request.headers['user-agent'],
      referrerUrl: request.headers['referer'] || request.headers['referrer'],
      utmSource: request.query?.utm_source,
      utmMedium: request.query?.utm_medium,
      utmCampaign: request.query?.utm_campaign,
      affiliateCode: request.query?.ref || request.headers['x-affiliate-code'],
    };
  }
}
