# Affiliate System Documentation

## Overview
The Affiliate System is a comprehensive multi-discount affiliate marketing platform that allows users to become affiliates, track clicks, manage multiple discount codes, calculate commissions, and monitor performance with UTM tracking and conversion analytics.

## Table of Contents
- [Backend Implementation](#backend-implementation)
- [Frontend Client](#frontend-client)
- [Frontend Admin](#frontend-admin)
- [API Endpoints](#api-endpoints)
- [Database Schema](#database-schema)
- [Commission Calculation](#commission-calculation)
- [UTM Tracking](#utm-tracking)
- [Testing](#testing)

---

## Backend Implementation

### Core Entities

#### 1. AffiliateProfile
```typescript
// Main affiliate entity
- id: number
- userId: number (FK to User)
- countryId: number (FK to Country)
- commissionType: 'percentage' | 'fixed'
- commissionRate: number
- paymentMethod: string
- paymentDetails: object
- bio: string
- websiteUrl: string
- socialMedia: object
- status: 'pending' | 'active' | 'inactive' | 'suspended'
- totalClicks: number
- totalConversions: number
- totalOrders: number
- totalCommissions: number
- conversionRate: number
- lastActivityAt: Date
```

#### 2. AffiliateDiscount (Many-to-Many)
```typescript
// Links affiliates to multiple discounts
- id: number
- affiliateId: number (FK to AffiliateProfile)
- discountId: number (FK to Discount)
- status: 'active' | 'inactive' | 'expired'
- maxUsage: number
- currentUsage: number
- startDate: Date
- endDate: Date
- notes: string
```

#### 3. AffiliateClick
```typescript
// UTM tracking and click analytics
- id: number
- affiliateId: number
- sessionId: string
- ipAddress: string
- userAgent: string
- referrerUrl: string
- landingPage: string
- source: 'direct' | 'social_media' | 'email' | 'search' | 'other'
- countryId: number
- city: string
- deviceType: string
- browser: string
- os: string
- utmSource: string
- utmMedium: string
- utmCampaign: string
- utmContent: string
- utmTerm: string
- converted: boolean
- conversionDate: Date
- orderId: number
- commissionEarned: number
```

#### 4. AffiliateConversion
```typescript
// Conversion tracking (signup, purchase, lead)
- id: number
- affiliateId: number
- clickId: number (FK to AffiliateClick)
- orderId: number
- type: 'signup' | 'purchase' | 'lead'
- conversionValue: number
- customerEmail: string
- customerPhone: string
- sessionId: string
- ipAddress: string
- conversionPage: string
- timeToConversion: number (minutes)
- couponUsed: string
- discountApplied: number
- commissionEarned: number
- conversionData: object
```

#### 5. AffiliateCommission
```typescript
// Commission records and payments
- id: number
- affiliateId: number
- orderId: number
- clickId: number
- conversionId: number
- commissionAmount: number
- commissionRate: number
- commissionType: 'percentage' | 'fixed'
- orderAmount: number
- discountAmount: number
- status: 'pending' | 'approved' | 'paid' | 'cancelled'
- source: 'order' | 'manual' | 'bonus'
- notes: string
- approvedAt: Date
- paidAt: Date
```

### Core Services

#### 1. AffiliateService
```typescript
// Main affiliate management
- createAffiliateProfile()
- getAllAffiliates()
- getAffiliateById()
- updateAffiliateProfile()
- approveApplication()
- rejectApplication()
- getApplications()
- assignDiscountToAffiliate()
- updateAffiliateDiscount()
- removeDiscountFromAffiliate()
- getAffiliateDiscounts()
- trackClick()
- processOrderCommission()
- updateAffiliateStats()
- getPerformanceReports()
```

#### 2. UTMTrackingService
```typescript
// UTM parameter tracking and analytics
- trackClick()
- trackConversion()
- getUTMAnalytics()
- parseUTMFromUrl()
- generateTrackingUrl()
```

#### 3. ConversionTrackingService
```typescript
// Conversion funnel and timeline analytics
- trackOrderConversion()
- trackSignupConversion()
- getConversionFunnel()
- getConversionTimeline()
```

#### 4. AffiliateDiscountCommissionService
```typescript
// Commission calculation and discount management
- calculateOrderCommission()
- previewCartCommission()
- calculateDiscountAmount()
- validateCommissionCalculation()
```

#### 5. AffiliateOrderIntegrationService
```typescript
// Order integration and earnings
- applyAffiliateDiscountToCart()
- validateAffiliateCoupon()
- getAffiliateEarningsSummary()
```

### Commission Calculation Logic

#### Strategy
- **Commission Base**: `cart.total` (includes all charges)
- **Formula**: `commission = cart.total × commission_rate / 100`
- **Discount Handling**: Uses actual `cart.discount` amount
- **Consistency**: Cart preview matches order calculation

#### Example
```typescript
// Cart Data
cartTotal: 979.1
commissionRate: 10.5%
discountAmount: 99.9

// Commission Calculation
commission = 979.1 × 10.5% = 102.81
```

---

## Frontend Client

### User Registration Flow
```typescript
// 1. User applies to become affiliate
POST /api/affiliate/register
{
  userId: number,
  countryId?: number,
  commissionType: 'percentage' | 'fixed',
  commissionRate: number,
  paymentMethod: string,
  bio?: string,
  websiteUrl?: string,
  socialMedia?: object
}

// 2. Application status: 'pending'
// 3. Admin approval required
// 4. Status changes to 'active'
// 5. Discounts assigned by admin
```

### Affiliate Dashboard Components

#### 1. Performance Overview
```vue
<template>
  <div class="affiliate-dashboard">
    <PerformanceCards 
      :totalClicks="stats.totalClicks"
      :totalConversions="stats.totalConversions"
      :conversionRate="stats.conversionRate"
      :totalCommissions="stats.totalCommissions"
    />
    
    <ConversionChart :data="conversionData" />
    <UTMAnalytics :utmData="utmAnalytics" />
  </div>
</template>
```

#### 2. Link Generator
```vue
<template>
  <div class="link-generator">
    <form @submit="generateLink">
      <input v-model="baseUrl" placeholder="Product URL" />
      <input v-model="utmSource" placeholder="UTM Source" />
      <input v-model="utmMedium" placeholder="UTM Medium" />
      <input v-model="utmCampaign" placeholder="UTM Campaign" />
      <button type="submit">Generate Tracking Link</button>
    </form>
    
    <div v-if="trackingUrl" class="generated-link">
      <input :value="trackingUrl" readonly />
      <button @click="copyToClipboard">Copy</button>
    </div>
  </div>
</template>
```

#### 3. Commission History
```vue
<template>
  <div class="commission-history">
    <DataTable 
      :items="commissions"
      :headers="commissionHeaders"
      :loading="loading"
    >
      <template #status="{ item }">
        <StatusChip :status="item.status" />
      </template>
    </DataTable>
  </div>
</template>
```

### Client API Integration
```typescript
// Composables
export const useAffiliate = () => {
  const getAffiliateStats = async (affiliateId: number) => {
    return await $fetch(`/api/affiliate/${affiliateId}/stats`)
  }
  
  const generateTrackingUrl = async (params: UTMParams) => {
    return await $fetch('/api/affiliate/generate-tracking-url', {
      method: 'POST',
      body: params
    })
  }
  
  const getCommissions = async (affiliateId: number, filters?: any) => {
    return await $fetch(`/api/affiliate/${affiliateId}/commissions`, {
      query: filters
    })
  }
}
```

---

## Frontend Admin

### Admin Dashboard Features

#### 1. Affiliate Management
```vue
<template>
  <div class="affiliate-management">
    <!-- Affiliate List -->
    <DataTable 
      :items="affiliates"
      :headers="affiliateHeaders"
      :search="search"
      :filters="filters"
    >
      <template #actions="{ item }">
        <v-btn @click="viewAffiliate(item.id)">View</v-btn>
        <v-btn @click="editAffiliate(item.id)">Edit</v-btn>
        <v-btn @click="manageDiscounts(item.id)">Discounts</v-btn>
      </template>
    </DataTable>
    
    <!-- Filters -->
    <AffiliateFilters 
      v-model:status="filters.status"
      v-model:country="filters.countryId"
      v-model:search="search"
    />
  </div>
</template>
```

#### 2. Application Approval
```vue
<template>
  <div class="application-approval">
    <v-card v-for="app in applications" :key="app.id">
      <v-card-title>{{ app.user.firstName }} {{ app.user.lastName }}</v-card-title>
      <v-card-text>
        <p>Email: {{ app.user.email }}</p>
        <p>Commission: {{ app.commissionRate }}% {{ app.commissionType }}</p>
        <p>Bio: {{ app.bio }}</p>
      </v-card-text>
      <v-card-actions>
        <v-btn color="success" @click="approveApplication(app.id)">
          Approve
        </v-btn>
        <v-btn color="error" @click="rejectApplication(app.id)">
          Reject
        </v-btn>
      </v-card-actions>
    </v-card>
  </div>
</template>
```

#### 3. Discount Assignment
```vue
<template>
  <div class="discount-assignment">
    <v-form @submit="assignDiscount">
      <v-select
        v-model="selectedAffiliate"
        :items="affiliates"
        item-title="user.email"
        item-value="id"
        label="Select Affiliate"
      />
      
      <v-select
        v-model="selectedDiscount"
        :items="discounts"
        item-title="title"
        item-value="id"
        label="Select Discount"
      />
      
      <v-text-field
        v-model="maxUsage"
        label="Max Usage"
        type="number"
      />
      
      <v-date-picker v-model="startDate" label="Start Date" />
      <v-date-picker v-model="endDate" label="End Date" />
      
      <v-btn type="submit">Assign Discount</v-btn>
    </v-form>
  </div>
</template>
```

#### 4. Commission Management
```vue
<template>
  <div class="commission-management">
    <!-- Commission Overview -->
    <CommissionStats :stats="commissionStats" />
    
    <!-- Commission List -->
    <DataTable 
      :items="commissions"
      :headers="commissionHeaders"
      selectable
      v-model:selected="selectedCommissions"
    >
      <template #actions="{ item }">
        <v-btn 
          v-if="item.status === 'pending'"
          @click="approveCommission(item.id)"
          color="success"
        >
          Approve
        </v-btn>
        <v-btn 
          v-if="item.status === 'approved'"
          @click="markAsPaid(item.id)"
          color="primary"
        >
          Mark Paid
        </v-btn>
      </template>
    </DataTable>
    
    <!-- Bulk Actions -->
    <div class="bulk-actions">
      <v-btn 
        @click="bulkApprove"
        :disabled="selectedCommissions.length === 0"
        color="success"
      >
        Approve Selected ({{ selectedCommissions.length }})
      </v-btn>
      <v-btn 
        @click="bulkPay"
        :disabled="selectedCommissions.length === 0"
        color="primary"
      >
        Mark as Paid ({{ selectedCommissions.length }})
      </v-btn>
    </div>
  </div>
</template>
```

### Admin API Integration
```typescript
// Admin composables
export const useAffiliateAdmin = () => {
  const getAffiliates = async (filters?: AffiliateFilters) => {
    return await $fetch('/api/affiliate/all', { query: filters })
  }
  
  const approveApplication = async (id: number, data: ApprovalData) => {
    return await $fetch(`/api/affiliate/approve/${id}`, {
      method: 'POST',
      body: data
    })
  }
  
  const assignDiscount = async (data: AssignDiscountData) => {
    return await $fetch('/api/affiliate/discounts/assign', {
      method: 'POST',
      body: data
    })
  }
  
  const getCommissions = async (filters?: CommissionFilters) => {
    return await $fetch('/api/affiliate/commissions', { query: filters })
  }
}
```

---

## API Endpoints

### Core Affiliate Management
```
GET    /api/affiliate/all                    # Get all affiliates (admin)
GET    /api/affiliate/:id                    # Get affiliate by ID
POST   /api/affiliate/register               # Register new affiliate
PUT    /api/affiliate/:id                    # Update affiliate profile
DELETE /api/affiliate/:id                    # Delete affiliate

# Application Management
GET    /api/affiliate/applications           # Get pending applications (admin)
POST   /api/affiliate/approve/:id            # Approve application (admin)
POST   /api/affiliate/reject/:id             # Reject application (admin)
```

### Discount Management
```
POST   /api/affiliate/discounts/assign       # Assign discount to affiliate (admin)
PUT    /api/affiliate/discounts/:id          # Update discount assignment (admin)
DELETE /api/affiliate/discounts/:id          # Remove discount from affiliate (admin)
GET    /api/affiliate/:id/discounts          # Get affiliate discounts
```

### Tracking & Analytics
```
POST   /api/affiliate/track-click            # Track affiliate click
GET    /api/affiliate/:id/utm-analytics      # Get UTM analytics
GET    /api/affiliate/:id/conversion-funnel  # Get conversion funnel
GET    /api/affiliate/:id/conversion-timeline # Get conversion timeline
POST   /api/affiliate/generate-tracking-url  # Generate tracking URL
```

### Commission Management
```
GET    /api/affiliate/commissions            # Get commissions (admin)
GET    /api/affiliate/:id/commissions        # Get affiliate commissions
POST   /api/affiliate/preview-commission     # Preview cart commission
POST   /api/affiliate/validate-commission    # Validate commission calculation
POST   /api/affiliate/calculate-order-commission # Calculate order commission
POST   /api/affiliate/test-commission-consistency # Test calculation consistency
```

### Performance & Reports
```
GET    /api/affiliate/:id/performance        # Get performance metrics
GET    /api/affiliate/:id/earnings-summary   # Get earnings summary
GET    /api/affiliate/reports                # Generate performance reports (admin)
```

---

## Database Schema

### Key Relationships
```sql
-- One-to-Many: User -> AffiliateProfile
-- One-to-Many: Country -> AffiliateProfile
-- Many-to-Many: AffiliateProfile <-> Discount (via AffiliateDiscount)
-- One-to-Many: AffiliateProfile -> AffiliateClick
-- One-to-Many: AffiliateProfile -> AffiliateConversion
-- One-to-Many: AffiliateProfile -> AffiliateCommission
-- One-to-Many: AffiliateClick -> AffiliateConversion
-- One-to-Many: AffiliateConversion -> AffiliateCommission
```

### Migration Files
```
- add-country-to-affiliate-profiles.ts
- create-affiliate-discount-table.ts
- create-affiliate-conversion-table.ts
- update-affiliate-click-utm-fields.ts
```

---

## Commission Calculation

### Current Strategy
- **Base Amount**: `cart.total` (full cart amount including shipping, tax)
- **Formula**: `commission = cart.total × commission_rate / 100`
- **Discount Handling**: Uses actual `cart.discount` value
- **Consistency**: Cart preview matches order calculation

### Example Calculation
```typescript
// Cart Data
const cart = {
  subtotal: 999.00,      // After product discounts
  total: 979.10,         // After all discounts, shipping, tax
  discount: 99.90        // Total discount applied
}

// Affiliate Settings
const affiliate = {
  commissionRate: 10.5,
  commissionType: 'percentage'
}

// Commission Calculation
const commission = cart.total * (affiliate.commissionRate / 100)
// commission = 979.10 * 0.105 = 102.81
```

---

## UTM Tracking

### UTM Parameters Supported
- `utm_source` - Traffic source (instagram, facebook, email)
- `utm_medium` - Marketing medium (social, email, cpc)
- `utm_campaign` - Campaign name (summer_sale_2024)
- `utm_content` - Content variation (story_post, feed_post)
- `utm_term` - Keyword terms (summer_fashion)

### Tracking URL Generation
```typescript
const trackingUrl = await generateTrackingUrl({
  baseUrl: 'https://store.com/products/shoes',
  affiliateCode: 'SUMMER20',
  utmSource: 'instagram',
  utmMedium: 'social',
  utmCampaign: 'summer_sale_2024',
  utmContent: 'story_post'
})

// Result: https://store.com/products/shoes?ref=SUMMER20&utm_source=instagram&utm_medium=social&utm_campaign=summer_sale_2024&utm_content=story_post
```

### Analytics Available
- Click-through rates by UTM parameter
- Conversion rates by traffic source
- Campaign performance metrics
- Device and location analytics

---

## Testing

### API Testing
```bash
# Test commission preview
curl -X POST http://localhost:4000/api/affiliate/preview-commission \
  -H "Content-Type: application/json" \
  -d '{"cartId": 2510, "couponCode": "SKPC2025"}'

# Test commission validation
curl -X POST http://localhost:4000/api/affiliate/validate-commission \
  -H "Content-Type: application/json" \
  -d '{"cartId": 2510, "couponCode": "SKPC2025", "actualDiscountAmount": 102.81}'

# Test commission consistency
curl -X POST http://localhost:4000/api/affiliate/test-commission-consistency \
  -H "Content-Type: application/json" \
  -d '{"cartId": 2510, "orderId": 456, "couponCode": "SKPC2025"}'
```

### Unit Tests
```typescript
describe('AffiliateService', () => {
  it('should calculate commission correctly', async () => {
    const result = await affiliateService.previewCartCommission(cartId, couponCode)
    expect(result.estimatedCommission).toBe(102.81)
    expect(result.commissionableAmount).toBe(979.1)
  })
  
  it('should track UTM parameters', async () => {
    const click = await utmTrackingService.trackClick(trackingData)
    expect(click.utmSource).toBe('instagram')
    expect(click.utmCampaign).toBe('summer_sale_2024')
  })
})
```

---

## Key Features

### ✅ Multi-Discount Support
- Affiliates can have multiple active discount codes
- Individual tracking and usage limits per discount
- Flexible assignment and management

### ✅ Comprehensive UTM Tracking
- Full UTM parameter support
- Device, location, and browser tracking
- Campaign performance analytics

### ✅ Accurate Commission Calculation
- Consistent cart preview and order calculation
- Based on cart.total as requested
- Proper discount handling

### ✅ Conversion Funnel Analytics
- Click → Signup → Purchase tracking
- Time-to-conversion metrics
- Conversion rate optimization

### ✅ Admin Management Tools
- Application approval workflow
- Bulk commission processing
- Performance reporting
- Discount assignment management

### ✅ Country-Based Operations
- Country-specific affiliate management
- Geographic performance tracking
- Localized commission structures

This affiliate system provides a complete, scalable solution for affiliate marketing with advanced tracking, analytics, and management capabilities.

---

## Advanced Features

### 1. Real-time Analytics Dashboard
```typescript
// WebSocket integration for real-time updates
export const useRealtimeAffiliate = () => {
  const socket = useSocket()

  const subscribeToAffiliateUpdates = (affiliateId: number) => {
    socket.emit('subscribe:affiliate', affiliateId)

    socket.on('affiliate:click', (data) => {
      // Update click counter in real-time
      updateClickCount(data.clickCount)
    })

    socket.on('affiliate:conversion', (data) => {
      // Update conversion metrics
      updateConversionMetrics(data)
    })

    socket.on('affiliate:commission', (data) => {
      // Update commission earnings
      updateCommissionEarnings(data)
    })
  }
}
```

### 2. Advanced Filtering & Search
```vue
<template>
  <div class="advanced-filters">
    <!-- Date Range Filter -->
    <DateRangePicker
      v-model:start="filters.dateFrom"
      v-model:end="filters.dateTo"
      label="Performance Period"
    />

    <!-- Performance Metrics Filter -->
    <v-range-slider
      v-model="filters.conversionRate"
      label="Conversion Rate (%)"
      min="0"
      max="100"
      step="0.1"
    />

    <!-- Commission Range Filter -->
    <v-range-slider
      v-model="filters.commissionRange"
      label="Total Commissions ($)"
      min="0"
      max="10000"
      step="10"
    />

    <!-- UTM Source Filter -->
    <v-select
      v-model="filters.utmSources"
      :items="utmSources"
      label="Traffic Sources"
      multiple
      chips
    />

    <!-- Geographic Filter -->
    <CountrySelect
      v-model="filters.countries"
      label="Countries"
      multiple
    />
  </div>
</template>
```

### 3. Automated Commission Processing
```typescript
// Scheduled commission processing
export class CommissionProcessor {
  @Cron('0 0 * * *') // Daily at midnight
  async processCommissions() {
    const pendingCommissions = await this.getCommissions({
      status: 'pending',
      olderThan: '24h'
    })

    for (const commission of pendingCommissions) {
      await this.validateAndApprove(commission)
    }
  }

  @Cron('0 0 1 * *') // Monthly on 1st
  async generatePayouts() {
    const approvedCommissions = await this.getCommissions({
      status: 'approved',
      payoutReady: true
    })

    const payoutsByAffiliate = this.groupByAffiliate(approvedCommissions)

    for (const [affiliateId, commissions] of payoutsByAffiliate) {
      await this.createPayout(affiliateId, commissions)
    }
  }
}
```

### 4. A/B Testing for Affiliate Campaigns
```typescript
// Campaign A/B testing
export const useCampaignTesting = () => {
  const createABTest = async (campaignData: {
    name: string
    variants: CampaignVariant[]
    trafficSplit: number[]
    metrics: string[]
  }) => {
    return await $fetch('/api/affiliate/ab-tests', {
      method: 'POST',
      body: campaignData
    })
  }

  const getTestResults = async (testId: number) => {
    return await $fetch(`/api/affiliate/ab-tests/${testId}/results`)
  }
}

// Example A/B test configuration
const abTest = {
  name: 'Summer Campaign 2024',
  variants: [
    {
      name: 'Control',
      utmCampaign: 'summer_sale_control',
      discountRate: 10,
      creativeAssets: ['banner_a.jpg', 'copy_a.txt']
    },
    {
      name: 'Variant B',
      utmCampaign: 'summer_sale_variant_b',
      discountRate: 15,
      creativeAssets: ['banner_b.jpg', 'copy_b.txt']
    }
  ],
  trafficSplit: [50, 50],
  metrics: ['click_rate', 'conversion_rate', 'commission_per_click']
}
```

---

## Integration Examples

### 1. E-commerce Platform Integration
```typescript
// Cart integration
export const useAffiliateCart = () => {
  const applyAffiliateDiscount = async (cartId: number, couponCode: string) => {
    // Validate affiliate coupon
    const validation = await $fetch('/api/affiliate/validate-coupon', {
      method: 'POST',
      body: { couponCode }
    })

    if (validation.isAffiliateDiscount) {
      // Apply discount to cart
      await $fetch('/api/cart/apply-discount', {
        method: 'POST',
        body: { cartId, couponCode }
      })

      // Track affiliate click if not already tracked
      await $fetch('/api/affiliate/track-click', {
        method: 'POST',
        body: {
          affiliateCode: couponCode,
          sessionId: getSessionId(),
          landingPage: window.location.href,
          source: 'direct'
        }
      })
    }

    return validation
  }
}
```

### 2. Email Marketing Integration
```typescript
// Email campaign tracking
export const useEmailCampaign = () => {
  const generateEmailLinks = async (affiliateId: number, campaign: EmailCampaign) => {
    const links = []

    for (const product of campaign.products) {
      const trackingUrl = await $fetch('/api/affiliate/generate-tracking-url', {
        method: 'POST',
        body: {
          baseUrl: product.url,
          affiliateCode: campaign.couponCode,
          utmSource: 'email',
          utmMedium: 'newsletter',
          utmCampaign: campaign.name,
          utmContent: product.id
        }
      })

      links.push({
        productId: product.id,
        trackingUrl: trackingUrl.data.trackingUrl
      })
    }

    return links
  }
}
```

### 3. Social Media Integration
```typescript
// Social media post generation
export const useSocialMedia = () => {
  const generateSocialPost = async (affiliateId: number, product: Product) => {
    const affiliate = await getAffiliate(affiliateId)
    const trackingUrl = await generateTrackingUrl({
      baseUrl: product.url,
      affiliateCode: affiliate.couponCode,
      utmSource: 'instagram',
      utmMedium: 'social',
      utmCampaign: 'product_promotion',
      utmContent: 'feed_post'
    })

    return {
      caption: `Check out this amazing ${product.name}! Use my code ${affiliate.couponCode} for ${affiliate.discountRate}% off! 🛍️ #affiliate #discount #shopping`,
      trackingUrl: trackingUrl.data.trackingUrl,
      hashtags: ['#affiliate', '#discount', '#shopping', `#${product.category}`],
      callToAction: 'Shop Now'
    }
  }
}
```

---

## Performance Optimization

### 1. Database Indexing
```sql
-- Optimize affiliate queries
CREATE INDEX idx_affiliate_status ON affiliate_profiles(status);
CREATE INDEX idx_affiliate_country ON affiliate_profiles(country_id);
CREATE INDEX idx_affiliate_performance ON affiliate_profiles(total_commissions, conversion_rate);

-- Optimize click tracking
CREATE INDEX idx_click_affiliate_date ON affiliate_clicks(affiliate_id, created_at);
CREATE INDEX idx_click_utm_source ON affiliate_clicks(utm_source, utm_campaign);
CREATE INDEX idx_click_conversion ON affiliate_clicks(converted, conversion_date);

-- Optimize commission queries
CREATE INDEX idx_commission_affiliate_status ON affiliate_commissions(affiliate_id, status);
CREATE INDEX idx_commission_date_amount ON affiliate_commissions(created_at, commission_amount);

-- Optimize conversion tracking
CREATE INDEX idx_conversion_affiliate_type ON affiliate_conversions(affiliate_id, type);
CREATE INDEX idx_conversion_timeline ON affiliate_conversions(created_at, conversion_value);
```

### 2. Caching Strategy
```typescript
// Redis caching for performance
export class AffiliateCacheService {
  private redis = new Redis(process.env.REDIS_URL)

  async cacheAffiliateStats(affiliateId: number, stats: AffiliateStats) {
    const key = `affiliate:stats:${affiliateId}`
    await this.redis.setex(key, 300, JSON.stringify(stats)) // 5 min cache
  }

  async getCachedStats(affiliateId: number): Promise<AffiliateStats | null> {
    const key = `affiliate:stats:${affiliateId}`
    const cached = await this.redis.get(key)
    return cached ? JSON.parse(cached) : null
  }

  async cacheUTMAnalytics(affiliateId: number, analytics: UTMAnalytics) {
    const key = `affiliate:utm:${affiliateId}`
    await this.redis.setex(key, 600, JSON.stringify(analytics)) // 10 min cache
  }

  async invalidateAffiliateCache(affiliateId: number) {
    const pattern = `affiliate:*:${affiliateId}`
    const keys = await this.redis.keys(pattern)
    if (keys.length > 0) {
      await this.redis.del(...keys)
    }
  }
}
```

### 3. Background Job Processing
```typescript
// Queue-based processing for heavy operations
export class AffiliateJobProcessor {
  @Process('calculate-affiliate-stats')
  async calculateStats(job: Job<{ affiliateId: number }>) {
    const { affiliateId } = job.data

    // Heavy calculation in background
    const stats = await this.affiliateService.calculateDetailedStats(affiliateId)

    // Cache results
    await this.cacheService.cacheAffiliateStats(affiliateId, stats)

    // Notify frontend via WebSocket
    this.socketService.emit(`affiliate:${affiliateId}:stats-updated`, stats)
  }

  @Process('process-bulk-commissions')
  async processBulkCommissions(job: Job<{ commissionIds: number[] }>) {
    const { commissionIds } = job.data

    for (const commissionId of commissionIds) {
      await this.commissionService.processCommission(commissionId)

      // Update progress
      job.progress((commissionIds.indexOf(commissionId) + 1) / commissionIds.length * 100)
    }
  }
}
```

---

## Security & Compliance

### 1. Data Protection
```typescript
// GDPR compliance for affiliate data
export class AffiliateDataProtection {
  async exportAffiliateData(affiliateId: number): Promise<AffiliateDataExport> {
    const affiliate = await this.affiliateRepo.findOne({
      where: { id: affiliateId },
      relations: ['user', 'clicks', 'conversions', 'commissions']
    })

    return {
      personalData: {
        name: affiliate.user.firstName + ' ' + affiliate.user.lastName,
        email: affiliate.user.email,
        phone: affiliate.user.phone,
        country: affiliate.country?.name
      },
      affiliateData: {
        commissionRate: affiliate.commissionRate,
        totalEarnings: affiliate.totalCommissions,
        joinDate: affiliate.createdAt
      },
      activityData: {
        totalClicks: affiliate.clicks.length,
        totalConversions: affiliate.conversions.length,
        lastActivity: affiliate.lastActivityAt
      }
    }
  }

  async deleteAffiliateData(affiliateId: number, retentionPeriod: number = 7) {
    // Soft delete with retention period
    await this.affiliateRepo.update(affiliateId, {
      status: 'deleted',
      deletedAt: new Date(),
      dataRetentionUntil: new Date(Date.now() + retentionPeriod * 24 * 60 * 60 * 1000)
    })

    // Anonymize tracking data
    await this.anonymizeTrackingData(affiliateId)
  }
}
```

### 2. Fraud Detection
```typescript
// Fraud detection system
export class AffiliateFraudDetection {
  async detectSuspiciousActivity(affiliateId: number): Promise<FraudAlert[]> {
    const alerts: FraudAlert[] = []

    // Check for click fraud
    const clickPattern = await this.analyzeClickPattern(affiliateId)
    if (clickPattern.suspiciousIPs.length > 0) {
      alerts.push({
        type: 'click_fraud',
        severity: 'high',
        description: `Suspicious click pattern from ${clickPattern.suspiciousIPs.length} IPs`
      })
    }

    // Check for conversion fraud
    const conversionPattern = await this.analyzeConversionPattern(affiliateId)
    if (conversionPattern.unusuallyHighRate) {
      alerts.push({
        type: 'conversion_fraud',
        severity: 'medium',
        description: 'Unusually high conversion rate detected'
      })
    }

    // Check for commission fraud
    const commissionPattern = await this.analyzeCommissionPattern(affiliateId)
    if (commissionPattern.suspiciousOrders.length > 0) {
      alerts.push({
        type: 'commission_fraud',
        severity: 'high',
        description: `${commissionPattern.suspiciousOrders.length} suspicious orders detected`
      })
    }

    return alerts
  }
}
```

---

## Deployment & Monitoring

### 1. Health Checks
```typescript
// Health check endpoints
@Controller('health')
export class AffiliateHealthController {
  @Get('affiliate')
  async checkAffiliateHealth(): Promise<HealthStatus> {
    const checks = await Promise.allSettled([
      this.checkDatabase(),
      this.checkRedis(),
      this.checkCommissionCalculation(),
      this.checkUTMTracking()
    ])

    return {
      status: checks.every(check => check.status === 'fulfilled') ? 'healthy' : 'unhealthy',
      checks: checks.map((check, index) => ({
        name: ['database', 'redis', 'commission', 'utm'][index],
        status: check.status,
        message: check.status === 'fulfilled' ? 'OK' : check.reason
      })),
      timestamp: new Date().toISOString()
    }
  }
}
```

### 2. Monitoring & Alerts
```typescript
// Monitoring service
export class AffiliateMonitoring {
  @Cron('*/5 * * * *') // Every 5 minutes
  async monitorSystemHealth() {
    // Check commission calculation accuracy
    const accuracy = await this.checkCommissionAccuracy()
    if (accuracy < 0.95) {
      await this.sendAlert('Commission calculation accuracy below 95%')
    }

    // Check UTM tracking
    const utmHealth = await this.checkUTMTracking()
    if (!utmHealth.isHealthy) {
      await this.sendAlert('UTM tracking issues detected')
    }

    // Check conversion rates
    const conversionRates = await this.checkConversionRates()
    if (conversionRates.anomalies.length > 0) {
      await this.sendAlert(`Conversion rate anomalies: ${conversionRates.anomalies.join(', ')}`)
    }
  }
}
```

This comprehensive affiliate system documentation covers all aspects of implementation, from basic setup to advanced features, performance optimization, security, and monitoring. The system is designed to be scalable, secure, and feature-rich for modern affiliate marketing needs.
