import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  Req,
  ParseIntPipe,
  BadRequestException,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery, ApiBody } from '@nestjs/swagger';
import { Request } from 'express';
import { AffiliateService } from '../services/affiliate.service';
import { AffiliateTrackingService } from '../services/affiliate-tracking.service';
import { CreateAffiliateProfileDto } from '../dto/create-affiliate-profile.dto';
import { UpdateAffiliateDto } from '../dto/update-affiliate.dto';
import { ApproveApplicationDto } from '../dto/approve-application.dto';
import { RejectApplicationDto } from '../dto/reject-application.dto';
import { ProcessPaymentsDto } from '../dto/process-payments.dto';
import { MarkCommissionPaidDto } from '../dto/mark-commission-paid.dto';
import { AssignDiscountToAffiliateDto } from '../dto/assign-discount.dto';
import { AssignAffiliateDiscountDto, UpdateAffiliateDiscountDto } from '../dto/assign-affiliate-discount.dto';
import { TrackClickDto } from '../dto/track-click.dto';
import { TrackConversionDto } from '../dto/track-conversion.dto';
import { AffiliateOrderIntegrationService } from '../services/affiliate-order-integration.service';
import { UTMTrackingService } from '../services/utm-tracking.service';
import { ConversionTrackingService } from '../services/conversion-tracking.service';
import { AffiliateDiscountCommissionService } from '../services/affiliate-discount-commission.service';
import { PageOptionsDto } from 'src/common/pagination/page-options.dto';
import { GenerateTrackingUrlDto } from '../dto/generate-tracking-url.dto';
import { ServiceResponse } from 'src/common/utils/service-response';
import { AffiliateStatus } from '../entities/affiliate-profile.entity';
// import { JwtAuthGuard } from '../../../auth/guards/jwt-auth.guard';
// import { RolesGuard } from '../../../auth/guards/roles.guard';
// import { Roles } from '../../../auth/decorators/roles.decorator';

@ApiTags('Affiliate')
@Controller('affiliate')
export class AffiliateController {
  constructor(
    private readonly affiliateService: AffiliateService,
    private readonly affiliateTrackingService: AffiliateTrackingService,
    private readonly affiliateOrderIntegrationService: AffiliateOrderIntegrationService,
    private readonly utmTrackingService: UTMTrackingService,
    private readonly conversionTrackingService: ConversionTrackingService,
    private readonly affiliateDiscountCommissionService: AffiliateDiscountCommissionService,
  ) { }


  // Application Management Endpoints
  @Get('applications')
  @ApiOperation({ summary: 'Get all affiliate applications (Admin only) [checked, working]✅' })
  @ApiResponse({ status: 200, description: 'Applications retrieved successfully' })
  async getApplications(
    @Query('status') status?: AffiliateStatus,
    @Query() pageOptionsDto?: PageOptionsDto,
  ) {
    console.log('getApplications:::', { status });
    return await this.affiliateService.getApplications({ status }, pageOptionsDto);
  }

  @Post('applications/:id/approve')
  @ApiOperation({ summary: 'Approve affiliate application (Admin only) [checked, working] [checked, working]✅' })
  @ApiResponse({ status: 200, description: 'Application approved successfully' })
  @ApiResponse({ status: 404, description: 'Application not found' })
  async approveApplication(
    @Param('id', ParseIntPipe) id: number,
    @Body() approvalData: ApproveApplicationDto
  ) {
    return await this.affiliateService.approveApplication(id, approvalData);
  }

  @Post('applications/:id/reject')
  @ApiOperation({ summary: 'Reject affiliate application (Admin only)✅' })
  @ApiResponse({ status: 200, description: 'Application rejected successfully' })
  @ApiResponse({ status: 404, description: 'Application not found' })
  async rejectApplication(
    @Param('id', ParseIntPipe) id: number,
    @Body() rejectionData: RejectApplicationDto
  ) {
    return await this.affiliateService.rejectApplication(id, rejectionData.reason);
  }

  // Commission Management Endpoints
  @Get('commissions')
  @ApiOperation({ summary: 'Get all affiliate commissions (Admin only) [checked, working]✅' })
  @ApiQuery({ name: 'status', required: false, type: String })
  @ApiQuery({ name: 'affiliateId', required: false, type: Number })
  @ApiQuery({ name: 'dateFrom', required: false, type: String })
  @ApiQuery({ name: 'dateTo', required: false, type: String })
  @ApiResponse({ status: 200, description: 'Commissions retrieved successfully' })
  async getCommissions(
    @Query('status') status?: string,
    @Query('affiliateId') affiliateId?: number,
    @Query('dateFrom') dateFrom?: string,
    @Query('dateTo') dateTo?: string,
    @Query() pageOptionsDto?: PageOptionsDto,
  ) {
    return await this.affiliateService.getCommissions(
      {
        status,
        affiliateId,
        dateFrom,
        dateTo
      },
      pageOptionsDto);
  }

  // Commission Affiliate Endpoints
  @Get('web/commissions/:affiliateId/findDataByPagination')
  @ApiOperation({ summary: 'Get all affiliate commissions (Admin only) [checked, working]✅' })
  @ApiParam({ name: 'affiliateId', required: true, type: Number })
  @ApiQuery({ name: 'status', required: false, type: String })
  @ApiQuery({ name: 'dateFrom', required: false, type: String })
  @ApiQuery({ name: 'dateTo', required: false, type: String })
  @ApiResponse({ status: 200, description: 'Commissions retrieved successfully' })
  async getAffiliateCommissions(
    @Param('affiliateId') affiliateId?: number,
    @Query('status') status?: string,
    @Query('dateFrom') dateFrom?: string,
    @Query('dateTo') dateTo?: string,
    @Query() pageOptionsDto?: PageOptionsDto,
  ) {
    return this.affiliateService.getAffiliateCommissions(
      {
        status,
        affiliateId,
        dateFrom,
        dateTo
      },
      pageOptionsDto);
  }

  @Post('commissions/:id/approve')
  @ApiOperation({ summary: 'Approve commission (Admin only) [checked, working]' })
  @ApiResponse({ status: 200, description: 'Commission approved successfully' })
  @ApiResponse({ status: 404, description: 'Commission not found' })
  async approveCommission(@Param('id', ParseIntPipe) id: number) {
    return await this.affiliateService.approveCommission(id);
  }

  @Post('commissions/:id/reject')
  @ApiOperation({ summary: 'Reject commission (Admin only) [checked, working]' })
  @ApiResponse({ status: 200, description: 'Commission rejected successfully' })
  @ApiResponse({ status: 404, description: 'Commission not found' })
  async rejectCommission(
    @Param('id', ParseIntPipe) id: number,
    @Body('reason') reason: string
  ) {
    return await this.affiliateService.rejectCommission(id, reason);
  }

  @Post('commissions/:id/pay')
  @ApiOperation({ summary: 'Mark commission as paid (Admin only)' })
  @ApiResponse({ status: 200, description: 'Commission marked as paid successfully' })
  @ApiResponse({ status: 404, description: 'Commission not found' })
  async markCommissionAsPaid(
    @Param('id', ParseIntPipe) id: number,
    @Body() paymentData: MarkCommissionPaidDto
  ) {
    return await this.affiliateService.markCommissionAsPaid(id, paymentData);
  }

  @Post('commissions/process-payments')
  @ApiOperation({ summary: 'Process multiple commission payments (Admin only)' })
  @ApiResponse({ status: 200, description: 'Payments processed successfully' })
  async processMultiplePayments(
    @Body() paymentData: ProcessPaymentsDto
  ) {
    return await this.affiliateService.processMultiplePayments(paymentData);
  }

  // Performance Reports Endpoints
  @Get('reports/performance')
  @ApiOperation({ summary: 'Get affiliate performance reports (Admin only) [checked, working] need to add filters ✅' })
  @ApiResponse({ status: 200, description: 'Performance reports retrieved successfully' })
  async getPerformanceReports(
    @Query('affiliateId') affiliateId?: number,
    @Query('dateFrom') dateFrom?: string,
    @Query('dateTo') dateTo?: string,
    // @Query('groupBy') groupBy?: string
  ) {
    return await this.affiliateService.getPerformanceReports({
      affiliateId,
      dateFrom,
      dateTo,
      // groupBy
    });
  }

  // Main affiliate CRUD endpoints
  @Get('profile/by/user/:userId')
  @ApiOperation({ summary: 'Get affiliate by ID with comprehensive statistics and relations ✅' })
  @ApiResponse({ status: 200, description: 'Affiliate retrieved successfully with comprehensive data including usage statistics, commissions, clicks, and recent activity' })
  @ApiResponse({ status: 404, description: 'Affiliate not found' })
  async getAffiliateByUserId(@Param('userId', ParseIntPipe) userId: number) {
    return await this.affiliateService.getAffiliateByUserId(userId);
  }

  @Get()
  @ApiOperation({ summary: 'Get all affiliates (Admin only) [checked, working] ✅' })
  @ApiResponse({ status: 200, description: 'Affiliates retrieved successfully' })
  @ApiQuery({ name: 'status', required: false, type: String })
  @ApiQuery({ name: 'search', required: false, type: String })
  @ApiQuery({ name: 'countryId', required: false, type: Number })
  @ApiResponse({ status: 400, description: 'Bad request' })
  async getAllAffiliates(
    @Query('status') status?: string,
    @Query('search') search?: string,
    @Query('countryId') countryId?: number,
    @Query('commissionType') commissionType?: string,
    @Query() pageOptionsDto?: PageOptionsDto,
  ) {
    return await this.affiliateService.getAllAffiliates({ status, search, countryId, commissionType }, pageOptionsDto);
  }

  @Get('by-code/:affiliateCode')
  @ApiOperation({ summary: 'Get affiliate by affiliate code ✅' })
  @ApiResponse({ status: 200, description: 'Affiliate retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Affiliate not found' })
  @ApiParam({ name: 'affiliateCode', description: 'Unique affiliate code', example: 'AFF123456' })
  async getAffiliateByCode(@Param('affiliateCode') affiliateCode: string) {
    return await this.affiliateService.getAffiliateByCode(affiliateCode);
  }

  @Get('check-code/:affiliateCode')
  @ApiOperation({ summary: 'Check if affiliate code is available ✅' })
  @ApiResponse({ status: 200, description: 'Code availability checked successfully' })
  @ApiParam({ name: 'affiliateCode', description: 'Affiliate code to check', example: 'AFF123456' })
  async checkAffiliateCodeAvailability(@Param('affiliateCode') affiliateCode: string) {
    return await this.affiliateService.checkAffiliateCodeAvailability(affiliateCode);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get affiliate by ID with comprehensive statistics and relations ✅' })
  @ApiResponse({ status: 200, description: 'Affiliate retrieved successfully with comprehensive data including usage statistics, commissions, clicks, and recent activity' })
  @ApiResponse({ status: 404, description: 'Affiliate not found' })
  async getAffiliateById(@Param('id', ParseIntPipe) id: number) {
    return await this.affiliateService.getAffiliateById(id);
  }

  @Get(':id/basic')
  @ApiOperation({ summary: 'Get affiliate by ID with basic relations (lightweight) ✅' })
  @ApiResponse({ status: 200, description: 'Affiliate retrieved successfully with basic relations only' })
  @ApiResponse({ status: 404, description: 'Affiliate not found' })
  async getAffiliateByIdBasic(@Param('id', ParseIntPipe) id: number) {
    return await this.affiliateService.getAffiliateByIdBasic(id);
  }

  @Post('register')
  @ApiOperation({ summary: 'Register as an affiliate [checked, working] [checked, working]✅' })
  @ApiResponse({ status: 201, description: 'Affiliate profile created successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  async registerAffiliate(@Body() dto: CreateAffiliateProfileDto) {
    console.log('registerAffiliate:::', dto);
    return await this.affiliateService.createAffiliateProfile(dto);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update affiliate (Admin only) [checked, working]' })
  @ApiResponse({ status: 200, description: 'Affiliate updated successfully' })
  @ApiResponse({ status: 404, description: 'Affiliate not found' })
  async updateAffiliate(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateData: UpdateAffiliateDto
  ) {
    return await this.affiliateService.updateAffiliateProfile(id, updateData);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete affiliate (Admin only) [checked, working]✅' })
  @ApiResponse({ status: 200, description: 'Affiliate deleted successfully' })
  @ApiResponse({ status: 404, description: 'Affiliate not found' })
  async softDeleteAffiliateProfile(@Param('id', ParseIntPipe) id: number) {
    return await this.affiliateService.softDeleteAffiliateProfile(id);
  }

  @Post('assign-discount')
  @ApiOperation({ summary: 'Assign discount to affiliate (Admin only) [checked, working]' })
  @ApiResponse({ status: 201, description: 'Discount assigned successfully' })
  // @UseGuards(JwtAuthGuard, RolesGuard)
  // @Roles('admin')
  // @ApiBearerAuth()
  async assignDiscount(@Body() dto: AssignDiscountToAffiliateDto) {
    console.log('assignDiscount:::', dto);
    return await this.affiliateService.assignDiscountToAffiliate(dto);
  }


  /*************  ✨ Windsurf Command ⭐  *************/
  /**
   * Track affiliate click
   * @param dto The TrackClickDto object containing the click data
   * @param request The Express request object
   * @returns A ServiceResponse object with the created click entity
   * @throws BadRequestException if required fields are not provided
   * @throws NotFoundException if affiliate not found
   */
  /*******  f63cd765-4dd0-4707-8ac4-c8e8a7be80c3  *******/
  @Post('track-click')
  @ApiOperation({ summary: 'Track affiliate click [checked, working]' })
  @ApiResponse({ status: 201, description: 'Click tracked successfully' })
  async trackClick(@Body() dto: TrackClickDto, @Req() request: Request) {
    return await this.affiliateService.trackClick(dto, request);
  }

  @Post('track-conversion')
  @ApiOperation({ summary: 'Track affiliate conversion ✅' })
  @ApiResponse({ status: 201, description: 'Conversion tracked successfully' })
  @ApiResponse({ status: 404, description: 'Affiliate not found' })
  async trackConversion(@Body() dto: TrackConversionDto, @Req() request: Request) {
    return await this.conversionTrackingService.trackConversion({
      affiliateCode: dto.affiliateCode,
      sessionId: dto.sessionId,
      orderId: dto.orderId,
      type: dto.type,
      conversionValue: dto.conversionValue,
      customerEmail: dto.customerEmail,
      customerPhone: dto.customerPhone,
      conversionPage: dto.conversionPage,
      couponUsed: dto.couponUsed,
      discountApplied: dto.discountApplied,
      commissionEarned: dto.commissionEarned,
      ipAddress: request.ip || request.socket?.remoteAddress,
      userAgent: request.headers['user-agent'],
    });
  }

  @Post('test-order-conversion/:orderId')
  @ApiOperation({ summary: 'Test order conversion tracking (for debugging) ✅' })
  @ApiResponse({ status: 200, description: 'Order conversion test completed' })
  @ApiParam({ name: 'orderId', description: 'Order ID to test conversion tracking', example: 1 })
  async testOrderConversion(@Param('orderId', ParseIntPipe) orderId: number) {
    try {
      // This is a test endpoint to manually trigger conversion tracking
      const result = await this.conversionTrackingService.trackOrderConversion({
        orderId,
        sessionId: `test_session_${Date.now()}`,
        ipAddress: '127.0.0.1',
        userAgent: 'Test User Agent',
        conversionPage: '/test/conversion'
      });

      return new ServiceResponse(result, 'Order conversion test completed');
    } catch (error) {
      return new ServiceResponse(null, `Order conversion test failed: ${error.message}`);
    }
  }

  /**
   * Generate an affiliate link that will track clicks and assign commissions to the affiliate
   * @param affiliateCode The affiliate's coupon code
   * @param targetUrl The target URL to redirect to
   * @param utmSource Optional UTM source parameter
   * @param utmMedium Optional UTM medium parameter
   * @param utmCampaign Optional UTM campaign parameter
   * @returns A URL string that can be used as an affiliate link
   * @throws BadRequestException if targetUrl is not provided
   */
  @ApiOperation({ summary: 'Generate affiliate link [checked, working]' })
  @ApiParam({
    name: 'affiliateCode',
    description: 'Affiliate code',
    example: 'JOHN2024',
  })
  @ApiQuery({
    name: 'url',
    description: 'Target URL',
    example: 'https://yourstore.com/products/summer-collection',
  })
  @ApiQuery({
    name: 'utm_source',
    description: 'UTM source',
    example: 'google',
  })
  @ApiQuery({
    name: 'utm_medium',
    description: 'UTM medium',
    example: 'cpc',
  })
  @ApiQuery({
    name: 'utm_campaign',
    description: 'UTM campaign',
    example: 'summer2024',
  })
  @ApiResponse({ status: 200, description: 'Affiliate link generated' })
  @Get('generate-link/:affiliateCode')
  async generateLink(
    @Param('affiliateCode') affiliateCode: string,
    @Query('url') targetUrl: string,
    @Query('utm_source') utmSource?: string,
    @Query('utm_medium') utmMedium?: string,
    @Query('utm_campaign') utmCampaign?: string,
  ) {
    if (!targetUrl) {
      throw new BadRequestException('Target URL is required');
    }

    const utmParams = {
      utm_source: utmSource,
      utm_medium: utmMedium,
      utm_campaign: utmCampaign,
    };

    const link = this.affiliateService.generateAffiliateLink(
      targetUrl,
      affiliateCode, // This is actually the coupon code now
      utmParams
    );

    return link;
  }

  @Post('generate-tracking-url')
  @ApiOperation({ summary: 'Generate affiliate tracking URL with UTM parameters [checked, working]' })
  @ApiResponse({ status: 200, description: 'Tracking URL generated successfully' })
  async generateTrackingUrl(@Body() body: GenerateTrackingUrlDto) {
    const utm = {
      utmSource: body.utmSource,
      utmMedium: body.utmMedium,
      utmCampaign: body.utmCampaign,
      utmContent: body.utmContent,
      utmTerm: body.utmTerm,
    };

    const trackingUrl = await this.utmTrackingService.generateTrackingUrl(
      body.baseUrl,
      body.affiliateCode,
      utm,
      body.additionalParams
    );

    return trackingUrl
  }

  @Get('profile/:couponCode')
  @ApiOperation({ summary: 'Get affiliate profile by coupon code [checked, working]' })
  @ApiResponse({ status: 200, description: 'Affiliate profile retrieved' })
  async getAffiliateProfile(@Param('couponCode') couponCode: string) {
    const profile = await this.affiliateService.getAffiliateProfile(couponCode);

    return profile;
  }

  @Get('dashboard/:affiliateId')
  @ApiOperation({ summary: 'Get affiliate dashboard data [checked, working]' })
  @ApiResponse({ status: 200, description: 'Dashboard data retrieved' })
  // @UseGuards(JwtAuthGuard)
  // @ApiBearerAuth()
  async getAffiliateDashboard(@Param('affiliateId', ParseIntPipe) affiliateId: number) {
    // const discounts = await this.affiliateTrackingService.getAffiliateDiscounts(affiliateId);
    // const stats = await this.affiliateTrackingService.getAffiliateDiscountStats(affiliateId);

    // return {
    //   discounts,
    //   stats,
    //   message: 'Dashboard data retrieved successfully'
    // };
    return await this.affiliateTrackingService.getAffiliateDashboardStats(affiliateId);
  }

  @Get('discounts/:affiliateId')
  @ApiOperation({ summary: 'Get affiliate discount assignments [checked, working]' })
  @ApiResponse({ status: 200, description: 'Discount assignments retrieved' })
  async getAffiliateDiscounts(@Param('affiliateId', ParseIntPipe) affiliateId: number) {
    return await this.affiliateService.getAffiliateDiscounts(affiliateId);
  }

  @Get('stats/:affiliateId')
  @ApiOperation({ summary: 'Get affiliate statistics [checked, working]' })
  @ApiResponse({ status: 200, description: 'Statistics retrieved' })
  async getAffiliateStats(
    @Param('affiliateId', ParseIntPipe) affiliateId: number,
    @Query('discountId') discountId?: number
  ) {
    return await this.affiliateTrackingService.getAffiliateDiscountStats(
      affiliateId,
      discountId
    );
  }

  @Get('validate/:affiliateId/:discountId')
  @ApiOperation({ summary: 'Validate affiliate discount assignment [checked, working]' })
  @ApiResponse({ status: 200, description: 'Validation result' })
  async validateAffiliateDiscount(
    @Param('affiliateId', ParseIntPipe) affiliateId: number,
    @Param('discountId', ParseIntPipe) discountId: number
  ) {
    return this.affiliateTrackingService.validateAffiliateDiscount(
      affiliateId,
      discountId
    );
  }

  @Get('by-coupon/:couponCode')
  @ApiOperation({ summary: 'Get affiliate by coupon code [checked, working]' })
  @ApiResponse({ status: 200, description: 'Affiliate found' })
  async getAffiliateByCoupon(@Param('couponCode') couponCode: string) {
    // const affiliate = await this.affiliateTrackingService.getAffiliateByDiscountCode(couponCode);

    // if (!affiliate) {
    //   return { affiliate: null, message: 'No affiliate found for this coupon' };
    // }

    // return { affiliate };
    return await this.affiliateTrackingService.getAffiliateByDiscountCode(couponCode);
  }

  @Get('preview-commission/:cartId/:couponCode')
  @ApiOperation({ summary: 'Preview commission for cart with discount applied [checked, working]' })
  @ApiResponse({ status: 200, description: 'Commission preview' })
  async previewCartCommission(
    @Param('cartId', ParseIntPipe) cartId: number,
    @Param('couponCode') couponCode: string
  ) {
    return await this.affiliateOrderIntegrationService.getCartCommissionPreview(cartId, couponCode);
  }

  // Affiliate Discount Management Endpoints
  @Post('discounts/assign')
  @ApiOperation({ summary: 'Assign a discount to an affiliate (Admin only)✅' })
  @ApiResponse({ status: 201, description: 'Discount assigned successfully' })
  async assignDiscountToAffiliate(@Body() dto: AssignAffiliateDiscountDto) {
    return await this.affiliateService.assignDiscountToAffiliate(dto);
  }

  @Post('discounts/:id/approve')
  @ApiOperation({ summary: 'Approve affiliate discount application (Admin only)✅' })
  @ApiResponse({ status: 200, description: 'Application approved successfully' })
  @ApiResponse({ status: 404, description: 'Application not found' })
  async approveDiscountApplication(
    @Param('id', ParseIntPipe) id: number,
    @Body() dto: ApproveApplicationDto
  ) {
    return await this.affiliateService.approveDiscountApplication(id, dto);
  }

  @Post('discounts/:id/reject')
  @ApiOperation({ summary: 'Reject affiliate discount application (Admin only)✅' })
  @ApiResponse({ status: 200, description: 'Application rejected successfully' })
  @ApiResponse({ status: 404, description: 'Application not found' })
  async rejectDiscountApplication(
    @Param('id', ParseIntPipe) id: number,
    @Body('reason') reason: string
  ) {
    return await this.affiliateService.rejectDiscountApplication(id, reason);
  }

  @Put('discounts/:id')
  @ApiOperation({ summary: 'Update affiliate discount assignment (Admin only)' })
  @ApiParam({ name: 'id', description: 'Affiliate discount assignment ID' })
  @ApiResponse({ status: 200, description: 'Assignment updated successfully' })
  async updateAffiliateDiscount(
    @Param('id', ParseIntPipe) id: number,
    @Body() dto: UpdateAffiliateDiscountDto
  ) {
    return await this.affiliateService.updateAffiliateDiscount(id, dto);
  }

  @Delete('discounts/:id')
  @ApiOperation({ summary: 'Remove discount from affiliate (Admin only)' })
  @ApiParam({ name: 'id', description: 'Affiliate discount assignment ID' })
  @ApiResponse({ status: 200, description: 'Discount removed successfully' })
  async removeDiscountFromAffiliate(@Param('id', ParseIntPipe) id: number) {
    return await this.affiliateService.removeDiscountFromAffiliate(id);
  }
  
  // UTM Analytics & Conversion Tracking Endpoints
  @Get(':affiliateId/utm-analytics')
  @ApiOperation({ summary: 'Get UTM performance analytics for affiliate [checked, working]' })
  @ApiParam({ name: 'affiliateId', description: 'Affiliate ID' })
  @ApiQuery({ name: 'dateFrom', required: false, type: String, description: 'Start date (YYYY-MM-DD)' })
  @ApiQuery({ name: 'dateTo', required: false, type: String, description: 'End date (YYYY-MM-DD)' })
  @ApiResponse({ status: 200, description: 'UTM analytics retrieved successfully' })
  async getUTMAnalytics(
    @Param('affiliateId', ParseIntPipe) affiliateId: number,
    @Query('dateFrom') dateFrom?: string,
    @Query('dateTo') dateTo?: string
  ) {
    const dateFromObj = dateFrom ? new Date(dateFrom) : undefined;
    const dateToObj = dateTo ? new Date(dateTo) : undefined;
    return await this.utmTrackingService.getUTMAnalytics(affiliateId, dateFromObj, dateToObj);
  }

  @Get(':affiliateId/conversion-funnel')
  @ApiOperation({ summary: 'Get conversion funnel analytics for affiliate [checked, working]' })
  @ApiParam({ name: 'affiliateId', description: 'Affiliate ID' })
  @ApiQuery({ name: 'dateFrom', required: false, type: String, description: 'Start date (YYYY-MM-DD)' })
  @ApiQuery({ name: 'dateTo', required: false, type: String, description: 'End date (YYYY-MM-DD)' })
  @ApiResponse({ status: 200, description: 'Conversion funnel retrieved successfully' })
  async getConversionFunnel(
    @Param('affiliateId', ParseIntPipe) affiliateId: number,
    @Query('dateFrom') dateFrom?: string,
    @Query('dateTo') dateTo?: string
  ) {
    const dateFromObj = dateFrom ? new Date(dateFrom) : undefined;
    const dateToObj = dateTo ? new Date(dateTo) : undefined;
    return await this.conversionTrackingService.getConversionFunnel(affiliateId, dateFromObj, dateToObj);
  }

  @Get(':affiliateId/conversion-timeline')
  @ApiOperation({ summary: 'Get conversion timeline for affiliate [checked, working]' })
  @ApiParam({ name: 'affiliateId', description: 'Affiliate ID' })
  @ApiQuery({ name: 'days', required: false, type: Number, description: 'Number of days (default: 30)' })
  @ApiResponse({ status: 200, description: 'Conversion timeline retrieved successfully' })
  async getConversionTimeline(
    @Param('affiliateId', ParseIntPipe) affiliateId: number,
    @Query('days') days?: number
  ) {
    return await this.conversionTrackingService.getConversionTimeline(affiliateId, days || 30);
  }


  // Commission Calculation & Validation Endpoints
  @Post('validate-commission')
  @ApiOperation({ summary: 'Validate affiliate commission calculation [checked, working] need to check' })
  @ApiBody({
    description: 'Cart and coupon details',
    required: true,
    schema: {
      properties: {
        cartId: {
          type: 'number',
          description: 'Cart ID',
          example: 123,
        },
        couponCode: {
          type: 'string',
          description: 'Coupon code',
          example: 'SUMMER20',
        },
        actualDiscountAmount: {
          type: 'number',
          description: 'Actual discount amount (optional)',
          example: 10.5,
        },
      },
    }
  })
  @ApiResponse({ status: 200, description: 'Commission validation result' })
  async validateCommissionCalculation(@Body() body: {
    cartId: number;
    couponCode: string;
    actualDiscountAmount?: number;
  }) {
    return await this.affiliateDiscountCommissionService.validateCommissionCalculation(
      body.cartId,
      body.couponCode,
      body.actualDiscountAmount
    );
  }

  @Post('preview-commission')
  @ApiOperation({ summary: 'Preview commission for cart with affiliate discount [checked, working] need to check' })
  @ApiBody({
    description: 'Cart and coupon details',
    required: true,
    schema: {
      properties: {
        cartId: {
          type: 'number',
          description: 'Cart ID',
          example: 123,
        },
        couponCode: {
          type: 'string',
          description: 'Coupon code',
          example: 'SUMMER20',
        },
      },
    }
  })
  @ApiResponse({ status: 200, description: 'Commission preview calculated' })
  async previewCommission(@Body() body: {
    cartId: number;
    couponCode: string;
  }) {
    const preview = await this.affiliateDiscountCommissionService.previewCartCommission(
      body.cartId,
      body.couponCode
    );

    if (!preview) {
      return {
        success: false,
        message: 'Unable to calculate commission preview'
      };
    }

    return {
      success: true,
      data: preview
    };
  }

  @Post('calculate-order-commission')
  @ApiOperation({ summary: 'Calculate commission for completed order' })
  @ApiBody({
    description: 'Order details',
    required: true,
    schema: {
      properties: {
        orderId: {
          type: 'number',
          description: 'Order ID',
          example: 456,
        },
      },
    }
  })
  @ApiResponse({ status: 200, description: 'Order commission calculated' })
  async calculateOrderCommission(@Body() body: {
    orderId: number;
  }) {
    const result = await this.affiliateDiscountCommissionService.calculateOrderCommission(
      body.orderId
    );

    return {
      success: result.success,
      data: result,
      message: result.message
    };
  }

  @Post('test-commission-consistency')
  @ApiOperation({ summary: 'Test commission calculation consistency between cart and order' })
  @ApiBody({
    description: 'Cart, order, and coupon details',
    required: true,
    schema: {
      properties: {
        cartId: {
          type: 'number',
          description: 'Cart ID',
          example: 123,
        },
        orderId: {
          type: 'number',
          description: 'Order ID',
          example: 456,
        },
        couponCode: {
          type: 'string',
          description: 'Coupon code',
          example: 'SUMMER20',
        },
      },
    }
  })
  @ApiResponse({ status: 200, description: 'Commission consistency test result' })
  async testCommissionConsistency(@Body() body: {
    cartId: number;
    orderId: number;
    couponCode: string;
  }): Promise<ServiceResponse> {
    try {
      // Get cart commission preview
      const cartPreview = await this.affiliateDiscountCommissionService.previewCartCommission(
        body.cartId,
        body.couponCode
      );

      // Get order commission calculation
      const orderResult = await this.affiliateDiscountCommissionService.calculateOrderCommission(
        body.orderId
      );

      if (!cartPreview || !orderResult.success) {
        return new ServiceResponse({
          success: false,
          message: 'Unable to calculate one or both commission values',
          cartPreview,
          orderResult
        }, 'Unable to calculate one or both commission values')
      }

      // Compare the calculations
      const cartCommission = cartPreview.estimatedCommission;
      const orderCommission = orderResult.commissionAmount;
      const difference = Math.abs(cartCommission - orderCommission);
      const tolerance = 0.01; // 1 cent tolerance

      const isConsistent = difference <= tolerance;

      return new ServiceResponse({
        success: true,
        isConsistent,
        cartPreview: {
          commission: cartCommission,
          commissionableAmount: cartPreview.commissionableAmount,
          cartSubtotal: cartPreview.cartSubtotal,
          cartTotal: cartPreview.cartTotal,
          discountAmount: cartPreview.discountAmount
        },
        orderResult: {
          commission: orderCommission,
          commissionableAmount: orderResult.commissionableAmount,
          orderAmount: orderResult.orderAmount,
          orderSubtotal: orderResult.orderSubtotal,
          discountAmount: orderResult.discountAmount
        },
        comparison: {
          commissionDifference: difference,
          tolerance,
          isWithinTolerance: isConsistent,
          percentageDifference: cartCommission > 0 ? ((difference / cartCommission) * 100).toFixed(2) + '%' : '0%'
        },
        message: isConsistent
          ? 'Commission calculations are consistent'
          : `Commission calculations differ by ${difference.toFixed(2)}`
      }, 'Commission consistency test result');

    } catch (error) {
      return new ServiceResponse({
        success: false,
        message: `Error testing commission consistency: ${error.message}`
      }, `Error: ${error.message}`);
    }
  }

  // Affiliate Statistics Management Endpoints

  @Post(':id/recalculate-statistics')
  @ApiOperation({ summary: 'Recalculate affiliate statistics from existing data' })
  @ApiParam({ name: 'id', description: 'Affiliate ID' })
  @ApiResponse({ status: 200, description: 'Statistics recalculated successfully' })
  async recalculateAffiliateStatistics(@Param('id', ParseIntPipe) id: number) {
    try {
      return await this.affiliateOrderIntegrationService.recalculateAffiliateStatistics(id);
    } catch (error) {
      return new ServiceResponse(null, `Error recalculating statistics: ${error.message}`);
    }
  }

  @Post('recalculate-all-statistics')
  @ApiOperation({ summary: 'Recalculate statistics for all affiliates (Admin only)' })
  @ApiResponse({ status: 200, description: 'Bulk statistics recalculation completed' })
  async recalculateAllAffiliateStatistics() {
    try {
      return await this.affiliateOrderIntegrationService.recalculateAllAffiliateStatistics();
    } catch (error) {
      return new ServiceResponse(null, `Error in bulk recalculation: ${error.message}`);
    }
  }

  @Get(':id/earnings-summary')
  @ApiOperation({ summary: 'Get comprehensive affiliate earnings summary' })
  @ApiParam({ name: 'id', description: 'Affiliate ID' })
  @ApiResponse({ status: 200, description: 'Earnings summary retrieved' })
  async getAffiliateEarningsSummary(@Param('id', ParseIntPipe) id: number) {
    try {
      return await this.affiliateOrderIntegrationService.getAffiliateEarningsSummary(id);
    } catch (error) {
      return new ServiceResponse(null, `Error getting earnings summary: ${error.message}`);
    }
  }

  @Get(':id/usage-statistics')
  @ApiOperation({ summary: 'Get detailed affiliate statistics based on discount usage tracking' })
  @ApiParam({ name: 'id', description: 'Affiliate ID' })
  @ApiResponse({ status: 200, description: 'Usage statistics retrieved' })
  async getAffiliateUsageStatistics(@Param('id', ParseIntPipe) id: number) {
    try {
      return await this.affiliateOrderIntegrationService.getAffiliateUsageStatistics(id);
    } catch (error) {
      return new ServiceResponse(null, `Error getting usage statistics: ${error.message}`);
    }
  }

  @Get(':id/discounts/:discountId/statistics')
  @ApiOperation({ summary: 'Get statistics for a specific affiliate discount ✅' })
  @ApiParam({ name: 'id', description: 'Affiliate ID' })
  @ApiParam({ name: 'discountId', description: 'Affiliate Discount ID' })
  @ApiResponse({ status: 200, description: 'Affiliate discount statistics retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Affiliate discount not found' })
  async getAffiliateDiscountStatistics(
    @Param('id', ParseIntPipe) id: number,
    @Param('discountId', ParseIntPipe) discountId: number
  ) {
    return await this.affiliateService.getAffiliateDiscountStatistics(id, discountId);
  }

}
