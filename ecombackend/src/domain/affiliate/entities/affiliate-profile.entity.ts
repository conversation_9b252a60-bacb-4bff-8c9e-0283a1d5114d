import { <PERSON><PERSON><PERSON>, <PERSON>umn, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>o<PERSON><PERSON> } from 'typeorm';
import { AbstractEntity } from '../../entities/abstract.entity';
import { User } from '../../entities/user.entity';
import { Country } from '../../entities/country.entity';
import { AffiliateCommission } from './affiliate-commission.entity';
import { AffiliateClick } from './affiliate-click.entity';
import { AffiliateConversion } from './affiliate-conversion.entity';
import { AffiliateDiscount } from './affiliate-discount.entity';

export enum AffiliateStatus {
  PENDING = 'pending',
  ACTIVE = 'active',
  SUSPENDED = 'suspended',
  REJECTED = 'rejected',
}

export enum CommissionType {
  PERCENTAGE = 'percentage',
  FIXED = 'fixed',
}

@Entity('affiliate_profiles')
export class AffiliateProfile extends AbstractEntity {
  @Column({ name: 'user_id', unique: true })
  userId: number;

  @Column({ name: 'affiliate_code', unique: true })
  affiliateCode: string;

  @Column({ name: 'country_id', nullable: true })
  countryId: number;

  @Column({ 
    name: 'status', 
    type: 'enum', 
    enum: AffiliateStatus, 
    default: AffiliateStatus.PENDING 
  })
  status: AffiliateStatus;

  @Column({ 
    name: 'commission_type', 
    type: 'enum', 
    enum: CommissionType, 
    default: CommissionType.PERCENTAGE 
  })
  commissionType: CommissionType;

  @Column({
    name: 'commission_rate',
    type: 'decimal',
    precision: 5,
    scale: 2,
    default: 0,
  })
  commissionRate: number; // Percentage or fixed amount

  @Column({
    name: 'total_earnings',
    type: 'decimal',
    precision: 10,
    scale: 2,
    default: 0,
  })
  totalEarnings: number;

  @Column({
    name: 'pending_earnings',
    type: 'decimal',
    precision: 10,
    scale: 2,
    default: 0,
  })
  pendingEarnings: number;

  @Column({
    name: 'paid_earnings',
    type: 'decimal',
    precision: 10,
    scale: 2,
    default: 0,
  })
  paidEarnings: number;

  @Column({ name: 'total_clicks', default: 0 })
  totalClicks: number;

  @Column({ name: 'total_conversions', default: 0 })
  totalConversions: number;

  @Column({ name: 'total_orders', default: 0 })
  totalOrders: number;

  @Column({
    name: 'conversion_rate',
    type: 'decimal',
    precision: 5,
    scale: 2,
    default: 0,
  })
  conversionRate: number;

  @Column({ name: 'payment_method', nullable: true })
  paymentMethod: string; // Bank, PayPal, etc.

  @Column({ name: 'payment_details', type: 'jsonb', nullable: true })
  paymentDetails: any; // Bank details, PayPal email, etc.

  @Column({ name: 'bio', type: 'text', nullable: true })
  bio: string;

  @Column({ name: 'website_url', nullable: true })
  websiteUrl: string;

  @Column({ name: 'social_media', type: 'jsonb', nullable: true })
  socialMedia: {
    instagram?: string;
    facebook?: string;
    twitter?: string;
    youtube?: string;
    tiktok?: string;
  };

  @Column({ name: 'approved_at', nullable: true })
  approvedAt: Date;

  @Column({ name: 'rejected_at', nullable: true })
  rejectedAt: Date;

  @Column({ name: 'rejection_reason', nullable: true })
  rejectionReason: string;
  
  @Column({ name: 'notes', nullable: true })
  notes: string;

  // Relations
  @ManyToOne(() => User, { eager: true })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @ManyToOne(() => Country)
  @JoinColumn({ name: 'country_id' })
  country: Country;

  @OneToMany(() => AffiliateCommission, (commission) => commission.affiliate)
  commissions: AffiliateCommission[];

  @OneToMany(() => AffiliateClick, (click) => click.affiliate)
  clicks: AffiliateClick[];

  @OneToMany(() => AffiliateConversion, (conversion) => conversion.affiliate)
  conversions: AffiliateConversion[];

  @OneToMany(() => AffiliateDiscount, (affiliateDiscount) => affiliateDiscount.affiliate)
  affiliateDiscounts: AffiliateDiscount[];
}
