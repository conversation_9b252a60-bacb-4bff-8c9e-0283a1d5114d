import { <PERSON>ti<PERSON>, <PERSON>umn, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { AbstractEntity } from '../../entities/abstract.entity';
import { AffiliateProfile } from './affiliate-profile.entity';
import { AffiliateDiscount } from './affiliate-discount.entity';
import { AffiliateClick } from './affiliate-click.entity';
import { ProductOrder } from '../../entities/product-order.entity';

export enum ConversionType {
  PURCHASE = 'purchase',
  SIGNUP = 'signup',
  LEAD = 'lead',
  DOWNLOAD = 'download',
}

@Entity('affiliate_conversions')
export class AffiliateConversion extends AbstractEntity {
  @Column({ name: 'affiliate_id' })
  affiliateId: number;

  @Column({ name: 'affiliate_discount_id', nullable: true })
  affiliateDiscountId: number;

  @Column({ name: 'click_id', nullable: true })
  clickId: number;

  @Column({ name: 'order_id', nullable: true })
  orderId: number;

  @Column({ 
    name: 'type', 
    type: 'enum', 
    enum: ConversionType, 
    default: ConversionType.PURCHASE 
  })
  type: ConversionType;

  @Column({
    name: 'conversion_value',
    type: 'decimal',
    precision: 10,
    scale: 2,
    default: 0,
  })
  conversionValue: number;

  @Column({ name: 'customer_email', nullable: true })
  customerEmail: string;

  @Column({ name: 'customer_phone', nullable: true })
  customerPhone: string;

  @Column({ name: 'session_id', nullable: true })
  sessionId: string;

  @Column({ name: 'ip_address', nullable: true })
  ipAddress: string;

  @Column({ name: 'conversion_page', type: 'text', nullable: true })
  conversionPage: string;

  @Column({ name: 'time_to_conversion', nullable: true })
  timeToConversion: number; // Minutes from click to conversion

  @Column({ name: 'coupon_used', nullable: true })
  couponUsed: string;

  @Column({ name: 'discount_applied', type: 'decimal', precision: 10, scale: 2, nullable: true })
  discountApplied: number;

  @Column({ name: 'commission_earned', type: 'decimal', precision: 10, scale: 2, default: 0 })
  commissionEarned: number;

  @Column({ name: 'conversion_data', type: 'jsonb', nullable: true })
  conversionData: any; // Additional conversion metadata

  // Relations
  @ManyToOne(() => AffiliateProfile)
  @JoinColumn({ name: 'affiliate_id' })
  affiliate: AffiliateProfile;

  @ManyToOne(() => AffiliateDiscount, { nullable: true })
  @JoinColumn({ name: 'affiliate_discount_id' })
  affiliateDiscount: AffiliateDiscount;

  @ManyToOne(() => AffiliateClick, { nullable: true })
  @JoinColumn({ name: 'click_id' })
  click: AffiliateClick;

  @ManyToOne(() => ProductOrder, { eager: true, nullable: true })
  @JoinColumn({ name: 'order_id' })
  order: ProductOrder;
}
