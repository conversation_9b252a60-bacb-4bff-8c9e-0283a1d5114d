import { <PERSON>ti<PERSON>, Column, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { AbstractEntity } from '../../entities/abstract.entity';
import { AffiliateProfile } from './affiliate-profile.entity';
import { AffiliateDiscount } from './affiliate-discount.entity';
import { Country } from '../../entities/country.entity';

export enum ClickSource {
  DIRECT_LINK = 'direct_link',
  COUPON_CODE = 'coupon_code',
  SOCIAL_MEDIA = 'social_media',
  EMAIL = 'email',
  WEBSITE = 'website',
  OTHER = 'other',
}

@Entity('affiliate_clicks')
export class AffiliateClick extends AbstractEntity {
  @Column({ name: 'affiliate_id' })
  affiliateId: number;

  @Column({ name: 'affiliate_discount_id', nullable: true })
  affiliateDiscountId: number;

  @Column({ name: 'session_id', nullable: true })
  sessionId: string;

  @Column({ name: 'ip_address', nullable: true })
  ipAddress: string;

  @Column({ name: 'user_agent', type: 'text', nullable: true })
  userAgent: string;

  @Column({ name: 'referrer_url', type: 'text', nullable: true })
  referrerUrl: string;

  @Column({ name: 'landing_page', type: 'text', nullable: true })
  landingPage: string;

  @Column({ 
    name: 'source', 
    type: 'enum', 
    enum: ClickSource, 
    default: ClickSource.DIRECT_LINK 
  })
  source: ClickSource;

  @Column({ name: 'country_id', nullable: true })
  countryId: number;

  @Column({ name: 'city', nullable: true })
  city: string;

  @Column({ name: 'device_type', nullable: true })
  deviceType: string; // mobile, desktop, tablet

  @Column({ name: 'browser', nullable: true })
  browser: string;

  @Column({ name: 'os', nullable: true })
  os: string;

  @Column({ name: 'utm_source', nullable: true })
  utmSource: string;

  @Column({ name: 'utm_medium', nullable: true })
  utmMedium: string;

  @Column({ name: 'utm_campaign', nullable: true })
  utmCampaign: string;

  @Column({ name: 'utm_content', nullable: true })
  utmContent: string;

  @Column({ name: 'utm_term', nullable: true })
  utmTerm: string;

  @Column({ name: 'converted', default: false })
  converted: boolean;

  @Column({ name: 'conversion_date', nullable: true })
  conversionDate: Date;

  @Column({ name: 'order_id', nullable: true })
  orderId: number;

  @Column({
    name: 'commission_earned',
    type: 'decimal',
    precision: 10,
    scale: 2,
    default: 0,
  })
  commissionEarned: number;

  // Relations
  @ManyToOne(() => AffiliateProfile)
  @JoinColumn({ name: 'affiliate_id' })
  affiliate: AffiliateProfile;

  @ManyToOne(() => AffiliateDiscount, { nullable: true })
  @JoinColumn({ name: 'affiliate_discount_id' })
  affiliateDiscount: AffiliateDiscount;

  @ManyToOne(() => Country, { eager: true })
  @JoinColumn({ name: 'country_id' })
  country: Country;
}
