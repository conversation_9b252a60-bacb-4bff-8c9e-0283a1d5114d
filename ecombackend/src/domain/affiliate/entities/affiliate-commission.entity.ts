import { <PERSON>ti<PERSON>, Column, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>in<PERSON><PERSON>um<PERSON> } from 'typeorm';
import { AbstractEntity } from '../../entities/abstract.entity';
import { AffiliateProfile, CommissionType } from './affiliate-profile.entity';
import { AffiliateDiscount } from './affiliate-discount.entity';
import { ProductOrder } from '../../entities/product-order.entity';
import { Discount } from '../../entities/discount.entity';

export enum CommissionStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  PAID = 'paid',
  CANCELLED = 'cancelled',
  REFUNDED = 'refunded',
}

export enum CommissionSource {
  ORDER = 'order',
  DISCOUNT_USAGE = 'discount_usage',
  REFERRAL = 'referral',
  BONUS = 'bonus',
}

@Entity('affiliate_commissions')
export class AffiliateCommission extends AbstractEntity {
  @Column({ name: 'affiliate_id' })
  affiliateId: number;

  @Column({ name: 'affiliate_discount_id' })
  affiliateDiscountId: number;

  @Column({ name: 'order_id', nullable: true })
  orderId: number;

  @Column({ name: 'discount_id', nullable: true })
  discountId: number;

  @Column({ name: 'click_id', nullable: true })
  clickId: number;

  @Column({ 
    name: 'source', 
    type: 'enum', 
    enum: CommissionSource, 
    default: CommissionSource.ORDER 
  })
  source: CommissionSource;

  @Column({ 
    name: 'status', 
    type: 'enum', 
    enum: CommissionStatus, 
    default: CommissionStatus.PENDING 
  })
  status: CommissionStatus;

  @Column({
    name: 'order_amount',
    type: 'decimal',
    precision: 10,
    scale: 2,
    default: 0,
  })
  orderAmount: number;

  @Column({
    name: 'commission_rate',
    type: 'decimal',
    precision: 5,
    scale: 2,
    default: 0,
  })
  commissionRate: number;

  @Column({
    name: 'commission_amount',
    type: 'decimal',
    precision: 10,
    scale: 2,
    default: 0,
  })
  commissionAmount: number;

  @Column({ name: 'currency_code', default: 'USD' })
  currencyCode: string;

  @Column({ name: 'customer_email', nullable: true })
  customerEmail: string;

  @Column({ name: 'customer_phone', nullable: true })
  customerPhone: string;

  @Column({ name: 'coupon_code', nullable: true })
  couponCode: string;

  @Column({ name: 'discount_amount', type: 'decimal', precision: 10, scale: 2, nullable: true })
  discountAmount: number;

  @Column({ name: 'approved_at', nullable: true })
  approvedAt: Date;

  @Column({ name: 'paid_at', nullable: true })
  paidAt: Date;

  @Column({ name: 'payment_reference', nullable: true })
  paymentReference: string;

  @Column({ name: 'notes', type: 'text', nullable: true })
  notes: string;

  @Column({ name: 'refund_reason', nullable: true })
  refundReason: string;

  @Column({ name: 'refunded_at', nullable: true })
  refundedAt: Date;

  @Column({ name: 'country_id', nullable: true })
  countryId: string;

  @Column({ name: 'uuid', nullable: true })
  uuid: string;

  @Column({ name: 'invoice_no', nullable: true })
  invoiceNo: string;

  @Column({ name: 'commission_type', nullable: true })
  commissionType: CommissionType;

  // Relations
  @ManyToOne(() => AffiliateProfile)
  @JoinColumn({ name: 'affiliate_id' })
  affiliate: AffiliateProfile;

  @ManyToOne(() => AffiliateDiscount)
  @JoinColumn({ name: 'affiliate_discount_id' })
  affiliateDiscount: AffiliateDiscount;

  @ManyToOne(() => ProductOrder, { eager: true })
  @JoinColumn({ name: 'order_id' })
  order: ProductOrder;

  @ManyToOne(() => Discount, { eager: true })
  @JoinColumn({ name: 'discount_id' })
  discount: Discount;
}
