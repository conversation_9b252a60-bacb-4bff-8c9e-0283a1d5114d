import { <PERSON><PERSON><PERSON>, <PERSON>um<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON><PERSON> } from 'typeorm';
import { AbstractEntity } from '../../entities/abstract.entity';
import { AffiliateProfile, CommissionType } from './affiliate-profile.entity';
import { Discount } from '../../entities/discount.entity';
import { AffiliateDiscountUsage } from './affiliate-discount-usage.entity';
// import { AffiliateDiscountUsage } from './affiliate-discount-usage.entity';

export enum AffiliateDiscountStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  EXPIRED = 'expired',
  PENDING = 'pending',
  REJECTED = 'rejected',
}

@Entity('affiliate_discounts')
export class AffiliateDiscount extends AbstractEntity {
  @Column({ name: 'affiliate_id' })
  affiliateId: number;

  @Column({ name: 'discount_id' })
  discountId: number;

  @Column({ 
    name: 'status', 
    type: 'enum', 
    enum: AffiliateDiscountStatus, 
    default: AffiliateDiscountStatus.ACTIVE 
  })
  status: AffiliateDiscountStatus;

  @Column({ name: 'usage_count', default: 0 })
  usageCount: number;

  @Column({ name: 'max_usage', nullable: true })
  maxUsage: number;

  @Column({
    name: 'total_commission_earned',
    type: 'decimal',
    precision: 10,
    scale: 2,
    default: 0,
  })
  totalCommissionEarned: number;

  @Column({ name: 'start_date', type: 'timestamp', nullable: true })
  startDate: Date;

  @Column({ name: 'end_date', type: 'timestamp', nullable: true })
  endDate: Date;

  @Column({ name: 'notes', type: 'text', nullable: true })
  notes: string;

  // Commission settings (override affiliate profile settings)
  @Column({
    name: 'commission_type',
    type: 'character varying',
    // enum: CommissionType,
    nullable: true
  })
  commissionType: CommissionType;

  @Column({
    name: 'commission_rate',
    type: 'decimal',
    precision: 5,
    scale: 2,
    nullable: true,
  })
  commissionRate: number; // Percentage or fixed amount - overrides affiliate profile rate

  // Relations
  @ManyToOne(() => AffiliateProfile, { eager: true })
  @JoinColumn({ name: 'affiliate_id' })
  affiliate: AffiliateProfile;

  @ManyToOne(() => Discount, { eager: true })
  @JoinColumn({ name: 'discount_id' })
  discount: Discount;

  @OneToMany(() => AffiliateDiscountUsage, (usage) => usage.affiliateDiscount)
  usages: AffiliateDiscountUsage[];

  @OneToMany('AffiliateCommission', 'affiliateDiscount')
  commissions: any[];

  @OneToMany('AffiliateClick', 'affiliateDiscount')
  clicks: any[];

  @OneToMany('AffiliateConversion', 'affiliateDiscount')
  conversions: any[];
}
