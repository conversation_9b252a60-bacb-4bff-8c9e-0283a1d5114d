import { <PERSON><PERSON><PERSON>, <PERSON>umn, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { AbstractEntity } from '../../entities/abstract.entity';
import { AffiliateDiscount } from './affiliate-discount.entity';
import { ProductOrder } from '../../entities/product-order.entity';
import { DiscountUsage } from '../../entities/discount-usage.entity';

@Entity('affiliate_discount_usages')
export class AffiliateDiscountUsage extends AbstractEntity {
  @Column({ name: 'affiliate_discount_id' })
  affiliateDiscountId: number;

  @Column({ name: 'discount_usage_id', nullable: true })
  discountUsageId: number;

  @Column({ name: 'order_id' })
  orderId: number;

  @Column({ name: 'customer_email' })
  customerEmail: string;

  @Column({ name: 'customer_phone', nullable: true })
  customerPhone: string;

  @Column({
    name: 'order_amount',
    type: 'decimal',
    precision: 10,
    scale: 2,
    default: 0,
  })
  orderAmount: number;

  @Column({
    name: 'discount_amount',
    type: 'decimal',
    precision: 10,
    scale: 2,
    default: 0,
  })
  discountAmount: number;

  @Column({
    name: 'commission_rate',
    type: 'decimal',
    precision: 5,
    scale: 2,
    default: 0,
  })
  commissionRate: number;

  @Column({
    name: 'commission_amount',
    type: 'decimal',
    precision: 10,
    scale: 2,
    default: 0,
  })
  commissionAmount: number;

  @Column({ name: 'session_id', nullable: true })
  sessionId: string;

  @Column({ name: 'ip_address', nullable: true })
  ipAddress: string;

  @Column({ name: 'user_agent', type: 'text', nullable: true })
  userAgent: string;

  @Column({ name: 'referrer_url', type: 'text', nullable: true })
  referrerUrl: string;

  @Column({ name: 'utm_source', nullable: true })
  utmSource: string;

  @Column({ name: 'utm_medium', nullable: true })
  utmMedium: string;

  @Column({ name: 'utm_campaign', nullable: true })
  utmCampaign: string;

  // Relations
  @ManyToOne(() => AffiliateDiscount)
  @JoinColumn({ name: 'affiliate_discount_id' })
  affiliateDiscount: AffiliateDiscount;

  @ManyToOne(() => DiscountUsage, { eager: true, nullable: true })
  @JoinColumn({ name: 'discount_usage_id' })
  discountUsage: DiscountUsage;

  @ManyToOne(() => ProductOrder, { eager: true })
  @JoinColumn({ name: 'order_id' })
  order: ProductOrder;
}
