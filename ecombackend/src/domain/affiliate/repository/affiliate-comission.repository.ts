import { Injectable } from '@nestjs/common';
import { Repository, DataSource, ILike } from 'typeorm';
import { PageOptionsDto } from 'src/common/pagination/page-options.dto';
import { PageDto } from 'src/common/pagination/page.dto';
import { ServiceResponse } from 'src/common/utils/service-response';
import { PageMetaDto } from 'src/common/pagination/page-meta.dto';
import { AffiliateCommission } from '../entities/affiliate-commission.entity';

@Injectable()
export class AffiliateComissionRepository extends Repository<AffiliateCommission> {
  constructor(private dataSource: DataSource) {
    super(
      AffiliateCommission,
      dataSource.createEntityManager(),
      dataSource.createQueryRunner(),
    );
  }

  // // public async findAll(page: number, limit: number): Promise<Courier[]> {
  // //   return this.find({
  // //     take: limit,
  // //     skip: (page - 1) * limit,
  // //     withDeleted: false,
  // //   });
  // // }

  // // repository.ts
  // // public findAll(): Promise<Courier[]> {
  // //   return this.courierRepositoryLink.findAll();
  // // }

  // public async findAll(): Promise<ServiceResponse> {
  //   const couriers = await this.find({
  //     relations: ['countries'],
  //     order: {
  //       name: 'ASC',
  //     },
  //     cache: false,
  //   });

  //   // console.log('Couriers found:', couriers); // Log the results

  //   return {
  //     data: couriers,
  //   };
  // }

  // // public async getAllActiveCountries(): Promise<Courier[]> {
  // //   return this.find({
  // //     where: {
  // //       isActive: true,
  // //       deletedAt: IsNull(),
  // //     },
  // //     select: [
  // //       'id',
  // //       'name',
  // //       'imageGalleryId',
  // //       'currencyId',
  // //       'taxPercentage',
  // //       'shippingCharge',
  // //       'codCharge',
  // //       'code',
  // //       'shippingChargeFree',
  // //     ],
  // //     relations: ['currency', 'imageGallery'],
  // //     order: {
  // //       name: 'ASC',
  // //     },
  // //     cache: 86400000,
  // //   });
  // // }

  public async findAllDataByPagination(
    filters?: {
      status?: string;
      affiliateId?: number;
      dateFrom?: string;
      dateTo?: string;
    },
    pageOptionsDto?: PageOptionsDto,
  ): Promise<PageDto<AffiliateCommission>> {
    // const whereCondition = params?.globalSearch
    //   ? { name: ILike(`%${params.globalSearch}%`) }
    //   : {};

    // const [list, itemCount] = await this.findAndCount({
    //   where: whereCondition,
    //   skip: pageOptionsDto.skip,
    //   take: pageOptionsDto.take,
    //   withDeleted: false,
    //   relations: ['countries'],
    //   order: {
    //     name: 'ASC',
    //   },
    // });

    const queryBuilder = this.createQueryBuilder('commission')
      .leftJoinAndSelect('commission.affiliate', 'affiliate')
      .leftJoinAndSelect('affiliate.user', 'user')
      .leftJoinAndSelect('affiliate.affiliateDiscounts', 'affiliateDiscounts')
      .leftJoinAndSelect('affiliateDiscounts.discount', 'discount');

    // Apply filters
    if (filters?.status) {
      queryBuilder.andWhere('commission.status = :status', { status: filters.status });
    }

    if (filters?.affiliateId) {
      queryBuilder.andWhere('commission.affiliateId = :affiliateId', { affiliateId: filters.affiliateId });
    }

    if (filters?.dateFrom) {
      queryBuilder.andWhere('commission.createdAt >= :dateFrom', { dateFrom: filters.dateFrom });
    }

    if (filters?.dateTo) {
      queryBuilder.andWhere('commission.createdAt <= :dateTo', { dateTo: filters.dateTo });
    }

    // Apply pagination
    const page = pageOptionsDto?.page || 1;
    const limit = pageOptionsDto?.take || 10;
    const skip = (page - 1) * limit;

    queryBuilder.skip(skip).take(limit);
    queryBuilder.orderBy('commission.createdAt', 'DESC');

    const [list, itemCount] = await queryBuilder.getManyAndCount();

    const pageMetaDto = new PageMetaDto({ itemCount, pageOptionsDto });

    return new PageDto(list, pageMetaDto);
  }

  // public async findById(id: number): Promise<Courier | null> {
  //   return this.findOneBy({ id: id });
  // }

  // public async getCourierByName(name: string): Promise<Courier | null> {
  //   // where: [{ name: nameOrCode }, { code: nameOrCode }],
  //   // return this.findOneBy({ name: name });
  //   return this.findOne({
  //     where: [{ name: name }, { code: name.toUpperCase() }],
  //     select: [
  //       'id',
  //       'name',
  //       'imageGalleryId',
  //       'currencyId',
  //       'taxPercentage',
  //       'shippingCharge',
  //       'codCharge',
  //       'code',
  //       'shippingChargeFree',
  //     ],
  //     relations: ['currency', 'imageGallery'],
  //   });
  // }

  // public async add(Courier: CreateCourierDto): Promise<Courier> {
  //   const newCourier = this.create(Courier);
  //   return this.save(newCourier);
  // }

  // public async updateOne(
  //   id: number,
  //   updateCourierDto: UpdateCourierDto,
  // ): Promise<Courier | null> {
  //   const updatedResult = await this.update(id, updateCourierDto);
  //   let Courier = null;
  //   if (updatedResult.affected > 0) {
  //     Courier = await this.findOneBy({ id: id });
  //   }
  //   return Courier;
  // }

  // public async destroy(id: number): Promise<number | null> {
  //   const result = await this.softDelete(id);
  //   return result.affected;
  // }

  // public async restoreData(id: number): Promise<Courier | null> {
  //   const result = await this.restore(id);
  //   let Courier = null;
  //   if (result.affected > 0) {
  //     Courier = await this.findOneBy({ id: id });
  //   }
  //   return Courier;
  // }

  // public async activeOrInactive(
  //   id: number,
  //   status: boolean,
  // ): Promise<Courier | null> {
  //   const result = await this.update(id, { isActive: status });
  //   let Courier = null;
  //   if (result.affected > 0) {
  //     Courier = await this.findOneBy({ id: id });
  //   }
  //   return Courier;
  // }

  // // Method to clear all cache
  // public async clearCache(): Promise<void> {
  //   await this.dataSource.queryResultCache?.clear(); // Clear the entire cache
  // }
}
