import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AffiliateController } from './controllers/affiliate.controller';
import { AffiliateService } from './services/affiliate.service';
import { AffiliateTrackingService } from './services/affiliate-tracking.service';
import { AffiliateIntegrationService } from './services/affiliate-integration.service';
import { AffiliateDiscountCommissionService } from './services/affiliate-discount-commission.service';
import { AffiliateOrderIntegrationService } from './services/affiliate-order-integration.service';
import { UTMTrackingService } from './services/utm-tracking.service';
import { ConversionTrackingService } from './services/conversion-tracking.service';

// Affiliate Entities
import { AffiliateProfile } from './entities/affiliate-profile.entity';
import { AffiliateDiscount } from './entities/affiliate-discount.entity';
import { AffiliateClick } from './entities/affiliate-click.entity';
import { AffiliateCommission } from './entities/affiliate-commission.entity';
import { AffiliateConversion } from './entities/affiliate-conversion.entity';
import { AffiliateDiscountUsage } from './entities/affiliate-discount-usage.entity';

// Existing Entities
import { User } from '../entities/user.entity';
import { Country } from '../entities/country.entity';
import { ProductOrder } from '../entities/product-order.entity';
import { Discount } from '../entities/discount.entity';
import { DiscountUsage } from '../entities/discount-usage.entity';
import { Cart } from '../cart/entities/cart.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      // Affiliate entities
      AffiliateProfile,
      AffiliateDiscount,
      AffiliateClick,
      AffiliateCommission,
      AffiliateConversion,
      AffiliateDiscountUsage,
      
      // Existing entities
      User,
      Country,
      ProductOrder,
      Discount,
      DiscountUsage,
      Cart,
    ]),
  ],
  controllers: [AffiliateController],
  providers: [
    AffiliateService,
    AffiliateTrackingService,
    AffiliateIntegrationService,
    AffiliateDiscountCommissionService,
    AffiliateOrderIntegrationService,
    UTMTrackingService,
    ConversionTrackingService,
  ],
  exports: [
    AffiliateService,
    AffiliateTrackingService,
    AffiliateIntegrationService,
    AffiliateDiscountCommissionService,
    AffiliateOrderIntegrationService,
    UTMTrackingService,
    ConversionTrackingService,
  ],
})
export class AffiliateModule {}
