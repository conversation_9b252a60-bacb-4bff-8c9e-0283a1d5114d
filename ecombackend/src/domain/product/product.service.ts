import { Inject, Injectable } from '@nestjs/common';
import { PageOptionsDto } from 'src/common/pagination/page-options.dto';
import { ServiceResponse } from 'src/common/utils/service-response';
import { ProductRepository } from './product.repository';
import { ProductMetaRepository } from './product-meta.repository';
import { ProductDto } from './dto/product.dto';
import { ProductMeta } from '../entities/product-meta.entity';
import { has } from 'lodash';
import {
  ProductDetailsResponseDto,
  ProductViewResponseDto,
} from './productResponseDto/product-details-response.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import {
  ProductPriceResponseDto,
  ProductVariantResponseDto,
} from './productResponseDto/product-price-variant-response.dto';
import { ProductListResponseDto } from './productResponseDto/product-list-response.dto';
import { Product, ProductLocalization } from '../entities/product.entity';
import { ImageGallery } from '../entities/image-gallery.entity';
import { ImageGalleryRepository } from '../image/image-gallery.repository';
import {
  createSlug,
  generateProductSlug,
  getLocalizeValue,
  isEmptyObject,
  sortPriceFromList,
} from 'src/common/utils/common-functions';
import { ProductViewDto } from './dto/product-view.dto';
import { CreateProductVariantDto } from './productVariantDto/create-product-variant.dto';
import { AttributeValueRepository } from '../attribute-value/attribute-value.repository';
import {
  IProductVariantJsonb,
  IVariantDetails,
} from 'src/common/interfaces/product-variant-jsonb.interface';
import { ProductDetailsDto } from './productDetailsDto/product-details.dto';
import { ViewProductTagDto } from '../product-tags/dto/view-product-tags.dto';
import { ProductTagsService } from '../product-tags/product-tags.service';
import { SeoMetaService } from '../seo-meta/seo-meta.service';
import { SeoMEtaEnum } from '../entities/seo-meta.entity';
import { SeoMetaDto } from '../seo-meta/dto/seo-meta.dto';
import { ProductSortingEnum } from 'src/common/enums/product-sorting';
import { ProductStockManageDto } from './dto/product-stock-manage.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import { ProductRelationRepository } from './product-relation.repository';
import {
  formatCategories,
  formatChildCategoriesArr,
} from 'src/product-feed/utils/helper';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { DeliveryStatusEnum } from 'src/common/enums/delivery-status.enum';

@Injectable()
export class ProductService {
  constructor(
    private readonly repository: ProductRepository,
    private readonly productMetaRepository: ProductMetaRepository,
    private readonly attributeValueRepo: AttributeValueRepository,
    private readonly imageGalleryRepository: ImageGalleryRepository,
    private readonly productTagsService: ProductTagsService,
    private readonly seoMetaService: SeoMetaService,
    @InjectRepository(ProductLocalization)
    private readonly productLocalizationRepository: Repository<ProductLocalization>,
    private readonly productRelationRepository: ProductRelationRepository,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
  ) { }

  async findAll(): Promise<ServiceResponse> {
    const result = await this.repository.findAll();
    if (!result) {
      return new ServiceResponse(result, 'Product not found');
    }
    return new ServiceResponse(result, 'All Products found successfully');
  }

  async findAllDataByPagination(
    params: {
      globalSearch?: string;
      skuSearch?: string;
      nameSearch?: string,
      categoryIds?: string,
      isPublish?: boolean,
      isActive?: boolean
    },
    pageOptionsDto: PageOptionsDto,
  ): Promise<ServiceResponse> {
    // const result =
    //   await this.repository.findAllDataByPagination(pageOptionsDto);
    const result = await this.repository.findAllDataByPagination(
      params,
      pageOptionsDto,
    );
    if (!result) {
      return new ServiceResponse(result, 'Product not found');
    }
    // let imageUrls: string[] = [];
    // result.itemList.forEach((item: any) => {
    //   const imageGallaryIds = item.imageGallaryIds.split(',');
    //   imageGallaryIds.forEach(async (id: number) => {
    //     const imageObj: ImageGallary =
    //       await this.imageGallaryRepository.findById(id);
    //     imageUrls.push(imageObj.imageUrl);
    //   });

    //   item.imageGallaryIds = imageUrls.toString();
    // });
    return new ServiceResponse(
      result.itemList,
      'All product found successfully',
      result.meta,
    );
  }

  async findAllDataByCountryIdPagination(
    countryId: number,
    pageOptionsDto: PageOptionsDto,
    languageId?: number,
  ): Promise<ServiceResponse> {
    if (countryId) {
      const result = await this.repository.findAllDataByCountryIdPagination(
        countryId,
        pageOptionsDto,
        languageId,
      );
      if (!result) {
        return new ServiceResponse(result, 'Product not found');
      }
      const productList: ProductListResponseDto[] = [];
      for (const product of result?.itemList) {
        // result?.itemList?.forEach(async (product: any) => {
        // const isExists = productList.some((p:ProductListResponseDto) => p.id === product.id);

        const newProduct: ProductListResponseDto = new ProductListResponseDto();
        newProduct.id = product.id;
        newProduct.name = product.name;

        if (product.featured_image_id) {
          const featureImg: ImageGallery =
            await this.imageGalleryRepository.findById(
              product.featured_image_id,
            );
          newProduct.featuredImage = featureImg;
        }

        if (product.hover_image_id) {
          const hoverImg: ImageGallery =
            await this.imageGalleryRepository.findById(product.hover_image_id);
          newProduct.hoverImage = hoverImg;
        }

        // newProduct.featuredImageId = product.featured_image_id;
        // newProduct.hoverImageId = product.hover_image_id;
        newProduct.isMultiVariantProduct = product?.is_multi_variant_product;
        newProduct.slug = product.slug;
        // newProduct.productPrice = [];

        const productMeta: ProductMeta =
          await this.productMetaRepository.findByProductIdCountryId(
            product.id,
            countryId,
          );

        newProduct.quantity = productMeta.quantity;
        if (!product?.is_multi_variant_product) {
          newProduct.discountPrice = productMeta.discountPrice;
          newProduct.unitPrice = productMeta.unitPrice;
        } else {
          // newProduct.unitPrice = sortPriceFromList(productMeta.variants);
          const price: number[] = sortPriceFromList(productMeta.variants);
          if (price.length > 0) {
            price[0]
              ? (newProduct.unitPrice = price[0])
              : (newProduct.unitPrice = 0);
          }
          if (price.length > 1) {
            price[1]
              ? (newProduct.discountPrice = price[1])
              : (newProduct.discountPrice = 0);
          }
        }
        productList.push(newProduct);
      }

      return new ServiceResponse(
        productList,
        'All product found successfully',
        result.meta,
      );
    } else {
      return new ServiceResponse(
        null,
        'Please provide country id and try again',
        null,
      );
    }
  }

  async getRelatedProductsById(
    countryId: number,
    productId: number,
    params: {
      languageId?: number;
      orderBy?: ProductSortingEnum;
      brandId?: number;
      size?: string;
      color?: string;
    },
    pageOptionsDto: PageOptionsDto,
  ): Promise<ServiceResponse> {
    let result = null;
    if (countryId) {
      const languageId = params.languageId || 1;

      result = await this.repository.getRelatedProductsById(
        countryId,
        productId,
        pageOptionsDto,
        languageId,
      );

      if (result?.itemList && result?.itemList.length == 0) {
        return new ServiceResponse([], 'Product not found');
      }

      const productsId = result?.itemList.map((item) => item.id);

      const relationdata = productsId
        ? await this.productRelationRepository.attachCategoriesToProducts(
          countryId,
          productsId,
          languageId,
        )
        : {};

      const productList: ProductListResponseDto[] = [];
      for (const product of result?.itemList) {
        const newProduct: ProductListResponseDto = new ProductListResponseDto();
        newProduct.id = product.id;
        newProduct.name = product.name;

        newProduct.countryId = countryId;

        newProduct.featuredImage = new ImageGallery();
        newProduct.hoverImage = new ImageGallery();

        newProduct.brand_name = relationdata[product.id]['brand'] || '';
        newProduct.categoriesArr = relationdata[product.id]['categories'] || '';
        newProduct.sku = relationdata[product.id]['sku'] || product?.parent_sku;
        newProduct.currency = relationdata[product.id]['currency'];

        newProduct.featuredImage.id = product.featured_image_id;
        newProduct.featuredImage.name = product.featured_image_name;
        newProduct.featuredImage.imageUrl = product.featured_imageurl;
        newProduct.featuredImage.originalImageUrl =
          product.featured_original_imageurl;
        newProduct.featuredImage.imageGalleryUrls =
          product.featured_image_gallery_urls;

        newProduct.hoverImage.id = product.hover_image_id;
        newProduct.hoverImage.name = product.hover_image_name;
        newProduct.hoverImage.imageUrl = product.hover_imageurl;
        newProduct.hoverImage.originalImageUrl =
          product.hover_original_imageurl;
        newProduct.hoverImage.imageGalleryUrls =
          product.hover_image_gallery_urls;

        newProduct.isMultiVariantProduct = product?.is_multi_variant_product;
        newProduct.slug = product.slug;

        // Deprecated
        newProduct.categoryId = 0; //product.category_id;
        newProduct.categorySlug = '--'; //product.cat_slug;
        newProduct.catParentName = '--'; //product.cat_parent_name;
        // Deprecated end

        newProduct.limitedEdition = product.limited_edition;
        newProduct.justArrived = product.just_arrived;
        newProduct.mostPopular = product.most_popular;
        newProduct.customerFavorite = product.customer_favorite;
        newProduct.bestValue = product.best_value;
        newProduct.specialOffer = product.special_offer;

        newProduct.quantity = product.quantity;
        if (!product?.is_multi_variant_product) {
          // let productPrice: ProductPriceResponseDto =
          //   new ProductPriceResponseDto();
          newProduct.discountPrice = product.discountPrice;
          newProduct.unitPrice = product.unitPrice;
        } else {
          const price: number[] = sortPriceFromList(product.variants);

          if (price.length > 0) {
            price[0]
              ? (newProduct.unitPrice = price[0])
              : (newProduct.unitPrice = 0);
          }
          if (price.length > 1) {
            price[1]
              ? (newProduct.discountPrice = price[1])
              : (newProduct.discountPrice = 0);
          }
        }
        productList.push(newProduct);
      }

      return new ServiceResponse(
        productList,
        'All product found successfully',
        result.meta,
      );
    } else {
      return new ServiceResponse(
        null,
        'Please provide country id and try again',
        null,
      );
    }
  }

  async countryCategoryWiseProductsPagination(
    countryId: number,
    categoryId: number,
    params: {
      languageId?: number;
      orderBy?: ProductSortingEnum;
      brandId?: number;
      size?: string;
      color?: string;
    },
    pageOptionsDto: PageOptionsDto,
  ): Promise<ServiceResponse> {
    try {
      // Enhanced parameter validation
      if (!countryId || !categoryId) {
        return new ServiceResponse(
          null,
          'Country ID and Category ID are required',
          null,
        );
      }

      // Validate numeric parameters
      if (params?.brandId && (isNaN(Number(params.brandId)) || Number(params.brandId) <= 0)) {
        return new ServiceResponse(
          null,
          'Invalid brand ID provided',
          null,
        );
      }

      const languageId = params.languageId || 1;
      let result = null;

      // Enhanced logging for debugging
      const hasFilters = !!(params?.brandId || params?.size || params?.color || params?.orderBy);
      console.log(`Product search - Country: ${countryId}, Category: ${categoryId}, Filters: ${hasFilters}`, {
        brandId: params?.brandId,
        size: params?.size,
        color: params?.color,
        orderBy: params?.orderBy,
        page: pageOptionsDto.page,
        take: pageOptionsDto.take
      });

      if (hasFilters) {
        result = await this.repository.productSearchWithFilters(
          params,
          countryId,
          pageOptionsDto,
          categoryId,
          languageId,
        );
      } else {
        result = await this.repository.webCountryCategoryWiseProductsPagination(
          countryId,
          categoryId,
          pageOptionsDto,
          languageId,
        );
      }

      if (!result || (result?.itemList && result?.itemList.length === 0)) {
        return new ServiceResponse([], 'Product not found');
      }

      const productsId = result?.itemList.map((item) => item.id);

      // console.log('productsId:::', productsId);

      const relationdata = productsId
        ? await this.productRelationRepository.attachCategoriesToProducts(
          countryId,
          productsId,
          languageId,
        )
        : {};

      // console.log('relationdata:::', relationdata);

      const productList: ProductListResponseDto[] = [];
      for (const product of result?.itemList) {
        const newProduct: ProductListResponseDto = new ProductListResponseDto();
        newProduct.id = product.id;
        newProduct.name = product.name;
        newProduct.countryId = countryId;

        newProduct.brand_name = relationdata[product.id]['brand'] || '';
        newProduct.categoriesArr = relationdata[product.id]['categories'] || '';
        newProduct.sku = relationdata[product.id]['sku'] || product?.parent_sku;
        newProduct.currency = relationdata[product.id]['currency'];

        newProduct.featuredImage = new ImageGallery();
        newProduct.hoverImage = new ImageGallery();

        newProduct.featuredImage.id = product.featured_image_id;
        newProduct.featuredImage.name = product.featured_image_name;
        newProduct.featuredImage.imageUrl = product.featured_imageurl;
        newProduct.featuredImage.originalImageUrl =
          product.featured_original_imageurl;
        newProduct.featuredImage.imageGalleryUrls =
          product.featured_image_gallery_urls;

        newProduct.hoverImage.id = product.hover_image_id;
        newProduct.hoverImage.name = product.hover_image_name;
        newProduct.hoverImage.imageUrl = product.hover_imageurl;
        newProduct.hoverImage.originalImageUrl =
          product.hover_original_imageurl;
        newProduct.hoverImage.imageGalleryUrls =
          product.hover_image_gallery_urls;

        // if (product.featured_image_id) {
        //   const featureImg: ImageGallery =
        //     await this.imageGalleryRepository.findById(
        //       product.featured_image_id,
        //     );
        //   newProduct.featuredImage = featureImg;
        // }

        // if (product.hover_image_id) {
        //   const hoverImg: ImageGallery =
        //     await this.imageGalleryRepository.findById(product.hover_image_id);
        //   newProduct.hoverImage = hoverImg;
        // }

        newProduct.isMultiVariantProduct = product?.is_multi_variant_product;
        newProduct.slug = product.slug;

        // Deprecated
        newProduct.categoryId = 0; //product.category_id;
        newProduct.categorySlug = '--'; //product.cat_slug;
        newProduct.catParentName = '--'; //product.cat_parent_name;
        // Deprecated end

        newProduct.limitedEdition = product.limited_edition;
        newProduct.justArrived = product.just_arrived;
        newProduct.mostPopular = product.most_popular;
        newProduct.customerFavorite = product.customer_favorite;
        newProduct.bestValue = product.best_value;
        newProduct.specialOffer = product.special_offer;

        // newProduct.productPrice = [];
        // const productMeta: ProductMeta =
        //   await this.productMetaRepository.findByProductIdCountryId(
        //     product.id,
        //     countryId,
        //   );

        newProduct.quantity = product.quantity;
        if (!product?.is_multi_variant_product) {
          // let productPrice: ProductPriceResponseDto =
          //   new ProductPriceResponseDto();
          newProduct.discountPrice = product.discountPrice;
          newProduct.unitPrice = product.unitPrice;
        } else {
          const price: number[] = sortPriceFromList(product.variants);

          if (price.length > 0) {
            price[0]
              ? (newProduct.unitPrice = price[0])
              : (newProduct.unitPrice = 0);
          }
          if (price.length > 1) {
            price[1]
              ? (newProduct.discountPrice = price[1])
              : (newProduct.discountPrice = 0);
          }
        }
        productList.push(newProduct);
      }

      return new ServiceResponse(
        productList,
        'All product found successfully',
        result.meta,
      );
    } catch (error) {
      console.error('Error in countryCategoryWiseProductsPagination:', error);
      return new ServiceResponse(
        null,
        'An error occurred while fetching products. Please try again.',
        null,
      );
    }
  }
  async countryBrandWiseProductsPagination(
    countryId: number,
    brandSlug: string,
    params: {
      languageId?: number;
      name?: string;
      size?: string;
      color?: string;
    },
    pageOptionsDto: PageOptionsDto,
  ): Promise<ServiceResponse> {
    const languageId = params.languageId || 1;
    let result = null;
    if (countryId && brandSlug) {
      if (params?.size || params?.color) {
        result = await this.repository.productSearchWithBrandFilters(
          params,
          countryId,
          pageOptionsDto,
          brandSlug,
        );
      } else {
        result = await this.repository.countryBrandWiseProductsPagination(
          countryId,
          brandSlug,
          pageOptionsDto,
          languageId,
        );
      }

      if (result?.itemList && result?.itemList.length == 0) {
        return new ServiceResponse([], 'Product not found');
      }
      const productList: ProductListResponseDto[] = [];
      for (const product of result?.itemList) {
        // result?.itemList?.forEach(async (product: any) => {
        // const isExists = productList.some((p:ProductListResponseDto) => p.id === product.id);

        const newProduct: ProductListResponseDto = new ProductListResponseDto();
        newProduct.id = product.id;
        newProduct.name = product.name;

        newProduct.featuredImage = new ImageGallery();
        newProduct.hoverImage = new ImageGallery();

        newProduct.featuredImage.id = product.featured_image_id;
        newProduct.featuredImage.name = product.featured_image_name;
        newProduct.featuredImage.imageUrl = product.featured_imageurl;
        newProduct.featuredImage.originalImageUrl =
          product.featured_original_imageurl;
        newProduct.featuredImage.imageGalleryUrls =
          product.featured_image_gallery_urls;

        newProduct.hoverImage.id = product.hover_image_id;
        newProduct.hoverImage.name = product.hover_image_name;
        newProduct.hoverImage.imageUrl = product.hover_imageurl;
        newProduct.hoverImage.originalImageUrl =
          product.hover_original_imageurl;
        newProduct.hoverImage.imageGalleryUrls =
          product.hover_image_gallery_urls;

        // if (product.featured_image_id) {
        //   const featureImg: ImageGallery =
        //     await this.imageGalleryRepository.findById(
        //       product.featured_image_id,
        //     );
        //   newProduct.featuredImage = featureImg;
        // }

        // if (product.hover_image_id) {
        //   const hoverImg: ImageGallery =
        //     await this.imageGalleryRepository.findById(product.hover_image_id);
        //   newProduct.hoverImage = hoverImg;
        // }

        newProduct.isMultiVariantProduct = product?.is_multi_variant_product;
        newProduct.slug = product.slug;
        newProduct.categoryId = product.category_id;
        newProduct.brandId = product.brand_id;
        // newProduct.productPrice = [];
        // const productMeta: ProductMeta =
        //   await this.productMetaRepository.findByProductIdCountryId(
        //     product.id,
        //     countryId,
        //   );

        newProduct.quantity = product.quantity;
        if (!product?.is_multi_variant_product) {
          // let productPrice: ProductPriceResponseDto =
          //   new ProductPriceResponseDto();
          newProduct.discountPrice = product.discountPrice;
          newProduct.unitPrice = product.unitPrice;
        } else {
          const price: number[] = sortPriceFromList(product.variants);

          if (price.length > 0) {
            price[0]
              ? (newProduct.unitPrice = price[0])
              : (newProduct.unitPrice = 0);
          }
          if (price.length > 1) {
            price[1]
              ? (newProduct.discountPrice = price[1])
              : (newProduct.discountPrice = 0);
          }
        }
        productList.push(newProduct);
      }

      return new ServiceResponse(
        productList,
        'All product found successfully',
        result.meta,
      );
    } else {
      return new ServiceResponse(
        null,
        'Please provide country id and try again',
        null,
      );
    }
  }

  async searchProductByNameCountry(
    name: string,
    countryCode: string,
    pageOptionsDto: PageOptionsDto,
    languageId?: number,
  ): Promise<ServiceResponse> {
    languageId = languageId || 1;

    if (countryCode && name) {
      const result = await this.repository.searchProductByNameCountry(
        name,
        countryCode.toUpperCase(),
        pageOptionsDto,
        languageId,
      );

      if (result?.itemList && result?.itemList.length == 0) {
        return new ServiceResponse([], 'No products found for the search criteria', result.meta);
      }

      const countryId =
        await this.productRelationRepository.getCountryIdByCode(countryCode);

      const productsId = result?.itemList.map((item) => item.id);

      const relationdata =
        productsId && countryId
          ? await this.productRelationRepository.attachCategoriesToProducts(
            countryId,
            productsId,
            languageId,
          )
          : {};

      const productList: ProductListResponseDto[] = [];
      for (const product of result?.itemList) {
        // result?.itemList?.forEach(async (product: any) => {
        // const isExists = productList.some((p:ProdusctListResponseDto) => p.id === product.id);

        const newProduct: ProductListResponseDto = new ProductListResponseDto();
        newProduct.id = product.id;
        newProduct.name = product.name;
        if (product.featured_image_id) {
          const featureImg: ImageGallery =
            await this.imageGalleryRepository.findById(
              product.featured_image_id,
            );
          newProduct.featuredImage = featureImg;
        }

        if (product.hover_image_id) {
          const hoverImg: ImageGallery =
            await this.imageGalleryRepository.findById(product.hover_image_id);
          newProduct.hoverImage = hoverImg;
        }
        newProduct.isMultiVariantProduct = product?.is_multi_variant_product;
        newProduct.slug = product.slug;

        // Deprecated
        newProduct.categoryId = product.category_id;
        newProduct.categorySlug = product.category_slug;
        // Deprecated end

        newProduct.brand_name = relationdata[product.id]['brand'] || '';
        newProduct.categoriesArr = relationdata[product.id]['categories'] || '';
        newProduct.sku = relationdata[product.id]['sku'] || product?.parent_sku;
        newProduct.currency = relationdata[product.id]['currency'];

        // newProduct.productPrice = [];
        // const productMeta: ProductMeta =
        //   await this.productMetaRepository.findByProductIdCountryId(
        //     product.id,
        //     product.countryId,
        //   );

        newProduct.countryId = product.countryId;
        newProduct.quantity = product.quantity;
        if (!product?.is_multi_variant_product) {
          // let productPrice: ProductPriceResponseDto =
          //   new ProductPriceResponseDto();
          newProduct.discountPrice = product.discountPrice;
          newProduct.unitPrice = product.unitPrice;
        } else {
          const price: number[] = sortPriceFromList(product.variants);

          if (price.length > 0) {
            price[0]
              ? (newProduct.unitPrice = price[0])
              : (newProduct.unitPrice = 0);
          }
          if (price.length > 1) {
            price[1]
              ? (newProduct.discountPrice = price[1])
              : (newProduct.discountPrice = 0);
          }
        }
        productList.push(newProduct);
      }

      return new ServiceResponse(
        productList,
        'All product found successfully',
        result.meta,
      );
    } else {
      return new ServiceResponse(
        null,
        'Please provide country id and try again',
        null,
      );
    }
  }
  async searchProductList(
    name: string,
    pageOptionsDto: PageOptionsDto,
  ): Promise<ServiceResponse> {
    // const result =
    //   await this.repository.findAllDataByPagination(pageOptionsDto);
    if (name) {
      const result = await this.repository.searchProductList(
        name,
        pageOptionsDto,
      );

      if (result && result.itemList.length > 0) {
        return new ServiceResponse(
          result.itemList,
          'All product found successfully',
          result.meta,
        );
      } else {
        return new ServiceResponse([], 'Product not found', result.meta);
      }
    } else {
      return new ServiceResponse(
        null,
        'Please provide product name and try again',
        null,
      );
    }
  }

  async createProduct(productDto: ProductDto): Promise<ServiceResponse> {
    const result = await this.repository.add(productDto);
    if (!result) {
      return new ServiceResponse(
        result,
        'Failed to saved Product! please try again',
      );
    }
    return new ServiceResponse(result, 'Product saved successfully');
  }

  async copyProduct(productId: number): Promise<ServiceResponse> {
    try {
      const originalProduct = productId
        ? await this.repository.findOne({
          where: { id: productId },
          relations: [
            'categories',
            'localizations',
            'productMetaList',
            'featuredImage',
            'hoverImage',
            'sizeChartImg',
          ],
        })
        : null;

      // Step 2: Clone the product
      const clonedProduct = this.repository.copyProduct(originalProduct);

      if (!originalProduct) {
        return new ServiceResponse(null, 'Product not found! please try again');
      }

      return new ServiceResponse(clonedProduct, 'Product copied successfully');
    } catch (e) {
      console.log(e);
      return new ServiceResponse(null, 'Product not found! please try again');
    }
  }

  async updateProduct(
    id: number,
    updateProductDto: UpdateProductDto,
  ): Promise<ServiceResponse> {
    if (id) {
      const exist = await this.repository.findById(id);
      if (!exist) {
        return new ServiceResponse(null, 'Product not found! please try again');
      }

      if (updateProductDto?.name.toLowerCase() !== exist?.name.toLowerCase()) {
        updateProductDto.slug = createSlug(updateProductDto?.name);

        const existSlug = await this.repository.getProductBySlug(
          updateProductDto?.slug,
        );
        if (existSlug) {
          // if (exist.slug.includes('-0')) {
          //   const lastDotIndex = exist.slug.lastIndexOf('-0');
          //   const slugCount =
          //     parseInt(exist.slug.substring(lastDotIndex + 1)) + 1;
          //   updateProductDto.slug = updateProductDto.slug + '-00' + slugCount;
          // } else {
          updateProductDto.slug =
            updateProductDto.slug + '-' + generateProductSlug();
          // }
        }
      }

      const result = await this.repository.updateProduct(updateProductDto);
      if (!result) {
        return new ServiceResponse(
          result,
          'Failed to saved Product! Please try again',
        );
      }
      return new ServiceResponse(result, 'Product saved successfully');
    } else {
      return new ServiceResponse(null, 'Provide valid product id');
    }
  }

  async publishProduct(
    id: number,
    isPublish: boolean,
    body: { updatedBy: number },
  ): Promise<ServiceResponse> {
    if (!id) {
      return new ServiceResponse(
        null,
        'Please provide valid product id! Try again',
      );
    }
    const exist: Product = await this.repository.findById(id);
    if (!exist) {
      return new ServiceResponse(null, 'Product not found! please try again');
    }
    exist.isPublish = isPublish;
    exist.updatedBy = body.updatedBy;
    if (!exist.slug) {
      exist.slug = createSlug(exist?.name);

      const existSlug = await this.repository.getProductBySlug(exist?.slug);
      if (existSlug) {
        exist.slug = exist.slug + '-' + generateProductSlug();
      }
    }

    const result = await this.repository.saveUpdateProduct(exist);
    if (!result) {
      return new ServiceResponse(
        result,
        'Failed to published Product! Please try again',
      );
    }
    return new ServiceResponse(result, 'Product successfully published');
  }

  async countryWiseActiveOrInactive(
    productId: number,
    countryId: number,
    status: boolean,
  ): Promise<ServiceResponse> {
    if (!productId && !countryId) {
      return new ServiceResponse(
        null,
        'Please provide valid country & product id! Try again',
      );
    }

    const exist: ProductMeta =
      await this.productMetaRepository.findByProductIdCountryId(
        productId,
        countryId,
      );

    if (!exist) {
      return new ServiceResponse(
        null,
        'Selected Country for this Product is not found! please try again',
      );
    }
    const result = await this.productMetaRepository.countryWiseActiveOrInactive(
      exist.id,
      status,
    );
    if (!result) {
      return new ServiceResponse(
        result,
        'Failed to changed status! Please try again',
      );
    }
    return new ServiceResponse(
      true,
      `Country successfully changed status as ${result?.isActive}`,
    );
  }

  async createVariant(
    createProductVariantDto: CreateProductVariantDto,
  ): Promise<ServiceResponse> {
    if (createProductVariantDto) {
      const product = await this.findById(createProductVariantDto.productId);
      if (!product) {
        return new ServiceResponse(
          null,
          `Product not found id ${createProductVariantDto.productId}! please try again`,
        );
      }
    }

    const result = await this.repository.addVariant(createProductVariantDto);
    if (!result) {
      return new ServiceResponse(
        null,
        'Failed to saved Product! please try again',
      );
    }
    return new ServiceResponse(result, 'Product saved successfully');
  }

  async getProductByProductCountrySlug(
    slug: string,
    countryId: number,
    languageId?: number,
  ): Promise<ServiceResponse> {
    languageId = languageId || 1;

    if (!countryId || !slug) {
      return new ServiceResponse(
        null,
        'Please provide valid slug or country id',
      );
    }

    const cacheKey = `products_details_${countryId}_${languageId}_${slug}`;

    // Try retrieving from cache
    const cachedProducts: ProductDetailsDto =
      await this.cacheManager.get(cacheKey);

    if (cachedProducts) {
      // console.log('2. Returning from cache');
      return new ServiceResponse(cachedProducts, 'Product found successfully.');
    }

    const productObj =
      await this.repository.getWebProductByProductSlugCountryId(
        slug,
        countryId,
      );

    if (!productObj) {
      return new ServiceResponse(null, 'Product not found');
    }

    const categories = await this.repository.getWebProductCategories(
      slug,
      countryId,
    );

    let sizeChartData = null;

    const sizeId = productObj.size_chart_id;
    if (sizeId) {
      // transaction not added
      sizeChartData = await this.repository.findSizeChartValue(
        sizeId,
        countryId,
      );
    }

    const attributesId = productObj.attribute_images?.map(
      (item: { attributeValueId: number }) => item.attributeValueId,
    );

    const attrValues = attributesId
      ? await this.repository.getProductAttributeLocalize(
        attributesId,
        languageId,
      )
      : {};

    const categoriesArr = categories
      ? formatChildCategoriesArr(categories, languageId)
      : null;

    const localization = await this.productLocalizationRepository.findOne({
      where: { product: { id: productObj.id }, languageId: languageId },
    });

    const productDetailsDto: ProductDetailsDto = new ProductDetailsDto();
    productDetailsDto.product = new ProductViewDto();
    productDetailsDto.product.id = productObj.id;
    productDetailsDto.product.name = localization?.name || productObj?.name;
    productDetailsDto.product.description =
      localization?.description || productObj?.description;
    productDetailsDto.product.materialCare =
      localization?.materialCare || productObj?.material_care;
    productDetailsDto.product.shippingReturn =
      localization?.shippingReturn || productObj?.shipping_return;
    productDetailsDto.product.wowFactors =
      localization?.wowFactors || productObj?.wow_factors;

    productDetailsDto.product.sku = productObj.parent_sku;
    productDetailsDto.product.brand_name = productObj.brand_name;

    productDetailsDto.product.setOrder = productObj?.sort_order;
    productDetailsDto.product.tagLine1 =
      localization?.tagLine1 || productObj?.tag_line1;
    productDetailsDto.product.tagLine2 =
      productObj?.tag_line2 ?? productObj?.tag_line2;
    productDetailsDto.product.tagLine2Type =
      productObj?.tag_line2_type ?? productObj?.tag_line2_type;

    productDetailsDto.product.isRefundable = productObj?.is_refundable;
    productDetailsDto.product.isActive = productObj?.is_active;
    productDetailsDto.product.brandId = productObj?.brand_id;
    productDetailsDto.product.isMultiVariantProduct =
      productObj?.is_multi_variant_product;
    productDetailsDto.product.isComboPackProduct =
      productObj?.is_combo_pack_product;
    productDetailsDto.product.slug = productObj?.slug;
    productDetailsDto.product.currency = productObj?.currency;

    productDetailsDto.product.categories = categories;
    productDetailsDto.categoriesArr = categoriesArr;

    // Deprecated
    productDetailsDto.product.categoryId = productObj?.category_id;
    productDetailsDto.product.categorySlug = productObj?.cat_slug;
    productDetailsDto.product.catParentName = productObj?.cat_parent_name;
    // Deprecated end

    productDetailsDto.product.limitedEdition = productObj?.limited_edition;
    productDetailsDto.product.justArrived = productObj?.just_arrived;
    productDetailsDto.product.mostPopular = productObj?.most_popular;
    productDetailsDto.product.customerFavorite = productObj?.customer_favorite;
    productDetailsDto.product.bestValue = productObj?.best_value;
    productDetailsDto.product.specialOffer = productObj?.special_offer;

    productDetailsDto.product.featuredImage = new ImageGallery();
    productDetailsDto.product.hoverImage = new ImageGallery();
    productDetailsDto.product.sizeChart = new ImageGallery();

    productDetailsDto.product.featuredImage.id = productObj?.featured_image_id;
    productDetailsDto.product.featuredImage.name =
      productObj?.featured_image_name;
    productDetailsDto.product.featuredImage.imageUrl =
      productObj?.featured_imageurl;
    productDetailsDto.product.featuredImage.originalImageUrl =
      productObj?.featured_original_imageurl;
    productDetailsDto.product.featuredImage.imageGalleryUrls =
      productObj?.featured_image_gallery_urls;
    productDetailsDto.product.featuredImage.imageType =
      productObj?.igf_image_type;
    productDetailsDto.product.featuredImage.imageMimeType =
      productObj?.igf_image_mime_type;

    productDetailsDto.product.hoverImage.id = productObj?.hover_image_id;
    productDetailsDto.product.hoverImage.name = productObj?.hover_image_name;
    productDetailsDto.product.hoverImage.imageUrl = productObj?.hover_imageurl;
    productDetailsDto.product.hoverImage.originalImageUrl =
      productObj?.hover_original_imageurl;
    productDetailsDto.product.hoverImage.imageGalleryUrls =
      productObj?.hover_image_gallery_urls;
    productDetailsDto.product.hoverImage.imageType =
      productObj?.igh_image_type;
    productDetailsDto.product.hoverImage.imageMimeType =
      productObj?.igh_image_mime_type;

    productDetailsDto.product.sizeChart.id = productObj?.size_chart_image_id;
    productDetailsDto.product.sizeChart.name =
      productObj?.size_chart_image_name;
    productDetailsDto.product.sizeChart.imageUrl =
      productObj?.size_chart_imageurl;
    productDetailsDto.product.sizeChart.originalImageUrl =
      productObj?.size_chart_original_imageurl;
    productDetailsDto.product.sizeChart.imageGalleryUrls =
      productObj?.size_chart_image_gallery_urls;
    productDetailsDto.product.sizeChart.imageType =
      productObj?.igs_image_type;
    productDetailsDto.product.sizeChart.imageMimeType =
      productObj?.igs_image_mime_type;

    productDetailsDto.product.unitPrice = productObj?.unitPrice;
    productDetailsDto.product.discountPrice = productObj?.discountPrice;
    productDetailsDto.product.quantity = productObj?.quantity;
    productDetailsDto.product.variants = productObj?.variants;
    productDetailsDto.product.comboType = productObj?.comboType;

    productDetailsDto.product.sizes = [];
    productDetailsDto.product.colors = [];

    productDetailsDto.product.sizeChartData = sizeChartData;

    if (productObj?.selected_attributes) {
      for (const attr of productObj?.selected_attributes) {
        if (attr.attributeId && attr.attributeId === 1) {
          // attr.values.sort((a, b) => a - b);
          for (const id of attr?.values) {
            const sizeAttrObj: any = {};
            sizeAttrObj.id = id;
            const attributeValueObj =
              await this.attributeValueRepo.findById(id);
            sizeAttrObj.name = getLocalizeValue(
              languageId,
              attributeValueObj?.locale,
              attributeValueObj.name,
            );
            sizeAttrObj.originalName = attributeValueObj.name;
            sizeAttrObj.sortOrder = attributeValueObj.sortOrder;
            productDetailsDto.product.sizes.push(sizeAttrObj);
          }

          productDetailsDto.product.sizes.sort(
            (a, b) => a.sortOrder - b.sortOrder,
          );
        } else if (attr?.attributeId && attr?.attributeId === 2) {
          for (const id of attr?.values) {
            const colorAttrObj: any = {};
            colorAttrObj.id = id;
            colorAttrObj.images = [];

            for (const attrImg of productObj?.attribute_images) {
              if (id == attrImg.attributeValueId) {
                colorAttrObj.originalName = attrImg.attributeValueName;
                const _name = has(attrValues, id)
                  ? attrValues[id]
                  : attrImg.attributeValueName;

                colorAttrObj.name = _name || attrImg.attributeValueName;

                // let galleryImage = [];
                for (const id of attrImg?.images) {
                  const imgObj = await this.imageGalleryRepository.findById(id);
                  if (imgObj) {
                    colorAttrObj.images.push(imgObj);
                  }
                }
              }
            }
            productDetailsDto.product.colors.push(colorAttrObj);
          }
        }
      }
    }

    productDetailsDto.product.imageGallery = [];
    // if (productObj?.image_gallery_ids) {
    //   const galleryImageIds: number[] = [];
    //   const stringImageList: string[] =
    //     productObj?.image_gallery_ids.split(',');
    //   for (const value of stringImageList) {
    //     galleryImageIds.push(parseInt(value));
    //   }

    //   const galleryImage =
    //     await this.imageGalleryRepository.getAllGalleryImagesById(
    //       galleryImageIds,
    //     );
    //   for (const gallery of galleryImage) {
    //     productDetailsDto.product.imageGallery.push(gallery);
    //   }
    // }
    if (productObj?.image_gallery_ids) {
      const imageIdStrings = productObj.image_gallery_ids.split(',');
      const imageIds = imageIdStrings.map(Number); // Convert to numbers

      const galleryImages = await this.imageGalleryRepository.getAllGalleryImagesById(imageIds);

      // Preserve original order using the string ID list
      productDetailsDto.product.imageGallery = imageIdStrings.map(
        id => galleryImages.find(image => image.id === Number(id))
      ).filter(item => item !== undefined)
    }

    const seoMetaDto = new SeoMetaDto();
    seoMetaDto.metaType = SeoMEtaEnum.PRODUCT;
    seoMetaDto.referenceId = productDetailsDto.product.id;

    productDetailsDto.seoMeta = await this.seoMetaService.getByKey(seoMetaDto);
    productDetailsDto.seoMetaLocalization = {
      metaTitle: localization?.metaTitle,
      metaDescription: localization?.metaDescription,
      isActive: localization?.isActive,
    };
    // productDetailsDto.productTags = [];
    // productDetailsDto.productTags = [...await this.productTagsService.getTagDetailsByProductId(productObj.id)];

    // Store result in cache
    try {
      await this.cacheManager.set(cacheKey, productDetailsDto, (3600000)); // Cache for 1 hour
    } catch (error) {
      console.log('cache set error:::', error);
    }

    return new ServiceResponse(productDetailsDto, 'Product found successfully');
  }

  async getProductsByCountry(countryId: number): Promise<ServiceResponse> {
    if (!countryId) {
      return new ServiceResponse(null, `Please provide valid country id}`);
    }
    const result = await this.productMetaRepository.findByCountryId(countryId);
    // if(result && result.length>0){
    //   // let productList: ProductNameDropdownResponseDto[] = []
    //   // result?.forEach((data:ProductNameDropdownResponseDto)=>{
    //   //   let productObj: ProductNameDropdownResponseDto = new ProductNameDropdownResponseDto();
    //   //   productObj.id = result.id;
    //   //   productObj.name = result.name;
    //   //   productObj.categoryName = result.categoryName;
    //   //   productList.
    //   // });

    // }

    if (!result) {
      return new ServiceResponse(
        null,
        `Product not found for id:${countryId}! please try again`,
      );
    }
    return new ServiceResponse(
      result,
      `Product data found for id:${countryId}`,
    );
  }

  async findById(id: number): Promise<ServiceResponse> {
    if (!id) {
      return new ServiceResponse(null, `Please provide valid Product id}`);
    }
    const result = await this.repository.findById(id);
    if (!result) {
      return new ServiceResponse(
        result,
        `Product not found for id:${id}! please try again`,
      );
    }
    return new ServiceResponse(result, `Product data found for id:${id}`);
  }

  async searchByProduct(
    name: string,
    countryCode: string,
    languageId?: number,
  ): Promise<ServiceResponse> {
    languageId = languageId || 1;
    const result = await this.repository.searchByProduct(
      name,
      countryCode,
      languageId,
    );
    if (result && result?.length == 0) {
      return new ServiceResponse(
        result,
        `Product not found for name: ${name}! please try again`,
      );
    }
    return new ServiceResponse(result, `Product data found for name: ${name}`);
  }

  async getProductsByProductId(productId: number): Promise<ServiceResponse> {
    if (!productId) {
      return new ServiceResponse(
        null,
        `Please provide valid product id! Try again`,
      );
    }

    const productObj = await this.repository.findOne({
      where: { id: productId },
      relations: ['categories', 'localizations'],
    });

    if (!productObj) {
      return new ServiceResponse(
        productObj,
        `Product not found for id:${productId}! please try again`,
      );
    }

    // Map the categories by their ID
    const locale =
      productObj.localizations?.reduce((acc, localize) => {
        acc[localize.languageId] = {
          name: localize?.name || '',
          tagLine1: localize?.tagLine1 || '',
          tagLine2: localize?.tagLine2 || '',
          tagLine2Type: localize?.tagLine2Type || '',
          description: localize?.description || '',
          materialCare: localize?.materialCare || '',
          shippingReturn: localize?.shippingReturn || '',
          wowFactors: localize?.wowFactors || '',
          metaTitle: localize?.metaTitle || '',
          metaDescription: localize?.metaDescription || '',
        };
        return acc;
      }, {}) || {};

    const categories = productObj.categories.map((category) => category.id);

    const productResponseDto: ProductDetailsResponseDto =
      new ProductDetailsResponseDto();
    productResponseDto.product = new ProductViewResponseDto();
    productResponseDto.product.id = productObj.id;
    productResponseDto.product.name = productObj.name;
    productResponseDto.product.brandId = productObj.brandId;
    productResponseDto.product.categoryId = categories;
    productResponseDto.product.code = productObj.code;
    productResponseDto.product.sku = productObj.sku;
    productResponseDto.product.isActive = productObj.isActive;
    productResponseDto.product.isMultiVariantProduct =
      productObj.isMultiVariantProduct;
    productResponseDto.product.isPublish = productObj.isPublish;
    productResponseDto.product.isRefundable = productObj.isRefundable;
    productResponseDto.product.slug = productObj.slug;
    productResponseDto.product.description = productObj.description;
    productResponseDto.product.materialCare = productObj.materialCare;
    productResponseDto.product.shippingReturn = productObj.shippingReturn;

    productResponseDto.product.sortOrder = productObj.sortOrder;
    productResponseDto.product.tagLine1 = productObj.tagLine1;
    productResponseDto.product.tagLine2 = productObj.tagLine2;
    productResponseDto.product.tagLine2Type = productObj.tagLine2Type;
    productResponseDto.product.sizeChart = productObj.sizeChart;
    productResponseDto.product.sizeChartId = productObj.sizeChartId;

    productResponseDto.product.locale = locale;

    productResponseDto.product.limitedEdition = productObj.limitedEdition;
    productResponseDto.product.justArrived = productObj.justArrived;
    productResponseDto.product.mostPopular = productObj.mostPopular;
    productResponseDto.product.customerFavorite = productObj.customerFavorite;
    productResponseDto.product.bestValue = productObj.bestValue;
    productResponseDto.product.specialOffer = productObj.specialOffer;

    productResponseDto.product.wowFactors = productObj.wowFactors;
    productResponseDto.product.featuredImageId = productObj.featuredImageId;
    productResponseDto.product.hoverImageId = productObj.hoverImageId;
    const ids = productObj.imageGalleryIds?.split(',') || [];
    productResponseDto.product.imageGalleryIds = ids?.map((value: string) =>
      parseInt(value),
    );
    productResponseDto.product.productPrice = [];
    const productMetaList: ProductMeta[] =
      await this.productMetaRepository.findByProductId(productId);
    productResponseDto.product.countryIds = productMetaList
      .map((item: ProductMeta) => item.countryId)
      .filter((item) => !!item);

    const countryWiseProductVariant = [];
    const productVariant: ProductVariantResponseDto =
      new ProductVariantResponseDto();
    productResponseDto.productVariant = productVariant;
    productResponseDto.productVariant.countryWiseProductVariant = [];
    productResponseDto.productVariant.selectedAttributes = [];
    productResponseDto.productVariant.attributeImages = [];
    for (const productMetaObj of productMetaList) {
      // productMetaList?.forEach((productMetaObj: ProductMeta) => {
      const productPriceResponseDto: ProductPriceResponseDto =
        new ProductPriceResponseDto();
      productPriceResponseDto.countryId = productMetaObj.countryId;
      productPriceResponseDto.unitPrice = productMetaObj.unitPrice;
      productPriceResponseDto.discountPrice = productMetaObj.discountPrice;
      productPriceResponseDto.quantity = productMetaObj.quantity;
      productResponseDto.product.productPrice.push(productPriceResponseDto);

      //variant format
      if (productMetaObj.variants) {
        const variant: any = {};
        variant.countryId = productMetaObj.countryId;
        variant.isActive = productMetaObj.isActive;
        variant.productVariations = productMetaObj.variants;
        countryWiseProductVariant.push(variant);
      }
    }
    // });

    productResponseDto.productVariant.countryWiseProductVariant =
      countryWiseProductVariant;
    if (productObj.selectedAttributes) {
      productResponseDto.productVariant.selectedAttributes =
        productObj.selectedAttributes;
    }
    productResponseDto.productVariant.attributeImages =
      productObj.attributeImages;

    // product tags
    productResponseDto.productTags = [];
    productResponseDto.productTags = [
      ...(await this.productTagsService.getTagIdsByProductId(productId)),
    ];

    return new ServiceResponse(
      productResponseDto,
      `Product data found for id:${productId}`,
    );
  }

  // async update(
  //   id: number,
  //   updateProductDto: UpdateProductDto,
  // ): Promise<ServiceResponse> {
  //   const result = await this.repository.updateOne(id, updateProductDto);
  //   if (!result) {
  //     return new ServiceResponse(
  //       result,
  //       'Failed to update Product! please try again',
  //     );
  //   }
  //   return new ServiceResponse(result, 'Product successfully updated');
  // }

  async delete(id: number): Promise<ServiceResponse> {
    const result = await this.repository.destroy(id);
    if (!result) {
      return new ServiceResponse(
        null,
        `Failed to delete Product id: ${id}! please try again`,
      );
    }
    return new ServiceResponse(
      result,
      `Product id: ${id} successfully deleted`,
    );
  }

  async restoreData(id: number): Promise<ServiceResponse> {
    if (!id) {
      return new ServiceResponse(
        null,
        `Please provide valid product id! Try again`,
      );
    }
    const result = await this.repository.restoreData(id);
    if (!result) {
      return new ServiceResponse(
        null,
        `Failed to restore Product id: ${id}! please try again`,
      );
    }
    return new ServiceResponse(
      result,
      `Product id: ${id} successfully restored`,
    );
  }

  async activeOrInactive(
    id: number,
    status: boolean,
  ): Promise<ServiceResponse> {
    if (!id) {
      return new ServiceResponse(
        null,
        `Please provide valid product id! Try again`,
      );
    }
    const result = await this.repository.activeOrInactive(id, status);
    if (!result) {
      return new ServiceResponse(
        null,
        'Failed to restore Product! please try again',
      );
    }
    return new ServiceResponse(result, 'Product successfully actived');
  }

  async getProductsByParams(
    params: any,
    pageOptionsDto: PageOptionsDto,
  ): Promise<ServiceResponse> {
    const result = await this.repository.getProductsByParams(
      params,
      pageOptionsDto,
    );
    if (!result) {
      return new ServiceResponse(
        null,
        `Product not found for params:${params}! please try again`,
      );
    }
    return new ServiceResponse(
      result.itemList,
      'All product found successfully',
      result.meta,
    );
  }

  async stockManageBySku(
    productStockManageDto: ProductStockManageDto,
  ): Promise<ServiceResponse> {
    if (
      !productStockManageDto?.warehouse &&
      productStockManageDto?.items?.length == 0
    ) {
      return new ServiceResponse(
        null,
        `Please provide warehouse name or item missing. please try again`,
      );
    }
    const result = await this.repository.stockManageFromInventoryBySku(
      productStockManageDto,
    );
    if (!result) {
      return new ServiceResponse(
        null,
        `Failed to update! sku not found. please try again`,
      );
    }
    return new ServiceResponse(result, 'Successfully Stock updated');
  }

  async excelDownloadProductStockReport(queryFilterDto: {
    exportTo?: string;
    startDate: Date;
    endDate: Date;
  }): Promise<any> {
    const result =
      await this.repository.excelDownloadProductStockReport(queryFilterDto);
    if (!result) {
      return null;
    }
    return result;
  }
}
