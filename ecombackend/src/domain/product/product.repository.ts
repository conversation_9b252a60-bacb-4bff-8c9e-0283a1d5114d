import { Injectable } from '@nestjs/common';
import { Repository, DataSource, Like, ILike, In, QueryRunner } from 'typeorm';
import { PageOptionsDto } from 'src/common/pagination/page-options.dto';
import { PageDto } from 'src/common/pagination/page.dto';
import { PageMetaDto } from 'src/common/pagination/page-meta.dto';
import { Product, ProductLocalization } from '../entities/product.entity';
import { ProductMetaRepository } from './product-meta.repository';
import { ProductDto } from './dto/product.dto';
import { ProductMeta } from '../entities/product-meta.entity';
import { CountryWiseProductVariantDto } from './productVariantDto/country-wise-product-variant.dto';
import { ProductPriceDto } from './dto/product-price.dto';
import {
  createSlug,
  generateProductSlug,
} from 'src/common/utils/common-functions';
import { UpdateProductDto } from './dto/update-product.dto';
import { CreateProductVariantDto } from './productVariantDto/create-product-variant.dto';
import { CountryRepository } from '../country/country.repository';
import { ProductOrderDto } from '../product-order/productOrderDto/product-order.dto';
import { ProductSortingEnum } from 'src/common/enums/product-sorting';
import { ProductStockManageDto } from './dto/product-stock-manage.dto';
import { InventoryEnum } from 'src/common/enums/inventory.enum';
import { StockManagementTypeEnum } from 'src/common/enums/stock-management-type.enum';
import { CategoryRepository } from '../category/category.repository';
import { formatCategories } from 'src/product-feed/utils/helper';
import { InjectRepository } from '@nestjs/typeorm';
import { CreateSizeChartDtoView } from 'src/size-chart/dto/create-size-chart.dto';
import { DeliveryStatusEnum } from 'src/common/enums/delivery-status.enum';

@Injectable()
export class ProductRepository extends Repository<Product> {
  constructor(
    private readonly dataSource: DataSource,
    @InjectRepository(ProductLocalization)
    private readonly productLocalizationRepository: Repository<ProductLocalization>,
    private readonly productMetaRepo: ProductMetaRepository,
    private readonly countryRepo: CountryRepository,
    private readonly categoryRepository: CategoryRepository,
  ) {
    super(
      Product,
      dataSource.createEntityManager(),
      dataSource.createQueryRunner(),
    );
  }

  // public async findAll(page: number, limit: number): Promise<Product[]> {
  //   return this.find({
  //     take: limit,
  //     skip: (page - 1) * limit,
  //     withDeleted: false,
  //   });
  // }

  public async findAll(): Promise<Product[]> {
    return this.find();
  }

  public async saveUpdateProduct(product: Product): Promise<Product | null> {
    const newProduct = this.create(product);
    return this.save(newProduct);
  }

  public async findAllDataByPagination(
    params: {
      globalSearch?: string;
      skuSearch?: string;
      nameSearch?: string,
      categoryIds?: string,
      isPublish?: boolean,
      isActive?: boolean
    },
    pageOptionsDto: PageOptionsDto,
  ): Promise<PageDto<Product>> {
    // console.log('params:::', params);

    // const [list, itemCount] = await this.findAndCount({
    //   skip: pageOptionsDto.skip,
    //   take: pageOptionsDto.take,
    //   withDeleted: false,
    //   order: {
    //     id: 'DESC',
    //   },
    // });

    // const whereCondition = {};
    // const pageOptions = {
    //   skip: pageOptionsDto.skip,
    //   take: pageOptionsDto.take,
    // }
    // if (params?.globalSearch) {
    //   whereCondition['name'] = ILike(`%${params.globalSearch}%`);
    //   delete pageOptions.skip;
    //   delete pageOptions.take;
    // }

    // if (params?.skuSearch) {
    //   whereCondition['sku'] = ILike(`%${params.skuSearch}%`);
    //   delete pageOptions.skip;
    //   delete pageOptions.take;
    // }

    // // if (params?.categoryIds) {
    // //   const categoryIds = params.categoryIds.split(',').map(id => id.trim());
    // //   whereCondition['categories.id'] = In(categoryIds);
    // //   delete pageOptions.skip;
    // //   delete pageOptions.take;
    // // }
    // if (params?.categoryIds) {
    //   const categoryIds = params.categoryIds.split(',').map(id => id.trim());

    //   query.andWhere('category.id IN (:...categoryIds)', { categoryIds });

    //   // Optional: if you want to filter out pagination when filtering by category
    //   delete pageOptions.skip;
    //   delete pageOptions.take;
    // } else {
    //   query.skip(pageOptions.skip).take(pageOptions.take);
    // }

    // // const pageMetaDto = new PageMetaDto({ itemCount, pageOptionsDto });

    // // return new PageDto(list, pageMetaDto);
    // // const whereCondition = params?.globalSearch
    // //   ? { name: ILike(`%${params.globalSearch}%`) }
    // //   : {};

    // const [list, itemCount] = await this.findAndCount({
    //   where: whereCondition,
    //   ...pageOptions,
    //   withDeleted: false,
    //   relations: ['categories', 'suggestions.suggestedProduct', 'productRelated'],
    //   order: {
    //     id: 'DESC',
    //   },
    // });

    // @ManyToOne(() => Brand, { eager: true })
    // @JoinColumn({ name: 'brand_id', referencedColumnName: 'id' })
    // brand: Brand;

    // @ManyToOne(() => ImageGallery, { eager: true })
    // @JoinColumn({ name: 'featured_image_id', referencedColumnName: 'id' })
    // featuredImage: ImageGallery;

    // @ManyToOne(() => ImageGallery, { eager: true })
    // @JoinColumn({ name: 'hover_image_id', referencedColumnName: 'id' })
    // hoverImage: ImageGallery;

    // @ManyToOne(() => ImageGallery, { eager: true })
    // @JoinColumn({ name: 'size_chart', referencedColumnName: 'id' })
    // sizeChartImg: ImageGallery;

    let skip = pageOptionsDto.skip;
    let take = pageOptionsDto.take;

    const query = this.createQueryBuilder('product')
      .leftJoinAndSelect('product.categories', 'category')
      .leftJoinAndSelect('product.suggestions', 'suggestions')
      .leftJoinAndSelect('suggestions.suggestedProduct', 'suggestedProduct')
      .leftJoinAndSelect('product.productRelated', 'productRelated')
      .leftJoinAndSelect('product.brand', 'brand')
      .leftJoinAndSelect('product.featuredImage', 'featuredImage')
      .leftJoinAndSelect('product.hoverImage', 'hoverImage')
      // .leftJoinAndSelect('product.sizeChart', 'sizeChartImg')
      .where('1 = 1');

    if (params?.globalSearch) {
      query.andWhere('LOWER(product.name) LIKE LOWER(:name)', {
        name: `%${params.globalSearch}%`,
      });
      skip = 0;
      take = 0;
    }

    if (params?.skuSearch) {
      query.andWhere('product.sku LIKE :sku', {
        sku: `%${params.skuSearch}%`,
      });
      skip = 0;
      take = 0;
    }

    if (params?.categoryIds) {
      const categoryIdList = params?.categoryIds.split(',').map((id) => id.trim());
      query.andWhere('category.id IN (:...categoryIdList)', { categoryIdList });
      skip = 0;
      take = 0;
    }

    if (params?.isPublish) {
      query.andWhere('product.is_publish = :isPublish', {
        isPublish: params.isPublish,
      });
    }

    if (params?.isActive) {
      query.andWhere('product.is_active = :isActive', {
        isActive: params.isActive,
      });
    }


    const [list, itemCount] = await query
      .skip(skip)
      .take(take)
      .orderBy('product.id', 'DESC')
      .getManyAndCount();

    const pageMetaDto = new PageMetaDto({ itemCount, pageOptionsDto });

    return new PageDto(list, pageMetaDto);
  }

  public async findAllDataByCountryIdPagination(
    countryId: number,
    pageOptionsDto: PageOptionsDto,
    languageId?: number,
  ): Promise<PageDto<any>> {
    languageId = languageId || 1;

    const query = `
    SELECT p.id, COALESCE(localization.name, p.name) as name, p.is_multi_variant_product, p.slug, p.hover_image_id, p.featured_image_id
    FROM product p
    INNER JOIN product_meta pm ON pm.product_id = p.id
    LEFT JOIN product_localization localization ON (localization.product_id = p.id AND localization.language_id = ${languageId})
    WHERE pm.country_id = $1 AND p.is_publish is true AND pm.deleted_at is null AND p.deleted_at is null AND p.is_active is true AND pm.is_active is true 
    order by p.id 
    Limit $2 offset $3`;

    const countQuery = `
      SELECT COUNT(p.id) AS total
      FROM Product p
      INNER JOIN product_meta pm ON p.id = pm.product_id
      WHERE  pm.country_id = $1 AND p.is_publish is true AND pm.deleted_at is null AND p.deleted_at is null AND p.is_active is true AND pm.is_active is true`;

    const [data, count] = await Promise.all([
      this.query(query, [countryId, pageOptionsDto.take, pageOptionsDto.skip]),
      this.query(countQuery, [countryId]),
    ]);

    const itemCount: number = parseInt(count[0].total);
    const pageMetaDto = new PageMetaDto({ itemCount, pageOptionsDto });

    return new PageDto(data, pageMetaDto);
  }

  /**
   *
   * @deprecated
   */
  public async countryCategoryWiseProductsPagination(
    countryId: number,
    categoryId: number,
    pageOptionsDto: PageOptionsDto,
    languageId?: number,
  ): Promise<PageDto<any>> {
    languageId = languageId || 1;

    const query = `
  WITH main_products AS (
    SELECT DISTINCT p.id
    FROM product p
    INNER JOIN product_meta pm ON pm.product_id = p.id
    LEFT JOIN category c ON c.id = p.category_id 
    WHERE pm.country_id = $1
    AND (c.id = $2 OR c.id IN (SELECT c2.id FROM category c2 WHERE c2.parent_id = $2))
    AND p.is_publish = true 
    AND pm.deleted_at IS NULL 
    AND p.deleted_at IS NULL 
    AND p.is_active = true 
    AND pm.is_active = true
),
tagged_products AS (
    SELECT DISTINCT ptm2.product_id
    FROM product_tags_meta ptm1
    INNER JOIN product_tags_meta ptm2 ON ptm1.product_tag_id = ptm2.product_tag_id
    WHERE ptm1.product_id IN (SELECT id FROM main_products) AND ptm2.deleted_at IS NULL 
    AND ptm2.is_active = true
)
SELECT p.id, COALESCE(localization.name, p.name) AS name, p.is_multi_variant_product, p.slug, p.category_id, c.slug as cat_slug, p.hover_image_id, p.featured_image_id, pm.quantity, pm.discount_price as discountPrice ,pm.unit_price as unitPrice,
     pm.variants, 
     (select name from category c2 where c2.id = c.parent_id) as cat_parent_name,
     p.limited_edition, p.just_arrived, p.most_popular, p.customer_favorite, p.best_value, p.special_offer 
     ,igf.id as featured_image_id, igf.name as featured_image_name, igf.image_url as featured_imageurl, igf.original_image_url as featured_original_imageurl, igf.image_gallery_urls as featured_image_gallery_urls
	 ,igh.id as hover_image_id, igh.name as hover_image_name, igh.image_url as hover_imageurl, igh.original_image_url as hover_original_imageurl, igh.image_gallery_urls as hover_image_gallery_urls
FROM product p 
INNER JOIN product_meta pm ON (pm.product_id = p.id AND country_id = $1)
LEFT JOIN category c ON c.id = p.category_id
JOIN image_gallery igf ON igf.id = p.featured_image_id 
JOIN image_gallery igh ON igh.id = p.hover_image_id
LEFT JOIN product_localization localization ON (localization.product_id = p.id AND localization.language_id = ${languageId}) 
WHERE p.id IN (SELECT id FROM main_products)
   OR p.id IN (SELECT product_id FROM tagged_products)
GROUP BY p.id, c.id, c.slug
ORDER BY p.sort_order, p.id
Limit $3 offset $4
  `;

    const countQuery = `
      WITH main_products AS (
    SELECT DISTINCT p.id
    FROM product p
    INNER JOIN product_meta pm ON pm.product_id = p.id
    LEFT JOIN category c ON c.id = p.category_id 
    WHERE pm.country_id = $1
    AND (c.id = $2 OR c.id IN (SELECT c2.id FROM category c2 WHERE c2.parent_id = $2))
    AND p.is_publish = true 
    AND pm.deleted_at IS NULL 
    AND p.deleted_at IS NULL 
    AND p.is_active = true 
    AND pm.is_active = true
),
tagged_products AS (
    SELECT DISTINCT ptm2.product_id
    FROM product_tags_meta ptm1
    INNER JOIN product_tags_meta ptm2 ON ptm1.product_tag_id = ptm2.product_tag_id
    WHERE ptm1.product_id IN (SELECT id FROM main_products) AND ptm2.deleted_at IS NULL 
    AND ptm2.is_active = true
)
SELECT COUNT(p.id) as total
FROM product p 
INNER JOIN product_meta pm ON (pm.product_id = p.id AND country_id = $1)
LEFT JOIN category c ON c.id = p.category_id
JOIN image_gallery igf ON igf.id = p.featured_image_id 
JOIN image_gallery igh ON igh.id = p.hover_image_id 
WHERE p.id IN (SELECT id FROM main_products)
   OR p.id IN (SELECT product_id FROM tagged_products)`;

    const [data, count] = await Promise.all([
      this.query(query, [
        countryId,
        categoryId,
        pageOptionsDto.take,
        pageOptionsDto.skip,
      ]),
      this.query(countQuery, [countryId, categoryId]),
    ]);

    const itemCount: number = parseInt(count[0].total);
    const pageMetaDto = new PageMetaDto({ itemCount, pageOptionsDto });

    return new PageDto(data, pageMetaDto);
  }

  public async getRelatedProductsById(
    countryId: number,
    productId: number,
    pageOptionsDto: PageOptionsDto,
    languageId?: number,
  ): Promise<PageDto<any>> {
    languageId = languageId || 1;

    // Step 1: Fetch all related category IDs for the given productId using a recursive CTE
    const categoryIdsQuery = `
      WITH RECURSIVE category_hierarchy AS (
        SELECT pc.category_id
        FROM product_category pc
        WHERE pc.product_id = $1
        
        UNION ALL
        
        SELECT c2.id
        FROM category c2
        INNER JOIN category_hierarchy ch ON c2.parent_id = ch.category_id
      )
      SELECT DISTINCT category_id FROM category_hierarchy
    `;

    const categoryIdsResult = await this.query(categoryIdsQuery, [productId]);
    const categoryIds = categoryIdsResult.map((row: any) => row.category_id);

    if (categoryIds.length === 0) {
      // If no categories are found, return an empty response
      return new PageDto([], new PageMetaDto({ itemCount: 0, pageOptionsDto }));
    }

    // Step 2: Adjust the main and count queries using the category IDs obtained earlier
    const query = `
      WITH main_products AS (
        SELECT DISTINCT p.id
        FROM product p
        INNER JOIN product_meta pm ON pm.product_id = p.id
        WHERE pm.country_id = $1
        AND p.is_publish = true 
        AND pm.deleted_at IS NULL 
        AND p.deleted_at IS NULL 
        AND p.is_active = true 
        AND pm.is_active = true
      ),
      tagged_products AS (
        SELECT DISTINCT ptm2.product_id
        FROM product_tags_meta ptm1
        INNER JOIN product_tags_meta ptm2 ON ptm1.product_tag_id = ptm2.product_tag_id
        WHERE ptm1.product_id IN (SELECT id FROM main_products) AND ptm2.deleted_at IS NULL 
        AND ptm2.is_active = true
      )
      SELECT p.id, COALESCE(localization.name, p.name) AS name, p.is_multi_variant_product, p.slug, p.hover_image_id, p.featured_image_id, pm.quantity, pm.discount_price as discountPrice ,pm.unit_price as unitPrice,
           pm.variants, 
           p.limited_edition, p.just_arrived, p.most_popular, p.customer_favorite, p.best_value, p.special_offer 
           ,igf.id as featured_image_id, igf.name as featured_image_name, igf.image_url as featured_imageurl, igf.original_image_url as featured_original_imageurl, igf.image_gallery_urls as featured_image_gallery_urls
         ,igh.id as hover_image_id, igh.name as hover_image_name, igh.image_url as hover_imageurl, igh.original_image_url as hover_original_imageurl, igh.image_gallery_urls as hover_image_gallery_urls
      FROM product p 
      INNER JOIN product_meta pm ON (pm.product_id = p.id AND country_id = $1)
      JOIN image_gallery igf ON igf.id = p.featured_image_id 
      JOIN image_gallery igh ON igh.id = p.hover_image_id
      LEFT JOIN product_localization localization ON (localization.product_id = p.id AND localization.language_id = ${languageId}) 
      WHERE (p.id IN (SELECT id FROM main_products)
         OR p.id IN (SELECT product_id FROM tagged_products))
        AND p.id <> $2  -- Exclude the original product
        AND EXISTS (
          SELECT 1
          FROM product_category pc
          WHERE pc.product_id = p.id
          AND pc.category_id = ANY($3::int[])
        )
      ORDER BY p.sort_order, p.id
      LIMIT $4 OFFSET $5
    `;

    const countQuery = `
      WITH main_products AS (
        SELECT DISTINCT p.id
        FROM product p
        INNER JOIN product_meta pm ON pm.product_id = p.id
        WHERE pm.country_id = $1
        AND p.is_publish = true 
        AND pm.deleted_at IS NULL 
        AND p.deleted_at IS NULL 
        AND p.is_active = true 
        AND pm.is_active = true
      ),
      tagged_products AS (
        SELECT DISTINCT ptm2.product_id
        FROM product_tags_meta ptm1
        INNER JOIN product_tags_meta ptm2 ON ptm1.product_tag_id = ptm2.product_tag_id
        WHERE ptm1.product_id IN (SELECT id FROM main_products) AND ptm2.deleted_at IS NULL 
        AND ptm2.is_active = true
      )
      SELECT COUNT(p.id) as total
      FROM product p 
      INNER JOIN product_meta pm ON (pm.product_id = p.id AND country_id = $1)
      JOIN image_gallery igf ON igf.id = p.featured_image_id 
      JOIN image_gallery igh ON igh.id = p.hover_image_id
      WHERE (p.id IN (SELECT id FROM main_products)
         OR p.id IN (SELECT product_id FROM tagged_products))
        AND p.id <> $2  -- Exclude the original product
        AND EXISTS (
          SELECT 1
          FROM product_category pc
          WHERE pc.product_id = p.id
          AND pc.category_id = ANY($3::int[])
        )
    `;

    // Execute the main query and count query
    const [data, count] = await Promise.all([
      this.query(query, [
        countryId,
        productId,
        categoryIds,
        pageOptionsDto.take,
        pageOptionsDto.skip,
      ]),
      this.query(countQuery, [countryId, productId, categoryIds]),
    ]);

    const itemCount: number = parseInt(count[0].total);
    const pageMetaDto = new PageMetaDto({ itemCount, pageOptionsDto });

    return new PageDto(data, pageMetaDto);
  }

  public async webCountryCategoryWiseProductsPagination(
    countryId: number,
    categoryId: number,
    pageOptionsDto: PageOptionsDto,
    languageId?: number,
  ): Promise<PageDto<any>> {
    languageId = languageId || 1;

    const query = `WITH main_products AS (
          SELECT DISTINCT p.id
          FROM product p
          INNER JOIN product_meta pm ON pm.product_id = p.id
          WHERE pm.country_id = $1
          AND p.is_publish = true 
          AND pm.deleted_at IS NULL 
          AND p.deleted_at IS NULL 
          AND p.is_active = true 
          AND pm.is_active = true
      ),
      tagged_products AS (
          SELECT DISTINCT ptm2.product_id
          FROM product_tags_meta ptm1
          INNER JOIN product_tags_meta ptm2 ON ptm1.product_tag_id = ptm2.product_tag_id
          WHERE ptm1.product_id IN (SELECT id FROM main_products) AND ptm2.deleted_at IS NULL 
          AND ptm2.is_active = true
      )
      SELECT p.sort_order,p.id, COALESCE(localization.name, p.name) AS name, p.is_multi_variant_product, p.slug, p.hover_image_id, p.featured_image_id, pm.quantity, pm.discount_price as discountPrice ,pm.unit_price as unitPrice,
          pm.variants, 
          p.limited_edition, p.just_arrived, p.most_popular, p.customer_favorite, p.best_value, p.special_offer 
          ,igf.id as featured_image_id, igf.name as featured_image_name, igf.image_url as featured_imageurl, igf.original_image_url as featured_original_imageurl, igf.image_gallery_urls as featured_image_gallery_urls
        ,igh.id as hover_image_id, igh.name as hover_image_name, igh.image_url as hover_imageurl, igh.original_image_url as hover_original_imageurl, igh.image_gallery_urls as hover_image_gallery_urls
      FROM product p 
      INNER JOIN product_meta pm ON (pm.product_id = p.id AND country_id = $1)
      JOIN image_gallery igf ON igf.id = p.featured_image_id 
      JOIN image_gallery igh ON igh.id = p.hover_image_id
      LEFT JOIN product_localization localization ON (localization.product_id = p.id AND localization.language_id = ${languageId}) 
      WHERE (p.id IN (SELECT id FROM main_products)
        OR p.id IN (SELECT product_id FROM tagged_products))

        AND EXISTS (
              SELECT 1
              FROM product_category pc
              WHERE pc.product_id = p.id
              AND (pc.category_id = $2 OR pc.category_id IN (
                  SELECT c2.id
                  FROM category c2
                  WHERE c2.parent_id = $2
              ))
          )
      ORDER BY p.sort_order, p.id desc
      LIMIT $3 OFFSET $4
    `;

    const countQuery = `WITH main_products AS (
        SELECT DISTINCT p.id
        FROM product p
        INNER JOIN product_meta pm ON pm.product_id = p.id
        WHERE pm.country_id = $1
        AND p.is_publish = true 
        AND pm.deleted_at IS NULL 
        AND p.deleted_at IS NULL 
        AND p.is_active = true 
        AND pm.is_active = true
      ),
      tagged_products AS (
          SELECT DISTINCT ptm2.product_id
          FROM product_tags_meta ptm1
          INNER JOIN product_tags_meta ptm2 ON ptm1.product_tag_id = ptm2.product_tag_id
          WHERE ptm1.product_id IN (SELECT id FROM main_products) AND ptm2.deleted_at IS NULL 
          AND ptm2.is_active = true
      )
      SELECT COUNT(p.id) as total
      FROM product p 
      INNER JOIN product_meta pm ON (pm.product_id = p.id AND country_id = $1)
      JOIN image_gallery igf ON igf.id = p.featured_image_id 
      JOIN image_gallery igh ON igh.id = p.hover_image_id 
      WHERE (p.id IN (SELECT id FROM main_products)
        OR p.id IN (SELECT product_id FROM tagged_products))
          AND EXISTS (
              SELECT 1
              FROM product_category pc
              WHERE pc.product_id = p.id
              AND (pc.category_id = $2 OR pc.category_id IN (
                  SELECT c2.id
                  FROM category c2
                  WHERE c2.parent_id = $2
              ))
          )`;

    // Create QueryRunner for transaction
    const queryRunner: QueryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Execute queries inside transaction
      const [data, count] = await Promise.all([
        queryRunner.manager.query(query, [
          countryId,
          categoryId,
          pageOptionsDto.take,
          pageOptionsDto.skip,
        ]),
        queryRunner.manager.query(countQuery, [countryId, categoryId]),
      ]);

      // Commit transaction
      await queryRunner.commitTransaction();

      // Parse and return results
      const itemCount: number = parseInt(count[0]?.total || '0');
      const pageMetaDto = new PageMetaDto({ itemCount, pageOptionsDto });

      return new PageDto(data, pageMetaDto);
    } catch (error) {
      console.log('error', error);
      await queryRunner.rollbackTransaction();
      throw error;
    }
    finally {
      console.log('release start');
      await queryRunner.release();
      console.log('release end');
    }
  }


  public async countryBrandWiseProductsPagination(
    countryId: number,
    brandSlug: string,
    pageOptionsDto: PageOptionsDto,
    languageId?: number,
  ): Promise<PageDto<any>> {
    languageId = languageId || 1;

    const query = `
    SELECT p.id, COALESCE(localization.name, p.name) AS name, p.is_multi_variant_product, p.slug, p.brand_id, b.name as brand_name, p.hover_image_id, p.featured_image_id, pm.quantity, pm.discount_price as discountPrice ,pm.unit_price as unitPrice,
    pm.variants 
    
    ,igf.id as featured_image_id, igf.name as featured_image_name, igf.image_url as featured_imageurl, igf.original_image_url as featured_original_imageurl, igf.image_gallery_urls as featured_image_gallery_urls
	,igh.id as hover_image_id, igh.name as hover_image_name, igh.image_url as hover_imageurl, igh.original_image_url as hover_original_imageurl, igh.image_gallery_urls as hover_image_gallery_urls

    FROM product p
    INNER JOIN product_meta pm ON pm.product_id = p.id
    LEFT JOIN  brand b ON b.id = p.brand_id 
    join image_gallery igf on igf.id = p.featured_image_id 
    join image_gallery igh on igh.id = p.hover_image_id
    LEFT JOIN product_localization localization ON (localization.product_id = p.id AND localization.language_id = ${languageId}) 
    WHERE  pm.country_id = $1
    AND b.slug = $2 
     AND p.is_publish is true AND pm.deleted_at is null AND p.deleted_at is null AND p.is_active is true AND pm.is_active is true 
    order by p.sort_order, p.id 
    Limit $3 offset $4`;

    const countQuery = `
      SELECT COUNT(p.id) AS total
      FROM Product p
      INNER JOIN product_meta pm ON p.id = pm.product_id
      LEFT JOIN  brand b ON b.id = p.brand_id 
      WHERE  pm.country_id = $1
      AND b.slug = $2
      AND p.is_publish is true AND pm.deleted_at is null AND p.deleted_at is null AND p.is_active is true AND pm.is_active is true`;

    const [data, count] = await Promise.all([
      this.query(query, [
        countryId,
        brandSlug,
        pageOptionsDto.take,
        pageOptionsDto.skip,
      ]),
      this.query(countQuery, [countryId, brandSlug]),
    ]);

    const itemCount: number = parseInt(count[0].total);
    const pageMetaDto = new PageMetaDto({ itemCount, pageOptionsDto });

    return new PageDto(data, pageMetaDto);
  }

  public async searchProductByNameCountry(
    name: string,
    countryCode: string,
    pageOptionsDto: PageOptionsDto,
    languageId?: number,
  ): Promise<PageDto<any>> {
    languageId = languageId || 1;

    // Simplified search query based on the working searchByProduct method
    // but with full product data and pagination support
    const query = `
      SELECT DISTINCT p.id,
             COALESCE(localization.name, p.name) as name,
             p.is_multi_variant_product,
             p.slug,
             p.hover_image_id,
             p.featured_image_id,
             co.id as countryId,
             pm.quantity,
             pm.discount_price as discountPrice,
             pm.unit_price as unitPrice,
             pm.variants,
             p.sort_order
      FROM product p
      INNER JOIN product_meta pm ON pm.product_id = p.id
      LEFT JOIN country co ON co.id = pm.country_id
      LEFT JOIN product_localization localization ON (localization.product_id = p.id AND localization.language_id = $3)
      WHERE co.code = $2
        AND (
          p.name ILIKE '%' || $1::text || '%'
          OR localization.name ILIKE '%' || $1::text || '%'
        )
        AND p.is_publish = true
        AND pm.deleted_at IS NULL
        AND p.deleted_at IS NULL
        AND p.is_active = true
        AND pm.is_active = true
      ORDER BY p.sort_order ASC, p.id DESC
      LIMIT $4 OFFSET $5`;

    const countQuery = `
      SELECT COUNT(DISTINCT p.id) AS total
      FROM product p
      INNER JOIN product_meta pm ON pm.product_id = p.id
      LEFT JOIN country co ON co.id = pm.country_id
      LEFT JOIN product_localization localization ON (localization.product_id = p.id AND localization.language_id = $3)
      WHERE co.code = $2
        AND (
          p.name ILIKE '%' || $1::text || '%'
          OR localization.name ILIKE '%' || $1::text || '%'
        )
        AND p.is_publish = true
        AND pm.deleted_at IS NULL
        AND p.deleted_at IS NULL
        AND p.is_active = true
        AND pm.is_active = true`;

    const [data, count] = await Promise.all([
      this.query(query, [
        name,
        countryCode,
        languageId,
        pageOptionsDto.take,
        pageOptionsDto.skip,
      ]),
      this.query(countQuery, [name, countryCode, languageId]),
    ]);

    const itemCount: number = parseInt(count[0].total);
    const pageMetaDto = new PageMetaDto({ itemCount, pageOptionsDto });

    return new PageDto(data, pageMetaDto);
  }
  public async searchProductList(
    name: string,
    pageOptionsDto: PageOptionsDto,
  ): Promise<PageDto<Product>> {
    const [list, itemCount] = await this.findAndCount({
      where: [{ name: ILike(`%${name}%`) }],
      skip: pageOptionsDto.skip,
      take: pageOptionsDto.take,
      withDeleted: false,
      order: {
        id: 'DESC',
      },
    });

    const pageMetaDto = new PageMetaDto({ itemCount, pageOptionsDto });

    return new PageDto(list, pageMetaDto);
  }

  public async findById(id: number): Promise<Product | null> {
    return this.findOneBy({ id: id });
  }

  public async getProductBySlug(slug: string): Promise<Product | null> {
    return this.findOne({
      where: { slug: slug },
      order: {
        id: 'DESC',
      },
    });
  }

  /**
   * @deprecated
   */
  public async getProductByProductSlugCountryId(
    slug: string,
    countryId: number,
  ): Promise<any | null> {
    const query = `select p.id, p.name, p.description, p.material_care, p.shipping_return,  p.wow_factors,  p.sort_order,  p.tag_line1, p.tag_line2,p.tag_line2_type,
    p.is_refundable, p.is_active, p.category_id, p.brand_id, p.is_multi_variant_product, p.is_combo_pack_product, p.slug,  p.image_gallery_ids,
    p.selected_attributes, p.attribute_images,
    pm.unit_price, pm.discount_price, pm.sku, pm.quantity, pm.variants, pm.combo_type, c.slug as cat_slug,
    (select name from category c2 where c2.id = c.parent_id) as cat_parent_name,
    p.limited_edition, p.just_arrived, p.most_popular, p.customer_favorite, p.best_value, p.special_offer 

    ,igf.id as featured_image_id, igf.name as featured_image_name, igf.image_url as featured_imageurl, igf.original_image_url as featured_original_imageurl, igf.image_gallery_urls as featured_image_gallery_urls

    ,igs.id as size_chart_image_id, igs.name as size_chart_image_name, igs.image_url as size_chart_imageurl, igs.original_image_url as size_chart_original_imageurl, igs.image_gallery_urls as size_chart_image_gallery_urls

    ,igh.id as hover_image_id, igh.name as hover_image_name, igh.image_url as hover_imageurl, igh.original_image_url as hover_original_imageurl, igh.image_gallery_urls as hover_image_gallery_urls
      
    from product p 
    join product_meta pm on (pm.product_id = p.id)
    join category c on (c.id = p.category_id)
    left join image_gallery igf on igf.id = p.featured_image_id 
    left join image_gallery igh on igh.id = p.hover_image_id
    left join image_gallery igs on igs.id = p.size_chart
    where p.slug='${slug}' and pm.country_id=${countryId} and pm.deleted_at IS NULL`;
    const data = await this.manager.query(query);

    return data && data.length > 0 ? data[0] : null;
  }

  public async findSizeChartValue(
    chartId: number,
    countryId: number,
  ): Promise<CreateSizeChartDtoView | null> {

    console.log('findSizeChartValue:::', chartId, countryId);

    // ✅ Create QueryRunner for Transaction
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const sizeChartValuesQuery = `
        SELECT 
          sv.name, 
          sv.values, 
          sv.image_id, 
          sv.chart_images,
          sv.note,
          im.image_url AS main_image_url,
          json_agg(json_build_object('id', img.id, 'image_url', img.image_url)) AS chart_image_urls
        FROM size_chart_value sv
        LEFT JOIN image_gallery im ON im.id = sv.image_id
        LEFT JOIN LATERAL (
          SELECT id, image_url
          FROM image_gallery
          WHERE id IN (
            SELECT (elem ->> 'imageId')::int
            FROM jsonb_array_elements(sv.chart_images) AS elem
          )
        ) img ON true
        WHERE sv.size_chart_id = $1 AND sv."countryId" = $2
        GROUP BY sv.id, im.image_url
        LIMIT 1;
      `;

      const sizeChartValues = await this.query(sizeChartValuesQuery, [
        chartId,
        countryId,
      ]);

      if (sizeChartValues.length > 0) {
        const item = sizeChartValues[0];
        return {
          title: item.name,
          payload: item.values,
          imageId: item.image_id,
          src: item.main_image_url,
          note: item.note,
          chartImages: item?.chart_images?.map((chartImg) => {
            const match = item?.chart_image_urls?.find((img) => img.id === chartImg.imageId);
            return {
              ...chartImg,
              imageUrl: match?.image_url || null,
            };
          }),
        };
      }

      const sizeChartQuery = `
        SELECT 
          sc.name, 
          sc.values, 
          sc.image_id, 
          sc.chart_images, 
          sc.note,
          im.image_url AS main_image_url,
          json_agg(
            json_build_object('id', img.id, 'image_url', img.image_url)
          ) AS chart_image_urls
        FROM size_chart sc
        LEFT JOIN image_gallery im ON im.id = sc.image_id
        LEFT JOIN LATERAL (
          SELECT id, image_url
          FROM image_gallery
          WHERE id IN (
            SELECT (elem ->> 'imageId')::int
            FROM jsonb_array_elements(sc.chart_images) AS elem
          )
        ) img ON true
        WHERE sc.id = $1
        GROUP BY sc.id, im.image_url
        LIMIT 1;
      `;

      const sizeChart = await this.query(sizeChartQuery, [chartId]);

      // ✅ Commit transaction after successful execution
      await queryRunner.commitTransaction();

      if (sizeChart.length) {
        const item = sizeChart[0];
        return {
          title: item.name,
          payload: item.values,
          imageId: item.image_id,
          src: item.main_image_url,
          note: item.note,
          chartImages: item?.chart_images?.map((chartImg) => {
            const match = item.chart_image_urls.find((img) => img.id === chartImg.imageId);
            return {
              ...chartImg,
              imageUrl: match?.image_url || null,
            };
          }),
        };
      }
      return null;
    } catch (error) {
      // 🔴 Rollback transaction on error
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      // ✅ Always release QueryRunner to prevent idle connections
      await queryRunner.release();
    }
  }

  public async getWebProductByProductSlugCountryId(
    slug: string,
    countryId: number,
  ): Promise<any | null> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const data = await queryRunner.manager
        .createQueryBuilder('product', 'p')
        .select([
          'p.id as id',
          'p.name as name',
          'p.sku as parent_sku',
          'p.size_chart_id as size_chart_id',
          'p.description as description',
          'p.material_care as material_care',
          'p.shipping_return as shipping_return',
          'p.wow_factors as wow_factors',
          'p.sort_order as sort_order',
          'p.tag_line1 as tag_line1',
          'p.tag_line2 as tag_line2',
          'p.tag_line2_type as tag_line2_type',
          'p.is_refundable as is_refundable',
          'p.is_active as is_active',
          'p.brand_id as brand_id',
          'p.is_multi_variant_product as is_multi_variant_product',
          'p.is_combo_pack_product as is_combo_pack_product',
          'p.slug as slug',
          'p.image_gallery_ids as image_gallery_ids',
          'p.selected_attributes as selected_attributes',
          'p.attribute_images as attribute_images',
          'p.limited_edition as limited_edition',
          'p.just_arrived as just_arrived',
          'p.most_popular as most_popular',
          'p.customer_favorite as customer_favorite',
          'p.best_value as best_value',
          'p.special_offer as special_offer',
          'b.name as brand_name',
        ])
        .addSelect([
          'pm.unit_price as unit_price',
          'pm.discount_price as discount_price',
          'pm.sku as sku',
          'pm.quantity as quantity',
          'pm.variants as variants',
          'pm.combo_type as combo_type',
        ])
        .addSelect([
          'igf.id as featured_image_id',
          'igf.name as featured_image_name',
          'igf.image_url as featured_imageurl',
          'igf.original_image_url as featured_original_imageurl',
          'igf.image_gallery_urls as featured_image_gallery_urls',
          'igf.image_type as igf_image_type',
          'igf.image_mime_type as igf_image_mime_type',
        ])
        .addSelect([
          'igh.id as hover_image_id',
          'igh.name as hover_image_name',
          'igh.image_url as hover_imageurl',
          'igh.original_image_url as hover_original_imageurl',
          'igh.image_gallery_urls as hover_image_gallery_urls',
          'igf.image_type as igh_image_type',
          'igf.image_mime_type as igh_image_mime_type',
        ])
        .addSelect([
          'igs.id  as size_chart_image_id',
          'igs.name as size_chart_image_name',
          'igs.image_url as size_chart_imageurl',
          'igs.original_image_url as size_chart_original_imageurl',
          'igs.image_gallery_urls as size_chart_image_gallery_urls',
          'igs.image_type as igs_image_type',
          'igs.image_mime_type as igs_image_mime_type'
        ])
        .addSelect(['pcur.currency as currency'])
        .leftJoin('p.productMeta', 'pm')
        .leftJoin('p.brand', 'b')
        .leftJoin('pm.country', 'ctn')
        .leftJoin('ctn.currency', 'pcur')
        .leftJoin('p.featuredImage', 'igf')
        .leftJoin('p.hoverImage', 'igh')
        .leftJoin('p.sizeChartImg', 'igs')
        .where('p.slug = :slug', { slug })
        .andWhere('p.is_active = :isActive', { isActive: true })
        .andWhere('p.deleted_at IS NULL')
        .andWhere('p.is_publish = :isPublish', { isPublish: true })
        .andWhere('pm.country_id = :countryId', { countryId })
        .getRawOne();

      await queryRunner.commitTransaction(); // ✅ Commit transaction
      return data
    }
    catch (error) {
      console.log('error', error);
      await queryRunner.rollbackTransaction();
      throw error;
    }
    finally {
      console.log('release start');
      await queryRunner.release();
      console.log('release end');
    }
  }

  public async getWebProductCategories(
    slug: string,
    countryId: number,
  ): Promise<any | null> {
    const queryRunner: QueryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction(); // ✅ Begin transaction

    try {
      const categories = await queryRunner.manager.createQueryBuilder('product', 'p')
        .select([
          'c.id as category_id',
          'c.name as category_name',
          'c.slug as category_slug',
          'c.locale as category_locale',
          'parent.id as parent_id',
          'parent.name as parent_name',
          'parent.slug as parent_slug',
          'parent.locale as parent_locale',
        ])
        .innerJoin('p.categories', 'c')
        .leftJoin('c.parent', 'parent')
        .innerJoin('p.productMeta', 'pm', 'pm.country_id = :countryId', {
          countryId,
        })
        .where('p.slug = :slug', { slug })
        .getRawMany();

      const categoryMap = new Map();
      categories.forEach((category) => {
        const {
          parent_id,
          parent_name,
          parent_slug,
          parent_locale,
          category_id,
          category_name,
          category_slug,
          category_locale,
        } = category;

        // Push parent category
        if (parent_id) {
          if (!categoryMap.has(parent_id)) {
            categoryMap.set(parent_id, {
              id: parent_id,
              name: parent_name,
              slug: parent_slug,
              locale: parent_locale,
              children: [
                {
                  id: category_id,
                  name: category_name,
                  slug: category_slug,
                  locale: category_locale,
                },
              ],
            });
          } else {
            const parentEntry = categoryMap.get(parent_id);
            const childExists = parentEntry.children.some(
              (child) => child.id === category_id,
            );
            if (!childExists) {
              parentEntry.children.push({
                id: category_id,
                name: category_name,
                slug: category_slug,
                locale: category_locale,
              });
            }
          }
        } else if (!categoryMap.has(category_id)) {
          categoryMap.set(category_id, {
            id: category_id,
            name: category_name,
            slug: category_slug,
            locale: category_locale,
            children: [],
          });
        }
      });


      await queryRunner.commitTransaction(); // ✅ Commit transaction
      return Array.from(categoryMap.values());
    }
    catch (error) {
      console.log('error', error);
      await queryRunner.rollbackTransaction();
      throw error;
    }
    finally {
      console.log('release start:');
      await queryRunner.release();
      console.log('release end:');
    }
  }

  public async getProductAttributeLocalize(
    attributesId: number[],
    languageId: number,
  ): Promise<Record<number, string>> {
    // Initialize an empty object to store the non-null locale names
    const localizedNamesMap: Record<number, string> = {};

    if (!attributesId || !attributesId.length) return localizedNamesMap;

    attributesId = attributesId.filter(
      (item) => item !== null && item !== undefined,
    );

    if (!attributesId || !attributesId.length) return localizedNamesMap;

    // ✅ Create QueryRunner for Transaction
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const query = `
      SELECT locale->>'${languageId}' as locale_name, id
      FROM attribute_value
      WHERE id IN (${attributesId.join(', ')})
      AND locale->>'${languageId}' IS NOT NULL
    `;

      // Execute the raw query
      const result = await this.manager.query(query);

      // Iterate through the result rows and add them to the map
      result.forEach((row) => {
        localizedNamesMap[row.id] = row.locale_name;
      });


      await queryRunner.commitTransaction();
      return localizedNamesMap;
    } catch (error) {
      // 🔴 Rollback transaction on error
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      // ✅ Always release QueryRunner to prevent idle connections
      await queryRunner.release();
    }

  }

  public async findByName(name: string): Promise<Product | null> {
    return this.findOne({
      where: { name: name },
      order: {
        id: 'DESC',
      },
    });
  }

  async searchByProduct(
    name: string,
    countryCode: string,
    languageId?: number,
  ): Promise<Product[]> {
    console.log('languageId', languageId);

    // Create QueryRunner for transaction
    const queryRunner: QueryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      languageId = languageId || 1;

      let query = '';
      if (name == 'ALL') {
        query = ` SELECT p.id, COALESCE(localization.name, p.name) as name, ig.image_url as image
        FROM product p
        LEFT JOIN image_gallery ig on ig.id = p.featured_image_id 
        LEFT JOIN product_meta pm ON pm.product_id = p.id 
        LEFT JOIN country c on c.id = pm.country_id
        LEFT JOIN product_localization localization ON (localization.product_id = p.id AND localization.language_id = ${languageId}) 
        where c.code = '${countryCode}'
        and p.deleted_at is null and p.is_active is true and pm.deleted_at is null and pm.is_active is true
            Limit 10`;
      } else {
        query = `
        SELECT p.id, COALESCE(localization.name, p.name) as name, ig.image_url as image
        FROM product p
        LEFT JOIN image_gallery ig on ig.id = p.featured_image_id 
        LEFT JOIN product_meta pm ON pm.product_id = p.id 
        LEFT JOIN country c on c.id = pm.country_id
        LEFT JOIN product_localization localization ON (localization.product_id = p.id AND localization.language_id = ${languageId}) 
        where c.code ='${countryCode}' AND (p.name ILIKE '%${name}%' OR localization.name ILIKE '%${name}%')
        and p.deleted_at is null and p.is_active is true and pm.deleted_at is null and pm.is_active is true
        LIMIT 10`;
      }
      return await queryRunner.manager.query(query);
    }
    catch (error) {
      console.log('error', error);
      await queryRunner.rollbackTransaction();
      throw error;
    }
    finally {
      console.log('release start');
      await queryRunner.release();
      console.log('release end');
    }
  }

  async copyProduct(originalProduct: Product): Promise<Product | null> {
    const time = Date.now();
    // Step 2: Clone the product
    const clonedProduct = this.create({
      ...originalProduct,
      id: undefined, // Clear the ID for a new entity
      sku: `${originalProduct.sku}`, // Unique SKU
      slug: `${originalProduct.slug}-${time}`, // Unique slug
      localizations: undefined, // Handle separately
      suggestions: undefined, // Handle separately
      suggestedFor: undefined,
      productMeta: undefined, // Handle separately
      productMetaList: undefined,
      featuredImage: originalProduct.featuredImage, // Retain existing relations
      hoverImage: originalProduct.hoverImage,
      sizeChartImg: originalProduct.sizeChartImg,
      collections: undefined,
      productCollectionPivots: undefined, // Handle separately
      isPublish: false,
    });

    const savedProduct = await this.save(clonedProduct);

    // Clone localizations
    const clonedLocalizations = originalProduct.localizations.map(
      (localization) =>
        this.productLocalizationRepository.create({
          ...localization,
          id: undefined,
          product: savedProduct,
        }),
    );
    await this.productLocalizationRepository.save(clonedLocalizations);

    // Clone product meta
    if (originalProduct.productMetaList) {
      const productMetaEntList = originalProduct.productMetaList.map(
        (originalProductMeta) => {
          if (originalProductMeta?.variants) {
            originalProductMeta.variants = originalProductMeta.variants?.map(
              (item) => {
                item.variantDetails = item.variantDetails.map((v) => ({
                  ...v,
                  sku: `${v.sku}`,
                }));
                return item;
              },
            );
          }

          return {
            ...originalProductMeta,
            id: undefined,
            product: savedProduct,
          };
        },
      );

      const clonedMeta = this.productMetaRepo.create(productMetaEntList);

      await this.productMetaRepo.save(clonedMeta);
    }

    return savedProduct;
  }

  public async add(createProductDto: ProductDto): Promise<Product | null> {
    // await this.queryRunner.connect();
    // await this.queryRunner.startTransaction();
    let savedProduct: Product = new Product();
    try {
      const { categoryId, ...productData } = createProductDto;

      const categories = await this.categoryRepository.findBy({
        id: In(categoryId),
      });

      await this.manager.transaction(async (transactionalEntityManager) => {
        createProductDto.slug = createSlug(createProductDto?.name);
        const exist = await this.getProductBySlug(createProductDto.slug);
        if (exist) {
          // if(exist.slug.includes('-0')){
          //   const lastDotIndex = exist.slug.lastIndexOf('-0');
          //   const slugCount = parseInt(exist.slug.substring(lastDotIndex + 1)) + 1;
          createProductDto.slug =
            createProductDto.slug + '-' + generateProductSlug();
          // }else{
          //   createProductDto.slug = createProductDto.slug + '-001';
          // }
        }
        const newProduct = this.create({ ...productData, categories });
        newProduct.imageGalleryIds =
          createProductDto.imageGalleryIds.toString();

        // const product = await this.queryRunner.manager.save(newProduct);
        savedProduct = await transactionalEntityManager.save(newProduct);

        if (savedProduct) {
          const productMetaEntList: ProductMeta[] = [];

          createProductDto.countryIds.forEach((id: number) => {
            const productMeta: ProductMeta = new ProductMeta();
            productMeta.createdBy = createProductDto.createdBy;
            productMeta.productId = savedProduct.id;
            productMeta.countryId = id;
            if (!createProductDto.isMultiVariantProduct) {
              createProductDto.productPrice.forEach(
                (price: ProductPriceDto) => {
                  if (id == price.countryId) {
                    productMeta.quantity = price.quantity;
                    productMeta.unitPrice = price.unitPrice;
                    productMeta.discountPrice = price.discountPrice;
                  }
                },
              );
            }
            productMetaEntList.push(productMeta);
          });

          const newProductMeta =
            this.productMetaRepo.create(productMetaEntList);
          await transactionalEntityManager.save(newProductMeta);
          // const productMeta = await this.queryRunner.manager.save(newProductMeta);
        }
      });

      // await this.queryRunner.commitTransaction();
    } catch (err) {
      console.log('error!!! failed to save product', err);

      // since we have errors lets rollback the changes we made
      // await this.queryRunner.rollbackTransaction();
      return null;
    } finally {
      console.log('finally saved product');
      // you need to release a queryRunner which was manually instantiated
      // await this.queryRunner.release();
    }
    return savedProduct;
  }
  public async updateProduct(
    updateProductDto: UpdateProductDto,
  ): Promise<Product | null> {
    // await this.queryRunner.connect();
    // await this.queryRunner.startTransaction();
    let savedProduct: Product = new Product();
    try {
      const { categoryId, ...productData } = updateProductDto;
      const categories = await this.categoryRepository.findBy({
        id: In(categoryId),
      });

      await this.manager.transaction(async (transactionalEntityManager) => {
        const newProduct = this.create({ ...productData, categories, updatedBy: updateProductDto.updatedBy });
        newProduct.imageGalleryIds =
          updateProductDto.imageGalleryIds.toString();
        // const product = await this.queryRunner.manager.save(newProduct);
        savedProduct = await transactionalEntityManager.save(newProduct);

        if (savedProduct && !savedProduct.isMultiVariantProduct) {
          // updateProductDto.productPrice.forEach(
          // async (price: UpdateProductPriceDto) => {
          // await this.productMetaRepo.softDelete({ id: price.id });
          const newProductMetaList = await this.productMetaRepo.findBy({
            productId: updateProductDto.id,
          });
          const notMatchingCountry = newProductMetaList.filter(
            (value) => !updateProductDto.countryIds.includes(value.countryId),
          );

          if (notMatchingCountry && notMatchingCountry?.length > 0) {
            await transactionalEntityManager.softRemove(notMatchingCountry);
          }

          // },
          // );
          const productMetaEntList: ProductMeta[] = [];

          updateProductDto.countryIds.forEach((id: number) => {
            const index = newProductMetaList.findIndex(
              (item: ProductMeta) =>
                item.countryId === id && item.productId === updateProductDto.id,
            );
            const productMetaObj: ProductMeta = new ProductMeta();
            productMetaObj.updatedBy = updateProductDto.updatedBy;
            if (index === -1) {
              productMetaObj.productId = savedProduct.id;
              productMetaObj.countryId = id;
              if (!updateProductDto.isMultiVariantProduct) {
                updateProductDto.productPrice.forEach(
                  (price: ProductPriceDto) => {
                    if (id == price.countryId) {
                      productMetaObj.quantity = price.quantity;
                      productMetaObj.unitPrice = price.unitPrice;
                      productMetaObj.discountPrice = price.discountPrice;
                    }
                  },
                );
              }

              productMetaEntList.push(productMetaObj);
            } else {
              updateProductDto.productPrice.forEach(
                (price: ProductPriceDto) => {
                  if (id == price.countryId) {
                    newProductMetaList[index].quantity = price.quantity;
                    newProductMetaList[index].unitPrice = price.unitPrice;
                    newProductMetaList[index].discountPrice =
                      price.discountPrice;
                  }
                },
              );

              productMetaEntList.push(newProductMetaList[index]);
            }
          });

          // updateProductDto.productPrice.forEach(
          //   (price: UpdateProductPriceDto) => {
          //     let productMeta: ProductMeta = new ProductMeta();
          //     productMeta.productId = savedProduct.id;
          //     productMeta.countryId = price.countryId;
          //     if (!updateProductDto.isMultiVariantProduct) {
          //       productMeta.quantity = price.quantity;
          //       productMeta.unitPrice = price.unitPrice;
          //       productMeta.discountPrice = price.discountPrice;
          //     }
          //     productMetaEntList.push(productMeta);
          //   },
          // );

          const newProductMeta =
            this.productMetaRepo.create(productMetaEntList);
          await transactionalEntityManager.save(newProductMeta);
          // const productMeta = await this.queryRunner.manager.save(newProductMeta);
        }
      });

      // await this.queryRunner.commitTransaction();
    } catch (err) {
      console.log('error!!! failed to update product');

      // since we have errors lets rollback the changes we made
      // await this.queryRunner.rollbackTransaction();
      return null;
    } finally {
      console.log('finally product updated');
      // you need to release a queryRunner which was manually instantiated
      // await this.queryRunner.release();
    }
    return savedProduct;
  }

  public async addVariant(
    createProductVariantDto: CreateProductVariantDto,
  ): Promise<CreateProductVariantDto | null> {
    try {
      await this.manager.transaction(async (transactionalEntityManager) => {
        const productObj: Product = await this.findById(
          createProductVariantDto.productId,
        );
        productObj.attributeImages = createProductVariantDto.attributeImages;
        productObj.selectedAttributes =
          createProductVariantDto.selectedAttributes;
        productObj.isMultiVariantProduct = true;

        const savedProduct = await transactionalEntityManager.save(productObj);
        const productMetaEntList: ProductMeta[] = [];

        for (const countryWiseProductVariantDto of createProductVariantDto.countryWiseProductVariant) {
          const existProductMetaObj =
            await this.productMetaRepo.findByProductIdCountryId(
              createProductVariantDto.productId,
              countryWiseProductVariantDto.countryId,
            );
          if (existProductMetaObj) {
            existProductMetaObj.variants =
              countryWiseProductVariantDto.productVariations;
            existProductMetaObj.unitPrice =
              countryWiseProductVariantDto.productVariations[0]
                ?.variantDetails[0]?.unitPrice;

            existProductMetaObj.updatedBy = createProductVariantDto.createdBy;
            productMetaEntList.push(existProductMetaObj);
          } else {
            const newProductMetaObj: ProductMeta = new ProductMeta();

            newProductMetaObj.productId = createProductVariantDto.productId;
            newProductMetaObj.countryId =
              countryWiseProductVariantDto.countryId;
            newProductMetaObj.variants =
              countryWiseProductVariantDto.productVariations;

            newProductMetaObj.unitPrice =
              countryWiseProductVariantDto.productVariations[0]
                ?.variantDetails[0]?.unitPrice;

            newProductMetaObj.createdBy = createProductVariantDto.createdBy;

            const newProductMeta =
              this.productMetaRepo.create(newProductMetaObj);

            await transactionalEntityManager.save(newProductMeta);
          }
        }

        if (productMetaEntList.length > 0) {
          await transactionalEntityManager.save(productMetaEntList);
        }

        // remove
        const ids = createProductVariantDto.countryWiseProductVariant.map(
          (obj) => obj.countryId,
        );
        const removeIds = await this.productMetaRepo.getCountryIdsByProductId(
          createProductVariantDto.productId,
          ids,
        );
        if (removeIds && removeIds?.length > 0) {
          await this.productMetaRepo.softDelete(removeIds);
        }
        // remove
      });
    } catch (err) {
      console.log('error!!! failed to update product');
      return null;
    } finally {
      console.log('finally product updated');
    }
    return createProductVariantDto;
  }

  // public async updateOne(
  //   id: number,
  //   updateProductDto: UpdateProductDto,
  // ): Promise<number> {
  //   const updatedResult = await this.update(id, updateProductDto);
  //   return updatedResult.affected;
  // }

  public async destroy(id: number): Promise<number> {
    const result = await this.softDelete(id);
    return result.affected;
  }

  public async restoreData(id: number): Promise<number> {
    const result = await this.restore(id);
    return result.affected;
  }

  public async activeOrInactive(id: number, status: boolean): Promise<number> {
    const result = await this.update(id, { isActive: status });
    return result.affected;
  }

  public async getProductByProductCountry(
    productId: number,
  ): Promise<CountryWiseProductVariantDto> {
    let productWiseVariantDto: CountryWiseProductVariantDto;
    try {
      const productMeta: ProductMeta[] = await this.productMetaRepo.findBy({
        productId: productId,
      });
      // let colorIds = this.productVariantRepo.findBy({productVariantId:productMeta.})
    } catch (err) {
      // since we have errors lets rollback the changes we made
    }
    return productWiseVariantDto;
  }

  public async getProductsByParams(
    params: any,
    pageOptionsDto: PageOptionsDto,
  ): Promise<PageDto<Product>> {
    const conditions: any = {};

    if (params.slug) {
      conditions.slug = params.slug;
    }

    if (params.name) {
      conditions.name = Like(`%${params.name}%`);
    }

    if (params.productId) {
      conditions.id = params.productId;
    }

    const [list, itemCount] = await this.findAndCount({
      where: conditions,
      skip: pageOptionsDto.skip,
      take: pageOptionsDto.take,
      withDeleted: false,
    });

    const pageMetaDto = new PageMetaDto({ itemCount, pageOptionsDto });

    return new PageDto(list, pageMetaDto);
    // return await this.findAndCount({ where: conditions });
  }

  public async productSearchWithFilters(
    params: any,
    countryId: number,
    pageOptionsDto: PageOptionsDto,
    categoryId?: number,
    languageId?: number,
  ): Promise<PageDto<any>> {
    let query = '';
    let countQuery = '';
    let data = [];
    let count = [];
    let brandId = null;
    let sizes = [];
    let colors = [];
    let orderByQuery = '';

    // Enhanced parameter validation and parsing
    if (params?.brandId) {
      brandId = Number(params.brandId);
      if (isNaN(brandId)) {
        throw new Error('Invalid brandId parameter');
      }
    }

    languageId = languageId || 1;

    if (!categoryId) {
      categoryId = null;
    }

    // Enhanced size parameter parsing with validation
    if (params?.size) {
      try {
        sizes = params.size.split(',')
          .map((item) => Number(item.trim()))
          .filter((id) => !isNaN(id) && id > 0);

        if (sizes.length === 0) {
          console.warn('No valid size IDs found in parameter:', params.size);
        }
      } catch (error) {
        console.error('Error parsing size parameter:', error);
        sizes = [];
      }
    }

    // Enhanced color parameter parsing with validation
    if (params?.color) {
      try {
        colors = params.color.split(',')
          .map((item) => Number(item.trim()))
          .filter((id) => !isNaN(id) && id > 0);

        if (colors.length === 0) {
          console.warn('No valid color IDs found in parameter:', params.color);
        }
      } catch (error) {
        console.error('Error parsing color parameter:', error);
        colors = [];
      }
    }

    // Enhanced ordering with better default handling
    if (params?.orderBy == ProductSortingEnum.Low_To_High) {
      orderByQuery = `ORDER BY pm.unit_price ASC, p.sort_order ASC, p.id DESC`;
    } else if (params?.orderBy == ProductSortingEnum.High_To_Low) {
      orderByQuery = `ORDER BY pm.unit_price DESC, p.sort_order ASC, p.id DESC`;
    } else {
      orderByQuery = `ORDER BY p.sort_order ASC, p.id DESC`;
    }
    const mainQuery = `
      WITH main_products AS (
      SELECT DISTINCT p.id, p.name, pm.variants
      FROM product p
      INNER JOIN product_meta pm ON pm.product_id = p.id
      LEFT JOIN brand b ON b.id = p.brand_id
      `;
    const mainQueryWhere = `
      AND p.is_publish = true 
      AND pm.deleted_at IS NULL 
      AND p.deleted_at IS NULL 
      AND p.is_active = true 
      AND pm.is_active = true
      `;
    const tagProduct = `
      tagged_products AS (
      SELECT DISTINCT ptm2.product_id
      FROM product_tags_meta ptm1
      INNER JOIN product_tags_meta ptm2 ON ptm1.product_tag_id = ptm2.product_tag_id
      WHERE ptm1.product_id IN (SELECT id FROM main_products) AND ptm2.deleted_at IS NULL
      AND ptm2.is_active = true
    )`;
    const selectQuery = `
      SELECT distinct p.id, COALESCE(localization.name, p.name) as name, p.is_multi_variant_product, p.slug, p.hover_image_id, p.featured_image_id, pm.quantity, pm.discount_price as discountPrice ,pm.unit_price as unitPrice,
      pm.variants, 
      p.limited_edition, p.just_arrived, p.most_popular, p.customer_favorite, p.best_value, p.special_offer 
      ,igf.id as featured_image_id, igf.name as featured_image_name, igf.image_url as featured_imageurl, igf.original_image_url as featured_original_imageurl, igf.image_gallery_urls as featured_image_gallery_urls
      ,igh.id as hover_image_id, igh.name as hover_image_name, igh.image_url as hover_imageurl, igh.original_image_url as hover_original_imageurl, igh.image_gallery_urls as hover_image_gallery_urls,p.sort_order

      FROM product p`;

    const selectQueryWhere = `
      JOIN image_gallery igf ON igf.id = p.featured_image_id 
      JOIN image_gallery igh ON igh.id = p.hover_image_id 
      LEFT JOIN product_localization localization ON (localization.product_id = p.id AND localization.language_id = ${languageId}) 
      WHERE p.id IN (SELECT id FROM main_products)
        OR p.id IN (SELECT product_id FROM tagged_products)
      `;

    const selectCount = ` SELECT count(distinct p.id) as total FROM product p`;

    // Create QueryRunner for transaction
    const queryRunner: QueryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {

      if (params?.size && params?.color) {
        query = `${mainQuery}
        WHERE  pm.country_id = $3
        AND (b.id = $4 OR $4 IS NULL)
  
          AND EXISTS (
              SELECT 1
              FROM product_category pc
              WHERE pc.product_id = p.id
              AND (pc.category_id = $5 OR pc.category_id IN (
                  SELECT c2.id
                  FROM category c2
                  WHERE c2.parent_id = $5
              ))
          )
  
          ${mainQueryWhere}
          AND (
              -- Products must have BOTH matching size AND color (more precise filtering)
              p.id IN (
                  SELECT product_id
                  FROM (
                      SELECT product_id,
                             (jsonb_array_elements(variants)->'size'->>'id')::int AS size_id
                      FROM product_meta
                      WHERE is_active = true AND country_id = $3
                  ) size_filter
                  WHERE size_id = ANY ($1::int[])
              )
              AND p.id IN (
                  SELECT product_id
                  FROM (
                      SELECT DISTINCT product_id,
                             (jsonb_array_elements(details)->'color'->>'id')::int AS color_id
                      FROM (
                          SELECT DISTINCT product_id,
                                 jsonb_array_elements(variants)->'variantDetails' AS details
                          FROM product_meta
                          WHERE is_active = true AND country_id = $3
                      ) AS sub
                  ) color_filter
                  WHERE color_id = ANY ($2::int[])
              )
          )
        ),
        ${tagProduct}
        ${selectQuery}
        INNER JOIN product_meta pm ON (pm.product_id = p.id AND country_id = $3)
        ${selectQueryWhere} ${orderByQuery}
        Limit $6 offset $7`;

        countQuery = `${mainQuery}
        WHERE  pm.country_id = $3
        AND (b.id = $4 OR $4 IS NULL)
        
        AND EXISTS (
          SELECT 1
          FROM product_category pc
          WHERE pc.product_id = p.id
          AND (pc.category_id = $5 OR pc.category_id IN (
              SELECT c2.id
              FROM category c2
              WHERE c2.parent_id = $5
          ))
        )
          ${mainQueryWhere}
          AND (
              -- Products must have BOTH matching size AND color (count query)
              p.id IN (
                  SELECT product_id
                  FROM (
                      SELECT product_id,
                             (jsonb_array_elements(variants)->'size'->>'id')::int AS size_id
                      FROM product_meta
                      WHERE is_active = true AND country_id = $3
                  ) size_filter
                  WHERE size_id = ANY ($1::int[])
              )
              AND p.id IN (
                  SELECT product_id
                  FROM (
                      SELECT DISTINCT product_id,
                             (jsonb_array_elements(details)->'color'->>'id')::int AS color_id
                      FROM (
                          SELECT DISTINCT product_id,
                                 jsonb_array_elements(variants)->'variantDetails' AS details
                          FROM product_meta
                          WHERE is_active = true AND country_id = $3
                      ) AS sub
                  ) color_filter
                  WHERE color_id = ANY ($2::int[])
              )
          )
        ),
        ${tagProduct}
        ${selectCount}
        INNER JOIN product_meta pm ON (pm.product_id = p.id AND country_id = $3)
        ${selectQueryWhere}
         `;

        data = await queryRunner.manager.query(query, [
          sizes,
          colors,
          countryId,
          brandId,
          categoryId,
          pageOptionsDto.take,
          pageOptionsDto.skip,
        ]);

        count = await queryRunner.manager.query(countQuery, [
          sizes,
          colors,
          countryId,
          brandId,
          categoryId,
        ]);
      }
      else if (params?.size) {
        query = `${mainQuery}
        WHERE  pm.country_id = $2
        AND (b.id = $3 OR $3 IS NULL)
  
        AND EXISTS (
              SELECT 1
              FROM product_category pc
              WHERE pc.product_id = p.id
              AND (pc.category_id = $4 OR pc.category_id IN (
                  SELECT c2.id
                  FROM category c2
                  WHERE c2.parent_id = $4
              ))
          )
  
          ${mainQueryWhere}
          AND (
              p.id IN (
                  SELECT distinct product_id
                  FROM (
                      SELECT product_id, (jsonb_array_elements(variants)->'size'->>'id')::int AS size_id
                      FROM product_meta 
                      WHERE is_active = true
                  ) AS sub
                  WHERE size_id = ANY ($1::int[])
              )
          )
        ),
        ${tagProduct}
        ${selectQuery}
        INNER JOIN product_meta pm ON (pm.product_id = p.id AND country_id = $2)
        ${selectQueryWhere} ${orderByQuery}
        Limit $5 offset $6`;

        countQuery = `${mainQuery}
        WHERE  pm.country_id = $2
        AND (b.id = $3 OR $3 IS NULL)
  
        AND EXISTS (
              SELECT 1
              FROM product_category pc
              WHERE pc.product_id = p.id
              AND (pc.category_id = $4 OR pc.category_id IN (
                  SELECT c2.id
                  FROM category c2
                  WHERE c2.parent_id = $4
              ))
          )
  
          ${mainQueryWhere}
          AND (
              p.id IN (
                  SELECT product_id
                  FROM (
                      SELECT product_id, (jsonb_array_elements(variants)->'size'->>'id')::int AS size_id
                      FROM product_meta 
                      WHERE is_active = true
                  ) AS sub
                  WHERE size_id = ANY ($1::int[])
              )
          )
        ),
        ${tagProduct}
        ${selectCount}
        INNER JOIN product_meta pm ON (pm.product_id = p.id AND country_id = $2)
        ${selectQueryWhere}
         `;

        data = await queryRunner.manager.query(query, [
          sizes,
          countryId,
          brandId,
          categoryId,
          pageOptionsDto.take,
          pageOptionsDto.skip,
        ]);

        count = await queryRunner.manager.query(countQuery, [
          sizes,
          countryId,
          brandId,
          categoryId,
        ]);
      }
      else if (params?.color) {
        query = `${mainQuery}
        WHERE  pm.country_id = $2
        AND (b.id = $3 OR $3 IS NULL)
  
        AND EXISTS (
              SELECT 1
              FROM product_category pc
              WHERE pc.product_id = p.id
              AND (pc.category_id = $4 OR pc.category_id IN (
                  SELECT c2.id
                  FROM category c2
                  WHERE c2.parent_id = $4
              ))
          )
  
          ${mainQueryWhere}
          AND (
              p.id IN (
               SELECT distinct product_id
              FROM (SELECT product_id, (jsonb_array_elements(details)->'color'->>'id')::int AS color_id
                FROM (
                    SELECT DISTINCT product_id, jsonb_array_elements(variants)->'variantDetails' AS details
                    FROM  product_meta  where is_active is true
                ) AS sub) as q WHERE color_id = ANY ($1::int[]) GROUP BY q.product_id
              )
          )
        ),
        ${tagProduct}
        ${selectQuery}
        INNER JOIN product_meta pm ON (pm.product_id = p.id AND country_id = $2)
        ${selectQueryWhere} ${orderByQuery}
        Limit $5 offset $6`;

        countQuery = `${mainQuery}
        WHERE  pm.country_id = $2
        AND (b.id = $3 OR $3 IS NULL)
  
        AND EXISTS (
              SELECT 1
              FROM product_category pc
              WHERE pc.product_id = p.id
              AND (pc.category_id = $4 OR pc.category_id IN (
                  SELECT c2.id
                  FROM category c2
                  WHERE c2.parent_id = $4
              ))
          )
        
          ${mainQueryWhere}
          AND (
              p.id IN (
               SELECT product_id
              FROM (SELECT product_id, (jsonb_array_elements(details)->'color'->>'id')::int AS color_id
                FROM (
                    SELECT DISTINCT product_id, jsonb_array_elements(variants)->'variantDetails' AS details
                    FROM  product_meta  where is_active is true
                ) AS sub) as q WHERE color_id = ANY ($1::int[]) GROUP BY q.product_id
              )
          )
        ),
        ${tagProduct}
        ${selectCount}
        INNER JOIN product_meta pm ON (pm.product_id = p.id AND country_id = $2)
        ${selectQueryWhere}
         `;

        data = await queryRunner.manager.query(query, [
          colors,
          countryId,
          brandId,
          categoryId,
          pageOptionsDto.take,
          pageOptionsDto.skip,
        ]);

        count = await queryRunner.manager.query(countQuery, [
          colors,
          countryId,
          brandId,
          categoryId,
        ]);
      }
      else if (brandId) {
        query = `${mainQuery}
        WHERE  pm.country_id = $1
        AND b.id = $2
  
        AND EXISTS (
              SELECT 1
              FROM product_category pc
              WHERE pc.product_id = p.id
              AND (pc.category_id = $3 OR pc.category_id IN (
                  SELECT c2.id
                  FROM category c2
                  WHERE c2.parent_id = $3
              ))
          )
  
          ${mainQueryWhere}
        ),
        ${tagProduct}
        ${selectQuery}
        INNER JOIN product_meta pm ON (pm.product_id = p.id AND country_id = $1)
        ${selectQueryWhere} ${orderByQuery}
        Limit $4 offset $5`;

        countQuery = `${mainQuery}
        WHERE  pm.country_id = $1
        AND b.id = $2
  
        AND EXISTS (
              SELECT 1
              FROM product_category pc
              WHERE pc.product_id = p.id
              AND (pc.category_id = $3 OR pc.category_id IN (
                  SELECT c2.id
                  FROM category c2
                  WHERE c2.parent_id = $3
              ))
          )
  
  
          ${mainQueryWhere}
        ),
        ${tagProduct}
        ${selectCount}
        INNER JOIN product_meta pm ON (pm.product_id = p.id AND country_id = $1)
        ${selectQueryWhere}
         `;

        data = await this.manager.query(query, [
          countryId,
          brandId,
          categoryId,
          pageOptionsDto.take,
          pageOptionsDto.skip,
        ]);

        count = await this.manager.query(countQuery, [
          countryId,
          brandId,
          categoryId,
        ]);
      }
      else if (params?.orderBy) {
        query = `${mainQuery}
        WHERE  pm.country_id = $1
  
          AND EXISTS (
              SELECT 1
              FROM product_category pc
              WHERE pc.product_id = p.id
              AND (pc.category_id = $2 OR pc.category_id IN (
                  SELECT c2.id
                  FROM category c2
                  WHERE c2.parent_id = $2
              ))
          )
  
          ${mainQueryWhere}
        ),
        ${tagProduct}
        ${selectQuery}
        INNER JOIN product_meta pm ON (pm.product_id = p.id AND country_id = $1)
        ${selectQueryWhere} ${orderByQuery}
        Limit $3 offset $4`;

        countQuery = `${mainQuery}
        WHERE  pm.country_id = $1
          AND EXISTS (
              SELECT 1
              FROM product_category pc
              WHERE pc.product_id = p.id
              AND (pc.category_id = $2 OR pc.category_id IN (
                  SELECT c2.id
                  FROM category c2
                  WHERE c2.parent_id = $2
              ))
          )
  
          ${mainQueryWhere}
        ),
        ${tagProduct}
        ${selectCount}
        INNER JOIN product_meta pm ON (pm.product_id = p.id AND country_id = $1)
        ${selectQueryWhere}
         `;

        data = await queryRunner.manager.query(query, [
          countryId,
          categoryId,
          pageOptionsDto.take,
          pageOptionsDto.skip,
        ]);

        count = await queryRunner.manager.query(countQuery, [countryId, categoryId]);
      }

      // Commit transaction
      await queryRunner.commitTransaction();
    }
    catch (error) {
      console.log('error', error);
      await queryRunner.rollbackTransaction();
      throw error;
    }
    finally {
      console.log('release start');
      await queryRunner.release();
      console.log('release end');
    }

    const itemCount: number = parseInt(count[0].total);
    const pageMetaDto = new PageMetaDto({ itemCount, pageOptionsDto });

    return new PageDto(data, pageMetaDto);

  }

  /**
   * @deprecated
   */
  public async productSearchWithFilters1(
    params: any,
    countryId: number,
    pageOptionsDto: PageOptionsDto,
    categoryId?: number,
  ): Promise<PageDto<any>> {
    let query = '';
    let countQuery = '';
    let data = [];
    let count = [];
    let brandId = null;
    let sizes = [];
    let colors = [];
    let orderBy = '';
    if (params?.brandId) {
      brandId = params?.brandId;
    }

    if (!categoryId) {
      categoryId = null;
    }

    if (params?.size) {
      sizes = params?.size.split(',').map((item) => Number(item.trim()));
    }

    if (params?.color) {
      colors = params?.color.split(',').map((item) => Number(item.trim()));
    }

    // if(params?.orderBy== ProductSortingEnum.Best_Sellers){
    //   orderBy = `, `;
    // }else if(params?.orderBy== ProductSortingEnum.New_Arrivals){
    //   orderBy = orderBy + ProductSortingEnum.New_Arrivals;
    // }else
    if (params?.orderBy == ProductSortingEnum.Low_To_High) {
      orderBy = ` pm.unit_price ASC`;
    } else if (params?.orderBy == ProductSortingEnum.High_To_Low) {
      orderBy = ` pm.unit_price DESC`;
    } else {
      orderBy = ` p.id`;
    }
    console.log('order', orderBy);

    const selectQuery = `
    SELECT distinct p.id, p.name, p.is_multi_variant_product, p.slug, p.category_id, c.slug as cat_slug, p.hover_image_id, p.featured_image_id, pm.quantity, pm.discount_price as discountPrice ,pm.unit_price as unitPrice,
    pm.variants, 
    (select name from category c2 where c2.id = c.parent_id) as cat_parent_name,
    p.limited_edition, p.just_arrived, p.most_popular, p.customer_favorite, p.best_value, p.special_offer 
    ,igf.id as featured_image_id, igf.name as featured_image_name, igf.image_url as featured_imageurl, igf.original_image_url as featured_original_imageurl, igf.image_gallery_urls as featured_image_gallery_urls
	,igh.id as hover_image_id, igh.name as hover_image_name, igh.image_url as hover_imageurl, igh.original_image_url as hover_original_imageurl, igh.image_gallery_urls as hover_image_gallery_urls,p.sort_order

    FROM product p
    INNER JOIN product_meta pm ON pm.product_id = p.id
    LEFT JOIN  category c ON c.id = p.category_id 
    join image_gallery igf on igf.id = p.featured_image_id 
    join image_gallery igh on igh.id = p.hover_image_id
    LEFT JOIN brand b ON b.id = p.brand_id`;

    const selectCount = `
    SELECT count(p.id) as total
    FROM product p
    INNER JOIN product_meta pm ON pm.product_id = p.id
    LEFT JOIN  category c ON c.id = p.category_id 
    join image_gallery igf on igf.id = p.featured_image_id 
    join image_gallery igh on igh.id = p.hover_image_id
    LEFT JOIN brand b ON b.id = p.brand_id`;

    if (params?.size && params?.color) {
      query = `${selectQuery}
        INNER JOIN (
    SELECT product_id, size_id, color_id
    FROM (
        SELECT product_id, (jsonb_array_elements(variants)->'size'->>'id' )::int AS size_id, NULL AS color_id
        FROM  product_meta  where is_active is true
        UNION
        SELECT product_id, NULL AS size_id, (jsonb_array_elements(details)->'color'->>'id')::int AS color_id
        FROM (
            SELECT DISTINCT product_id, jsonb_array_elements(variants)->'variantDetails' AS details
            FROM  product_meta  where is_active is true
        ) AS sub
    ) AS combined_data
    WHERE size_id = ANY ($1::int[]) OR color_id = ANY ($2::int[])
    
    ) AS filtered_products ON filtered_products.product_id = p.id
        WHERE  pm.country_id = $3
        AND (b.id = $4 OR $4 IS NULL)
        AND (c.id = $5 OR (c.id IN (SELECT c2.id FROM category c2 WHERE c2.parent_id = $5)))
         AND p.is_publish is true AND pm.deleted_at is null AND p.deleted_at is null AND p.is_active is true AND pm.is_active is true 
        order by p.sort_order, ${orderBy} 
        Limit $6 offset $7`;

      countQuery = `${selectCount}
        INNER JOIN (
    SELECT product_id, size_id, color_id
    FROM (
        SELECT product_id, (jsonb_array_elements(variants)->'size'->>'id') ::int AS size_id, NULL AS color_id
        FROM  product_meta  where is_active is true
        UNION
        SELECT product_id, NULL AS size_id, (jsonb_array_elements(details)->'color'->>'id' ) ::int AS color_id
        FROM (
            SELECT DISTINCT product_id, jsonb_array_elements(variants)->'variantDetails' AS details
            FROM  product_meta  where is_active is true
        ) AS sub
    ) AS combined_data
    WHERE size_id = ANY ($1::int[]) OR color_id = ANY ($2::int[])
    ) AS filtered_products ON filtered_products.product_id = p.id
        WHERE  pm.country_id = $3
        AND (b.id = $4 OR $4 IS NULL)
        AND (c.id = $5 OR (c.id IN (SELECT c2.id FROM category c2 WHERE c2.parent_id = $5)))
         AND p.is_publish is true AND pm.deleted_at is null AND p.deleted_at is null AND p.is_active is true AND pm.is_active is true  `;

      data = await this.query(query, [
        sizes,
        colors,
        countryId,
        brandId,
        categoryId,
        pageOptionsDto.take,
        pageOptionsDto.skip,
      ]);

      count = await this.query(countQuery, [
        sizes,
        colors,
        countryId,
        brandId,
        categoryId,
      ]);
    } else if (params?.size) {
      query = `${selectQuery}
      INNER JOIN (
      SELECT DISTINCT product_id
      FROM (
          SELECT product_id, (jsonb_array_elements(variants)->'size'->>'id')::int  AS size_id
          FROM product_meta where is_active is true
      ) AS sub
      WHERE size_id = ANY ($1::int[])
  ) AS filtered_products ON filtered_products.product_id = p.id
      WHERE  pm.country_id = $2
      AND (b.id = $3 OR $3 IS NULL)
        AND (c.id = $4 OR (c.id IN (SELECT c2.id FROM category c2 WHERE c2.parent_id = $4)))
       AND p.is_publish is true AND pm.deleted_at is null AND p.deleted_at is null AND p.is_active is true AND pm.is_active is true  
      order by p.sort_order, ${orderBy}
      Limit $5 offset $6`;

      countQuery = `${selectCount}
      INNER JOIN (
      SELECT DISTINCT product_id
      FROM (
          SELECT product_id, (jsonb_array_elements(variants)->'size'->>'id')::int AS size_id
          FROM product_meta  where is_active is true
      ) AS sub
      WHERE size_id = ANY ($1::int[])
  ) AS filtered_products ON filtered_products.product_id = p.id
      WHERE  pm.country_id = $2
      AND (b.id = $3 OR $3 IS NULL)
        AND (c.id = $4 OR (c.id IN (SELECT c2.id FROM category c2 WHERE c2.parent_id = $4)))
       AND p.is_publish is true AND pm.deleted_at is null AND p.deleted_at is null AND p.is_active is true AND pm.is_active is true  `;
      console.log('query size', query);

      data = await this.query(query, [
        sizes,
        countryId,
        brandId,
        categoryId,
        pageOptionsDto.take,
        pageOptionsDto.skip,
      ]);

      count = await this.query(countQuery, [
        sizes,
        countryId,
        brandId,
        categoryId,
      ]);
    } else if (params?.color) {
      query = `${selectQuery}
      INNER JOIN (
      
  SELECT q.product_id
  FROM (SELECT product_id, (jsonb_array_elements(details)->'color'->>'id')::int AS color_id
  FROM (
      SELECT DISTINCT product_id, jsonb_array_elements(variants)->'variantDetails' AS details
      FROM  product_meta  where is_active is true
  ) AS sub) as q WHERE color_id = ANY ($1::int[]) GROUP BY q.product_id
  ) AS filtered_products ON filtered_products.product_id = p.id
      WHERE  pm.country_id = $2
      AND (b.id = $3 OR $3 IS NULL)
        AND (c.id = $4 OR (c.id IN (SELECT c2.id FROM category c2 WHERE c2.parent_id = $4)))
       AND p.is_publish is true AND pm.deleted_at is null AND p.deleted_at is null AND p.is_active is true AND pm.is_active is true  
      order by p.sort_order, ${orderBy}
      Limit $5 offset $6`;

      countQuery = `${selectCount}
      INNER JOIN (
      
  SELECT q.product_id
  FROM (SELECT product_id, (jsonb_array_elements(details)->'color'->>'id')::int AS color_id
  FROM (
      SELECT DISTINCT product_id, jsonb_array_elements(variants)->'variantDetails' AS details
      FROM  product_meta  where is_active is true
  ) AS sub) as q WHERE color_id = ANY ($1::int[]) GROUP BY q.product_id
  ) AS filtered_products ON filtered_products.product_id = p.id
      WHERE  pm.country_id = $2
      AND (b.id = $3 OR $3 IS NULL)
        AND (c.id = $4 OR (c.id IN (SELECT c2.id FROM category c2 WHERE c2.parent_id = $4)))
       AND p.is_publish is true AND pm.deleted_at is null AND p.deleted_at is null AND p.is_active is true AND pm.is_active is true  `;

      data = await this.manager.query(query, [
        colors,
        countryId,
        brandId,
        categoryId,
        pageOptionsDto.take,
        pageOptionsDto.skip,
      ]);

      count = await this.manager.query(countQuery, [
        colors,
        countryId,
        brandId,
        categoryId,
      ]);
    } else if (brandId) {
      query = `${selectQuery}
      WHERE  pm.country_id = $1
        AND (b.id = $2 OR $2 IS NULL)
        AND (c.id = $3 OR (c.id IN (SELECT c2.id FROM category c2 WHERE c2.parent_id = $3)))
       AND p.is_publish is true AND pm.deleted_at is null AND p.deleted_at is null AND p.is_active is true AND pm.is_active is true
      order by p.sort_order, ${orderBy}
      Limit $4 offset $5`;

      countQuery = `${selectCount}
      WHERE  pm.country_id = $1
      AND (b.id = $2 OR $2 IS NULL)
        AND (c.id = $3 OR (c.id IN (SELECT c2.id FROM category c2 WHERE c2.parent_id = $3)))
       AND p.is_publish is true AND pm.deleted_at is null AND p.deleted_at is null AND p.is_active is true AND pm.is_active is true  `;

      data = await this.manager.query(query, [
        countryId,
        brandId,
        categoryId,
        pageOptionsDto.take,
        pageOptionsDto.skip,
      ]);

      count = await this.manager.query(countQuery, [
        countryId,
        brandId,
        categoryId,
      ]);
    } else if (params?.orderBy) {
      query = `${selectQuery}
      WHERE  pm.country_id = $1
        AND (c.id = $2 OR (c.id IN (SELECT c2.id FROM category c2 WHERE c2.parent_id = $2)))
       AND p.is_publish is true AND pm.deleted_at is null AND p.deleted_at is null AND p.is_active is true AND pm.is_active is true
      order by p.sort_order, ${orderBy}
      Limit $3 offset $4`;

      countQuery = `${selectCount}
      WHERE  pm.country_id = $1
        AND (c.id = $2 OR (c.id IN (SELECT c2.id FROM category c2 WHERE c2.parent_id = $2)))
       AND p.is_publish is true AND pm.deleted_at is null AND p.deleted_at is null AND p.is_active is true AND pm.is_active is true  `;

      data = await this.manager.query(query, [
        countryId,
        categoryId,
        pageOptionsDto.take,
        pageOptionsDto.skip,
      ]);

      count = await this.manager.query(countQuery, [countryId, categoryId]);
    }
    console.log('query', query);

    const itemCount: number = parseInt(count[0].total);
    const pageMetaDto = new PageMetaDto({ itemCount, pageOptionsDto });

    return new PageDto(data, pageMetaDto);
  }
  public async productSearchWithBrandFilters(
    params: any,
    countryId: number,
    pageOptionsDto: PageOptionsDto,
    brandSlug: string,
  ): Promise<PageDto<any>> {
    let query = '';
    let countQuery = '';
    let data = [];
    let count = [];
    let sizes = [];
    let colors = [];

    const languageId = params?.languageId || 1;

    if (params?.size) {
      sizes = params?.size.split(',').map((item) => Number(item.trim()));
    }

    if (params?.color) {
      colors = params?.color.split(',').map((item) => Number(item.trim()));
    }

    const selectQuery = `
    SELECT distinct p.id, COALESCE(localization.name, p.name) AS name, p.is_multi_variant_product, p.slug, p.category_id, p.hover_image_id, p.featured_image_id, pm.quantity, pm.discount_price as discountPrice ,pm.unit_price as unitPrice,
    pm.variants 
    
    ,igf.id as featured_image_id, igf.name as featured_image_name, igf.image_url as featured_imageurl, igf.original_image_url as featured_original_imageurl, igf.image_gallery_urls as featured_image_gallery_urls
	,igh.id as hover_image_id, igh.name as hover_image_name, igh.image_url as hover_imageurl, igh.original_image_url as hover_original_imageurl, igh.image_gallery_urls as hover_image_gallery_urls,p.sort_order

    FROM product p
    INNER JOIN product_meta pm ON pm.product_id = p.id
    LEFT JOIN  category c ON c.id = p.category_id 
    join image_gallery igf on igf.id = p.featured_image_id 
    join image_gallery igh on igh.id = p.hover_image_id
    LEFT JOIN product_localization localization ON (localization.product_id = p.id AND localization.language_id = ${languageId}) 
    LEFT JOIN brand b ON b.id = p.brand_id`;

    const selectCount = `
    SELECT count(p.id) as total
    FROM product p
    INNER JOIN product_meta pm ON pm.product_id = p.id
    LEFT JOIN  category c ON c.id = p.category_id 
    join image_gallery igf on igf.id = p.featured_image_id 
    join image_gallery igh on igh.id = p.hover_image_id
    LEFT JOIN brand b ON b.id = p.brand_id`;

    if (params?.size && params?.color) {
      query = `${selectQuery}
        INNER JOIN (
    SELECT product_id, size_id, color_id
    FROM (
        SELECT product_id, (jsonb_array_elements(variants)->'size'->>'id' )::int AS size_id, NULL AS color_id
        FROM  product_meta  where is_active is true
        UNION
        SELECT product_id, NULL AS size_id, (jsonb_array_elements(details)->'color'->>'id')::int AS color_id
        FROM (
            SELECT DISTINCT product_id, jsonb_array_elements(variants)->'variantDetails' AS details
            FROM  product_meta  where is_active is true
        ) AS sub
    ) AS combined_data
    WHERE size_id = ANY ($1::int[]) OR color_id = ANY ($2::int[])
    
    ) AS filtered_products ON filtered_products.product_id = p.id
        WHERE  pm.country_id = $3
        AND b.slug = $4 
         AND p.is_publish is true AND pm.deleted_at is null AND p.deleted_at is null AND p.is_active is true AND pm.is_active is true 
        order by p.sort_order, p.id 
        Limit $5 offset $6`;

      countQuery = `${selectCount}
        INNER JOIN (
    SELECT product_id, size_id, color_id
    FROM (
        SELECT product_id, (jsonb_array_elements(variants)->'size'->>'id') ::int AS size_id, NULL AS color_id
        FROM  product_meta  where is_active is true
        UNION
        SELECT product_id, NULL AS size_id, (jsonb_array_elements(details)->'color'->>'id' ) ::int AS color_id
        FROM (
            SELECT DISTINCT product_id, jsonb_array_elements(variants)->'variantDetails' AS details
            FROM  product_meta  where is_active is true
        ) AS sub
    ) AS combined_data
    WHERE size_id = ANY ($1::int[]) OR color_id = ANY ($2::int[])
    ) AS filtered_products ON filtered_products.product_id = p.id
        WHERE  pm.country_id = $3
        AND b.slug = $4 
         AND p.is_publish is true AND pm.deleted_at is null AND p.deleted_at is null AND p.is_active is true AND pm.is_active is true  `;

      data = await this.query(query, [
        sizes,
        colors,
        countryId,
        brandSlug,
        pageOptionsDto.take,
        pageOptionsDto.skip,
      ]);

      count = await this.query(countQuery, [
        sizes,
        colors,
        countryId,
        brandSlug,
      ]);
    } else if (params?.size) {
      query = `${selectQuery}
      INNER JOIN (
      SELECT DISTINCT product_id
      FROM (
          SELECT product_id, (jsonb_array_elements(variants)->'size'->>'id')::int  AS size_id
          FROM product_meta where is_active is true
      ) AS sub
      WHERE size_id = ANY ($1::int[])
  ) AS filtered_products ON filtered_products.product_id = p.id
      WHERE  pm.country_id = $2
      AND b.slug = $3 
       AND p.is_publish is true AND pm.deleted_at is null AND p.deleted_at is null AND p.is_active is true AND pm.is_active is true  
      order by p.sort_order, p.id 
      Limit $4 offset $5`;

      countQuery = `${selectCount}
      INNER JOIN (
      SELECT DISTINCT product_id
      FROM (
          SELECT product_id, (jsonb_array_elements(variants)->'size'->>'id')::int AS size_id
          FROM product_meta  where is_active is true
      ) AS sub
      WHERE size_id = ANY ($1::int[])
  ) AS filtered_products ON filtered_products.product_id = p.id
      WHERE  pm.country_id = $2
      AND b.slug = $3
       AND p.is_publish is true AND pm.deleted_at is null AND p.deleted_at is null AND p.is_active is true AND pm.is_active is true  `;

      data = await this.query(query, [
        sizes,
        countryId,
        brandSlug,
        pageOptionsDto.take,
        pageOptionsDto.skip,
      ]);

      count = await this.query(countQuery, [sizes, countryId, brandSlug]);
    } else if (params?.color) {
      query = `${selectQuery}
      INNER JOIN (
      
  SELECT q.product_id
  FROM (SELECT product_id, (jsonb_array_elements(details)->'color'->>'id')::int AS color_id
  FROM (
      SELECT DISTINCT product_id, jsonb_array_elements(variants)->'variantDetails' AS details
      FROM  product_meta  where is_active is true
  ) AS sub) as q WHERE color_id = ANY ($1::int[]) GROUP BY q.product_id
  ) AS filtered_products ON filtered_products.product_id = p.id
      WHERE  pm.country_id = $2
      AND b.slug = $3 
       AND p.is_publish is true AND pm.deleted_at is null AND p.deleted_at is null AND p.is_active is true AND pm.is_active is true  
      order by p.sort_order, p.id 
      Limit $4 offset $5`;

      countQuery = `${selectCount}
      INNER JOIN (
      
  SELECT q.product_id
  FROM (SELECT product_id, (jsonb_array_elements(details)->'color'->>'id')::int AS color_id
  FROM (
      SELECT DISTINCT product_id, jsonb_array_elements(variants)->'variantDetails' AS details
      FROM  product_meta  where is_active is true
  ) AS sub) as q WHERE color_id = ANY ($1::int[]) GROUP BY q.product_id
  ) AS filtered_products ON filtered_products.product_id = p.id
      WHERE  pm.country_id = $2
      AND b.slug = $3
       AND p.is_publish is true AND pm.deleted_at is null AND p.deleted_at is null AND p.is_active is true AND pm.is_active is true  `;

      data = await this.manager.query(query, [
        colors,
        countryId,
        brandSlug,
        pageOptionsDto.take,
        pageOptionsDto.skip,
      ]);

      count = await this.manager.query(countQuery, [
        colors,
        countryId,
        brandSlug,
      ]);
    }

    const itemCount: number = parseInt(count[0].total);
    const pageMetaDto = new PageMetaDto({ itemCount, pageOptionsDto });

    return new PageDto(data, pageMetaDto);
  }

  // public async afterSellStockManage(
  //   productOrderDto: ProductOrderDto,
  // ): Promise<ProductMeta[] | null> {
  //   const country = await this.countryRepo.findById(productOrderDto?.countryId);
  //   for (let item of productOrderDto.orderDetails) {
  //     const productMeta =
  //       await this.productMetaRepo.findListByProductIdCountryCode(
  //         item.productId,
  //         country.id,
  //         country?.code,
  //       );

  //     for (let metaObj of productMeta) {
  //       if (metaObj.product.isMultiVariantProduct) {
  //         for (let variants of metaObj?.variants) {
  //           if (variants?.size?.id == item?.size?.id) {
  //             if (item?.color) {
  //               for (let variantObj of variants?.variantDetails) {
  //                 if (
  //                   variantObj?.color &&
  //                   variantObj?.color?.id == item?.color?.id
  //                 ) {
  //                   const qty =
  //                     parseInt(variantObj?.quantity.toString()) -
  //                     parseInt(item?.quantity.toString());
  //                   variantObj.quantity = qty;
  //                 }
  //               }
  //             } else if (variants?.variantDetails?.length == 1) {
  //               const qty =
  //                 parseInt(variants?.variantDetails[0]?.quantity.toString()) -
  //                 parseInt(item?.quantity.toString());
  //               variants.variantDetails[0].quantity = qty;
  //             }
  //           }
  //         }
  //       } else {
  //         metaObj.quantity = metaObj.quantity - item?.quantity;
  //       }
  //     }
  //     return this.productMetaRepo.save(productMeta);
  //   }
  // }
  // public async stockManageBySku(
  //   productOrderDto: ProductOrderDto,
  // ): Promise<ProductMeta[] | null> {
  //   const country = await this.countryRepo.findById(productOrderDto?.countryId);
  //   for (let item of productOrderDto.orderDetails) {
  //     const productMeta =
  //       await this.productMetaRepo.findListByProductIdCountryCode(
  //         item.productId,
  //         country.id,
  //         country?.code,
  //       );

  //     for (let metaObj of productMeta) {
  //         for (let variants of metaObj?.variants) {
  //             // if (item?.color) {
  //               for (let variantObj of variants?.variantDetails) {
  //                 if ( variantObj?.sku == 'SKU') {
  //                   const qty =
  //                     parseInt(variantObj?.quantity.toString()) -
  //                     parseInt(item?.quantity.toString());
  //                   variantObj.quantity = qty;
  //                 }
  //               }
  //             // } else if (variants?.variantDetails?.length == 1) {
  //             //   const qty =
  //             //     parseInt(variants?.variantDetails[0]?.quantity.toString()) -
  //             //     parseInt(item?.quantity.toString());
  //             //   variants.variantDetails[0].quantity = qty;
  //             // }
  //         }
  //     }
  //     return this.productMetaRepo.save(productMeta);
  //   }
  // }
  public async stockManageFromInventoryBySku(
    productStockManageDto: ProductStockManageDto,
  ): Promise<boolean | null> {
    const country = await this.countryRepo.getCountryByName(InventoryEnum.BD);
    let countryQuery = '';
    if (InventoryEnum.BD == productStockManageDto?.warehouse.toUpperCase()) {
      countryQuery = ` = ${country?.id}`;
    } else {
      countryQuery = ` not in (${country?.id})`;
    }

    for (const item of productStockManageDto.items) {
      const query = `
       SELECT q.id, q.sku, q.variants
        FROM (
            SELECT id, variants,
                  (jsonb_array_elements(details)->>'sku') AS sku
            FROM (
              SELECT id, variants, country_id,jsonb_array_elements(variants)->'variantDetails' AS details
              FROM product_meta
              WHERE is_active = true and country_id ${countryQuery}
            ) AS sub
        ) AS q
        WHERE q.sku = '${item?.sku}'
        GROUP BY q.id, q.sku, q.variants
      `;
      const variantDetailsList = await this.manager.query(query);
      if (variantDetailsList && variantDetailsList?.length > 0) {
        for (const metaObj of variantDetailsList) {
          for (const variants of metaObj?.variants) {
            for (const variantObj of variants?.variantDetails) {
              if (variantObj?.sku == item?.sku) {
                if (
                  productStockManageDto?.operation ==
                  StockManagementTypeEnum.INVENTORY
                ) {
                  variantObj.quantity = item?.quantity;
                  // parseInt(variantObj?.quantity.toString()) +
                  // parseInt(item?.quantity.toString());
                } else {
                  const qty =
                    parseInt(variantObj?.quantity?.toString()) -
                    parseInt(item?.quantity.toString());
                  variantObj.quantity = qty > 0 ? qty : 0;
                }
              }
            }
            await this.productMetaRepo.productVariantUpdate(
              metaObj?.id,
              metaObj?.variants,
            );
          }
        }
      }
    }
    return true;
  }

  public async excelDownloadProductStockReport(
    queryFilterDto: {
      exportTo?: string;
      startDate: Date;
      endDate: Date;
    },
  ): Promise<any> {
    // ✅ Create QueryRunner for Transaction
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const query = `
        SELECT 
            p.created_at,
            p.id AS product_id,
            p.name AS name,
            pm.country_id AS country,
            size_info->'size'->>'id' AS size_id,
            size_info->'size'->>'name' AS size_name,
            p.sku AS style_sku,
            variant_detail->>'sku' AS sku,
            p.is_publish AS is_publish,
            (variant_detail->'color')->>'name' AS color_name,
            variant_detail->>'quantity' AS quantity,
            variant_detail->>'unitPrice' AS unit_price,
            variant_detail->>'discountPrice' AS discount_price
        FROM product p
        JOIN product_meta pm ON p.id = pm.product_id
        CROSS JOIN LATERAL jsonb_array_elements(COALESCE(pm.variants, '[]'::jsonb)) AS size_info
        CROSS JOIN LATERAL jsonb_array_elements(COALESCE(size_info->'variantDetails', '[]'::jsonb)) AS variant_detail
        WHERE p.deleted_at IS NULL AND p.created_at BETWEEN $1 AND $2;
    `;

      const queryParams: any[] = [];
      if (queryFilterDto.startDate && queryFilterDto.endDate) {
        // Convert to UTC before sending to API
        const startDate = new Date(queryFilterDto?.startDate)
        const endDate = new Date(queryFilterDto?.endDate)
        const startUTC = new Date(startDate.toISOString().split('T')[0] + 'T00:00:00.000Z');
        const endUTC = new Date(endDate.toISOString().split('T')[0] + 'T23:59:59.999Z');

        queryParams.push(startUTC, endUTC);
      }
      const [data] = await Promise.all([
        queryRunner.query(query, queryParams)
      ]);

      // return data;
      // ✅ Commit transaction after successful execution
      await queryRunner.commitTransaction();
      return data;
    } catch (error) {
      // 🔴 Rollback transaction on error
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      // ✅ Always release QueryRunner to prevent idle connections
      await queryRunner.release();
    }
  }
}
