import { Injectable } from '@nestjs/common';
import { PageOptionsDto } from 'src/common/pagination/page-options.dto';
import { ServiceResponse } from 'src/common/utils/service-response';
import { SubscriptionPopupRepository } from './subscription-popup.repository';
import { CreateSubscriptionPopupDto } from './dto/create-subscription-popup.dto';
import { UpdateSubscriptionPopupDto } from './dto/update-subscription-popup.dto';
import { POPUP_TYPE_ENUMS } from './enum/popup-type.enum';

@Injectable()
export class SubscriptionPopupService {
  constructor(
    private readonly repository: SubscriptionPopupRepository,
  ) {}

  async findAll(): Promise<ServiceResponse> {
    const result = await this.repository.findAll();
    if (!result) {
      return new ServiceResponse(result, 'Subscription Popup not found');
    }
    return new ServiceResponse(result, 'Subscription Popup found successfully');
  }
  async findAllDataByPagination(
    pageOptionsDto: PageOptionsDto,
  ): Promise<ServiceResponse> {
    const result =
      await this.repository.findAllDataByPagination(pageOptionsDto);
    if (!result) {
      return new ServiceResponse(result, 'Subscription Popup not found');
    }
    return new ServiceResponse(
      result.itemList,
      'All Subscription Popup found successfully',
      result.meta,
    );
  }
  

  async create(
    // selectedImage: Express.Multer.File | any,
    createSubscriptionPopupDto: CreateSubscriptionPopupDto,
  ): Promise<ServiceResponse> {
    const exist = await this.repository.findByTypeCountryId(createSubscriptionPopupDto.countryId, createSubscriptionPopupDto?.type);
    if(exist){
      return new ServiceResponse(
        null,
        `Already added one popup information for this country`,
      );
    }
    const result = await this.repository.add(createSubscriptionPopupDto);
    if (!result) {
      return new ServiceResponse(
        result,
        'Failed to saved Subscription Popup! Please try again',
      );
    }
    return new ServiceResponse(result, 'Subscription Popup saved successfully');
  }

  async findById(id: number): Promise<ServiceResponse> {
    if(!id){
      return new ServiceResponse(
        null,
        `Please provide valid subscription popup id! Try again`,
      );
    }
    const result = await this.repository.findById(id);
    if (!result) {
      return new ServiceResponse(
        result,
        `Subscription Popup not found for id:${id}! Please try again`,
      );
    }
    return new ServiceResponse(result, `Subscription Popup data found for id:${id}`);
  }

  async findByCountryId(countryId: number, type: POPUP_TYPE_ENUMS): Promise<ServiceResponse> {
    if(!countryId){
      return new ServiceResponse(
        null,
        `Please provide valid subscription popup id! Try again`,
      );
    }
    const result = await this.repository.findByTypeCountryId(countryId, type);
    if (!result) {
      return new ServiceResponse(
        result,
        `Subscription Popup not found for id:${countryId}! Please try again`,
      );
    }
    return new ServiceResponse(result, `Subscription Popup data found for id:${countryId}`);
  }

  async update(
    id: number,
    // selectedImage: Express.Multer.File | null,
    subscriptionPopup: UpdateSubscriptionPopupDto,
  ): Promise<ServiceResponse> {
    
    if(!id){
      return new ServiceResponse(
        null,
        `Please provide valid subscription popup id! Try again`,
      );
    }
    const exist = await this.repository.findById(id);
    if (!exist) {
      return new ServiceResponse(null, 'subscription popup not found! please try again');
    }

    const result = await this.repository.updateOne(id, subscriptionPopup);
    if (!result) {
      return new ServiceResponse(
        result,
        'Failed to update Subscription Popup! Please try again',
      );
    }
    return new ServiceResponse(result, 'Subscription Popup successfully updated');
  }

  async delete(id: number): Promise<ServiceResponse> {
    if(!id){
      return new ServiceResponse(
        null,
        `Please provide valid subscription popup id! Try again`,
      );
    }
    const result = await this.repository.destroy(id);
    if (!result) {
      return new ServiceResponse(
        result,
        `Failed to delete Subscription Popup id: ${id}! Please try again`,
      );
    }
    return new ServiceResponse(id, `Subscription Popup id: ${id} successfully deleted`);
  }

  async restoreData(id: number): Promise<ServiceResponse> {
    if(!id){
      return new ServiceResponse(
        null,
        `Please provide valid subscription popup id! Try again`,
      );
    }
    const result = await this.repository.restoreData(id);
    if (!result) {
      return new ServiceResponse(
        result,
        `Failed to restore Subscription Popup id: ${id}! Please try again`,
      );
    }
    return new ServiceResponse(result, `Subscription Popup id: ${id} successfully restored`);
  }

  async activeOrInactive(
    id: number,
    status: boolean,
  ): Promise<ServiceResponse> {
    if(!id){
      return new ServiceResponse(
        null,
        `Please provide valid subscription popup id! Try again`,
      );
    }
    const result = await this.repository.activeOrInactive(id, status);
    if (!result) {
      return new ServiceResponse(
        result,
        'Failed to restore Subscription Popup! Please try again',
      );
    }
    return new ServiceResponse(result, 'Subscription Popup successfully changed status');
  }
}
