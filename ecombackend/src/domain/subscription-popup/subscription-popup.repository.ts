import { Injectable } from '@nestjs/common';
import { Repository, DataSource,IsNull, ILike } from 'typeorm';
import { PageOptionsDto } from 'src/common/pagination/page-options.dto';
import { PageDto } from 'src/common/pagination/page.dto';
import { PageMetaDto } from 'src/common/pagination/page-meta.dto';
import { SubscriptionPopup } from '../entities/subscription-popup.entity';
import { CreateSubscriptionPopupDto } from './dto/create-subscription-popup.dto';
import { UpdateSubscriptionPopupDto } from './dto/update-subscription-popup.dto';
import { POPUP_TYPE_ENUMS } from './enum/popup-type.enum';

@Injectable()
export class SubscriptionPopupRepository extends Repository<SubscriptionPopup> {
  constructor(dataSource: DataSource) {
    super(
      SubscriptionPopup,
      dataSource.createEntityManager(),
      dataSource.createQueryRunner(),
    );
  }

  public async findAll(): Promise<SubscriptionPopup[]> {
    return this.find();
  }


  public async findAllDataByPagination(
    pageOptionsDto: PageOptionsDto,
  ): Promise<PageDto<SubscriptionPopup>> {
    const [list, itemCount] = await this.findAndCount({
      skip: pageOptionsDto.skip,
      take: pageOptionsDto.take,
      withDeleted: false,
      order: {
        id: 'DESC',
      },
    });

    const pageMetaDto = new PageMetaDto({ itemCount, pageOptionsDto });

    return new PageDto(list, pageMetaDto);
  }

  public async findById(id: number): Promise<SubscriptionPopup | null> {
    return this.findOneBy({ id: id });
  }

  public async findByTypeCountryId(countryId: number, type: POPUP_TYPE_ENUMS): Promise<SubscriptionPopup | null> {
    return this.findOneBy({ 
      countryId: countryId,
      type,
      isActive: true,
      deletedAt: IsNull(),
     });
  }

  public async add(popup: CreateSubscriptionPopupDto): Promise<SubscriptionPopup | null> {
    const newSubscriptionPopup = this.create(popup);
    return this.save(newSubscriptionPopup);
  }

  public async updateOne(
    id: number,
    updateSubscriptionPopupDto: UpdateSubscriptionPopupDto,
  ): Promise<SubscriptionPopup> {
    const updatedResult = await this.update(id, updateSubscriptionPopupDto);
    let saveSubscriptionPopup = null;
    if (updatedResult.affected > 0) {
      saveSubscriptionPopup = await this.findOneBy({ id: id });
    }
    return saveSubscriptionPopup;
  }

  public async destroy(id: number): Promise<boolean> {
    const result = await this.softDelete(id);
    return result.affected > 0 ? true : false;
  }

  public async restoreData(id: number): Promise<SubscriptionPopup | null> {
    const result = await this.restore(id);
    let subscriptionPopup = null;
    if (result.affected > 0) {
      subscriptionPopup = await this.findOneBy({ id: id });
    }
    return subscriptionPopup;
  }

  public async activeOrInactive(
    id: number,
    status: boolean,
  ): Promise<SubscriptionPopup | null> {
    const result = await this.update(id, { isActive: status });
    let subscriptionPopup = null;
    if (result.affected > 0) {
      subscriptionPopup = await this.findOneBy({ id: id });
    }
    return subscriptionPopup;
  }
}
