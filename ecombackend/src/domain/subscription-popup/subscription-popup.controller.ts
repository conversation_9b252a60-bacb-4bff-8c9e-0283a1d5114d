import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Put,
  Query,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { Post, UsePipes, ValidationPipe } from '@nestjs/common';
import { PageOptionsDto } from 'src/common/pagination/page-options.dto';
import { SubscriptionPopupService } from './subscription-popup.service';
import { CreateSubscriptionPopupDto } from './dto/create-subscription-popup.dto';
import { UpdateSubscriptionPopupDto } from './dto/update-subscription-popup.dto';
import { POPUP_TYPE_ENUMS } from './enum/popup-type.enum';
import { ApiTags } from '@nestjs/swagger';

@ApiTags('Subscription Popup')
@Controller('subscriptionPopup')
@UsePipes(new ValidationPipe())
export class SubscriptionPopupController {
  constructor(private readonly service: SubscriptionPopupService) {}

  @Get()
  public async findAll() {
    return this.service.findAll();
  }

  @Get('findAllDataByPagination')
  public async findAllDataByPagination(
    @Query('type') type: POPUP_TYPE_ENUMS,
    @Query() pageOptionsDto: PageOptionsDto,
  ) {
    return this.service.findAllDataByPagination(pageOptionsDto);
  }
  

  @Get('/:id')
  public async getById(@Param('id') id: number) {
    return this.service.findById(id);
  }

  @Get('web/country/:countryId')
  public async getByCountryId(
    @Param('countryId') countryId: number,
    @Query('type') type: POPUP_TYPE_ENUMS,
  ) {
    return this.service.findByCountryId(countryId, type);
  }

  @Post()
  public async create(
    @Body() createSubscriptionPopupDto: CreateSubscriptionPopupDto,
  ) {
    return this.service.create(createSubscriptionPopupDto);
  }

  @Put(':id')
  public async update(
    @Param('id') id: number,
    @Body() updateSubscriptionPopupDto: UpdateSubscriptionPopupDto,
  ) {
    return this.service.update(id, updateSubscriptionPopupDto);
  }

  @Delete('delete/:id')
  public async delete(@Param('id') id: number) {
    return this.service.delete(id);
  }

  @Post('restore/:id')
  public async restoreData(@Param('id') id: number) {
    return this.service.restoreData(id);
  }

  @Put('activeOrInactive/:id')
  public async activeOrInactive(
    @Param('id') id: number,
    @Query('status') status: boolean,
  ) {
    return this.service.activeOrInactive(id, status);
  }
}
