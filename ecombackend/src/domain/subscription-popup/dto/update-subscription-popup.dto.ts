import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Length, IsString, IsOptional, IsNotEmpty } from 'class-validator';
import { POPUP_TYPE_ENUMS } from '../enum/popup-type.enum';

export class UpdateSubscriptionPopupDto {
  @IsOptional()
  id: number;

  @ApiPropertyOptional()
  title: string;

  @ApiProperty()
  @IsString()
  details?: string;

  @ApiPropertyOptional()
  @IsOptional()
  imageGalleryId?: number;

  @ApiProperty()
  countryId?: number;

  @ApiProperty()
  type: POPUP_TYPE_ENUMS
}
