import { Length, IsString, IsNotEmpty, IsOptional } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { POPUP_TYPE_ENUMS } from '../enum/popup-type.enum';

export class CreateSubscriptionPopupDto {
  @ApiPropertyOptional()
  @IsString()
  title: string;

  @ApiProperty()
  @IsString()
  details?: string;

  @ApiPropertyOptional()
  @IsOptional()
  imageGalleryId?: number;

  @ApiProperty()
  countryId?: number;

  @ApiProperty()
  type: POPUP_TYPE_ENUMS
}
