import { Injectable } from '@nestjs/common';
import { PageOptionsDto } from 'src/common/pagination/page-options.dto';
import { ServiceResponse } from 'src/common/utils/service-response';
import { ProductOrderRepository } from './product-order.repository';
import { ProductOrderDto } from './productOrderDto/product-order.dto';
import { ProductOrder } from '../entities/product-order.entity';
import { InvoiceService } from 'src/common/services/invoice.service';
import { MailService } from 'src/common/services/mail.service';
import { PaymentStatusEnum } from 'src/common/enums/payment-status.enum';
import { DeliveryStatusEnum } from 'src/common/enums/delivery-status.enum';
import {
  ViewRefundListDto,
  ViewRefundProductDto,
} from './productOrderDto/view-refund-list.dto';
import { ImageGalleryRepository } from '../image/image-gallery.repository';
import { PaymentMethodEnum } from 'src/common/enums/payment-method.enum';
import { CustomerPurchaseHistoryDto } from './dto/customer-purchase-history.dto';
import { HttpService } from '@nestjs/axios';
import { lastValueFrom } from 'rxjs';
import { ProductService } from '../product/product.service';
import {
  ProductStockManageDto,
  ProductStockManageItemsDto,
} from '../product/dto/product-stock-manage.dto';
import { StockManagementTypeEnum } from 'src/common/enums/stock-management-type.enum';
import { getLanguageCodeById } from '../country/utils';
import { Response } from 'express';
import { PdfService } from 'src/node-pdf/pdf.service';
import { EntityManager } from 'typeorm';
import { QueryFilterDto } from './dto/query-filter.dto';
import { ExternalEnventoryService } from 'src/common/services/external/manage-inventory.service';
import { getCourierNameByCode } from 'src/common/utils/common-functions';
import { Courier } from '../entities/courier.entity';
import { Cart } from '../cart/entities/cart.entity';
import { DiscountUsage } from '../entities/discount-usage.entity';
import { AffiliateIntegrationService } from '../affiliate/services/affiliate-integration.service';
import { AffiliateOrderIntegrationService } from '../affiliate/services/affiliate-order-integration.service';

@Injectable()
export class ProductOrderService {
  constructor(
    private readonly repository: ProductOrderRepository,
    private readonly productService: ProductService,
    private readonly invoiceService: InvoiceService,
    private readonly imageGalleryRepo: ImageGalleryRepository,
    private readonly mailService: MailService,
    protected readonly httpService: HttpService,
    protected readonly pdfService: PdfService,
    protected readonly externalEnventoryService: ExternalEnventoryService,
    private readonly affiliateIntegrationService: AffiliateIntegrationService,
    private readonly affiliateOrderIntegrationService: AffiliateOrderIntegrationService,
  ) { }

  /**
 * Partially cancel products in an order and create a credit note
 */
  async partialCancelWithCreditNote(orderId: number, productOrderDetails: Array<{ id: number, creditQty: number }>) {
    // Find the order and its details
    const order: any = await this.repository.findById(orderId);
    if (!order || order?.creditedOrderId) {
      return new ServiceResponse(null, 'Order not found');
    }
    if (!Array.isArray(productOrderDetails) || productOrderDetails.length === 0) {
      return new ServiceResponse(null, 'No details provided for partial cancel');
    }
    // Validate each detail exists and is not already fully credited
    const detailsMap = new Map(order.productOrderDetails.map(d => [d.id, d]));
    for (const req of productOrderDetails) {
      const orig: any = detailsMap.get(req.id);
      if (!orig) {
        return new ServiceResponse(null, `Order detail ${req.id} not found in this order`);
      }
      const alreadyCredited = orig.creditQty || 0;
      const availableQty = orig.quantity - alreadyCredited;
      if (req.creditQty <= 0 || req.creditQty > availableQty) {
        return new ServiceResponse(null, `Invalid credit quantity for detail ${req.id}`);
      }
    }
    // Delegate credit note creation to repository
    const savedCreditNote = await this.repository.createCreditNoteForPartialCancel(order, productOrderDetails);
    return new ServiceResponse({ updatedOrderId: orderId, creditNoteId: savedCreditNote.id }, 'Partial cancellation and credit note created');
  }

  async findAll(): Promise<ServiceResponse> {
    const result = await this.repository.findAll();
    if (!result) {
      return new ServiceResponse(result, 'Order not found');
    }
    return new ServiceResponse(result, 'All orders found successfully');
  }

  async findAllDataByPagination(
    params: {
      globalSearch?: string;
      deliveryStatus: DeliveryStatusEnum;
      countrySearch?: string;
      nameSearch?: string;
      phoneSearch?: string;
      paymentSearch?: string;
      startDate?: Date;
      endDate?: Date;
    },
    pageOptionsDto: PageOptionsDto,
  ): Promise<ServiceResponse> {
    const result = await this.repository.findAllDataByPagination(
      params,
      pageOptionsDto,
    );
    if (!result) {
      return new ServiceResponse(result, 'Order not found');
    }
    return new ServiceResponse(
      result.itemList,
      'All orders found successfully',
      result.meta,
    );
  }

  async findById(id: number): Promise<ServiceResponse> {
    if (!id) {
      return new ServiceResponse(
        null,
        `Please provide valid product id! Try again`,
      );
    }
    const result = await this.repository.findById(id);
    if (!result) {
      return new ServiceResponse(
        result,
        `Order not found for id:${id}! Please try again`,
      );
    }
    return new ServiceResponse(result, `Order data found for id:${id}`);
  }

  async delete(id: number): Promise<ServiceResponse> {
    if (!id) {
      return new ServiceResponse(
        null,
        `Please provide valid product id! Try again`,
      );
    }
    const result = await this.repository.destroy(id);
    if (!result) {
      return new ServiceResponse(
        result,
        `Failed to delete order id: ${id}! Please try again`,
      );
    }
    return new ServiceResponse(id, `Order id: ${id} successfully deleted`);
  }

  async deleteDraftInvoice(invoiceNo: string): Promise<boolean> {
    return await this.repository.deleteDraftInvoice(invoiceNo);
  }

  async restoreData(id: number): Promise<ServiceResponse> {
    if (!id) {
      return new ServiceResponse(
        null,
        `Please provide valid product id! Try again`,
      );
    }
    const result = await this.repository.restoreData(id);
    if (!result) {
      return new ServiceResponse(
        result,
        `Failed to restore order id: ${id}! Please try again`,
      );
    }
    return new ServiceResponse(result, `Order id: ${id} successfully restored`);
  }

  async activeOrInactive(
    id: number,
    status: boolean,
    body: { createdBy?: number },
  ): Promise<ServiceResponse> {
    if (!id) {
      return new ServiceResponse(
        null,
        `Please provide valid product id! Try again`,
      );
    }
    const result = await this.repository.activeOrInactive(id, status, body);
    if (!result) {
      return new ServiceResponse(
        result,
        'Failed to restore order! Please try again',
      );
    }
    return new ServiceResponse(result, 'Order successfully actived');
  }

  async getOrderDetailsByOrderIdOrInvoice(params: {
    orderId?: number;
    invoice?: string;
    userId?: number;
  }): Promise<ServiceResponse> {
    if (Object.keys(params).length == 0) {
      return new ServiceResponse(
        null,
        `Please provide valid parameter! please try again`,
      );
    }
    const result =
      await this.repository.getOrderDetailsByOrderIdOrInvoice(params);
    if (!result) {
      return new ServiceResponse(
        result,
        `Product order information not found with given parameter! please try again`,
      );
    }
    return new ServiceResponse(result, `Order information found`);
  }

  // order servic
  async createProductOrder(
    productOrderDto: ProductOrderDto,
    cart?: Cart,
    transactionalEM?: EntityManager,
  ): Promise<ServiceResponse> {
    const result = await this.repository.addProductOrder(
      productOrderDto,
      transactionalEM,
    );
    if (!result) {
      return new ServiceResponse(
        null,
        'Failed to saved order! please try again',
      );
    }

    if (productOrderDto.paymentMethod == PaymentMethodEnum.COD) {
      await this.confirmOrder(result.invoiceNo, transactionalEM);
    }

    try {
      await this.storeDiscountUsages(
        productOrderDto,
        result,
        cart,
        transactionalEM,
      );
    } catch (e) {
      console.log('failed to store discount usages', e);
    }

    // Process affiliate commission if applicable
    try {
      await this.processAffiliateCommission(result, cart, transactionalEM);
    } catch (e) {
      console.log('failed to process affiliate commission', e);
    }

    return new ServiceResponse(result, 'Product order saved successfully');
  }

  async storeDiscountUsages(
    productOrderDto: ProductOrderDto,
    order: ProductOrder,
    cart: Cart,
    transactionalEM?: EntityManager,
  ) {
    if (!cart.discount) return;

    const productDiscountsId =
      cart.cart?.map((cartItem) => cartItem.discountId)?.filter((id) => !!id) ||
      [];

    if (cart.orderDiscountId) {
      productDiscountsId.push(cart.orderDiscountId);
    }

    if (cart.shippingDiscountId) {
      productDiscountsId.push(cart.shippingDiscountId);
    }

    if (!productDiscountsId.length) return;

    const _discountIds = new Set(productDiscountsId.map((id) => Number(id)));
    const discountIds = [..._discountIds];

    const email = productOrderDto?.shippingAddress?.email;
    const phone = productOrderDto?.shippingAddress?.phone;

    const discountUsageRepo = transactionalEM.getRepository(DiscountUsage);

    const usageRecords = discountIds.map((discountId) =>
      discountUsageRepo.create({
        discountId,
        orderId: order.id,
        userEmail: email,
        userPhone: phone,
      }),
    );

    await discountUsageRepo.save(usageRecords);

    // Count all usages grouped by discountId
    const usageCounts = await discountUsageRepo
      .createQueryBuilder('usage')
      .select('usage.discountId', 'discountId')
      .addSelect('COUNT(*)', 'count')
      .where('usage.discountId IN (:...discountIds)', { discountIds })
      .groupBy('usage.discountId')
      .getRawMany();

    // Build a bulk UPDATE using CASE
    if (usageCounts.length > 0) {
      const cases = usageCounts
        .map(({ discountId, count }) => `WHEN id = ${discountId} THEN ${count}`)
        .join(' ');

      const ids = usageCounts.map(({ discountId }) => discountId).join(', ');

      const sql = `
        UPDATE discount
        SET used_count = CASE ${cases} END
        WHERE id IN (${ids});
      `;

      await transactionalEM.query(sql);
    }
  }

  /**
   * Process affiliate commission for order
   */
  private async processAffiliateCommission(
    order: ProductOrder,
    cart: Cart,
    transactionalEM?: EntityManager,
  ): Promise<void> {
    try {
      console.log(`Starting affiliate commission processing for order ${order.id}`);

      // Check if order has discount/coupon that could be affiliate-related
      if (!order.coupons?.coupon) {
        console.log(`Order ${order.id} has no coupon - skipping affiliate commission`);
        return;
      }

      console.log(`Order ${order.id} has coupon: ${order.coupons.coupon} - processing affiliate commission`);

      // Process affiliate commission if order is completed
      const commissionResult = await this.affiliateOrderIntegrationService.processOrderCompletion(
        order.id,
        transactionalEM
      );

      if (commissionResult.data) {
        console.log(`✅ Affiliate commission processed successfully for order ${order.id}:`, {
          commissionId: commissionResult.data.commission?.id,
          affiliateId: commissionResult.data.calculationResult?.affiliateId,
          commissionAmount: commissionResult.data.calculationResult?.commissionAmount,
          discountId: commissionResult.data.calculationResult?.discountId
        });
      } else {
        console.log(`❌ No affiliate commission created for order ${order.id}:`, commissionResult.msg);
      }
    } catch (error) {
      console.error(`❌ Failed to process affiliate commission for order ${order.id}:`, {
        error: error.message,
        stack: error.stack,
        orderData: {
          orderId: order.id,
          coupon: order.coupons?.coupon,
          amount: order.amount,
          userId: order.userId
        }
      });
      // Don't throw error to avoid breaking order creation
    }
  }

  /**
   * Confirm order
   */
  public async confirmOrder(
    invoiceId: string,
    transactionalEM?: EntityManager,
  ): Promise<ServiceResponse> {
    const entityManager = transactionalEM || this.repository.manager;
    const result = await entityManager.findOneBy(ProductOrder, {
      invoiceNo: invoiceId,
    });

    if (!result) {
      return new ServiceResponse(
        null,
        'Something went wrong. Please contact with support!',
      );
    }

    const shippingCountry = result?.shippingAddress?.country?.name || '';

    // Update draft value
    if (result.isDraft) {
      await entityManager.update(ProductOrder, result.id, { isDraft: false });
    }

    // const languageId = result.country.languageId;

    // const countryCode = getLanguageCodeById(languageId).toLocaleLowerCase();

    const { subject, senderEmailAddress } =
      this.mailService.getTranslatedEmailSubject(
        result.country.languageId,
        'orderPlacedSubject',
      );

    // let senderEmailAddress: string = `no-reply@${result.country.domain}` || process.env.SES_FROM_MAIL;
    // let senderEmailAddress: string = process.env.SES_FROM_MAIL;
    // switch (countryCode) {
    //   case 'en':
    //     subject = 'Your order has been placed';
    //     break;
    //   case 'bg':
    //     subject = 'Вашата поръчка е направена';
    //     break;
    //   case 'bd':
    //     subject = 'আপনার অর্ডারটি সফলভাবে সম্পন্ন হয়েছে';
    //     break;
    //   case 'cz':
    //     subject = 'Vaše objednávka byla zadána';
    //     break;
    //   case 'de':
    //     subject = 'Ihre Bestellung wurde aufgegeben';
    //     break;
    //   case 'at':
    //     subject = 'Ihre Bestellung wurde aufgegeben';
    //     break;
    //   case 'de-at':
    //     subject = 'Ihre Bestellung wurde aufgegeben';
    //     break;
    //   case 'gr':
    //     subject = 'Η παραγγελία σας έχει καταχωρηθεί';
    //     break;
    //   case 'el':
    //     subject = 'Η παραγγελία σας έχει καταχωρηθεί';
    //     break;
    //   case 'es':
    //     subject = 'Tu pedido ha sido realizado';
    //     break;
    //   case 'hr':
    //     subject = 'Vaša narudžba je poslana';
    //     break;
    //   case 'hu':
    //     subject = 'A rendelésed leadásra került';
    //     break;
    //   case 'it':
    //     subject = 'Il tuo ordine è stato effettuato';
    //     break;
    //   case 'lt':
    //     subject = 'Jūsų užsakymas pateiktas';
    //     break;
    //   case 'pl':
    //     subject = 'Twoje zamówienie zostało złożone';
    //     break;
    //   case 'pt':
    //     subject = 'Seu pedido foi realizado';
    //     break;
    //   case 'ro':
    //     subject = 'Comanda dvs. a fost plasată';
    //     break;
    //   case 'sk':
    //     subject = 'Vaša objednávka bola zadaná';
    //     break;
    //   case 'sl':
    //     subject = 'Vaše naročilo je bilo oddano';
    //     break;
    //   case 'si':
    //     subject = 'Vaše naročilo je bilo oddano';
    //     break;
    //   default:
    //     subject = 'Your order has been placed'; // Fallback to English if no match
    // }

    if (process.env.NODE_ENV === 'production') {
      const productStockManage = this.formateStockObj(result);
      this.productService.stockManageBySku(productStockManage);
      this.stockManageBySelling(result);
    }
    if (
      result?.shippingAddress?.email &&
      result?.invoiceNo &&
      process.env.NODE_ENV === 'production'
    ) {
      try {
        // const html = await this.invoiceService.generateInvoiceHtml(result);
        // console.log('html:::', html);

        // // Generate the PDF
        // const pdfBase64 = await this.pdfService.generatePdf(html);

        // const { invoice, locales } = await this.invoiceService.getInvoiceData(result);

        const invoiceData =
          await this.invoiceService.generateInvoiceData(result);
        const invoiceHtml = await this.mailService.renderEmailTemplate(
          'invoice-new',
          invoiceData,
        );

        // const invoiceHtmlPdf = await this.mailService.renderEmailTemplate('invoice-pdf', invoiceData);
        // // Generate the PDF
        // const pdfBuffer = await this.pdfService.generatePdf(invoiceHtmlPdf);

        const docPdfBuffer = await this.pdfService.generatePdfBuffer(
          null,
          invoiceData?.locales,
          invoiceData,
        );

        const attachmentArr = [
          // {
          //   filename: `${invoiceId}_order_confirmation.pdf`,
          //   content: docPdfBuffer,
          // },
          {
            filename: `invoice_${invoiceId}.pdf`,
            content: docPdfBuffer,
          },
        ];

        // sending to customer
        this.mailService.sendMail(
          result?.shippingAddress?.email,
          subject,
          invoiceHtml,
          senderEmailAddress,
          attachmentArr,
        );

        // sending to notification
        this.mailService.sendMail(
          process.env.MATRIX_NOTIFICATION_MAIL,
          `One Order has been placed - ${shippingCountry}`,
          `Dear Concern <br> <br>
          One order has been successfully placed with Order Number: #${result?.invoiceNo}.
          <br> <br>
          ${invoiceHtml}`,
          senderEmailAddress,
          attachmentArr,
        );
      } catch (error) {
        console.log(error);
      }
    }
    return new ServiceResponse(result, 'Order confirmed successfully');
  }

  private formateStockObj(productOrder: ProductOrder): ProductStockManageDto {
    const productStockManageDto: ProductStockManageDto =
      new ProductStockManageDto();
    productStockManageDto.warehouse = productOrder.country.code;
    productStockManageDto.operation = StockManagementTypeEnum.SELL;
    productStockManageDto.items = [];
    for (const product of productOrder.productOrderDetails) {
      const productStockManageItemsDto: ProductStockManageItemsDto =
        new ProductStockManageItemsDto();
      productStockManageItemsDto.quantity = product.quantity;
      productStockManageItemsDto.sku = product.sku;
      productStockManageItemsDto.code = product.sku;
      productStockManageDto.items.push(productStockManageItemsDto);
    }
    return productStockManageDto;
  }

  getTranslatedEmailSubject(obj) {
    const result = this.mailService.getTranslatedEmailSubject(
      obj?.languageId,
      obj?.subjectKey,
    );
    return result;
  }

  async getProductOrderListByCountryOrUser(
    params: any,
    pageOptionsDto: PageOptionsDto,
  ): Promise<ServiceResponse> {
    if (Object.keys(params).length == 0) {
      return new ServiceResponse(
        null,
        `Please provide valid parameter! please try again`,
      );
    }
    const result = await this.repository.getProductOrderListByCountryOrUser(
      params,
      pageOptionsDto,
    );
    if (!result) {
      return new ServiceResponse(
        result,
        `Product order not found for params:${params}! please try again`,
      );
    }
    return new ServiceResponse(
      result.itemList,
      'All product found successfully',
      result.meta,
    );
  }

  async findOrderDetailsByInvoice(invoiceNo: any): Promise<ProductOrder> {
    const result = await this.repository.findOrderDetailsByInvoice(invoiceNo);
    if (!result) {
      return null;
    }
    return result;
  }

  async findOrderUnpaidDetailsByUUID(uuid: string): Promise<ProductOrder> {
    const result = await this.repository.findOrderUnpaidDetailsByUUID(uuid);
    if (!result) {
      return null;
    }
    return result;
  }

  async testInvoice(invoiceNo): Promise<ProductOrder> {
    const result = await this.repository.findOrderDetailsByInvoice(invoiceNo);
    await this.invoiceService.generateInvoiceFilePath(result);
    if (!result) {
      return null;
    }
    return result;
  }

  // async downloadInvoice(invoiceNo: string): Promise<any> {
  //   const result = await this.repository.findOrderDetailsByInvoice(invoiceNo);
  //   const pdfObj = await this.invoiceService.generateInvoicePDF(result);
  //   if (!pdfObj) {
  //     return new ServiceResponse(null, 'Failed to generate invoice pdf');
  //   }
  //   return new ServiceResponse(pdfObj, 'Successfully generate invoice pdf');
  // }

  async downloadInvoice(invoiceNo: string): Promise<ServiceResponse> {
    const result = await this.repository.findOrderDetailsByInvoice(invoiceNo);
    const pdfObj = await this.invoiceService.generateInvoiceHTML(result);
    if (pdfObj) {
      return new ServiceResponse(pdfObj, `converted html found successfully`);
    }
    return new ServiceResponse(
      null,
      `converted html not found! please try again`,
    );
  }

  async streamInvoicePdf(res: Response, invoiceNo: string) {
    const result = await this.repository.findOrderDetailsByInvoice(invoiceNo);
    if (!result || !result?.country) {
      return res.json({
        message: `Need to provide valid invoice! please try again`,
      });
    }
    const invoiceData = await this.invoiceService.generateInvoiceData(result);

    return this.pdfService.generateInvoice(
      res,
      invoiceData,
      invoiceData?.locales,
    );
  }

  async manuallySendInvoiceCustomer(
    invoiceNo: string,
  ): Promise<ServiceResponse> {
    if (!invoiceNo) {
      return new ServiceResponse(
        null,
        `Need to provide valid invoice! please try again`,
      );
    }
    const result = await this.repository.findOrderDetailsByInvoice(invoiceNo);

    if (
      result?.shippingAddress?.email &&
      result?.invoiceNo &&
      process.env.NODE_ENV === 'production'
    ) {
      try {
        // OLD
        // const html = await this.invoiceService.generateInvoiceHtml(result);

        // NEW
        // const { invoice, locales } = await this.invoiceService.generateInvoiceData(result);
        const senderEmailAddress: string = process.env.SES_FROM_MAIL;
        const invoiceData =
          await this.invoiceService.generateInvoiceData(result);
        const invoiceHtml = await this.mailService.renderEmailTemplate(
          'invoice-new',
          invoiceData,
        );

        // const invoiceHtmlPdf = await this.mailService.renderEmailTemplate('invoice-pdf', invoiceData);
        // // Generate the PDF
        // const htmlPdfBuffer = await this.pdfService.generatePdf(invoiceHtmlPdf);

        // const pdfDoc = await this.pdfService.generatePdfDocument(null, null, invoiceData);
        const docPdfBuffer = await this.pdfService.generatePdfBuffer(
          null,
          invoiceData?.locales,
          invoiceData,
        );

        const attachmentArr = [
          // {
          //   filename: `${invoiceNo}_order_confirmation.pdf`,
          //   content: htmlPdfBuffer,
          // },
          {
            filename: `invoice_${invoiceNo}.pdf`,
            content: docPdfBuffer,
          },
        ];

        await this.mailService.sendMail(
          result?.shippingAddress?.email,
          `Invoice for Your Order #${invoiceNo} – Pantoneclo`,
          invoiceHtml,
          senderEmailAddress,
          attachmentArr,
        );

        return new ServiceResponse(
          true,
          `Invoice successfully sent to the customer.`,
        );
      } catch (error) {
        console.log('manuallySendInvoiceCustomer error', error);
      }

      return new ServiceResponse(
        null,
        `converted html not found! please try again`,
      );
    } else {
      return new ServiceResponse(
        null,
        `In development mode mail will not trigger! please try again`,
      );
    }
  }

  async updatePaymentStatusAsPaid(invoiceNo: string) {
    await this.repository.updatePaymentStatusAsPaid(invoiceNo);

    // Process affiliate commission when payment is confirmed
    try {
      const order = await this.repository.findOrderDetailsByInvoice(invoiceNo);
      // if (order) {
      //   await this.affiliateOrderIntegrationService.processOrderCompletion(order.id);
      //   console.log(`Affiliate commission processed for paid order ${invoiceNo}`);
      // }
    } catch (error) {
      console.error(`Failed to process affiliate commission for paid order ${invoiceNo}:`, error);
    }
  }

  async findGuestInvoiceForRefund(
    // phone: string,
    email: string,
    invoiceNo: string,
    // countryId: number
  ): Promise<ServiceResponse> {
    if (!email && !invoiceNo) {
      return new ServiceResponse(
        null,
        `Please provide valid information! Try again`,
      );
    }
    const result = await this.repository.findGustOrderInfo(invoiceNo);
    if (!result) {
      return new ServiceResponse(
        null,
        'Product not found for refund! Please try again',
      );
    }
    if (
      result &&
      result?.shippingAddress &&
      result?.shippingAddress?.email != email
      // && result?.billingAddress?.phone != phone
    ) {
      return new ServiceResponse(
        null,
        'Email address not match with invoice number! Please try again',
      );
    }
    return this.productRefund(invoiceNo);
    // return new ServiceResponse(result, 'Order details found for refund');
  }

  async updateOrderStatus(
    params: {
      invoiceNo?: string;
      orderStatus?: DeliveryStatusEnum;
      paymentStatus?: PaymentStatusEnum;
      courier?: number;
      remarks?: string;
      courierInfo?: Courier;
      trackingId?: string,
    },
    body?: { createdBy?: number },
  ): Promise<ServiceResponse> {
    const orderInfo = await this.findOrderDetailsByInvoice(params.invoiceNo);

    let result = null;
    if (params?.invoiceNo && params?.orderStatus) {
      let orderStatus: DeliveryStatusEnum;
      if (params?.orderStatus == DeliveryStatusEnum.Pending) {
        orderStatus = DeliveryStatusEnum.Pending;
      } else if (params?.orderStatus == DeliveryStatusEnum.On_The_Way) {
        orderStatus = DeliveryStatusEnum.On_The_Way;
      } else if (params?.orderStatus == DeliveryStatusEnum.Confirmed) {
        orderStatus = DeliveryStatusEnum.Confirmed;
      } else if (params?.orderStatus == DeliveryStatusEnum.Picked_Up) {
        orderStatus = DeliveryStatusEnum.Picked_Up;
      } else if (params?.orderStatus == DeliveryStatusEnum.Delivered) {
        orderStatus = DeliveryStatusEnum.Delivered;
        await this.repository.updatePaymentStatus(
          params?.invoiceNo,
          PaymentStatusEnum.PAID,
        );
      } else if (params?.orderStatus == DeliveryStatusEnum.Cancelled) {
        orderStatus = DeliveryStatusEnum.Cancelled;
      } else if (params?.orderStatus == DeliveryStatusEnum.MAKE_CREDIT_NOTE) {
        orderStatus = DeliveryStatusEnum.MAKE_CREDIT_NOTE;
      } else if (params?.orderStatus == DeliveryStatusEnum.CREDIT_NOTE) {
        orderStatus = DeliveryStatusEnum.CREDIT_NOTE;
      } else if (params?.orderStatus == DeliveryStatusEnum.Failed_Delivery) {
        orderStatus = DeliveryStatusEnum.Failed_Delivery;
      } else if (params?.orderStatus == DeliveryStatusEnum.Returned) {
        orderStatus = DeliveryStatusEnum.Returned;
      }

      if (params?.courier) {
        await this.repository.updateCourier(params?.invoiceNo, params?.courier);
      }

      result = await this.repository.updateDeliveryStatus(
        params?.invoiceNo,
        orderStatus,
        params?.remarks,
        body,
      );
    }

    if (params?.invoiceNo && params?.paymentStatus) {
      let paymentStatus: PaymentStatusEnum;
      if (params?.paymentStatus == PaymentStatusEnum.PAID) {
        paymentStatus = PaymentStatusEnum.PAID;
      } else if (params?.paymentStatus == PaymentStatusEnum.UNPAID) {
        paymentStatus = PaymentStatusEnum.UNPAID;
      }
      result = await this.repository.updatePaymentStatus(
        params?.invoiceNo,
        paymentStatus,
      );
    }

    if (!result) {
      return new ServiceResponse(null, 'Failed to update order status');
    }

    const productOrder = await this.findOrderDetailsByInvoice(params.invoiceNo);

    if (params?.invoiceNo && process.env.NODE_ENV === 'production') {
      // this.externalApiInventoryOrderCancel(params?.invoiceNo);
      if (params?.orderStatus == DeliveryStatusEnum.Cancelled) {
        this.externalEnventoryService.inventoryAdjustByOrderStatus(
          productOrder,
          DeliveryStatusEnum.Cancelled,
        );
      } else if (params?.orderStatus == DeliveryStatusEnum.Returned) {
        this.externalEnventoryService.inventoryAdjustByOrderStatus(
          productOrder,
          DeliveryStatusEnum.Returned,
        );
      } else if (params?.orderStatus == DeliveryStatusEnum.Failed_Delivery) {
        this.externalEnventoryService.inventoryAdjustByOrderStatus(
          productOrder,
          DeliveryStatusEnum.Failed_Delivery,
        );
      } else {
        this.externalEnventoryService.inventoryAdjustByOrderStatus(
          productOrder,
          params?.orderStatus as DeliveryStatusEnum,
        );
      }
    }

    if (
      params?.orderStatus == DeliveryStatusEnum.Confirmed &&
      params?.invoiceNo &&
      process.env.NODE_ENV === 'production'
    ) {
      try {
        if (params?.trackingId) {
          await this.repository.update(
            { invoiceNo: params.invoiceNo },
            { trackingNumber: params?.trackingId },
          );
        }

        // const productOrder = await this.findOrderDetailsByInvoice(params.invoiceNo);
        // const courierService = productOrder?.courierServices?.code;
        const courierService =
          getCourierNameByCode(productOrder?.courierServices?.code) ||
          productOrder?.courierServices?.code;
        const trackingNumber = productOrder?.trackingNumber;
        const trackingLink = productOrder?.courierServices?.trackingLink;
        const languageCode = getLanguageCodeById(
          productOrder.country.languageId,
        ).toLocaleLowerCase();
        const language = languageCode;

        const shippingLastname = productOrder?.shippingAddress?.lastName
          ? productOrder?.shippingAddress?.lastName
          : '';
        const shippingName =
          productOrder?.shippingAddress?.firstName + ' ' + shippingLastname;
        const orderData = {
          order: productOrder,
          shippingName,
          trackingLink,
          trackingNumber,
          courierService,
          languageCode,
          language,
          socialMediaLinks: productOrder.country,
        };

        const isWereHousePickup = orderInfo?.courierServices?.code == 'wp';
        if (isWereHousePickup) {
          const { subject, senderEmailAddress } =
            this.mailService.getTranslatedEmailSubject(
              productOrder?.country?.languageId,
              'wpConfirmedSubject',
            );
          const orderHtml = await this.mailService.renderEmailTemplate(
            'order-confirmation-with-warehouse-pickup',
            {
              ...orderData,
              wpAddress: {
                cpaName: process.env.CPA_NAME,
                cpaStreet: process.env.CPA_STREET,
                cpaHouse: process.env.CPA_HOUSE_NO,
                cpaCode: process.env.CPA_ZIP_CODE,
                cpaCity: process.env.CPA_CITY,
                cpaCountry: process.env.CPA_COUNTRY,
              },
            },
          );

          await this.mailService.sendMail(
            productOrder?.shippingAddress?.email,
            subject,
            orderHtml,
            null,
          );
        } else {
          const { subject, senderEmailAddress } =
            this.mailService.getTranslatedEmailSubject(
              productOrder?.country?.languageId,
              'orderConfirmedSubject',
            );
          const orderHtml = await this.mailService.renderEmailTemplate(
            'order-confirmation-with-courier-place',
            orderData,
          );

          await this.mailService.sendMail(
            productOrder?.shippingAddress?.email,
            subject,
            orderHtml,
            null,
          );
        }
      } catch (error) {
        console.log('error', error);
      }
    }

    return new ServiceResponse(result, 'Successfully updated order status');
  }

  async productRefund(invoiceNo: string): Promise<ServiceResponse> {
    if (!invoiceNo) {
      return new ServiceResponse(
        null,
        `Please provide valid invoice number! please try again`,
      );
    }
    const result = await this.repository.productRefund(invoiceNo);

    if (!result) {
      return new ServiceResponse(
        result,
        `Product order information not found with given parameter! please try again`,
      );
    }

    const viewRefundObj: ViewRefundListDto = new ViewRefundListDto();
    viewRefundObj.userId = result[0]?.user_id;
    viewRefundObj.orderId = result[0]?.order_id;
    viewRefundObj.countryId = result[0]?.country_id;
    viewRefundObj.currencySymbol = result[0]?.currency_symbol;
    viewRefundObj.product = [];
    for (const product of result) {
      const viewRefundProductDto: ViewRefundProductDto =
        new ViewRefundProductDto();
      viewRefundProductDto.id = product?.id;
      viewRefundProductDto.productName = product?.product_name;
      viewRefundProductDto.sizeVariant = product?.size_variant;
      viewRefundProductDto.colorVariant = product?.color_variant;
      viewRefundProductDto.sku = product?.sku;
      viewRefundProductDto.quantity = product?.quantity;
      viewRefundProductDto.unitPrice = product?.unit_price;
      viewRefundProductDto.discountPrice = product?.discount_price;
      viewRefundProductDto.refundStatus = null;
      if (
        product?.refund_size &&
        product?.refund_color &&
        product?.refund_size == product?.size_variant?.name &&
        product?.refund_color == product?.color_variant?.name
      ) {
        viewRefundProductDto.refundStatus = product?.refund_status;
      } else if (
        product?.refund_size &&
        !product?.refund_color &&
        product?.refund_size == product?.size_variant?.name
      ) {
        viewRefundProductDto.refundStatus = product?.refund_status;
      }
      // else if(product?.refund_color && product?.refund_color?.id == product?.color_variant?.id){
      //   viewRefundProductDto.refundStatus = product?.refund_status;
      // }
      else if (!product?.refund_size && !product?.refund_color) {
        viewRefundProductDto.refundStatus = product?.refund_status;
      }

      viewRefundProductDto.featuredImage = await this.imageGalleryRepo.findById(
        product?.featured_image_id,
      );
      viewRefundObj.product.push(viewRefundProductDto);
    }
    return new ServiceResponse(viewRefundObj, `Order information found`);
  }

  async getProductOrderDetailsByOrderId(
    orderId: number,
  ): Promise<ProductOrder> {
    const result = await this.repository.findById(orderId);
    if (!result) {
      return null;
    }
    return result;
  }

  async countOrderStatusByUserId(userId: number): Promise<ServiceResponse> {
    if (!userId) {
      return new ServiceResponse(
        null,
        `Please provide valid user id! Try again`,
      );
    }
    const result = await this.repository.countOrderStatusByUserId(userId);
    if (!result) {
      return new ServiceResponse(
        null,
        'Product not found for refund! Please try again',
      );
    }
    // return this.productRefund(invoiceNo);
    // return new ServiceResponse(result, 'Order details found for refund');
  }

  async financialReportList(
    customerPurchaseHistoryDto: CustomerPurchaseHistoryDto,
    pageOptionsDto: PageOptionsDto,
  ): Promise<ServiceResponse> {
    const result = await this.repository.financialReportList(
      customerPurchaseHistoryDto,
      pageOptionsDto,
    );
    if (!result) {
      return new ServiceResponse(result, `No data found.`);
    }
    return new ServiceResponse(
      result.itemList,
      'All product found successfully',
      result.meta,
    );
  }

  async excelDownloadFinancialReport(): Promise<any> {
    const result = await this.repository.excelDownloadFinancialReport();
    if (!result) {
      return null;
    }
    return result;
  }
  async excelDownloadCustomerPurchaseHistory(): Promise<any> {
    const result = await this.repository.excelDownloadCustomerPurchaseHistory();
    if (!result) {
      return null;
    }
    return result;
  }

  async reportProductBestSelling(
    pageOptionsDto: PageOptionsDto,
  ): Promise<ServiceResponse> {
    const result =
      await this.repository.reportProductBestSelling(pageOptionsDto);
    if (!result) {
      return new ServiceResponse(result, `No data found.`);
    }
    return new ServiceResponse(
      result.itemList,
      'All product found successfully',
      result.meta,
    );
  }
  async reportProductSlowMoving(
    pageOptionsDto: PageOptionsDto,
  ): Promise<ServiceResponse> {
    const result =
      await this.repository.reportProductSlowMoving(pageOptionsDto);
    if (!result) {
      return new ServiceResponse(result, `No data found.`);
    }
    return new ServiceResponse(
      result.itemList,
      'All product found successfully',
      result.meta,
    );
  }
  async reportProductSalesReport(
    pageOptionsDto: PageOptionsDto,
    queryFilterDto: QueryFilterDto,
  ): Promise<ServiceResponse> {
    const result = await this.repository.reportProductSalesReport(
      pageOptionsDto,
      queryFilterDto,
    );
    if (!result) {
      return new ServiceResponse(result, `No data found.`);
    }
    return new ServiceResponse(
      result.itemList,
      'All product found successfully',
      result.meta,
    );
  }
  async excelDownloadReportProductSlowMoving(): Promise<any> {
    const result = await this.repository.excelDownloadReportProductSlowMoving();
    if (!result) {
      return null;
    }
    return result;
  }

  async excelDownloadProductSalesReport(): Promise<any> {
    const result = await this.repository.excelDownloadProductSalesReport();
    if (!result) {
      return null;
    }
    return result;
  }
  async excelDownloadReportProductBestSelling(): Promise<any> {
    const result =
      await this.repository.excelDownloadReportProductBestSelling();
    if (!result) {
      return null;
    }
    return result;
  }

  async externalApiInventoryOrderCancel(
    oderNo: string,
  ): Promise<ServiceResponse> {
    const body: any = {
      order_no: oderNo,
    };
    const host = process.env.INVENTORY_HOST;
    const uri = process.env.INVENTORY_URI_ORDER_CANCEL;
    const method = 'post';
    const accessToken = process.env.INVENTORY_ACCESSTOKEN;

    try {
      const response = await lastValueFrom(
        this.httpService.request({
          url: `${host}/${uri}`,
          method,
          data: body,
          headers: {
            'Content-Type': 'application/json',
            'x-api-key': accessToken,
          },
        }),
      );
      if (!response) {
        return new ServiceResponse(null, `Product not found! please try again`);
      }
      return new ServiceResponse(response.data, 'Quantity updated');
    } catch (error) {
      console.error('API call error 2:', error.message);
      if (error.response) {
        console.error('Status:', error.response.status);
        console.error('Response data:', error.response.data);
      }

      return new ServiceResponse(null, `'Failed to call external API'`);
    }
  }
  async stockManageBySelling(
    productOrderDto: ProductOrder,
  ): Promise<ServiceResponse> {
    const body: any = this.formatBodyInventory(productOrderDto);

    const host = process.env.INVENTORY_HOST;
    const uri = process.env.INVENTORY_URI;
    const method = 'post';
    const accessToken = process.env.INVENTORY_ACCESSTOKEN;

    try {
      const response = await lastValueFrom(
        this.httpService.request({
          url: `${host}/${uri}`,
          method,
          data: body,
          headers: {
            'Content-Type': 'application/json',
            'x-api-key': accessToken,
          },
        }),
      );

      if (!response) {
        return new ServiceResponse(null, `Product not found! please try again`);
      }
      return new ServiceResponse(response.data, 'Quantity updated');
    } catch (error) {
      console.error('API call error 3:', error.message);
      if (error.response) {
        console.error('Status:', error.response.status);
        console.error('Response data:', error.response.data);
      }

      return new ServiceResponse(null, `'Failed to call external API'`);
    }
  }

  private formatBodyInventory(productOrderDto: ProductOrder) {
    // [1:cash, 2:cheque, 3:Bank Transfer, 4:other, 5:code, 6:ssl commerce, 7:stripe] Gneerate swith case for payment method
    let paymentType = 1;
    switch (productOrderDto.paymentMethod) {
      case 'COD':
        paymentType = 1;
        break;
      case 'ssl-commerce':
        paymentType = 6;
        break;
      case 'stripe':
        paymentType = 7;
        break;
      case 'revolut':
        paymentType = 8;
        break;
      default:
        paymentType = 4;
    }

    const body: any = {
      warehouse:
        productOrderDto.country.code == 'BD'
          ? productOrderDto.country.code
          : 'SI',
      tax_rate: productOrderDto.vatTax,
      discount: productOrderDto.discount,
      shipping: productOrderDto?.shippingCharge,
      grand_total: productOrderDto?.amount,
      received_amount: productOrderDto?.amount,
      paid_amount: productOrderDto?.amount,
      status: 2, // [1:received, 2:pending, 3:ordered]
      payment_type: paymentType, // [1:cash, 2:cheque, 3:Bank Transfer, 4:other, 5:code, 6:ssl commerce, 7:stripe]   Integer: Payment type, assuming 1 represents a specific payment method
      order_type: productOrderDto.paymentMethod == 'COD' ? 1 : 2, // [1:cod, 2:Prepaid]
      payment_status: productOrderDto.paymentStatus == 'PAID' ? 1 : 2, // [1:Paid, 2:Unpaid, 3:Partial]
      order_no: productOrderDto?.invoiceNo,
      market_place: 'Pantoneclo',
      country: productOrderDto.country.code,
      date: productOrderDto?.createdAt,
      reference_code: productOrderDto?.invoiceNo,
      currency: productOrderDto?.currency?.currency,
      customer: {
        name: `${productOrderDto?.shippingAddress?.firstName} ${productOrderDto?.shippingAddress?.lastName}`,
        email: productOrderDto?.shippingAddress?.email,
        phone: `${productOrderDto?.shippingAddress?.phone}`,
        country: productOrderDto?.country?.code,
        city: productOrderDto?.shippingAddress?.city,
        address: productOrderDto?.shippingAddress?.address,
      },
      items: [],
    };

    for (const details of productOrderDto?.productOrderDetails) {
      const item: any = {};
      item.code = details?.sku;
      item.price =
        details?.discountPrice > 0
          ? details?.discountPrice
          : details?.unitPrice;
      item.quantity = details.quantity;
      body?.items?.push(item);
    }
    return body;
  }

  async excelDownloadProductOrderReport(
    pageOptionsDto: PageOptionsDto,
    queryFilterDto: {
      globalSearch?: string;
      deliveryStatus: DeliveryStatusEnum;
      countrySearch?: string;
      nameSearch?: string;
      phoneSearch?: string;
      paymentSearch?: string;
      startDate?: Date;
      endDate?: Date;
    },
  ): Promise<any> {
    const result = await this.repository.excelDownloadProductOrderReport(
      pageOptionsDto,
      queryFilterDto,
    );
    if (!result) {
      return null;
    }
    return result;
  }

  async getProductOrderListWithUserAsSubscriber(
    countryId: number,
    orderId: number,
  ): Promise<any> {
    const result =
      await this.repository.getProductOrderListWithUserAsSubscriber(
        countryId,
        orderId,
      );
    return result;
  }
}
