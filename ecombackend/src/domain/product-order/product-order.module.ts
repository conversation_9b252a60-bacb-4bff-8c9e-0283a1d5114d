import { MiddlewareConsumer, Module, RequestMethod } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ProductOrderController } from './product-order.controller';
import { ProductOrderRepository } from './product-order.repository';
import { ProductOrderService } from './product-order.service';
import { ProductOrder } from '../entities/product-order.entity';
import { ProductOrderDetailsRepository } from './product-order-details.repository';
import { MailService } from 'src/common/services/mail.service';
import { CountryRepository } from '../country/country.repository';
import { InvoiceService } from 'src/common/services/invoice.service';
import { ImageGalleryRepository } from '../image/image-gallery.repository';
import { HttpModule } from '@nestjs/axios';
import { ProductModule } from '../product/product.module';
import { ExcelService } from 'src/common/services/excel.service';
import { PdfService } from 'src/node-pdf/pdf.service';
import { ExternalEnventoryService } from 'src/common/services/external/manage-inventory.service';
import { AuthService } from 'src/application/auth/auth.service';
import { LoginService } from '../login/login.service';
import { TokenManagementService } from 'src/common/providers/token-management/token-management.service';
import { LoginRepository } from '../login/login.repository';
import { RolesService } from '../roles/roles.service';
import { RolesRepository } from '../roles/roles.repository';
import { UserService } from '../user/user.service';
import { UserRepository } from '../user/user.repository';
import { CacheModule } from '@nestjs/cache-manager';
import * as fsStore from 'cache-manager-fs-hash';
import { AffiliateModule } from '../affiliate/affiliate.module';
import { AddressShippingRepository } from '../address-shipping/address-shipping.repository';
import { AddressBillingRepository } from '../address-billing/address-billing.repository';
// import { JwtTokenVerifierMiddleware } from '../../common/middlewares/jwt-token-verifier.middleware';
// import { TokenManagementModule } from '../../common/providers/token-management/token-management.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([ProductOrder]),
    HttpModule,
    ProductModule,
    AffiliateModule,
    CacheModule.register({
      store: fsStore, // File system cache store
      options: {
        path: './cache', // Ensure this directory exists
        ttl: 3600000, // 1 hour
        maxsize: **********, // 1GB
        zip: true, // Compress cache
        preventfill: false, // Ensure cache is preloaded on start
      },
    }),
  ],
  providers: [
    ProductOrderService,
    ProductOrderRepository,
    ProductOrderDetailsRepository,
    MailService,
    CountryRepository,
    ImageGalleryRepository,
    InvoiceService,
    ExcelService,
    PdfService,
    ExternalEnventoryService,
    AuthService,
    LoginService,
    TokenManagementService,
    LoginRepository,
    RolesService,
    RolesRepository,
    UserService,
    UserRepository,
    AddressShippingRepository,
    AddressBillingRepository,
  ],
  controllers: [ProductOrderController],
  exports: [ProductOrderService],
})
export class ProductOrderModule {
  configure(consumer: MiddlewareConsumer) {
    //   consumer.apply(JwtTokenVerifierMiddleware).forRoutes('*');
    // consumer.apply(JwtTokenVerifierMiddleware).forRoutes({ path: 'order', method: RequestMethod.POST });
    // // consumer.apply(JwtTokenVerifierMiddleware).forRoutes({ path: 'order/findAllDataByPagination', method: RequestMethod.GET });
    // consumer.apply(JwtTokenVerifierMiddleware).forRoutes({ path: 'order/:id', method: RequestMethod.PUT });

    // consumer.apply(JwtTokenVerifierMiddleware).forRoutes({ path: 'order/delete/:id', method: RequestMethod.DELETE });
    // consumer.apply(JwtTokenVerifierMiddleware).forRoutes({ path: 'order/restore/:id', method: RequestMethod.POST });
    // consumer.apply(JwtTokenVerifierMiddleware).forRoutes({ path: 'order/activeOrInactive', method: RequestMethod.POST });
    // consumer.apply(JwtTokenVerifierMiddleware).forRoutes({ path: 'order/sendInvoice/customer', method: RequestMethod.POST });
    // consumer.apply(JwtTokenVerifierMiddleware).forRoutes({ path: 'order/report/financialReport', method: RequestMethod.GET });
    // consumer.apply(JwtTokenVerifierMiddleware).forRoutes({ path: 'order/report/customerPurchaseHistory', method: RequestMethod.GET });
    // consumer.apply(JwtTokenVerifierMiddleware).forRoutes({ path: 'order/report/product/bestSelling', method: RequestMethod.GET });
    // consumer.apply(JwtTokenVerifierMiddleware).forRoutes({ path: 'order/report/product/slowMoving', method: RequestMethod.GET });
    // consumer.apply(JwtTokenVerifierMiddleware).forRoutes({ path: 'order/report/product/salesReport', method: RequestMethod.GET });
    // consumer.apply(JwtTokenVerifierMiddleware).forRoutes({ path: 'order/report/excelDownload/product/salesReport', method: RequestMethod.GET });
    // consumer.apply(JwtTokenVerifierMiddleware).forRoutes({ path: 'order/report/excelDownload/customerPurchaseHistory', method: RequestMethod.GET });
    // consumer.apply(JwtTokenVerifierMiddleware).forRoutes({ path: 'order/report/excelDownload/productSlowMoving', method: RequestMethod.GET });
    // consumer.apply(JwtTokenVerifierMiddleware).forRoutes({ path: 'order/report/excelDownload/financialReport', method: RequestMethod.GET });
    // consumer.apply(JwtTokenVerifierMiddleware).forRoutes({ path: 'order/report/excelDownload/bestSelling', method: RequestMethod.GET });
  }
}
