import { MiddlewareConsumer, Module, RequestMethod } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MailIntegrationController } from './mail-integration.controller';
import { MailIntegrationService } from './mail-integration.service';
import { JwtTokenVerifierMiddleware } from 'src/common/middlewares/jwt-token-verifier.middleware';
import { MailService } from 'src/common/services/mail.service';
import { InvoiceService } from 'src/common/services/invoice.service';
import { CountryRepository } from '../country/country.repository';
import { ProductService } from '../product/product.service';
import { ProductRepository } from '../product/product.repository';
import { ProductMetaRepository } from '../product/product-meta.repository';
import { AttributeValueRepository } from '../attribute-value/attribute-value.repository';
import { ImageGalleryRepository } from '../image/image-gallery.repository';
import { ProductTagsService } from '../product-tags/product-tags.service';
import { SeoMetaService } from '../seo-meta/seo-meta.service';
import { CategoryRepository } from '../category/category.repository';
import { ProductRelationRepository } from '../product/product-relation.repository';
import { Product, ProductLocalization } from '../entities/product.entity';
import { Country } from '../entities/country.entity';
import { ProductTagsRepository } from '../product-tags/product-tags.repository';
import { ProductTagsMetaRepository } from '../product-tags/product-tags-meta.repository';
import { SeoMetaRepository } from '../seo-meta/seo-meta.repository';
import { CategoryService } from '../category/category.service';
import { S3Service } from 'src/common/services/s3.service';
import { NewsSubscriptionService } from '../news-subscription/news-subscription.service';
import { NewsSubscriptionRepository } from '../news-subscription/news-subscription.repository';
import { UserService } from '../user/user.service';
import { UserRepository } from '../user/user.repository';
import { CountryService } from '../country/country.service';
import { CartService } from '../cart/cart.service';
import { CartRepository } from '../cart/cart.repository';
import { CartItem } from '../cart/entities/cart.-item.entity';
import { CartItemRepository } from '../cart/cart-item.repository';
import { CartOtpRepository } from '../cart/cart-otp.repository';
import { SmsService } from '../../common/services/sms.service';
import { CacheModule } from '@nestjs/cache-manager';
import * as fsStore from 'cache-manager-fs-hash';
import { ProductOrderService } from '../product-order/product-order.service';
import { ProductOrderRepository } from '../product-order/product-order.repository';
import { HttpModule } from '@nestjs/axios';
import { PdfService } from 'src/node-pdf/pdf.service';
import { ProductOrderDetailsRepository } from '../product-order/product-order-details.repository';
import { ExternalEnventoryService } from 'src/common/services/external/manage-inventory.service';
import { DiscountsService } from 'src/discounts/discounts.service';
import { Discount } from '../entities/discount.entity';
import { Cart } from '../cart/entities/cart.entity';
import { DiscountCountry } from '../entities/discount-country.entity';
import { DiscountUsage } from '../entities/discount-usage.entity';
import {
  FortunateWheel,
  FortunateWheelUsage,
} from '../entities/fortunate-wheel.entity';
import { FortunateWheelService } from 'src/discounts/fortunate-wheel.service';
import { AffiliateIntegrationService } from '../affiliate/services/affiliate-integration.service';
import { AffiliateOrderIntegrationService } from '../affiliate/services/affiliate-order-integration.service';
import { AffiliateTrackingService } from '../affiliate/services/affiliate-tracking.service';
import { AffiliateService } from '../affiliate/services/affiliate.service';
import { AffiliateProfile } from '../affiliate/entities/affiliate-profile.entity';
import { AffiliateCommission } from '../affiliate/entities/affiliate-commission.entity';
import { ProductOrder } from '../entities/product-order.entity';
import { AffiliateDiscountCommissionService } from '../affiliate/services/affiliate-discount-commission.service';
import { AffiliateDiscount } from '../affiliate/entities/affiliate-discount.entity';
import { AffiliateClick } from '../affiliate/entities/affiliate-click.entity';
import { User } from '../entities/user.entity';
import { AffiliateConversion } from '../affiliate/entities/affiliate-conversion.entity';
import { UTMTrackingService } from '../affiliate/services/utm-tracking.service';
import { ConversionTrackingService } from '../affiliate/services/conversion-tracking.service';
import { AffiliateDiscountUsage } from '../affiliate/entities/affiliate-discount-usage.entity';

// import { JwtTokenVerifierMiddleware } from '../../common/middlewares/jwt-token-verifier.middleware';
// import { TokenManagementModule } from '../../common/providers/token-management/token-management.module';
import { AddressShippingRepository } from '../address-shipping/address-shipping.repository';
import { AddressBillingRepository } from '../address-billing/address-billing.repository';

@Module({
  imports: [
    // TypeOrmModule.forFeature([MailIntegration])
    TypeOrmModule.forFeature([
      Product,
      ProductLocalization,
      Country,
      Discount,
      DiscountCountry,
      DiscountUsage,
      FortunateWheel,
      FortunateWheelUsage,
      Cart,
      CartItem,
      AffiliateProfile,
      AffiliateCommission,
      ProductOrder,
      AffiliateDiscount,
      AffiliateClick,
      User,
      AffiliateConversion,
      AffiliateDiscountUsage,
    ]),
    CacheModule.register({
      store: fsStore, // File system cache store
      options: {
        path: './cache', // Ensure this directory exists
        ttl: 3600000, // 1 hour
        maxsize: **********, // 1GB
        zip: true, // Compress cache
        preventfill: false, // Ensure cache is preloaded on start
      },
    }),
    HttpModule,
  ],
  providers: [
    MailIntegrationService,
    ProductService,
    ProductRepository,
    ProductMetaRepository,
    AttributeValueRepository,
    ImageGalleryRepository,
    ProductTagsService,
    SeoMetaService,
    ProductRelationRepository,
    CountryRepository,
    CategoryRepository,
    ProductTagsRepository,
    ProductTagsMetaRepository,
    SeoMetaRepository,
    CategoryService,
    S3Service,
    NewsSubscriptionService,
    NewsSubscriptionRepository,
    MailService,
    InvoiceService,
    UserService,
    UserRepository,
    CountryService,
    CartService,
    CartRepository,
    CartItemRepository,
    CartOtpRepository,
    SmsService,
    ProductOrderService,
    ProductOrderRepository,
    PdfService,
    ProductOrderDetailsRepository,
    ExternalEnventoryService,
    DiscountsService,
    FortunateWheelService,
    AffiliateIntegrationService,
    AffiliateOrderIntegrationService,
    AffiliateTrackingService,
    AffiliateService,
    AffiliateDiscountCommissionService,
    UTMTrackingService,
    ConversionTrackingService,
    AddressShippingRepository,
    AddressBillingRepository,
  ],
  controllers: [MailIntegrationController],
  exports: [MailIntegrationService],
})
export class MailIntegrationModule {
  configure(consumer: MiddlewareConsumer) {
    // consumer.apply(JwtTokenVerifierMiddleware).forRoutes({ path: 'State', method: RequestMethod.POST });
    consumer.apply(JwtTokenVerifierMiddleware).forRoutes({
      path: 'mail-integration/findAllUserDataByPagination',
      method: RequestMethod.GET,
    });
  }
}
