import { IProductVariantJsonb } from '../interfaces/product-variant-jsonb.interface';

export interface PriceRange {
  minPrice: number;
  maxPrice: number;
  discountMinPercentage: number;
  discountMaxPercentage: number;
}

export class PriceCalculatorUtil {
  /**
   * Calculate min and max prices and discount percentages from product variants or single prices
   */
  static calculatePriceRange(
    variants: IProductVariantJsonb[] | null,
    discountPrice: number | null,
    unitPrice: number | null
  ): PriceRange {
    // For single-variant products, use discount_price and unit_price
    if (!variants || variants.length === 0) {
      const finalDiscountPrice = discountPrice || 0;
      const finalUnitPrice = unitPrice || 0;
      const discountPercentage = this.calculateDiscountPercentage(finalUnitPrice, finalDiscountPrice);

      return {
        minPrice: finalDiscountPrice || finalUnitPrice,
        maxPrice: finalDiscountPrice || finalUnitPrice,
        discountMinPercentage: discountPercentage,
        discountMaxPercentage: discountPercentage
      };
    }

    // For multi-variant products, extract all prices from variants
    const allDiscountPrices: number[] = [];
    const allUnitPrices: number[] = [];
    const allDiscountPercentages: number[] = [];

    variants.forEach(variant => {
      if (variant.variantDetails && Array.isArray(variant.variantDetails)) {
        variant.variantDetails.forEach(detail => {
          const discountPrice = typeof detail.discountPrice === 'string'
            ? parseFloat(detail.discountPrice)
            : detail.discountPrice;
          const unitPrice = typeof detail.unitPrice === 'string'
            ? parseFloat(detail.unitPrice)
            : detail.unitPrice;

          if (!isNaN(discountPrice) && discountPrice > 0) {
            allDiscountPrices.push(discountPrice);
          }
          if (!isNaN(unitPrice) && unitPrice > 0) {
            allUnitPrices.push(unitPrice);
          }

          // Calculate discount percentage for this variant
          const discountPercentage = this.calculateDiscountPercentage(unitPrice, discountPrice);
          if (discountPercentage > 0) {
            allDiscountPercentages.push(discountPercentage);
          }
        });
      }
    });

    // If no valid prices found in variants, fallback to single prices
    if (allDiscountPrices.length === 0 && allUnitPrices.length === 0) {
      const finalDiscountPrice = discountPrice || 0;
      const finalUnitPrice = unitPrice || 0;
      const discountPercentage = this.calculateDiscountPercentage(finalUnitPrice, finalDiscountPrice);

      return {
        minPrice: finalDiscountPrice || finalUnitPrice,
        maxPrice: finalDiscountPrice || finalUnitPrice,
        discountMinPercentage: discountPercentage,
        discountMaxPercentage: discountPercentage
      };
    }

    // Use discount prices if available, otherwise unit prices
    const pricesForMinMax = allDiscountPrices.length > 0 ? allDiscountPrices : allUnitPrices;
    const minDiscountPercentage = allDiscountPercentages.length > 0
      ? Math.min(...allDiscountPercentages)
      : 0;
    const maxDiscountPercentage = allDiscountPercentages.length > 0
      ? Math.max(...allDiscountPercentages)
      : 0;

    return {
      minPrice: Math.min(...pricesForMinMax),
      maxPrice: Math.max(...pricesForMinMax),
      discountMinPercentage: Math.round(minDiscountPercentage * 100) / 100, // Round to 2 decimal places
      discountMaxPercentage: Math.round(maxDiscountPercentage * 100) / 100  // Round to 2 decimal places
    };
  }

  /**
   * Calculate discount percentage from unit price and discount price
   */
  static calculateDiscountPercentage(unitPrice: number, discountPrice: number): number {
    if (!unitPrice || unitPrice <= 0 || !discountPrice || discountPrice <= 0) {
      return 0;
    }

    if (discountPrice >= unitPrice) {
      return 0; // No discount if discount price is higher than or equal to unit price
    }

    return ((unitPrice - discountPrice) / unitPrice) * 100;
  }

  /**
   * Update min/max prices and discount percentages for a product meta record
   */
  static updateProductMetaPrices(productMeta: any): void {
    const priceRange = this.calculatePriceRange(
      productMeta.variants,
      productMeta.discountPrice,
      productMeta.unitPrice
    );

    productMeta.minPrice = priceRange.minPrice;
    productMeta.maxPrice = priceRange.maxPrice;
    productMeta.discountMinPercentage = priceRange.discountMinPercentage;
    productMeta.discountMaxPercentage = priceRange.discountMaxPercentage;
  }
}
