import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  Logger,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { ApiResponse } from '../utils/api-response';

@Catch(HttpException)
export class HttpExceptionFilter implements ExceptionFilter {
  private logger = new Logger(HttpExceptionFilter.name);
  catch(exception: HttpException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();
    const status = exception.getStatus();

    const logFormat = `<< ${request.method} ${request.url} ${status}`;

    this.logger.error(
      `${logFormat} ${JSON.stringify(exception.message)} ${JSON.stringify(
        exception.stack,
      )}`,
    );

    const errorResponse = new ApiResponse(
      false,
      null,
      'Something went wrong, Please try again later',
      status,
    );

    response.status(status).json(errorResponse);
  }
}
