import {
  ArgumentsHost,
  Catch,
  ExceptionFilter,
  HttpStatus,
  ValidationError,
} from '@nestjs/common';
import { Response } from 'express';
import { ApiResponse } from '../utils/api-response';

@Catch()
export class ValidationExceptionFilter implements ExceptionFilter {
  catch(exception: ValidationError[], host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const status = HttpStatus.BAD_REQUEST;
    // console.log('exception', exception);

    const validationErrors = exception.map(
      (error: { constraints: any; property: any }) => {
        const { constraints, property } = error;
        const errorMessage = Object.values(constraints).join(', ');
        return { property, errorMessage };
      },
    );

    const errorResponse = new ApiResponse(
      false,
      validationErrors,
      'Validation failed',
      status,
    );
    response.status(status).json(errorResponse);
  }
}
