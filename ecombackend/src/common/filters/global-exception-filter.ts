import { Catch, ExceptionFilter, Logger, ArgumentsHost } from '@nestjs/common';
import { ApiResponse } from '../utils/api-response';

@Catch()
export class GlobalExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(GlobalExceptionFilter.name);

  catch(error: any, host: ArgumentsHost): void {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse();
    const request = ctx.getRequest();

    const environment = process.env.NODE_ENV || 'development';

    if (environment === 'development') {
      this.logger.error(`Unhandled error: ${error}`, error.stack);
    }

    // response.status(500).json({
    //   statusCode: 500,
    //   timestamp: new Date().toISOString(),
    //   path: request.url,
    //   message: 'Internal Server Error',
    // });
    const errorMsg = error.message ? error.message : error + error.stack;
    const errorResponse = new ApiResponse(false, null, errorMsg, 500);

    response.status(500).json(errorResponse);
  }
}
