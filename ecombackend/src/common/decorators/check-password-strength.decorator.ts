import { ValidationOptions, registerDecorator } from 'class-validator';
import { PasswordStrengthConstraint } from '../validators/password-strength.validator';

export const CheckPasswordStrength = (
  validationOptions?: ValidationOptions,
) => {
  return (object: unknown, propertyName: string) => {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [],
      validator: PasswordStrengthConstraint,
    });
  };
};
