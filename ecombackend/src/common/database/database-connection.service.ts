import { Injectable, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { DataSource } from 'typeorm';

@Injectable()
export class DatabaseReconnectService implements OnModuleInit, OnModuleDestroy {
  constructor(private dataSource: DataSource) {}

  async onModuleInit() {
    await this.checkAndReconnect();
    this.startConnectionMonitor();
  }

  async onModuleDestroy() {
    await this.dataSource.destroy();
    console.log('Database connection closed.');
  }

  private async checkAndReconnect() {
    try {
      if (!this.dataSource.isInitialized) {
        console.warn('Database connection lost ❌ Attempting to reconnect...');
        await this.dataSource.initialize();
        console.log('Database reconnected ✅');
      }
    } catch (error) {
      console.log(
        'Database reconnection failed ❌ Retrying in 5 seconds...',
        error,
      );
      setTimeout(() => this.checkAndReconnect(), 5000); // Retry after 5s
    }
  }

  private startConnectionMonitor() {
    setInterval(async () => {
      try {
        if (!this.dataSource.isInitialized) {
          await this.checkAndReconnect();
        }
      } catch (error) {
        console.log('Database health check failed ❌', error);
      }
    }, 10000); // Check every 10 seconds
  }
}
