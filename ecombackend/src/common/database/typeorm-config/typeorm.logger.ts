import { Logger, QueryRunner } from 'typeorm';
import { performance } from 'perf_hooks';
import * as fs from 'fs';
import * as path from 'path';

export class CustomTypeOrmLogger implements Logger {
    // logQuery(query: string, parameters?: any[]) {
    //     const start = performance.now(); // Start time 
    //     console.log(`🚀 QUERY::: ${query} - PARAM:::${parameters ? JSON.stringify(parameters) : ''}`);

    //     setImmediate(() => {
    //         const duration = performance.now() - start;
    //         console.log(`⏳ Execution Time:::: ${duration.toFixed(2)}ms`);
    //     });
    // }

    logQuery(query: string, parameters?: any[], queryRunner?: QueryRunner) {
        const startTime = process.hrtime(); // High-resolution timer

        // queryRunner?.query(query, parameters).then(() => {
        //     const [seconds, nanoseconds] = process.hrtime(startTime);
        //     const executionTime = (seconds * 1e3 + nanoseconds / 1e6).toFixed(2); // Convert to ms 

        //     console.log(`🚀 QUERY EXECUTED in ${executionTime}ms`);
        //     if (Number(executionTime) > 1000) {
        //         console.warn(`⚠️ SLOW QUERY DETECTED: ${query.substring(0, 100)}...`); // Log only first 100 chars
        //     }
        // }).catch(error => {
        //     console.error(`❌ QUERY ERROR: ${query.substring(0, 100)}...`);
        //     console.error(`🚨 ERROR: ${error.message}`);
        // });


        // const [seconds, nanoseconds] = process.hrtime(startTime);
        // const executionTime = (seconds * 1e3 + nanoseconds / 1e6).toFixed(2); // Convert to ms 

        // console.log(`🚀 QUERY EXECUTED in ${executionTime} ms ::: ${query.substring(0, 100)}...  - PARAM:::${parameters ? JSON.stringify(parameters) : ''}`);
        // if (Number(executionTime) > 1000) {
        //     console.warn(`⚠️ SLOW QUERY DETECTED: ${query.substring(0, 100)}...`); // Log only first 100 chars
        // }





        // const start = performance.now(); // Start time 
        // // console.log(`🚀 QUERY::: ${query} - PARAM:::${parameters ? JSON.stringify(parameters) : ''}`);

        // setImmediate(() => {
        //     const duration = performance.now() - start;
        //     if (Number(duration) > 10) {
        //         console.warn(`⚠️ SLOW QUERY DETECTED: ${duration.toFixed(2)} ms ::: ${query.substring(0, 100)}...  - PARAM:::${parameters ? JSON.stringify(parameters) : ''}`); // Log only first 100 chars
        //     }
        //     else {
        //         console.log(`🚀 QUERY EXECUTED in ${duration.toFixed(2)} ms ::: ${query.substring(0, 100)}...  - PARAM:::${parameters ? JSON.stringify(parameters) : ''}`);
        //     }
        //     // console.log(`⏳ Execution Time:::: ${duration.toFixed(2)}ms`);
        // });
    }

    logQueryError(error: string, query: string, parameters?: any[]) {
        // console.error(`❌ QUERY ERROR:::: ${query}`);
        // console.error(`🚨 ERROR::: ${error}`);
        const newDate = new Date().toISOString().split('T')[0];

        fs.appendFileSync(`./logs/${newDate}_error_query_logs.txt`, `❌ ERROR QUERY DETECTED: ${new Date().toISOString()}; QUERY::: ${query.substring(0, 100)}...  - PARAM:::${parameters ? JSON.stringify(parameters) : ''} - ERROR::: ${error}` + '\n');
    }

    logQuerySlow(time: number, query: string, parameters?: any[]) {
        console.warn(`⚠️ SLOW QUERY DETECTED: ${time} ms ::: ${query.substring(0, 100)}...  - PARAM:::${parameters ? JSON.stringify(parameters) : ''}`);
        const newDate = new Date().toISOString().split('T')[0];

        fs.appendFileSync(`./logs/${newDate}_slow_query_logs.txt`, `⚠️ SLOW QUERY DETECTED: ${time} ms ::: ${query.substring(0, 100)}...  - PARAM:::${parameters ? JSON.stringify(parameters) : ''}` + '\n');
    }

    log(level: 'log' | 'info' | 'warn', message: any) {
        // console.log(`📌 ${level.toUpperCase()}::: ${message}`);
    }
    logSchemaBuild(message: string, queryRunner?: QueryRunner) {
        // console.log('📌 SCHEMA BUILD::: ', message);
    }

    logMigration(message: string, queryRunner?: QueryRunner) {
        // console.log('📌 MIGRATION::: ', message);

    }
}
