import { ConfigService } from '@nestjs/config';
import { TypeOrmModuleOptions, TypeOrmOptionsFactory } from '@nestjs/typeorm';
import { Injectable } from '@nestjs/common';
import { IDatabaseConfig } from 'src/config/database-config';
import { User } from 'src/domain/entities/user.entity';
import { Country } from 'src/domain/entities/country.entity';
import { Currency } from 'src/domain/entities/currency.entity';
import { Login } from 'src/domain/entities/login.entity';
import { Brand } from 'src/domain/entities/brand.entity';
import { CouponMeta } from 'src/domain/entities/coupon-meta.entity';
import { EventActivityMeta } from 'src/domain/entities/event-activity-meta.entity';
import { ProductVariant } from 'src/domain/entities/product-variant.entity';
import { ImageGallery } from 'src/domain/entities/image-gallery.entity';
import { Address } from 'src/domain/entities/address.entity';
import { Category } from 'src/domain/entities/category.entity';
import {
  Product,
  ProductLocalization,
} from 'src/domain/entities/product.entity';
import { Attribute } from 'src/domain/entities/attribute.entity';
import { ProductMeta } from 'src/domain/entities/product-meta.entity';
import { EventActivity } from 'src/domain/entities/event-activity.entity';
import { State } from 'src/domain/entities/state.entity';
import { City } from 'src/domain/entities/city.entity';
import { AttributeValue } from 'src/domain/entities/attribute-value.entity';
import { UserType } from 'src/domain/entities/user-type.entity';
import { EventActivityHistory } from 'src/domain/entities/event-activity-history.entity';
import { Coupon } from 'src/domain/entities/coupon.entity';
import { ProductOrder } from 'src/domain/entities/product-order.entity';
import { ProductOrderDetails } from 'src/domain/entities/product-order-details.entity';
import { Wishlist } from 'src/domain/entities/wishlist.entity';
import { ProductRefundType } from 'src/domain/entities/product-refund-type.entity';
import { ProductRefund } from 'src/domain/entities/product-refund.entity';
import { Payment } from 'src/domain/entities/payment.entity';
import { QuickLinks } from 'src/domain/entities/quick-links.entity';
import { CourierServices } from 'src/domain/entities/courier-services.entity';
import { NewsSubscription } from 'src/domain/entities/news-subscription.entity';
import { SubscriptionPopup } from 'src/domain/entities/subscription-popup.entity';
import { SetupHome } from 'src/domain/entities/setup-home.entity';
import { Faq } from 'src/domain/entities/faq.entity';
import { ProductTags } from 'src/domain/entities/product-tags.entity';
import { ProductTagsMeta } from 'src/domain/entities/product-tags-meta.entity';
import { SetupHomePartnerInstagram } from 'src/domain/entities/setup-home-partner-instagram.entity';
import { Setup } from 'src/domain/entities/setup.entity';
import { SeoMeta } from 'src/domain/entities/seo-meta.entity';
import { CourierHistory } from 'src/domain/entities/courier-history.entity';
import { Courier } from 'src/domain/entities/courier.entity';
import { ProductFeed } from 'src/domain/entities/product-feed.entity';
import { UniversalExport } from 'src/domain/entities/universal-export.entity';
import { FieldMapping } from 'src/domain/entities/field-mapping.entity';
import {
  ProductCollection,
  ProductCollectionPivot,
} from 'src/domain/entities/product-collection.entity';
import { SuggestedProduct } from 'src/product-suggestion/entities/product-suggestion.entity';
import { Seller } from 'src/domain/seller/entity/seller.entity';
import {
  SizeChart,
  SizeChartValue,
} from 'src/size-chart/entities/size-chart.entity';
import { Cart } from 'src/domain/cart/entities/cart.entity';
import { CartItem } from 'src/domain/cart/entities/cart.-item.entity';
import { CartOtp } from 'src/domain/cart/entities/cart-otp.entity';
import { ProductRelated } from 'src/domain/product-related/entity/product-related.entity';
import { CustomTypeOrmLogger } from './typeorm.logger';
import { Role } from 'src/domain/roles/entities/role.entity';
import { Permission } from 'src/domain/permissions/entities/permission.entity';
import { CouponUsage } from 'src/domain/coupon/entities/coupon-usages.entity';
import { CategoryProductRelated } from 'src/domain/category-product-related/entity/category-product-related.entity';
import { BlogPost } from 'src/domain/blog/entity/blog-post.entity';
import { BlogCategory } from 'src/domain/blog/entity/blog-category.entity';
import { BlogComment } from 'src/domain/blog/entity/blog-comment.entity';
import { BlogTag } from 'src/domain/blog/entity/blog-tag.entity';
import { BlogPostProduct } from 'src/domain/blog/entity/blog-post-product.entity';
import { BlogAuthor } from 'src/domain/blog/entity/blog-author.entity';
import { BlogPostLocalization } from 'src/domain/blog/entity/blog-post-localization.entity';
import {
  EmailTemplate,
  EmailTranslation,
} from 'src/domain/mail-template/entity/email-template.entity';
import {
  EmailCampaign,
  EmailSendHistory,
} from 'src/domain/mail-template/entity/email-campaign.entity';
import {
  TranslationKey,
  TranslationValue,
} from 'src/domain/translation/entity/translation.entity';
import { Discount } from 'src/domain/entities/discount.entity';
import { DiscountCountry } from 'src/domain/entities/discount-country.entity';
import { DiscountUsage } from 'src/domain/entities/discount-usage.entity';
import {
  FortunateWheel,
  FortunateWheelUsage,
} from 'src/domain/entities/fortunate-wheel.entity';
import {
  Slider,
  SliderCountry,
  SliderMedia,
} from 'src/domain/media-slider/entity/media-slider.entity';
import { AffiliateProfile } from 'src/domain/affiliate/entities/affiliate-profile.entity';
import { AffiliateDiscount } from 'src/domain/affiliate/entities/affiliate-discount.entity';
import { AffiliateClick } from 'src/domain/affiliate/entities/affiliate-click.entity';
import { AffiliateCommission } from 'src/domain/affiliate/entities/affiliate-commission.entity';
import { AffiliateConversion } from 'src/domain/affiliate/entities/affiliate-conversion.entity';
import { AffiliateDiscountUsage } from 'src/domain/affiliate/entities/affiliate-discount-usage.entity';
import { AddressBilling } from 'src/domain/address-billing/entity/address-billing.entity';
import { AddressShipping } from 'src/domain/address-shipping/entity/address-shipping.entity';
@Injectable()
export class TypeormConfigService implements TypeOrmOptionsFactory {
  constructor(private configService: ConfigService) {}

  private async reconnect(connection) {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const databaseConfig = this.configService.get(
      'database',
    ) as IDatabaseConfig;

    try {
      await connection.connect();
      console.log('Reconnected to the database.');
    } catch (error) {
      console.log('Reconnection failed:', error);
    }
  }

  createTypeOrmOptions(): TypeOrmModuleOptions {
    const databaseConfig = this.configService.get(
      'database',
    ) as IDatabaseConfig;

    return {
      type: databaseConfig.type,
      host: databaseConfig.host,
      port: databaseConfig.port,
      username: databaseConfig.username,
      password: databaseConfig.password,
      database: databaseConfig.database,
      canRetry: true,
      ssl:
        databaseConfig.dbSSL === 'true' ? { rejectUnauthorized: false } : false,
      entities: [
        Address,
        AddressShipping,
        AddressBilling,
        Attribute,
        AttributeValue,
        Login,
        UserType,
        User,
        Country,
        Currency,
        ProductFeed,
        UniversalExport,
        FieldMapping,
        ProductCollection,
        ProductCollectionPivot,
        City,
        Brand,
        Coupon,
        CouponMeta,
        Category,
        EventActivity,
        EventActivityMeta,
        EventActivityHistory,
        Product,
        SuggestedProduct,
        ProductLocalization,
        ProductMeta,
        ProductVariant,
        ProductOrder,
        ProductOrderDetails,
        ImageGallery,
        Wishlist,
        // ImageGallaryMeta,
        State,
        ProductRefundType,
        ProductRefund,
        Payment,
        QuickLinks,
        CourierServices,
        NewsSubscription,
        SubscriptionPopup,
        SetupHome,
        Faq,
        ProductTags,
        ProductTagsMeta,
        SetupHomePartnerInstagram,
        Setup,
        SeoMeta,
        CourierHistory,
        Courier,
        Seller,
        SizeChart,
        SizeChartValue,
        Cart,
        CartItem,
        CartOtp,
        ProductRelated,
        Role,
        Permission,
        CouponUsage,
        CategoryProductRelated,
        Slider,
        SliderMedia,
        SliderCountry,
        EmailTemplate,
        EmailTranslation,
        EmailCampaign,
        EmailSendHistory,
        TranslationKey,
        TranslationValue,
        Discount,
        DiscountCountry,
        DiscountUsage,
        FortunateWheel,
        FortunateWheelUsage,
        BlogPost,
        BlogCategory,
        BlogTag,
        BlogAuthor,
        BlogComment,
        BlogPostProduct,
        BlogPostLocalization,
        Discount,
        DiscountCountry,
        DiscountUsage,
        FortunateWheel,
        FortunateWheelUsage,
        AffiliateProfile,
        AffiliateDiscount,
        AffiliateClick,
        AffiliateCommission,
        AffiliateConversion,
        AffiliateDiscountUsage,
      ], // All the entities that you create should be added here
      synchronize: false,
      logging: process.env.NODE_ENV == 'production', // Disable logs in production
      logger:
        process.env.NODE_ENV == 'production'
          ? new CustomTypeOrmLogger()
          : 'advanced-console',
      maxQueryExecutionTime: 300,
      keepConnectionAlive: true,
      extra: {
        idleTimeoutMillis: 10000, // 10 seconds
        keepAlive: true,
        max: 10,
        connectionTimeoutMillis: 5000,
      },
      beforeQuery: async (queryRunner) => {
        if (!queryRunner.connection.isConnected) {
          console.log('Connection lost, reconnecting...');
          await this.reconnect(queryRunner.connection);
        }
      },
    } as TypeOrmModuleOptions;
  }
}
