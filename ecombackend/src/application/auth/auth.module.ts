import { MiddlewareConsumer, Module, RequestMethod } from '@nestjs/common';
import { AuthService } from './auth.service';
import { AuthController } from './auth.controller';
import { UserModule } from 'src/domain/user/user.module';
import { TokenManagementModule } from 'src/common/providers/token-management/token-management.module';
import { JwtModule } from '@nestjs/jwt';
import { JwtTokenVerifierMiddleware } from 'src/common/middlewares/jwt-token-verifier.middleware';
import { LoginModule } from 'src/domain/login/login.module';
import { PasswordHasherMiddleware } from 'src/common/middlewares/password-hasher.middleware';
import { MailService } from 'src/common/services/mail.service';
import { JwtStrategy } from './strategies/jwt.strategy';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { CacheModule } from '@nestjs/cache-manager';
import * as fsStore from 'cache-manager-fs-hash';

@Module({
  imports: [
    UserModule,
    LoginModule,
    // PermissionsModule,
    // RoleModule,
    TokenManagementModule,
    JwtModule.register({
      global: true,
      secret: 'secret@1234', //process.env.JWT_SECRET as string
      signOptions: { expiresIn: '1d' },
    }),
    CacheModule.register({
      store: fsStore, // File system cache store
      options: {
        path: './cache', // Ensure this directory exists
        ttl: 3600000, // 1 hour
        maxsize: **********, // 1GB
        zip: true, // Compress cache
        preventfill: false, // Ensure cache is preloaded on start
      },
    }),
  ],
  providers: [MailService, AuthService],
  controllers: [AuthController],
  exports: [AuthService],
})
export class AuthModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(JwtTokenVerifierMiddleware, PasswordHasherMiddleware).forRoutes({ path: 'auth/admin/register', method: RequestMethod.POST });
    consumer.apply(JwtTokenVerifierMiddleware).forRoutes({ path: 'auth/admin/changePassword/:id', method: RequestMethod.PUT });
    consumer
      .apply(JwtTokenVerifierMiddleware)
      .exclude({ path: 'auth/login', method: RequestMethod.POST })
      .exclude({ path: 'auth/register', method: RequestMethod.POST })
      .forRoutes('auth/logout')
      .apply(PasswordHasherMiddleware)
      .forRoutes({ path: 'auth/register', method: RequestMethod.POST });
  }
}
