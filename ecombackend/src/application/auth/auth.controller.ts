import {
  Body,
  Controller,
  Param,
  Patch,
  Post,
  Put,
  Query,
  Request,
  UseGuards,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { AuthService } from '../../application/auth/auth.service';
import { JwtUtil } from '../../common/utils/jwt-util';
import { Request as R } from 'express';
import { CreateUserDto } from 'src/domain/user/dto/create-user.dto';
import { ResetPasswordDto } from './dto/password-reset.dto';
import { JwtPayload } from './dto/jwt-payload.interface';
import { LoginDto } from 'src/domain/login/dto/login.dto';
import { ServiceResponse } from 'src/common/utils/service-response';
import { UserChangePasswordDto } from 'src/domain/user/dto/user-change-password.dto';
import { UserForgetPasswordDto } from 'src/domain/user/dto/user-forget-password.dto';
import { CurrentDomain } from 'src/domain/country/current-domain.decorator';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
interface CustomRequest extends R {
  jwtPayload?: JwtPayload;
}

@Controller('auth')
@UsePipes(new ValidationPipe())
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('login')
  async login(@Body() body: LoginDto) {
    return this.authService.login(body.email, body.password, body.userTypeId);
  }

  @Post('register')
  async singUp(
    @CurrentDomain() domain: string,
    @Body() createUserDto: CreateUserDto,
  ) {
    return this.authService.registerCustomer(createUserDto, domain);
  }

  @Post('admin/register')
  async adminRegistration(
    @CurrentDomain() domain: string,
    @Body() createUserDto: CreateUserDto,
  ) {
    return this.authService.registerCustomer(createUserDto, domain);
  }

  @Put('admin/changePassword/:id')
  async adminChangePassword(
    @Param('id') id: number,
    @Body() changePassDto: UserChangePasswordDto,
  ) {
    return this.authService.changePassword(id, changePassDto);
  }
  @Put('changePassword/:id')
  async changePassword(
    @Param('id') id: number,
    @Body() changePassDto: UserChangePasswordDto,
  ) {
    return this.authService.changePassword(id, changePassDto);
  }

  @Post('logout')
  logout(@Request() request: CustomRequest) {
    const token = JwtUtil.extractTokenFromHeader(request) as string;
    return this.authService.logout(token);
  }

  @Post('account-verification-link')
  sendAccountVerificationLinkToEmail(
    @Body() email: string,
  ): Promise<{ message: string }> {
    return this.authService.sendAccountVerificationLinkToEmail(email);
  }

  @Patch('verify-account')
  verifyUserAccount(
    @Query('verify_token') verifyToken: string,
  ): Promise<ServiceResponse> {
    return this.authService.verifyUserAccount(verifyToken);
  }

  @Post('reset-password-link')
  sendResetPasswordLinkToEmail(
    @CurrentDomain() domain: string,
    @Body() resetPasswordDto: ResetPasswordDto,
  ): Promise<ServiceResponse> {
    return this.authService.sendResetPasswordLinkToEmail(
      resetPasswordDto,
      domain,
    );
  }

  @Post('reset-password')
  resetUserPassword(
    @Body() resetPassDto: UserForgetPasswordDto,
  ): Promise<ServiceResponse> {
    return this.authService.resetUserPassword(resetPassDto);
  }

  @Post('test')
  async test(): Promise<string> {
    return 'check string';
  }

  @UseGuards(JwtAuthGuard)
  @Post('cache/clear')
  async clearCache(): Promise<ServiceResponse> {
    return this.authService.clearCache();
  }
}
