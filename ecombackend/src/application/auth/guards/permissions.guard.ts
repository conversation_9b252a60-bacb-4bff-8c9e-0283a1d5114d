import { Injectable, CanActivate, ExecutionContext, ForbiddenException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { PERMISSIONS_KEY } from 'src/common/decorators/permissions.decorator';
import { RolesService } from 'src/domain/roles/roles.service';

@Injectable()
export class PermissionsGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private rolesService: RolesService
  ) { }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const requiredPermissions = this.reflector.get<string[]>(PERMISSIONS_KEY, context.getHandler());
    if (!requiredPermissions) return true;
    
    const { user } = context.switchToHttp().getRequest();
    const roleInfo = await this.rolesService.findOneByIdName(user?.user?.role?.id, user?.user?.role?.name);    
    const userPermissions = roleInfo?.permissions?.map(p => `${p.module}:${p.action}`);

    const hasPermission = requiredPermissions.some((permission) =>
      userPermissions?.includes(permission),
    );

    if (!hasPermission) throw new ForbiddenException('Insufficient permissions');

    return true;
  }
}
