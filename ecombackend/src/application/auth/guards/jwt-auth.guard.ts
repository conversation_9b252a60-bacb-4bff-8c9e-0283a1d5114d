import {
  CanActivate, ExecutionContext, Injectable, UnauthorizedException,
  ForbiddenException,
} from '@nestjs/common';
import { AuthService } from '../auth.service';
import { JwtPayload } from '../dto/jwt-payload.interface';

@Injectable()
export class JwtAuthGuard implements CanActivate {
  constructor(private readonly authService: AuthService) { }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    try {
      const request = context.switchToHttp().getRequest();
      const { authorization }: any = request.headers;

      if (!authorization || authorization.trim() === '') {
        throw new UnauthorizedException('Please provide token');
      }
      
      const authToken = authorization.replace(/bearer/gim, '').trim();
      const payload: JwtPayload = await this.authService.validateToken(authToken);
      request.jwtPayload = payload; // Add the JWT payload to the request object
      request.user = payload;

      request.jwtPayload;

      if (!request.body.id && request.method === 'POST') {
        request.body.createdBy = request.jwtPayload?.sub;
        // request.body.createdAt = Date.now();
      }
      else if (
        // request.body.id ||
        (request.method === 'POST' || request.method === 'PUT' || request.method === 'PATCH')
      ) {
        request.body.updatedBy = request.jwtPayload?.sub;
        // request.body.updatedAt = Date.now();
      }
      else if (request.method === 'DELETE') {
        request.body.updatedBy = request.jwtPayload?.sub;
      }

      return true;
    } catch (error) {
      console.log('JwtAuthGuard error:::', error.message);
      throw new ForbiddenException(error.message || 'session expired! Please sign In');
    }
  }
}