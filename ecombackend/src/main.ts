import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { configDotenv } from 'dotenv';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { IAppConfig, appConfig } from './config/app-config';
import { TypeORMExceptionFilter } from './common/filters/typeorm-exception.filter';
import { HttpExceptionFilter } from './common/filters/http-exception.filter';
import { TransformInterceptor } from './common/interceptors/transform.interceptor';
import { ValidationError, useContainer } from 'class-validator';
import { HttpException, UnauthorizedException, ValidationPipe } from '@nestjs/common';
import { GlobalExceptionFilter } from './common/filters/global-exception-filter';
import { urlencoded, json } from 'express';
import compression from 'compression';
import helmet from 'helmet';
import { NotFoundExceptionFilter } from './filters/not-found-exception.filter';
// import rateLimit from 'express-rate-limit';
// import * as csurf from 'csurf';
import * as Sentry from '@sentry/node';
import { SentryExceptionFilter } from './filters/sentry-exception.filter';
import * as swaggerStats from 'swagger-stats';
import * as express from 'express';
import * as fs from 'fs';
import * as path from 'path';
import cookieParser from 'cookie-parser';

configDotenv({
  path: `.env`,
});

async function bootstrap() {
  const app = await NestFactory.create(AppModule, {
    forceCloseConnections: false,
  });

  app.use(cookieParser());

  // Enable Swagger Stats Middleware
  app.use(
    swaggerStats.getMiddleware({
      uriPath: '/swagger-stats', // Swagger Stats dashboard URL
      swaggerSpec: '/swagger-json', // Swagger JSON endpoint
      authentication: process.env.NODE_ENV === 'production', // Set to true if authentication is needed
      metricsPrefix: 'ss_', // Prefix for metrics
      durationBuckets: [0.1, 0.5, 1, 5, 10], // Response time tracking
      requestSizeBuckets: [128, 512, 1024, 5120, 10000], // Request size tracking
      responseSizeBuckets: [128, 512, 1024, 5120, 10000], // Response size tracking
      onResponseFinish: (req, res, metrics) => {
        if (metrics.responsetime > 500 || metrics?.http?.response?.code >= 400) {
          const newDate = new Date().toISOString().split('T')[0];
          const logData = {
            timestamp: new Date().toISOString(),
            status: res.statusCode,
            responseTime: metrics.responsetime,
            real_ip: req.headers['x-forwarded-for'] || req?.connection?.remoteAddress || req?.socket?.remoteAddress || req?.ip || metrics?.real_ip || 'unknown',
            method: req.method,
            path: req.originalUrl,
            node: metrics?.http?.request?.headers['user-agent'] || 'unknown',
            // agent: JSON.stringify(metrics?.node) || 'unknown',
          };
          fs.appendFileSync(`./logs/${newDate}_logs.txt`, JSON.stringify(logData) + '\n');
        }
      },
      onAuthenticate: function (req, username, password) {
        // simple check for username and password
        return ((username === 'swagger_stats_admin')
          && (password === 'swagger_stats_admin@123'));
      }
    }),
  );

  Sentry.init({
    dsn: process.env.SENTRY_URL,
    beforeSend(event) {
      // if (event?.exception?.values?.[0]?.type === HttpException.name) {
      //   return null;
      // }

      if (event?.exception?.values?.[0]?.type === UnauthorizedException.name) {
        return null;
      }

      return event;
    },
    tracesSampleRate: 1.0,
  });

  const config: IAppConfig = app.get<IAppConfig>(appConfig.KEY);
  const options = new DocumentBuilder()
    .setTitle('Pantoneclo Ecommerce API')
    .setDescription('Pantoneclo Ecommerce API Description')
    .setVersion('1.0')
    .addServer('api') // Add 'api' as a prefix for Swagger
    .addBearerAuth()
    .build();

  const document = SwaggerModule.createDocument(app, options);
  SwaggerModule.setup('api', app, document, {
    swaggerOptions: { basePath: 'api' },
    swaggerUiEnabled: process.env.NODE_ENV == 'production',
    // raw: ['json'], // JSON API definition is still accessible (YAML is disabled)
  });
  app.use(json({ limit: '50mb' }));
  app.use(urlencoded({ extended: true, limit: '50mb' }));

  app.use(helmet());
  app.use(compression());

  if (process.env.NODE_ENV === 'production') {
    // app.use(csurf());
    // app.use(
    //   rateLimit({
    //     windowMs: 15 * 60 * 1000, // 15 minutes
    //     max: 100, // Limit each IP to 100 requests per windowMs
    //   }),
    // );
  }

  app.enableShutdownHooks();

  // SwaggerModule.setup('swagger', app, document, { swaggerOptions: { basePath: 'api' } });
  // Disable CORS

  // Read allowed domains from environment variable and split into an array
  const allowedDomains = process.env.ALLOWED_DOMAINS
    ? process.env.ALLOWED_DOMAINS.split(',').map((domain) => domain.trim())
    : '*';

  app.enableCors({
    origin: allowedDomains, // Allows all origins
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
    credentials: true,
  });

  app.useGlobalFilters(
    new HttpExceptionFilter(),
    new GlobalExceptionFilter(),
    // new ValidationExceptionFilter(),
  );
  app.useGlobalInterceptors(new TransformInterceptor());
  useContainer(app.select(AppModule), { fallbackOnErrors: true });
  // app.useGlobalFilters(new SentryExceptionFilter());
  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      // exceptionFactory: (errors: ValidationError[]) => {
      //   console.log('error', errors);
      //   return errors.map(error => ({
      //     field: error.property,
      //     constraints: Object.values(error.constraints),
      //   }));
      // },
      exceptionFactory: (errors: ValidationError[]) => errors,
    }),
  );

  app.setGlobalPrefix('api');

  app.getHttpAdapter().get('/', (req, res) => {
    res.json({
      status: 200,
      message: 'Pantoneclo up and running',
      error: null,
    });
  });

  app.getHttpAdapter().get('/error-test', (req, res) => {
    throw new Error('My first Sentry error!');
  });

  app.useGlobalFilters(new NotFoundExceptionFilter());

  await app.listen(config.port, () => {
    console.log(`Server is listening on port ${config.port}`);
  });
}
bootstrap();
