{"sqltools.connections": [{"previewLimit": 50, "server": "localhost", "port": 5432, "driver": "PostgreSQL", "name": "pantoneclo", "database": "new_pantoneclo", "username": "postgres", "password": "sajib72542"}, {"pgOptions": {"ssl": {"rejectUnauthorized": false, "requestCert": false}}, "previewLimit": 50, "server": "pantoneclodbs.clium4q8isx2.us-east-1.rds.amazonaws.com", "port": 5432, "driver": "PostgreSQL", "name": "Pantoneclo_live", "username": "postgresadmin", "database": "pantoneclo_dev", "password": "MyPant0necl02024"}]}