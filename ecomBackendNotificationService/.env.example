PORT=4001
NODE_ENV=production
DB_TYPE=postgres
DB_HOST=localhost   # ***************
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=admin
DB_DATABASE=ecom_notification_db_local
DB_SSL=

JWT_SECRET=secret@1234
JWT_EXPIRES_IN=1d

AWS_REGION=auto
AWS_S3_ACCESS_KEY=LE6tGX0wpPnDmcpbfEx5
AWS_S3_KEY_SECRET=5TYcG6aa8xQF6pnNvseJJzGQPrHTd2OGvsMvGe1T
AWS_S3_BUCKET=pantoneclo
AWS_S3_CUSTOM_END_POINT=storage.aicart.store


STRIPE_SECRET_KEY=
STRIPE_PUBLIC_KEY=

# <EMAIL> ssl sandbox
SSL_STORE_ID=
SSL_STORE_PASS=

EMAIL_USERNAME=
EMAIL_PASSWORD=

FRONTEND_URL=http://**************:3000 # http://localhost:3001
ADMIN_URL=http://localhost:3002

# DEFAULT_DOMAIN=pantoneclo.si

#FRONTEND_URL=
#ALLOWED_DOMAINS=

# SES_SMTP_USERNAME=<EMAIL>
# SES_SMTP_PASSWORD='ciev pqmm stqq prsf'
# SES_FROM_MAIL=<EMAIL>
# SES_HOST=smtp.gmail.com
# SES_PORT=465
# SES_SMTP_SECURE=true
# SMTP_SERVICE=gmail

MATRIX_NOTIFICATION_MAIL=<EMAIL>

REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_USERNAME=
REDIS_PASSWORD=
REDIS_DATABASE=0
BULL_REGISTER_EMAIL_QUEUE=mail-queue

BECOME_SELLER_TO_MAIL=<EMAIL>
ECOMMERCE_API_HOST=http://localhost:4000/api