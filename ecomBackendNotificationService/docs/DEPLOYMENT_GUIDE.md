# Campaign Bulk Mail System - Deployment Guide

## 🚀 Production Deployment

### Prerequisites

#### System Requirements
- **Node.js**: 18.x or higher
- **PostgreSQL**: 12.x or higher
- **Redis**: 6.x or higher
- **Memory**: Minimum 2GB RAM (4GB+ recommended)
- **Storage**: 10GB+ available space
- **Network**: Outbound SMTP access (port 587/465)

#### AWS Services (Recommended)
- **EC2**: Application hosting
- **RDS PostgreSQL**: Database
- **ElastiCache Redis**: Queue management
- **SES**: Email sending service
- **S3**: Report storage
- **CloudWatch**: Monitoring and logs

### Environment Configuration

#### Production Environment Variables
```env
# Application
NODE_ENV=production
PORT=3000
API_PREFIX=/api/v1

# Database
DB_HOST=your-rds-endpoint.amazonaws.com
DB_PORT=5432
DB_USERNAME=your-db-username
DB_PASSWORD=your-secure-password
DB_DATABASE=notification_service
DB_SSL=true
DB_POOL_SIZE=20

# Redis (ElastiCache)
REDIS_HOST=your-elasticache-endpoint.cache.amazonaws.com
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password
REDIS_TLS=true
REDIS_CLUSTER_MODE=false

# AWS SES Configuration
SES_REGION=us-east-1
SES_ACCESS_KEY=your-ses-access-key
SES_SECRET_KEY=your-ses-secret-key
SES_FROM_MAIL=<EMAIL>
SES_FROM_NAME=Your Store Name
SES_REPLY_TO=<EMAIL>

# Campaign Settings
CAMPAIGN_DEFAULT_THROTTLE_RATE=100
CAMPAIGN_MAX_RETRIES=3
CAMPAIGN_QUEUE_CONCURRENCY=5
CAMPAIGN_BATCH_SIZE=1000

# Security
JWT_SECRET=your-super-secure-jwt-secret
API_KEY=your-api-key
CORS_ORIGINS=https://yourstore.com,https://admin.yourstore.com

# Monitoring
LOG_LEVEL=info
ENABLE_METRICS=true
SENTRY_DSN=your-sentry-dsn

# File Storage
UPLOAD_PATH=/tmp/uploads
EXPORT_PATH=/tmp/exports
S3_BUCKET=your-reports-bucket
S3_REGION=us-east-1

# Rate Limiting
RATE_LIMIT_WINDOW=60000
RATE_LIMIT_MAX_REQUESTS=100
```

### Docker Deployment

#### Dockerfile
```dockerfile
FROM node:18-alpine

WORKDIR /app

# Install dependencies
COPY package*.json ./
RUN npm ci --only=production

# Copy application code
COPY . .

# Build application
RUN npm run build

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nestjs -u 1001

# Set permissions
RUN chown -R nestjs:nodejs /app
USER nestjs

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

# Start application
CMD ["npm", "run", "start:prod"]
```

#### docker-compose.yml
```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
    env_file:
      - .env.production
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/uploads
      - ./exports:/app/exports

  postgres:
    image: postgres:14-alpine
    environment:
      POSTGRES_DB: notification_service
      POSTGRES_USER: ${DB_USERNAME}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    command: redis-server --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
```

### Kubernetes Deployment

#### deployment.yaml
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: campaign-mail-service
  labels:
    app: campaign-mail-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: campaign-mail-service
  template:
    metadata:
      labels:
        app: campaign-mail-service
    spec:
      containers:
      - name: app
        image: your-registry/campaign-mail-service:latest
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          value: "production"
        - name: DB_HOST
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: host
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: password
        - name: REDIS_HOST
          valueFrom:
            secretKeyRef:
              name: redis-secret
              key: host
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: campaign-mail-service
spec:
  selector:
    app: campaign-mail-service
  ports:
  - port: 80
    targetPort: 3000
  type: LoadBalancer
```

### Database Setup

#### Migration in Production
```bash
# Run migrations
npm run migration:run

# Verify tables created
npm run migration:show

# Create indexes for performance
npm run db:create-indexes
```

#### Database Optimization
```sql
-- Create additional indexes for large datasets
CREATE INDEX CONCURRENTLY idx_email_send_history_campaign_status 
ON email_send_history (campaign_id, status);

CREATE INDEX CONCURRENTLY idx_email_group_members_email_status 
ON email_group_members (email, status);

CREATE INDEX CONCURRENTLY idx_campaigns_scheduled_status 
ON email_campaigns (scheduled_at, status) 
WHERE status = 'scheduled';

-- Optimize for analytics queries
CREATE INDEX CONCURRENTLY idx_send_history_analytics 
ON email_send_history (campaign_id, sent_at, opened, clicked);

-- Partition large tables (for high volume)
CREATE TABLE email_send_history_2024 PARTITION OF email_send_history
FOR VALUES FROM ('2024-01-01') TO ('2025-01-01');
```

### Performance Optimization

#### Redis Configuration
```conf
# redis.conf
maxmemory 2gb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000

# Queue-specific settings
timeout 300
tcp-keepalive 300
```

#### Application Tuning
```typescript
// Queue configuration
const queueOptions = {
  defaultJobOptions: {
    attempts: 3,
    backoff: {
      type: 'exponential',
      delay: 2000,
    },
    removeOnComplete: 1000,
    removeOnFail: 500,
  },
  settings: {
    stalledInterval: 30000,
    maxStalledCount: 1,
  },
};

// Database connection pool
const dbConfig = {
  type: 'postgres',
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT),
  username: process.env.DB_USERNAME,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_DATABASE,
  ssl: process.env.NODE_ENV === 'production',
  pool: {
    min: 5,
    max: 20,
    acquire: 30000,
    idle: 10000,
  },
};
```

### Monitoring & Logging

#### Application Monitoring
```typescript
// metrics.service.ts
import { Injectable } from '@nestjs/common';
import { PrometheusService } from '@nestjs/prometheus';

@Injectable()
export class MetricsService {
  constructor(private prometheus: PrometheusService) {}

  // Campaign metrics
  campaignsSent = this.prometheus.createCounter({
    name: 'campaigns_sent_total',
    help: 'Total number of campaigns sent',
    labelNames: ['type', 'status'],
  });

  emailsSent = this.prometheus.createCounter({
    name: 'emails_sent_total',
    help: 'Total number of emails sent',
    labelNames: ['campaign_id', 'status'],
  });

  // Queue metrics
  queueSize = this.prometheus.createGauge({
    name: 'queue_size',
    help: 'Current queue size',
    labelNames: ['queue_name'],
  });
}
```

#### Log Configuration
```typescript
// logger.config.ts
import { WinstonModule } from 'nest-winston';
import * as winston from 'winston';

export const loggerConfig = WinstonModule.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json(),
  ),
  transports: [
    new winston.transports.File({
      filename: 'logs/error.log',
      level: 'error',
    }),
    new winston.transports.File({
      filename: 'logs/combined.log',
    }),
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple(),
      ),
    }),
  ],
});
```

### Security Configuration

#### NGINX Configuration
```nginx
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req zone=api burst=20 nodelay;

    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";

    location / {
        proxy_pass http://app:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # Static files
    location /exports/ {
        alias /app/exports/;
        expires 1d;
        add_header Cache-Control "public, immutable";
    }
}
```

### Backup Strategy

#### Database Backup
```bash
#!/bin/bash
# backup.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups"
DB_NAME="notification_service"

# Create backup
pg_dump -h $DB_HOST -U $DB_USERNAME -d $DB_NAME > $BACKUP_DIR/db_backup_$DATE.sql

# Compress backup
gzip $BACKUP_DIR/db_backup_$DATE.sql

# Upload to S3
aws s3 cp $BACKUP_DIR/db_backup_$DATE.sql.gz s3://your-backup-bucket/database/

# Clean old backups (keep 30 days)
find $BACKUP_DIR -name "db_backup_*.sql.gz" -mtime +30 -delete
```

#### Redis Backup
```bash
#!/bin/bash
# redis-backup.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups"

# Create Redis backup
redis-cli --rdb $BACKUP_DIR/redis_backup_$DATE.rdb

# Upload to S3
aws s3 cp $BACKUP_DIR/redis_backup_$DATE.rdb s3://your-backup-bucket/redis/

# Clean old backups
find $BACKUP_DIR -name "redis_backup_*.rdb" -mtime +7 -delete
```

### Health Checks

#### Application Health Check
```typescript
// health.controller.ts
import { Controller, Get } from '@nestjs/common';
import { HealthCheck, HealthCheckService, TypeOrmHealthIndicator } from '@nestjs/terminus';

@Controller('health')
export class HealthController {
  constructor(
    private health: HealthCheckService,
    private db: TypeOrmHealthIndicator,
  ) {}

  @Get()
  @HealthCheck()
  check() {
    return this.health.check([
      () => this.db.pingCheck('database'),
      () => this.checkRedis(),
      () => this.checkQueues(),
    ]);
  }

  private async checkRedis() {
    // Redis health check implementation
  }

  private async checkQueues() {
    // Queue health check implementation
  }
}
```

### Deployment Checklist

#### Pre-deployment
- [ ] Environment variables configured
- [ ] Database migrations tested
- [ ] Redis connection verified
- [ ] SES credentials validated
- [ ] SSL certificates installed
- [ ] Monitoring setup configured
- [ ] Backup strategy implemented

#### Post-deployment
- [ ] Health checks passing
- [ ] Logs flowing correctly
- [ ] Metrics being collected
- [ ] Queue processing working
- [ ] Email sending functional
- [ ] Performance benchmarks met
- [ ] Security scan completed

### Scaling Considerations

#### Horizontal Scaling
- Use load balancer for multiple app instances
- Implement Redis Cluster for high availability
- Use read replicas for database scaling
- Consider microservices architecture for large scale

#### Vertical Scaling
- Monitor CPU and memory usage
- Optimize database queries
- Tune Redis memory settings
- Adjust queue concurrency settings

---

**Deployment Version**: 1.0.0  
**Last Updated**: January 2024  
**Tested Environments**: AWS, GCP, Azure, Docker, Kubernetes
