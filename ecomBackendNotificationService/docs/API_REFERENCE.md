# Campaign Bulk Mail System - API Reference

## 🔗 Base URL
```
http://localhost:3000
```

## 🔐 Authentication
All endpoints require Basic Authentication:
```bash
Authorization: Basic your-auth-token
```

## 📧 Campaign Management

### Create Campaign
**POST** `/campaign`

Creates a new email campaign.

**Request Body:**
```json
{
  "name": "Summer Sale 2024",
  "description": "Promotional campaign for summer sale",
  "templateId": 1,
  "type": "promotional",
  "priority": "high",
  "emailGroups": [1, 2],
  "customEmails": ["<EMAIL>"],
  "senderName": "Store Team",
  "senderEmail": "<EMAIL>",
  "replyTo": "<EMAIL>",
  "subjectLine": "🌞 Summer Sale - 50% Off!",
  "preheaderText": "Limited time offer",
  "settings": {
    "trackOpens": true,
    "trackClicks": true,
    "throttleRate": 100
  }
}
```

**Response:**
```json
{
  "id": 1,
  "name": "Summer Sale 2024",
  "status": "draft",
  "totalRecipients": 1500,
  "createdAt": "2024-01-15T10:00:00Z"
}
```

### List Campaigns
**GET** `/campaign`

Retrieves all campaigns with optional filtering.

**Query Parameters:**
- `status` (optional): Filter by campaign status
- `type` (optional): Filter by campaign type
- `limit` (optional): Number of results (default: 50)
- `offset` (optional): Pagination offset (default: 0)

**Response:**
```json
{
  "campaigns": [
    {
      "id": 1,
      "name": "Summer Sale 2024",
      "status": "completed",
      "type": "promotional",
      "totalRecipients": 1500,
      "sentCount": 1485,
      "openRate": 28.5,
      "clickRate": 5.2,
      "createdAt": "2024-01-15T10:00:00Z"
    }
  ],
  "total": 1,
  "limit": 50,
  "offset": 0
}
```

### Get Campaign Details
**GET** `/campaign/:id`

Retrieves detailed information about a specific campaign.

**Response:**
```json
{
  "id": 1,
  "name": "Summer Sale 2024",
  "description": "Promotional campaign for summer sale",
  "status": "completed",
  "type": "promotional",
  "priority": "high",
  "totalRecipients": 1500,
  "sentCount": 1485,
  "deliveredCount": 1450,
  "openedCount": 413,
  "clickedCount": 75,
  "bouncedCount": 35,
  "startedAt": "2024-01-15T10:00:00Z",
  "completedAt": "2024-01-15T12:30:00Z",
  "statistics": {
    "deliveryRate": 97.6,
    "openRate": 28.5,
    "clickRate": 5.2,
    "bounceRate": 2.4
  }
}
```

### Update Campaign
**PUT** `/campaign/:id`

Updates a campaign (only allowed for draft campaigns).

**Request Body:** Same as Create Campaign (partial updates allowed)

### Schedule Campaign
**POST** `/campaign/:id/schedule`

Schedules a campaign for sending.

**Request Body:**
```json
{
  "sendImmediately": false,
  "scheduledAt": "2024-01-20T09:00:00Z",
  "notes": "Send Monday morning for better engagement"
}
```

### Send Campaign
**POST** `/campaign/:id/send`

Sends a campaign immediately.

**Response:**
```json
{
  "message": "Campaign queued for sending",
  "campaignId": 1,
  "estimatedSendTime": 15
}
```

### Pause Campaign
**POST** `/campaign/:id/pause`

Pauses an active campaign.

### Resume Campaign
**POST** `/campaign/:id/resume`

Resumes a paused campaign.

### Cancel Campaign
**DELETE** `/campaign/:id`

Cancels a campaign (marks as cancelled).

## 👥 Email Group Management

### Create Email Group
**POST** `/campaign/email-groups`

Creates a new email group.

**Request Body:**
```json
{
  "name": "VIP Customers",
  "description": "High-value customers",
  "type": "static",
  "tags": ["vip", "high-value"],
  "segmentationCriteria": {
    "purchaseHistory": {
      "minAmount": 500,
      "minOrders": 3
    }
  }
}
```

### List Email Groups
**GET** `/campaign/email-groups`

Retrieves all email groups.

**Response:**
```json
{
  "groups": [
    {
      "id": 1,
      "name": "VIP Customers",
      "type": "static",
      "status": "active",
      "memberCount": 250,
      "activeMemberCount": 240,
      "createdAt": "2024-01-10T10:00:00Z"
    }
  ]
}
```

### Get Email Group
**GET** `/campaign/email-groups/:id`

Retrieves detailed information about an email group.

**Query Parameters:**
- `includeMembers` (optional): Include group members in response

### Add Member to Group
**POST** `/campaign/email-groups/:id/members`

Adds a new member to an email group.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "firstName": "John",
  "lastName": "Doe",
  "phone": "+1234567890",
  "countryId": 1,
  "source": "manual",
  "preferences": {
    "frequency": "weekly",
    "language": "en"
  }
}
```

### Import Emails
**POST** `/campaign/email-groups/:id/import`

Imports emails to a group via CSV file or text list.

**Form Data (CSV Upload):**
```bash
curl -X POST http://localhost:3000/campaign/email-groups/1/import \
  -F "file=@emails.csv" \
  -F "source=import" \
  -F "skipDuplicates=true"
```

**JSON Body (Text List):**
```json
{
  "emailList": "<EMAIL>,<EMAIL>\<EMAIL>",
  "source": "import",
  "skipDuplicates": true,
  "validateEmails": true
}
```

**Response:**
```json
{
  "imported": 150,
  "skipped": 5,
  "errors": [
    "invalid@email: Invalid email format",
    "<EMAIL>: Email already exists"
  ]
}
```

### Get Group Members
**GET** `/campaign/email-groups/:id/members`

Retrieves members of an email group.

**Query Parameters:**
- `limit` (optional): Number of members to return (default: 100)
- `offset` (optional): Pagination offset (default: 0)
- `status` (optional): Filter by member status

### Remove Member
**DELETE** `/campaign/email-groups/:groupId/members/:email`

Removes a member from an email group.

### Unsubscribe Member
**POST** `/campaign/email-groups/:groupId/members/:email/unsubscribe`

Unsubscribes a member from an email group.

### Get Group Statistics
**GET** `/campaign/email-groups/:id/stats`

Retrieves statistics for an email group.

**Response:**
```json
{
  "totalMembers": 250,
  "activeMembers": 240,
  "unsubscribedMembers": 8,
  "bouncedMembers": 2,
  "averageOpenRate": 32.5,
  "averageClickRate": 6.8
}
```

## 📊 Analytics & Reporting

### Generate Campaign Report
**POST** `/campaign/:id/reports`

Generates a comprehensive campaign report.

**Request Body:**
```json
{
  "type": "detailed",
  "exportFormat": "excel",
  "userId": 1,
  "filters": {
    "dateRange": {
      "start": "2024-01-01T00:00:00Z",
      "end": "2024-01-31T23:59:59Z"
    }
  }
}
```

**Response:**
```json
{
  "id": 1,
  "type": "detailed",
  "reportDate": "2024-01-15T15:30:00Z",
  "exportUrl": "/exports/campaign-report-1-1705329000.xlsx",
  "expiresAt": "2024-02-14T15:30:00Z"
}
```

### Get Campaign Summary
**GET** `/campaign/:id/summary`

Retrieves campaign summary statistics.

**Response:**
```json
{
  "campaign": {
    "id": 1,
    "name": "Summer Sale 2024",
    "status": "completed"
  },
  "metrics": {
    "totalSent": 1485,
    "delivered": 1450,
    "opened": 413,
    "clicked": 75,
    "bounced": 35,
    "deliveryRate": 97.6,
    "openRate": 28.5,
    "clickRate": 5.2,
    "bounceRate": 2.4,
    "uniqueOpens": 380,
    "uniqueClicks": 68
  }
}
```

### Geographic Report
**GET** `/campaign/:id/geographic-report`

Retrieves geographic performance data.

**Response:**
```json
{
  "geographic": [
    {
      "countryId": 1,
      "countryName": "United States",
      "sent": 800,
      "opened": 240,
      "clicked": 48,
      "openRate": 30.0,
      "clickRate": 6.0
    },
    {
      "countryId": 2,
      "countryName": "Canada",
      "sent": 300,
      "opened": 75,
      "clicked": 12,
      "openRate": 25.0,
      "clickRate": 4.0
    }
  ]
}
```

### Time-based Report
**GET** `/campaign/:id/time-based-report`

Retrieves time-based performance analytics.

**Response:**
```json
{
  "hourlyStats": [
    {
      "hour": 9,
      "opens": 45,
      "uniqueOpens": 42,
      "clicks": 8,
      "uniqueClicks": 7
    },
    {
      "hour": 10,
      "opens": 67,
      "uniqueOpens": 61,
      "clicks": 12,
      "uniqueClicks": 11
    }
  ]
}
```

### Engagement Report
**GET** `/campaign/:id/engagement-report`

Retrieves engagement analysis.

**Response:**
```json
{
  "engagementSegments": {
    "highEngagement": 125,
    "mediumEngagement": 255,
    "lowEngagement": 890,
    "noEngagement": 215
  }
}
```

## 📋 Data Models

### Campaign Types
- `promotional` - Sales and promotional campaigns
- `newsletter` - Regular newsletters
- `transactional` - Order confirmations, receipts
- `abandoned_cart` - Cart abandonment recovery
- `welcome_series` - Welcome email sequences
- `re_engagement` - Win-back campaigns
- `product_announcement` - New product launches
- `seasonal` - Holiday and seasonal campaigns
- `survey` - Customer feedback surveys
- `event_invitation` - Event invitations

### Campaign Status
- `draft` - Campaign being created
- `scheduled` - Scheduled for future sending
- `sending` - Currently being sent
- `sent` - Sending completed
- `paused` - Temporarily paused
- `cancelled` - Cancelled before completion
- `failed` - Failed to send
- `completed` - Successfully completed

### Email Group Types
- `static` - Manually managed groups
- `dynamic` - Auto-updated based on criteria
- `imported` - Created from imports
- `segmented` - Advanced segmentation rules

### Member Status
- `active` - Active subscriber
- `unsubscribed` - Unsubscribed from group
- `bounced` - Email bounced
- `complained` - Marked as spam
- `suppressed` - Suppressed from sending

## ❌ Error Responses

### Standard Error Format
```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid email address format",
    "details": {
      "field": "email",
      "value": "invalid-email"
    }
  },
  "timestamp": "2024-01-15T10:00:00Z",
  "path": "/campaign/email-groups/1/members"
}
```

### Common Error Codes
- `VALIDATION_ERROR` - Request validation failed
- `NOT_FOUND` - Resource not found
- `UNAUTHORIZED` - Authentication required
- `FORBIDDEN` - Insufficient permissions
- `CONFLICT` - Resource conflict (e.g., duplicate email)
- `RATE_LIMIT_EXCEEDED` - Too many requests
- `INTERNAL_ERROR` - Server error

## 🔄 Rate Limits

- **Campaign Creation**: 10 requests/minute
- **Email Import**: 5 requests/minute
- **Report Generation**: 3 requests/minute
- **General API**: 100 requests/minute

Rate limit headers included in responses:
```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1705329000
```

---

**API Version**: 1.0.0  
**Last Updated**: January 2024
