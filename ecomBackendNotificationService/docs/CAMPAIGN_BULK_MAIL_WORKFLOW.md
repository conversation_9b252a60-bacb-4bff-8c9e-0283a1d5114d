# Campaign Bulk Mail System - Complete Workflow Guide

## 📋 Table of Contents

1. [Overview](#overview)
2. [System Architecture](#system-architecture)
3. [Prerequisites Setup](#prerequisites-setup)
4. [Database Setup](#database-setup)
5. [API Endpoints Reference](#api-endpoints-reference)
6. [Complete Workflow](#complete-workflow)
7. [Advanced Features](#advanced-features)
8. [Monitoring & Analytics](#monitoring--analytics)
9. [Best Practices](#best-practices)
10. [Troubleshooting](#troubleshooting)

## 🎯 Overview

The Campaign Bulk Mail System is a comprehensive email marketing solution that provides:

- **Campaign Management**: Create, schedule, and manage email campaigns
- **Email Group Management**: Organize recipients into targeted groups
- **Import System**: Bulk import emails from CSV files or manual entry
- **Advanced Scheduling**: Send immediately or schedule for optimal timing
- **Real-time Analytics**: Track opens, clicks, bounces, and conversions
- **A/B Testing**: Test different subject lines and content variants
- **Compliance Management**: Handle unsubscribes and bounce management

## 🏗️ System Architecture

```mermaid
graph TB
    A[Campaign Controller] --> B[Campaign Service]
    A --> C[Email Group Service]
    A --> D[Campaign Report Service]
    
    B --> E[Campaign Processor]
    E --> F[Email Queue]
    F --> G[Mail Service]
    
    B --> H[Email Groups]
    B --> I[Email Campaigns]
    B --> J[Campaign Reports]
    
    H --> K[Email Group Members]
    I --> L[Email Send History]
    
    M[Redis Queue] --> E
    N[Cron Jobs] --> B
```

### Key Components

- **Campaign Service**: Core campaign management logic
- **Email Group Service**: Manages recipient groups and imports
- **Campaign Processor**: Handles queue processing and email sending
- **Campaign Report Service**: Generates analytics and reports
- **Redis Queues**: Manages email sending queues with throttling
- **Cron Jobs**: Handles scheduled campaigns and cleanup tasks

## 🔧 Prerequisites Setup

### 1. Install Dependencies

```bash
# Core dependencies
npm install @nestjs/bullmq bullmq redis
npm install @nestjs/schedule
npm install csv-parser exceljs
npm install multer @nestjs/platform-express

# Development dependencies
npm install --save-dev @types/multer
```

### 2. Environment Configuration

Create or update your `.env` file:

```env
# Redis Configuration (Required for queues)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# Email Service Configuration
SES_FROM_MAIL=<EMAIL>
SES_REGION=us-east-1
SES_ACCESS_KEY=your-aws-access-key
SES_SECRET_KEY=your-aws-secret-key

# Application Configuration
NODE_ENV=development
PORT=3000

# Database Configuration (your existing config)
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=your-username
DB_PASSWORD=your-password
DB_DATABASE=your-database
```

### 3. Redis Setup

#### Install Redis (macOS)
```bash
brew install redis
brew services start redis
```

#### Install Redis (Ubuntu)
```bash
sudo apt update
sudo apt install redis-server
sudo systemctl start redis-server
sudo systemctl enable redis-server
```

#### Verify Redis Installation
```bash
redis-cli ping
# Should return: PONG
```

## 💾 Database Setup

### 1. Run Migration

```bash
# Generate migration (if needed)
npm run migration:generate -- -n CampaignBulkMailSystem

# Run the migration
npm run migration:run

# Or with TypeORM CLI
npx typeorm migration:run -d src/config/database-config.ts
```

### 2. Verify Database Tables

After running the migration, verify these tables exist:

- `email_groups` - Email recipient groups
- `email_group_members` - Individual group members
- `email_campaigns` - Campaign definitions (enhanced)
- `email_send_history` - Email delivery tracking (enhanced)
- `campaign_reports` - Analytics and reports

### 3. Database Indexes

The migration creates optimized indexes for:
- Email lookups and group membership
- Campaign status and scheduling queries
- Analytics and reporting queries
- Performance optimization for large datasets

## 📡 API Endpoints Reference

### Campaign Management

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/campaign` | Create new campaign |
| GET | `/campaign` | List all campaigns |
| GET | `/campaign/:id` | Get campaign details |
| PUT | `/campaign/:id` | Update campaign (draft only) |
| POST | `/campaign/:id/schedule` | Schedule campaign |
| POST | `/campaign/:id/send` | Send campaign immediately |
| POST | `/campaign/:id/pause` | Pause sending campaign |
| POST | `/campaign/:id/resume` | Resume paused campaign |
| DELETE | `/campaign/:id` | Cancel campaign |

### Email Group Management

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/campaign/email-groups` | Create email group |
| GET | `/campaign/email-groups` | List all groups |
| GET | `/campaign/email-groups/:id` | Get group details |
| POST | `/campaign/email-groups/:id/members` | Add member to group |
| POST | `/campaign/email-groups/:id/import` | Import emails (CSV/text) |
| GET | `/campaign/email-groups/:id/members` | Get group members |
| GET | `/campaign/email-groups/:id/stats` | Get group statistics |
| DELETE | `/campaign/email-groups/:groupId/members/:email` | Remove member |
| POST | `/campaign/email-groups/:groupId/members/:email/unsubscribe` | Unsubscribe member |

### Analytics & Reporting

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/campaign/:id/reports` | Generate campaign report |
| GET | `/campaign/:id/reports` | List campaign reports |
| GET | `/campaign/:id/summary` | Get campaign summary |
| GET | `/campaign/:id/geographic-report` | Geographic performance |
| GET | `/campaign/:id/time-based-report` | Time-based analytics |
| GET | `/campaign/:id/engagement-report` | Engagement analysis |

## 🔄 Complete Workflow

### Step 1: Start Services

```bash
# Start Redis
redis-server

# Start the application
npm run start:dev
```

### Step 2: Create Email Template

```bash
curl -X POST http://localhost:3000/mail-template/email-template \
  -H "Content-Type: application/json" \
  -H "Authorization: Basic your-auth-token" \
  -d '{
    "name": "Welcome Newsletter",
    "subject": "Welcome to Our Store!",
    "htmlContent": "<html><body><h1>Hello {{firstName}}!</h1><p>Welcome to our store. Enjoy 10% off your first purchase!</p><a href=\"https://yourstore.com/shop\">Shop Now</a></body></html>",
    "isActive": true,
    "templateType": "newsletter"
  }'
```

### Step 3: Create Email Groups

#### Static Group
```bash
curl -X POST http://localhost:3000/campaign/email-groups \
  -H "Content-Type: application/json" \
  -H "Authorization: Basic your-auth-token" \
  -d '{
    "name": "Newsletter Subscribers",
    "description": "General newsletter subscribers",
    "type": "static",
    "tags": ["newsletter", "general"]
  }'
```

#### Dynamic Group with Segmentation
```bash
curl -X POST http://localhost:3000/campaign/email-groups \
  -H "Content-Type: application/json" \
  -H "Authorization: Basic your-auth-token" \
  -d '{
    "name": "High-Value Customers",
    "description": "Customers with high purchase value",
    "type": "dynamic",
    "segmentationCriteria": {
      "purchaseHistory": {
        "minAmount": 500,
        "minOrders": 3
      },
      "engagement": {
        "minOpenRate": 25,
        "lastOpenDays": 60
      }
    },
    "autoSyncEnabled": true,
    "syncFrequencyHours": 24
  }'
```

### Step 4: Import Email Addresses

#### Individual Member Addition
```bash
curl -X POST http://localhost:3000/campaign/email-groups/1/members \
  -H "Content-Type: application/json" \
  -H "Authorization: Basic your-auth-token" \
  -d '{
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "countryId": 1,
    "source": "manual",
    "preferences": {
      "frequency": "weekly",
      "language": "en"
    }
  }'
```

#### CSV Import
Create `subscribers.csv`:
```csv
email,firstName,lastName,phone,countryId
<EMAIL>,Alice,Smith,+1234567890,1
<EMAIL>,Bob,Johnson,+1234567891,1
<EMAIL>,Carol,Williams,+1234567892,1
```

```bash
curl -X POST http://localhost:3000/campaign/email-groups/1/import \
  -H "Authorization: Basic your-auth-token" \
  -F "file=@subscribers.csv" \
  -F "source=import" \
  -F "skipDuplicates=true" \
  -F "validateEmails=true"
```

#### Bulk Text Import
```bash
curl -X POST http://localhost:3000/campaign/email-groups/1/import \
  -H "Content-Type: application/json" \
  -H "Authorization: Basic your-auth-token" \
  -d '{
    "emailList": "<EMAIL>,<EMAIL>\<EMAIL>,<EMAIL>",
    "source": "import",
    "skipDuplicates": true,
    "tags": ["bulk-import", "2024-batch"]
  }'
```

### Step 5: Create Campaign

#### Basic Campaign
```bash
curl -X POST http://localhost:3000/campaign \
  -H "Content-Type: application/json" \
  -H "Authorization: Basic your-auth-token" \
  -d '{
    "name": "Monthly Newsletter - January 2024",
    "description": "Monthly newsletter with latest updates and offers",
    "templateId": 1,
    "type": "newsletter",
    "priority": "medium",
    "emailGroups": [1],
    "customEmails": ["<EMAIL>"],
    "senderName": "Your Store Team",
    "senderEmail": "<EMAIL>",
    "replyTo": "<EMAIL>",
    "subjectLine": "Your January Newsletter is Here! 📧",
    "preheaderText": "New products, exclusive offers, and more inside",
    "settings": {
      "trackOpens": true,
      "trackClicks": true,
      "enableUnsubscribe": true,
      "throttleRate": 100,
      "retryFailures": true,
      "maxRetries": 3
    }
  }'
```

#### Campaign with A/B Testing
```bash
curl -X POST http://localhost:3000/campaign \
  -H "Content-Type: application/json" \
  -H "Authorization: Basic your-auth-token" \
  -d '{
    "name": "Product Launch A/B Test",
    "templateId": 1,
    "type": "product_announcement",
    "priority": "high",
    "emailGroups": [1, 2],
    "subjectLine": "Introducing Our New Product Line",
    "abTestSettings": {
      "enabled": true,
      "subjectVariants": [
        "Introducing Our New Product Line",
        "🚀 New Products Just Launched!",
        "You Asked, We Delivered: New Products Here"
      ],
      "splitPercentage": 33,
      "winnerCriteria": "open_rate"
    },
    "settings": {
      "throttleRate": 150
    }
  }'
```

### Step 6: Schedule and Send

#### Send Immediately
```bash
curl -X POST http://localhost:3000/campaign/1/send \
  -H "Authorization: Basic your-auth-token"
```

#### Schedule for Later
```bash
curl -X POST http://localhost:3000/campaign/1/schedule \
  -H "Content-Type: application/json" \
  -H "Authorization: Basic your-auth-token" \
  -d '{
    "sendImmediately": false,
    "scheduledAt": "2024-01-15T09:00:00Z",
    "notes": "Send Monday morning for better engagement"
  }'
```

### Step 7: Monitor Campaign

#### Check Campaign Status
```bash
curl -X GET http://localhost:3000/campaign/1 \
  -H "Authorization: Basic your-auth-token"
```

#### Get Real-time Statistics
```bash
curl -X GET http://localhost:3000/campaign/1/summary \
  -H "Authorization: Basic your-auth-token"
```

### Step 8: Generate Reports

#### Detailed Performance Report
```bash
curl -X POST http://localhost:3000/campaign/1/reports \
  -H "Content-Type: application/json" \
  -H "Authorization: Basic your-auth-token" \
  -d '{
    "type": "detailed",
    "exportFormat": "excel",
    "userId": 1,
    "expirationDays": 30
  }'
```

#### Geographic Analysis
```bash
curl -X GET http://localhost:3000/campaign/1/geographic-report \
  -H "Authorization: Basic your-auth-token"
```

#### Time-based Performance
```bash
curl -X GET http://localhost:3000/campaign/1/time-based-report \
  -H "Authorization: Basic your-auth-token"
```

## 🚀 Advanced Features

### 1. Dynamic Segmentation

Create smart groups that automatically update based on customer behavior:

```json
{
  "name": "Re-engagement Targets",
  "type": "dynamic",
  "segmentationCriteria": {
    "engagement": {
      "lastOpenDays": 90,
      "maxOpenRate": 10
    },
    "purchaseHistory": {
      "lastPurchaseDays": 180
    }
  },
  "autoSyncEnabled": true,
  "syncFrequencyHours": 12
}
```

### 2. Campaign Automation

Set up automated campaigns triggered by customer actions:

```json
{
  "name": "Welcome Series - Email 1",
  "type": "welcome_series",
  "trigger": {
    "event": "user_signup",
    "delay": 0
  },
  "settings": {
    "autoSend": true,
    "followUpCampaigns": [2, 3, 4]
  }
}
```

### 3. Advanced A/B Testing

Test multiple elements simultaneously:

```json
{
  "abTestSettings": {
    "enabled": true,
    "subjectVariants": ["Subject A", "Subject B", "Subject C"],
    "contentVariants": ["content_v1", "content_v2"],
    "senderVariants": ["Team Name", "Personal Name"],
    "splitPercentage": 25,
    "winnerCriteria": "click_rate",
    "testDuration": 24
  }
}
```

## 📊 Monitoring & Analytics

### Key Metrics Tracked

1. **Delivery Metrics**
   - Total sent
   - Delivered
   - Bounced
   - Failed

2. **Engagement Metrics**
   - Open rate
   - Click rate
   - Unsubscribe rate
   - Complaint rate

3. **Performance Metrics**
   - Best performing time
   - Geographic performance
   - Device/client analysis
   - Revenue attribution

### Real-time Dashboard Data

```bash
# Get comprehensive campaign statistics
curl -X GET http://localhost:3000/campaign/1/summary
```

Response example:
```json
{
  "campaign": {
    "id": 1,
    "name": "Monthly Newsletter",
    "status": "completed",
    "startedAt": "2024-01-15T09:00:00Z",
    "completedAt": "2024-01-15T11:30:00Z"
  },
  "metrics": {
    "totalSent": 10000,
    "delivered": 9850,
    "opened": 2955,
    "clicked": 590,
    "bounced": 150,
    "complaints": 5,
    "deliveryRate": 98.5,
    "openRate": 30.0,
    "clickRate": 6.0,
    "bounceRate": 1.5,
    "complaintRate": 0.05
  }
}
```

## ✅ Best Practices

### 1. Email List Management

- **Regular Cleaning**: Remove bounced and inactive emails monthly
- **Segmentation**: Create targeted groups for better engagement
- **Permission-based**: Only email subscribers who opted in
- **Unsubscribe Handling**: Process unsubscribes immediately

### 2. Campaign Optimization

- **Subject Line Testing**: A/B test subject lines for better open rates
- **Send Time Optimization**: Test different send times for your audience
- **Content Personalization**: Use merge tags for personalized content
- **Mobile Optimization**: Ensure emails render well on mobile devices

### 3. Compliance & Deliverability

- **CAN-SPAM Compliance**: Include unsubscribe links and sender information
- **GDPR Compliance**: Respect data protection regulations
- **Sender Reputation**: Monitor bounce and complaint rates
- **Authentication**: Set up SPF, DKIM, and DMARC records

### 4. Performance Monitoring

- **Regular Reporting**: Generate weekly/monthly performance reports
- **Trend Analysis**: Monitor engagement trends over time
- **Benchmark Comparison**: Compare against industry standards
- **Continuous Improvement**: Iterate based on performance data

## 🔧 Troubleshooting

### Common Issues and Solutions

#### 1. Emails Not Sending

**Problem**: Campaign stuck in "sending" status

**Solutions**:
```bash
# Check Redis connection
redis-cli ping

# Check queue status
curl -X GET http://localhost:3000/admin/queues/campaign-queue

# Restart queue processing
npm run queue:restart
```

#### 2. High Bounce Rate

**Problem**: Bounce rate > 5%

**Solutions**:
- Validate email addresses before import
- Clean email lists regularly
- Check sender reputation
- Review email content for spam triggers

#### 3. Low Open Rates

**Problem**: Open rate < 15%

**Solutions**:
- A/B test subject lines
- Optimize send times
- Improve sender name recognition
- Clean inactive subscribers

#### 4. Import Failures

**Problem**: CSV import failing

**Solutions**:
```bash
# Check CSV format
head -5 your-file.csv

# Validate required columns: email, firstName, lastName
# Ensure UTF-8 encoding
# Check file size limits
```

#### 5. Queue Processing Issues

**Problem**: Emails stuck in queue

**Solutions**:
```bash
# Check Redis memory usage
redis-cli info memory

# Clear failed jobs
redis-cli FLUSHDB

# Restart application
npm run start:dev
```

### Debug Commands

```bash
# Check campaign status
curl -X GET http://localhost:3000/campaign/1

# View queue statistics
curl -X GET http://localhost:3000/admin/queues/stats

# Check email group member count
curl -X GET http://localhost:3000/campaign/email-groups/1/stats

# View recent send history
curl -X GET http://localhost:3000/admin/send-history?limit=10
```

## 📞 Support

For additional support:

1. **Documentation**: Check this guide and API documentation
2. **Logs**: Review application logs for detailed error information
3. **Monitoring**: Use the built-in analytics dashboard
4. **Community**: Join our developer community for help

---

**Last Updated**: January 2024  
**Version**: 1.0.0  
**Compatibility**: Node.js 18+, PostgreSQL 12+, Redis 6+
