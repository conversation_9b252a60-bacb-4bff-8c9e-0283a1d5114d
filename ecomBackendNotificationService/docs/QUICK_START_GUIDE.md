# Campaign Bulk Mail System - Quick Start Guide

## 🚀 5-Minute Setup

### 1. Prerequisites Check
```bash
# Check Node.js version (requires 18+)
node --version

# Check Redis installation
redis-cli ping
# Should return: PONG

# Check PostgreSQL connection
psql -h localhost -U your-username -d your-database -c "SELECT version();"
```

### 2. Install & Setup
```bash
# Install dependencies
npm install

# Run database migration
npm run migration:run

# Start Redis (if not running)
redis-server

# Start application
npm run start:dev
```

### 3. Verify Installation
```bash
# Test API health
curl http://localhost:3000/health

# Check campaign endpoints
curl -X GET http://localhost:3000/campaign/email-groups \
  -H "Authorization: Basic your-auth-token"
```

## 📧 Quick Campaign Creation

### Step 1: Create Email Template
```bash
curl -X POST http://localhost:3000/mail-template/email-template \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Quick Test Template",
    "subject": "Test Email",
    "htmlContent": "<h1>Hello {{firstName}}!</h1><p>This is a test email.</p>"
  }'
```

### Step 2: Create Email Group
```bash
curl -X POST http://localhost:3000/campaign/email-groups \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Group",
    "type": "static"
  }'
```

### Step 3: Add Test Email
```bash
curl -X POST http://localhost:3000/campaign/email-groups/1/members \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "firstName": "Test",
    "lastName": "User"
  }'
```

### Step 4: Create & Send Campaign
```bash
# Create campaign
curl -X POST http://localhost:3000/campaign \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Campaign",
    "templateId": 1,
    "type": "newsletter",
    "emailGroups": [1],
    "subjectLine": "Test Email Campaign"
  }'

# Send immediately
curl -X POST http://localhost:3000/campaign/1/send
```

### Step 5: Check Results
```bash
# View campaign status
curl -X GET http://localhost:3000/campaign/1

# Get campaign statistics
curl -X GET http://localhost:3000/campaign/1/summary
```

## 📊 Common API Calls

### Email Group Management
```bash
# List all groups
GET /campaign/email-groups

# Create group
POST /campaign/email-groups
{
  "name": "Group Name",
  "type": "static"
}

# Add member
POST /campaign/email-groups/{id}/members
{
  "email": "<EMAIL>",
  "firstName": "John"
}

# Import CSV
POST /campaign/email-groups/{id}/import
# Form data with file upload
```

### Campaign Management
```bash
# List campaigns
GET /campaign

# Create campaign
POST /campaign
{
  "name": "Campaign Name",
  "templateId": 1,
  "emailGroups": [1],
  "subjectLine": "Subject"
}

# Send campaign
POST /campaign/{id}/send

# Schedule campaign
POST /campaign/{id}/schedule
{
  "scheduledAt": "2024-12-25T10:00:00Z"
}

# Pause campaign
POST /campaign/{id}/pause

# Resume campaign
POST /campaign/{id}/resume
```

### Analytics & Reporting
```bash
# Campaign summary
GET /campaign/{id}/summary

# Detailed report
POST /campaign/{id}/reports
{
  "type": "detailed",
  "exportFormat": "excel"
}

# Geographic report
GET /campaign/{id}/geographic-report

# Time-based report
GET /campaign/{id}/time-based-report
```

## 🔧 Configuration Examples

### Basic Campaign Settings
```json
{
  "settings": {
    "trackOpens": true,
    "trackClicks": true,
    "enableUnsubscribe": true,
    "throttleRate": 100,
    "retryFailures": true,
    "maxRetries": 3
  }
}
```

### A/B Testing Setup
```json
{
  "abTestSettings": {
    "enabled": true,
    "subjectVariants": [
      "Subject A",
      "Subject B"
    ],
    "splitPercentage": 50,
    "winnerCriteria": "open_rate"
  }
}
```

### Dynamic Group Segmentation
```json
{
  "segmentationCriteria": {
    "engagement": {
      "minOpenRate": 25,
      "lastOpenDays": 30
    },
    "purchaseHistory": {
      "minAmount": 100,
      "minOrders": 2
    }
  }
}
```

## 📈 Performance Metrics

### Key Metrics to Monitor
- **Delivery Rate**: > 95%
- **Open Rate**: 20-25% (industry average)
- **Click Rate**: 2-5% (industry average)
- **Bounce Rate**: < 2%
- **Unsubscribe Rate**: < 0.5%
- **Complaint Rate**: < 0.1%

### Optimization Tips
1. **Subject Lines**: Keep under 50 characters
2. **Send Time**: Test Tuesday-Thursday, 10 AM - 2 PM
3. **Frequency**: Start with weekly, adjust based on engagement
4. **Personalization**: Use first name and relevant content
5. **Mobile**: Ensure responsive design

## 🚨 Troubleshooting Quick Fixes

### Campaign Not Sending
```bash
# Check Redis
redis-cli ping

# Restart queues
npm run queue:restart

# Check campaign status
curl -X GET http://localhost:3000/campaign/{id}
```

### High Bounce Rate
```bash
# Validate emails before import
# Clean inactive subscribers
# Check sender reputation
```

### Import Failures
```bash
# Verify CSV format:
# email,firstName,lastName
# <EMAIL>,John,Doe

# Check file encoding (UTF-8)
# Ensure no special characters in headers
```

## 📞 Quick Support

### Log Locations
- Application logs: `logs/application.log`
- Queue logs: `logs/queue.log`
- Error logs: `logs/error.log`

### Debug Commands
```bash
# Check queue status
curl -X GET http://localhost:3000/admin/queues/stats

# View recent errors
tail -f logs/error.log

# Check database connections
npm run db:check
```

### Environment Variables
```env
# Required
REDIS_HOST=localhost
REDIS_PORT=6379
SES_FROM_MAIL=<EMAIL>

# Optional
CAMPAIGN_THROTTLE_RATE=100
CAMPAIGN_MAX_RETRIES=3
REPORT_EXPIRY_DAYS=30
```

---

**Need more help?** Check the [complete workflow guide](./CAMPAIGN_BULK_MAIL_WORKFLOW.md) for detailed instructions.
