{"name": "nest-typescript-starter", "private": true, "version": "1.0.0", "description": "Nest TypeScript starter repository", "license": "MIT", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/src/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/jest/bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "typeorm": "ts-node ./node_modules/typeorm/cli", "typeorm:create-migration": "npm run typeorm migration:create -- ./migrations/%npm_config_migration_name%", "typeorm:generate-migration": "npx typeorm-ts-node-commonjs migration:generate -d ./typeorm.config.ts ./migrations/$npm_config_name", "typeorm:run-migrations": "npx typeorm-ts-node-commonjs migration:run -- -d ./typeorm.config.ts", "typeorm:revert-migrations": "npm run typeorm migration:revert -- -d ./typeorm.config.ts", "run-specific-migration": "bash run-migration.sh %MigrationFileName%"}, "engines": {"npm": ">=10.0.0", "node": ">=20.0.0"}, "dependencies": {"@aws-sdk/client-s3": "^3.817.0", "@bull-board/api": "^6.10.1", "@bull-board/express": "^6.10.1", "@nestjs-modules/mailer": "^2.0.2", "@nestjs/bullmq": "^11.0.2", "@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.1.1", "@nestjs/schedule": "^6.0.0", "@nestjs/swagger": "^11.2.0", "@nestjs/typeorm": "^11.0.0", "@socket.io/redis-adapter": "^8.3.0", "axios": "^1.10.0", "bcrypt": "^6.0.0", "bullmq": "^5.53.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "compression": "^1.8.0", "csv-parser": "^3.2.0", "dotenv": "^16.5.0", "exceljs": "^4.4.0", "handlebars": "^4.7.8", "helmet": "^8.1.0", "html-pdf": "^3.0.1", "html-to-text": "^9.0.5", "ioredis": "^5.6.1", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pg": "^8.16.0", "redis": "^4.7.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "sharp": "^0.34.2", "socket.io": "^4.8.1", "swagger-stats": "^0.99.7", "tmp": "^0.2.3", "typeorm": "^0.3.24", "xml2js": "^0.6.2"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.8", "@types/bcrypt": "^5.0.2", "@types/compression": "^1.8.0", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/multer": "^1.4.12", "@types/node": "^22.10.7", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/supertest": "^6.0.2", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.3", "globals": "^15.14.0", "jest": "^29.7.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "pnpm": {"onlyBuiltDependencies": ["@nestjs/core", "@swc/core"]}}