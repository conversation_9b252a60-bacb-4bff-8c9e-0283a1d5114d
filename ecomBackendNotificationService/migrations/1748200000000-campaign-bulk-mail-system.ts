import { MigrationInterface, QueryRunner } from 'typeorm';

export class CampaignBulkMailSystem1748200000000 implements MigrationInterface {
  name = 'CampaignBulkMailSystem1748200000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create enums for email groups
    await queryRunner.query(`
      CREATE TYPE "public"."email_groups_type_enum" AS ENUM('static', 'dynamic', 'imported', 'segmented')
    `);
    await queryRunner.query(`
      CREATE TYPE "public"."email_groups_status_enum" AS ENUM('active', 'inactive', 'archived')
    `);

    // Create enums for email group members
    await queryRunner.query(`
      CREATE TYPE "public"."email_group_members_status_enum" AS ENUM('active', 'unsubscribed', 'bounced', 'complained', 'suppressed')
    `);
    await queryRunner.query(`
      CREATE TYPE "public"."email_group_members_source_enum" AS ENUM('manual', 'import', 'api', 'website', 'checkout', 'newsletter')
    `);

    // Create enums for campaigns
    await queryRunner.query(`
      CREATE TYPE "public"."email_campaigns_type_enum" AS ENUM('promotional', 'newsletter', 'transactional', 'abandoned_cart', 'welcome_series', 're_engagement', 'product_announcement', 'seasonal', 'survey', 'event_invitation')
    `);
    await queryRunner.query(`
      CREATE TYPE "public"."email_campaigns_priority_enum" AS ENUM('low', 'medium', 'high', 'urgent')
    `);

    // Update existing campaign status enum
    await queryRunner.query(`
      ALTER TYPE "public"."email_campaigns_status_enum" ADD VALUE IF NOT EXISTS 'sending'
    `);
    await queryRunner.query(`
      ALTER TYPE "public"."email_campaigns_status_enum" ADD VALUE IF NOT EXISTS 'paused'
    `);
    await queryRunner.query(`
      ALTER TYPE "public"."email_campaigns_status_enum" ADD VALUE IF NOT EXISTS 'failed'
    `);
    await queryRunner.query(`
      ALTER TYPE "public"."email_campaigns_status_enum" ADD VALUE IF NOT EXISTS 'completed'
    `);

    // Update existing delivery status enum
    await queryRunner.query(`
      ALTER TYPE "public"."email_send_history_status_enum" ADD VALUE IF NOT EXISTS 'queued'
    `);
    await queryRunner.query(`
      ALTER TYPE "public"."email_send_history_status_enum" ADD VALUE IF NOT EXISTS 'sending'
    `);
    await queryRunner.query(`
      ALTER TYPE "public"."email_send_history_status_enum" ADD VALUE IF NOT EXISTS 'delivered'
    `);
    await queryRunner.query(`
      ALTER TYPE "public"."email_send_history_status_enum" ADD VALUE IF NOT EXISTS 'opened'
    `);
    await queryRunner.query(`
      ALTER TYPE "public"."email_send_history_status_enum" ADD VALUE IF NOT EXISTS 'clicked'
    `);
    await queryRunner.query(`
      ALTER TYPE "public"."email_send_history_status_enum" ADD VALUE IF NOT EXISTS 'bounced'
    `);
    await queryRunner.query(`
      ALTER TYPE "public"."email_send_history_status_enum" ADD VALUE IF NOT EXISTS 'complained'
    `);
    await queryRunner.query(`
      ALTER TYPE "public"."email_send_history_status_enum" ADD VALUE IF NOT EXISTS 'unsubscribed'
    `);
    await queryRunner.query(`
      ALTER TYPE "public"."email_send_history_status_enum" ADD VALUE IF NOT EXISTS 'suppressed'
    `);

    // Create enums for campaign reports
    await queryRunner.query(`
      CREATE TYPE "public"."campaign_reports_type_enum" AS ENUM('summary', 'detailed', 'geographic', 'device', 'time_based', 'engagement')
    `);

    // Create email_groups table
    await queryRunner.query(`
      CREATE TABLE "email_groups" (
        "created_by" integer,
        "updated_by" integer,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        "deleted_at" TIMESTAMP,
        "id" SERIAL NOT NULL,
        "is_active" boolean NOT NULL DEFAULT true,
        "remarks" character varying,
        "name" character varying NOT NULL,
        "description" character varying,
        "type" "public"."email_groups_type_enum" NOT NULL DEFAULT 'static',
        "status" "public"."email_groups_status_enum" NOT NULL DEFAULT 'active',
        "member_count" integer NOT NULL DEFAULT '0',
        "active_member_count" integer NOT NULL DEFAULT '0',
        "segmentation_criteria" jsonb,
        "tags" jsonb,
        "created_by_user_id" integer,
        "last_sync_at" TIMESTAMP,
        "auto_sync_enabled" boolean NOT NULL DEFAULT false,
        "sync_frequency_hours" integer,
        CONSTRAINT "PK_email_groups" PRIMARY KEY ("id")
      )
    `);

    // Create email_group_members table
    await queryRunner.query(`
      CREATE TABLE "email_group_members" (
        "created_by" integer,
        "updated_by" integer,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        "deleted_at" TIMESTAMP,
        "id" SERIAL NOT NULL,
        "is_active" boolean NOT NULL DEFAULT true,
        "remarks" character varying,
        "group_id" integer NOT NULL,
        "email" character varying NOT NULL,
        "first_name" character varying,
        "last_name" character varying,
        "phone" character varying,
        "country_id" integer,
        "city" character varying,
        "gender" character varying,
        "date_of_birth" TIMESTAMP,
        "status" "public"."email_group_members_status_enum" NOT NULL DEFAULT 'active',
        "source" "public"."email_group_members_source_enum" NOT NULL DEFAULT 'manual',
        "subscribed_at" TIMESTAMP NOT NULL,
        "unsubscribed_at" TIMESTAMP,
        "last_email_sent_at" TIMESTAMP,
        "last_email_opened_at" TIMESTAMP,
        "last_email_clicked_at" TIMESTAMP,
        "total_emails_sent" integer NOT NULL DEFAULT '0',
        "total_emails_opened" integer NOT NULL DEFAULT '0',
        "total_emails_clicked" integer NOT NULL DEFAULT '0',
        "total_emails_bounced" integer NOT NULL DEFAULT '0',
        "bounce_count" integer NOT NULL DEFAULT '0',
        "complaint_count" integer NOT NULL DEFAULT '0',
        "custom_fields" jsonb,
        "tags" jsonb,
        "preferences" jsonb,
        "email_verified" boolean NOT NULL DEFAULT false,
        "verification_token" character varying,
        "verification_sent_at" TIMESTAMP,
        "ip_address" character varying,
        "user_agent" character varying,
        "referrer_url" character varying,
        "utm_source" character varying,
        "utm_medium" character varying,
        "utm_campaign" character varying,
        CONSTRAINT "PK_email_group_members" PRIMARY KEY ("id"),
        CONSTRAINT "UQ_email_group_members_email_group" UNIQUE ("email", "group_id")
      )
    `);

    // Update existing email_campaigns table with new columns
    await queryRunner.query(`
      ALTER TABLE "email_campaigns" 
      ADD COLUMN IF NOT EXISTS "description" character varying
    `);
    await queryRunner.query(`
      ALTER TABLE "email_campaigns" 
      ADD COLUMN IF NOT EXISTS "template_id" integer
    `);
    await queryRunner.query(`
      ALTER TABLE "email_campaigns" 
      ADD COLUMN IF NOT EXISTS "type" "public"."email_campaigns_type_enum" DEFAULT 'promotional'
    `);
    await queryRunner.query(`
      ALTER TABLE "email_campaigns" 
      ADD COLUMN IF NOT EXISTS "priority" "public"."email_campaigns_priority_enum" DEFAULT 'medium'
    `);
    await queryRunner.query(`
      ALTER TABLE "email_campaigns" 
      ADD COLUMN IF NOT EXISTS "email_groups" jsonb
    `);
    await queryRunner.query(`
      ALTER TABLE "email_campaigns" 
      ADD COLUMN IF NOT EXISTS "custom_emails" jsonb
    `);
    await queryRunner.query(`
      ALTER TABLE "email_campaigns" 
      ADD COLUMN IF NOT EXISTS "sender_name" character varying
    `);
    await queryRunner.query(`
      ALTER TABLE "email_campaigns" 
      ADD COLUMN IF NOT EXISTS "sender_email" character varying
    `);
    await queryRunner.query(`
      ALTER TABLE "email_campaigns" 
      ADD COLUMN IF NOT EXISTS "reply_to" character varying
    `);
    await queryRunner.query(`
      ALTER TABLE "email_campaigns" 
      ADD COLUMN IF NOT EXISTS "subject_line" character varying
    `);
    await queryRunner.query(`
      ALTER TABLE "email_campaigns" 
      ADD COLUMN IF NOT EXISTS "preheader_text" character varying
    `);
    await queryRunner.query(`
      ALTER TABLE "email_campaigns" 
      ADD COLUMN IF NOT EXISTS "total_recipients" integer DEFAULT 0
    `);
    await queryRunner.query(`
      ALTER TABLE "email_campaigns" 
      ADD COLUMN IF NOT EXISTS "sent_count" integer DEFAULT 0
    `);
    await queryRunner.query(`
      ALTER TABLE "email_campaigns" 
      ADD COLUMN IF NOT EXISTS "delivered_count" integer DEFAULT 0
    `);
    await queryRunner.query(`
      ALTER TABLE "email_campaigns" 
      ADD COLUMN IF NOT EXISTS "opened_count" integer DEFAULT 0
    `);
    await queryRunner.query(`
      ALTER TABLE "email_campaigns" 
      ADD COLUMN IF NOT EXISTS "clicked_count" integer DEFAULT 0
    `);
    await queryRunner.query(`
      ALTER TABLE "email_campaigns" 
      ADD COLUMN IF NOT EXISTS "bounced_count" integer DEFAULT 0
    `);
    await queryRunner.query(`
      ALTER TABLE "email_campaigns" 
      ADD COLUMN IF NOT EXISTS "unsubscribed_count" integer DEFAULT 0
    `);
    await queryRunner.query(`
      ALTER TABLE "email_campaigns" 
      ADD COLUMN IF NOT EXISTS "complaint_count" integer DEFAULT 0
    `);
    await queryRunner.query(`
      ALTER TABLE "email_campaigns" 
      ADD COLUMN IF NOT EXISTS "started_at" TIMESTAMP
    `);
    await queryRunner.query(`
      ALTER TABLE "email_campaigns" 
      ADD COLUMN IF NOT EXISTS "completed_at" TIMESTAMP
    `);
    await queryRunner.query(`
      ALTER TABLE "email_campaigns" 
      ADD COLUMN IF NOT EXISTS "estimated_send_time" integer
    `);
    await queryRunner.query(`
      ALTER TABLE "email_campaigns" 
      ADD COLUMN IF NOT EXISTS "settings" jsonb
    `);
    await queryRunner.query(`
      ALTER TABLE "email_campaigns" 
      ADD COLUMN IF NOT EXISTS "segmentation_rules" jsonb
    `);
    await queryRunner.query(`
      ALTER TABLE "email_campaigns" 
      ADD COLUMN IF NOT EXISTS "ab_test_settings" jsonb
    `);

    // Create campaign_reports table
    await queryRunner.query(`
      CREATE TABLE "campaign_reports" (
        "created_by" integer,
        "updated_by" integer,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        "deleted_at" TIMESTAMP,
        "id" SERIAL NOT NULL,
        "is_active" boolean NOT NULL DEFAULT true,
        "remarks" character varying,
        "campaign_id" integer NOT NULL,
        "type" "public"."campaign_reports_type_enum" NOT NULL,
        "report_date" TIMESTAMP NOT NULL,
        "data" jsonb NOT NULL,
        "filters" jsonb,
        "generated_by_user_id" integer,
        "is_automated" boolean NOT NULL DEFAULT false,
        "export_format" character varying,
        "export_url" character varying,
        "expires_at" TIMESTAMP,
        CONSTRAINT "PK_campaign_reports" PRIMARY KEY ("id")
      )
    `);

    // Add foreign key constraints
    await queryRunner.query(`
      ALTER TABLE "email_group_members" 
      ADD CONSTRAINT "FK_email_group_members_group_id" 
      FOREIGN KEY ("group_id") REFERENCES "email_groups"("id") ON DELETE CASCADE ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "campaign_reports" 
      ADD CONSTRAINT "FK_campaign_reports_campaign_id" 
      FOREIGN KEY ("campaign_id") REFERENCES "email_campaigns"("id") ON DELETE CASCADE ON UPDATE NO ACTION
    `);

    // Create indexes for better performance
    await queryRunner.query(`CREATE INDEX "IDX_email_groups_name" ON "email_groups" ("name")`);
    await queryRunner.query(`CREATE INDEX "IDX_email_groups_type" ON "email_groups" ("type")`);
    await queryRunner.query(`CREATE INDEX "IDX_email_groups_status" ON "email_groups" ("status")`);
    await queryRunner.query(`CREATE INDEX "IDX_email_groups_created_by_user_id" ON "email_groups" ("created_by_user_id")`);

    await queryRunner.query(`CREATE INDEX "IDX_email_group_members_group_id" ON "email_group_members" ("group_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_email_group_members_email" ON "email_group_members" ("email")`);
    await queryRunner.query(`CREATE INDEX "IDX_email_group_members_status" ON "email_group_members" ("status")`);
    await queryRunner.query(`CREATE INDEX "IDX_email_group_members_source" ON "email_group_members" ("source")`);
    await queryRunner.query(`CREATE INDEX "IDX_email_group_members_country_id" ON "email_group_members" ("country_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_email_group_members_subscribed_at" ON "email_group_members" ("subscribed_at")`);

    await queryRunner.query(`CREATE INDEX "IDX_email_campaigns_type" ON "email_campaigns" ("type")`);
    await queryRunner.query(`CREATE INDEX "IDX_email_campaigns_priority" ON "email_campaigns" ("priority")`);
    await queryRunner.query(`CREATE INDEX "IDX_email_campaigns_status" ON "email_campaigns" ("status")`);
    await queryRunner.query(`CREATE INDEX "IDX_email_campaigns_scheduled_at" ON "email_campaigns" ("scheduledAt")`);
    await queryRunner.query(`CREATE INDEX "IDX_email_campaigns_started_at" ON "email_campaigns" ("started_at")`);
    await queryRunner.query(`CREATE INDEX "IDX_email_campaigns_template_id" ON "email_campaigns" ("template_id")`);

    await queryRunner.query(`CREATE INDEX "IDX_campaign_reports_campaign_id" ON "campaign_reports" ("campaign_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_campaign_reports_type" ON "campaign_reports" ("type")`);
    await queryRunner.query(`CREATE INDEX "IDX_campaign_reports_report_date" ON "campaign_reports" ("report_date")`);
    await queryRunner.query(`CREATE INDEX "IDX_campaign_reports_generated_by_user_id" ON "campaign_reports" ("generated_by_user_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_campaign_reports_expires_at" ON "campaign_reports" ("expires_at")`);

    // Create indexes for email_send_history improvements
    await queryRunner.query(`CREATE INDEX IF NOT EXISTS "IDX_email_send_history_campaign_id" ON "email_send_history" ("campaign_id")`);
    await queryRunner.query(`CREATE INDEX IF NOT EXISTS "IDX_email_send_history_recipient_email" ON "email_send_history" ("recipient_email")`);
    await queryRunner.query(`CREATE INDEX IF NOT EXISTS "IDX_email_send_history_status" ON "email_send_history" ("status")`);
    await queryRunner.query(`CREATE INDEX IF NOT EXISTS "IDX_email_send_history_opened" ON "email_send_history" ("opened")`);
    await queryRunner.query(`CREATE INDEX IF NOT EXISTS "IDX_email_send_history_clicked" ON "email_send_history" ("clicked")`);
    await queryRunner.query(`CREATE INDEX IF NOT EXISTS "IDX_email_send_history_bounced" ON "email_send_history" ("bounced")`);
    await queryRunner.query(`CREATE INDEX IF NOT EXISTS "IDX_email_send_history_sent_at" ON "email_send_history" ("sent_at")`);
    await queryRunner.query(`CREATE INDEX IF NOT EXISTS "IDX_email_send_history_opened_at" ON "email_send_history" ("opened_at")`);
    await queryRunner.query(`CREATE INDEX IF NOT EXISTS "IDX_email_send_history_clicked_at" ON "email_send_history" ("clicked_at")`);

    // Create composite indexes for better query performance
    await queryRunner.query(`CREATE INDEX "IDX_email_group_members_group_status" ON "email_group_members" ("group_id", "status")`);
    await queryRunner.query(`CREATE INDEX "IDX_email_send_history_campaign_status" ON "email_send_history" ("campaign_id", "status")`);
    await queryRunner.query(`CREATE INDEX "IDX_email_send_history_campaign_opened" ON "email_send_history" ("campaign_id", "opened")`);
    await queryRunner.query(`CREATE INDEX "IDX_email_send_history_campaign_clicked" ON "email_send_history" ("campaign_id", "clicked")`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_email_send_history_campaign_clicked"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_email_send_history_campaign_opened"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_email_send_history_campaign_status"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_email_group_members_group_status"`);
    
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_campaign_reports_expires_at"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_campaign_reports_generated_by_user_id"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_campaign_reports_report_date"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_campaign_reports_type"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_campaign_reports_campaign_id"`);
    
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_email_campaigns_template_id"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_email_campaigns_started_at"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_email_campaigns_scheduled_at"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_email_campaigns_status"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_email_campaigns_priority"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_email_campaigns_type"`);
    
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_email_group_members_subscribed_at"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_email_group_members_country_id"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_email_group_members_source"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_email_group_members_status"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_email_group_members_email"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_email_group_members_group_id"`);
    
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_email_groups_created_by_user_id"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_email_groups_status"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_email_groups_type"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_email_groups_name"`);

    // Drop foreign key constraints
    await queryRunner.query(`ALTER TABLE "campaign_reports" DROP CONSTRAINT IF EXISTS "FK_campaign_reports_campaign_id"`);
    await queryRunner.query(`ALTER TABLE "email_group_members" DROP CONSTRAINT IF EXISTS "FK_email_group_members_group_id"`);

    // Drop tables
    await queryRunner.query(`DROP TABLE IF EXISTS "campaign_reports"`);
    await queryRunner.query(`DROP TABLE IF EXISTS "email_group_members"`);
    await queryRunner.query(`DROP TABLE IF EXISTS "email_groups"`);

    // Remove columns from email_campaigns table
    await queryRunner.query(`ALTER TABLE "email_campaigns" DROP COLUMN IF EXISTS "ab_test_settings"`);
    await queryRunner.query(`ALTER TABLE "email_campaigns" DROP COLUMN IF EXISTS "segmentation_rules"`);
    await queryRunner.query(`ALTER TABLE "email_campaigns" DROP COLUMN IF EXISTS "settings"`);
    await queryRunner.query(`ALTER TABLE "email_campaigns" DROP COLUMN IF EXISTS "estimated_send_time"`);
    await queryRunner.query(`ALTER TABLE "email_campaigns" DROP COLUMN IF EXISTS "completed_at"`);
    await queryRunner.query(`ALTER TABLE "email_campaigns" DROP COLUMN IF EXISTS "started_at"`);
    await queryRunner.query(`ALTER TABLE "email_campaigns" DROP COLUMN IF EXISTS "complaint_count"`);
    await queryRunner.query(`ALTER TABLE "email_campaigns" DROP COLUMN IF EXISTS "unsubscribed_count"`);
    await queryRunner.query(`ALTER TABLE "email_campaigns" DROP COLUMN IF EXISTS "bounced_count"`);
    await queryRunner.query(`ALTER TABLE "email_campaigns" DROP COLUMN IF EXISTS "clicked_count"`);
    await queryRunner.query(`ALTER TABLE "email_campaigns" DROP COLUMN IF EXISTS "opened_count"`);
    await queryRunner.query(`ALTER TABLE "email_campaigns" DROP COLUMN IF EXISTS "delivered_count"`);
    await queryRunner.query(`ALTER TABLE "email_campaigns" DROP COLUMN IF EXISTS "sent_count"`);
    await queryRunner.query(`ALTER TABLE "email_campaigns" DROP COLUMN IF EXISTS "total_recipients"`);
    await queryRunner.query(`ALTER TABLE "email_campaigns" DROP COLUMN IF EXISTS "preheader_text"`);
    await queryRunner.query(`ALTER TABLE "email_campaigns" DROP COLUMN IF EXISTS "subject_line"`);
    await queryRunner.query(`ALTER TABLE "email_campaigns" DROP COLUMN IF EXISTS "reply_to"`);
    await queryRunner.query(`ALTER TABLE "email_campaigns" DROP COLUMN IF EXISTS "sender_email"`);
    await queryRunner.query(`ALTER TABLE "email_campaigns" DROP COLUMN IF EXISTS "sender_name"`);
    await queryRunner.query(`ALTER TABLE "email_campaigns" DROP COLUMN IF EXISTS "custom_emails"`);
    await queryRunner.query(`ALTER TABLE "email_campaigns" DROP COLUMN IF EXISTS "email_groups"`);
    await queryRunner.query(`ALTER TABLE "email_campaigns" DROP COLUMN IF EXISTS "priority"`);
    await queryRunner.query(`ALTER TABLE "email_campaigns" DROP COLUMN IF EXISTS "type"`);
    await queryRunner.query(`ALTER TABLE "email_campaigns" DROP COLUMN IF EXISTS "template_id"`);
    await queryRunner.query(`ALTER TABLE "email_campaigns" DROP COLUMN IF EXISTS "description"`);

    // Drop enums
    await queryRunner.query(`DROP TYPE IF EXISTS "public"."campaign_reports_type_enum"`);
    await queryRunner.query(`DROP TYPE IF EXISTS "public"."email_group_members_source_enum"`);
    await queryRunner.query(`DROP TYPE IF EXISTS "public"."email_group_members_status_enum"`);
    await queryRunner.query(`DROP TYPE IF EXISTS "public"."email_groups_status_enum"`);
    await queryRunner.query(`DROP TYPE IF EXISTS "public"."email_groups_type_enum"`);
    await queryRunner.query(`DROP TYPE IF EXISTS "public"."email_campaigns_priority_enum"`);
    await queryRunner.query(`DROP TYPE IF EXISTS "public"."email_campaigns_type_enum"`);
  }
}
