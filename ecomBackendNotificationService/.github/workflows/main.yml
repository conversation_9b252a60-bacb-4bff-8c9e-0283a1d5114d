name: Deploy via SSH
on:
  push:
    branches: [ main ]  # Trigger on push to main branch
  workflow_dispatch:    # Allow manual triggering

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: SSH Deployment
        uses: appleboy/ssh-action@v0.1.10
        with:
          host: ${{ secrets.SSH_HOST }}       # Server IP or host
          username: ${{ secrets.SSH_USER }} # SSH username
          key: ${{ secrets.SSH_MAIL_SERVICE_PK }}  # SSH private key
          port: ${{ secrets.SSH_PORT || 22 }}  # SSH port (default: 22)
          script: |
            # Move to project directory
            cd /var/www/mail-service/backend/ecomBackendNotificationService
            
            # Make sure origin is SSH
            git remote set-<NAME_EMAIL>:Pantoneclo-LTD/ecomBackendNotificationService.git
            
            # Checkout main branch explicitly
            git fetch origin main
            git checkout main
            
            # Hard reset to remote
            git reset --hard origin/main
            
            # Clean up everything
            git clean -df
            
            # Clean built and deps
            rm -rf node_modules dist
            
            # Install & build fresh
            npm install --omit=dev
            npm run build
            
            # Restart with PM2
            # pm2 restart mail-service
            # Reload PM2 with the updated ecosystem
            pm2 start ecosystem.config.js --env production
