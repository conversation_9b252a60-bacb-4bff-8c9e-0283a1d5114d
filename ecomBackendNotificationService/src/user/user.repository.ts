import { Injectable } from '@nestjs/common';
import { Repository, DataSource, IsNull, QueryRunner } from 'typeorm';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { UpdateProfileDto } from './dto/update-profile.dto';
import { PasswordUtil } from 'libs/common/utils/password.util';
import { PageMetaDto } from 'libs/common/pagination/page-meta.dto';
import { PageDto } from 'libs/common/pagination/page.dto';
import { PageOptionsDto } from 'libs/common/pagination/page-options.dto';
import { User } from './entity/user.entity';

@Injectable()
export class UserRepository extends Repository<User> {
  constructor(
    private dataSource: DataSource
  ) {
    super(
      User,
      dataSource.createEntityManager(),
      dataSource.createQueryRunner(),
    );
  }

  // public async findAll(page: number, limit: number): Promise<User[]> {
  //   return this.find({
  //     take: limit,
  //     skip: (page - 1) * limit,
  //     withDeleted: false,
  //   });
  // }

  public async findAll(): Promise<User[]> {
    return this.find();
  }
  public async findAllDataByPagination(
    userTypeId: number,
    params: any,
    pageOptionsDto: PageOptionsDto,
  ): Promise<PageDto<User>> {
    const queryBuilder = this.createQueryBuilder('user')
      // .where('user.userTypeId = :userTypeId', { userTypeId })
      .andWhere('user.deletedAt IS NULL')
      // .leftJoinAndSelect('user.userType', 'userType')
      // .leftJoinAndSelect('user.address', 'address')
      .skip(pageOptionsDto.skip)
      .take(pageOptionsDto.take)
      .orderBy('user.createdAt', 'DESC');

    if (params?.globalSearch) {
      queryBuilder.andWhere(
        '(user.firstName ILIKE :search OR user.lastName ILIKE :search OR user.email ILIKE :search OR user.phone ILIKE :search)',
        { search: `%${params?.globalSearch}%` },
      );
    }
    const [list, itemCount] = await queryBuilder.getManyAndCount();

    const pageMetaDto = new PageMetaDto({ itemCount, pageOptionsDto });

    return new PageDto(list, pageMetaDto);
  }

  public async findAllUserListByPagination(
    params: any,
    pageOptionsDto: PageOptionsDto,
  ): Promise<PageDto<User>> {
    const queryBuilder = this.createQueryBuilder('user')
      .andWhere('user.deletedAt IS NULL')
      // .leftJoinAndSelect('user.userType', 'userType')
      // .leftJoinAndSelect('user.address', 'address')
      // .leftJoinAndSelect('user.role', 'role')
      .skip(pageOptionsDto.skip)
      .take(pageOptionsDto.take)
      .orderBy('user.createdAt', 'DESC');

    if (params?.globalSearch) {
      queryBuilder.andWhere(
        '(user.firstName ILIKE :search OR user.lastName ILIKE :search OR user.email ILIKE :search OR user.phone ILIKE :search OR role.name ILIKE :search)',
        { search: `%${params?.globalSearch}%` },
      );
    }
    const [list, itemCount] = await queryBuilder.getManyAndCount();

    const pageMetaDto = new PageMetaDto({ itemCount, pageOptionsDto });

    return new PageDto(list, pageMetaDto);
  }

  public async findById(id: number): Promise<User | null> {
    return this.findOneBy({ id: id });
  }

  public async add(createUserDto: CreateUserDto): Promise<User> {
    // const setRole = createUserDto?.userTypeId === 2 ? 2 : createUserDto?.role || null;
    // const newUser = this.create({ ...createUserDto, role: setRole as any });
    const newUser = this.create({ ...createUserDto });
    return this.save(newUser);
  }

  public async updateOne(
    id: number,
    updateUserDto: UpdateUserDto,
  ): Promise<User | null> {
    const updatedResult = await this.update(id, updateUserDto);
    let user = null;
    if (updatedResult.affected > 0) {
      user = await this.findOneBy({ id: id });
    }
    return user;
  }
  public async updateUserProfile(
    id: number,
    updateUserDto: UpdateProfileDto,
  ): Promise<User | null> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction(); // ✅ Begin transaction

    try {
      const updateUserData: Partial<User> = {
        ...updateUserDto,
        // userType: updateUserDto?.userTypeId as any,
      };

      let hashedPassword: string | undefined;
      if (updateUserDto?.password) {
        hashedPassword = await PasswordUtil.hashPassword(updateUserDto.password);
        updateUserData.password = hashedPassword;
      }

      // Update User
      await queryRunner.manager
        .createQueryBuilder()
        .update(User)
        .set(updateUserData)
        .where('id = :id', { id })
        .execute();

      // // If password is updated, also update Login entity
      // if (hashedPassword) {
      //   const user = await queryRunner.manager.findOneBy(User, { id });
      //   if (user) {
      //     await queryRunner.manager
      //       .createQueryBuilder()
      //       .update(Login)
      //       .set({ password: hashedPassword })
      //       .where('email = :email', { email: user.email })
      //       .execute();
      //   }
      // }

      await queryRunner.commitTransaction(); // ✅ Commit transaction

      return await queryRunner.manager.findOneBy(User, { id });
    } catch (error) {
      console.error('Transaction error:', error);
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  public async setDefaultAddress(
    id: number,
    addressId?: number,
  ): Promise<User | null> {
    const updatedResult = await this.update(id, {
      addressId: addressId,
    });
    return updatedResult.affected > 0 ? this.findById(id) : null;
  }

  // public async setDefaultShippingAddress(
  //   id: number,
  //   shippingAddressId?: number,
  // ): Promise<User | null> {
  //   const updatedResult = await this.update(id, {
  //     shippingAddressId: shippingAddressId,
  //   });
  //   return updatedResult.affected > 0 ? this.findById(id) : null;
  // }

  public async destroy(id: number): Promise<number | null> {
    const result = await this.softDelete(id);
    return result.affected;
  }

  public async restoreData(id: number): Promise<User | null> {
    const result = await this.restore(id);
    let user = null;
    if (result.affected > 0) {
      user = await this.findOneBy({ id: id });
    }
    return user;
  }

  public async activeOrInactive(
    id: number,
    status: boolean,
  ): Promise<User | null> {
    const result = await this.update(id, { isActive: status });
    let user = null;
    if (result.affected > 0) {
      user = await this.findOneBy({ id: id });
    }
    return user;
  }

  public async findUserByEmail(email: string): Promise<User | null> {
    // const queryBuilder = this.createQueryBuilder('user')
    //   .andWhere({ email })
    //   .leftJoinAndSelect('user.userType', 'userType')
    //   .leftJoinAndSelect('user.role', 'role')
    //   .leftJoinAndSelect('role.permissions', 'permissions')
    //   .orderBy('user.createdAt', 'DESC')

    // return queryBuilder.getOne();
    return this.createQueryBuilder('user')
      // .leftJoinAndSelect('user.userType', 'userType')
      // .leftJoinAndSelect('user.role', 'role')
      // .leftJoinAndSelect('role.permissions', 'permissions')
      .addSelect('user.password')
      .where('user.email = :email', { email })
      .orderBy('user.createdAt', 'DESC')
      .getOne();

  }

  public async reportCustomerDemographicsList(
    pageOptionsDto: PageOptionsDto,
  ): Promise<PageDto<User>> {
    const [list, itemCount] = await this.findAndCount({
      where: {
        userTypeId: 2,
      },
      skip: pageOptionsDto.skip,
      take: pageOptionsDto.take,
      withDeleted: false,
      order: {
        firstName: 'ASC',
      },
    });

    const pageMetaDto = new PageMetaDto({ itemCount, pageOptionsDto });

    return new PageDto(list, pageMetaDto);
    // return await this.findAndCount({ where: conditions });
  }
  public async excelDownloadReportCustomerDemographicsList(): Promise<any[]> {
    const query = `
      select u.first_name, u.last_name, u.phone, u.email, u.gender, 
      CONCAT(a.address, ', ', a.city, ', ', c.name, ' - ', a.code) as address
      from "user" u 
      left join address a on a.id = u.address_id 
      left join country c on c.id = a.country_id
      where u.is_active is true and u.deleted_at is null and u.user_type_id = 2
      `;

    return this.manager.query(query);
  }
  public async adminDashboardCards(): Promise<any[]> {
    const query = `SELECT
    (SELECT COUNT(*) FROM product) AS total_products,
    (SELECT COUNT(*) FROM brand) AS total_brands,
    (SELECT COUNT(*) FROM category) AS total_categories,
    (SELECT COUNT(*) FROM attribute_value where attribute_id = 1) AS total_size,
    (SELECT COUNT(*) FROM attribute_value where attribute_id = 2) AS total_color,
    (SELECT COUNT(*) FROM product_order) AS total_order
      `;

    return this.manager.query(query);
  }


  public async findUserByApiKey(apiKey: string): Promise<User | null> {
    // const queryBuilder = this.createQueryBuilder('user')
    //   .andWhere({ email })
    //   .leftJoinAndSelect('user.userType', 'userType')
    //   .leftJoinAndSelect('user.role', 'role')
    //   .leftJoinAndSelect('role.permissions', 'permissions')
    //   .orderBy('user.createdAt', 'DESC')

    // return queryBuilder.getOne();
    return this.createQueryBuilder('user')
      .leftJoinAndSelect('user.userType', 'userType')
      .leftJoinAndSelect('user.role', 'role')
      .leftJoinAndSelect('role.permissions', 'permissions')
      .where('user.apiKey = :apiKey', { apiKey })
      .orderBy('user.createdAt', 'DESC')
      .getOne();

  }
}
