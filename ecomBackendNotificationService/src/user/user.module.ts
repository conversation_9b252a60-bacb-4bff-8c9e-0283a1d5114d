import { MiddlewareConsumer, Module, RequestMethod } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserRepository } from './user.repository';
import { UserService } from './user.service';
import { UserController } from './user.controller';
import { User } from './entity/user.entity';
// import { AuthService } from 'src/auth/auth.service';
import { PasswordHasherMiddleware } from 'libs/common/middlewares/password-hasher.middleware';
import { JwtService } from '@nestjs/jwt';
import { AuthService } from 'src/auth/auth.service';
import { TokenManagementService } from 'libs/common/providers/token-management/token-management.service';
// import { JwtTokenVerifierMiddleware } from '../../common/middlewares/jwt-token-verifier.middleware';
// import { TokenManagementModule } from '../../common/providers/token-management/token-management.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([User]),
  ],
  providers: [
    UserService,
    UserRepository,
    AuthService,
    JwtService,
    TokenManagementService
  ],
  controllers: [UserController],
  exports: [UserService],
})
export class UserModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(PasswordHasherMiddleware).forRoutes(
      { path: 'user', method: RequestMethod.POST }, // create route
    );
  }
}
