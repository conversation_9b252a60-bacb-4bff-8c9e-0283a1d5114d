import { Injectable } from '@nestjs/common';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { UserRepository } from './user.repository';
import { User } from './entity/user.entity';
import { UpdateProfileDto } from './dto/update-profile.dto';
// import { MailService } from 'src/common/services/mail.service';
import { ServiceResponse } from 'libs/common/utils/service-response';
import { PageOptionsDto } from 'libs/common/pagination/page-options.dto';

@Injectable()
export class UserService {
  constructor(
    private readonly repository: UserRepository,
    // private mailService: MailService,
  ) { }

  async findAll(): Promise<ServiceResponse> {
    const result = await this.repository.findAll();
    if (!result) {
      return new ServiceResponse(result, 'User not found');
    }
    return new ServiceResponse(result, 'All Users found successfully');
  }
  async findAllDataByPagination(
    userTypeId: number,
    params: { globalSearch?: string },
    pageOptionsDto: PageOptionsDto,
  ): Promise<ServiceResponse> {
    const result = await this.repository.findAllDataByPagination(
      userTypeId,
      params,
      pageOptionsDto,
    );
    if (!result) {
      return new ServiceResponse(result, 'User not found');
    }
    return new ServiceResponse(
      result.itemList,
      'All user found successfully',
      result.meta,
    );
  }
  async findAllUserListByPagination(
    params: { globalSearch?: string },
    pageOptionsDto: PageOptionsDto,
  ): Promise<ServiceResponse> {
    const result = await this.repository.findAllUserListByPagination(
      params,
      pageOptionsDto,
    );
    if (!result) {
      return new ServiceResponse(null, 'User not found');
    }
    return new ServiceResponse(
      result.itemList,
      'All user found successfully',
      result.meta,
    );
  }

  async create(
    createUserDto: CreateUserDto,
    domain?: string,
  ): Promise<ServiceResponse> {
    const result = await this.repository.add(createUserDto);
    if (!result) {
      return new ServiceResponse(
        result,
        'Failed to saved User! Please try again',
      );
    }

    const email = result.email;
    let baseUrl = process.env.FRONTEND_URL;

    if (domain === 'adminpanel.pantoneclo.com') {
      baseUrl = `https://${domain}/login`; // Replace with your production domain
    }
    else {
      baseUrl = `https://${domain}/auth/login`;
    }

    const html = `
    <div style="text-align: center;
          background-color: #000;
          margin: 0 0 22px 0;
          padding: 10px;">
      <img src="https://pantoneclosbucket.s3.eu-central-1.amazonaws.com/pantoneclo-img/logo-white.png" width="250" alt="pantoneclo logo">
  </div>

      <div style="text-align: center;">
        <p style="color: #58aed4;">Thanks for joining</p>
        <br/>
        <h1>Your registration is complete</h1>
        
        <br>
        <br>
        <a href="${baseUrl}" style="background-color: #78b642; color: white; text-decoration: none; padding: 7px 10px; font-size: 18px;">Log In To Your Account</a>
      </div>
      
    <br> 
    <br><br>
    
      <div style="text-align: center; margin-top:30px">
          
          <div style="padding-top: 30px">
            <div style="margin-bottom: 10px; font-size: 30px;color: #999999;font-weight: 600;">Follow us</div>
            <div style="padding-top: 10px;">
              <table style="width: 100%;">
                <tr align="center">
                  <td style="width: 33%;">
                  </td>
                  <td style="width: 33%;">
                    <table>
                      <tbody>
                        <tr>
                          <td>
                            <a href="https://www.facebook.com/profile.php?id=**************" target="_blank" >
                              <img src="https://pantoneclosbucket.s3.eu-central-1.amazonaws.com/pantoneclo-img/fb.png" width="30" alt="pantoneclo logo" />
                            </a>
                          </td>
                          <td>
                            <a href="https://www.instagram.com/pantoneclo/" target="_blank" >
                              <img src="https://pantoneclosbucket.s3.eu-central-1.amazonaws.com/pantoneclo-img/insta.png" width="30" alt="pantoneclo logo" />
                            </a>
                          </td>
                          <td>
                            <a href="https://www.linkedin.com/company/98777995/admin/dashboard/" target="_blank" >
                              <img src="https://pantoneclosbucket.s3.eu-central-1.amazonaws.com/pantoneclo-img/in.png" width="30" alt="pantoneclo logo" />
                            </a>
                          </td>
                          <td>
                            <a href="https://x.com/pantoneclo" target="_blank" >
                              <img src="https://pantoneclosbucket.s3.eu-central-1.amazonaws.com/pantoneclo-img/x.png" width="30" alt="pantoneclo logo" />
                            </a>
                          </td>
                          <td>
                            <a href="https://www.youtube.com/embed/usnQk469EWs" target="_blank" >
                              <img src="https://pantoneclosbucket.s3.eu-central-1.amazonaws.com/pantoneclo-img/youtube.png" width="30" alt="pantoneclo logo" />
                            </a>
                          </td>
                          <td>
                            <a href="https://www.tiktok.com/@pantoneclo" target="_blank" >
                              <img src="https://pantoneclosbucket.s3.eu-central-1.amazonaws.com/pantoneclo-img/tiktok.png" width="30" alt="pantoneclo logo" />
                            </a>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </td>
                  <td style="width: 33%;">
                  </td>
                </tr>
              </table>
            </div>

            <!-- Rest of the template remains the same -->
          </div>
          
          
          <div style="margin-top: 10px;">
              Need a break from out emails? You can <a href="https://pantoneclo.com/unsubscribe?email=${email}" target="_blank" >unsubscribe here</a> 
          </div>
          <div>PANTONECLO © All rights reserved, all data are protected by the EU intellectual property.</div>
      </div>
    `;

    // await this.mailService.sendMail(email, 'Thanks for joining', html, null);

    return new ServiceResponse(result, 'User saved successfully');
  }

  async findById(id: number): Promise<ServiceResponse> {
    if (!id) {
      return new ServiceResponse(
        null,
        `Please provide valid product id! Try again`,
      );
    }
    const result = await this.repository.findById(id);
    if (!result) {
      return new ServiceResponse(
        result,
        `User not found for id:${id}! Please try again`,
      );
    }
    return new ServiceResponse(result, `User data found for id:${id}`);
  }

  async update(
    id: number,
    updateUserDto: UpdateUserDto,
  ): Promise<ServiceResponse> {
    if (!id) {
      return new ServiceResponse(
        null,
        `Please provide valid product id! Try again`,
      );
    }
    const result = await this.repository.updateOne(id, updateUserDto);
    if (!result) {
      return new ServiceResponse(
        result,
        'Failed to update User! Please try again',
      );
    }
    return new ServiceResponse(result, 'User successfully updated');
  }
  async updateUserProfile(
    id: number,
    updateProfileDto: UpdateProfileDto,
  ): Promise<ServiceResponse> {
    if (!id) {
      return new ServiceResponse(
        null,
        `Please provide valid product id! Try again`,
      );
    }

    const isExist = await this.repository.findById(id);
    if (!isExist) {
      return new ServiceResponse(null, 'User not found! Please try again');
    }
    const result = await this.repository.updateUserProfile(
      id,
      updateProfileDto,
    );
    if (!result) {
      return new ServiceResponse(
        result,
        'Failed to update User! Please try again',
      );
    }
    return new ServiceResponse(result, 'User successfully updated');
  }
  async setDefaultAddress(
    userId: number,
    addressId?: number,
  ): Promise<ServiceResponse> {
    if (!userId) {
      return new ServiceResponse(
        null,
        `Please provide valid user id! Try again`,
      );
    }

    const isExist = await this.repository.findById(userId);
    if (!isExist) {
      return new ServiceResponse(null, 'User not found! Please try again');
    }

    let result = null;
    if (userId && addressId) {
      result = await this.repository.setDefaultAddress(userId, addressId);
    }

    // if(userId && shippingAddressId){
    //   result = await this.repository.setDefaultShippingAddress(userId, shippingAddressId);
    // }

    if (!result) {
      return new ServiceResponse(
        result,
        'Failed to update User! Please try again',
      );
    }
    return new ServiceResponse(result, 'User address successfully updated');
  }

  async delete(id: number): Promise<ServiceResponse> {
    if (!id) {
      return new ServiceResponse(
        null,
        `Please provide valid product id! Try again`,
      );
    }
    const result = await this.repository.destroy(id);
    if (!result) {
      return new ServiceResponse(
        result,
        `Failed to delete User! Please try again`,
      );
    }
    return new ServiceResponse(id, `User successfully deleted`);
  }

  async restoreData(id: number): Promise<ServiceResponse> {
    if (!id) {
      return new ServiceResponse(
        null,
        `Please provide valid product id! Try again`,
      );
    }
    const result = await this.repository.restoreData(id);
    if (!result) {
      return new ServiceResponse(
        result,
        `Failed to restore User id: ${id}! Please try again`,
      );
    }
    return new ServiceResponse(result, `User id: ${id} successfully restored`);
  }

  async activeOrInactive(
    id: number,
    status: boolean,
  ): Promise<ServiceResponse> {
    if (!id) {
      return new ServiceResponse(
        null,
        `Please provide valid product id! Try again`,
      );
    }
    const result = await this.repository.activeOrInactive(id, status);
    if (!result) {
      return new ServiceResponse(
        result,
        'Failed to restore User! Please try again',
      );
    }
    return new ServiceResponse(result, 'User successfully actived');
  }

  async findUserByEmail(email: string): Promise<User | null> {
    return this.repository.findUserByEmail(email);
  }

  async emailExistCheckingByEmail(email: string): Promise<ServiceResponse> {
    if (!email) {
      return new ServiceResponse(null, `Please provide valid email! Try again`);
    }
    const result = await this.repository.findUserByEmail(email);
    if (!result) {
      return new ServiceResponse(null, 'User not Exist');
    }
    return new ServiceResponse(result, 'User Exist');
  }

  async reportCustomerDemographicsList(
    pageOptionsDto: PageOptionsDto,
  ): Promise<ServiceResponse> {
    const result =
      await this.repository.reportCustomerDemographicsList(pageOptionsDto);
    if (!result) {
      return new ServiceResponse(result, `No data found.`);
    }
    return new ServiceResponse(
      result.itemList,
      'All data found successfully',
      result.meta,
    );
  }

  async adminDashboardCards(): Promise<ServiceResponse> {
    const result = await this.repository.adminDashboardCards();
    if (!result && result?.length == 0) {
      return new ServiceResponse(null, `No data found.`);
    }
    return new ServiceResponse(result[0], 'All data found successfully');
  }

  async excelDownloadReportCustomerDemographicsList(): Promise<any[]> {
    const result =
      await this.repository.excelDownloadReportCustomerDemographicsList();
    if (!result) {
      return null;
    }
    return result;
  }


  async findUserByApiKey(email: string): Promise<User | null> {
    return this.repository.findUserByApiKey(email);
  }
}
