import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import {
  Length,
  IsString,
  IsOptional,
  IsNotEmpty,
  IsBoolean,
  IsDate,
  IsDefined,
  IsEmail,
  IsNumber,
  MinDate,
} from 'class-validator';
import { IsCustomEmail } from 'libs/common/decorators/email-format.decorator';
// import { CheckPasswordStrength } from 'src/common/decorators/check-password-strength.decorator';

export class UpdateUserDto {
  @ApiPropertyOptional()
  @IsOptional()
  id: number;

  @ApiPropertyOptional()
  @IsOptional()
  firstName: string;

  @ApiPropertyOptional()
  @IsOptional()
  lastName: string;

  @ApiPropertyOptional()
  @Length(1, 100)
  @IsString()
  @IsNotEmpty()
  @IsCustomEmail()
  @IsOptional()
  email: string;

  @ApiPropertyOptional()
  @Length(1, 11)
  @IsString()
  @IsOptional()
  phone: string;

  @ApiPropertyOptional()
  @IsString()
  password: string;

  @ApiPropertyOptional()
  @IsOptional()
  dob: Date;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  gender: string;

  @ApiPropertyOptional()
  @IsOptional()
  userTypeId: number;
  // @ApiProperty()
  // @IsBoolean()
  // isVerified: boolean;

  // @ApiPropertyOptional()
  // @IsDefined()
  // @IsNumber()
  // userTypeId: number;

  @ApiPropertyOptional()
  @IsOptional()
  addressId: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  roleId?: number;

}
