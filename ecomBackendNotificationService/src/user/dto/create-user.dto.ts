import {
  Length,
  IsString,
  IsNotEmpty,
  IsDefined,
  <PERSON>N<PERSON>ber,
  IsOptional,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsCustomEmail } from 'libs/common/decorators/email-format.decorator';
// import { IsCustomEmail } from 'src/common/decorators/email-format.decorator';
// import { CheckPasswordStrength } from 'src/common/decorators/check-password-strength.decorator';
// import { IsUnique } from 'src/common/decorators/is-unique.decorator';
// import { Transform } from 'class-transformer';
// import { Role } from 'src/domain/roles/entities/role.entity';

export class CreateUserDto {
  @ApiPropertyOptional()
  @IsOptional()
  firstName: string;

  @ApiPropertyOptional()
  @IsOptional()
  lastName: string;

  @ApiProperty()
  @Length(1, 100)
  @IsString()
  @IsNotEmpty()
  @IsCustomEmail()
  email: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  phone: string;

  @ApiPropertyOptional()
  countryCode: number;

  @ApiProperty()
  @Length(1, 100)
  @IsString()
  @IsNotEmpty()
  password: string;

  @ApiPropertyOptional()
  @IsOptional()
  dob: Date;

  @ApiPropertyOptional()
  @IsOptional()
  gender: string;

  // @ApiPropertyOptional()
  // @IsOptional()
  // userTypeId: number = 2;

  // @ApiProperty()
  // @IsBoolean()
  // isVerified: boolean;

  // @ApiPropertyOptional()
  // @IsDefined()
  // @IsNumber()
  // userTypeId: number;

  @ApiPropertyOptional()
  @IsOptional()
  addressId: number;

  // @ApiPropertyOptional()
  // @IsOptional()
  // loginId: number;
  
  // @ApiPropertyOptional()
  // @IsOptional()
  // role: number;

  @ApiPropertyOptional()
  apiKey: string
}
