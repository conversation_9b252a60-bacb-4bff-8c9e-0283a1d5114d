import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import { Length, IsString, IsOptional } from 'class-validator';
import { IsCustomEmail } from 'libs/common/decorators/email-format.decorator';

export class UpdateProfileDto {
  @ApiPropertyOptional()
  @IsOptional()
  id: number;

  @ApiPropertyOptional()
  @IsOptional()
  firstName: string;

  @ApiPropertyOptional()
  @IsOptional()
  lastName: string;

  @ApiPropertyOptional()
  @Length(1, 100)
  @IsString()
  @IsCustomEmail()
  @IsOptional()
  email: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  phone: string;

  @ApiPropertyOptional()
  countryCode: number;

  // @ApiPropertyOptional()
  // @Length(1, 100)
  // @IsString()
  // @CheckPasswordStrength()
  // password: string;

  @ApiPropertyOptional()
  @IsOptional()
  dob: Date;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  gender: string;

  @ApiPropertyOptional()
  @IsOptional()
  imageUrl: string;

  @ApiPropertyOptional()
  @IsOptional()
  userTypeId: number;

  @ApiPropertyOptional()
  @IsOptional()
  updatedBy: number;

  @ApiPropertyOptional()
  @IsOptional()
  password: string;

}
