import { AbstractEntity } from 'libs/common/database/entity/abstract.entity';
import { IUser } from 'libs/common/interfaces/user.interface';
import { Entity, Column, ManyToOne, JoinColumn, OneToOne } from 'typeorm';
// import { Address } from './address.entity';
// import { Login } from './login.entity';
// import { AbstractEntity } from './abstract.entity';
// import { UserType } from './user-type.entity';
// import { AddressOrder } from './address-order.entity';
// import { Role } from '../roles/entities/role.entity';

@Entity()
export class User extends AbstractEntity implements IUser {
    @Column({ name: 'first_name', length: 100, default: null, nullable: true })
    firstName: string;

    @Column({ name: 'last_name', length: 100, default: null, nullable: true })
    lastName: string;

    @Column({ unique: true, length: 100 })
    email: string;

    @Column({ name: 'phone', default: null, nullable: true })
    phone: string;

    @Column({ select: false })
    password: string;

    @Column({ type: 'date', default: null, nullable: true })
    dob: Date;

    @Column({ default: null, nullable: true })
    gender: string;

    @Column({ name: 'is_verified', default: false })
    isVerified: boolean;

    // @Column({ name: 'user_type_id', nullable: true })
    // userTypeId: number;

    @Column({ name: 'address_id', default: null, nullable: true })
    addressId: number;

    @Column({ name: 'user_type_id', default: null, nullable: true })
    userTypeId: number;

    @Column({ name: 'image_url', default: null, nullable: true })
    imageUrl: string;

    // @Column({ name: 'login_id' })
    // loginId: number;

    @Column({ name: 'country_code', default: null, nullable: true })
    countryCode: number;

    @Column({ name: 'api_key', default: null, nullable: true})
    apiKey: string;

    // @ManyToOne(() => Address, { eager: true })
    // @JoinColumn({ name: 'address_id', referencedColumnName: 'id' })
    // address: Address;

    // @ManyToOne(() => AddressOrder, { eager: true })
    // @JoinColumn({ name: 'shipping_address_id', referencedColumnName: 'id' })
    // shippingAddress: AddressOrder;

    // @ManyToOne(() => Login, { eager: true })
    // @JoinColumn({ name: 'login_id', referencedColumnName: 'id' })
    // login: Login;

    // @ManyToOne(() => UserType, { eager: true })
    // @JoinColumn({ name: 'user_type_id', referencedColumnName: 'id' })
    // userType: UserType;

    // @ManyToOne(() => Role, (role) => role.users, { eager: true })
    // @JoinColumn({ name: 'role_id', referencedColumnName: 'id' })
    // role: Role;
}
