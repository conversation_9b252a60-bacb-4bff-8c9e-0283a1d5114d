import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Put,
  Query, Res,
  UseGuards
} from '@nestjs/common';
import { Post, UsePipes, ValidationPipe } from '@nestjs/common';
import { UpdateUserDto } from './dto/update-user.dto';
import { CreateUserDto } from './dto/create-user.dto';
import { UserService } from './user.service';
import { UpdateProfileDto } from './dto/update-profile.dto';
import { Response } from 'express';
// import { PermissionsGuard } from 'src/application/auth/guards/permissions.guard';
// import { JwtAuthGuard } from 'src/application/auth/guards/jwt-auth.guard';
// import { Permissions } from 'src/common/decorators/permissions.decorator';
// import { ModulesEnum } from '../permissions/enum/modules.enum';
// import { DynamicModuleEnum, DynamicPermissionEnum } from '../permissions/enum/permission.enum';
// import { CurrentDomain } from '../country/current-domain.decorator';
import { ApiTags } from '@nestjs/swagger';
import { PageOptionsDto } from 'libs/common/pagination/page-options.dto';
import { JwtService } from '@nestjs/jwt';

@ApiTags('User')
@Controller('user')
@UsePipes(new ValidationPipe())
export class UserController {
  constructor(
    private readonly service: UserService,
    // private readonly excelService: ExcelService,
  ) { }

  // @UseGuards(
  //   JwtAuthGuard,
  //   PermissionsGuard
  // )
  // @Permissions(
  //   `${DynamicModuleEnum.USER}:${DynamicPermissionEnum.READ_MANY}`,
  //   `${DynamicModuleEnum.USER}:${DynamicPermissionEnum.READ_ONE}`
  // )
  @Get()
  public async findAll() {
    return this.service.findAll();
  }


  @UseGuards(JwtService)
  // @Permissions(`${DynamicModuleEnum.USER}:${DynamicPermissionEnum.READ_MANY}`)
  @Get('/findAllDataByPagination')
  public async findAllDataByPagination(
    @Query('userTypeId') userTypeId: number,
    @Query() params: { globalSearch?: string },
    // @Query() params: { firstName?: string; lastName?: string; phone?: string; email?: string, sortOrderColumn:string },
    @Query() pageOptionsDto: PageOptionsDto,
  ) {
    return this.service.findAllDataByPagination(
      userTypeId,
      params,
      pageOptionsDto,
    );
  }

  // @UseGuards(JwtAuthGuard, PermissionsGuard)
  // @Permissions(`${DynamicModuleEnum.USER}:${DynamicPermissionEnum.READ_MANY}`)
  @Get('/userListByPagination')
  public async findAllUserListByPagination(
    @Query() params: { globalSearch?: string },
    @Query() pageOptionsDto: PageOptionsDto,
  ) {
    return this.service.findAllUserListByPagination(params, pageOptionsDto);
  }

  // @UseGuards(JwtAuthGuard, PermissionsGuard)
  // @Permissions(`${DynamicModuleEnum.USER}:${DynamicPermissionEnum.READ_ONE}`)
  @Get('/:id')
  public async getById(@Param('id') id: number) {
    return this.service.findById(id);
  }

  @Get('emailExistChecking/:email')
  public async emailExistCheckingByEmail(@Param('email') email: string) {
    return this.service.emailExistCheckingByEmail(email);
  }

  // @UseGuards(JwtAuthGuard, PermissionsGuard)
  // @Permissions(`${DynamicModuleEnum.USER}:${DynamicPermissionEnum.CREATE}`)
  @Post()
  public async create(
    // @CurrentDomain() domain: string,
    @Body() createUserDto: CreateUserDto,
  ) {
    return this.service.create(createUserDto);
  }

  // @UseGuards(JwtAuthGuard, PermissionsGuard)
  // @Permissions(`${DynamicModuleEnum.USER}:${DynamicPermissionEnum.UPDATE}`)
  @Put('/:id')
  public async update(
    @Param('id') id: number,
    @Body() updateUserDto: UpdateUserDto,
  ) {
    return this.service.update(id, updateUserDto);
  }

  // @UseGuards(JwtAuthGuard, PermissionsGuard)
  // @Permissions(`${DynamicModuleEnum.USER}:${DynamicPermissionEnum.UPDATE}`)
  @Put('updateUserProfile/:id')
  public async updateUserProfile(
    @Param('id') id: number,
    @Body() updateUserDto: UpdateProfileDto,
  ) {
    return this.service.updateUserProfile(id, updateUserDto);
  }

  // @UseGuards(JwtAuthGuard)
  @Post('web/setDefaultAddress')
  public async setDefaultAddress(
    @Query('userId') userId: number,
    @Query('addressId') addressId?: number,
    // @Query('shippingAddressId') shippingAddressId?: number,
  ) {
    return this.service.setDefaultAddress(userId, addressId);
  }

  // @UseGuards(JwtAuthGuard, PermissionsGuard)
  // @Permissions(`${DynamicModuleEnum.USER}:${DynamicPermissionEnum.SOFT_DELETE}`)
  // @Delete('delete/:id')
  // public async delete(@Param('id') id: number) {
  //   return this.service.delete(id);
  // }

  // @UseGuards(JwtAuthGuard, PermissionsGuard)
  // @Permissions(`${DynamicModuleEnum.USER}:${DynamicPermissionEnum.RESTORE}`)
  @Post('restore/:id')
  public async restoreData(@Param('id') id: number) {
    return this.service.restoreData(id);
  }

  // @UseGuards(JwtAuthGuard, PermissionsGuard)
  // @Permissions(`${DynamicModuleEnum.USER}:${DynamicPermissionEnum.ACTIVE_DEACTIVE}`)
  @Post('activeOrInactive')
  public async activeOrInactive(
    @Query('id') id: number,
    @Query('status') status: boolean,
  ) {
    return this.service.activeOrInactive(id, status);
  }

  // @UseGuards(JwtAuthGuard)
  @Get('report/customerDemographics')
  public async reportCustomerDemographicsList(
    @Query() pageOptionsDto: PageOptionsDto,
    // @Query('invoiceNo') invoiceNo: string,
  ) {
    return this.service.reportCustomerDemographicsList(pageOptionsDto);
  }

  // @UseGuards(JwtAuthGuard)
  @Get('admin/dashboard')
  public async adminDashboardCards() {
    return this.service.adminDashboardCards();
  }

  // @UseGuards(JwtAuthGuard)
  // @Get('/report/excelDownload/customerDemographics')
  // public async excelDownloadReportCustomerDemographicsList(
  //   @Res() res: Response,
  // ): Promise<any> {
  //   const list =
  //     await this.service.excelDownloadReportCustomerDemographicsList();
  //   const data = await this.excelService.generateExcel(list);
  //   return res.download(data);
  // }
}
