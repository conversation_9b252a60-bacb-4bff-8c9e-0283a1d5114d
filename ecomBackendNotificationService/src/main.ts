import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { SwaggerService } from 'libs/swagger';
import { appConfig, IAppConfig } from './config/app-config';
import { HttpExceptionFilter } from 'libs/common/filters/http-exception.filter';
import { GlobalExceptionFilter } from 'libs/common/filters/global-exception-filter';
import { TransformInterceptor } from 'libs/common/interceptors/transform.interceptor';
import { useContainer, ValidationError } from 'class-validator';
import { ValidationPipe } from '@nestjs/common';
import { urlencoded, json } from 'express';
import compression from 'compression';
import helmet from 'helmet';
import { configDotenv } from 'dotenv';

configDotenv({
  path: `.env`,
});

async function bootstrap() {
  const app = await NestFactory.create(AppModule, {
    forceCloseConnections: false,
  });

  // Enable Swagger
  const swagger = new SwaggerService();
  swagger.setup(app);

  // Enable Swagger Stats Middleware
  swagger.swaggerStats(app);


  app.use(json({ limit: '50mb' }));
  app.use(urlencoded({ extended: true, limit: '50mb' }));

  app.use(helmet());
  app.use(compression());

  if (process.env.NODE_ENV === 'production') {
    // app.use(csurf());
    // app.use(
    //   rateLimit({
    //     windowMs: 15 * 60 * 1000, // 15 minutes
    //     max: 100, // Limit each IP to 100 requests per windowMs
    //   }),
    // );
  }

  app.enableShutdownHooks();

  // SwaggerModule.setup('swagger', app, document, { swaggerOptions: { basePath: 'api' } });
  // Disable CORS

  // Read allowed domains from environment variable and split into an array
  const allowedDomains = process.env.ALLOWED_DOMAINS
    ? process.env.ALLOWED_DOMAINS.split(',').map((domain) => domain.trim())
    : '*';

  app.enableCors({
    origin: '*', // Allows all origins
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
    credentials: true,
  });

  // app.useGlobalFilters(
  //   new HttpExceptionFilter(),
  //   new GlobalExceptionFilter(),
  //   // new ValidationExceptionFilter(),
  // );
  // app.useGlobalInterceptors(new TransformInterceptor());
  
  useContainer(app.select(AppModule), { fallbackOnErrors: true });
  // app.useGlobalFilters(new SentryExceptionFilter());
  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      // exceptionFactory: (errors: ValidationError[]) => {
      //   console.log('error', errors);
      //   return errors.map(error => ({
      //     field: error.property,
      //     constraints: Object.values(error.constraints),
      //   }));
      // },
      exceptionFactory: (errors: ValidationError[]) => errors,
    }),
  );



  app.setGlobalPrefix('api');

  app.getHttpAdapter().get('/', (req, res) => {
    res.json({
      status: 200,
      message: 'Pantoneclo Notification Service up and running',
      error: null,
    });
  });

  app.getHttpAdapter().get('/error-test', (req, res) => {
    throw new Error('My first Sentry error!');
  });


  const config: IAppConfig = app.get<IAppConfig>(appConfig.KEY);

  // console.log('appConfig.KEY:::', appConfig.KEY);
  // console.log('process.env.PORT:::', process.env.PORT);


  await app.listen(config.port, () => {
    console.log(`Server is listening on port ${config.port}`);
  });
}
bootstrap();
