import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
// import { PasswordUtil } from '../../common/utils/password.util';
import { Socket } from 'socket.io';
// import { User } from '../../domain/entities/user.entity';
// import { IJwtConfig } from '../../config/jwt-config';
import { ConfigService } from '@nestjs/config';
// import { TokenManagementService } from '../../common/providers/token-management/token-management.service';
// import { CreateUserDto } from 'src/domain/user/dto/create-user.dto';
import { JwtPayload } from './dto/jwt-payload.interface';
import { ResetPasswordDto } from './dto/password-reset.dto';
import { IJwtConfig } from 'src/config/jwt-config';
import { UserService } from 'src/user/user.service';
import { TokenManagementService } from 'libs/common/providers/token-management/token-management.service';
import { ServiceResponse } from 'libs/common/utils/service-response';
import { PasswordUtil } from 'libs/common/utils/password.util';
import { LoginnResponse } from 'libs/common/utils/login-response';
import { CreateUserDto } from 'src/user/dto/create-user.dto';
import { UserChangePasswordDto } from 'src/user/dto/user-change-password.dto';
import { User } from 'src/user/entity/user.entity';
import { UserForgetPasswordDto } from 'src/user/dto/user-forget-password.dto';
// import { UserService } from 'src/domain/user/user.service';
// import { LoginService } from 'src/domain/login/login.service';
// import { ServiceResponse } from 'src/common/utils/service-response';
// import { LoginnResponse } from 'src/common/utils/login-response';
// import { UserChangePasswordDto } from 'src/domain/user/dto/user-change-password.dto';
// import { MailService } from 'src/common/services/mail.service';
// import { UserForgetPasswordDto } from 'src/domain/user/dto/user-forget-password.dto';
// import { CACHE_MANAGER } from '@nestjs/cache-manager';
// import { Cache } from 'cache-manager';
// import { Role } from 'src/domain/roles/entities/role.entity';

interface CustomSocket extends Socket {
  jwtPayload?: JwtPayload;
}

@Injectable()
export class AuthService {
  private jwtConfig: IJwtConfig;

  constructor(
    private userService: UserService,
    // private loginService: LoginService,
    private jwtService: JwtService,
    // private permissionsService: PermissionsService,
    // private mailService: MailService,
    private configService: ConfigService,
    // private roleService: RoleService,
    private tokenManagementService: TokenManagementService,
    // @Inject(CACHE_MANAGER) private cacheManager: Cache,
  ) {
    this.jwtConfig = this.configService.get('jwt') as IJwtConfig;
  }

  async login(
    email: string,
    password: string,
    // userTypeId: number,
  ): Promise<ServiceResponse> {
    const user = await this.userService.findUserByEmail(email);
    if (user && !user?.isActive && user?.deletedAt !== null) {
      throw new HttpException(
        'User blocked! Please contact with admin',
        HttpStatus.UNAUTHORIZED,
      );
    }

    // if (user && user?.userTypeId != userTypeId) {
    //   throw new HttpException(
    //     `User type not match! Only ${user?.userType.userType} can login`,
    //     HttpStatus.UNAUTHORIZED,
    //   );
    // }

    // const loginUser = await this.loginService.findUserByEmail(email);
    const loginResponse = new LoginnResponse();

    console.log('user?.password:::', user?.password);
    console.log('password:::', password);
    
    if (user?.email) {
      const passwordMatch = await PasswordUtil.comparePassword(
        password,
        user?.password,
      );

      if (!passwordMatch) {
        throw new HttpException('Invalid credentials', HttpStatus.UNAUTHORIZED);
      }

      const tokenPayload = this.generateTokenPayload(user);
      const accessToken = await this.jwtService.signAsync(tokenPayload);
      // console.log('user', user);
      loginResponse.id = user.id;
      loginResponse.firstName = user.firstName;
      loginResponse.lastName = user.lastName;
      loginResponse.email = user.email;
      loginResponse.gender = user.gender;
      loginResponse.token = accessToken;
      // loginResponse.testTokenPayload = tokenPayload;
    } else {
      throw new HttpException(`User not found`, HttpStatus.UNAUTHORIZED);
    }
    // const redirectUrl = this.generateRedirectUrl(user.role.name);

    return new ServiceResponse(loginResponse, 'Login successful');
  }

  // async registerCustomer(
  //   createUserDto: CreateUserDto,
  //   domain: string,
  // ): Promise<ServiceResponse> {
  //   // const loginUser = await this.loginService.findUserByEmail(
  //   //   createUserDto?.email,
  //   // );
  //   const userExist = await this.userService.findUserByEmail(
  //     createUserDto.email,
  //   );
  //   if (userExist) {
  //     throw new HttpException('User already exist', HttpStatus.CONFLICT);
  //   }

  //   // const role = await this.roleService.findRoleByName(UserRole.Customer);
  //   // console.log('userExist', role);

  //   const newLoginUser = await this.loginService.create(createUserDto);
  //   createUserDto.loginId = newLoginUser?.data?.id;
  //   return this.userService.create(createUserDto, domain);

  //   // return this.userService.create({ ...createUserDto, roleId: role.id });
  // }

  // async changePassword(
  //   id: number,
  //   changePassDto: UserChangePasswordDto,
  // ): Promise<ServiceResponse> {
  //   return this.userService.updatePassword(id, changePassDto);
  // }

  // findAllPermissionsOfRole(role_id: number): Promise<Permission[]> {
  //   return this.permissionsService.findAllPermissionsOfRole(role_id);
  // }

  private generateTokenPayload(user: User) {
    const payload: JwtPayload = {
      sub: user?.id,
      user: {
        firstName: user?.firstName,
        lastName: user?.lastName,
        email: user?.email,
        // userTypeId: user.userTypeId,
        // userType: {
        //   id: user?.userType?.id,
        //   isActive: user?.userType?.isActive,
        //   userType: user?.userType?.userType,
        // },
        // // role: user?.role,
        // role: {
        //   id: user?.role?.id,
        //   name: user?.role?.name,
        // } as Role,
      },
    };

    return payload;
  }

  // private generateRedirectUrl(role: UserRole): string {
  //   let redirectUrl = '';

  //   if (role === UserRole.Admin) {
  //     redirectUrl = '/admin';
  //   } else if (role === UserRole.Employee) {
  //     redirectUrl = '/employee';
  //   } else if (role == UserRole.Customer) {
  //     redirectUrl = '/';
  //   }

  //   return redirectUrl;
  // }

  async logout(token: string) {
    if (!this.tokenManagementService.addTokenToBlacklist(token)) {
      throw new HttpException('Revoking token failed', HttpStatus.BAD_REQUEST);
    }
    return true;
  }

  async sendAccountVerificationLinkToEmail(
    email: string,
  ): Promise<{ message: string }> {
    try {
      const verificationToken = this.jwtService.sign(
        { email },
        { expiresIn: `${this.jwtConfig.jwt_expires_in}` },
      );

      // await this.mailService.sendVerificationEmail(email, verificationToken);

      return { message: `Verification link sent successfully to: ${email}` };
    } catch (error) {
      throw new HttpException(
        'Unable to send verification link',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async verifyUserAccount(verificationToken: string): Promise<ServiceResponse> {
    try {
      const { email } = this.jwtService.verify(verificationToken) as {
        email: string;
      };
      const userExist = await this.userService.findUserByEmail(email);

      if (!userExist) {
        throw new HttpException('User not found', HttpStatus.BAD_REQUEST);
      }

      userExist.isVerified = true;
      return this.userService.update(userExist.id, userExist);
    } catch (error) {
      throw new HttpException(
        'Invalid verification token',
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  async sendResetPasswordLinkToEmail(
    resetPasswordDto: ResetPasswordDto,
    domain?: string,
  ): Promise<ServiceResponse> {
    try {
      const email = resetPasswordDto.email;
      const user = await this.userService.findUserByEmail(email);

      if (!user) {
        // throw new HttpException('User not found', HttpStatus.BAD_REQUEST);
        return new ServiceResponse(null, `Email not found! Try again`);
      }

      const resetPasswordToken = this.jwtService.sign(
        { sub: user.id },
        {
          expiresIn: `${this.jwtConfig.jwt_expires_in}`,
        },
      );

      const baseUrl = domain ? `https://${domain}` : process.env.FRONTEND_URL;

      const html = `
      
    <div style="text-align: center;
            background-color: #000;
            margin: 0 0 22px 0;
            padding: 10px;">
        <img src="https://pantoneclosbucket.s3.eu-central-1.amazonaws.com/pantoneclo-img/logo-white.png" width="250" alt="pantoneclo logo">
    </div>

      Click the link below to reset your pantoneclo.com account password. This link is valid for 24 hours:
      <br> <br>
          <a href="${baseUrl}/auth/reset-password?token=${resetPasswordToken}">Click here to reset your password</a>
      <br> <br>
                If you have any questions, feedback or suggestions please let us know!
      <br>
      - Pantoneclo Support Team

      
        <div style="text-align: center; margin-top:30px">
            
            <div style="padding-top: 30px">
              <div style="margin-bottom: 10px; font-size: 30px;color: #999999;font-weight: 600;">Follow us</div>
              <div style="padding-top: 10px;">
                <table style="width: 100%;">
                  <tr align="center">
                    <td style="width: 33%;">
                    </td>
                    <td style="width: 33%;">
                      <table>
                        <tbody>
                          <tr>
                            <td>
                              <a href="https://www.facebook.com/profile.php?id=61563869323043" target="_blank" >
                                <img src="https://pantoneclosbucket.s3.eu-central-1.amazonaws.com/pantoneclo-img/fb.png" width="30" alt="pantoneclo logo" />
                              </a>
                            </td>
                            <td>
                              <a href="https://www.instagram.com/pantoneclo/" target="_blank" >
                                <img src="https://pantoneclosbucket.s3.eu-central-1.amazonaws.com/pantoneclo-img/insta.png" width="30" alt="pantoneclo logo" />
                              </a>
                            </td>
                            <td>
                              <a href="https://www.linkedin.com/company/98777995/admin/dashboard/" target="_blank" >
                                <img src="https://pantoneclosbucket.s3.eu-central-1.amazonaws.com/pantoneclo-img/in.png" width="30" alt="pantoneclo logo" />
                              </a>
                            </td>
                            <td>
                              <a href="https://x.com/pantoneclo" target="_blank" >
                                <img src="https://pantoneclosbucket.s3.eu-central-1.amazonaws.com/pantoneclo-img/x.png" width="30" alt="pantoneclo logo" />
                              </a>
                            </td>
                            <td>
                              <a href="https://www.youtube.com/embed/usnQk469EWs" target="_blank" >
                                <img src="https://pantoneclosbucket.s3.eu-central-1.amazonaws.com/pantoneclo-img/youtube.png" width="30" alt="pantoneclo logo" />
                              </a>
                            </td>
                            <td>
                              <a href="https://www.tiktok.com/@pantoneclo" target="_blank" >
                                <img src="https://pantoneclosbucket.s3.eu-central-1.amazonaws.com/pantoneclo-img/tiktok.png" width="30" alt="pantoneclo logo" />
                              </a>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </td>
                    <td style="width: 33%;">
                    </td>
                  </tr>
                </table>
              </div>

              <!-- Rest of the template remains the same -->
            </div>
            
            
            <div style="margin-top: 10px;">
                Need a break from out emails? You can <a href="https://pantoneclo.com/unsubscribe?email=${user.email}" target="_blank" >unsubscribe here</a> 
            </div>
            <div>PANTONECLO © All rights reserved, all data are protected by the EU intellectual property.</div>
        </div>
      `;

      // await this.mailService.sendMail(user.email, 'Reset Password', html, null);
      const _message = `Link to reset password was sent successfully to ${user.email}. You have 1 day before the link expires.`;
      return new ServiceResponse(
        `Link to reset password was sent successfully to ${user.email}. You have 1 day before the link expires.`,
        _message,
      );
      // return {
      //   message: `Link to reset password was sent successfully to ${user.email}. You have 1 day before the link expires.`,
      // };
    } catch (error) {
      console.log(error);
      throw new HttpException(
        'Unable to send link to reset password.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // async resetUserPassword(
  //   resetPassDto: UserForgetPasswordDto,
  // ): Promise<ServiceResponse> {
  //   try {
  //     const decodedToken = this.jwtService.verify(resetPassDto?.resetToken) as {
  //       sub: number;
  //     };

  //     return this.loginService.resetPassword(decodedToken.sub, resetPassDto);
  //   } catch (error) {
  //     throw new HttpException(
  //       'Invalid reset password token',
  //       HttpStatus.BAD_REQUEST,
  //     );
  //   }
  // }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  verifySocketJwtToken(socket: CustomSocket): boolean {
    // try {
    //   const token = JwtUtil.extractTokenFromSocketHeader(socket);

    //   if (!token) {
    //     throw new WsException('Missing JWT token');
    //   }

    //   const payload: JwtPayload = this.jwtService.verify(token, { secret: 'secret' });

    //   if (payload.role == UserRole.Customer || payload.role == UserRole.Employee) {
    //     socket.jwtPayload = payload;
    //     return true;
    //   }

    //   return false;
    // } catch (error) {
    //   throw new WsException(`Invalid JWT Token ErrorMessage: ${error}`);
    // }
    return true;
  }

  validateToken(token: string) {
    return this.jwtService.verify(token, {
      secret: 'secret@1234'
    });
  }

  // async clearCache(): Promise<ServiceResponse> {
  //   const result = await this.cacheManager.clear(); // Clears all cache
  //   // or
  //   // await this.cacheManager.del('specific_key'); // Clears specific key
  //   return new ServiceResponse(
  //     result,
  //     'Cache cleared successfully',
  //   );
  // }
}
