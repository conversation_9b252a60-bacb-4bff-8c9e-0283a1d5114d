import { CanActivate, ExecutionContext, Injectable, UnauthorizedException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { UserService } from 'src/user/user.service';

@Injectable()
export class ApiKeyGuard implements CanActivate {
    constructor(
        private reflector: Reflector,
        private usersService: UserService
    ) { }

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const request = context.switchToHttp().getRequest();
        const apiKey = request.headers['x-api-key'];

        if (!apiKey) throw new UnauthorizedException('API Key missing');

        const user = await this.usersService.findUserByApiKey(apiKey);
        if (!user) throw new UnauthorizedException('Invalid API Key');

        request.user = user;
        return true;
    }
}
