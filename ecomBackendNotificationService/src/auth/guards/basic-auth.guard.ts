import { CanActivate, ExecutionContext, Injectable, UnauthorizedException } from '@nestjs/common';
import { Request } from 'express';

@Injectable()
export class BasicAuthGuard implements CanActivate {
    private readonly USERNAME = process.env.BASIC_AUTH_USERNAME || 'pantoneclo_basic_auth';
    private readonly PASSWORD = process.env.BASIC_AUTH_PASSWORD || 'pantoneclo_basic_auth@123';

    canActivate(context: ExecutionContext): boolean {
        const request = context.switchToHttp().getRequest<Request>();
        const authHeader = request.headers.authorization;

        if (!authHeader || !authHeader.startsWith('Basic ')) {
            throw new UnauthorizedException('Missing Authorization Header');
        }

        const base64Credentials = authHeader.split(' ')[1];
        const credentials = Buffer.from(base64Credentials, 'base64').toString('ascii');
        const [username, password] = credentials.split(':');

        if (
            username !== this.USERNAME ||
            password !== this.PASSWORD
        ) {
            throw new UnauthorizedException('Invalid credentials');
        }

        return true;
    }
}
