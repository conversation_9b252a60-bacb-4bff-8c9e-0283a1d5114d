import {
    Controller,
    Post,
    Put,
    Get,
    Param,
    Body,
    Query,
    ParseIntPipe,
    UseGuards,
} from '@nestjs/common';
import { ApiBasicAuth, ApiTags } from '@nestjs/swagger';
import { TranslationService } from './translation.service';
import { PageOptionsDto } from 'libs/common/pagination/page-options.dto';
import { CreateTranslationDto } from './dto/create-translation.dto';
import { CreateTranslationByEmailDto } from './dto/create-translation-by-email.dto';
import { CreateTranslationBatchDto } from './dto/create-translation-batch.dto';
import { BasicAuthGuard } from 'src/auth/guards/basic-auth.guard';

@ApiTags('translation')
@Controller('translation')
export class TranslationController {
    constructor(private readonly currentService: TranslationService) { }

    // @Post()
    // async createTemplate(
    //     @Body() body: CreateTranslationDto,
    // ) {
    //     return this.currentService.createTemplate(body);
    // }

    // @Put(':id')
    // async updateTemplate(
    //     @Param('id', ParseIntPipe) id: number,
    //     @Body() body: UpdateTranslationDto,
    // ) {
    //     return this.currentService.updateTemplate(id, body);
    // }

    // @Get(':id')
    // async getTemplateByCountry(
    //     @Param('id', ParseIntPipe) id: number,
    //     @Query('countryId', ParseIntPipe) countryId: number,
    //     // @Query('locale') locale?: string,
    // ) {
    //     return this.currentService.getTemplateByCountryId(id, countryId);
    // }

    @ApiBasicAuth()
    @UseGuards(BasicAuthGuard)
    @Post('add/byTemplate/:template')
    async addTranslation(
        @Param('template') template: number,
        // @Query('country', ParseIntPipe) country: Country,
        // @Query('locale') locale: string,
        // @Body() body: {
        //     subject: string;
        //     htmlContent: string;
        //     mjmlJson?: any,
        //     emailTemplates: {
        //         key: string;
        //         baseText: string;
        //         section: string;
        //         translations: Record<string, any>;
        //     }[]
        // }
        @Body() body: CreateTranslationByEmailDto
    ) {
        return this.currentService.addTranslation(template, body);
    }

    // @UseGuards(JwtAuthGuard, PermissionsGuard)
    // @Permissions(`${DynamicModuleEnum.PRODUCT}:${DynamicPermissionEnum.READ_MANY}`)
    @ApiBasicAuth()
    @UseGuards(BasicAuthGuard)
    @Get('findAllDataByPagination')
    public async findAllDataByPagination(
        @Query() pageOptionsDto: PageOptionsDto,
    ) {
        return this.currentService.findAllDataByPagination(pageOptionsDto);
    }



    @ApiBasicAuth()
    @UseGuards(BasicAuthGuard)
    @Post('new/byTemplate/:templateId')
    async createTranslation(
        @Param('templateId') templateId: number,
        @Body() body: CreateTranslationBatchDto,
    ) {
        return this.currentService.createTranslations(templateId, body);
    }
}