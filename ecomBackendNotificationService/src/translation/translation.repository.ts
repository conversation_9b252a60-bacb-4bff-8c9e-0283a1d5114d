import { Injectable } from '@nestjs/common';
import { Repository, DataSource } from 'typeorm';
import { TranslationKey, TranslationValue } from './entity/translation.entity';
import { CreateTranslationDto } from './dto/create-translation.dto';
import { EmailTemplate } from '../mail-template/entity/email-template.entity';
import { PageOptionsDto } from 'libs/common/pagination/page-options.dto';
import { PageDto } from 'libs/common/pagination/page.dto';
import { PageMetaDto } from 'libs/common/pagination/page-meta.dto';
import { CreateTranslationByEmailDto } from './dto/create-translation-by-email.dto';
import { CreateTranslationBatchDto } from './dto/create-translation-batch.dto';

@Injectable()
export class TranslationRepository extends Repository<TranslationKey> {
  constructor(
    private readonly dataSource: DataSource,
    // private readonly translationValueRepository: Repository<TranslationValue>,
  ) {
    super(
      TranslationKey,
      dataSource.createEntityManager(),
      dataSource.createQueryRunner(),
    );
  }

  public async findAll(): Promise<TranslationKey[]> {
    return this.find();
  }
  public async findAllDataByPagination(
    pageOptionsDto: PageOptionsDto,
  ): Promise<PageDto<TranslationKey>> {
    const [list, itemCount] = await this.findAndCount({
      skip: pageOptionsDto.skip,
      take: pageOptionsDto.take,
      withDeleted: false,
      relations: ['translationValues'],
    });

    const pageMetaDto = new PageMetaDto({ itemCount, pageOptionsDto });

    return new PageDto(list, pageMetaDto);
  }

  public async findById(id: number): Promise<TranslationKey | null> {
    return this.findOneBy({ id: id });
  }

  // public async add(createTranslationDto: CreateTranslationDto): Promise<TranslationKey> {
  //   const newSeller = this.create(createTranslationDto);
  //   return this.save(newSeller);
  // }

  // public async updateOne(id: number, sellerDto: UpdateEmailTemplatedDto): Promise<number> {
  //   const updatedResult = await this.update(id, sellerDto);
  //   let data = null;
  //   if (updatedResult.affected > 0) {
  //     data = await this.findOne({ where: { id }, relations: ['translations'] });
  //   }
  //   return data;
  // }

  // public async destroy(id: number): Promise<number> {
  //   const result = await this.softDelete(id);
  //   return result.affected;
  // }

  // public async restoreData(id: number): Promise<number> {
  //   const result = await this.restore(id);
  //   return result.affected;
  // }

  // async getTemplateByCountryId(id: number, countryId: number) {
  //   const template = await this.findOne({ where: { id }, relations: ['translations'] });
  //   if (!countryId) return template;
  //   const translation = template.translations.find((t) => (t.country.id === countryId));  // (t.locale === locale) || 
  //   return {
  //     ...template,
  //     subject: translation?.subject ?? template.subject,
  //     htmlContent: translation?.htmlContent ?? template.htmlContent
  //   };
  // }

  // async addTranslation(templateId: number, country: Country, translationData: { subject: string; htmlContent: string; mjmlJson?: any }) {
  //   const template = await this.findOne({ where: { id: templateId } });
  //   const translation = this.emailTranslationRepo.create({ ...translationData, country, template });
  //   return this.emailTranslationRepo.save(translation);
  // }

  // async addTranslation(
  //   template: number,
  //   body: {
  //     subject: string;
  //     htmlContent: string;
  //     mjmlJson?: any,
  //     emailTemplates: [
  //       {
  //         key: string;
  //         baseText: string;
  //         translations: Record<string, any>;
  //       }
  //     ]
  //   }
  // ) {

  //   console.log('template:::', template);
  //   console.log('body:::', body);

  //   for (const item of body.emailTemplates) {
  //     const key = new TranslationKey();
  //     key.key = item.key;
  //     key.baseText = item.baseText;
  //     // key.template = Number(template); // fetched template
  //     await this.save(key);

  //     for (const [locale, value] of Object.entries(item.translations)) {
  //       const valueEntry = new TranslationValue();
  //       valueEntry.locale = locale;
  //       valueEntry.value = value;
  //       valueEntry.translationKey = key;
  //       await this.translationValueRepository.save(valueEntry);
  //     }
  //   }

  //   return this.findBy({ template });
  // }


  /**
   * Working method to add translation
   * @param templateId 
   * @param body 
   * @returns 
   */
  // async addTranslation(
  //   templateId: number,
  //   body: CreateTranslationByEmailDto
  // ) {
  //   const queryRunner = this.dataSource.createQueryRunner();
  //   await queryRunner.connect();
  //   await queryRunner.startTransaction();

  //   try {
  //     // Find the template first (optional: validate existence)
  //     const template = await queryRunner.manager.findOneOrFail(EmailTemplate, {
  //       where: { id: templateId },
  //     });

  //     for (const item of body.emailTemplates) {
  //       const key = new TranslationKey();
  //       key.key = item.key;
  //       key.baseText = item.baseText;
  //       key.section = item.section;
  //       key.template = template; // Set proper relation

  //       const savedKey = await queryRunner.manager.save(TranslationKey, key);

  //       for (const [locale, value] of Object.entries(item.translations)) {
  //         const valueEntry = new TranslationValue();
  //         valueEntry.locale = locale;
  //         valueEntry.value = value;
  //         valueEntry.translationKey = savedKey;

  //         await queryRunner.manager.save(TranslationValue, valueEntry);
  //       }
  //     }

  //     await queryRunner.commitTransaction();
  //     return queryRunner.manager.find(TranslationKey, {
  //       // where: { template: { id: templateId } },
  //       relations: ['translationValues'],
  //     });
  //   } catch (error) {
  //     console.log('error:::', error);

  //     await queryRunner.rollbackTransaction();
  //     throw error;
  //   } finally {
  //     await queryRunner.release();
  //   }
  // }


  async addTranslation(
    templateId: number,
    body: CreateTranslationByEmailDto,
  ) {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const template = await queryRunner.manager.findOneOrFail(EmailTemplate, {
        where: { id: templateId },
      });

      for (const item of body.emailTemplates) {
        // Check if the key already exists for the template
        let existingKey = await queryRunner.manager.findOne(TranslationKey, {
          where: {
            key: item.key,
            template: { id: templateId },
          },
          relations: ['translationValues'],
        });

        if (!existingKey) {
          // Create new key
          existingKey = new TranslationKey();
          existingKey.key = item.key;
          existingKey.baseText = item.baseText;
          existingKey.section = item.section;
          existingKey.template = template;

          existingKey = await queryRunner.manager.save(TranslationKey, existingKey);
        } else {
          // Update baseText or section if changed
          existingKey.baseText = item.baseText;
          existingKey.section = item.section;
          await queryRunner.manager.save(TranslationKey, existingKey);
        }

        // Process translation values
        for (const [locale, value] of Object.entries(item.translations)) {
          let valueEntry = existingKey.translationValues?.find(
            (val) => val.locale === locale,
          );

          if (valueEntry) {
            // Update existing translation
            valueEntry.value = value;
          } else {
            // Create new translation value
            valueEntry = new TranslationValue();
            valueEntry.locale = locale;
            valueEntry.value = value;
            valueEntry.translationKey = existingKey;
          }

          await queryRunner.manager.save(TranslationValue, valueEntry);
        }
      }

      await queryRunner.commitTransaction();

      return queryRunner.manager.find(TranslationKey, {
        where: { template: { id: templateId } },
        relations: ['translationValues'],
      });
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }




  async createTranslations(templateId: number, body: CreateTranslationBatchDto) {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const template = await queryRunner.manager.findOneOrFail(EmailTemplate, {
        where: { id: templateId },
      });

      for (const item of body.emailTemplates) {
        const key = queryRunner.manager.create(TranslationKey, {
          key: item.key,
          baseText: item.baseText,
          section: item.section,
          template,
        });

        const savedKey = await queryRunner.manager.save(TranslationKey, key);

        for (const [locale, value] of Object.entries(item.translations)) {
          const valueEntry = queryRunner.manager.create(TranslationValue, {
            locale,
            value,
            translationKey: savedKey,
          });

          await queryRunner.manager.save(TranslationValue, valueEntry);
        }
      }

      await queryRunner.commitTransaction();

      return queryRunner.manager.find(TranslationKey, {
        where: { template: { id: templateId } },
        relations: ['translationValues'],
      });
    } catch (error) {
      console.error('Translation create error:', error);
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

}
