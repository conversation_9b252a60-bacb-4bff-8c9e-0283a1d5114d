import { Injectable } from "@nestjs/common";
import { TranslationRepository } from "./translation.repository";
import { ServiceResponse } from "libs/common/utils/service-response";
import { PageOptionsDto } from "libs/common/pagination/page-options.dto";
import { CreateTranslationDto } from "./dto/create-translation.dto";
import { CreateTranslationByEmailDto } from "./dto/create-translation-by-email.dto";
import { EmailTemplate } from "src/mail-template/entity/email-template.entity";
import { TranslationKey, TranslationValue } from "./entity/translation.entity";
import { CreateTranslationBatchDto } from "./dto/create-translation-batch.dto";

// 5. EmailTemplateService
@Injectable()
export class TranslationService {
    constructor(
        // @InjectRepository(EmailTemplate) private templateRepo: Repository<EmailTemplate>,
        // @InjectRepository(EmailTranslation) private translationRepo: Repository<EmailTranslation>
        private readonly translationRepo: TranslationRepository,
    ) { }

    async addTranslation(
        template: number,
        body: CreateTranslationByEmailDto
    ): Promise<ServiceResponse> {
        const result = await this.translationRepo.addTranslation(template, body);
        if (!result) {
            return new ServiceResponse(null, 'Failed to add translation! Please try again');
        }
        return new ServiceResponse(result, 'Translation added successfully');
    }

    async findAllDataByPagination(
        pageOptionsDto: PageOptionsDto,
    ): Promise<ServiceResponse> {
        // const result =
        //   await this.repository.findAllDataByPagination(pageOptionsDto);
        const result = await this.translationRepo.findAllDataByPagination(pageOptionsDto);
        if (!result) {
            return new ServiceResponse(result, 'Translation not found');
        }
        return new ServiceResponse(
            result.itemList,
            'All translations found successfully',
            result.meta,
        );
    }


    async createTranslations(templateId: number, body: CreateTranslationBatchDto) {
        const result = await this.translationRepo.createTranslations(templateId, body);
        if (!result) {
            return new ServiceResponse(null, 'Failed to add translation! Please try again');
        }
        return new ServiceResponse(result, 'Translation added successfully');
    }
}
