import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany } from "typeorm";
import { EmailTemplate, EmailTranslation } from "../../mail-template/entity/email-template.entity";
import { ITranslationKey, ITranslationValue } from "../interface/translation.interface";
import { AbstractEntity } from "../../../libs/common/database/entity/abstract.entity";

@Entity('translation_keys')
export class TranslationKey extends AbstractEntity implements ITranslationKey {
    @ManyToOne(() => EmailTemplate, (t) => t.translationKeys)
    @JoinColumn({ name: 'template_id' })
    template: number | EmailTemplate;

    @ManyToOne(() => EmailTranslation, (t) => t.translationKeys)
    @JoinColumn({ name: 'email_translation_id' })
    emailTranslation: number | EmailTemplate;

    @Column('varchar', { name: 'key' })
    key: string;

    @Column('text', { name: 'base_text' })
    baseText: string;

    @Column('varchar', { name: 'section', nullable: true })
    section?: string; // optional grouping e.g., 'footer', 'body', 'global'

    @OneToMany(() => TranslationValue, (v) => v.translationKey, { cascade: true })
    translationValues: TranslationValue[];
}


@Entity('translation_values')
export class TranslationValue extends AbstractEntity implements ITranslationValue {
    @ManyToOne(() => TranslationKey, (k) => k.translationValues)
    @JoinColumn({ name: 'translation_key_id' })
    translationKey: TranslationKey;

    @Column('varchar', { length: 2 })
    locale: string;

    @Column('text')
    value: string;
}

