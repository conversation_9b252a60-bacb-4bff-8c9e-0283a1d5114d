import { ApiProperty } from '@nestjs/swagger';
import {
  IsArray,
  IsObject,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

export class EmailTemplateTranslationDto {
  @ApiProperty({ example: 'greeting', description: 'Translation key identifier' })
  @IsString()
  key: string;

  @ApiProperty({ example: 'Hello, user!', description: 'Default (base) text for this key' })
  @IsString()
  baseText: string;

  @ApiProperty({ example: 'header', description: 'Section of the email where this translation is used' })
  @IsString()
  @IsOptional()
  section?: string;

  @ApiProperty({
    example: {
      en: 'Hello, user!',
      de: '<PERSON><PERSON>, <PERSON><PERSON><PERSON>!',
      fr: 'Bonjour, utilisateur!',
    },
    description: 'Object mapping locales to their translated values',
  })
  @IsObject()
  translations: { [locale: string]: string };
}

export class CreateTranslationByEmailDto {
  @ApiProperty({
    type: [EmailTemplateTranslationDto],
    description: 'List of translation keys and their localized values',
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => EmailTemplateTranslationDto)
  emailTemplates: EmailTemplateTranslationDto[];
}
