import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import { Column } from 'typeorm';
import {
  IsString,
  IsOptional,
  IsNotEmpty,
  IsNumber
} from 'class-validator';
import { CreateTranslationDto } from './create-translation.dto';

export class UpdateTranslationDto extends PartialType(CreateTranslationDto) {
  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  id: number;

}
