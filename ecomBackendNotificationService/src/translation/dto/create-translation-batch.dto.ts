import { IsArray, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { CreateTranslationKeyDto } from './create-translation-key.dto';
import { ApiBody, ApiProperty } from '@nestjs/swagger';

export class CreateTranslationBatchDto {
    @ApiProperty({
        type: [CreateTranslationKeyDto],
        isArray: true,
        example: [
            {
                key: 'test',
                baseText: 'test',
                section: 'test',
                translations: {
                    en: 'test',
                    es: 'test',
                },
            },
        ]
    })
    
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => CreateTranslationKeyDto)
    emailTemplates: CreateTranslationKeyDto[];
}
