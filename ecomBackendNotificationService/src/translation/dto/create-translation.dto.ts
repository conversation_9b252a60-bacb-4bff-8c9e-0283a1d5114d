import {
  Is<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>E<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { ITranslationValue } from '../interface/translation.interface';
import { EmailTemplate } from 'src/mail-template/entity/email-template.entity';

export class CreateTranslationDto {
  @ApiPropertyOptional()
  template?: number | EmailTemplate;

  @ApiProperty()
  // @IsNotEmpty()
  // @IsString()
  key: string;

  @ApiProperty()
  // @IsNotEmpty()
  // @IsString()
  baseText: string;

  @ApiPropertyOptional()
  section?: string;

  // @ApiPropertyOptional()
  // translations?: ITranslationValue[];

}
