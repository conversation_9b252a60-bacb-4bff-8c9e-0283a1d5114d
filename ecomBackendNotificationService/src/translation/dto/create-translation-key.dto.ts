import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsObject } from 'class-validator';

export class CreateTranslationKeyDto {
    @ApiPropertyOptional()
    @IsString()
    @IsNotEmpty()
    key: string;

    @ApiPropertyOptional()
    @IsString()
    @IsNotEmpty()
    baseText: string;

    @ApiPropertyOptional()
    @IsString()
    @IsNotEmpty()
    section: string;

    @ApiPropertyOptional()
    @IsObject()
    translations: {
        [locale: string]: string;
    };
}
