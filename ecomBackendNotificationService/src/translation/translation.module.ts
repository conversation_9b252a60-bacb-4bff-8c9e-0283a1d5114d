import { MiddlewareConsumer, Module, RequestMethod } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TranslationKey, TranslationValue } from './entity/translation.entity';
import { TranslationService } from './translation.service';
import { TranslationRepository } from './translation.repository';
import { TranslationController } from './translation.controller';
import { MailService } from 'libs/common/services/mail.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([TranslationKey, TranslationValue]),
    // CacheModule.register({
    //   store: fsStore, // File system cache store
    //   options: {
    //     path: './cache', // Ensure this directory exists
    //     ttl: 3600000, // 1 hour
    //     maxsize: **********, // 1GB
    //     zip: true, // Compress cache
    //     preventfill: false, // Ensure cache is preloaded on start
    //   },
    // }),
  ],
  providers: [
    TranslationService,
    TranslationRepository,
    Repository<TranslationValue>,
    MailService,
  ],
  controllers: [TranslationController],
  exports: [TranslationService],
})
export class TranslationModule {}
