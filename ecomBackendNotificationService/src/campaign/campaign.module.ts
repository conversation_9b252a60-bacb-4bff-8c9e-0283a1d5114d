import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bullmq';
import { ScheduleModule } from '@nestjs/schedule';

// Controllers
import { CampaignController } from './campaign.controller';

// Services
import { CampaignService } from './campaign.service';
import { EmailGroupService } from './email-group.service';
import { CampaignReportService } from './campaign-report.service';

// Processors
import { CampaignProcessor } from './campaign.processor';

// Entities
import { EmailCampaign } from './entity/email-campaign.entity';
import { EmailGroup } from './entity/email-group.entity';
import { EmailGroupMember } from './entity/email-group-member.entity';
import { CampaignReport } from './entity/campaign-report.entity';
import { EmailTemplate } from '../mail-template/entity/email-template.entity';

// External Services
import { MailTemplateConvertionService } from '../mail/mail-template-cnvertion.service';
import { MailService } from '../../libs/common/services/mail.service';
import { EmailTemplateService } from '../mail-template/email-template.service';
import { EmailTemplateRepository } from '../mail-template/email-template.repository';
import { EmailSendHistory } from 'src/mail-template/entity/email-template-history.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      EmailCampaign,
      EmailGroup,
      EmailGroupMember,
      EmailSendHistory,
      CampaignReport,
      EmailTemplate,
    ]),
    BullModule.registerQueue(
      {
        name: 'campaign-queue',
        defaultJobOptions: {
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 2000,
          },
          removeOnComplete: 100,
          removeOnFail: 50,
        },
      },
      {
        name: 'email-queue',
        defaultJobOptions: {
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 1000,
          },
          removeOnComplete: 1000,
          removeOnFail: 100,
        },
      }
    ),
    // ScheduleModule.forRoot(),
  ],
  controllers: [CampaignController],
  providers: [
    CampaignService,
    EmailGroupService,
    CampaignReportService,
    CampaignProcessor,
    MailTemplateConvertionService,
    MailService,
    EmailTemplateService,
    EmailTemplateRepository,
  ],
  exports: [
    CampaignService,
    EmailGroupService,
    CampaignReportService,
  ],
})
export class CampaignModule {}
