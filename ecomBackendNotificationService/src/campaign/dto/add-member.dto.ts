import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEmail, IsString, IsOptional, IsNumber, IsEnum, IsObject, IsArray, IsDateString } from 'class-validator';
import { SubscriptionSource } from '../entity/email-group-member.entity';

export class AddMemberDto {
  @ApiProperty({ description: 'Email address', example: '<EMAIL>' })
  @IsEmail()
  email: string;

  @ApiPropertyOptional({ description: 'First name' })
  @IsString()
  @IsOptional()
  firstName?: string;

  @ApiPropertyOptional({ description: 'Last name' })
  @IsString()
  @IsOptional()
  lastName?: string;

  @ApiPropertyOptional({ description: 'Phone number' })
  @IsString()
  @IsOptional()
  phone?: string;

  @ApiPropertyOptional({ description: 'Country ID' })
  @IsNumber()
  @IsOptional()
  countryId?: number;

  @ApiPropertyOptional({ description: 'City' })
  @IsString()
  @IsOptional()
  city?: string;

  @ApiPropertyOptional({ description: 'Gender' })
  @IsString()
  @IsOptional()
  gender?: string;

  @ApiPropertyOptional({ description: 'Date of birth' })
  @IsDateString()
  @IsOptional()
  dateOfBirth?: string;

  @ApiProperty({ 
    description: 'Subscription source', 
    enum: SubscriptionSource,
    default: SubscriptionSource.MANUAL 
  })
  @IsEnum(SubscriptionSource)
  source: SubscriptionSource = SubscriptionSource.MANUAL;

  @ApiPropertyOptional({ description: 'Custom fields' })
  @IsObject()
  @IsOptional()
  customFields?: Record<string, any>;

  @ApiPropertyOptional({ description: 'Tags' })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  tags?: string[];

  @ApiPropertyOptional({ description: 'Email preferences' })
  @IsObject()
  @IsOptional()
  preferences?: {
    frequency?: 'daily' | 'weekly' | 'monthly';
    categories?: string[];
    language?: string;
    timezone?: string;
  };

  @ApiPropertyOptional({ description: 'IP address for tracking' })
  @IsString()
  @IsOptional()
  ipAddress?: string;

  @ApiPropertyOptional({ description: 'User agent for tracking' })
  @IsString()
  @IsOptional()
  userAgent?: string;

  @ApiPropertyOptional({ description: 'Referrer URL' })
  @IsString()
  @IsOptional()
  referrerUrl?: string;

  @ApiPropertyOptional({ description: 'UTM source' })
  @IsString()
  @IsOptional()
  utmSource?: string;

  @ApiPropertyOptional({ description: 'UTM medium' })
  @IsString()
  @IsOptional()
  utmMedium?: string;

  @ApiPropertyOptional({ description: 'UTM campaign' })
  @IsString()
  @IsOptional()
  utmCampaign?: string;
}
