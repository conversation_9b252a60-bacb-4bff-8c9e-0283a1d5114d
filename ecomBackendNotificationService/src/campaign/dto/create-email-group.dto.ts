import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsEnum, IsOptional, IsObject, IsArray, IsNumber, IsBoolean } from 'class-validator';
import { EmailGroupType } from '../entity/email-group.entity';

export class CreateEmailGroupDto {
  @ApiProperty({ description: 'Email group name', example: 'VIP Customers' })
  @IsString()
  name: string;

  @ApiPropertyOptional({ description: 'Email group description' })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ 
    description: 'Group type', 
    enum: EmailGroupType,
    default: EmailGroupType.STATIC 
  })
  @IsEnum(EmailGroupType)
  type: EmailGroupType = EmailGroupType.STATIC;

  @ApiPropertyOptional({ description: 'Segmentation criteria for dynamic groups' })
  @IsObject()
  @IsOptional()
  segmentationCriteria?: {
    countries?: number[];
    ageRange?: { min: number; max: number };
    gender?: string[];
    purchaseHistory?: {
      minOrders?: number;
      maxOrders?: number;
      minAmount?: number;
      maxAmount?: number;
      categories?: string[];
      brands?: string[];
    };
    engagement?: {
      lastOpenDays?: number;
      lastClickDays?: number;
      minOpenRate?: number;
      minClickRate?: number;
    };
    customFields?: Record<string, any>;
  };

  @ApiPropertyOptional({ description: 'Tags for organization' })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  tags?: string[];

  @ApiPropertyOptional({ description: 'User ID who created this group' })
  @IsNumber()
  @IsOptional()
  createdByUserId?: number;

  @ApiPropertyOptional({ description: 'Enable automatic synchronization', default: false })
  @IsBoolean()
  @IsOptional()
  autoSyncEnabled?: boolean = false;

  @ApiPropertyOptional({ description: 'Sync frequency in hours' })
  @IsNumber()
  @IsOptional()
  syncFrequencyHours?: number;
}
