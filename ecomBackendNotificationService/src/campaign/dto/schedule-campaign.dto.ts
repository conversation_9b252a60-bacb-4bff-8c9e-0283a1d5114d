import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsBoolean, IsDateString, IsOptional, IsString } from 'class-validator';

export class ScheduleCampaignDto {
  @ApiPropertyOptional({ 
    description: 'Send campaign immediately', 
    default: false 
  })
  @IsBoolean()
  @IsOptional()
  sendImmediately?: boolean = false;

  @ApiPropertyOptional({ 
    description: 'Scheduled send time (ISO string)', 
    example: '2024-12-25T10:00:00Z' 
  })
  @IsDateString()
  @IsOptional()
  scheduledAt?: string;

  @ApiPropertyOptional({ description: 'Notes about the scheduling' })
  @IsString()
  @IsOptional()
  notes?: string;
}
