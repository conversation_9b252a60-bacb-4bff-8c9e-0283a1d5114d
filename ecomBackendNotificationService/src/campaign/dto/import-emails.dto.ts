import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsEnum, IsArray, IsEmail } from 'class-validator';
import { SubscriptionSource } from '../entity/email-group-member.entity';

export class ImportEmailsDto {
  @ApiPropertyOptional({ description: 'Path to CSV file containing emails' })
  @IsString()
  @IsOptional()
  csvFile?: string;

  @ApiPropertyOptional({ 
    description: 'List of emails (comma or newline separated)',
    example: '<EMAIL>,<EMAIL>\<EMAIL>'
  })
  @IsString()
  @IsOptional()
  emailList?: string;

  @ApiPropertyOptional({ 
    description: 'Array of email addresses',
    example: ['<EMAIL>', '<EMAIL>']
  })
  @IsArray()
  @IsEmail({}, { each: true })
  @IsOptional()
  emails?: string[];

  @ApiProperty({ 
    description: 'Source of the import', 
    enum: SubscriptionSource,
    default: SubscriptionSource.IMPORT 
  })
  @IsEnum(SubscriptionSource)
  source: SubscriptionSource = SubscriptionSource.IMPORT;

  @ApiPropertyOptional({ description: 'Skip duplicate emails', default: true })
  @IsOptional()
  skipDuplicates?: boolean = true;

  @ApiPropertyOptional({ description: 'Validate email format', default: true })
  @IsOptional()
  validateEmails?: boolean = true;

  @ApiPropertyOptional({ description: 'Send welcome email to imported contacts', default: false })
  @IsOptional()
  sendWelcomeEmail?: boolean = false;

  @ApiPropertyOptional({ description: 'Tags to apply to imported contacts' })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  tags?: string[];
}
