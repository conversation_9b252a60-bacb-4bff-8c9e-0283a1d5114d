import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsObject, IsNumber, IsString } from 'class-validator';
import { ReportType } from '../entity/campaign-report.entity';

export class GenerateReportDto {
  @ApiProperty({ 
    description: 'Report type', 
    enum: ReportType,
    default: ReportType.SUMMARY 
  })
  @IsEnum(ReportType)
  type: ReportType = ReportType.SUMMARY;

  @ApiPropertyOptional({ description: 'Report filters' })
  @IsObject()
  @IsOptional()
  filters?: {
    dateRange?: { start: Date; end: Date };
    countries?: number[];
    emailGroups?: number[];
    segments?: string[];
  };

  @ApiPropertyOptional({ description: 'User ID generating the report' })
  @IsNumber()
  @IsOptional()
  userId?: number;

  @ApiPropertyOptional({ 
    description: 'Export format', 
    enum: ['pdf', 'excel', 'csv', 'json'] 
  })
  @IsEnum(['pdf', 'excel', 'csv', 'json'])
  @IsOptional()
  exportFormat?: 'pdf' | 'excel' | 'csv' | 'json';

  @ApiPropertyOptional({ description: 'Report expiration in days', default: 30 })
  @IsNumber()
  @IsOptional()
  expirationDays?: number = 30;
}
