import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsNumber,
  IsEnum,
  IsArray,
  IsOptional,
  IsEmail,
  IsObject,
  IsBoolean,
  ValidateNested,
  ArrayMinSize,
  <PERSON><PERSON><PERSON>th,
  <PERSON><PERSON>ength
} from 'class-validator';
import { Type } from 'class-transformer';
import { CampaignType, CampaignPriority } from '../../mail-template/enum/email-campaign.enum';

class CampaignSettingsDto {
  @ApiPropertyOptional({ description: 'Track email opens', default: true })
  @IsBoolean()
  @IsOptional()
  trackOpens?: boolean = true;

  @ApiPropertyOptional({ description: 'Track email clicks', default: true })
  @IsBoolean()
  @IsOptional()
  trackClicks?: boolean = true;

  @ApiPropertyOptional({ description: 'Enable unsubscribe link', default: true })
  @IsBoolean()
  @IsOptional()
  enableUnsubscribe?: boolean = true;

  @ApiPropertyOptional({ description: 'Send test email before campaign', default: false })
  @IsBoolean()
  @IsOptional()
  sendTestEmail?: boolean = false;

  @ApiPropertyOptional({ description: 'Throttle rate (emails per minute)', default: 100 })
  @IsNumber()
  @IsOptional()
  throttleRate?: number = 100;

  @ApiPropertyOptional({ description: 'Retry failed emails', default: true })
  @IsBoolean()
  @IsOptional()
  retryFailures?: boolean = true;

  @ApiPropertyOptional({ description: 'Maximum retry attempts', default: 3 })
  @IsNumber()
  @IsOptional()
  maxRetries?: number = 3;
}

class ABTestSettingsDto {
  @ApiPropertyOptional({ description: 'Enable A/B testing', default: false })
  @IsBoolean()
  @IsOptional()
  enabled?: boolean = false;

  @ApiPropertyOptional({ description: 'Subject line variants for testing' })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  subjectVariants?: string[];

  @ApiPropertyOptional({ description: 'Content variants for testing' })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  contentVariants?: string[];

  @ApiPropertyOptional({ description: 'Split percentage for A/B test', default: 50 })
  @IsNumber()
  @IsOptional()
  splitPercentage?: number = 50;

  @ApiPropertyOptional({
    description: 'Winner criteria',
    enum: ['open_rate', 'click_rate', 'conversion_rate'],
    default: 'open_rate'
  })
  @IsEnum(['open_rate', 'click_rate', 'conversion_rate'])
  @IsOptional()
  winnerCriteria?: 'open_rate' | 'click_rate' | 'conversion_rate' = 'open_rate';
}

export class CreateCampaignDto {
  @ApiProperty({ description: 'Campaign name', example: 'Summer Sale 2024' })
  @IsString()
  @MinLength(3)
  @MaxLength(100)
  name: string;

  @ApiPropertyOptional({ description: 'Campaign description' })
  @IsString()
  @IsOptional()
  @MaxLength(500)
  description?: string;

  @ApiProperty({ description: 'Email template ID', example: 1 })
  @IsNumber()
  templateId: number;

  @ApiProperty({
    description: 'Campaign type',
    enum: CampaignType,
    example: CampaignType.PROMOTIONAL
  })
  @IsEnum(CampaignType)
  type: CampaignType;

  @ApiPropertyOptional({
    description: 'Campaign priority',
    enum: CampaignPriority,
    default: CampaignPriority.MEDIUM
  })
  @IsEnum(CampaignPriority)
  @IsOptional()
  priority?: CampaignPriority = CampaignPriority.MEDIUM;

  @ApiPropertyOptional({ description: 'Email group IDs to target' })
  @IsArray()
  @IsNumber({}, { each: true })
  @IsOptional()
  emailGroups?: number[];

  @ApiPropertyOptional({ description: 'Custom email addresses to include' })
  @IsArray()
  @IsEmail({}, { each: true })
  @IsOptional()
  customEmails?: string[];

  @ApiPropertyOptional({ description: 'Sender name', example: 'PANTONECLO Team' })
  @IsString()
  @IsOptional()
  @MaxLength(50)
  senderName?: string;

  @ApiPropertyOptional({ description: 'Sender email address' })
  @IsEmail()
  @IsOptional()
  senderEmail?: string;

  @ApiPropertyOptional({ description: 'Reply-to email address' })
  @IsEmail()
  @IsOptional()
  replyTo?: string;

  @ApiProperty({ description: 'Email subject line', example: 'Don\'t Miss Our Summer Sale!' })
  @IsString()
  @MinLength(5)
  @MaxLength(150)
  subjectLine: string;

  @ApiPropertyOptional({ description: 'Email preheader text' })
  @IsString()
  @IsOptional()
  @MaxLength(100)
  preheaderText?: string;

  @ApiPropertyOptional({ description: 'Campaign settings' })
  @ValidateNested()
  @Type(() => CampaignSettingsDto)
  @IsOptional()
  settings?: CampaignSettingsDto;

  @ApiPropertyOptional({ description: 'Segmentation rules for dynamic targeting' })
  @IsObject()
  @IsOptional()
  segmentationRules?: any;

  @ApiPropertyOptional({ description: 'A/B testing settings' })
  @ValidateNested()
  @Type(() => ABTestSettingsDto)
  @IsOptional()
  abTestSettings?: ABTestSettingsDto;

  @ApiPropertyOptional({ description: 'Campaign tags for organization' })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  tags?: string[];

  @ApiPropertyOptional({ description: 'Total number of recipients' })
  @IsNumber()
  @IsOptional()
  totalRecipients?: number;
}
