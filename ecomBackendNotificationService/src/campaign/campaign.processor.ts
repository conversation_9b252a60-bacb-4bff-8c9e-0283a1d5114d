import { Processor, WorkerHost, OnWorkerEvent } from '@nestjs/bullmq';
import { Logger, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { Job } from 'bullmq';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';

import { EmailCampaign } from './entity/email-campaign.entity';
import { EmailGroup } from './entity/email-group.entity';
import { EmailGroupMember, MemberStatus } from './entity/email-group-member.entity';
import { EmailTemplate } from '../mail-template/entity/email-template.entity';

import {
  CampaignStatusEnum,
  CampaignDeliveryStatusEnum
} from '../mail-template/enum/email-campaign.enum';

import { MailTemplateConvertionService } from '../mail/mail-template-cnvertion.service';
import { MailService } from '../../libs/common/services/mail.service';
import { EmailSendHistory } from 'src/mail-template/entity/email-template-history.entity';

@Processor('campaign-queue')
@Injectable()
export class CampaignProcessor extends WorkerHost {
  private readonly logger = new Logger(CampaignProcessor.name);

  constructor(
    @InjectRepository(EmailCampaign)
    private campaignRepo: Repository<EmailCampaign>,

    @InjectRepository(EmailGroup)
    private emailGroupRepo: Repository<EmailGroup>,

    @InjectRepository(EmailGroupMember)
    private emailGroupMemberRepo: Repository<EmailGroupMember>,

    @InjectRepository(EmailSendHistory)
    private sendHistoryRepo: Repository<EmailSendHistory>,

    @InjectRepository(EmailTemplate)
    private emailTemplateRepo: Repository<EmailTemplate>,

    @InjectQueue('email-queue')
    private emailQueue: Queue,

    private readonly mailTemplateService: MailTemplateConvertionService,
    private readonly mailService: MailService,
  ) {
    super();
  }

  async process(job: Job<any>): Promise<any> {
    switch (job.name) {
      case 'process-campaign':
        return await this.processCampaign(job);
      case 'send-scheduled-campaign':
        return await this.sendScheduledCampaign(job);
      case 'send-campaign-email':
        return await this.sendCampaignEmail(job);
      default:
        this.logger.warn(`Unknown job type: ${job.name}`);
    }
  }

  /**
   * Process campaign - prepare and queue individual emails
   */
  private async processCampaign(job: Job): Promise<void> {
    const { campaignId } = job.data;

    try {
      const campaign = await this.campaignRepo.findOne({
        where: { id: campaignId },
        relations: ['template']
      });

      if (!campaign) {
        throw new Error(`Campaign ${campaignId} not found`);
      }

      if (campaign.status !== CampaignStatusEnum.SENDING) {
        this.logger.warn(`Campaign ${campaignId} is not in sending status`);
        return;
      }

      this.logger.log(`Processing campaign: ${campaign.name} (ID: ${campaignId})`);

      // Get email template
      const template = await this.emailTemplateRepo.findOne({
        where: { id: campaign.templateId }
      });

      if (!template) {
        throw new Error(`Template ${campaign.templateId} not found`);
      }

      // Collect all recipients
      const recipients = await this.collectRecipients(campaign);

      this.logger.log(`Found ${recipients.length} recipients for campaign ${campaignId}`);

      // Update campaign with actual recipient count
      campaign.totalRecipients = recipients.length;
      await this.campaignRepo.save(campaign);

      // Render email template
      const renderedContent = await this.renderEmailTemplate(template, campaign);

      // Queue individual emails with throttling
      const throttleRate = campaign.settings?.throttleRate || 100; // emails per minute
      const delayBetweenEmails = Math.ceil(60000 / throttleRate); // milliseconds

      for (let i = 0; i < recipients.length; i++) {
        const recipient = recipients[i];

        // Create send history record
        const sendHistory = this.sendHistoryRepo.create({
          campaignId: campaign.id,
          emailTemplateId: campaign.templateId,
          recipientEmail: recipient.email,
          countryId: recipient.countryId || 1,
          locale: recipient.locale || 'en',
          status: CampaignDeliveryStatusEnum.QUEUED,
        });

        const savedHistory = await this.sendHistoryRepo.save(sendHistory);

        // Queue email with delay for throttling
        await this.emailQueue.add(
          'send-campaign-email',
          {
            campaignId: campaign.id,
            sendHistoryId: savedHistory.id,
            recipient,
            emailContent: renderedContent,
            subject: campaign.subjectLine,
            senderEmail: campaign.senderEmail || process.env.SES_FROM_MAIL,
            senderName: campaign.senderName || 'PANTONECLO',
            replyTo: campaign.replyTo,
          },
          {
            delay: i * delayBetweenEmails,
            attempts: campaign.settings?.maxRetries || 3,
            backoff: {
              type: 'exponential',
              delay: 2000,
            },
          }
        );
      }

      this.logger.log(`Queued ${recipients.length} emails for campaign ${campaignId}`);

    } catch (error) {
      this.logger.error(`Error processing campaign ${campaignId}:`, error);

      // Mark campaign as failed
      await this.campaignRepo.update(campaignId, {
        status: CampaignStatusEnum.FAILED
      });

      throw error;
    }
  }

  /**
   * Send scheduled campaign
   */
  private async sendScheduledCampaign(job: Job): Promise<void> {
    const { campaignId } = job.data;

    const campaign = await this.campaignRepo.findOne({ where: { id: campaignId } });

    if (!campaign) {
      this.logger.warn(`Scheduled campaign ${campaignId} not found`);
      return;
    }

    if (campaign.status !== CampaignStatusEnum.SCHEDULED) {
      this.logger.warn(`Campaign ${campaignId} is not scheduled`);
      return;
    }

    // Update status and start processing
    campaign.status = CampaignStatusEnum.SENDING;
    campaign.startedAt = new Date();
    await this.campaignRepo.save(campaign);

    // Queue for processing
    await this.emailQueue.add('process-campaign', { campaignId });
  }

  /**
   * Send individual campaign email
   */
  private async sendCampaignEmail(job: Job): Promise<void> {
    const {
      campaignId,
      sendHistoryId,
      recipient,
      emailContent,
      subject,
      senderEmail,
      senderName,
      replyTo
    } = job.data;

    try {
      // Update send history status
      await this.sendHistoryRepo.update(sendHistoryId, {
        status: CampaignDeliveryStatusEnum.SENDING
      });

      // Personalize content for recipient
      const personalizedContent = await this.personalizeContent(
        emailContent,
        recipient
      );

      // Send email
      await this.mailService.sendMail(
        recipient.email,
        subject,
        personalizedContent,
        senderEmail,
        null, // attachments
        // null, // cc
        // null, // bcc
        // senderName,
        // replyTo
      );

      // Update send history
      await this.sendHistoryRepo.update(sendHistoryId, {
        status: CampaignDeliveryStatusEnum.SENT,
        sentAt: new Date(),
        messageBody: JSON.parse(personalizedContent)
      });

      // Update campaign sent count
      await this.campaignRepo.increment(
        { id: campaignId },
        'sentCount',
        1
      );

      this.logger.debug(`Email sent to ${recipient.email} for campaign ${campaignId}`);

    } catch (error) {
      this.logger.error(`Failed to send email to ${recipient.email}:`, error);

      // Update send history with error
      await this.sendHistoryRepo.update(sendHistoryId, {
        status: CampaignDeliveryStatusEnum.FAILED,
        errorMessage: error.message
      });

      throw error;
    }
  }

  /**
   * Collect all recipients for a campaign
   */
  private async collectRecipients(campaign: EmailCampaign): Promise<any[]> {
    const recipients = [];

    // Get recipients from email groups
    if (campaign.emailGroups?.length > 0) {
      const groupMembers = await this.emailGroupMemberRepo.find({
        where: {
          groupId: In(campaign.emailGroups),
          status: MemberStatus.ACTIVE
        }
      });

      recipients.push(...groupMembers.map(member => ({
        email: member.email,
        firstName: member.firstName,
        lastName: member.lastName,
        countryId: member.countryId,
        locale: member.preferences?.language || 'en',
        customFields: member.customFields,
        source: 'group'
      })));
    }

    // Add custom emails
    if (campaign.customEmails?.length > 0) {
      recipients.push(...campaign.customEmails.map(email => ({
        email,
        firstName: '',
        lastName: '',
        countryId: 1,
        locale: 'en',
        source: 'custom'
      })));
    }

    // Remove duplicates based on email
    const uniqueRecipients = recipients.filter((recipient, index, self) =>
      index === self.findIndex(r => r.email === recipient.email)
    );

    return uniqueRecipients;
  }

  /**
   * Render email template with campaign data
   */
  private async renderEmailTemplate(
    template: EmailTemplate,
    campaign: EmailCampaign
  ): Promise<string> {
    const templateData = {
      campaign: {
        name: campaign.name,
        type: campaign.type,
        preheaderText: campaign.preheaderText,
      },
      // Add more template variables as needed
    };

    return await this.mailTemplateService.renderTemplate(
      null,
      template.htmlContent,
      templateData
    );
  }

  /**
   * Personalize content for individual recipient
   */
  private async personalizeContent(content: string, recipient: any): Promise<string> {
    let personalizedContent = content;

    // Replace personalization tokens
    const replacements = {
      '{{firstName}}': recipient.firstName || 'Valued Customer',
      '{{lastName}}': recipient.lastName || '',
      '{{fullName}}': [recipient.firstName, recipient.lastName].filter(Boolean).join(' ') || 'Valued Customer',
      '{{email}}': recipient.email,
    };

    Object.entries(replacements).forEach(([token, value]) => {
      personalizedContent = personalizedContent.replace(new RegExp(token, 'g'), value);
    });

    // Handle custom fields
    if (recipient.customFields) {
      Object.entries(recipient.customFields).forEach(([key, value]) => {
        const token = `{{${key}}}`;
        personalizedContent = personalizedContent.replace(new RegExp(token, 'g'), String(value));
      });
    }

    return personalizedContent;
  }

  @OnWorkerEvent('completed')
  onCompleted(job: Job) {
    this.logger.log(`Job ${job.name} completed for campaign processing`);
  }

  @OnWorkerEvent('failed')
  onFailed(job: Job, err: Error) {
    this.logger.error(`Job ${job.name} failed:`, err);
  }
}
