import { Injectable, NotFoundException, BadRequestException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In, EntityManager } from 'typeorm';
// import * as csvParser from 'csv-parser';
import * as fs from 'fs';
import { Readable } from 'stream';

import { EmailGroup, EmailGroupType, EmailGroupStatus } from './entity/email-group.entity';
import { EmailGroupMember, MemberStatus, SubscriptionSource } from './entity/email-group-member.entity';

import { CreateEmailGroupDto } from './dto/create-email-group.dto';
import { UpdateEmailGroupDto } from './dto/update-email-group.dto';
import { ImportEmailsDto } from './dto/import-emails.dto';
import { AddMemberDto } from './dto/add-member.dto';
import csvParser from 'csv-parser';

@Injectable()
export class EmailGroupService {
  private readonly logger = new Logger(EmailGroupService.name);

  constructor(
    @InjectRepository(EmailGroup)
    private emailGroupRepo: Repository<EmailGroup>,
    
    @InjectRepository(EmailGroupMember)
    private emailGroupMemberRepo: Repository<EmailGroupMember>,
  ) {}

  /**
   * Create a new email group
   */
  async createEmailGroup(dto: CreateEmailGroupDto): Promise<EmailGroup> {
    const emailGroup = this.emailGroupRepo.create({
      ...dto,
      memberCount: 0,
      activeMemberCount: 0,
    });

    return await this.emailGroupRepo.save(emailGroup);
  }

  /**
   * Update email group
   */
  async updateEmailGroup(id: number, dto: UpdateEmailGroupDto): Promise<EmailGroup> {
    const emailGroup = await this.emailGroupRepo.findOne({ where: { id } });
    
    if (!emailGroup) {
      throw new NotFoundException('Email group not found');
    }

    Object.assign(emailGroup, dto);
    return await this.emailGroupRepo.save(emailGroup);
  }

  /**
   * Get all email groups
   */
  async getAllEmailGroups(): Promise<EmailGroup[]> {
    return await this.emailGroupRepo.find({
      where: { status: EmailGroupStatus.ACTIVE },
      order: { createdAt: 'DESC' }
    });
  }

  /**
   * Get email group by ID with members
   */
  async getEmailGroupById(id: number, includeMembers = false): Promise<EmailGroup> {
    const relations = includeMembers ? ['members'] : [];
    
    const emailGroup = await this.emailGroupRepo.findOne({
      where: { id },
      relations
    });
    
    if (!emailGroup) {
      throw new NotFoundException('Email group not found');
    }

    return emailGroup;
  }

  /**
   * Add single member to email group
   */
  async addMember(groupId: number, dto: AddMemberDto): Promise<EmailGroupMember> {
    const emailGroup = await this.emailGroupRepo.findOne({ where: { id: groupId } });
    
    if (!emailGroup) {
      throw new NotFoundException('Email group not found');
    }

    // Check if member already exists
    const existingMember = await this.emailGroupMemberRepo.findOne({
      where: { groupId, email: dto.email }
    });

    if (existingMember) {
      if (existingMember.status === MemberStatus.ACTIVE) {
        throw new BadRequestException('Email already exists in this group');
      } else {
        // Reactivate if previously unsubscribed
        existingMember.status = MemberStatus.ACTIVE;
        existingMember.subscribedAt = new Date();
        existingMember.unsubscribedAt = null;
        return await this.emailGroupMemberRepo.save(existingMember);
      }
    }

    const member = this.emailGroupMemberRepo.create({
      ...dto,
      groupId,
      subscribedAt: new Date(),
      status: MemberStatus.ACTIVE,
      source: dto.source || SubscriptionSource.MANUAL,
    });

    const savedMember = await this.emailGroupMemberRepo.save(member);

    // Update group member count
    await this.updateGroupMemberCounts(groupId);

    return savedMember;
  }

  /**
   * Import emails from CSV file
   */
  async importEmails(groupId: number, dto: ImportEmailsDto): Promise<{
    imported: number;
    skipped: number;
    errors: string[];
  }> {
    const emailGroup = await this.emailGroupRepo.findOne({ where: { id: groupId } });
    
    if (!emailGroup) {
      throw new NotFoundException('Email group not found');
    }

    const results = {
      imported: 0,
      skipped: 0,
      errors: []
    };

    try {
      const emails = await this.parseEmailsFromInput(dto);
      
      for (const emailData of emails) {
        try {
          await this.addMemberFromImport(groupId, emailData, dto.source);
          results.imported++;
        } catch (error) {
          results.skipped++;
          results.errors.push(`${emailData.email}: ${error.message}`);
        }
      }

      // Update group member count
      await this.updateGroupMemberCounts(groupId);

      this.logger.log(`Import completed for group ${groupId}: ${results.imported} imported, ${results.skipped} skipped`);

    } catch (error) {
      this.logger.error(`Import failed for group ${groupId}:`, error);
      throw new BadRequestException(`Import failed: ${error.message}`);
    }

    return results;
  }

  /**
   * Remove member from email group
   */
  async removeMember(groupId: number, email: string): Promise<void> {
    const member = await this.emailGroupMemberRepo.findOne({
      where: { groupId, email }
    });

    if (!member) {
      throw new NotFoundException('Member not found in this group');
    }

    await this.emailGroupMemberRepo.remove(member);
    await this.updateGroupMemberCounts(groupId);
  }

  /**
   * Unsubscribe member from email group
   */
  async unsubscribeMember(groupId: number, email: string): Promise<EmailGroupMember> {
    const member = await this.emailGroupMemberRepo.findOne({
      where: { groupId, email }
    });

    if (!member) {
      throw new NotFoundException('Member not found in this group');
    }

    member.status = MemberStatus.UNSUBSCRIBED;
    member.unsubscribedAt = new Date();

    const updatedMember = await this.emailGroupMemberRepo.save(member);
    await this.updateGroupMemberCounts(groupId);

    return updatedMember;
  }

  /**
   * Get active members of an email group
   */
  async getActiveMembers(groupId: number, limit = 1000, offset = 0): Promise<EmailGroupMember[]> {
    return await this.emailGroupMemberRepo.find({
      where: { 
        groupId, 
        status: MemberStatus.ACTIVE 
      },
      order: { subscribedAt: 'DESC' },
      take: limit,
      skip: offset
    });
  }

  /**
   * Get member statistics for a group
   */
  async getGroupStatistics(groupId: number): Promise<any> {
    const stats = await this.emailGroupMemberRepo
      .createQueryBuilder('member')
      .select([
        'COUNT(*) as total_members',
        'SUM(CASE WHEN status = :active THEN 1 ELSE 0 END) as active_members',
        'SUM(CASE WHEN status = :unsubscribed THEN 1 ELSE 0 END) as unsubscribed_members',
        'SUM(CASE WHEN status = :bounced THEN 1 ELSE 0 END) as bounced_members',
        'AVG(total_emails_opened::float / NULLIF(total_emails_sent, 0) * 100) as avg_open_rate',
        'AVG(total_emails_clicked::float / NULLIF(total_emails_sent, 0) * 100) as avg_click_rate',
      ])
      .where('member.groupId = :groupId', { groupId })
      .setParameters({
        active: MemberStatus.ACTIVE,
        unsubscribed: MemberStatus.UNSUBSCRIBED,
        bounced: MemberStatus.BOUNCED
      })
      .getRawOne();

    return {
      totalMembers: parseInt(stats.total_members) || 0,
      activeMembers: parseInt(stats.active_members) || 0,
      unsubscribedMembers: parseInt(stats.unsubscribed_members) || 0,
      bouncedMembers: parseInt(stats.bounced_members) || 0,
      averageOpenRate: parseFloat(stats.avg_open_rate) || 0,
      averageClickRate: parseFloat(stats.avg_click_rate) || 0,
    };
  }

  /**
   * Delete email group
   */
  async deleteEmailGroup(id: number): Promise<void> {
    const emailGroup = await this.emailGroupRepo.findOne({ where: { id } });
    
    if (!emailGroup) {
      throw new NotFoundException('Email group not found');
    }

    // Soft delete by marking as archived
    emailGroup.status = EmailGroupStatus.ARCHIVED;
    await this.emailGroupRepo.save(emailGroup);
  }

  /**
   * Parse emails from various input formats
   */
  private async parseEmailsFromInput(dto: ImportEmailsDto): Promise<any[]> {
    if (dto.csvFile) {
      return await this.parseCSVFile(dto.csvFile);
    } else if (dto.emailList) {
      return this.parseEmailList(dto.emailList);
    } else {
      throw new BadRequestException('No email data provided');
    }
  }

  /**
   * Parse CSV file
   */
  private async parseCSVFile(filePath: string): Promise<any[]> {
    return new Promise((resolve, reject) => {
      const results = [];
      
      fs.createReadStream(filePath)
        .pipe(csvParser())
        .on('data', (data) => {
          if (data.email && this.isValidEmail(data.email)) {
            results.push({
              email: data.email.toLowerCase().trim(),
              firstName: data.firstName || data.first_name || '',
              lastName: data.lastName || data.last_name || '',
              phone: data.phone || '',
              customFields: this.extractCustomFields(data)
            });
          }
        })
        .on('end', () => resolve(results))
        .on('error', (error) => reject(error));
    });
  }

  /**
   * Parse email list (comma or newline separated)
   */
  private parseEmailList(emailList: string): any[] {
    const emails = emailList
      .split(/[,\n\r]+/)
      .map(email => email.trim().toLowerCase())
      .filter(email => email && this.isValidEmail(email));

    return emails.map(email => ({ email }));
  }

  /**
   * Add member from import
   */
  private async addMemberFromImport(
    groupId: number, 
    emailData: any, 
    source: SubscriptionSource
  ): Promise<EmailGroupMember> {
    // Check if member already exists
    const existingMember = await this.emailGroupMemberRepo.findOne({
      where: { groupId, email: emailData.email }
    });

    if (existingMember && existingMember.status === MemberStatus.ACTIVE) {
      throw new Error('Email already exists');
    }

    if (existingMember) {
      // Reactivate existing member
      existingMember.status = MemberStatus.ACTIVE;
      existingMember.subscribedAt = new Date();
      existingMember.unsubscribedAt = null;
      existingMember.source = source;
      return await this.emailGroupMemberRepo.save(existingMember);
    }

    // Create new member
    const member = this.emailGroupMemberRepo.create({
      groupId,
      email: emailData.email,
      firstName: emailData.firstName,
      lastName: emailData.lastName,
      phone: emailData.phone,
      customFields: emailData.customFields,
      subscribedAt: new Date(),
      status: MemberStatus.ACTIVE,
      source,
    });

    return await this.emailGroupMemberRepo.save(member);
  }

  /**
   * Update group member counts
   */
  private async updateGroupMemberCounts(groupId: number): Promise<void> {
    const counts = await this.emailGroupMemberRepo
      .createQueryBuilder('member')
      .select([
        'COUNT(*) as total_count',
        'SUM(CASE WHEN status = :active THEN 1 ELSE 0 END) as active_count'
      ])
      .where('member.groupId = :groupId', { groupId })
      .setParameter('active', MemberStatus.ACTIVE)
      .getRawOne();

    await this.emailGroupRepo.update(groupId, {
      memberCount: parseInt(counts.total_count) || 0,
      activeMemberCount: parseInt(counts.active_count) || 0,
    });
  }

  /**
   * Validate email format
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Extract custom fields from CSV data
   */
  private extractCustomFields(data: any): Record<string, any> {
    const standardFields = ['email', 'firstName', 'first_name', 'lastName', 'last_name', 'phone'];
    const customFields = {};

    Object.keys(data).forEach(key => {
      if (!standardFields.includes(key) && data[key]) {
        customFields[key] = data[key];
      }
    });

    return Object.keys(customFields).length > 0 ? customFields : null;
  }
}
