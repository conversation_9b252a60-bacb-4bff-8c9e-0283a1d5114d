import { Injectable, NotFoundException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between } from 'typeorm';
import * as ExcelJS from 'exceljs';
import * as fs from 'fs';
import * as path from 'path';

import { CampaignReport, ReportType } from './entity/campaign-report.entity';
import { EmailCampaign } from './entity/email-campaign.entity';
import { CampaignDeliveryStatusEnum } from '../mail-template/enum/email-campaign.enum';

import { GenerateReportDto } from './dto/generate-report.dto';
import { EmailSendHistory } from 'src/mail-template/entity/email-template-history.entity';

@Injectable()
export class CampaignReportService {
  private readonly logger = new Logger(CampaignReportService.name);

  constructor(
    @InjectRepository(CampaignReport)
    private campaignReportRepo: Repository<CampaignReport>,

    @InjectRepository(EmailCampaign)
    private campaignRepo: Repository<EmailCampaign>,

    @InjectRepository(EmailSendHistory)
    private sendHistoryRepo: Repository<EmailSendHistory>,
  ) { }

  /**
   * Generate comprehensive campaign report
   */
  async generateCampaignReport(campaignId: number, dto: GenerateReportDto): Promise<CampaignReport> {
    const campaign = await this.campaignRepo.findOne({
      where: { id: campaignId },
      relations: ['template']
    });

    if (!campaign) {
      throw new NotFoundException('Campaign not found');
    }

    const reportData = await this.collectReportData(campaignId, dto);

    const report = this.campaignReportRepo.create({
      campaignId,
      type: dto.type || ReportType.SUMMARY,
      reportDate: new Date(),
      data: reportData,
      filters: dto.filters,
      generatedByUserId: dto.userId,
      isAutomated: false,
    });

    const savedReport = await this.campaignReportRepo.save(report);

    // Generate export file if requested
    if (dto.exportFormat) {
      const exportUrl = await this.generateExportFile(savedReport, dto.exportFormat);
      savedReport.exportFormat = dto.exportFormat;
      savedReport.exportUrl = exportUrl;
      await this.campaignReportRepo.save(savedReport);
    }

    return savedReport;
  }

  /**
   * Get campaign summary statistics
   */
  async getCampaignSummary(campaignId: number): Promise<any> {
    const campaign = await this.campaignRepo.findOne({ where: { id: campaignId } });

    if (!campaign) {
      throw new NotFoundException('Campaign not found');
    }

    const summary = await this.sendHistoryRepo
      .createQueryBuilder('history')
      .select([
        'COUNT(*) as total_sent',
        'SUM(CASE WHEN status = :delivered THEN 1 ELSE 0 END) as delivered',
        'SUM(CASE WHEN opened = true THEN 1 ELSE 0 END) as opened',
        'SUM(CASE WHEN clicked = true THEN 1 ELSE 0 END) as clicked',
        'SUM(CASE WHEN bounced = true THEN 1 ELSE 0 END) as bounced',
        'SUM(CASE WHEN complaint = true THEN 1 ELSE 0 END) as complaints',
        'COUNT(DISTINCT CASE WHEN opened = true THEN recipient_email END) as unique_opens',
        'COUNT(DISTINCT CASE WHEN clicked = true THEN recipient_email END) as unique_clicks',
      ])
      .where('history.campaignId = :campaignId', { campaignId })
      .setParameter('delivered', CampaignDeliveryStatusEnum.DELIVERED)
      .getRawOne();

    const totalSent = parseInt(summary.total_sent) || 0;
    const delivered = parseInt(summary.delivered) || 0;
    const opened = parseInt(summary.opened) || 0;
    const clicked = parseInt(summary.clicked) || 0;
    const bounced = parseInt(summary.bounced) || 0;
    const complaints = parseInt(summary.complaints) || 0;
    const uniqueOpens = parseInt(summary.unique_opens) || 0;
    const uniqueClicks = parseInt(summary.unique_clicks) || 0;

    return {
      campaign: {
        id: campaign.id,
        name: campaign.name,
        type: campaign.type,
        status: campaign.status,
        startedAt: campaign.startedAt,
        completedAt: campaign.completedAt,
      },
      metrics: {
        totalSent,
        delivered,
        opened,
        clicked,
        bounced,
        complaints,
        uniqueOpens,
        uniqueClicks,
        deliveryRate: totalSent > 0 ? (delivered / totalSent) * 100 : 0,
        openRate: delivered > 0 ? (opened / delivered) * 100 : 0,
        clickRate: delivered > 0 ? (clicked / delivered) * 100 : 0,
        bounceRate: totalSent > 0 ? (bounced / totalSent) * 100 : 0,
        complaintRate: totalSent > 0 ? (complaints / totalSent) * 100 : 0,
        uniqueOpenRate: delivered > 0 ? (uniqueOpens / delivered) * 100 : 0,
        uniqueClickRate: delivered > 0 ? (uniqueClicks / delivered) * 100 : 0,
      }
    };
  }

  /**
   * Get geographic performance data
   */
  async getGeographicReport(campaignId: number): Promise<any> {
    const geoData = await this.sendHistoryRepo
      .createQueryBuilder('history')
      .select([
        'history.countryId',
        'COUNT(*) as sent',
        'SUM(CASE WHEN opened = true THEN 1 ELSE 0 END) as opened',
        'SUM(CASE WHEN clicked = true THEN 1 ELSE 0 END) as clicked',
        'SUM(CASE WHEN bounced = true THEN 1 ELSE 0 END) as bounced',
      ])
      .where('history.campaignId = :campaignId', { campaignId })
      .groupBy('history.countryId')
      .orderBy('sent', 'DESC')
      .getRawMany();

    return geoData.map(row => ({
      countryId: row.countryId,
      sent: parseInt(row.sent),
      opened: parseInt(row.opened),
      clicked: parseInt(row.clicked),
      bounced: parseInt(row.bounced),
      openRate: row.sent > 0 ? (parseInt(row.opened) / parseInt(row.sent)) * 100 : 0,
      clickRate: row.sent > 0 ? (parseInt(row.clicked) / parseInt(row.sent)) * 100 : 0,
      bounceRate: row.sent > 0 ? (parseInt(row.bounced) / parseInt(row.sent)) * 100 : 0,
    }));
  }

  /**
   * Get time-based performance data
   */
  async getTimeBasedReport(campaignId: number): Promise<any> {
    const timeData = await this.sendHistoryRepo
      .createQueryBuilder('history')
      .select([
        'EXTRACT(hour FROM opened_at) as hour',
        'COUNT(*) as opens',
        'COUNT(DISTINCT recipient_email) as unique_opens',
      ])
      .where('history.campaignId = :campaignId', { campaignId })
      .andWhere('history.opened = true')
      .groupBy('EXTRACT(hour FROM opened_at)')
      .orderBy('hour', 'ASC')
      .getRawMany();

    const clickData = await this.sendHistoryRepo
      .createQueryBuilder('history')
      .select([
        'EXTRACT(hour FROM clicked_at) as hour',
        'COUNT(*) as clicks',
        'COUNT(DISTINCT recipient_email) as unique_clicks',
      ])
      .where('history.campaignId = :campaignId', { campaignId })
      .andWhere('history.clicked = true')
      .groupBy('EXTRACT(hour FROM clicked_at)')
      .orderBy('hour', 'ASC')
      .getRawMany();

    // Merge open and click data by hour
    const hourlyStats = [];
    for (let hour = 0; hour < 24; hour++) {
      const openStat = timeData.find(d => parseInt(d.hour) === hour);
      const clickStat = clickData.find(d => parseInt(d.hour) === hour);

      hourlyStats.push({
        hour,
        opens: openStat ? parseInt(openStat.opens) : 0,
        uniqueOpens: openStat ? parseInt(openStat.unique_opens) : 0,
        clicks: clickStat ? parseInt(clickStat.clicks) : 0,
        uniqueClicks: clickStat ? parseInt(clickStat.unique_clicks) : 0,
      });
    }

    return hourlyStats;
  }

  /**
   * Get engagement segments
   */
  async getEngagementReport(campaignId: number): Promise<any> {
    // This would typically integrate with your user segmentation logic
    // For now, we'll create basic segments based on email history

    const engagementData = await this.sendHistoryRepo
      .createQueryBuilder('history')
      .select([
        'recipient_email',
        'opened',
        'clicked',
        'bounced',
        'complaint'
      ])
      .where('history.campaignId = :campaignId', { campaignId })
      .getRawMany();

    const segments = {
      highEngagement: 0,    // Opened and clicked
      mediumEngagement: 0,  // Opened but didn't click
      lowEngagement: 0,     // Delivered but didn't open
      noEngagement: 0,      // Bounced or complained
    };

    engagementData.forEach(record => {
      if (record.bounced || record.complaint) {
        segments.noEngagement++;
      } else if (record.opened && record.clicked) {
        segments.highEngagement++;
      } else if (record.opened) {
        segments.mediumEngagement++;
      } else {
        segments.lowEngagement++;
      }
    });

    return segments;
  }

  /**
   * Collect all report data based on type
   */
  private async collectReportData(campaignId: number, dto: GenerateReportDto): Promise<any> {
    const baseData = await this.getCampaignSummary(campaignId);

    switch (dto.type) {
      case ReportType.SUMMARY:
        return baseData;

      case ReportType.DETAILED:
        return {
          ...baseData,
          geographic: await this.getGeographicReport(campaignId),
          timeBased: await this.getTimeBasedReport(campaignId),
          engagement: await this.getEngagementReport(campaignId),
        };

      case ReportType.GEOGRAPHIC:
        return {
          ...baseData,
          geographic: await this.getGeographicReport(campaignId),
        };

      case ReportType.TIME_BASED:
        return {
          ...baseData,
          timeBased: await this.getTimeBasedReport(campaignId),
        };

      case ReportType.ENGAGEMENT:
        return {
          ...baseData,
          engagement: await this.getEngagementReport(campaignId),
        };

      default:
        return baseData;
    }
  }

  /**
   * Generate export file
   */
  private async generateExportFile(report: CampaignReport, format: string): Promise<string> {
    const fileName = `campaign-report-${report.campaignId}-${Date.now()}.${format}`;
    const filePath = path.join(process.cwd(), 'exports', fileName);

    // Ensure exports directory exists
    const exportsDir = path.dirname(filePath);
    if (!fs.existsSync(exportsDir)) {
      fs.mkdirSync(exportsDir, { recursive: true });
    }

    switch (format) {
      case 'excel':
        await this.generateExcelReport(report, filePath);
        break;
      case 'csv':
        await this.generateCSVReport(report, filePath);
        break;
      case 'json':
        await this.generateJSONReport(report, filePath);
        break;
      default:
        throw new Error(`Unsupported export format: ${format}`);
    }

    return `/exports/${fileName}`;
  }

  /**
   * Generate Excel report
   */
  private async generateExcelReport(report: CampaignReport, filePath: string): Promise<void> {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Campaign Report');

    // Add headers and data based on report type
    const data = report.data;

    // Summary section
    worksheet.addRow(['Campaign Summary']);
    worksheet.addRow(['Metric', 'Value']);
    worksheet.addRow(['Total Sent', data?.totalSent || 0]);
    worksheet.addRow(['Delivered', data?.totalDelivered || 0]);
    worksheet.addRow(['Opened', data?.totalOpened || 0]);
    worksheet.addRow(['Clicked', data?.totalClicked || 0]);
    worksheet.addRow(['Bounced', data?.totalBounced || 0]);
    worksheet.addRow(['Open Rate (%)', (data?.openRate || 0).toFixed(2)]);
    worksheet.addRow(['Click Rate (%)', (data?.clickRate || 0).toFixed(2)]);

    await workbook.xlsx.writeFile(filePath);
  }

  /**
   * Generate CSV report
   */
  private async generateCSVReport(report: CampaignReport, filePath: string): Promise<void> {
    const data = report.data;
    const csvContent = [
      'Metric,Value',
      `Total Sent,${data?.totalSent || 0}`,
      `Delivered,${data?.totalDelivered || 0}`,
      `Opened,${data?.totalOpened || 0}`,
      `Clicked,${data?.totalClicked || 0}`,
      `Bounced,${data?.totalBounced || 0}`,
      `Open Rate (%),${(data?.openRate || 0).toFixed(2)}`,
      `Click Rate (%),${(data?.clickRate || 0).toFixed(2)}`,
    ].join('\n');

    fs.writeFileSync(filePath, csvContent);
  }

  /**
   * Generate JSON report
   */
  private async generateJSONReport(report: CampaignReport, filePath: string): Promise<void> {
    const jsonContent = JSON.stringify(report.data, null, 2);
    fs.writeFileSync(filePath, jsonContent);
  }

  /**
   * Get all reports for a campaign
   */
  async getCampaignReports(campaignId: number): Promise<CampaignReport[]> {
    return await this.campaignReportRepo.find({
      where: { campaignId },
      order: { createdAt: 'DESC' }
    });
  }

  /**
   * Delete expired reports
   */
  async cleanupExpiredReports(): Promise<void> {
    const expiredReports = await this.campaignReportRepo.find({
      where: {
        expiresAt: Between(new Date('1970-01-01'), new Date())
      }
    });

    for (const report of expiredReports) {
      // Delete export file if exists
      if (report.exportUrl) {
        const filePath = path.join(process.cwd(), report.exportUrl);
        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath);
        }
      }

      // Delete report record
      await this.campaignReportRepo.remove(report);
    }

    this.logger.log(`Cleaned up ${expiredReports.length} expired reports`);
  }
}
