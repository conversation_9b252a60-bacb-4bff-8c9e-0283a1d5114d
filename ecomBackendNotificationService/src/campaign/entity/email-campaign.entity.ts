import { <PERSON>umn, CreateDateColumn, <PERSON>tity, <PERSON>in<PERSON><PERSON>um<PERSON>, ManyToOne, OneToMany } from "typeorm";
// import { Country } from "../../entities/country.entity";
import { EmailTemplate } from "../../mail-template/entity/email-template.entity";
import { CampaignDeliveryStatusEnum, CampaignStatusEnum, CampaignType, CampaignPriority } from "../../mail-template/enum/email-campaign.enum";
import { IEmailCampaign, IEmailSendHistory } from "../../mail-template/interface/email-campaign.interface";
import { AbstractEntity } from "../../../libs/common/database/entity/abstract.entity";
import { EmailSendHistory } from "src/mail-template/entity/email-template-history.entity";

// Enhanced EmailCampaign Entity
@Entity('email_campaigns')
export class EmailCampaign extends AbstractEntity implements IEmailCampaign {
    @Column()
    name: string;

    @Column({ nullable: true })
    description: string;

    @ManyToOne(() => EmailTemplate)
    @JoinColumn({ name: 'template_id' })
    template: number | EmailTemplate;

    @Column({ name: 'template_id' })
    templateId: number;

    @Column({ type: 'enum', enum: CampaignType, default: CampaignType.PROMOTIONAL })
    type: CampaignType;

    @Column({ type: 'enum', enum: CampaignPriority, default: CampaignPriority.MEDIUM })
    priority: CampaignPriority;

    @Column('timestamp', { nullable: true })
    scheduledAt: Date;

    @Column({ type: 'enum', enum: CampaignStatusEnum, default: CampaignStatusEnum.DRAFT })
    status: CampaignStatusEnum;

    @Column('jsonb', { nullable: true })
    targetGroup: any;

    @Column('jsonb', { nullable: true })
    emailGroups: number[]; // Array of email group IDs

    @Column('jsonb', { nullable: true })
    customEmails: string[]; // Array of individual email addresses

    @Column({ name: 'sender_name', nullable: true })
    senderName: string;

    @Column({ name: 'sender_email', nullable: true })
    senderEmail: string;

    @Column({ name: 'reply_to', nullable: true })
    replyTo: string;

    @Column({ name: 'subject_line' })
    subjectLine: string;

    @Column({ name: 'preheader_text', nullable: true })
    preheaderText: string;

    @Column({ name: 'total_recipients', default: 0 })
    totalRecipients: number;

    @Column({ name: 'sent_count', default: 0 })
    sentCount: number;

    @Column({ name: 'delivered_count', default: 0 })
    deliveredCount: number;

    @Column({ name: 'opened_count', default: 0 })
    openedCount: number;

    @Column({ name: 'clicked_count', default: 0 })
    clickedCount: number;

    @Column({ name: 'bounced_count', default: 0 })
    bouncedCount: number;

    @Column({ name: 'unsubscribed_count', default: 0 })
    unsubscribedCount: number;

    @Column({ name: 'complaint_count', default: 0 })
    complaintCount: number;

    @Column({ name: 'started_at', nullable: true })
    startedAt: Date;

    @Column({ name: 'completed_at', nullable: true })
    completedAt: Date;

    @Column({ name: 'estimated_send_time', nullable: true })
    estimatedSendTime: number; // in minutes

    @Column('jsonb', { nullable: true })
    settings: {
        trackOpens?: boolean;
        trackClicks?: boolean;
        enableUnsubscribe?: boolean;
        sendTestEmail?: boolean;
        throttleRate?: number; // emails per minute
        retryFailures?: boolean;
        maxRetries?: number;
    };

    @Column('jsonb', { nullable: true })
    segmentationRules: any; // Advanced segmentation criteria

    @Column('jsonb', { nullable: true })
    abTestSettings: {
        enabled?: boolean;
        subjectVariants?: string[];
        contentVariants?: string[];
        splitPercentage?: number;
        winnerCriteria?: 'open_rate' | 'click_rate' | 'conversion_rate';
    };

    @OneToMany(() => EmailSendHistory, (history) => history.campaignId)
    sendHistory: EmailSendHistory[];
}