import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "typeorm";
import { AbstractEntity } from "../../../libs/common/database/entity/abstract.entity";
import { EmailCampaign } from "./email-campaign.entity";

export enum ReportType {
    SUMMARY = 'summary',
    DETAILED = 'detailed',
    GEOGRAPHIC = 'geographic',
    DEVICE = 'device',
    TIME_BASED = 'time_based',
    ENGAGEMENT = 'engagement'
}

@Entity('campaign_reports')
export class CampaignReport extends AbstractEntity {
    @Column({ name: 'campaign_id' })
    campaignId: number;

    @Column({ type: 'enum', enum: ReportType })
    type: ReportType;

    @Column({ name: 'report_date' })
    reportDate: Date;

    @Column('jsonb')
    data: {
        // Summary metrics
        totalSent?: number;
        totalDelivered?: number;
        totalOpened?: number;
        totalClicked?: number;
        totalBounced?: number;
        totalUnsubscribed?: number;
        totalComplaints?: number;
        
        // Rates
        deliveryRate?: number;
        openRate?: number;
        clickRate?: number;
        bounceRate?: number;
        unsubscribeRate?: number;
        complaintRate?: number;
        
        // Geographic data
        countryStats?: Array<{
            countryId: number;
            countryName: string;
            sent: number;
            opened: number;
            clicked: number;
            openRate: number;
            clickRate: number;
        }>;
        
        // Device data
        deviceStats?: Array<{
            deviceType: string;
            count: number;
            percentage: number;
        }>;
        
        // Time-based data
        hourlyStats?: Array<{
            hour: number;
            opens: number;
            clicks: number;
        }>;
        
        // Top performing links
        linkStats?: Array<{
            url: string;
            clicks: number;
            uniqueClicks: number;
            clickRate: number;
        }>;
        
        // Engagement segments
        engagementSegments?: {
            highEngagement: number;
            mediumEngagement: number;
            lowEngagement: number;
            noEngagement: number;
        };
        
        // Revenue tracking (if applicable)
        revenue?: {
            totalRevenue: number;
            averageOrderValue: number;
            conversionRate: number;
            revenuePerEmail: number;
        };
        
        // Comparison with previous campaigns
        comparison?: {
            previousCampaignId?: number;
            openRateChange?: number;
            clickRateChange?: number;
            performanceTrend?: 'improving' | 'declining' | 'stable';
        };
    };

    @Column('jsonb', { nullable: true })
    filters: {
        dateRange?: { start: Date; end: Date };
        countries?: number[];
        emailGroups?: number[];
        segments?: string[];
    };

    @Column({ name: 'generated_by_user_id', nullable: true })
    generatedByUserId: number;

    @Column({ name: 'is_automated', default: false })
    isAutomated: boolean;

    @Column({ name: 'export_format', nullable: true })
    exportFormat: 'pdf' | 'excel' | 'csv' | 'json';

    @Column({ name: 'export_url', nullable: true })
    exportUrl: string;

    @Column({ name: 'expires_at', nullable: true })
    expiresAt: Date;

    @ManyToOne(() => EmailCampaign)
    @JoinColumn({ name: 'campaign_id' })
    campaign: EmailCampaign;
}
