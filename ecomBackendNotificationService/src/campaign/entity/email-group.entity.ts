import { Column, <PERSON><PERSON><PERSON>, OneToMany, ManyToMany, JoinT<PERSON> } from "typeorm";
import { AbstractEntity } from "../../../libs/common/database/entity/abstract.entity";
import { EmailGroupMember } from "./email-group-member.entity";

export enum EmailGroupType {
    STATIC = 'static',
    DYNAMIC = 'dynamic',
    IMPORTED = 'imported',
    SEGMENTED = 'segmented'
}

export enum EmailGroupStatus {
    ACTIVE = 'active',
    INACTIVE = 'inactive',
    ARCHIVED = 'archived'
}

@Entity('email_groups')
export class EmailGroup extends AbstractEntity {
    @Column()
    name: string;

    @Column({ nullable: true })
    description: string;

    @Column({ type: 'enum', enum: EmailGroupType, default: EmailGroupType.STATIC })
    type: EmailGroupType;

    @Column({ type: 'enum', enum: EmailGroupStatus, default: EmailGroupStatus.ACTIVE })
    status: EmailGroupStatus;

    @Column({ name: 'member_count', default: 0 })
    memberCount: number;

    @Column({ name: 'active_member_count', default: 0 })
    activeMemberCount: number;

    @Column('jsonb', { nullable: true })
    segmentationCriteria: {
        countries?: number[];
        ageRange?: { min: number; max: number };
        gender?: string[];
        purchaseHistory?: {
            minOrders?: number;
            maxOrders?: number;
            minAmount?: number;
            maxAmount?: number;
            categories?: string[];
            brands?: string[];
        };
        engagement?: {
            lastOpenDays?: number;
            lastClickDays?: number;
            minOpenRate?: number;
            minClickRate?: number;
        };
        customFields?: Record<string, any>;
    };

    @Column('jsonb', { nullable: true })
    tags: string[];

    @Column({ name: 'created_by_user_id', nullable: true })
    createdByUserId: number;

    @Column({ name: 'last_sync_at', nullable: true })
    lastSyncAt: Date;

    @Column({ name: 'auto_sync_enabled', default: false })
    autoSyncEnabled: boolean;

    @Column({ name: 'sync_frequency_hours', nullable: true })
    syncFrequencyHours: number;

    @OneToMany(() => EmailGroupMember, (member) => member.group)
    members: EmailGroupMember[];

    // Computed properties
    get openRate(): number {
        // Calculate from send history
        return 0;
    }

    get clickRate(): number {
        // Calculate from send history
        return 0;
    }

    get unsubscribeRate(): number {
        // Calculate from send history
        return 0;
    }
}
