import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "typeorm";
import { AbstractEntity } from "../../../libs/common/database/entity/abstract.entity";
import { EmailGroup } from "./email-group.entity";

export enum MemberStatus {
    ACTIVE = 'active',
    UNSUBSCRIBED = 'unsubscribed',
    BOUNCED = 'bounced',
    COMPLAINED = 'complained',
    SUPPRESSED = 'suppressed'
}

export enum SubscriptionSource {
    MANUAL = 'manual',
    IMPORT = 'import',
    API = 'api',
    WEBSITE = 'website',
    CHECKOUT = 'checkout',
    NEWSLETTER = 'newsletter'
}

@Entity('email_group_members')
@Index(['email', 'groupId'], { unique: true })
export class EmailGroupMember extends AbstractEntity {
    @Column({ name: 'group_id' })
    groupId: number;

    @Column()
    @Index()
    email: string;

    @Column({ name: 'first_name', nullable: true })
    firstName: string;

    @Column({ name: 'last_name', nullable: true })
    lastName: string;

    @Column({ nullable: true })
    phone: string;

    @Column({ name: 'country_id', nullable: true })
    countryId: number;

    @Column({ nullable: true })
    city: string;

    @Column({ nullable: true })
    gender: string;

    @Column({ name: 'date_of_birth', nullable: true })
    dateOfBirth: Date;

    @Column({ type: 'enum', enum: MemberStatus, default: MemberStatus.ACTIVE })
    status: MemberStatus;

    @Column({ type: 'enum', enum: SubscriptionSource, default: SubscriptionSource.MANUAL })
    source: SubscriptionSource;

    @Column({ name: 'subscribed_at' })
    subscribedAt: Date;

    @Column({ name: 'unsubscribed_at', nullable: true })
    unsubscribedAt: Date;

    @Column({ name: 'last_email_sent_at', nullable: true })
    lastEmailSentAt: Date;

    @Column({ name: 'last_email_opened_at', nullable: true })
    lastEmailOpenedAt: Date;

    @Column({ name: 'last_email_clicked_at', nullable: true })
    lastEmailClickedAt: Date;

    @Column({ name: 'total_emails_sent', default: 0 })
    totalEmailsSent: number;

    @Column({ name: 'total_emails_opened', default: 0 })
    totalEmailsOpened: number;

    @Column({ name: 'total_emails_clicked', default: 0 })
    totalEmailsClicked: number;

    @Column({ name: 'total_emails_bounced', default: 0 })
    totalEmailsBounced: number;

    @Column({ name: 'bounce_count', default: 0 })
    bounceCount: number;

    @Column({ name: 'complaint_count', default: 0 })
    complaintCount: number;

    @Column('jsonb', { nullable: true })
    customFields: Record<string, any>;

    @Column('jsonb', { nullable: true })
    tags: string[];

    @Column('jsonb', { nullable: true })
    preferences: {
        frequency?: 'daily' | 'weekly' | 'monthly';
        categories?: string[];
        language?: string;
        timezone?: string;
    };

    @Column({ name: 'email_verified', default: false })
    emailVerified: boolean;

    @Column({ name: 'verification_token', nullable: true })
    verificationToken: string;

    @Column({ name: 'verification_sent_at', nullable: true })
    verificationSentAt: Date;

    @Column({ name: 'ip_address', nullable: true })
    ipAddress: string;

    @Column({ name: 'user_agent', nullable: true })
    userAgent: string;

    @Column({ name: 'referrer_url', nullable: true })
    referrerUrl: string;

    @Column({ name: 'utm_source', nullable: true })
    utmSource: string;

    @Column({ name: 'utm_medium', nullable: true })
    utmMedium: string;

    @Column({ name: 'utm_campaign', nullable: true })
    utmCampaign: string;

    @ManyToOne(() => EmailGroup, (group) => group.members)
    @JoinColumn({ name: 'group_id' })
    group: EmailGroup;

    // Computed properties
    get openRate(): number {
        if (this.totalEmailsSent === 0) return 0;
        return (this.totalEmailsOpened / this.totalEmailsSent) * 100;
    }

    get clickRate(): number {
        if (this.totalEmailsSent === 0) return 0;
        return (this.totalEmailsClicked / this.totalEmailsSent) * 100;
    }

    get engagementScore(): number {
        // Calculate engagement score based on opens, clicks, and recency
        const openWeight = 0.4;
        const clickWeight = 0.6;
        const recencyWeight = 0.2;

        const openScore = this.openRate * openWeight;
        const clickScore = this.clickRate * clickWeight;
        
        // Recency score (higher for more recent activity)
        const daysSinceLastOpen = this.lastEmailOpenedAt 
            ? Math.floor((Date.now() - this.lastEmailOpenedAt.getTime()) / (1000 * 60 * 60 * 24))
            : 365;
        const recencyScore = Math.max(0, (365 - daysSinceLastOpen) / 365) * 100 * recencyWeight;

        return Math.min(100, openScore + clickScore + recencyScore);
    }

    get fullName(): string {
        return [this.firstName, this.lastName].filter(Boolean).join(' ') || this.email;
    }
}
