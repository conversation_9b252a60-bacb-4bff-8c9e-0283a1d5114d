import { Injectable, NotFoundException, BadRequestException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, EntityManager, In, <PERSON><PERSON>han, <PERSON><PERSON>han } from 'typeorm';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { Cron, CronExpression } from '@nestjs/schedule';

import { EmailCampaign } from './entity/email-campaign.entity';
import { EmailGroup } from './entity/email-group.entity';
import { EmailGroupMember, MemberStatus } from './entity/email-group-member.entity';
import { CampaignReport } from './entity/campaign-report.entity';
import { EmailTemplate } from '../mail-template/entity/email-template.entity';

import { 
  CampaignStatusEnum, 
  CampaignDeliveryStatusEnum, 
  CampaignType, 
  CampaignPriority,
  SendingStatus 
} from '../mail-template/enum/email-campaign.enum';

import { CreateCampaignDto } from './dto/create-campaign.dto';
import { UpdateCampaignDto } from './dto/update-campaign.dto';
import { ScheduleCampaignDto } from './dto/schedule-campaign.dto';
import { EmailSendHistory } from 'src/mail-template/entity/email-template-history.entity';
// import { CampaignReportDto } from './dto/campaign-report.dto';

@Injectable()
export class CampaignService {
  private readonly logger = new Logger(CampaignService.name);

  constructor(
    @InjectRepository(EmailCampaign)
    private campaignRepo: Repository<EmailCampaign>,
    
    @InjectRepository(EmailGroup)
    private emailGroupRepo: Repository<EmailGroup>,
    
    @InjectRepository(EmailGroupMember)
    private emailGroupMemberRepo: Repository<EmailGroupMember>,
    
    @InjectRepository(EmailSendHistory)
    private sendHistoryRepo: Repository<EmailSendHistory>,
    
    @InjectRepository(CampaignReport)
    private campaignReportRepo: Repository<CampaignReport>,
    
    @InjectRepository(EmailTemplate)
    private emailTemplateRepo: Repository<EmailTemplate>,
    
    @InjectQueue('campaign-queue') 
    private campaignQueue: Queue,
    
    @InjectQueue('email-queue') 
    private emailQueue: Queue,
  ) {}

  /**
   * Create a new campaign
   */
  async createCampaign(dto: CreateCampaignDto): Promise<EmailCampaign> {
    // Validate template exists
    const template = await this.emailTemplateRepo.findOne({
      where: { id: dto.templateId }
    });
    
    if (!template) {
      throw new NotFoundException('Email template not found');
    }

    // Validate email groups exist
    if (dto.emailGroups?.length > 0) {
      const groups = await this.emailGroupRepo.find({
        where: { id: In(dto.emailGroups) }
      });
      
      if (groups.length !== dto.emailGroups.length) {
        throw new BadRequestException('One or more email groups not found');
      }
    }

    // Calculate total recipients
    const totalRecipients = await this.calculateTotalRecipients(
      dto.emailGroups || [],
      dto.customEmails || []
    );

    const campaign = this.campaignRepo.create({
      ...dto,
      totalRecipients,
      status: CampaignStatusEnum.DRAFT,
    });

    return await this.campaignRepo.save(campaign);
  }

  /**
   * Update campaign (only if in draft status)
   */
  async updateCampaign(id: number, dto: UpdateCampaignDto): Promise<EmailCampaign> {
    const campaign = await this.campaignRepo.findOne({ where: { id } });
    
    if (!campaign) {
      throw new NotFoundException('Campaign not found');
    }

    if (campaign.status !== CampaignStatusEnum.DRAFT) {
      throw new BadRequestException('Can only update campaigns in draft status');
    }

    // Recalculate recipients if groups or emails changed
    if (dto.emailGroups || dto.customEmails) {
      const totalRecipients = await this.calculateTotalRecipients(
        dto.emailGroups || campaign.emailGroups || [],
        dto.customEmails || campaign.customEmails || []
      );
      dto.totalRecipients = totalRecipients;
    }

    Object.assign(campaign, dto);
    return await this.campaignRepo.save(campaign);
  }

  /**
   * Schedule campaign for sending
   */
  async scheduleCampaign(id: number, dto: ScheduleCampaignDto): Promise<EmailCampaign> {
    const campaign = await this.campaignRepo.findOne({ where: { id } });
    
    if (!campaign) {
      throw new NotFoundException('Campaign not found');
    }

    if (campaign.status !== CampaignStatusEnum.DRAFT) {
      throw new BadRequestException('Can only schedule campaigns in draft status');
    }

    // Validate schedule time is in the future
    if (dto.scheduledAt && new Date(dto.scheduledAt) <= new Date()) {
      throw new BadRequestException('Scheduled time must be in the future');
    }

    // Update campaign status
    campaign.scheduledAt = dto.scheduledAt ? new Date(dto.scheduledAt) : new Date();
    campaign.status = dto.sendImmediately ? 
      CampaignStatusEnum.SENDING : 
      CampaignStatusEnum.SCHEDULED;

    const updatedCampaign = await this.campaignRepo.save(campaign);

    // Queue for immediate sending or schedule
    if (dto.sendImmediately) {
      await this.queueCampaignForSending(updatedCampaign);
    } else {
      await this.scheduleCampaignJob(updatedCampaign);
    }

    return updatedCampaign;
  }

  /**
   * Send campaign immediately
   */
  async sendCampaignNow(id: number): Promise<EmailCampaign> {
    const campaign = await this.campaignRepo.findOne({ 
      where: { id },
      relations: ['template']
    });
    
    if (!campaign) {
      throw new NotFoundException('Campaign not found');
    }

    if (![CampaignStatusEnum.DRAFT, CampaignStatusEnum.SCHEDULED].includes(campaign.status)) {
      throw new BadRequestException('Campaign cannot be sent in current status');
    }

    // Update status to sending
    campaign.status = CampaignStatusEnum.SENDING;
    campaign.startedAt = new Date();
    await this.campaignRepo.save(campaign);

    // Queue for sending
    await this.queueCampaignForSending(campaign);

    return campaign;
  }

  /**
   * Pause campaign sending
   */
  async pauseCampaign(id: number): Promise<EmailCampaign> {
    const campaign = await this.campaignRepo.findOne({ where: { id } });
    
    if (!campaign) {
      throw new NotFoundException('Campaign not found');
    }

    if (campaign.status !== CampaignStatusEnum.SENDING) {
      throw new BadRequestException('Can only pause campaigns that are currently sending');
    }

    campaign.status = CampaignStatusEnum.PAUSED;
    return await this.campaignRepo.save(campaign);
  }

  /**
   * Resume paused campaign
   */
  async resumeCampaign(id: number): Promise<EmailCampaign> {
    const campaign = await this.campaignRepo.findOne({ where: { id } });
    
    if (!campaign) {
      throw new NotFoundException('Campaign not found');
    }

    if (campaign.status !== CampaignStatusEnum.PAUSED) {
      throw new BadRequestException('Can only resume paused campaigns');
    }

    campaign.status = CampaignStatusEnum.SENDING;
    await this.campaignRepo.save(campaign);

    // Resume sending
    await this.queueCampaignForSending(campaign);

    return campaign;
  }

  /**
   * Cancel campaign
   */
  async cancelCampaign(id: number): Promise<EmailCampaign> {
    const campaign = await this.campaignRepo.findOne({ where: { id } });
    
    if (!campaign) {
      throw new NotFoundException('Campaign not found');
    }

    if ([CampaignStatusEnum.SENT, CampaignStatusEnum.COMPLETED].includes(campaign.status)) {
      throw new BadRequestException('Cannot cancel completed campaigns');
    }

    campaign.status = CampaignStatusEnum.CANCELLED;
    return await this.campaignRepo.save(campaign);
  }

  /**
   * Get campaign details with statistics
   */
  async getCampaignDetails(id: number): Promise<any> {
    const campaign = await this.campaignRepo.findOne({
      where: { id },
      relations: ['template']
    });
    
    if (!campaign) {
      throw new NotFoundException('Campaign not found');
    }

    // Get sending statistics
    const stats = await this.getCampaignStatistics(id);

    return {
      ...campaign,
      statistics: stats
    };
  }

  /**
   * Get campaign statistics
   */
  async getCampaignStatistics(campaignId: number): Promise<any> {
    const stats = await this.sendHistoryRepo
      .createQueryBuilder('history')
      .select([
        'COUNT(*) as total_sent',
        'SUM(CASE WHEN status = :delivered THEN 1 ELSE 0 END) as delivered',
        'SUM(CASE WHEN opened = true THEN 1 ELSE 0 END) as opened',
        'SUM(CASE WHEN clicked = true THEN 1 ELSE 0 END) as clicked',
        'SUM(CASE WHEN bounced = true THEN 1 ELSE 0 END) as bounced',
        'SUM(CASE WHEN complaint = true THEN 1 ELSE 0 END) as complaints',
      ])
      .where('history.campaignId = :campaignId', { campaignId })
      .setParameter('delivered', CampaignDeliveryStatusEnum.DELIVERED)
      .getRawOne();

    const totalSent = parseInt(stats.total_sent) || 0;
    const delivered = parseInt(stats.delivered) || 0;
    const opened = parseInt(stats.opened) || 0;
    const clicked = parseInt(stats.clicked) || 0;
    const bounced = parseInt(stats.bounced) || 0;
    const complaints = parseInt(stats.complaints) || 0;

    return {
      totalSent,
      delivered,
      opened,
      clicked,
      bounced,
      complaints,
      deliveryRate: totalSent > 0 ? (delivered / totalSent) * 100 : 0,
      openRate: delivered > 0 ? (opened / delivered) * 100 : 0,
      clickRate: delivered > 0 ? (clicked / delivered) * 100 : 0,
      bounceRate: totalSent > 0 ? (bounced / totalSent) * 100 : 0,
      complaintRate: totalSent > 0 ? (complaints / totalSent) * 100 : 0,
    };
  }

  /**
   * Calculate total recipients for a campaign
   */
  private async calculateTotalRecipients(
    emailGroupIds: number[], 
    customEmails: string[]
  ): Promise<number> {
    let total = 0;

    // Count from email groups
    if (emailGroupIds.length > 0) {
      const groupMemberCount = await this.emailGroupMemberRepo.count({
        where: {
          groupId: In(emailGroupIds),
          status: MemberStatus.ACTIVE
        }
      });
      total += groupMemberCount;
    }

    // Add custom emails
    total += customEmails.length;

    return total;
  }

  /**
   * Queue campaign for sending
   */
  private async queueCampaignForSending(campaign: EmailCampaign): Promise<void> {
    await this.campaignQueue.add('process-campaign', {
      campaignId: campaign.id
    }, {
      priority: this.getPriorityValue(campaign.priority),
      delay: campaign.scheduledAt ? 
        Math.max(0, campaign.scheduledAt.getTime() - Date.now()) : 0
    });
  }

  /**
   * Schedule campaign job
   */
  private async scheduleCampaignJob(campaign: EmailCampaign): Promise<void> {
    const delay = campaign.scheduledAt.getTime() - Date.now();
    
    await this.campaignQueue.add('send-scheduled-campaign', {
      campaignId: campaign.id
    }, {
      delay: Math.max(0, delay),
      priority: this.getPriorityValue(campaign.priority)
    });
  }

  /**
   * Get priority value for queue
   */
  private getPriorityValue(priority: CampaignPriority): number {
    switch (priority) {
      case CampaignPriority.URGENT: return 1;
      case CampaignPriority.HIGH: return 2;
      case CampaignPriority.MEDIUM: return 3;
      case CampaignPriority.LOW: return 4;
      default: return 3;
    }
  }

  /**
   * Cron job to check for scheduled campaigns
   */
  @Cron(CronExpression.EVERY_MINUTE)
  async checkScheduledCampaigns(): Promise<void> {
    const now = new Date();
    const scheduledCampaigns = await this.campaignRepo.find({
      where: {
        status: CampaignStatusEnum.SCHEDULED,
        scheduledAt: LessThan(now)
      }
    });

    for (const campaign of scheduledCampaigns) {
      try {
        await this.sendCampaignNow(campaign.id);
        this.logger.log(`Started scheduled campaign: ${campaign.id}`);
      } catch (error) {
        this.logger.error(`Failed to start scheduled campaign ${campaign.id}:`, error);
        
        // Mark as failed
        campaign.status = CampaignStatusEnum.FAILED;
        await this.campaignRepo.save(campaign);
      }
    }
  }
}
