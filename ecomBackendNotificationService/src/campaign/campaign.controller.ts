import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  ParseIntPipe,
  UseGuards,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiTags, ApiOperation, ApiResponse, ApiConsumes, ApiParam, ApiQuery, ApiBasicAuth } from '@nestjs/swagger';
import { diskStorage } from 'multer';
import { extname } from 'path';

import { CampaignService } from './campaign.service';
import { EmailGroupService } from './email-group.service';
import { CampaignReportService } from './campaign-report.service';

import { CreateCampaignDto } from './dto/create-campaign.dto';
import { UpdateCampaignDto } from './dto/update-campaign.dto';
import { ScheduleCampaignDto } from './dto/schedule-campaign.dto';
import { CreateEmailGroupDto } from './dto/create-email-group.dto';
import { ImportEmailsDto } from './dto/import-emails.dto';
import { AddMemberDto } from './dto/add-member.dto';
import { GenerateReportDto } from './dto/generate-report.dto';

import { BasicAuthGuard } from '../auth/guards/basic-auth.guard';

@ApiTags('Campaign Management')
@Controller('campaign')
@UseGuards(BasicAuthGuard)
@ApiBasicAuth()
export class CampaignController {
  constructor(
    private readonly campaignService: CampaignService,
    private readonly emailGroupService: EmailGroupService,
    private readonly campaignReportService: CampaignReportService,
  ) {}

  // ==================== CAMPAIGN ENDPOINTS ====================

  @Post()
  @ApiOperation({ summary: 'Create a new email campaign' })
  @ApiResponse({ status: 201, description: 'Campaign created successfully' })
  async createCampaign(@Body() dto: CreateCampaignDto) {
    return await this.campaignService.createCampaign(dto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all campaigns' })
  @ApiQuery({ name: 'status', required: false, description: 'Filter by campaign status' })
  @ApiQuery({ name: 'type', required: false, description: 'Filter by campaign type' })
  async getAllCampaigns(
    @Query('status') status?: string,
    @Query('type') type?: string,
  ) {
    // Implementation would include filtering logic
    return { message: 'Get all campaigns endpoint' };
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get campaign details' })
  @ApiParam({ name: 'id', description: 'Campaign ID' })
  async getCampaignDetails(@Param('id', ParseIntPipe) id: number) {
    return await this.campaignService.getCampaignDetails(id);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update campaign (draft only)' })
  @ApiParam({ name: 'id', description: 'Campaign ID' })
  async updateCampaign(
    @Param('id', ParseIntPipe) id: number,
    @Body() dto: UpdateCampaignDto,
  ) {
    return await this.campaignService.updateCampaign(id, dto);
  }

  @Post(':id/schedule')
  @ApiOperation({ summary: 'Schedule campaign for sending' })
  @ApiParam({ name: 'id', description: 'Campaign ID' })
  async scheduleCampaign(
    @Param('id', ParseIntPipe) id: number,
    @Body() dto: ScheduleCampaignDto,
  ) {
    return await this.campaignService.scheduleCampaign(id, dto);
  }

  @Post(':id/send')
  @ApiOperation({ summary: 'Send campaign immediately' })
  @ApiParam({ name: 'id', description: 'Campaign ID' })
  async sendCampaign(@Param('id', ParseIntPipe) id: number) {
    return await this.campaignService.sendCampaignNow(id);
  }

  @Post(':id/pause')
  @ApiOperation({ summary: 'Pause campaign sending' })
  @ApiParam({ name: 'id', description: 'Campaign ID' })
  async pauseCampaign(@Param('id', ParseIntPipe) id: number) {
    return await this.campaignService.pauseCampaign(id);
  }

  @Post(':id/resume')
  @ApiOperation({ summary: 'Resume paused campaign' })
  @ApiParam({ name: 'id', description: 'Campaign ID' })
  async resumeCampaign(@Param('id', ParseIntPipe) id: number) {
    return await this.campaignService.resumeCampaign(id);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Cancel campaign' })
  @ApiParam({ name: 'id', description: 'Campaign ID' })
  async cancelCampaign(@Param('id', ParseIntPipe) id: number) {
    return await this.campaignService.cancelCampaign(id);
  }

  // ==================== EMAIL GROUP ENDPOINTS ====================

  @Post('email-groups')
  @ApiOperation({ summary: 'Create a new email group' })
  async createEmailGroup(@Body() dto: CreateEmailGroupDto) {
    return await this.emailGroupService.createEmailGroup(dto);
  }

  @Get('email-groups')
  @ApiOperation({ summary: 'Get all email groups' })
  async getAllEmailGroups() {
    return await this.emailGroupService.getAllEmailGroups();
  }

  @Get('email-groups/:id')
  @ApiOperation({ summary: 'Get email group details' })
  @ApiParam({ name: 'id', description: 'Email group ID' })
  @ApiQuery({ name: 'includeMembers', required: false, description: 'Include group members' })
  async getEmailGroup(
    @Param('id', ParseIntPipe) id: number,
    @Query('includeMembers') includeMembers?: boolean,
  ) {
    return await this.emailGroupService.getEmailGroupById(id, includeMembers);
  }

  @Post('email-groups/:id/members')
  @ApiOperation({ summary: 'Add member to email group' })
  @ApiParam({ name: 'id', description: 'Email group ID' })
  async addMemberToGroup(
    @Param('id', ParseIntPipe) id: number,
    @Body() dto: AddMemberDto,
  ) {
    return await this.emailGroupService.addMember(id, dto);
  }

  @Post('email-groups/:id/import')
  @ApiOperation({ summary: 'Import emails to group' })
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(
    FileInterceptor('file', {
      storage: diskStorage({
        destination: './uploads/imports',
        filename: (req, file, cb) => {
          const randomName = Array(32)
            .fill(null)
            .map(() => Math.round(Math.random() * 16).toString(16))
            .join('');
          cb(null, `${randomName}${extname(file.originalname)}`);
        },
      }),
      fileFilter: (req, file, cb) => {
        if (file.mimetype === 'text/csv' || file.originalname.endsWith('.csv')) {
          cb(null, true);
        } else {
          cb(new Error('Only CSV files are allowed'), false);
        }
      },
    }),
  )
  async importEmails(
    @Param('id', ParseIntPipe) id: number,
    @Body() dto: ImportEmailsDto,
    @UploadedFile() file?: Express.Multer.File,
  ) {
    if (file) {
      dto.csvFile = file.path;
    }
    return await this.emailGroupService.importEmails(id, dto);
  }

  @Delete('email-groups/:groupId/members/:email')
  @ApiOperation({ summary: 'Remove member from email group' })
  @ApiParam({ name: 'groupId', description: 'Email group ID' })
  @ApiParam({ name: 'email', description: 'Member email address' })
  async removeMemberFromGroup(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('email') email: string,
  ) {
    return await this.emailGroupService.removeMember(groupId, email);
  }

  @Post('email-groups/:groupId/members/:email/unsubscribe')
  @ApiOperation({ summary: 'Unsubscribe member from email group' })
  @ApiParam({ name: 'groupId', description: 'Email group ID' })
  @ApiParam({ name: 'email', description: 'Member email address' })
  async unsubscribeMember(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('email') email: string,
  ) {
    return await this.emailGroupService.unsubscribeMember(groupId, email);
  }

  @Get('email-groups/:id/members')
  @ApiOperation({ summary: 'Get email group members' })
  @ApiParam({ name: 'id', description: 'Email group ID' })
  @ApiQuery({ name: 'limit', required: false, description: 'Number of members to return' })
  @ApiQuery({ name: 'offset', required: false, description: 'Offset for pagination' })
  async getGroupMembers(
    @Param('id', ParseIntPipe) id: number,
    @Query('limit') limit = 100,
    @Query('offset') offset = 0,
  ) {
    return await this.emailGroupService.getActiveMembers(id, +limit, +offset);
  }

  @Get('email-groups/:id/stats')
  @ApiOperation({ summary: 'Get email group statistics' })
  @ApiParam({ name: 'id', description: 'Email group ID' })
  async getGroupStatistics(@Param('id', ParseIntPipe) id: number) {
    return await this.emailGroupService.getGroupStatistics(id);
  }

  // ==================== REPORTING ENDPOINTS ====================

  @Post(':id/reports')
  @ApiOperation({ summary: 'Generate campaign report' })
  @ApiParam({ name: 'id', description: 'Campaign ID' })
  async generateReport(
    @Param('id', ParseIntPipe) id: number,
    @Body() dto: GenerateReportDto,
  ) {
    return await this.campaignReportService.generateCampaignReport(id, dto);
  }

  @Get(':id/reports')
  @ApiOperation({ summary: 'Get all reports for campaign' })
  @ApiParam({ name: 'id', description: 'Campaign ID' })
  async getCampaignReports(@Param('id', ParseIntPipe) id: number) {
    return await this.campaignReportService.getCampaignReports(id);
  }

  @Get(':id/summary')
  @ApiOperation({ summary: 'Get campaign summary statistics' })
  @ApiParam({ name: 'id', description: 'Campaign ID' })
  async getCampaignSummary(@Param('id', ParseIntPipe) id: number) {
    return await this.campaignReportService.getCampaignSummary(id);
  }

  @Get(':id/geographic-report')
  @ApiOperation({ summary: 'Get geographic performance report' })
  @ApiParam({ name: 'id', description: 'Campaign ID' })
  async getGeographicReport(@Param('id', ParseIntPipe) id: number) {
    return await this.campaignReportService.getGeographicReport(id);
  }

  @Get(':id/time-based-report')
  @ApiOperation({ summary: 'Get time-based performance report' })
  @ApiParam({ name: 'id', description: 'Campaign ID' })
  async getTimeBasedReport(@Param('id', ParseIntPipe) id: number) {
    return await this.campaignReportService.getTimeBasedReport(id);
  }

  @Get(':id/engagement-report')
  @ApiOperation({ summary: 'Get engagement analysis report' })
  @ApiParam({ name: 'id', description: 'Campaign ID' })
  async getEngagementReport(@Param('id', ParseIntPipe) id: number) {
    return await this.campaignReportService.getEngagementReport(id);
  }
}
