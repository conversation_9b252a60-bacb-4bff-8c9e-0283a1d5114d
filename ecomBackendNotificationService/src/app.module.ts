import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { MailService } from 'libs/common/services/mail.service';
import { MailerModule } from './mail/mail.module';
import { EmailTemplateModule } from './mail-template/email-template.module';
import { ConfigModule } from '@nestjs/config';
import { appConfig, redisConfig } from './config/app-config';
import { databaseConfig } from './config/database-config';
import { jwtConfig } from './config/jwt-config';
import { gmailerConfig, mailerConfig } from './config/mailer.config';
import { DatabaseModule } from 'libs/common/database/database.module';
import { BullModule } from '@nestjs/bullmq';
import { CustomBullModule } from 'libs/common/bullmq/bullmq.module';
import { TranslationModule } from './translation/translation.module';
import { UserModule } from './user/user.module';
import { AuthModule } from './auth/auth.module';
import { CampaignModule } from './campaign/campaign.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      load: [
        appConfig,
        redisConfig,
        databaseConfig,
        jwtConfig,
        mailerConfig,
        gmailerConfig
      ],
      isGlobal: true,
    }),
    // BullModule.forRoot({
    //   connection: {
    //     host: 'localhost',
    //     port: 6379,
    //   },
    // }),
    CustomBullModule,
    DatabaseModule,
    AuthModule,
    UserModule,
    MailerModule,
    EmailTemplateModule,
    TranslationModule,
    CampaignModule,
  ],
  controllers: [
    AppController
  ],
  providers: [
    AppService,
    // MailService,
  ],
})
export class AppModule { }
