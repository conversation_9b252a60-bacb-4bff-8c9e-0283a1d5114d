import { MailerOptions } from '@nestjs-modules/mailer';
import { HandlebarsAdapter } from '@nestjs-modules/mailer/dist/adapters/handlebars.adapter';
import * as path from 'path';
import { registerAs } from '@nestjs/config';

export const gmailerConfig = registerAs('gmailer', (): MailerOptions => {
  return {
    transport: {
      service: 'gmail',
      auth: {
        user: process.env.GMAIL_SMTP_USERNAME,
        pass: process.env.GMAIL_SMTP_PASSWORD,
      },
    },
    preview: false,
    template: {
      dir: path.join(__dirname, 'templates'),
      adapter: new HandlebarsAdapter(), // or any other adapter you prefer
      options: {
        strict: true,
      },
    },
    defaults: {
      from: `"Pantoneclo" <${process.env.SES_FROM_MAIL}>`,
    },
  } as MailerOptions;
});

export const mailerConfig = registerAs('mailer', (): MailerOptions => {
  let transport = null;
  if (process.env.SMTP_SERVICE == 'gmail') {
    transport = {
      service: 'gmail',
      auth: {
        user: process.env.SES_SMTP_USERNAME,
        pass: process.env.SES_SMTP_PASSWORD,
      },
    };
  } else {
    transport = {
      host: process.env.SES_HOST,
      port: process.env.SES_PORT,
      ignoreTLS: false,
      secure: process.env.SES_SMTP_SECURE ? process.env.SES_SMTP_SECURE : false,
      auth: {
        user: process.env.SES_SMTP_USERNAME,
        pass: process.env.SES_SMTP_PASSWORD,
      },
    };
  }

  return {
    transport: transport,
    preview: false,
    template: {
      dir: path.join(__dirname, 'templates'),
      adapter: new HandlebarsAdapter(), // or any other adapter you prefer
      options: {
        strict: true,
      },
    },
    defaults: {
      from: `"Pantoneclo" <${process.env.SES_FROM_MAIL}>`,
    },
  } as MailerOptions;
});
