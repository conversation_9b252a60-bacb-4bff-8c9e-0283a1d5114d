import { registerAs } from '@nestjs/config';

export interface IDatabaseConfig {
  type: string;
  host: string;
  port: number;
  username: string;
  password: string;
  database: string;
  dbSSL: string;
}

export const databaseConfig = registerAs('database', (): IDatabaseConfig => {
  console.log('DB:::', {
    type: process.env.DB_TYPE,
    host: process.env.DB_HOST,
    port: parseInt(process.env.DB_PORT) || 5432,
    username: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_DATABASE,
    dbSSL: process.env.DB_SSL,
  });
  
  const config: IDatabaseConfig = {
    type: process.env.DB_TYPE,
    host: process.env.DB_HOST,
    port: parseInt(process.env.DB_PORT) || 5432,
    username: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_DATABASE,
    dbSSL: process.env.DB_SSL,
  };
  return config;
});
