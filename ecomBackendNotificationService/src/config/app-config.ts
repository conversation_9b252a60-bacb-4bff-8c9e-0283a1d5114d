import { registerAs } from '@nestjs/config';

export interface IAppConfig {
  port: number;
}

export const appConfig = registerAs('app', (): IAppConfig => {
  const config: IAppConfig = {
    port: parseInt(process.env.PORT) || 3000,
  };
  return config;
});


export interface IRedisConfig {
  host: string;
  port: number;
  username: string;
  password: string;
  database: number;
  bull_register_email_queue: string;
}

export const redisConfig = registerAs('redis-config', (): IRedisConfig => {
  const config: IRedisConfig = {
    host: process.env.REDIS_HOST,
    port: parseInt(process.env.REDIS_PORT) || 6379,
    username: process.env.REDIS_USERNAME,
    password: process.env.REDIS_PASSWORD,
    database: parseInt(process.env.REDIS_DATABASE),
    bull_register_email_queue: process.env.BULL_REGISTER_EMAIL_QUEUE || 'email_queue',
  };
  return config;
});


