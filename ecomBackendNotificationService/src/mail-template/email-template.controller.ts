import {
    Controller,
    Post,
    Put,
    Get,
    Param,
    Body,
    Query,
    ParseIntPipe,
    Delete,
    UseGuards,
} from '@nestjs/common';
import { CreateEmailTemplateDto, CreateEmailTemplateTranslationDto } from './dto/create-email-template.dto';
import { UpdateEmailTemplatedDto } from './dto/update-email-template.dto';
import { EmailTemplateService } from './email-template.service';
import { ApiBasicAuth, ApiTags } from '@nestjs/swagger';
import { PageOptionsDto } from 'libs/common/pagination/page-options.dto';
import { BasicAuthGuard } from 'src/auth/guards/basic-auth.guard';

@ApiTags('email-template')
@Controller('email-template')
export class EmailTemplateController {
    constructor(private readonly currentService: EmailTemplateService) { }

    @UseGuards(BasicAuthGuard)
    @Post()
    async createTemplate(
        @Body() body: CreateEmailTemplateDto,
    ) {
        return this.currentService.createTemplate(body);
    }

    // @UseGuards(JwtAuthGuard, PermissionsGuard)
    // @Permissions(`${DynamicModuleEnum.PRODUCT}:${DynamicPermissionEnum.READ_MANY}`)
    @UseGuards(BasicAuthGuard)
    @Get('findAllDataByPagination')
    public async findAllDataByPagination(
        @Query('name') name: string,
        @Query() pageOptionsDto: PageOptionsDto,
    ) {
        console.log('pageOptionsDto:::', pageOptionsDto);

        return this.currentService.findAllDataByPagination(name, pageOptionsDto);
    }

    @UseGuards(BasicAuthGuard)
    @Put(':id')
    async updateTemplate(
        @Param('id') id: number,
        @Body() body: UpdateEmailTemplatedDto,
    ) {
        return this.currentService.updateTemplate(id, body);
    }

    @UseGuards(BasicAuthGuard)
    @Get(':id')
    async getTemplateByCountry(
        @Param('id', ParseIntPipe) id: number,
        @Query('countryId', ParseIntPipe) countryId: number,
        // @Query('locale') locale?: string,
    ) {
        return this.currentService.getTemplateByCountryId(id, countryId);
    }

    // @Post(':id/translate')
    // async addTranslation(
    //     @Param('id', ParseIntPipe) templateId: number,
    //     @Query('country', ParseIntPipe) country: number,
    //     // @Query('locale') locale: string,
    //     @Body()
    //     body: {
    //         subject: string;
    //         htmlContent: string;
    //         mjmlJson?: any;
    //     },
    // ) {
    //     return this.currentService.addTranslation(templateId, country, body);
    // }


    @UseGuards(BasicAuthGuard)
    @Delete('delete/:id')
    public async delete(@Param('id') id: number) {
        return this.currentService.delete(id);
    }

    @UseGuards(BasicAuthGuard)
    @Post(':id/email-template/translations')
    async addEmailTemplateTranslation(
        @Param('id', ParseIntPipe) templateId: number,
        @Query('country', ParseIntPipe) country: number,
        @Body() body: CreateEmailTemplateTranslationDto,
    ) {
        return this.currentService.addEmailTemplateTranslation(templateId, country, body);
    }


    @ApiBasicAuth()
    @UseGuards(BasicAuthGuard)
    @Get('get-template-with-translations/by/:templateId')
    async getTemplateWithTranslations(@Param('templateId', ParseIntPipe) templateId: number) {
        return this.currentService.getTemplateWithTranslations(templateId);
    }



}