import {
    Controller,
    Post,
    Put,
    Get,
    Param,
    Body,
    Query,
    ParseIntPipe,
    Res,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import { EmailSendHistoryService } from './email-send-history.service';
import { EmailSendHistory } from './entity/email-template-history.entity';

@ApiTags('email-tracking')
@Controller('email-tracking')
export class EmailTrackingController {
    constructor(
        private readonly sendHistoryRepo: EmailSendHistoryService,
    ) { }

    /**
     * @example <img src="https://your-domain.com/api/email-tracking/open?email={{email}}&campaignId={{campaignId}}" width="1" height="1" style="display:none;" />
     * @param email 
     * @param campaign 
     * @param res 
     * @returns 
     */
    @Get('open')
    async trackOpen(
        @Query('email') email: string,
        @Query('campaign') campaign: number,
        @Res() res: Response,
    ) {
        
        try {
            const result = await this.sendHistoryRepo.trackOpen(email, campaign);
    
        } catch (error) {
            console.log('error:::', error);
            
        }
        const pixel = Buffer.from(
            'R0lGODlhAQABAIAAAAAAAP///ywAAAAAAQABAAACAUwAOw==',
            'base64',
        );

        res.set('Content-Type', 'image/gif');
        return res.end(pixel);
    }


    /**
     * @example <a href="https://your-domain.com/api/email-tracking/click?email={{email}}&campaignId={{campaignId}}&url={{encodedUrl}}">Click here</a>
     * @param email 
     * @param campaign 
     * @param url 
     * @param res 
     * @returns 
     */
    @Get('click')
    async trackClick(
        @Query('email') email: string,
        @Query('campaign') campaign: number,
        @Query('url') url: string,
        @Res() res: Response,
    ) {
        const result = await this.sendHistoryRepo.trackClick(email, campaign, url);

        return res.redirect(url);
    }




    // ---------------------------------------

    @Get(':campaignId/stats')
    async getCampaignStats(@Param('campaign') campaign: number) {
        const emails = await this.sendHistoryRepo.getStatsByCampaign(campaign);

        const total = emails.length;
        const opened = emails.filter(e => e.opened).length;
        const clicked = emails.filter(e => e.clicked).length;

        return {
            total,
            opened,
            clicked,
            openRate: ((opened / total) * 100).toFixed(2),
            clickRate: ((clicked / total) * 100).toFixed(2),
        };
    }


    @Post('provider')
    async handleWebhook(@Body() body: any) {
        // For example: from SendGrid
        for (const event of body) {
            const { email, event: type, timestamp } = event;
            const data: Partial<EmailSendHistory> = {};

            if (type === 'bounce') data.bounced = true;
            if (type === 'delivered') data.deliveredAt = new Date(timestamp * 1000);
            if (type === 'spamreport') data.complaint = true;

            await this.sendHistoryRepo.updateByProvider(email, data);
        }

        return { received: true };
    }
}