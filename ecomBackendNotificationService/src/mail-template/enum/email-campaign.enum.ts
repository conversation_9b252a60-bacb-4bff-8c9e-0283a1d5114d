// Enhanced Campaign Status Enum
export enum CampaignStatusEnum {
  DRAFT = 'draft',
  SCHEDULED = 'scheduled',
  SENDING = 'sending',
  SENT = 'sent',
  PAUSED = 'paused',
  CANCELLED = 'cancelled',
  FAILED = 'failed',
  COMPLETED = 'completed'
}

// Enhanced Delivery Status Enum
export enum CampaignDeliveryStatusEnum {
  PENDING = 'pending',
  QUEUED = 'queued',
  SENDING = 'sending',
  SENT = 'sent',
  DELIVERED = 'delivered',
  OPENED = 'opened',
  CLICKED = 'clicked',
  BOUNCED = 'bounced',
  COMPLAINED = 'complained',
  UNSUBSCRIBED = 'unsubscribed',
  FAILED = 'failed',
  SUPPRESSED = 'suppressed'
}

// Campaign Type Enum
export enum CampaignType {
  PROMOTIONAL = 'promotional',
  NEWSLETTER = 'newsletter',
  TRANSACTIONAL = 'transactional',
  ABANDONED_CART = 'abandoned_cart',
  WELCOME_SERIES = 'welcome_series',
  RE_ENGAGEMENT = 're_engagement',
  PRODUCT_ANNOUNCEMENT = 'product_announcement',
  SEASONAL = 'seasonal',
  SURVEY = 'survey',
  EVENT_INVITATION = 'event_invitation'
}

// Campaign Priority Enum
export enum CampaignPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent'
}

// Sending Status Enum
export enum SendingStatus {
  NOT_STARTED = 'not_started',
  IN_PROGRESS = 'in_progress',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}
