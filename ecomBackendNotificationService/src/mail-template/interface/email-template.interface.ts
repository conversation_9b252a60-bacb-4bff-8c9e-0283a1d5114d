
export interface IEmailTemplate {
    id: number;
    name: string;
    subject: string;
    builderContent: Record<string, any>;
    htmlContent: string;
    dynamicVariable?: Record<string, any>;
    translations?: IEmailTranslation[];
}

export interface IEmailTranslation {
    id: number;
    template?: number | IEmailTemplate;
    countryId: number;
    locale: string;
    subject: string;
    htmlContent: string;
}