import { EmailCampaign } from "../../campaign/entity/email-campaign.entity";
import { EmailTemplate } from "../entity/email-template.entity";
import { CampaignDeliveryStatusEnum, CampaignStatusEnum } from "../enum/email-campaign.enum";

export interface IEmailCampaign {
    id: number;
    name: string;
    template: number | EmailTemplate;
    scheduledAt: Date;
    status: CampaignStatusEnum;
    targetGroup: any;
}

export interface IEmailSendHistory {
    id: number;
    campaignId?: number;
    emailTemplateId?: number;
    campaign?: number | EmailCampaign;
    recipientEmail: string;
    countryId: number;
    locale: string;
    status: CampaignDeliveryStatusEnum;
    errorMessage?: string;
    sentAt?: Date;
    opened: boolean;
    clicked: boolean;
    openedAt?: Date;
    clickedAt?: Date;
    bounced: boolean;
    deliveredAt?: Date;
    complaint: boolean;
    messageBody?: string;
}