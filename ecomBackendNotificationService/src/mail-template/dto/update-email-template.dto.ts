import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import { Column } from 'typeorm';
import {
  IsString,
  IsOptional,
  IsNotEmpty,
  IsNumber
} from 'class-validator';
import { CreateEmailTemplateDto } from './create-email-template.dto';

export class UpdateEmailTemplatedDto extends PartialType(CreateEmailTemplateDto) {
  // @ApiProperty()
  // @IsNotEmpty()
  // @IsNumber()
  // id: number;

  // @ApiPropertyOptional()
  // subject: string;

  // @ApiPropertyOptional()
  // builderContent: Record<string, any>;

  // @ApiPropertyOptional()
  // htmlContent: string;

  // @ApiPropertyOptional()
  // dynamicVariable: Record<string, any>;
}
