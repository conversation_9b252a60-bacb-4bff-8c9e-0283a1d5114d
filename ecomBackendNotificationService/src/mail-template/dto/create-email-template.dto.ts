import {
  IsString,
  <PERSON>NotEmpty,
  <PERSON>N<PERSON><PERSON>,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateEmailTemplateDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiPropertyOptional()
  subject: string;

  @ApiPropertyOptional()
  type: string;

  @ApiPropertyOptional()
  builderContent: object;

  @ApiPropertyOptional()
  htmlContent: string;

  @ApiPropertyOptional()
  dynamicVariable: object;

}

export class CreateEmailTemplateTranslationDto {
  // @ApiProperty()
  // @IsNotEmpty()
  // @IsNumber()
  // templateId: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  countryId: number;

  @ApiPropertyOptional()
  locale: string;

  @ApiPropertyOptional()
  // @IsNotEmpty()
  subject: string;

  @ApiPropertyOptional()
  // @IsNotEmpty()
  htmlContent: string;

  @ApiPropertyOptional()
  // @IsNotEmpty()
  dynamicVariable: Record<string, any>;
} 
