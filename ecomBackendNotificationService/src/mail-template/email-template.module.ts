import { MiddlewareConsumer, Module, RequestMethod } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
// import * as fsStore from 'cache-manager-fs-hash';
// import { CacheModule } from '@nestjs/cache-manager';
import { EmailTemplate, EmailTranslation } from './entity/email-template.entity';
import { EmailTemplateService } from './email-template.service';
import { EmailTemplateRepository } from './email-template.repository';
import { EmailTemplateController } from './email-template.controller';
import { Repository } from 'typeorm';
import { EmailTrackingController } from './email-tracking.controller';
import { EmailSendHistoryService } from './email-send-history.service';
import { EmailSendHistoryRepository } from './email-send-history.repository';

@Module({
  imports: [
    TypeOrmModule.forFeature([EmailTemplate, EmailTranslation]),
    // CacheModule.register({
    //   store: fsStore, // File system cache store
    //   options: {
    //     path: './cache', // Ensure this directory exists
    //     ttl: 3600000, // 1 hour
    //     maxsize: **********, // 1GB
    //     zip: true, // Compress cache
    //     preventfill: false, // Ensure cache is preloaded on start
    //   },
    // }),
  ],
  providers: [
    EmailTemplateService,
    EmailTemplateRepository,
    Repository<EmailTranslation>,
    EmailSendHistoryService,
    EmailSendHistoryRepository,
  ],
  controllers: [
    EmailTemplateController, 
    EmailTrackingController
  ],
  exports: [EmailTemplateService],
})
export class EmailTemplateModule {}
