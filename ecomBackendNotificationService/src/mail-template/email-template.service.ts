import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { EmailTemplate, EmailTranslation } from "./entity/email-template.entity";
import { Repository } from "typeorm";
import { CreateEmailTemplateDto, CreateEmailTemplateTranslationDto } from "./dto/create-email-template.dto";
import { UpdateEmailTemplatedDto } from "./dto/update-email-template.dto";
import { EmailTemplateRepository } from "./email-template.repository";
import { ServiceResponse } from "libs/common/utils/service-response";
import { PageOptionsDto } from "libs/common/pagination/page-options.dto";
import { mapTranslations } from "libs/common/utils/translation-helper";


// 5. EmailTemplateService
@Injectable()
export class EmailTemplateService {
    constructor(
        // @InjectRepository(EmailTemplate) private templateRepo: Repository<EmailTemplate>,
        // @InjectRepository(EmailTranslation) private translationRepo: Repository<EmailTranslation>
        private readonly emailTemplateRepository: EmailTemplateRepository,
    ) { }

    async createTemplate(data: CreateEmailTemplateDto): Promise<ServiceResponse> {
        const result = await this.emailTemplateRepository.add(data);
        if (!result) {
            return new ServiceResponse(
                null,
                'Failed to saved Data! Please try again',
            );
        }
        return new ServiceResponse(result, 'Data saved successfully');
    }

    async updateTemplate(id: number, data: UpdateEmailTemplatedDto): Promise<ServiceResponse> {
        const result = await this.emailTemplateRepository.updateOne(id, data);
        if (!result) {
            return new ServiceResponse(
                null,
                'Failed to update Data! Please try again',
            );
        }
        return new ServiceResponse(result, 'Data successfully updated');
    }

    async getTemplateByCountryId(id: number, countryId: number): Promise<ServiceResponse> {
        // return this.emailTemplateRepo.getTemplateByCountryId(id, countryId);
        const result = await this.emailTemplateRepository.findOne({
            where: { id },
            relations: [
                'translations',
                'translations.translationKeys',
                'translations.translationKeys.translationValues',
                'translationKeys',
                'translationKeys.translationValues',

            ],
        });
        if (!countryId) return;
        const translation = result?.translations?.find((t) => (t.countryId === countryId));  // (t.locale === locale) || 
        // return {
        //     ...result,
        //     subject: translation?.subject ?? result.subject,
        //     htmlContent: translation?.htmlContent ?? result.htmlContent
        // };
        return new ServiceResponse({
            ...result,
            subject: translation?.subject ?? result?.subject,
            htmlContent: translation?.htmlContent ?? result?.htmlContent
        }, 'Data successfully fetched');
    }

    // async addTranslation(templateId: number, countryId: number, translationData: { subject: string; htmlContent: string; mjmlJson?: any }) {
    //     return this.emailTemplateRepository.addTranslation(templateId, countryId, translationData);
    // }

    async findAllDataByPagination(
        name: string,
        pageOptionsDto: PageOptionsDto,
    ): Promise<ServiceResponse> {
        // const result =
        //   await this.repository.findAllDataByPagination(pageOptionsDto);
        const result = await this.emailTemplateRepository.findAllDataByPagination(name, pageOptionsDto);
        if (!result) {
            return new ServiceResponse(result, 'Template not found');
        }
        return new ServiceResponse(
            result.itemList,
            'All template found successfully',
            result.meta,
        );
    }


    async getTemplateWithTranslations(templateId: number): Promise<ServiceResponse> {
        const template = await this.emailTemplateRepository.findOne({
            where: { id: templateId },
            relations: ['translationKeys', 'translationKeys.translationValues'],
        });

        if (!template) {
            return new ServiceResponse(null, 'Template not found');
        }

        const translations = mapTranslations(template.translationKeys)

        // for (const key of template.translationKeys) {
        //     for (const val of key.translationValues) {
        //         if (!translations[val.locale]) {
        //             translations[val.locale] = {};
        //         }
        //         translations[val.locale][key.key] = val.value;
        //     }
        // }

        // await Promise.all(
        //     template.translationKeys.map(async (key) => {
        //         await Promise.all(
        //             key.translationValues.map(async (val) => {
        //                 // Example async operation:
        //                 // const value = await someAsyncFetch(val);
        //                 const value = val.value;

        //                 if (!translations[val.locale]) {
        //                     translations[val.locale] = {};
        //                 }
        //                 translations[val.locale][key.key] = value;
        //             }),
        //         );
        //     }),
        // );


        // return {
        //     template,
        //     translations
        // };

        return new ServiceResponse({
            template,
            translations
        }, 'Data successfully fetched');
    }


    async addEmailTemplateTranslation(
        templateId: number,
        countryId: number,
        translationData: CreateEmailTemplateTranslationDto) {
        const result = await this.emailTemplateRepository.addEmailTemplateTranslation(templateId, countryId, translationData);
        if (!result) {
            return new ServiceResponse(
                null,
                'Failed to saved Data! Please try again',
            );
        }

        return new ServiceResponse(result, 'Data saved successfully');
    }

    async getTemplateByType(type: string): Promise<ServiceResponse> {
        const result = await this.emailTemplateRepository.getTemplateByType(type);
        if (!result) {
            return new ServiceResponse(result, 'Template not found');
        }
        return new ServiceResponse(result, 'Template found successfully');
    }

    async getTemplateId(id: number): Promise<ServiceResponse> {
        const result = await this.emailTemplateRepository.findOne({ where: { id }, relations: ['translationKeys', 'translationKeys.translationValues'] });
        if (!result) {
            return new ServiceResponse(result, 'Template not found');
        }
        return new ServiceResponse(result, 'Template found successfully');
    }

    async delete(id: number): Promise<ServiceResponse> {
        if (!id) {
            return new ServiceResponse(
                null,
                `Please provide valid product id! Try again`,
            );
        }
        const result = await this.emailTemplateRepository.destroy(id);
        if (!result) {
            return new ServiceResponse(
                result,
                `Failed to delete coupon id: ${id}! Please try again`,
            );
        }
        return new ServiceResponse(id, `coupon id: ${id} successfully deleted`);
    }
}
