import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { DataSource, Repository } from "typeorm";
import { EmailSendHistory } from "./entity/email-template-history.entity";

@Injectable()
export class EmailSendHistoryRepository extends Repository<EmailSendHistory> {
    constructor(
        private readonly dataSource: DataSource,
    ) {
        super(
            EmailSendHistory,
            dataSource.createEntityManager(),
            dataSource.createQueryRunner(),
        );
    }

    async getStatsByCampaign(campaignId: number) {
        return this.find({
            where: { campaignId },
        });
    }

    async updateByProvider(email: string, data: Partial<EmailSendHistory>) {
        return this.update(
            { recipientEmail: email },
            data,
        );
    }

    async logSend(data: Partial<EmailSendHistory>) {
        return this.save(data);
    }

    async getStats(campaignId: number) {
        return this.find({
            where: { campaignId },
        });
    }

    async trackOpen(recipientEmail: string, campaignId: number) {
        return this.update(
            { recipientEmail, campaignId },
            { opened: true, openedAt: new Date() },
        );
    }

    async trackClick(recipientEmail: string, campaignId: number, url: string) {
        return this.update(
            { recipientEmail, campaignId },
            { clicked: true, clickedAt: new Date() },
        );

        // return res.redirect(url);
    }
}