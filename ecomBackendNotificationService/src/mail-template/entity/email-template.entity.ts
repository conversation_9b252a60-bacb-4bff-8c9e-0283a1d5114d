import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ToOne, OneToMany } from "typeorm";
import { IEmailTemplate, IEmailTranslation } from "../interface/email-template.interface";
import { TranslationKey } from "../../translation/entity/translation.entity";
import { AbstractEntity } from "../../../libs/common/database/entity/abstract.entity";

// 1. EmailTemplate Entity (TypeORM)
@Entity('email_templates')
export class EmailTemplate extends AbstractEntity implements IEmailTemplate {
    @Column('text', { name: 'name' })
    name: string;

    @Column('text', { name: 'subject' })
    subject: string;

    @Column({ name: 'type', nullable: true })
    type: string;

    @Column('jsonb', { name: 'builder_content' })
    builderContent: Record<string, any>;

    @Column({ name: 'html_content', type: 'text', nullable: true })
    htmlContent: string;

    @Column('jsonb', { nullable: true })
    dynamicVariable: Record<string, any>;

    @OneToMany(() => EmailTranslation, (t) => t.template)
    translations: EmailTranslation[];

    @OneToMany(() => TranslationKey, (k) => k.template)
    translationKeys: TranslationKey[];

    // @Column('jsonb', { nullable: true })
    // mjmlJson: any;
}

// 2. EmailTranslation Entity
@Entity('email_translations')
export class EmailTranslation extends AbstractEntity implements IEmailTranslation {
    @ManyToOne(() => EmailTemplate, (t) => t.translations)
    @JoinColumn({ name: 'template_id' })
    template: EmailTemplate;

    @OneToMany(() => TranslationKey, (k) => k.emailTranslation)
    translationKeys: TranslationKey[];

    // @ManyToOne(() => Country, { eager: true })
    // @JoinColumn({ name: 'country_id', referencedColumnName: 'id' })
    // country: Country;
    @Column({ name: 'country_id', nullable: true, default: null })
    countryId: number;

    @Column('character varying', { length: 2 })
    locale: string;

    @Column('text', { name: 'subject' })
    subject: string;

    @Column({ name: 'html_content' })
    htmlContent: string;

    // @OneToMany(() => TranslationKey, (k) => k.template)
    // translationKeys: TranslationKey[];

    // @Column('jsonb', { nullable: true })
    // mjmlJson: any;
}