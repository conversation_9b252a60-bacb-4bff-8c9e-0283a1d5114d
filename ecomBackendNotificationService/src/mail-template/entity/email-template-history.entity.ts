import { <PERSON>umn, CreateDateColumn, Entity, Index, JoinColumn, ManyToOne, OneToMany } from "typeorm";
// import { Country } from "../../entities/country.entity";
import { CampaignDeliveryStatusEnum } from "../enum/email-campaign.enum";
import { IEmailSendHistory } from "../interface/email-campaign.interface";
import { AbstractEntity } from "../../../libs/common/database/entity/abstract.entity";

// 4. EmailSendHistory Entity
@Entity('email_send_history')
@Index('idx_email_send_history_campaign', ['campaignId'])
@Index('idx_email_send_history_recipient', ['recipientEmail'])
@Index('idx_email_send_history_status', ['status'])
export class EmailSendHistory extends AbstractEntity implements IEmailSendHistory {
    @Column({ name: 'campaign_id', type: 'int', nullable: true, default: null })
    campaignId: number | null;

    @Column({ name: 'email_template_id', type: 'int', nullable: true, default: null })
    emailTemplateId: number | null;

    @Column({ name: 'recipient_email' })
    recipientEmail: string;

    @Column({ name: 'country_id', type: 'int', nullable: false })
    countryId: number;

    // widen if you need tags like en-US
    @Column('character varying', { name: 'locale', length: 2 })
    locale: string;

    @Column({ type: 'enum', enum: CampaignDeliveryStatusEnum, default: CampaignDeliveryStatusEnum.PENDING })
    status: CampaignDeliveryStatusEnum;

    @Column({ name: 'sent_at', type: 'timestamp', nullable: true })
    sentAt: Date | null;

    @Column({ default: false })
    opened: boolean;

    @Column({ default: false })
    clicked: boolean;

    @Column({ name: 'opened_at', type: 'timestamp', nullable: true })
    openedAt: Date | null;

    @Column({ name: 'clicked_at', type: 'timestamp', nullable: true })
    clickedAt: Date | null;

    @Column({ name: 'bounced', type: 'boolean', default: false })
    bounced: boolean;

    @Column({ name: 'delivered_at', type: 'timestamp', nullable: true })
    deliveredAt: Date | null;

    @Column({ name: 'complaint', type: 'boolean', default: false })
    complaint: boolean;

    @Column({ name: 'error_message', type: 'text', nullable: true })
    errorMessage: string | null;

    // Prefer jsonb for provider payloads; switch to 'text' if you only want strings
    @Column({ name: 'message_body', type: 'jsonb', nullable: true })
    messageBody: any | null;

    @Column({ name: 'idempotency_key', type: 'varchar', unique: true, nullable: true })
    idempotencyKey: string | null;
}



@Entity('cart_mail_history')
export class CartMailHistory extends AbstractEntity {

    @Column()
    cartUUID: string;

    @Column()
    stage: string;  // '2h' | '2d' | '7d';

    @Column({ default: false })
    sent: boolean;

    @CreateDateColumn()
    sentAt: Date;
}
