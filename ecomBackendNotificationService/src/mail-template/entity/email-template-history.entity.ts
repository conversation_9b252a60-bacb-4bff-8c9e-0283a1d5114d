import { <PERSON><PERSON><PERSON>, <PERSON>reateDate<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany } from "typeorm";
// import { Country } from "../../entities/country.entity";
import { CampaignDeliveryStatusEnum } from "../enum/email-campaign.enum";
import { IEmailSendHistory } from "../interface/email-campaign.interface";
import { AbstractEntity } from "../../../libs/common/database/entity/abstract.entity";

// 4. EmailSendHistory Entity
@Entity('email_send_history')
export class EmailSendHistory extends AbstractEntity implements IEmailSendHistory {
    // @ManyToOne(() => EmailCampaign)
    // @JoinColumn({ name: 'campaign_id' })
    @Column({ name: 'campaign_id', default: null, nullable: true })
    campaignId: number;

    @Column({ name: 'email_template_id', default: null, nullable: true })
    emailTemplateId: number;

    @Column({ name: 'recipient_email' })
    recipientEmail: string;

    // @ManyToOne(() => Country)
    // @JoinColumn({ name: 'country_id', referencedColumnName: 'id' })
    // country: Country;
    @Column({ name: 'country_id' })
    countryId: number;

    @Column('character varying', { length: 2 })
    locale: string;

    @Column({ type: 'enum', enum: CampaignDeliveryStatusEnum, default: 'pending' })
    status: CampaignDeliveryStatusEnum;

    @Column({ name: 'sent_at', nullable: true })
    sentAt: Date;

    @Column({ default: false })
    opened: boolean;

    @Column({ default: false })
    clicked: boolean;

    @Column({ name: 'opened_at', type: 'timestamp', nullable: true })
    openedAt: Date;

    @Column({ name: 'clicked_at', type: 'timestamp', nullable: true })
    clickedAt: Date;

    @Column({ name: 'bounced', default: false })
    bounced: boolean;

    @Column({ name: 'delivered_at', type: 'timestamp', nullable: true })
    deliveredAt: Date;

    @Column({ name: 'complaint', default: false })
    complaint: boolean;

    @Column({ name: 'error_message', nullable: true })
    errorMessage: string;

    @Column({ name: 'message_body', nullable: true })
    messageBody: string;


}


@Entity('cart_mail_history')
export class CartMailHistory extends AbstractEntity {

    @Column()
    cartUUID: string;

    @Column()
    stage: string;  // '2h' | '2d' | '7d';

    @Column({ default: false })
    sent: boolean;

    @CreateDateColumn()
    sentAt: Date;
}
