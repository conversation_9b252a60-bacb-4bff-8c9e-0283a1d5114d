import { Injectable } from '@nestjs/common';
import { Repository, DataSource, ILike } from 'typeorm';
import { EmailTemplate, EmailTranslation } from './entity/email-template.entity';
import { CreateEmailTemplateDto, CreateEmailTemplateTranslationDto } from './dto/create-email-template.dto';
import { UpdateEmailTemplatedDto } from './dto/update-email-template.dto';
import { PageOptionsDto } from 'libs/common/pagination/page-options.dto';
import { PageDto } from 'libs/common/pagination/page.dto';
import { PageMetaDto } from 'libs/common/pagination/page-meta.dto';

@Injectable()
export class EmailTemplateRepository extends Repository<EmailTemplate> {
  constructor(
    private readonly dataSource: DataSource,
    // private readonly emailTranslationRepo: Repository<EmailTranslation>,
  ) {
    super(
      EmailTemplate,
      dataSource.createEntityManager(),
      dataSource.createQueryRunner(),
    );
  }

  // public async findAll(page: number, limit: number): Promise<Seller[]> {
  //   return this.find({
  //     take: limit,
  //     skip: (page - 1) * limit,
  //     withDeleted: false,
  //   });
  // }

  public async findAll(): Promise<EmailTemplate[]> {
    return this.find();
  }
  public async findAllDataByPagination(
    name: string,
    pageOptionsDto: PageOptionsDto,
  ): Promise<PageDto<EmailTemplate>> {
    const where = name ? { name: ILike(`%${name}%`) } : {};

    const [list, itemCount] = await this.findAndCount({
      where,
      skip: pageOptionsDto.skip,
      take: pageOptionsDto.take,
      withDeleted: false,
      relations: ['translationKeys', 'translationKeys.translationValues'],
      order: {
        // createdAt: pageOptionsDto.order,
        id: 'DESC',
      },
    });

    const pageMetaDto = new PageMetaDto({ itemCount, pageOptionsDto });

    return new PageDto(list, pageMetaDto);
  }

  public async findById(id: number): Promise<EmailTemplate | null> {
    return this.findOne({ where: { id }, relations: ['translationKeys', 'translationKeys.translationValues'] });
  }

  public async add(createSellerDto: CreateEmailTemplateDto): Promise<EmailTemplate> {
    const newSeller = this.create(createSellerDto);
    return this.save(newSeller);
  }

  public async updateOne(id: number, updateDto: UpdateEmailTemplatedDto): Promise<any> {
    return this.update(id, updateDto);

  }

  public async destroy(id: number): Promise<number> {
    const result = await this.softDelete(id);
    return result.affected;
  }

  public async restoreData(id: number): Promise<number> {
    const result = await this.restore(id);
    return result.affected;
  }

  async getTemplateByCountryId(id: number, countryId: number) {
    const template = await this.findOne({ where: { id }, relations: ['translations'] });
    if (!countryId) return template;
    const translation = template.translations.find((t) => (t.countryId === countryId));  // (t.locale === locale) || 
    return {
      ...template,
      subject: translation?.subject ?? template.subject,
      htmlContent: translation?.htmlContent ?? template.htmlContent
    };
  }

  // async addTranslation(templateId: number, countryId: number, translationData: { subject: string; htmlContent: string; mjmlJson?: any }) {
  //   const template = await this.findOne({ where: { id: templateId } });
  //   const translation = this.emailTranslationRepo.create({ ...translationData, countryId, template });
  //   return this.emailTranslationRepo.save(translation);
  // }

  // public async activeOrInactive(id: number, status: boolean): Promise<number> {
  //   const result = await this.update(id, { isActive: status });
  //   return result.affected;
  // }


  async addEmailTemplateTranslation(
    templateId: number,
    countryId: number,
    translationData: CreateEmailTemplateTranslationDto
  ) {
    // console.log('translationData:::', translationData);
    console.log('countryId:::', countryId);
    console.log('templateId:::', templateId);

    const template = await this.findOne({ where: { id: templateId } });
    // // console.log('template:::', template);
    // console.log('template:::', { ...translationData, countryId, template });

    // const translation = this.emailTranslationRepo.create({ ...translationData, countryId, template });
    // return this.emailTranslationRepo.save(translation);

    return this.dataSource
      .createQueryBuilder()
      .insert()
      .into(EmailTranslation)
      .values({ ...translationData, countryId, template })
      .execute();
  }

  async getTemplateByType(type: string) {
    return this.findOne({
      where: { type, isActive: true },
      relations: ['translationKeys', 'translationKeys.translationValues'],
    });
    // return this.findOne({ where: { type, isActive: true } });
  }
}
