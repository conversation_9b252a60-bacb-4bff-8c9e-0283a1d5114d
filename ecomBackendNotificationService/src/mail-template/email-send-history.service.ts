import { Inject, Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { EmailSendHistoryRepository } from "./email-send-history.repository";
import { EmailSendHistory } from "./entity/email-template-history.entity";

@Injectable()
export class EmailSendHistoryService {
    constructor(
        private readonly emailSendHistoryRepository: EmailSendHistoryRepository,
    ) { }

    async getStatsByCampaign(campaign: number) {
        return this.emailSendHistoryRepository.getStatsByCampaign(campaign);
    }

    async updateByProvider(email: string, data: Partial<EmailSendHistory>) {
        return this.emailSendHistoryRepository.updateByProvider(email, data);
    }

    async logSend(data: Partial<EmailSendHistory>) {
        return this.emailSendHistoryRepository.save(data);
    }

    async getStats(campaign: number) {
        return this.emailSendHistoryRepository.getStats(campaign);
    }

    async trackOpen(recipientEmail: string, campaign: number) {
        return this.emailSendHistoryRepository.trackOpen(recipientEmail, campaign);
    }

    async trackClick(recipientEmail: string, campaign: number, url: string) {
        return this.emailSendHistoryRepository.trackClick(recipientEmail, campaign, url);
    }
}