export interface IRenderTemplate {
    receiverInfo?: {
        email: string; // multiple emails comma-separated
        subject: string;
    };
    firstName: string;
    orderId: string;
    locale: string;
    translations: Record<string, Record<string, string>>;
    cartInfo: {
        cartUUID: string;
        subtotal: string;
        tax: string;
        total: string;
        cart: Array<{
            name: string;
            discountPrice: string;
            quantity: number;
            currency: string;
            size: {
                name: string;
            };
            featuredImage: {
                imageGalleryUrls: {
                    medium: string;
                };
            };
        }>;
    };
}
