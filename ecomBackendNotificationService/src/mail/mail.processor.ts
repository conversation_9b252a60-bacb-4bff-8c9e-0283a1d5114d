// import { Processor, WorkerHost } from '@nestjs/bullmq';
// import { Job } from 'bullmq';
// import * as nodemailer from 'nodemailer';

// @Processor('mailQueue')
// export class MailProcessor extends WorkerHost {
//     //   @Process('sendMail')
//     //   async handleSendMail(job: Job) {
//     //     const { to, subject, text, html } = job.data;

//     //     const transporter = nodemailer.createTransport({
//     //       host: 'smtp.gmail.com', // or your SMTP
//     //       port: 587,
//     //       secure: false,
//     //       auth: {
//     //         user: process.env.SMTP_USER,
//     //         pass: process.env.SMTP_PASS,
//     //       },
//     //     });

//     //     await transporter.sendMail({
//     //       from: `"Pantoneclo" <${process.env.SMTP_USER}>`,
//     //       to,
//     //       subject,
//     //       text,
//     //       html,
//     //     });

//     //     console.log(`✅ Email sent to ${to}`);
//     //   }

//     async process(job: Job<any, any, string>): Promise<any> {
//         let progress = 0;
//         for (let i = 0; i < 100; i++) {
//             await doSomething(job.data);
//             progress += 1;
//             await job.updateProgress(progress);
//         }
//         return {};
//     }
// }

// mail.processor.ts
import { MailerService } from '@nestjs-modules/mailer';
import { Processor, WorkerHost, OnWorkerEvent } from '@nestjs/bullmq';
import { ConfigService } from '@nestjs/config';
import { Job } from 'bullmq';
import * as nodemailer from 'nodemailer';

@Processor('mail-queue')
export class MailProcessor extends WorkerHost {
    private readonly mailerService: MailerService;
    private readonly gmailerService: MailerService;
    constructor(private readonly configService: ConfigService) {
        super();
        this.mailerService = new MailerService(
            this.configService.get('mailer'),
            null,
        );

        this.gmailerService = new MailerService(
            this.configService.get('gmailer'),
            null,
        );

        // this.registerHandlebarsHelpers();
    }

    // @OnQueueActive()
    // onActive(job: Job) {
    //     console.log(`Processing job ${job.id} of type ${job.name}. Data: ${JSON.stringify(job.data)}`);
    // }

    async process(job: Job, token?: string): Promise<any> {
        // console.log('job:::', job);
        console.log('job token:::', token);
        const {
            to,
            subject,
            body,
            senderEmailAddress,
            attachments,
            cc,
            bcc,
            emailTemplateId
        } = job.data;

        const senderEmail = senderEmailAddress || process.env.SES_FROM_MAIL;

        try {
            return await this.mailerService.sendMail({
                to,
                subject,
                from: `"PANTONECLO" <${senderEmail}>`,
                text: body, // plain text body
                html: body, // HTML body (you can adjust if you want to use templates)
                attachments,
                cc,
                bcc,
                // headers: {
                //     'List-Unsubscribe-Post': `<https://pantoneclo.com/unsubscribe?email=${user}>`,
                //     'List-Unsubscribe': `<mailto:${user}?subject=unsubscribe>, <https://pantoneclo.com/unsubscribe?email=>`,
                // },
            });
        } catch (error) {
            try {
                return await this.gmailerService.sendMail({
                    to,
                    subject,
                    from: `"PANTONECLO" <${senderEmail}>`,
                    text: body, // plain text body
                    html: body, // HTML body (you can adjust if you want to use templates)
                    attachments,
                });
                console.error(
                    'Primary SMTP failed. Switching to fallback SMTP...',
                    error,
                );
            } catch (fallbackError) {
                console.log('Both primary and fallback SMTP failed.', fallbackError);
            }
        }
    }

    @OnWorkerEvent('active')
    onActive(job: Job) {
        console.log(`🚀 Job Started: ID ${job.id}, Name: ${job.name}`);
    }

    @OnWorkerEvent('completed')
    onCompleted(job: Job, result: any) {
        console.log(`✅ Job Completed: ID ${job.id} | Result: ${JSON.stringify(result)}`);
    }

    @OnWorkerEvent('failed')
    onFailed(job: Job, error: Error) {
        console.error(`❌ Job Failed: ID ${job.id} | Error: ${error.message}`);
    }

    @OnWorkerEvent('error')
    onError(error: Error) {
        console.error(`🔥 Worker Error: ${error.message}`);
    }

    @OnWorkerEvent('progress')
    onProgress(job: Job, progress: number) {
        console.log(`⏳ Job Progress: ID ${job.id} | Progress: ${progress}%`);
    }

    //     private async loadTranslations(): Promise<void> {
    //         try {
    //             const translationsPath = path.join(
    //                 process.cwd(),
    //                 'src',
    //                 'i18n',
    //                 'translations',
    //             );
    //             const files = await fs.promises.readdir(translationsPath);

    //             for (const file of files) {
    //                 if (file.endsWith('.json')) {
    //                     const language = path.basename(file, '.json');
    //                     const filePath = path.join(translationsPath, file);

    //                     try {
    //                         const fileContent = await fs.promises.readFile(filePath, 'utf8');
    //                         this.translations[language] = JSON.parse(fileContent);
    //                         // console.log(`Successfully loaded translations for ${language}`);
    //                     } catch (error) {
    //                         console.error(`Error loading translations for ${language}:`, error);
    //                     }
    //                 }
    //             }
    //         } catch (error) {
    //             console.error('Error loading translations:', error);
    //             throw new Error('Failed to load translations');
    //         }
    //     }

    //     private async registerHandlebarsHelpers() {
    //         Handlebars.registerHelper('t', (key: string, options) => {
    //             const language = options.data.root?.language || options.data.root?.languageCode || 'en';

    //             try {
    //                 const languageTranslations = this.translations[language];
    //                 if (!languageTranslations) {
    //                     console.warn(`No translations found for language: ${language}`);
    //                     return key;
    //                 }

    //                 // Handle nested keys (e.g., "items.sn")
    //                 const parts = key.split('.');
    //                 let translation = languageTranslations;
    //                 for (const part of parts) {
    //                     translation = translation[part];
    //                     if (translation === undefined) {
    //                         console.warn(
    //                             `No translation found for key: ${key} in language: ${language}`,
    //                         );
    //                         return key;
    //                     }
    //                 }

    //                 return translation;
    //             } catch (error) {
    //                 console.error(`Translation error for key ${key}:`, error);
    //                 return key;
    //             }
    //         });
    //         // Register the 'eq' helper
    //         Handlebars.registerHelper('eq', (arg1, arg2) => arg1 === arg2);
    //     }

    //     private async registerHandlebarsSocialMediaHelpers(socialMedia: any) {
    //         const defaultSocialLinks = {
    //             facebook: 'https://www.facebook.com/profile.php?id=61563869323043',
    //             instagram: 'https://www.instagram.com/pantoneclo/',
    //             linkedin: 'https://www.linkedin.com/company/98777995/admin/dashboard/',
    //             twitter: 'https://x.com/pantoneclo',
    //             youtube: 'https://www.youtube.com/embed/usnQk469EWs',
    //             tiktok: 'https://www.tiktok.com/@pantoneclo',
    //         };
    //         // Create social media links object with country-specific links
    //         const socialMediaLinks = {
    //             facebook: socialMedia?.facebook || defaultSocialLinks.facebook,
    //             instagram: socialMedia?.instagram || defaultSocialLinks.instagram,
    //             linkedin: socialMedia?.linkedin || defaultSocialLinks.linkedin,
    //             twitter: socialMedia?.x || defaultSocialLinks.twitter,
    //             youtube: socialMedia?.youtube || defaultSocialLinks.youtube,
    //             tiktok: socialMedia?.tiktok || defaultSocialLinks.tiktok,
    //         };

    //         // Register Handlebars helper for social media links
    //         Handlebars.registerHelper('getSocialLink', function (platform: string) {
    //             return socialMediaLinks[platform] || defaultSocialLinks[platform];
    //         });
    //     }

    //     public getTranslatedEmailSubject(languageId: number, subjectKey: string) {
    //     const languageCode = getLanguageCodeById(languageId).toLocaleLowerCase();

    //     let senderEmailAddress: string = process.env.SES_FROM_MAIL;
    //     const subject = this.translations[languageCode][subjectKey]

    //     return { subject, senderEmailAddress };
    //   }


}

