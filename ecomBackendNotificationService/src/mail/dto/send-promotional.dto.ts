import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";

class ReceiverInfoDto {
    @ApiPropertyOptional({
        description: 'Subject of the email',
        example: 'Weekend Drop'
    })
    subject?: string;

    @ApiPropertyOptional({
        description: 'Email address of the recipient',
        example: '<EMAIL>'
    })
    email?: string;              // optional label/display email

    @ApiPropertyOptional({
        description: 'List of email addresses for the recipients',
        example: ['<EMAIL>', '<EMAIL>']
    })
    userEmails!: string[];        // target recipients

    @ApiPropertyOptional({
        description: 'List of email addresses to CC',
        example: ['<EMAIL>', '<EMAIL>']
    })
    cc?: string | string[];

    @ApiPropertyOptional({
        description: 'List of email addresses to BCC',
        example: ['<EMAIL>', '<EMAIL>']
    })
    bcc?: string | string[];
}

class Schedule {
    @ApiPropertyOptional()
    sendAt?: string;

    @ApiPropertyOptional()
    delayMs?: number;

    @ApiPropertyOptional()
    cron?: string;

    @ApiPropertyOptional()
    tz?: string;
}

class CampaignMeta {
    @ApiPropertyOptional()
    campaignId?: number;

    @ApiPropertyOptional()
    countryId?: number;

    @ApiPropertyOptional()
    locale?: any;
}

class Data {
    @ApiPropertyOptional({
        description: 'Translations for the email content',
        example: {
            en: { greeting: 'Hello' },
            de: { greeting: 'Hallo' },
        },
    })
    translations?: Record<string, any>;

    @ApiPropertyOptional({
        description: 'Sender info for the email',
        example: { senderEmail: '<EMAIL>' },
    })
    senderInfo?: { senderEmail?: string };

    @ApiPropertyOptional()
    receiverInfo?: ReceiverInfoDto
}

export class SendPromotionalDto {
    @ApiProperty({
        description: 'ID of the email template to use',
        example: 123
    })
    emailTemplateId!: number;        // required

    @ApiPropertyOptional({
        description: 'MJML content for the email',
        example: '<mjml>...</mjml>'
    })
    mjml?: string;                   // optional

    @ApiPropertyOptional({
        description: 'HTML content for the email',
        example: '<div>...</div>'
    })
    htmlData?: string;               // optional

    @ApiPropertyOptional()
    data!: Data

    // optional scheduling + campaign meta
    @ApiPropertyOptional({ type: Schedule })
    schedule?: Schedule;

    @ApiPropertyOptional({ type: CampaignMeta })
    campaignMeta?: CampaignMeta;
}

// 1) Immediate bulk send (no schedule):
// POST /email/test/preview/queue/send-promotional-email-to-users
// {
//   "emailTemplateId": 123,
//   "data": {
//     "senderInfo": {"senderEmail":"<EMAIL>"},
//     "receiverInfo":{
//       "subject":"Weekend Drop",
//       "userEmails":["<EMAIL>","<EMAIL>"]
//     }
//   },
//   "campaignMeta":{"campaignId": 9001, "countryId": 1}
// }

// 2) One-time scheduled send (absolute):
// POST /email/schedule/promotional
// {
//   "emailTemplateId": 123,
//   "data": {
//     "senderInfo": {"senderEmail":"<EMAIL>"},
//     "receiverInfo":{"subject":"Tonight 8 PM", "userEmails":["<EMAIL>","<EMAIL>"]}
//   },
//   "schedule": { "sendAt": "2025-08-13T20:00:00+06:00" },
//   "campaignMeta": {"campaignId": 9002, "countryId": 1}
// }


// 3) Recurring schedule (every Monday 10:00 Asia/Dhaka):
// POST /email/schedule/promotional
// {
//   "emailTemplateId": 123,
//   "data": {
//     "receiverInfo":{"subject":"Weekly Picks", "userEmails":["<EMAIL>","<EMAIL>"]}
//   },
//   "schedule": { "cron": "0 10 * * MON", "tz":"Asia/Dhaka" },
//   "campaignMeta": {"campaignId": 9100, "countryId": 1}
// }


// 4) Get all repeatable schedules
// GET /email/schedule/repeatables


// 5) Delete a schedule by key:
// DELETE /email/schedule/promotional
// { "key": "repeat:promo-send-email:xxx..." }


// 6) Or delete by pattern (must match jobId used on creation):
// DELETE /email/schedule/promotional
// { "cron": "0 10 * * MON", "tz": "Asia/Dhaka", "jobId": "promo-email-schedule-..." }




