import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

class ImageGalleryUrlsDto {
    @ApiProperty()
    medium: string;
}

class FeaturedImageDto {
    @ApiProperty({ type: ImageGalleryUrlsDto })
    imageGalleryUrls: ImageGalleryUrlsDto;
}

class SizeDto {
    @ApiProperty()
    name: string;
}

class CartItemDto {
    @ApiProperty()
    name: string;

    @ApiProperty()
    discountPrice: string;

    @ApiProperty()
    quantity: number;

    @ApiProperty()
    currency: string;

    @ApiProperty({ type: SizeDto })
    size: SizeDto;

    @ApiProperty({ type: FeaturedImageDto })
    featuredImage: FeaturedImageDto;
}

class CartInfoDto {
    @ApiProperty()
    cartUUID: string;

    @ApiProperty()
    subtotal: string;

    @ApiProperty()
    tax: string;

    @ApiProperty()
    total: string;

    @ApiProperty({ type: [CartItemDto] })
    cart: CartItemDto[];
}

class ReceiverInfoDto {
    @ApiProperty({ type: [String] })
    userEmails: string[];

    @ApiProperty()
    email: string;

    @ApiProperty()
    subject: string;

    @ApiProperty()
    cc: string;

    @ApiProperty()
    bcc: string;
}

class TranslationsLanguageDto {
    @ApiProperty()
    greeting: string;
}

class TranslationsDto {
    @ApiProperty({ type: TranslationsLanguageDto })
    en: TranslationsLanguageDto;

    @ApiProperty({ type: TranslationsLanguageDto })
    de: TranslationsLanguageDto;
}

class SenderInfoDto {
    @ApiProperty()
    senderEmail: string;

    @ApiProperty()
    senderName: string;
}

class DataDto {
    @ApiProperty({ type: ReceiverInfoDto })
    receiverInfo: ReceiverInfoDto;

    @ApiProperty({ type: SenderInfoDto })
    senderInfo: SenderInfoDto;

    @ApiProperty()
    firstName: string;

    @ApiProperty()
    orderId: string;

    @ApiProperty()
    locale: string;

    @ApiProperty({ type: TranslationsDto })
    translations: TranslationsDto;

    @ApiProperty({ type: CartInfoDto })
    cartInfo: CartInfoDto;
}

export class EmailTemplateDto {
    @ApiPropertyOptional()
    mjml: string;

    @ApiPropertyOptional()
    htmlData: string;

    @ApiPropertyOptional()
    emailTemplateId: number;

    @ApiProperty({ type: DataDto })
    data: DataDto;
}
