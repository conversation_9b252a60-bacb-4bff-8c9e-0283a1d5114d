import {
    Is<PERSON>rray,
    IsEmail,
    IsInt,
    IsObject,
    IsOptional,
    IsString,
    ValidateNested,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';

// ========== Reusable Nested DTOs ==========

export class SizeDto {
    @ApiProperty({ example: 'XS' })
    @IsString()
    name: string;
}

export class ImageGalleryUrlsDto {
    @ApiProperty({ example: 'https://cdn.pantoneclo.com/product/image.jpg' })
    @IsString()
    medium: string;
}

export class FeaturedImageDto {
    @ApiProperty({ type: ImageGalleryUrlsDto })
    @ValidateNested()
    @Type(() => ImageGalleryUrlsDto)
    imageGalleryUrls: ImageGalleryUrlsDto;
}

export class CartItemDto {
    @ApiProperty()
    @IsString()
    name: string;

    @ApiProperty()
    @IsString()
    discountPrice: string;

    @ApiProperty()
    @IsInt()
    quantity: number;

    @ApiProperty()
    @IsString()
    currency: string;

    @ApiProperty({ type: SizeDto })
    @ValidateNested()
    @Type(() => SizeDto)
    size: SizeDto;

    @ApiProperty({ type: FeaturedImageDto })
    @ValidateNested()
    @Type(() => FeaturedImageDto)
    featuredImage: FeaturedImageDto;
}

export class CartInfoDto {
    @ApiProperty()
    @IsInt()
    countryId: number;

    @ApiProperty()
    @IsString()
    cartUUID: string;

    @ApiProperty()
    @IsString()
    subtotal: string;

    @ApiProperty()
    @IsString()
    tax: string;

    @ApiProperty()
    @IsString()
    total: string;

    @ApiProperty({ type: [CartItemDto] })
    @ValidateNested({ each: true })
    @Type(() => CartItemDto)
    cart: CartItemDto[];
}

export class ReceiverInfoDto {
    @ApiProperty({ example: '<EMAIL>, <EMAIL>' })
    @IsString()
    email: string;

    @ApiProperty()
    @IsString()
    subject: string;
}

// ========== Combined Email Data DTO ==========

export class EmailPayloadDto {
    @ApiProperty({ type: ReceiverInfoDto })
    @ValidateNested()
    @Type(() => ReceiverInfoDto)
    receiverInfo: ReceiverInfoDto;

    @ApiProperty()
    @IsString()
    firstName: string;

    @ApiProperty()
    @IsString()
    orderId: string;

    @ApiProperty()
    @IsString()
    locale: string;

    @ApiProperty({ type: CartInfoDto })
    @ValidateNested()
    @Type(() => CartInfoDto)
    cartInfo: CartInfoDto;

    @ApiProperty({
        example: {
            en: { greeting: 'Hello' },
            de: { greeting: 'Hallo' },
        },
    })
    @IsObject()
    translations: Record<string, Record<string, string>>;
}

// ========== Final DTO ==========

export class SendEmailWithQueueDto {
    @ApiPropertyOptional({ example: 6 })
    @IsInt()
    @IsOptional()
    emailTemplateId: number;

    @ApiPropertyOptional({ example: '<mjml>...</mjml>' })
    @IsString()
    @IsOptional()
    mjml: string;

    @ApiPropertyOptional({ example: '<html>...</html>' })
    @IsString()
    @IsOptional()
    htmlData: string;

    @ApiProperty({ type: EmailPayloadDto })
    @ValidateNested()
    @Type(() => EmailPayloadDto)
    data: EmailPayloadDto;
}
