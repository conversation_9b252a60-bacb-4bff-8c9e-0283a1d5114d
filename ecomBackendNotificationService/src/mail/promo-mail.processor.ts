// promo-email.processor.ts

import { MailerService } from '@nestjs-modules/mailer';
import { Processor, WorkerHost, OnWorkerEvent, OnQueueEvent } from '@nestjs/bullmq';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Job } from 'bullmq';
import { htmlToText } from 'html-to-text';
import { EmailSendHistory } from 'src/mail-template/entity/email-template-history.entity';
import { CampaignDeliveryStatusEnum } from 'src/mail-template/enum/email-campaign.enum';
import { Repository } from 'typeorm';

@Processor('promo-mail-queue')
export class PromoMailProcessor extends WorkerHost {
    private readonly mailerService: MailerService;
    private readonly gmailerService: MailerService;
    constructor(
        private readonly configService: ConfigService,
        @InjectRepository(EmailSendHistory) private emailSendHistoryRepo: Repository<EmailSendHistory>
    ) {
        super();
        this.mailerService = new MailerService(
            this.configService.get('mailer'),
            null,
        );

        this.gmailerService = new MailerService(
            this.configService.get('gmailer'),
            null,
        );

        // this.registerHandlebarsHelpers();
    }

    stripCss(html) {
        let cleaned = html.replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '');
        cleaned = cleaned.replace(/ style="[^"]*"/gi, '');
        return cleaned;
    }

    async process(job: Job, token?: string): Promise<any> {
        try {
            // const { users, subject, body, senderEmailAddress, attachments, cc, bcc } = job.data;
            const {
                to,
                subject,
                body,
                senderEmailAddress,
                attachments,
                users,
                cc,
                bcc,
                emailTemplateId
            } = job.data;

            // Process in chunks of 50 users (prevents memory overload)
            const senderEmail = senderEmailAddress || process.env.SES_FROM_MAIL;

            const chunkSize = 200;
            for (let i = 0; i < users.length; i += chunkSize) {
                const chunk = users.slice(i, i + chunkSize);
                console.log('chunk:::', chunk);

                await this.processChunk(
                    chunk,
                    {
                        subject,
                        body,
                        senderEmail,
                        attachments,
                        cc,
                        bcc,
                        emailTemplateId,
                    },
                    job
                );
            }

            console.log(`Successfully processed job ${job.id}`);
        } catch (error) {
            console.error(`Failed job ${job.id}: ${error.message}`, error.stack);
            throw error; // Triggers retry
        }
    }

    private async processChunk(
        users: any[],
        emailParams: {
            subject: string;
            body: string;
            senderEmail?: string;
            attachments?: any;
            cc?: string | string[];
            bcc?: string | string[];
            emailTemplateId?: number;
        },
        job: Job
    ) {
        const promises = users.map(async user => {
            console.log('user:::', user);

            const finalHtml = emailParams?.body.replace(/#unsubscribe_email/g, user);
            const htmlWithoutCss = this.stripCss(finalHtml);
            const plainText = htmlToText(htmlWithoutCss);

            try {
                // ✅ Try sending
                const sendResult = await this.mailerService.sendMail({
                    to: user,
                    subject: emailParams?.subject,
                    from: `"PANTONECLO" <${emailParams?.senderEmail}>`,
                    text: plainText,
                    html: finalHtml,
                    attachments: emailParams?.attachments,
                    cc: emailParams?.cc,
                    bcc: emailParams?.bcc,
                    headers: {
                        'List-Unsubscribe-Post': `<https://pantoneclo.com/unsubscribe?email=${user}>`,
                        'List-Unsubscribe': `<mailto:${user}?subject=unsubscribe>, <https://pantoneclo.com/unsubscribe?email=${user}>`,
                    },
                    sender: {
                        name: 'Pantoneclo',
                        address: emailParams?.senderEmail,
                    },
                });

                // ✅ Record success
                await this.emailSendHistoryRepo.save({
                    bounced: false,
                    clicked: false,
                    opened: false,
                    recipientEmail: user,
                    sentAt: new Date(),
                    status: CampaignDeliveryStatusEnum.SENT,
                    campaignId: job?.data?.campaignId || 0,
                    emailTemplateId: emailParams?.emailTemplateId || 0,
                    countryId: job?.data?.countryId || 0,
                    locale: job?.data?.locale || {},
                    messageBody: sendResult, // Save the mailer response
                    openedAt: null,
                    clickedAt: null,
                    deliveredAt: null,
                    complaint: false,
                    errorMessage: null,
                });

                return { user, success: true };

            } catch (error) {
                console.error(`❌ Failed to send to ${user}: ${error.message}`);

                // ✅ Record failure
                await this.emailSendHistoryRepo.save({
                    bounced: false,
                    clicked: false,
                    opened: false,
                    recipientEmail: user,
                    sentAt: new Date(),
                    status: CampaignDeliveryStatusEnum.FAILED, // Or a custom FAILED enum
                    campaignId: job?.data?.campaignId || 0,
                    emailTemplateId: emailParams?.emailTemplateId || 0,
                    countryId: job?.data?.countryId || 0,
                    locale: job?.data?.locale || {},
                    messageBody: null,
                    openedAt: null,
                    clickedAt: null,
                    deliveredAt: null,
                    complaint: false,
                    errorMessage: error.message,
                });

                return { user, success: false, error: error.message };
            }
        });

        const results = await Promise.all(promises);
        return results;
    }

    private logFailedAttempts(results: PromiseSettledResult<any>[]) {
        results.forEach(result => {
            // console.log('result:::', result);
            if (result.status === 'rejected') {
                console.warn(`Failed to send to ${result.reason.user?.email}: ${result.reason.error}`);
            }
            else if (result.status === 'fulfilled') {
                console.log(`Successfully sent to ${result}`);
            }
        });
    }

    @OnWorkerEvent('active')
    onActive(job: Job) {
        console.log(`🚀 Job Started: ID ${job.id}, Name: ${job.name}`);
    }

    @OnWorkerEvent('completed')
    onCompleted(job: Job, result: any) {
        console.log(`✅ Job Completed: ID ${job.id} | Result: ${JSON.stringify(result)}`);
    }

    @OnWorkerEvent('failed')
    onFailed(job: Job, error: Error) {
        console.error(`❌ Job Failed: ID ${job.id} | Error: ${error.message}`);
    }

    @OnWorkerEvent('error')
    onError(error: Error) {
        console.error(`🔥 Worker Error: ${error.message}`);
    }

    @OnWorkerEvent('progress')
    onProgress(job: Job, progress: number) {
        console.log(`⏳ Job Progress: ID ${job.id} | Progress: ${progress}%`);
    }

}
