import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { Injectable } from '@nestjs/common';
import { IRedisConfig, redisConfig } from 'src/config/app-config';
import { MailerService } from '@nestjs-modules/mailer';

@Injectable()
export class MailService {
  constructor(
    @InjectQueue(redisConfig().bull_register_email_queue) private mailQueue: Queue,
    private readonly mailerService: MailerService
  ) {}

  // async sendMail(to: string, subject: string, text: string) {
  //   await this.mailQueue.add('send-email', {
  //     to,
  //     subject,
  //     text,
  //   });
  // }
  
  // async promoMailSend(to: string, subject: string, text: string) {
  //   await this.mailQueue.add('promo-send-email', {
  //     to,
  //     subject,
  //     text,
  //   });
  // }

  async sendWelcomeEmail(to: string, name: string) {
    await this.mailerService.sendMail({
      to,
      subject: 'Welcome!',
      template: './welcome', // e.g., templates/welcome.hbs
      context: {
        name,
      },
    });
  }
}
