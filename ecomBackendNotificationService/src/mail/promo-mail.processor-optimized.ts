// promo-email.processor.ts

import { MailerService } from '@nestjs-modules/mailer';
import { Processor, WorkerHost, OnWorkerEvent, OnQueueEvent } from '@nestjs/bullmq';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Job } from 'bullmq';
import { htmlToText } from 'html-to-text';
import { chunkify, dedupeAndValidateEmails, sleep, stripCss } from 'libs/common/utils/promo-mail.helper';
import { PROMO_MAIL_CONFIG } from 'src/config/promo-mail.config';
import { EmailSendHistory } from 'src/mail-template/entity/email-template-history.entity';
import { CampaignDeliveryStatusEnum } from 'src/mail-template/enum/email-campaign.enum';
import { Repository } from 'typeorm';

@Processor('promo-mail-queue-optimized')
export class PromoMailProcessorOptimized extends WorkerHost {
    private readonly mailerService: MailerService;
    private readonly gmailerService: MailerService;
    constructor(
        private readonly configService: ConfigService,
        @InjectRepository(EmailSendHistory) private emailSendHistoryRepo: Repository<EmailSendHistory>
    ) {
        super();
        this.mailerService = new MailerService(
            this.configService.get('mailer'),
            null,
        );

        this.gmailerService = new MailerService(
            this.configService.get('gmailer'),
            null,
        );

        // this.registerHandlebarsHelpers();
    }

    private stripCss(html: string) {
        // your existing CSS-stripping impl (placeholder)
        return html;
    }

    private async sendOneWithRetry(params: {
        email: string;
        subject: string;
        body: string;
        senderEmail: string;
        attachments?: any;
        cc?: string | string[];
        bcc?: string | string[];
        emailTemplateId?: number;
        campaignId: number;
        countryId: number;
        locale: any;
    }) {
        const {
            email, subject, body, senderEmail, attachments, cc, bcc,
            emailTemplateId, campaignId, countryId, locale,
        } = params;

        const finalHtml = (body || '')
            .replace(/#unsubscribe_email/g, email)
            .replace(/\{\{email\}\}/g, email);

        const htmlWithoutCss = this.stripCss(finalHtml);
        const text = htmlToText(htmlWithoutCss);

        const idempotencyKey = `${campaignId}:${email}`;
        const listUnsubValue = `<${PROMO_MAIL_CONFIG.LIST_UNSUB_MAILTO}?subject=unsubscribe&body=email=${encodeURIComponent(
            email
        )}>, <${PROMO_MAIL_CONFIG.LIST_UNSUB_URL}?email=${encodeURIComponent(email)}>`;

        let attempt = 0;
        let lastError: any = null;
        let providerResponse: any = null;

        while (attempt <= 2) {
            try {
                providerResponse = await this.mailerService.sendMail({
                    to: email,
                    subject,
                    from: `"PANTONECLO" <${senderEmail}>`,
                    text,
                    html: finalHtml,
                    attachments,
                    cc,
                    bcc,
                    headers: {
                        'X-Idempotency-Key': idempotencyKey,
                        'List-Unsubscribe-Post': 'List-Unsubscribe=One-Click',
                        'List-Unsubscribe': listUnsubValue,
                    },
                    sender: { name: 'Pantoneclo', address: senderEmail },
                });

                return {
                    success: true,
                    historyRow: {
                        bounced: false,
                        clicked: false,
                        opened: false,
                        recipientEmail: email,
                        sentAt: new Date(),
                        status: CampaignDeliveryStatusEnum.SENT,
                        campaignId,
                        emailTemplateId: emailTemplateId || 0,
                        countryId,
                        locale,
                        messageBody: providerResponse,
                        openedAt: null,
                        clickedAt: null,
                        deliveredAt: null,
                        complaint: false,
                        errorMessage: null,
                        idempotencyKey,
                    },
                };
            } catch (err: any) {
                lastError = err;
                attempt++;
                const backoffMs = (250 + Math.random() * 250) * attempt ** 2;
                await sleep(backoffMs);
            }
        }

        return {
            success: false,
            historyRow: {
                bounced: false,
                clicked: false,
                opened: false,
                recipientEmail: email,
                sentAt: new Date(),
                status: CampaignDeliveryStatusEnum.FAILED,
                campaignId,
                emailTemplateId: emailTemplateId || 0,
                countryId,
                locale,
                messageBody: null,
                openedAt: null,
                clickedAt: null,
                deliveredAt: null,
                complaint: false,
                errorMessage: (lastError && lastError.message) || 'Unknown error',
                idempotencyKey,
            },
        };
    }

    // @Process('promo-send-email')
    async process(job: Job): Promise<any> {
        console.log('Processing promotional email job:', job.id);

        try {
            const {
                users = [],
                subject,
                body,
                senderEmailAddress,
                attachments,
                cc,
                bcc,
                emailTemplateId,
                campaignId = 0,
                countryId = 0,
                locale = {},
            } = job.data || {};

            const senderEmail = senderEmailAddress || process.env.SES_FROM_MAIL;
            const recipients: string[] = users;

            // Idempotency: preload already-sent for this campaign
            const already = await this.emailSendHistoryRepo.find({
                select: ['recipientEmail'],
                where: { campaignId, status: CampaignDeliveryStatusEnum.SENT },
            });
            const alreadySet = new Set(already.map(r => r.recipientEmail.toLowerCase()));
            const toSend = recipients.filter(e => !alreadySet.has(String(e || '').toLowerCase()));
            const skipped = recipients.length - toSend.length;

            let sent = 0;
            let failed = 0;

            // process in DB-sized batches
            for (let i = 0; i < toSend.length; i += PROMO_MAIL_CONFIG.BATCH_SIZE) {
                const batch = toSend.slice(i, i + PROMO_MAIL_CONFIG.BATCH_SIZE);
                const historyBuffer: any[] = [];

                // manual concurrency control
                let active = 0;
                let idx = 0;
                const queue: Promise<void>[] = [];

                const baseDelay = Math.ceil(1000 / Math.max(1, PROMO_MAIL_CONFIG.RATE_PER_SEC));

                const runNext = async () => {
                    while (active < PROMO_MAIL_CONFIG.CONCURRENCY && idx < batch.length) {
                        const email = batch[idx++];
                        active++;
                        queue.push(
                            (async () => {
                                const res = await this.sendOneWithRetry({
                                    email,
                                    subject,
                                    body,
                                    senderEmail,
                                    attachments,
                                    cc,
                                    bcc,
                                    emailTemplateId,
                                    campaignId,
                                    countryId,
                                    locale,
                                });

                                if (res.success) sent++;
                                else failed++;

                                historyBuffer.push(res.historyRow);

                                const jitter = Math.floor(Math.random() * PROMO_MAIL_CONFIG.JITTER_MS) - PROMO_MAIL_CONFIG.JITTER_MS / 2;
                                await sleep(Math.max(0, baseDelay + jitter));

                                active--;
                                await runNext();
                            })(),
                        );
                    }
                };

                await runNext();
                await Promise.allSettled(queue);

                if (historyBuffer.length) {
                    await this.emailSendHistoryRepo
                        .createQueryBuilder()
                        .insert()
                        .values(historyBuffer)
                        .execute();
                }

                const done = sent + failed + skipped;
                const total = recipients.length;
                const pct = Math.floor((done / total) * 100);
                await job.updateProgress(Math.min(99, pct));
            }

            await job.updateProgress(100);
            return { total: recipients.length, sent, failed, skipped };
        } catch (error: any) {
            console.error(`Failed job ${job.id}: ${error.message}`, error.stack);
            // Allow Bull to retry the whole job if this throws
            throw error;
        }
    }

    @OnWorkerEvent('active')
    onActive(job: Job) {
        console.log(`🚀 Job Started: ID ${job.id}, Name: ${job.name}`);
    }

    @OnWorkerEvent('completed')
    onCompleted(job: Job, result: any) {
        console.log(`✅ Job Completed: ID ${job.id} | Result: ${JSON.stringify(result)}`);
    }

    @OnWorkerEvent('failed')
    onFailed(job: Job, error: Error) {
        console.error(`❌ Job Failed: ID ${job.id} | Error: ${error.message}`);
    }

    @OnWorkerEvent('error')
    onError(error: Error) {
        console.error(`🔥 Worker Error: ${error.message}`);
    }

    @OnWorkerEvent('progress')
    onProgress(job: Job, progress: number) {
        console.log(`⏳ Job Progress: ID ${job.id} | Progress: ${progress}%`);
    }

}
