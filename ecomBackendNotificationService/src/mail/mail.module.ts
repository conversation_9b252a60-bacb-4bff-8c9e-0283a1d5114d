import { MiddlewareConsumer, Module, RequestMethod } from '@nestjs/common';
import { MailController } from './mail.controller';

import { MailTemplateConvertionService } from './mail-template-cnvertion.service';
import { MailService } from 'libs/common/services/mail.service';
import { ConfigService } from '@nestjs/config';
import { TranslationService } from 'src/translation/translation.service';
import { TranslationRepository } from 'src/translation/translation.repository';
import { BullModule } from '@nestjs/bullmq';
import { CustomBullModule } from 'libs/common/bullmq/bullmq.module';
import { MailProcessor } from './mail.processor';
import { EmailTemplateService } from 'src/mail-template/email-template.service';
import { EmailTemplateRepository } from 'src/mail-template/email-template.repository';
import { PromoMailProcessor } from './promo-mail.processor';
import { AbandonedCartService } from './abandoned-cart.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ScheduleModule } from '@nestjs/schedule';
import { CartMailHistory, EmailSendHistory } from 'src/mail-template/entity/email-template-history.entity';
import { PromoMailProcessorOptimized } from './promo-mail.processor-optimized';

// import { JwtTokenVerifierMiddleware } from '../../common/middlewares/jwt-token-verifier.middleware';
// import { TokenManagementModule } from '../../common/providers/token-management/token-management.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      CartMailHistory,
      EmailSendHistory
    ]),
    CustomBullModule,
    BullModule.registerQueue(
      {
        name: 'mail-queue',
      },
      {
        name: 'promo-mail-queue',
        defaultJobOptions: {
          attempts: 3,  // auto-retry failed jobs
          backoff: {
            type: 'exponential',
            delay: 500,
          },
        },
      },
      {
        name: 'promo-mail-queue-optimized',
        defaultJobOptions: {
          attempts: 3,  // auto-retry failed jobs
          backoff: {
            type: 'exponential',
            delay: 500,
          },
        },
      },
    ),
    ScheduleModule.forRoot(),
  ],
  controllers: [
    MailController
  ],
  providers: [
    MailService,
    MailTemplateConvertionService,
    TranslationService,
    TranslationRepository,
    MailProcessor,
    PromoMailProcessor,
    EmailTemplateService,
    EmailTemplateRepository,
    AbandonedCartService,
    PromoMailProcessorOptimized,
    // CartMailHistoryRepository
  ],
  exports: [
    MailService,
  ],
})
export class MailerModule {
  configure(consumer: MiddlewareConsumer) {
    // consumer.apply(JwtTokenVerifierMiddleware).forRoutes({ path: 'Payment', method: RequestMethod.POST });
  }
}
