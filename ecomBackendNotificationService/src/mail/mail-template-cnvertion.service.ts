// email-template.service.ts
// import * as mjml2html from 'mjml'
import * as Handlebars from 'handlebars'
import mjml2html from 'mjml'
// import * as mjml2html from 'mjml'

export class MailTemplateConvertionService {

    constructor() { }

    renderTemplate(mjml: string, htmlData: string, data: Record<string, any>) {
        Handlebars.registerHelper('t', function (key, options) {
            const translations = data?.translations;

            console.log('translations:::', options.data.root.locale);

            const locale = options.data.root.locale || 'en';
            return translations[locale]?.[key] || key;
        });

        // Country:https://www.facebook.com/pantoneclo
        // Instagram EU:https://www.instagram.com/pantoneclo
        // Youtube EU:https://www.youtube.com/@pantoneclo
        // Tiktok EU:https://www.tiktok.com/@pantoneclo

        const defaultSocialLinks = {
            facebook: 'https://www.facebook.com/pantoneclo',
            instagram: 'https://www.instagram.com/pantoneclo',
            linkedin: 'https://www.linkedin.com/company/pantoneclo',
            // twitter: 'https://x.com/pantoneclo',
            youtube: 'https://www.youtube.com/@pantoneclo',
            tiktok: 'https://www.tiktok.com/@pantoneclo',
        };

        // Create social media links object with country-specific links
        const socialMediaLinks = {
            facebook: data?.socialMediaLink?.facebook || defaultSocialLinks.facebook,
            instagram: data?.socialMediaLink?.instagram || defaultSocialLinks.instagram,
            linkedin: data?.socialMediaLink?.linkedin || defaultSocialLinks.linkedin,
            // twitter: data?.socialMediaLink?.twitter || defaultSocialLinks.twitter,
            youtube: data?.socialMediaLink?.youtube || defaultSocialLinks.youtube,
            tiktok: data?.socialMediaLink?.tiktok || defaultSocialLinks.tiktok,
        };

        // Register Handlebars helper for social media links
        Handlebars.registerHelper('socialMediaLink', function (platform: string) {
            return socialMediaLinks[platform]
        });

        const compiled = Handlebars.compile(htmlData)
        const filled = compiled(data)
        // const { html } = mjml2html(filled)

        // console.log('filled:::', filled);
        // console.log('html:::', html);

        return filled
    }


}
