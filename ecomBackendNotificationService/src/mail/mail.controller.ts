import { Body, Controller, Delete, Get, NotFoundException, Query, Res, UseGuards } from '@nestjs/common';
import { Post, UsePipes, ValidationPipe } from '@nestjs/common';
import { MailTemplateConvertionService } from './mail-template-cnvertion.service';
import { MailService } from 'libs/common/services/mail.service';
import axios from 'axios';
import * as fs from 'fs';
import * as path from 'path';
import * as Handlebars from 'handlebars';
import * as pdf from 'html-pdf';
import { ServiceResponse } from 'libs/common/utils/service-response';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { EmailTemplateService } from 'src/mail-template/email-template.service';
import { EmailTemplateDto } from './dto/create-send-mail.dto';
import { EmailTemplate } from 'src/mail-template/entity/email-template.entity';
import { BasicAuthGuard } from 'src/auth/guards/basic-auth.guard';
import { getLanguageCodeByCountryId } from 'libs/common/utils/country-helper';
import { mapTranslations } from 'libs/common/utils/translation-helper';
import { SendEmailWithQueueDto } from './dto/send-mail-template.dto';
import { chunkArray, cleanEmails } from 'libs/common/utils/promo-mail.helper';
import { SendPromotionalDto } from './dto/send-promotional.dto';
// import { Response } from 'express';

// type ScheduleInput =
//   | { sendAt?: string; delayMs?: number }                // one-time
//   | { cron: string; tz?: string };                       // recurring

type ScheduleInput = {
  sendAt?: string;     // ISO string with TZ, e.g. "2025-08-13T15:45:00+06:00"
  delayMs?: number;    // relative delay in ms
  cron?: string;       // e.g. "0 10 * * 1"
  tz?: string;         // e.g. "Asia/Dhaka"
};

type RepeatOpts = {
  cron: string;
  tz?: string;
};

type JobsOptions = {
  jobId: string;
  priority: number;
  attempts: number;
  backoff: { type: 'exponential'; delay: number };
  removeOnComplete: { age: number; count: number };
  removeOnFail: { age: number; count: number };
  delay?: number;
  repeat?: RepeatOpts;
};

@Controller('mail')
@UsePipes(new ValidationPipe())
export class MailController {
  constructor(
    private readonly mailService: MailService,
    private readonly mailTemplateConvertionService: MailTemplateConvertionService,
    private readonly mailTemplateService: EmailTemplateService,
    @InjectQueue('mail-queue') private mailQueue: Queue,
    @InjectQueue('promo-mail-queue') private promoMailQueue: Queue,
    @InjectQueue('promo-mail-queue-optimized') private promoMailQueueOptimized: Queue,
  ) { }

  @UseGuards(BasicAuthGuard)
  @Get('web/send-3456dfge435cxre4306drrl')
  public async generateInvoice() {
    try {
      try {
        const html = 'Hello mail';

        await this.mailService.sendMail(
          '<EMAIL>',
          // result?.billingAddress?.email,
          'Your order has been placed',
          `Dear DEV, <br> <br> Thank you for shopping with PANTONECLO! We are delighted to have you as a customer and hope you love your new clothing.  <br> <br>` + html,
          null
        );
      } catch (error) {
        return new ServiceResponse([], error.message, null);
      }
    } catch (error) {
      return new ServiceResponse([], error.message, null);
    }
  }

  @UseGuards(BasicAuthGuard)
  @Get('web/genPdf')
  public async generateInvoicePdf() {
    const pdfFilePath = ''; // await this.invoiceService.generateInvoiceFilePath();

    return null;
  }

  @UseGuards(BasicAuthGuard)
  @Get('web/test-send')
  async sendEmail(@Query('email') email: string, @Query('name') name: string, @Query('imageUrl') imageUrl: string) {
    // console.log(email, name, imageUrl);

    await this.sendEmailWithPdfAttachment(email, name, imageUrl);
    return { message: 'Email sent successfully' };
  }

  @UseGuards(BasicAuthGuard)
  @Post('test/preview')
  async renderPreview(@Res() res: any, @Body() body: { mjml: string; htmlData: string; data: any }) {
    const convertedHtml = await this.mailTemplateConvertionService.renderTemplate(body.mjml, body.htmlData, body.data);

    await this.mailService.sendMail(
      body?.data?.receiverInfo?.email,
      body?.data?.receiverInfo?.subject,
      convertedHtml,
      null
    );

    res.send(convertedHtml)
  }

  async sendEmailWithPdfAttachment(email: string, name: string, imageUrl: string) {
    const { htmlContent, attachment } = await this.generateEmailWithPdfAttachment(name, imageUrl);

    await this.mailService.sendMail(
      email,
      'Order Confirmation',
      htmlContent,
      null,
      [
        {
          filename: 'order-confirmation.pdf',
          content: attachment,
        },
      ],
    );
  }

  async generateEmailWithPdfAttachment(name: string, imageUrl: string): Promise<any> {
    // Convert image URL to Base64
    const base64Image = await this.getBase64ImageFromUrl(imageUrl);

    const possibleTemplatePaths = [
      path.join(process.cwd(), 'src', 'common', 'templates', 'test-invoice.html'),
      path.join(process.cwd(), 'dist', 'common', 'templates', 'test-invoice.html'),
    ];

    const templatePath = possibleTemplatePaths.find((path) =>
      fs.existsSync(path),
    );

    if (!templatePath) {
      throw new Error('Invoice template not found');
    }

    // Load and compile Handlebars template
    // const templatePath = path.join(__dirname, '..', 'templates', 'email-template.hbs');
    const templateSource = fs.readFileSync(templatePath, 'utf8');
    const template = Handlebars.compile(templateSource);

    // Prepare dynamic data
    const htmlContent = template({ name, imageBase64: base64Image });

    // Generate PDF from HTML content
    return {
      attachment: await new Promise((resolve, reject) => {
        pdf.create(htmlContent, { format: 'A4' }).toBuffer((err, buffer) => {
          if (err) {
            reject(err);
          } else {
            resolve(buffer);
          }
        });
      }),
      htmlContent
    }
  }

  async getBase64ImageFromUrl(imageUrl: string): Promise<string> {
    try {
      const response = await axios.get(imageUrl, {
        responseType: 'arraybuffer', // Get binary data
      });

      const base64 = Buffer.from(response.data).toString('base64');
      const mimeType = response.headers['content-type']; // Get MIME type (e.g., image/png)
      return `data:${mimeType};base64,${base64}`;
    } catch (error) {
      throw new Error(`Failed to convert image to base64: ${error.message}`);
    }
  }


  @UseGuards(BasicAuthGuard)
  @Post('test/preview/queue')
  async sendEmailWithQueue(
    @Res() res: any,
    @Body() body: SendEmailWithQueueDto, // { emailTemplateId: number; mjml: string; htmlData: string; data: any }
  ) {
    // body.emailTemplateId = 6
    body.data['cartInfo'] = {
      countryId: 1,
      "cartUUID": "1748148676",
      "subtotal": "333.00",
      "tax": "33.85",
      "total": "333.00",
      "cart": [
        {
          "name": "Maneca scurta tricou bărbați - pachet de 4",
          "discountPrice": "222.00",
          "quantity": 1,
          "currency": "RON",
          "size": { "name": "XS" },
          "featuredImage": {
            "imageGalleryUrls": {
              "medium": "https://cdn.pantoneclo.com/product/dsc69070_1731322589366_medium_452_505.jpg"
            }
          }
        },
        {
          "name": "Maneca scurta tricou bărbați - pachet de 4",
          "discountPrice": "111.00",
          "quantity": 2,
          "currency": "RON",
          "size": { "name": "XS" },
          "featuredImage": {
            "imageGalleryUrls": {
              "medium": "https://cdn.pantoneclo.com/product/dsc69070_1731322589366_medium_452_505.jpg"
            }
          }
        }
      ]
    };

    const template = body.htmlData ? null : await this.mailTemplateService.getTemplateByCountryId(body?.emailTemplateId || 6, 1);

    if (body.emailTemplateId) {
      body.mjml = template?.data?.mjml;
      body.htmlData = template?.data?.htmlContent;
    }

    const locale = getLanguageCodeByCountryId(body?.data?.cartInfo?.countryId) || 'en';
    const translations = template?.data?.translationKeys?.length ? mapTranslations(template?.data?.translationKeys) : {};
    const subjectLocale = translations?.[locale]?.EMAIL_SUBJECT || body?.data?.receiverInfo?.subject

    const convertedHtml = await this.mailTemplateConvertionService.renderTemplate(
      body?.mjml,
      body?.htmlData,
      {
        ...body?.data,
        firstName: 'Customer',
        // cartInfo,
        locale,
        translations
      }
    );

    try {
      await this.mailQueue.add('send-email', {
        to: body?.data?.receiverInfo?.email,
        subject: subjectLocale,
        body: convertedHtml,
        senderEmailAddress: process.env.SES_FROM_MAIL,
        attachments: null,
        // bcc: "<EMAIL>, <EMAIL>",
      });

    } catch (error) {
      console.log('test/preview/queue error:::', error);
    }

    res.send(convertedHtml)
  }



  @UseGuards(BasicAuthGuard)
  @Post('test/preview/queue/abandonedCart')
  async handleAbandonedCart() {
    // 1) Fetch template
    const templateInfo = await this.mailTemplateService.getTemplateByType('ABANDONED_CART');

    // 2) Get cart data from DB
    let axiosConfig = {
      headers: {
        'Content-Type': 'application/json;charset=UTF-8',
        "Access-Control-Allow-Origin": "*",
      }
    };

    let data = JSON.stringify({
      "timeFrame": "2h"
    });
    // const cartData = await this.cartService.getCartData(cartUUID);
    const cartData = await axios.post(
      `${process.env.ECOMMERCE_API_HOST}/cart/web/getAbandonedCarts`,
      data,
      axiosConfig
    );

    console.log('templateInfo:::', templateInfo?.data);
    console.log('cartData:::', cartData?.data?.data);

    for (const cartInfo of cartData?.data?.data) {
      const data = {
        "receiverInfo": {
          // "email": "<EMAIL>, <EMAIL>, <EMAIL>",
          "email": "<EMAIL>, <EMAIL>",
          "subject": "Builder Testing Abandoned Cart"
        },
        "firstName": "John",
        "orderId": "123456",
        "locale": "de",
        "translations": {
          "en": {
            "greeting": "Hello"
          },
          "de": {
            "greeting": "Hallo"
          }
        },
        "cartInfo": {
          "cartUUID": "1748148676",
          "subtotal": "333.00",
          "tax": "33.85",
          "total": "333.00",
          "cart": [
            {
              "name": "Maneca scurta tricou bărbați - pachet de 4",
              "discountPrice": "222.00",
              "quantity": 1,
              "currency": "RON",
              "size": { "name": "XS" },
              "featuredImage": {
                "imageGalleryUrls": {
                  "medium": "https://cdn.pantoneclo.com/product/dsc69070_1731322589366_medium_452_505.jpg"
                }
              }
            },
            {
              "name": "Maneca scurta tricou bărbați - pachet de 4",
              "discountPrice": "111.00",
              "quantity": 2,
              "currency": "RON",
              "size": { "name": "XS" },
              "featuredImage": {
                "imageGalleryUrls": {
                  "medium": "https://cdn.pantoneclo.com/product/dsc69070_1731322589366_medium_452_505.jpg"
                }
              }
            }
          ]
        }
      }

      const firstName = cartInfo?.billingAddress?.firstName ? `${cartInfo?.billingAddress?.firstName} ${cartInfo?.billingAddress?.lastName}` : 'Customer'
      // 3) Render template
      const renderedHtml = await this.mailTemplateConvertionService.renderTemplate(
        templateInfo?.data?.mjml,
        templateInfo?.data?.htmlContent,
        {
          ...data,
          firstName,
          cartInfo,
        }
      );

      // 4) Send now if needed
      await this.mailQueue.add('send-email', {
        to: data?.receiverInfo?.email, //  data?.receiverInfo?.email,
        subject: templateInfo?.data?.subject,
        body: renderedHtml,
        senderEmailAddress: process.env.SES_FROM_MAIL
      });

    }
  }



  @UseGuards(BasicAuthGuard)
  @Post('test/preview/queue/send-promotional-email-to-users')
  async sendPromotionalEmailToUsers(
    @Res() res: any,
    @Body() body: EmailTemplateDto
    // userList: User[], chunkSize = 50
  ) {
    const chunkSize = 1;

    const templateInfo = body?.emailTemplateId ?
      await this.mailTemplateService.getTemplateWithTranslations(body?.emailTemplateId)
      : null

    if (!templateInfo?.data?.template?.id) {
      throw new NotFoundException('Template not found');
    }

    const { template, translations } = templateInfo?.data
    const convertedHtml = await this.mailTemplateConvertionService.renderTemplate(
      body?.mjml,
      template?.htmlContent || body?.htmlData,
      {
        ...body.data,
        translations: {
          ...body.data?.translations,
          ...translations
        }
      }
    );

    // console.log('convertedHtml:::', convertedHtml);
    console.log('body.data?.receiverInfo?.userEmail?.length:::', body.data?.receiverInfo?.userEmails?.length);


    // Chunk users to prevent Redis overload
    const chunkSizeNumber = 200;
    const userChunks = this.chunkArray(body.data?.receiverInfo?.userEmails, chunkSizeNumber);

    const jobs = userChunks.map((chunk, index) => ({
      name: 'promo-send-email',
      data: {
        users: chunk,
        to: body?.data?.receiverInfo?.email,
        subject: template?.subject || body?.data?.receiverInfo?.subject,
        body: convertedHtml,
        senderEmailAddress: body?.data?.senderInfo?.senderEmail || process.env.SES_FROM_MAIL,
        attachments: null,
        cc: body?.data?.receiverInfo?.cc,
        bcc: body?.data?.receiverInfo?.bcc,
        emailTemplateId: body?.emailTemplateId
      },
      opts: {
        jobId: `promo-email-${Date.now()}-${index}`,
        priority: index === 0 ? 1 : 3, // Higher priority for first chunk
      },
    }));

    await this.promoMailQueue.addBulk(jobs);

    // // 5) Save history
    // await this.cartMailHistoryRepo.save({
    //   cartUUID: cartInfo.cartUUID,
    //   stage,
    //   sent: true,
    // });


    res.send(convertedHtml)
  }

  private chunkArray<T>(array: T[], size: number): T[][] {
    return Array.from(
      { length: Math.ceil(array.length / size) },
      (_, i) => array.slice(i * size, i * size + size)
    );
  }



  // private buildJobOpts(index: number, schedule?: SendPromotionalDto['schedule']) {
  //   const opts: any = {
  //     jobId: `promo-email-${Date.now()}-${index}-${crypto.randomUUID()}`,
  //     priority: index === 0 ? 1 : 3,
  //     attempts: 3,
  //     backoff: { type: 'exponential', delay: 2000 },
  //     removeOnComplete: { age: 3600, count: 1000 },
  //     removeOnFail: { age: 24 * 3600, count: 1000 },
  //   };

  //   // Scheduled (recurring)
  //   if (schedule?.cron) {
  //     opts.repeat = { cron: schedule.cron, tz: schedule.tz || 'UTC' };
  //     return opts;
  //   }

  //   // One-time (delayed absolute/relative)
  //   let delay = 0;
  //   if (typeof schedule?.delayMs === 'number' && schedule.delayMs > 0) {
  //     delay = schedule.delayMs;
  //   } else if (schedule?.sendAt) {
  //     const when = Date.parse(schedule.sendAt);
  //     const now = Date.now();
  //     if (!Number.isNaN(when) && when > now) delay = when - now;
  //   }
  //   if (delay > 0) opts.delay = delay;

  //   return opts;
  // }


  // @UseGuards(BasicAuthGuard)
  // @Post('optimized/preview/queue/send-promotional-email-to-users')
  // async sendPromotionalEmailToUsersOptimized(@Res() res: any, @Body() body: SendPromotionalDto) {
  //   // 1) Resolve & render template
  //   const templateInfo = body?.emailTemplateId
  //     ? await this.mailTemplateService.getTemplateWithTranslations(body.emailTemplateId)
  //     : null;

  //   if (!templateInfo?.data?.template?.id) {
  //     throw new NotFoundException('Template not found');
  //   }

  //   const { template, translations } = templateInfo.data;

  //   const convertedHtml = await this.mailTemplateConvertionService.renderTemplate(
  //     body?.mjml,
  //     template?.htmlContent || body?.htmlData,
  //     {
  //       ...body.data,
  //       translations: { ...body.data?.translations, ...translations },
  //     },
  //   );

  //   // 2) Recipients
  //   const recipients = cleanEmails(body?.data?.receiverInfo?.userEmails || []);
  //   if (!recipients.length) {
  //     return res.status(400).json({ message: 'No valid recipient emails provided' });
  //   }

  //   // 3) Chunking to protect Redis / keep jobs lean
  //   const CHUNK_SIZE = 200;
  //   const userChunks = chunkArray(recipients, CHUNK_SIZE);

  //   // 4) Meta
  //   const senderEmail = body?.data?.senderInfo?.senderEmail || process.env.SES_FROM_MAIL;
  //   const campaignId = body?.campaignMeta?.campaignId ?? 0;
  //   const countryId = body?.campaignMeta?.countryId ?? 0;
  //   const locale = body?.campaignMeta?.locale ?? {};

  //   // 5) Bulk jobs (instant, delayed, or recurring)
  //   const jobs = userChunks.map((chunk, index) => ({
  //     name: 'promo-send-email',
  //     data: {
  //       users: chunk,
  //       to: body?.data?.receiverInfo?.email, // optional label
  //       subject: template?.subject || body?.data?.receiverInfo?.subject,
  //       body: convertedHtml,
  //       senderEmailAddress: senderEmail,
  //       attachments: null,
  //       cc: body?.data?.receiverInfo?.cc,
  //       bcc: body?.data?.receiverInfo?.bcc,
  //       emailTemplateId: body?.emailTemplateId,
  //       // pass-through to processor for history saving
  //       campaignId,
  //       countryId,
  //       locale,
  //     },
  //     opts: this.buildJobOpts(index, body?.schedule),
  //   }));

  //   await this.promoMailQueueOptimized.addBulk(jobs);

  //   // 6) API response
  //   return res.json({
  //     status: 'queued',
  //     chunks: userChunks.length,
  //     recipients: recipients.length,
  //     perChunk: CHUNK_SIZE,
  //     schedule: {
  //       mode: body?.schedule?.cron
  //         ? 'recurring'
  //         : (body?.schedule?.sendAt || body?.schedule?.delayMs) ? 'delayed' : 'immediate',
  //       sendAt: body?.schedule?.sendAt || null,
  //       delayMs: body?.schedule?.delayMs || null,
  //       cron: body?.schedule?.cron || null,
  //       tz: body?.schedule?.tz || null,
  //     },
  //     meta: { campaignId, countryId },
  //   });
  // }




  // ---------- Helpers ----------
  // private buildJobOpts(
  //   index: number,
  //   schedule?: ScheduleInput,
  //   baseId = 'promo-email'
  // ): JobsOptions {
  //   const jobId = `${baseId}-${Date.now()}-${index}-${crypto.getRandomValues(new Uint32Array(1))[0]}`;
  //   const opts: JobsOptions = {
  //     jobId,
  //     priority: index === 0 ? 1 : 3,
  //     attempts: 3,
  //     backoff: { type: 'exponential', delay: 2000 },
  //     removeOnComplete: { age: 3600, count: 1000 },
  //     removeOnFail: { age: 24 * 3600, count: 1000 },
  //   };

  //   // Recurring (cron)
  //   if ((schedule as any)?.cron) {
  //     const { cron, tz } = schedule as { cron: string; tz?: string };
  //     opts.repeat = { cron, tz: tz || 'Asia/Dhaka' } as RepeatOpts;
  //     return opts;
  //   }

  //   // One-time (absolute or relative)
  //   let delay = 0;
  //   const s = (schedule || {}) as { sendAt?: string; delayMs?: number };
  //   if (typeof s.delayMs === 'number' && s.delayMs > 0) {
  //     delay = s.delayMs;
  //   } else if (s.sendAt) {
  //     const when = Date.parse(s.sendAt);
  //     const now = Date.now();
  //     if (!Number.isNaN(when) && when > now) delay = when - now;
  //   }
  //   if (delay > 0) opts.delay = delay;

  //   return opts;
  // }

  private buildJobOpts(
    index: number,
    schedule?: ScheduleInput,
    baseId = 'promo-email'
  ): JobsOptions {
    const s = schedule ?? {};
    const opts: JobsOptions = {
      // default jobId for non-repeat (can be anything unique)
      jobId: `${baseId}:${Date.now()}:${index}:${crypto.getRandomValues(new Uint32Array(1))[0]}:once`,
      priority: index === 0 ? 1 : 3,
      attempts: 3,
      backoff: { type: 'exponential', delay: 2000 } as any, // Bull & BullMQ both accept this shape
      removeOnComplete: { age: 3600, count: 1000 } as any,  // both libs support object form in current versions
      removeOnFail: { age: 86400, count: 1000 } as any,
    };

    // 1) Recurring (cron)
    if (s.cron) {
      const repeat: RepeatOpts = { cron: s.cron, tz: s.tz || 'Asia/Dhaka' } as RepeatOpts;
      opts.repeat = repeat;

      // Use a STABLE jobId for repeatables to avoid duplicates
      // You can add knobs like countryId/locale/campaignId if that logically scopes the repeat
      // opts.jobId = `${baseId}:repeat:${repeat.cron}:${repeat.tz ?? 'UTC'}`;
      opts.jobId = opts.jobId.replace('once', 'repeat'); // replace once with repeat
      return opts;
    }

    // 2) One-time (absolute or relative)
    let delay = 0;
    if (typeof s.delayMs === 'number' && s.delayMs > 0) {
      delay = s.delayMs;
    } else if (s.sendAt) {
      const when = Date.parse(s.sendAt);
      if (!Number.isNaN(when)) {
        delay = Math.max(0, when - Date.now());
      }
    }
    if (delay > 0) opts.delay = delay;

    return opts;
  }


  // ---------- Existing: immediate / delayed / recurring via body.schedule ----------
  /**
   * Send promotional email to users (optimized)
   * @param res Response object
   * @param body Request body
   * @returns Queued job information
   */
  @UseGuards(BasicAuthGuard)
  @Post('optimized/preview/queue/send-promotional-email-to-users')
  async sendPromotionalEmailToUsersOptimized(@Res() res: any, @Body() body: SendPromotionalDto) {
    // 1) Resolve & render template
    const templateInfo = body?.emailTemplateId
      ? await this.mailTemplateService.getTemplateWithTranslations(body.emailTemplateId)
      : null;

    if (!templateInfo?.data?.template?.id) {
      throw new NotFoundException('Template not found');
    }

    const { template, translations } = templateInfo.data;
    const convertedHtml = await this.mailTemplateConvertionService.renderTemplate(
      body?.mjml,
      template?.htmlContent || body?.htmlData,
      {
        ...body.data,
        translations: { ...body.data?.translations, ...translations },
      },
    );

    // 2) Recipients
    const recipients = cleanEmails(body?.data?.receiverInfo?.userEmails || []);
    if (!recipients.length) return res.status(400).json({ message: 'No valid recipient emails provided' });

    // 3) Chunk
    const CHUNK_SIZE = 200;
    const userChunks = chunkArray(recipients, CHUNK_SIZE);

    // 4) Meta
    const senderEmail = body?.data?.senderInfo?.senderEmail || process.env.SES_FROM_MAIL;
    const campaignId = body?.campaignMeta?.campaignId ?? 0;
    const countryId = body?.campaignMeta?.countryId ?? 0;
    const locale = body?.campaignMeta?.locale ?? {};

    // 5) Enqueue (instant / delayed / recurring based on body.schedule)
    const opts = this.buildJobOpts(0, body?.schedule);
    console.log('Job options::::', opts);

    const jobs = userChunks.map((chunk, index) => ({
      name: 'promo-send-email-optimized',
      data: {
        users: chunk,
        to: body?.data?.receiverInfo?.email,
        subject: template?.subject || body?.data?.receiverInfo?.subject,
        body: convertedHtml,
        senderEmailAddress: senderEmail,
        attachments: null,
        cc: body?.data?.receiverInfo?.cc,
        bcc: body?.data?.receiverInfo?.bcc,
        emailTemplateId: body?.emailTemplateId,
        // pass-through for history saving
        campaignId,
        countryId,
        locale,
      },
      opts
    }));

    await this.promoMailQueueOptimized.addBulk(jobs);

    return res.json({
      status: 'queued',
      chunks: userChunks.length,
      recipients: recipients.length,
      perChunk: CHUNK_SIZE,
      schedule: {
        mode: (body?.schedule as any)?.cron
          ? 'recurring'
          : ((body?.schedule as any)?.sendAt || (body?.schedule as any)?.delayMs) ? 'delayed' : 'immediate',
        sendAt: (body?.schedule as any)?.sendAt || null,
        delayMs: (body?.schedule as any)?.delayMs || null,
        cron: (body?.schedule as any)?.cron || null,
        tz: (body?.schedule as any)?.tz || null,
      },
      meta: { campaignId, countryId },
    });
  }

  // ---------- NEW: Dedicated schedule endpoints ----------

  // Create a scheduled (one-time or recurring) promotional send
  @UseGuards(BasicAuthGuard)
  @Post('schedule/promotional')
  async schedulePromotional(@Res() res: any, @Body() body: SendPromotionalDto) {
    // Reuse the same rendering + chunking path
    const templateInfo = body?.emailTemplateId
      ? await this.mailTemplateService.getTemplateWithTranslations(body.emailTemplateId)
      : null;

    if (!templateInfo?.data?.template?.id) {
      throw new NotFoundException('Template not found');
    }

    const { template, translations } = templateInfo.data;
    const convertedHtml = await this.mailTemplateConvertionService.renderTemplate(
      body?.mjml,
      template?.htmlContent || body?.htmlData,
      {
        ...body.data,
        translations: { ...body.data?.translations, ...translations },
      },
    );

    const recipients = cleanEmails(body?.data?.receiverInfo?.userEmails || []);
    if (!recipients.length) return res.status(400).json({ message: 'No valid recipient emails provided' });

    const CHUNK_SIZE = 200;
    const userChunks = chunkArray(recipients, CHUNK_SIZE);

    const senderEmail = body?.data?.senderInfo?.senderEmail || process.env.SES_FROM_MAIL;
    const campaignId = body?.campaignMeta?.campaignId ?? 0;
    const countryId = body?.campaignMeta?.countryId ?? 0;
    const locale = body?.campaignMeta?.locale ?? {};

    // If cron provided → recurring; else → one-time (sendAt or delayMs must be present, or it will send immediately)
    const opts = this.buildJobOpts(0, body?.schedule);
    console.log('Job options::::', opts);

    // return
    
    const jobs = userChunks.map((chunk, index) => ({
      name: 'promo-send-email-optimized',
      data: {
        users: chunk,
        to: body?.data?.receiverInfo?.email,
        subject: template?.subject || body?.data?.receiverInfo?.subject,
        body: convertedHtml,
        senderEmailAddress: senderEmail,
        attachments: null,
        cc: body?.data?.receiverInfo?.cc,
        bcc: body?.data?.receiverInfo?.bcc,
        emailTemplateId: body?.emailTemplateId,
        campaignId,
        countryId,
        locale,
      },
      opts: this.buildJobOpts(index, body?.schedule, 'promo-send-email-optimized-schedule'),
    }));

    await this.promoMailQueueOptimized.addBulk(jobs);

    return res.json({
      status: 'scheduled',
      mode: (body?.schedule as any)?.cron ? 'recurring' : 'one-time',
      chunks: userChunks.length,
      recipients: recipients.length,
      schedule: body?.schedule || {},
      meta: { campaignId, countryId },
      note:
        (body?.schedule as any)?.cron
          ? 'Multiple repeatable jobs (one per chunk) were created. Use the list endpoint to see their keys.'
          : 'One-time scheduled jobs created.',
    });
  }

  // List all repeatable (recurring) jobs for this queue
  @UseGuards(BasicAuthGuard)
  @Get('schedule/repeatables')
  async listRepeatables(@Res() res: any) {
    const items = await this.promoMailQueueOptimized.getRepeatableJobs();
    console.log('schedule/repeatables items:::', items);

    // You may filter by name === 'promo-send-email' if the queue has other repeatables
    const filtered = items.filter(i => i.name === 'promo-send-email-optimized');
    return res.json({ count: filtered.length, items: filtered });
  }

  // Delete a scheduled recurring job
  // Accept either a 'key' returned by listRepeatables()
  // OR { cron, tz, jobId } triplet
  @UseGuards(BasicAuthGuard)
  @Delete('schedule/promotional')
  async deleteSchedule(
    @Res() res: any,
    @Body() body: { key?: string; cron?: string; tz?: string; jobId?: string }
  ) {
    if (body?.key) {
      await this.promoMailQueueOptimized.removeRepeatableByKey(body.key);
      return res.json({ status: 'removed', method: 'key', key: body.key });
    }
    if (body?.cron) {
      await this.promoMailQueueOptimized.removeRepeatable('promo-send-email-optimized', {
        cron: body.cron,
        tz: body.tz || 'Asia/Dhaka',
        jobId: body.jobId, // must match the jobId used at creation time
      } as any);
      return res.json({ status: 'removed', method: 'pattern', cron: body.cron, tz: body.tz || 'Asia/Dhaka', jobId: body.jobId || null });
    }

    return res.status(400).json({
      message: 'Provide either { key } or { cron, tz?, jobId? } to remove a schedule.',
    });
  }



  // 1) Immediate bulk send (no schedule):
  // POST /email/optimize/preview/queue/send-promotional-email-to-users
  // {
  //   "emailTemplateId": 123,
  //   "data": {
  //     "senderInfo": {"senderEmail":"<EMAIL>"},
  //     "receiverInfo":{
  //       "subject":"Weekend Drop",
  //       "userEmails":["<EMAIL>","<EMAIL>"]
  //     }
  //   },
  //   "campaignMeta":{"campaignId": 9001, "countryId": 1}
  // }

  // 2) One-time scheduled send (absolute):
  // POST /email/schedule/promotional
  // {
  //   "emailTemplateId": 123,
  //   "data": {
  //     "senderInfo": {"senderEmail":"<EMAIL>"},
  //     "receiverInfo":{"subject":"Tonight 8 PM", "userEmails":["<EMAIL>","<EMAIL>"]}
  //   },
  //   "schedule": { "sendAt": "2025-08-13T20:00:00+06:00" },
  //   "campaignMeta": {"campaignId": 9002, "countryId": 1}
  // }


  // 3) Recurring schedule (every Monday 10:00 Asia/Dhaka):
  // POST /email/schedule/promotional
  // {
  //   "emailTemplateId": 123,
  //   "data": {
  //     "receiverInfo":{"subject":"Weekly Picks", "userEmails":["<EMAIL>","<EMAIL>"]}
  //   },
  //   "schedule": { "cron": "0 10 * * MON", "tz":"Asia/Dhaka" },
  //   "campaignMeta": {"campaignId": 9100, "countryId": 1}
  // }


  // 4) Get all repeatable schedules
  // GET /email/schedule/repeatables


  // 5) Delete a schedule by key:
  // DELETE /email/schedule/promotional
  // { "key": "repeat:promo-send-email:xxx..." }


  // 6) Or delete by pattern (must match jobId used on creation):
  // DELETE /email/schedule/promotional
  // { "cron": "0 10 * * MON", "tz": "Asia/Dhaka", "jobId": "promo-email-schedule-..." }


}

