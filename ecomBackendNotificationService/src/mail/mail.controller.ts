import { Body, Controller, Get, NotFoundException, Query, Res, UseGuards } from '@nestjs/common';
import { Post, UsePipes, ValidationPipe } from '@nestjs/common';
import { MailTemplateConvertionService } from './mail-template-cnvertion.service';
import { MailService } from 'libs/common/services/mail.service';
import axios from 'axios';
import * as fs from 'fs';
import * as path from 'path';
import * as Handlebars from 'handlebars';
import * as pdf from 'html-pdf';
import { ServiceResponse } from 'libs/common/utils/service-response';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { EmailTemplateService } from 'src/mail-template/email-template.service';
import { EmailTemplateDto } from './dto/create-send-mail.dto';
import { EmailTemplate } from 'src/mail-template/entity/email-template.entity';
import { BasicAuthGuard } from 'src/auth/guards/basic-auth.guard';
import { getLanguageCodeByCountryId } from 'libs/common/utils/country-helper';
import { mapTranslations } from 'libs/common/utils/translation-helper';
import { SendEmailWithQueueDto } from './dto/send-mail-template.dto';
// import { Response } from 'express';

@Controller('mail')
@UsePipes(new ValidationPipe())
export class MailController {
  constructor(
    private readonly mailService: MailService,
    private readonly mailTemplateConvertionService: MailTemplateConvertionService,
    private readonly mailTemplateService: EmailTemplateService,
    @InjectQueue('mail-queue') private mailQueue: Queue,
    @InjectQueue('promo-mail-queue') private promoMailQueue: Queue,
  ) { }

  @UseGuards(BasicAuthGuard)
  @Get('web/send-3456dfge435cxre4306drrl')
  public async generateInvoice() {
    try {
      try {
        const html = 'Hello mail';

        await this.mailService.sendMail(
          '<EMAIL>',
          // result?.billingAddress?.email,
          'Your order has been placed',
          `Dear DEV, <br> <br> Thank you for shopping with PANTONECLO! We are delighted to have you as a customer and hope you love your new clothing.  <br> <br>` + html,
          null
        );
      } catch (error) {
        return new ServiceResponse([], error.message, null);
      }
    } catch (error) {
      return new ServiceResponse([], error.message, null);
    }
  }

  @UseGuards(BasicAuthGuard)
  @Get('web/genPdf')
  public async generateInvoicePdf() {
    const pdfFilePath = ''; // await this.invoiceService.generateInvoiceFilePath();

    return null;
  }

  @UseGuards(BasicAuthGuard)
  @Get('web/test-send')
  async sendEmail(@Query('email') email: string, @Query('name') name: string, @Query('imageUrl') imageUrl: string) {
    // console.log(email, name, imageUrl);

    await this.sendEmailWithPdfAttachment(email, name, imageUrl);
    return { message: 'Email sent successfully' };
  }

  @UseGuards(BasicAuthGuard)
  @Post('test/preview')
  async renderPreview(@Res() res: any, @Body() body: { mjml: string; htmlData: string; data: any }) {
    const convertedHtml = await this.mailTemplateConvertionService.renderTemplate(body.mjml, body.htmlData, body.data);

    await this.mailService.sendMail(
      body?.data?.receiverInfo?.email,
      body?.data?.receiverInfo?.subject,
      convertedHtml,
      null
    );

    res.send(convertedHtml)
  }

  async sendEmailWithPdfAttachment(email: string, name: string, imageUrl: string) {
    const { htmlContent, attachment } = await this.generateEmailWithPdfAttachment(name, imageUrl);

    await this.mailService.sendMail(
      email,
      'Order Confirmation',
      htmlContent,
      null,
      [
        {
          filename: 'order-confirmation.pdf',
          content: attachment,
        },
      ],
    );
  }

  async generateEmailWithPdfAttachment(name: string, imageUrl: string): Promise<any> {
    // Convert image URL to Base64
    const base64Image = await this.getBase64ImageFromUrl(imageUrl);

    const possibleTemplatePaths = [
      path.join(process.cwd(), 'src', 'common', 'templates', 'test-invoice.html'),
      path.join(process.cwd(), 'dist', 'common', 'templates', 'test-invoice.html'),
    ];

    const templatePath = possibleTemplatePaths.find((path) =>
      fs.existsSync(path),
    );

    if (!templatePath) {
      throw new Error('Invoice template not found');
    }

    // Load and compile Handlebars template
    // const templatePath = path.join(__dirname, '..', 'templates', 'email-template.hbs');
    const templateSource = fs.readFileSync(templatePath, 'utf8');
    const template = Handlebars.compile(templateSource);

    // Prepare dynamic data
    const htmlContent = template({ name, imageBase64: base64Image });

    // Generate PDF from HTML content
    return {
      attachment: await new Promise((resolve, reject) => {
        pdf.create(htmlContent, { format: 'A4' }).toBuffer((err, buffer) => {
          if (err) {
            reject(err);
          } else {
            resolve(buffer);
          }
        });
      }),
      htmlContent
    }
  }

  async getBase64ImageFromUrl(imageUrl: string): Promise<string> {
    try {
      const response = await axios.get(imageUrl, {
        responseType: 'arraybuffer', // Get binary data
      });

      const base64 = Buffer.from(response.data).toString('base64');
      const mimeType = response.headers['content-type']; // Get MIME type (e.g., image/png)
      return `data:${mimeType};base64,${base64}`;
    } catch (error) {
      throw new Error(`Failed to convert image to base64: ${error.message}`);
    }
  }


  @UseGuards(BasicAuthGuard)
  @Post('test/preview/queue')
  async sendEmailWithQueue(
    @Res() res: any,
    @Body() body: SendEmailWithQueueDto, // { emailTemplateId: number; mjml: string; htmlData: string; data: any }
  ) {
    // body.emailTemplateId = 6
    body.data['cartInfo'] = {
      countryId: 1,
      "cartUUID": "1748148676",
      "subtotal": "333.00",
      "tax": "33.85",
      "total": "333.00",
      "cart": [
        {
          "name": "Maneca scurta tricou bărbați - pachet de 4",
          "discountPrice": "222.00",
          "quantity": 1,
          "currency": "RON",
          "size": { "name": "XS" },
          "featuredImage": {
            "imageGalleryUrls": {
              "medium": "https://cdn.pantoneclo.com/product/dsc69070_1731322589366_medium_452_505.jpg"
            }
          }
        },
        {
          "name": "Maneca scurta tricou bărbați - pachet de 4",
          "discountPrice": "111.00",
          "quantity": 2,
          "currency": "RON",
          "size": { "name": "XS" },
          "featuredImage": {
            "imageGalleryUrls": {
              "medium": "https://cdn.pantoneclo.com/product/dsc69070_1731322589366_medium_452_505.jpg"
            }
          }
        }
      ]
    };

    const template = body.htmlData ? null : await this.mailTemplateService.getTemplateByCountryId(body?.emailTemplateId || 6, 1);

    if (body.emailTemplateId) {
      body.mjml = template?.data?.mjml;
      body.htmlData = template?.data?.htmlContent;
    }

    const locale = getLanguageCodeByCountryId(body?.data?.cartInfo?.countryId) || 'en';
    const translations = template?.data?.translationKeys?.length ? mapTranslations(template?.data?.translationKeys) : {};
    const subjectLocale = translations?.[locale]?.EMAIL_SUBJECT || body?.data?.receiverInfo?.subject

    const convertedHtml = await this.mailTemplateConvertionService.renderTemplate(
      body?.mjml,
      body?.htmlData,
      {
        ...body?.data,
        firstName: 'Customer',
        // cartInfo,
        locale,
        translations
      }
    );

    try {
      await this.mailQueue.add('send-email', {
        to: body?.data?.receiverInfo?.email,
        subject: subjectLocale,
        body: convertedHtml,
        senderEmailAddress: process.env.SES_FROM_MAIL,
        attachments: null,
        // bcc: "<EMAIL>, <EMAIL>",
      });

    } catch (error) {
      console.log('test/preview/queue error:::', error);
    }

    res.send(convertedHtml)
  }



  @UseGuards(BasicAuthGuard)
  @Post('test/preview/queue/abandonedCart')
  async handleAbandonedCart() {
    // 1) Fetch template
    const templateInfo = await this.mailTemplateService.getTemplateByType('ABANDONED_CART');

    // 2) Get cart data from DB
    let axiosConfig = {
      headers: {
        'Content-Type': 'application/json;charset=UTF-8',
        "Access-Control-Allow-Origin": "*",
      }
    };

    let data = JSON.stringify({
      "timeFrame": "2h"
    });
    // const cartData = await this.cartService.getCartData(cartUUID);
    const cartData = await axios.post(
      `${process.env.ECOMMERCE_API_HOST}/cart/web/getAbandonedCarts`,
      data,
      axiosConfig
    );

    console.log('templateInfo:::', templateInfo?.data);
    console.log('cartData:::', cartData?.data?.data);

    for (const cartInfo of cartData?.data?.data) {
      const data = {
        "receiverInfo": {
          // "email": "<EMAIL>, <EMAIL>, <EMAIL>",
          "email": "<EMAIL>, <EMAIL>",
          "subject": "Builder Testing Abandoned Cart"
        },
        "firstName": "John",
        "orderId": "123456",
        "locale": "de",
        "translations": {
          "en": {
            "greeting": "Hello"
          },
          "de": {
            "greeting": "Hallo"
          }
        },
        "cartInfo": {
          "cartUUID": "1748148676",
          "subtotal": "333.00",
          "tax": "33.85",
          "total": "333.00",
          "cart": [
            {
              "name": "Maneca scurta tricou bărbați - pachet de 4",
              "discountPrice": "222.00",
              "quantity": 1,
              "currency": "RON",
              "size": { "name": "XS" },
              "featuredImage": {
                "imageGalleryUrls": {
                  "medium": "https://cdn.pantoneclo.com/product/dsc69070_1731322589366_medium_452_505.jpg"
                }
              }
            },
            {
              "name": "Maneca scurta tricou bărbați - pachet de 4",
              "discountPrice": "111.00",
              "quantity": 2,
              "currency": "RON",
              "size": { "name": "XS" },
              "featuredImage": {
                "imageGalleryUrls": {
                  "medium": "https://cdn.pantoneclo.com/product/dsc69070_1731322589366_medium_452_505.jpg"
                }
              }
            }
          ]
        }
      }

      const firstName = cartInfo?.billingAddress?.firstName ? `${cartInfo?.billingAddress?.firstName} ${cartInfo?.billingAddress?.lastName}` : 'Customer'
      // 3) Render template
      const renderedHtml = await this.mailTemplateConvertionService.renderTemplate(
        templateInfo?.data?.mjml,
        templateInfo?.data?.htmlContent,
        {
          ...data,
          firstName,
          cartInfo,
        }
      );

      // 4) Send now if needed
      await this.mailQueue.add('send-email', {
        to: data?.receiverInfo?.email, //  data?.receiverInfo?.email,
        subject: templateInfo?.data?.subject,
        body: renderedHtml,
        senderEmailAddress: process.env.SES_FROM_MAIL
      });

    }
  }



  @UseGuards(BasicAuthGuard)
  @Post('test/preview/queue/send-promotional-email-to-users')
  async sendPromotionalEmailToUsers(
    @Res() res: any,
    @Body() body: EmailTemplateDto
    // userList: User[], chunkSize = 50
  ) {
    const chunkSize = 1;

    const templateInfo = body?.emailTemplateId ?
      await this.mailTemplateService.getTemplateWithTranslations(body?.emailTemplateId)
      : null

    if (!templateInfo?.data?.template?.id) {
      throw new NotFoundException('Template not found');
    }

    const { template, translations } = templateInfo?.data
    const convertedHtml = await this.mailTemplateConvertionService.renderTemplate(
      body?.mjml,
      template?.htmlContent || body?.htmlData,
      {
        ...body.data,
        translations: {
          ...body.data?.translations,
          ...translations
        }
      }
    );

    // console.log('convertedHtml:::', convertedHtml);
    console.log('body.data?.receiverInfo?.userEmail?.length:::', body.data?.receiverInfo?.userEmails?.length);


    // // Split users into batches
    // for (let i = 0; i < body.data?.receiverInfo?.userEmails?.length; i += chunkSize) {
    //   const chunk = body.data?.receiverInfo?.userEmails.slice(i, i + chunkSize);

    //   console.log('chunk:::', chunk);

    //   // Add chunk as a job
    //   await this.promoMailQueue.add('promo-send-email', {
    //     users: chunk,
    //     to: body?.data?.receiverInfo?.email,
    //     subject: body?.data?.receiverInfo?.subject,
    //     body: convertedHtml,
    //     senderEmailAddress: process.env.SES_FROM_MAIL,
    //     attachments: null,
    //     cc: body?.data?.receiverInfo?.cc,
    //     bcc: body?.data?.receiverInfo?.bcc
    //   });
    // }

    // 
    // body.data?.receiverInfo?.userEmails?.forEach(user => {
    //   // Add chunk as a job
    //   this.promoMailQueue.add('promo-send-email', {
    //     users: [user],
    //     to: body?.data?.receiverInfo?.email,
    //     subject: template?.subject || body?.data?.receiverInfo?.subject,
    //     body: convertedHtml,
    //     senderEmailAddress: process.env.SES_FROM_MAIL,
    //     attachments: null,
    //     cc: body?.data?.receiverInfo?.cc,
    //     bcc: body?.data?.receiverInfo?.bcc,
    //     emailTemplateId: body?.emailTemplateId
    //   });
    // })

    // Chunk users to prevent Redis overload
    const chunkSizeNumber = 200;
    const userChunks = this.chunkArray(body.data?.receiverInfo?.userEmails, chunkSizeNumber);

    const jobs = userChunks.map((chunk, index) => ({
      name: 'promo-send-email',
      data: {
        users: chunk,
        to: body?.data?.receiverInfo?.email,
        subject: template?.subject || body?.data?.receiverInfo?.subject,
        body: convertedHtml,
        senderEmailAddress: body?.data?.senderInfo?.senderEmail || process.env.SES_FROM_MAIL,
        attachments: null,
        cc: body?.data?.receiverInfo?.cc,
        bcc: body?.data?.receiverInfo?.bcc,
        emailTemplateId: body?.emailTemplateId
      },
      opts: {
        jobId: `promo-email-${Date.now()}-${index}`,
        priority: index === 0 ? 1 : 3, // Higher priority for first chunk
      },
    }));

    await this.promoMailQueue.addBulk(jobs);

    // // 5) Save history
    // await this.cartMailHistoryRepo.save({
    //   cartUUID: cartInfo.cartUUID,
    //   stage,
    //   sent: true,
    // });


    res.send(convertedHtml)
  }

  private chunkArray<T>(array: T[], size: number): T[][] {
    return Array.from(
      { length: Math.ceil(array.length / size) },
      (_, i) => array.slice(i * size, i * size + size)
    );
  }


}

