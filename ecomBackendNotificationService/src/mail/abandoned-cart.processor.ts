// src/abandoned-cart.processor.ts

import { Processor } from '@nestjs/bullmq';
import { Job } from 'bullmq';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { MailerService } from '@nestjs-modules/mailer';
import { CartMailHistory } from 'src/mail-template/entity/email-template-history.entity';

@Processor('mail-queue')
export class AbandonedCartProcessor {
  constructor(
    // @InjectRepository(Cart) private cartRepo: Repository<Cart>,
    @InjectRepository(CartMailHistory) private mailHistoryRepo: Repository<CartMailHistory>,
    private mailer: MailerService,
  ) {}

//   @Process('send-abandoned-cart')
//   async handle(job: Job) {
//     const { cartUUID, stage } = job.data;

//     const cart = await this.cartRepo.findOne({ where: { uuid: cartUUID } });
//     if (!cart) return;

//     const email = cart.shippingAddress?.email;
//     if (!email) return;

//     // Render & send
//     await this.mailer.sendMail({
//       to: email,
//       subject: `You left something in your cart!`,
//       template: './abandoned-cart', // your template file
//       context: {
//         name: cart.shippingAddress?.name || 'Customer',
//         cart,
//         stage,
//       },
//     });

//     // Record history
//     await this.mailHistoryRepo.save({
//       cartUUID,
//       stage,
//       sent: true,
//     });
//   }
}
