// src/abandoned-cart.service.ts

import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { InjectQueue } from '@nestjs/bullmq';
import { In, <PERSON>Than, <PERSON><PERSON>han, Not, Raw, Repository } from 'typeorm';
import { Queue } from 'bullmq';
import { Cron, CronExpression } from '@nestjs/schedule';

// import { Cart } from './entities/cart.entity';
// import { CartMailHistory } from './entities/cart-mail-history.entity';
import axios from 'axios';
import { EmailTemplateService } from 'src/mail-template/email-template.service';
import { MailTemplateConvertionService } from './mail-template-cnvertion.service';
import { mapTranslations } from 'libs/common/utils/translation-helper';
import { getLanguageCodeByCountryId } from 'libs/common/utils/country-helper';
import { SendEmailWithQueueDto } from './dto/send-mail-template.dto';
import { CartMailHistory } from 'src/mail-template/entity/email-template-history.entity';

@Injectable()
export class AbandonedCartService {
    constructor(
        // @InjectRepository(Cart) private cartRepo: Repository<Cart>,
        @InjectRepository(CartMailHistory) private cartMailHistoryRepo: Repository<CartMailHistory>,
        @InjectQueue('mail-queue') private mailQueue: Queue,
        private readonly mailTemplateService: EmailTemplateService,
        private readonly mailTemplateConvertionService: MailTemplateConvertionService,
    ) { }

    async getAbandonedCartsOlderThan(stage: string, hours: number, usedCartUUIDs?: string[]) {
        // 2) Get cart data from DB
        let axiosConfig = {
            headers: {
                'Content-Type': 'application/json;charset=UTF-8',
                "Access-Control-Allow-Origin": "*",
                'Authorization': `Basic ${process.env.ECOMMERCE_API_BASIC_AUTH}`
            }
        };

        let data = JSON.stringify({
            "timeFrame": stage,
            "hours": hours,
            "usedCartUUIDs": usedCartUUIDs
        });

        try {
            const cartData = await axios.post(
                `${process.env.ECOMMERCE_API_HOST}/cart/web/getAbandonedCarts`,
                data,
                axiosConfig
            );

            return cartData?.data?.data
        } catch (error) {
            console.log('getAbandonedCartsOlderThan error:::', error?.message);
        }
    }

    @Cron(CronExpression.EVERY_30_MINUTES)
    async checkAbandonedCarts() {
        console.log('Running abandoned cart check...');

        await this.processStage('30m');
        // await this.processStage('2h');
        // await this.processStage('2d');
        // await this.processStage('7d');
    }

    private async processStage(stage: '2h' | '2d' | '7d' | any, hours?: number) {
        const notBeforeDate = new Date('2025-08-12');
        const cartMailHistoryData = await this.cartMailHistoryRepo.find({
            where: {
                stage: In([stage]),
                sent: true,
                createdAt: MoreThan(notBeforeDate)
            },
            select: ['id', 'stage', 'sent', 'cartUUID']
        })

        const usedCartUUIDs = cartMailHistoryData?.map((item) => item.cartUUID);

        const cartData = await this.getAbandonedCartsOlderThan(stage, hours, usedCartUUIDs);

        const sendMailObject: SendEmailWithQueueDto = {
            emailTemplateId: 1,
            mjml: '<mjml>...</mjml>',
            htmlData: '<html>...</html>',
            data: {
                "receiverInfo": {
                    // "email": "<EMAIL>, <EMAIL>, <EMAIL>",
                    "email": "<EMAIL>, <EMAIL>",
                    "subject": "Builder Testing Abandoned Cart"
                },
                "firstName": "",
                "orderId": "123456",
                "locale": "de",
                "translations": {
                    "en": {
                        "greeting": "Hello"
                    },
                    "de": {
                        "greeting": "Hallo"
                    }
                },
                "cartInfo": {
                    countryId: 1,
                    "cartUUID": "1748148676",
                    "subtotal": "333.00",
                    "tax": "33.85",
                    "total": "333.00",
                    "cart": [
                        {
                            "name": "Maneca scurta tricou bărbați - pachet de 4",
                            "discountPrice": "222.00",
                            "quantity": 1,
                            "currency": "RON",
                            "size": { "name": "XS" },
                            "featuredImage": {
                                "imageGalleryUrls": {
                                    "medium": "https://cdn.pantoneclo.com/product/dsc69070_1731322589366_medium_452_505.jpg"
                                }
                            }
                        },
                        {
                            "name": "Maneca scurta tricou bărbați - pachet de 4",
                            "discountPrice": "111.00",
                            "quantity": 2,
                            "currency": "RON",
                            "size": { "name": "XS" },
                            "featuredImage": {
                                "imageGalleryUrls": {
                                    "medium": "https://cdn.pantoneclo.com/product/dsc69070_1731322589366_medium_452_505.jpg"
                                }
                            }
                        }
                    ]
                }
            }
        }

        // 1) Fetch template
        const templateInfo = await this.mailTemplateService.getTemplateByType('ABANDONED_CART');

        if (!templateInfo?.data?.id) {
            console.log('ABANDONED_CART template not found');
            return
        }

        for (const cartInfo of cartData) {
            const locale = getLanguageCodeByCountryId(cartInfo?.countryId) || 'en';
            const translations = mapTranslations(templateInfo?.data?.translationKeys);
            const subjectLocale = translations?.[locale]?.EMAIL_SUBJECT || templateInfo?.data?.subject

            const mailHistory = await this.shouldSend(stage, cartInfo.cartUUID);
            if (mailHistory) {
                const renderedHtml = await this.mailTemplateConvertionService.renderTemplate(
                    templateInfo?.data?.mjml,
                    templateInfo?.data?.htmlContent,
                    {
                        ...sendMailObject?.data,
                        firstName: cartInfo?.billingAddress?.firstName || 'Customer',
                        cartInfo,
                        locale,
                        translations
                    }
                );

                // 4) Send now if needed
                await this.mailQueue.add('send-email', {
                    to: [cartInfo?.user?.email || cartInfo?.billingAddress?.email], // ['<EMAIL>'], //  cartInfo?.billingAddress?.email
                    subject: subjectLocale,
                    body: renderedHtml,
                    senderEmailAddress: process.env.SES_FROM_MAIL,
                    bcc: ['<EMAIL>'], // "<EMAIL>"
                });

                // 5) Save history
                await this.cartMailHistoryRepo.save({
                    cartUUID: cartInfo.cartUUID,
                    stage,
                    sent: true,
                });

            }
        }
    }

    private parseStageToMs(stage: string): number {
        const regex = /^(\d+)([smhd])$/;
        const match = stage.match(regex);
        if (!match) throw new Error(`Invalid stage format: ${stage}`);
        const value = parseInt(match[1], 10);
        const unit = match[2];
        switch (unit) {
            case 's': return value * 1000;
            case 'm': return value * 60 * 1000;
            case 'h': return value * 60 * 60 * 1000;
            case 'd': return value * 24 * 60 * 60 * 1000;
            default: throw new Error(`Unsupported unit: ${unit}`);
        }
    }

    private getSortedStages(stages: string[]): string[] {
        const sortedStages = stages
            .map(stage => ({ stage, ms: this.parseStageToMs(stage) }))
            .sort((a, b) => a.ms - b.ms)
            .map(item => item.stage);

        return sortedStages
    }

    private async shouldSend(stage: string, cartUUID: string): Promise<boolean> {
        const history = await this.cartMailHistoryRepo.findOne({
            where: { cartUUID, stage, sent: true },
        });
        if (history) return false;

        const allStages = [stage]; // or load from DB or config
        const sortedStages = this.getSortedStages(allStages);

        const currentIndex = sortedStages.indexOf(stage);
        if (currentIndex === -1) {
            throw new Error(`Invalid stage: ${stage}`);
        }

        for (let i = 0; i < currentIndex; i++) {
            const prevStage = sortedStages[i];
            const prevSent = await this.cartMailHistoryRepo.findOne({
                where: { cartUUID, stage: prevStage, sent: true },
            });
            if (!prevSent) return false;
        }

        return true;
    }

}
