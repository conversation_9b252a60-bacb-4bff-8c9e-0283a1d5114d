import { INestApplication } from '@nestjs/common';
import { SwaggerModule as NestSwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import * as swaggerStats from 'swagger-stats';
import * as fs from 'fs';


export class SwaggerService {
    setup(app: INestApplication) {
        const options = new DocumentBuilder()
            .setTitle('Pantoneclo Ecommerce API')
            .setDescription('Pantoneclo Ecommerce API Description')
            .setVersion('1.0')
            .addServer('api') // Add 'api' as a prefix
            // .addBearerAuth()
            .addBasicAuth()
            .build();

        const document = NestSwaggerModule.createDocument(app, options);
        NestSwaggerModule.setup('api', app, document, {
            swaggerOptions: { basePath: 'api' },
            // swaggerUiEnabled: process.env.NODE_ENV === 'production',
        });
    }

    swaggerStats(app: INestApplication) {
        app.use(
            swaggerStats.getMiddleware({
                uriPath: '/swagger-stats', // Swagger Stats dashboard URL
                swaggerSpec: '/swagger-json', // Swagger JSON endpoint
                authentication: process.env.NODE_ENV === 'production', // Set to true if authentication is needed
                metricsPrefix: 'api_', // Prefix for metrics
                durationBuckets: [0.1, 0.5, 1, 5, 10], // Response time tracking
                requestSizeBuckets: [128, 512, 1024, 5120, 10000], // Request size tracking
                responseSizeBuckets: [128, 512, 1024, 5120, 10000], // Response size tracking
                onResponseFinish: (req, res, metrics) => {
                    if (metrics.responsetime > 500 || metrics?.http?.response?.code >= 400) {
                        const newDate = new Date().toISOString().split('T')[0];
                        const logData = {
                            timestamp: new Date().toISOString(),
                            status: res.statusCode,
                            responseTime: metrics.responsetime,
                            real_ip: req.headers['x-forwarded-for'] || req?.connection?.remoteAddress || req?.socket?.remoteAddress || req?.ip || metrics?.real_ip || 'unknown',
                            method: req.method,
                            path: req.originalUrl,
                            node: metrics?.http?.request?.headers['user-agent'] || 'unknown',
                            // agent: JSON.stringify(metrics?.node) || 'unknown',
                        };
                        fs.appendFileSync(`./logs/${newDate}_logs.txt`, JSON.stringify(logData) + '\n');
                    }
                },
                onAuthenticate: function (req, username, password) {
                    // simple check for username and password
                    return ((username === 'swagger_stats_admin')
                        && (password === 'swagger_stats_admin@123'));
                }
            }),
        );
    }
}
