// import { Role } from 'src/domain/roles/entities/role.entity';
import { ITimeStamp } from './timestamp.interface';

export interface IUser extends ITimeStamp {
  id: number;
  firstName?: string;
  lastName?: string;
  email: string;
  phone: string;
  password: string;
  imageUrl:string
  dob?: Date;
  gender?: string;
  userTypeId?: number;
  addressId?: number;
  loginId?: number;
  countryCode?: number;
  userType?: object;
  // role?: Role;
}
