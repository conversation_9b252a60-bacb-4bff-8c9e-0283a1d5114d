import { Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bullmq';
import { redisConfig } from 'src/config/app-config';

@Module({
  imports: [
    // TypeOrmModule.forRootAsync({
    //   useClass: TypeormConfigService,
    // }),
    BullModule.forRootAsync({
      useFactory: async () => ({
        connection: {
          host: redisConfig().host,
          port: redisConfig().port,
          username: redisConfig().username,
          password: redisConfig().password,
          db: redisConfig().database,
        },
      }),
    }),
    BullModule.registerQueue({
      name: 'mail-queue',
    }),
  ],
  exports: [CustomBullModule],
})
export class CustomBullModule { }
