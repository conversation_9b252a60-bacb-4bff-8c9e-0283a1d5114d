// // s3.service.ts

// import { Injectable } from '@nestjs/common';
// import * as AWS from 'aws-sdk';

// @Injectable()
// export class S3Service {
//   // private s3: AWS.S3;

//   // constructor() {
//   //   this.s3 = new AWS.S3({
//   //     accessKeyId: process.env.AWS_S3_ACCESS_KEY,
//   //     secretAccessKey: process.env.AWS_S3_KEY_SECRET,
//   //   });
//   // }

//   private s3: AWS.S3 = new AWS.S3({
//     accessKeyId: process.env.AWS_S3_ACCESS_KEY,
//     secretAccessKey: process.env.AWS_S3_KEY_SECRET,
//   });

//   async uploadImage(
//     file: Buffer,
//     fileName: string,
//     folderName: string,
//     mimetype: string
//   ): Promise<string> {
//     const params: AWS.S3.PutObjectRequest = {
//       Bucket: process.env.AWS_S3_BUCKET,
//       Key: `${folderName}/${fileName}`,
//       Body: file,
//       ContentType: mimetype,
//       ACL: 'public-read',
//       // ContentType: mimetype,
//       ContentDisposition: 'inline',
//     };
//     let s3Response = null;
//     try {
//       s3Response = await this.s3.upload(params).promise();
//     } catch (e) {
//       console.log(e);
//     }
//     return s3Response?.Location;
//   }
//   async uploadVideo(
//     file: Express.Multer.File,
//     fileName: string,
//     folderName: string,
//     mimetype: string
//   ): Promise<string> {
//     const params: AWS.S3.PutObjectRequest = {
//       Bucket: process.env.AWS_S3_BUCKET,
//       Key: `${folderName}/${fileName}`,
//       Body: file,
//       ContentType: file.mimetype,
//       ACL: 'public-read',
//       // ContentType: mimetype,
//       ContentDisposition: 'inline',
//     };
//     let s3Response = null;
//     try {
//       s3Response = await this.s3.upload(params).promise();
//     } catch (e) {
//       console.log(e);
//     }
//     return s3Response?.Location;
//   }

//   async getImageUrl(key: string): Promise<string> {
//     const params: AWS.S3.GetObjectRequest = {
//       Bucket: process.env.AWS_S3_BUCKET,
//       Key: key,
//     };

//     const url = await this.s3.getSignedUrlPromise('getObject', params);
//     return url;
//   }
// }
