import { Injectable } from '@nestjs/common';
import { MailerService } from '@nestjs-modules/mailer';
import { ConfigService } from '@nestjs/config';
import * as Handlebars from 'handlebars';
import * as fs from 'fs';
import * as path from 'path';
import { getLanguageCodeById } from '../utils/country-utils';

@Injectable()
export class MailService {
  private readonly mailerService: MailerService;
  private readonly gmailerService: MailerService;
  private translations: Record<string, Record<string, any>> = {};

  async onModuleInit() {
    await this.loadTranslations();
    await this.registerHandlebarsHelpers();
  }

  constructor(private readonly configService: ConfigService) {
    this.mailerService = new MailerService(
      this.configService.get('mailer'),
      null,
    );

    this.gmailerService = new MailerService(
      this.configService.get('gmailer'),
      null,
    );

    this.registerHandlebarsHelpers();
  }

  private async loadTranslations(): Promise<void> {
    try {
      const translationsPath = path.join(
        process.cwd(),
        'src',
        'i18n',
        'translations',
      );
      const files = await fs.promises.readdir(translationsPath);

      for (const file of files) {
        if (file.endsWith('.json')) {
          const language = path.basename(file, '.json');
          const filePath = path.join(translationsPath, file);

          try {
            const fileContent = await fs.promises.readFile(filePath, 'utf8');
            this.translations[language] = JSON.parse(fileContent);
            // console.log(`Successfully loaded translations for ${language}`);
          } catch (error) {
            console.error(`Error loading translations for ${language}:`, error);
          }
        }
      }
    } catch (error) {
      console.error('Error loading translations:', error);
      throw new Error('Failed to load translations');
    }
  }

  private async registerHandlebarsHelpers() {
    Handlebars.registerHelper('t', (key: string, options) => {
      const language = options.data.root?.language || options.data.root?.languageCode || 'en';

      try {
        const languageTranslations = this.translations[language];
        if (!languageTranslations) {
          console.warn(`No translations found for language: ${language}`);
          return key;
        }

        // Handle nested keys (e.g., "items.sn")
        const parts = key.split('.');
        let translation = languageTranslations;
        for (const part of parts) {
          translation = translation[part];
          if (translation === undefined) {
            console.warn(
              `No translation found for key: ${key} in language: ${language}`,
            );
            return key;
          }
        }

        return translation;
      } catch (error) {
        console.error(`Translation error for key ${key}:`, error);
        return key;
      }
    });
    // Register the 'eq' helper
    Handlebars.registerHelper('eq', (arg1, arg2) => arg1 === arg2);
  }

  private async registerHandlebarsSocialMediaHelpers(socialMedia: any) {
    const defaultSocialLinks = {
      facebook: 'https://www.facebook.com/profile.php?id=61563869323043',
      instagram: 'https://www.instagram.com/pantoneclo/',
      linkedin: 'https://www.linkedin.com/company/98777995/admin/dashboard/',
      twitter: 'https://x.com/pantoneclo',
      youtube: 'https://www.youtube.com/embed/usnQk469EWs',
      tiktok: 'https://www.tiktok.com/@pantoneclo',
    };
    // Create social media links object with country-specific links
    const socialMediaLinks = {
      facebook: socialMedia?.facebook || defaultSocialLinks.facebook,
      instagram: socialMedia?.instagram || defaultSocialLinks.instagram,
      linkedin: socialMedia?.linkedin || defaultSocialLinks.linkedin,
      twitter: socialMedia?.x || defaultSocialLinks.twitter,
      youtube: socialMedia?.youtube || defaultSocialLinks.youtube,
      tiktok: socialMedia?.tiktok || defaultSocialLinks.tiktok,
    };

    // Register Handlebars helper for social media links
    Handlebars.registerHelper('getSocialLink', function (platform: string) {
      return socialMediaLinks[platform] || defaultSocialLinks[platform];
    });
  }
/**
 * 
 * @param to 
 * @param subject 
 * @param body 
 * @param senderEmailAddress Add Sender Email
 * @param attachments 
 */
  async sendMail(
    to: string,
    subject: string,
    body: string,
    // pdfFilePath: string,
    senderEmailAddress: string,
    attachments?: any,
  ) {
    const senderEmail = senderEmailAddress || process.env.SES_FROM_MAIL;

    console.log('to:::', to);
    console.log('subject:::', subject);
    // console.log('body:::', body);
    console.log('senderEmail:::', senderEmail);
    console.log('attachments:::', attachments);
    

    try {
      await this.mailerService.sendMail({
        to,
        subject,
        from: `"PANTONECLO" <${senderEmail}>`,
        text: body, // plain text body
        html: body, // HTML body (you can adjust if you want to use templates)
        attachments,
      });
    } catch (error) {
      try {
        await this.gmailerService.sendMail({
          to,
          subject,
          from: `"PANTONECLO" <${senderEmail}>`,
          text: body, // plain text body
          html: body, // HTML body (you can adjust if you want to use templates)
          attachments,
        });
        console.error(
          'Primary SMTP failed. Switching to fallback SMTP...',
          error,
        );
      } catch (fallbackError) {
        console.log('Both primary and fallback SMTP failed.', fallbackError);
      }
    }
  }

  async renderEmailTemplate(templateName: string, data: any): Promise<string> {
    const possibleTemplatePaths = [
      path.join(
        process.cwd(),
        'src',
        'common',
        'templates',
        `${templateName}.html`,
      ),
      path.join(
        process.cwd(),
        'dist',
        'common',
        'templates',
        `${templateName}.html`,
      ),
    ];

    const templatePath = possibleTemplatePaths.find((path) =>
      fs.existsSync(path),
    );

    if (!templatePath) {
      throw new Error('Invoice template not found');
    }

    await this.registerHandlebarsSocialMediaHelpers(data?.socialMediaLinks);

    const templateSource = fs.readFileSync(templatePath, 'utf8');
    const compiledTemplate = Handlebars.compile(templateSource);
    return compiledTemplate({ ...data, t: this.translations });
  }


  /**
   * getTranslatedEmailSubject
   */
  public getTranslatedEmailSubject(languageId: number, subjectKey: string) {
    const languageCode = getLanguageCodeById(languageId).toLocaleLowerCase();
    // let senderEmailAddress: string = `no-reply@${result.country.domain}` || process.env.SES_FROM_MAIL;
    // switch (languageCode) {
    //   case 'en':
    //     subject = 'Your order has been placed';
    //     break;
    //   case 'bg':
    //     subject = 'Вашата поръчка е направена';
    //     break;
    //   case 'bd':
    //     subject = 'আপনার অর্ডারটি সফলভাবে সম্পন্ন হয়েছে';
    //     break;
    //   case 'cz':
    //     subject = 'Vaše objednávka byla zadána';
    //     break;
    //   case 'de':
    //     subject = 'Ihre Bestellung wurde aufgegeben';
    //     break;
    //   case 'at':
    //     subject = 'Ihre Bestellung wurde aufgegeben';
    //     break;
    //   case 'de-at':
    //     subject = 'Ihre Bestellung wurde aufgegeben';
    //     break;
    //   case 'gr':
    //     subject = 'Η παραγγελία σας έχει καταχωρηθεί';
    //     break;
    //   case 'el':
    //     subject = 'Η παραγγελία σας έχει καταχωρηθεί';
    //     break;
    //   case 'es':
    //     subject = 'Tu pedido ha sido realizado';
    //     break;
    //   case 'hr':
    //     subject = 'Vaša narudžba je poslana';
    //     break;
    //   case 'hu':
    //     subject = 'A rendelésed leadásra került';
    //     break;
    //   case 'it':
    //     subject = 'Il tuo ordine è stato effettuato';
    //     break;
    //   case 'lt':
    //     subject = 'Jūsų užsakymas pateiktas';
    //     break;
    //   case 'pl':
    //     subject = 'Twoje zamówienie zostało złożone';
    //     break;
    //   case 'pt':
    //     subject = 'Seu pedido foi realizado';
    //     break;
    //   case 'ro':
    //     subject = 'Comanda dvs. a fost plasată';
    //     break;
    //   case 'sk':
    //     subject = 'Vaša objednávka bola zadaná';
    //     break;
    //   case 'sl':
    //     subject = 'Vaše naročilo je bilo oddano';
    //     break;
    //   case 'si':
    //     subject = 'Vaše naročilo je bilo oddano';
    //     break;
    //   default:
    //     subject = 'Your order has been placed'; // Fallback to English if no match
    // }
    let senderEmailAddress: string = process.env.SES_FROM_MAIL;
    const subject = this.translations[languageCode][subjectKey]
    
    return { subject, senderEmailAddress };
  }
}
