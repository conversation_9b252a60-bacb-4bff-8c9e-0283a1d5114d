// s3.service.ts

import { Injectable } from '@nestjs/common';
import { S3Client, PutObjectCommand, GetBucketLocationCommand, S3ClientConfig } from '@aws-sdk/client-s3';
import { Express } from 'express';

@Injectable()
export class S3Service {
  // private s3: AWS.S3;

  // constructor() {
  //   this.s3 = new AWS.S3({
  //     accessKeyId: process.env.AWS_S3_ACCESS_KEY,
  //     secretAccessKey: process.env.AWS_S3_KEY_SECRET,
  //   });
  // };

  ;
  private readonly S3_CLIENT_CONFIG: S3ClientConfig = process.env.AWS_S3_CUSTOM_END_POINT ? {
    region: process.env.AWS_REGION,
    endpoint: `https://${process.env.AWS_S3_CUSTOM_END_POINT}`,
    credentials: {
      accessKeyId: process.env.AWS_S3_ACCESS_KEY,
      secretAccessKey: process.env.AWS_S3_KEY_SECRET,
    },
    forcePathStyle: true,
  } : {
    region: process.env.AWS_REGION,
    credentials: {
      accessKeyId: process.env.AWS_S3_ACCESS_KEY,
      secretAccessKey: process.env.AWS_S3_KEY_SECRET,
    },
  }

  private readonly s3Client: S3Client = new S3Client(this.S3_CLIENT_CONFIG);
  private readonly bucketName: string = process.env.AWS_S3_BUCKET;




  async uploadImage(
    file: Buffer,
    fileName: string,
    folderName: string,
    mimetype: string
  ): Promise<string> {
    // folderName = 'test';
    const key = folderName ? `${folderName}/${fileName}` : fileName;

    const commandConfig: any = process.env.AWS_S3_CUSTOM_END_POINT ? {
      Bucket: this.bucketName,
      Key: key,
      Body: file,
      ContentType: mimetype,
      ACL: 'public-read',
    } : {
      Bucket: this.bucketName,
      Key: key,
      Body: file,
      ACL: 'public-read',
      ContentType: mimetype,
      ContentDisposition: 'inline',
    }
    const command = new PutObjectCommand(commandConfig);

    try {
      await this.s3Client.send(command);
      if (process.env.AWS_S3_CUSTOM_END_POINT) {
        return `https://${process.env.AWS_S3_CUSTOM_END_POINT}/${this.bucketName}/${folderName}/${fileName}`;
      }
      else {
        return `https://${this.bucketName}.s3.amazonaws.com/${folderName}/${fileName}`;
      }
    } catch (error) {
      console.error('Error uploading file: ', error);
      throw error;
    }
  }

  // async uploadImage(
  //   file: Buffer,
  //   fileName: string,
  //   folderName: string,
  // ): Promise<string> {
  //   const params: AWS.S3.PutObjectRequest = {
  //     Bucket: process.env.AWS_S3_BUCKET,
  //     Key: `${folderName}/${fileName}`,
  //     Body: file,
  //     ContentType: 'webp',
  //     ACL: 'public-read',
  //     // ContentType: mimetype,
  //     ContentDisposition: 'inline',
  //   };
  //   let s3Response = null;
  //   try {
  //     s3Response = await this.s3.upload(params).promise();
  //   } catch (e) {
  //     console.log(e);
  //   }
  //   return s3Response?.Location;
  // }

  async uploadVideo(
    file: Express.Multer.File,
    fileName: string,
    folderName: string,
    mimetype: string,
  ): Promise<string> {
    const key = folderName ? `${folderName}/${fileName}` : fileName;

    const commandConfig: any = process.env.AWS_S3_CUSTOM_END_POINT ? {
      Bucket: this.bucketName,
      Key: key,
      Body: file.buffer,
      ContentType: mimetype,
      ACL: 'public-read',
    } : {
      Bucket: this.bucketName,
      Key: key,
      Body: file.buffer,
      ACL: 'public-read',
      ContentType: mimetype,
      ContentDisposition: 'inline',
    }
    const command = new PutObjectCommand(commandConfig);
    
    try {
      await this.s3Client.send(command);
      if (process.env.AWS_S3_CUSTOM_END_POINT) {
        return `https://${process.env.AWS_S3_CUSTOM_END_POINT}/${this.bucketName}/${folderName}/${fileName}`;
      }
      else {
        return `https://${this.bucketName}.s3.amazonaws.com/${folderName}/${fileName}`;
      }
    } catch (error) {
      console.error('Error uploading file: ', error);
      throw error;
    }
  }

  // async getImageUrl(key: string): Promise<string> {
  //   const params: AWS.S3.GetObjectRequest = {
  //     Bucket: process.env.AWS_S3_BUCKET,
  //     Key: key,
  //   };

  //   const url = await this.s3.getSignedUrlPromise('getObject', params);
  //   return url;
  // }

  async uploadFile(
    file: Buffer | string,
    fileName: string,
    folderName: string,
    mimetype: string
  ): Promise<string> {
    // folderName = 'test';
    const key = folderName ? `${folderName}/${fileName}` : fileName;

    const commandConfig: any = process.env.AWS_S3_CUSTOM_END_POINT ? {
      Bucket: this.bucketName,
      Key: key,
      Body: file,
      ContentType: mimetype,
      ACL: 'public-read',
    } : {
      Bucket: this.bucketName,
      Key: key,
      Body: file,
      ACL: 'public-read',
      ContentType: mimetype,
      ContentDisposition: 'inline',
    }
    const command = new PutObjectCommand(commandConfig);

    try {
      await this.s3Client.send(command);
      if (process.env.AWS_S3_CUSTOM_END_POINT) {
        return `https://${process.env.AWS_S3_CUSTOM_END_POINT}/${this.bucketName}/${folderName}/${fileName}`;
      }
      else {
        return `https://${this.bucketName}.s3.amazonaws.com/${folderName}/${fileName}`;
      }
    } catch (error) {
      console.error('Error uploading file: ', error);
      throw error;
    }
  }
}
