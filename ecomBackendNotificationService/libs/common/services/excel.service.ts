import { Injectable } from '@nestjs/common';
import { Workbook } from 'exceljs';
import { file } from 'tmp';

@Injectable()
export class ExcelService {
  async generateExcel(data: any) {
   
    const rows = [];
    data.forEach((item) => {
      rows.push(Object.values(item));
    });
    const book = new Workbook();
    const sheet = book.addWorksheet('Sheet 1');
    rows.unshift(Object.keys(data[0]));
    sheet.addRows(rows);

    const gfile = new Promise((resolve, reject) => {
      file(
        {
          discardDescriptor: true,
          prefix: 'excel-1',
          postfix: '.xlsx',
          mode: parseInt('0600', 8),
        },
        (err, file) => {
          book.xlsx
            .writeFile(file)
            .then(() => {
              console.log('Excel generated');
              resolve(file);
            })
            .catch((err) => {
              console.log(err);
            });
        },
      );
    }) as Promise<string>;

    return gfile;
  }
}
