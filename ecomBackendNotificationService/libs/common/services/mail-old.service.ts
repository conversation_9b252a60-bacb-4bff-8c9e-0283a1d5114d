import * as nodemailer from 'nodemailer';
import { Injectable } from '@nestjs/common';

@Injectable()
export class MailOldService {
  private transporter: nodemailer.Transporter;

  constructor() {
    this.transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST,
      port: parseInt(process.env.SMTP_PORT),
      secure: process.env.SMTP_SECURE === 'true', // Set to true if using TLS
      auth: {
        user: process.env.SMTP_USERNAME,
        pass: process.env.SMTP_PASSWORD,
      },
    });
  }

  public async sendEmailWithAttachment(
    to: string,
    subject: string,
    text: string,
    attachment?: Buffer,
  ): Promise<void> {
    const mailOptions = {
      from: process.env.EMAIL_FROM,
      to: to,
      subject: subject,
      text: text,
      attachments: [{ filename: 'invoice.pdf', content: attachment }],
    };
    // const mailOptions = {
    //   from: '<EMAIL>',
    //   to: to,
    //   subject: subject,
    //   text: text,
    //   attachments: [{ filename: 'invoice.pdf', content: attachment }],
    // };

    await this.transporter.sendMail(mailOptions);
  }
}
