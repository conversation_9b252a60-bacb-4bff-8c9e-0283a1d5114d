import { ConfigService } from '@nestjs/config';
import { TypeOrmModuleOptions, TypeOrmOptionsFactory } from '@nestjs/typeorm';
import { Injectable } from '@nestjs/common';
import { IDatabaseConfig } from 'src/config/database-config';
import { CustomTypeOrmLogger } from './typeorm.logger';
import { EmailTemplate, EmailTranslation } from 'src/mail-template/entity/email-template.entity';
import { TranslationKey, TranslationValue } from 'src/translation/entity/translation.entity';
import { EmailCampaign } from 'src/campaign/entity/email-campaign.entity';
import { User } from 'src/user/entity/user.entity';
import { CampaignReport } from 'src/campaign/entity/campaign-report.entity';
import { EmailGroup } from 'src/campaign/entity/email-group.entity';
import { EmailGroupMember } from 'src/campaign/entity/email-group-member.entity';
import { CartMailHistory, EmailSendHistory } from 'src/mail-template/entity/email-template-history.entity';

@Injectable()
export class TypeormConfigService implements TypeOrmOptionsFactory {
  constructor(private configService: ConfigService) { }

  private async reconnect(connection) {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars 
    const databaseConfig = this.configService.get(
      'database',
    ) as IDatabaseConfig;

    try {
      await connection.connect();
      console.log('Reconnected to the database.');
    } catch (error) {
      console.log('Reconnection failed:', error);
    }
  }

  createTypeOrmOptions(): TypeOrmModuleOptions {
    const databaseConfig = this.configService.get(
      'database',
    ) as IDatabaseConfig;

    return {
      type: databaseConfig.type,
      host: databaseConfig.host,
      port: databaseConfig.port,
      username: databaseConfig.username,
      password: databaseConfig.password,
      database: databaseConfig.database,
      canRetry: true,
      ssl:
        databaseConfig.dbSSL === 'true' ? { rejectUnauthorized: false } : false,
      entities: [
        EmailTemplate,
        EmailTranslation,
        TranslationKey,
        TranslationValue,
        CartMailHistory,
        EmailSendHistory,
        User,
        EmailCampaign,
        CampaignReport,
        EmailGroup,
        EmailGroupMember,

      ], // All the entities that you create should be added here
      synchronize: true,
      logging: process.env.NODE_ENV == 'production', // Disable logs in production
      logger: process.env.NODE_ENV == 'production' ? new CustomTypeOrmLogger() : 'advanced-console',
      maxQueryExecutionTime: 300,
      keepConnectionAlive: true,
      extra: {
        idleTimeoutMillis: 10000, // 10 seconds
        keepAlive: true,
        max: 10,
        connectionTimeoutMillis: 5000,
      },
      beforeQuery: async (queryRunner) => {
        if (!queryRunner.connection.isConnected) {
          console.log('Connection lost, reconnecting...');
          await this.reconnect(queryRunner.connection);
        }
      },
    } as TypeOrmModuleOptions;
  }
}
