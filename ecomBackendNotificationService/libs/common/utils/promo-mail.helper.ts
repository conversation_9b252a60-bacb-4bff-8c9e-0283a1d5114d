// utils
const EMAIL_REGEX =
    /^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/i;

const sleep = (ms: number) => new Promise(res => setTimeout(res, ms));
const chunkify = <T>(arr: T[], size: number) =>
    Array.from({ length: Math.ceil(arr.length / size) }, (_, i) =>
        arr.slice(i * size, i * size + size)
    );

function dedupeAndValidateEmails(raw: unknown[]): string[] {
    const uniq = new Set<string>();
    for (const v of raw || []) {
        const e = String(v || '').trim().toLowerCase();
        if (e && EMAIL_REGEX.test(e)) uniq.add(e);
    }
    return Array.from(uniq);
}

function stripCss(html) {
    let cleaned = html.replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '');
    cleaned = cleaned.replace(/ style="[^"]*"/gi, '');
    return cleaned;
}


export function cleanEmails(raw: unknown[] = []): string[] {
    const seen = new Set<string>();
    for (const v of raw) {
        const e = String(v || '').trim().toLowerCase();
        if (e && EMAIL_REGEX.test(e)) seen.add(e);
    }
    return Array.from(seen);
}

export function chunkArray<T>(array: T[], size: number): T[][] {
    return Array.from({ length: Math.ceil(array.length / size) },
        (_, i) => array.slice(i * size, i * size + size));
}

export { dedupeAndValidateEmails, chunkify, sleep, stripCss };
