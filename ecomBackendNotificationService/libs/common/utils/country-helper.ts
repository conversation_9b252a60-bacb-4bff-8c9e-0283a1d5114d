export const domainData = [
    { id: 15, name: "Austria", code: "AT", domain: "pantoneclo.at", lang: "at", langId: 17, currency: 'EUR', currencySymbol: '€' },
    { id: 1, name: "Bangladesh", code: "BD", domain: "pantoneclo.com.bd", lang: "en", langId: 1, currency: 'BDT', currencySymbol: 'Tk.' },
    { id: 14, name: "Bulgaria", code: "BG", domain: "pantoneclo.bg", lang: "bg", langId: 4, currency: 'BGN', currencySymbol: 'BGN' },
    { id: 13, name: "Croatia", code: "HR", domain: "pantoneclo.hr", lang: "hr", langId: 8, currency: 'EUR', currencySymbol: '€' },
    { id: 12, name: "Czech Republic", code: "CZ", domain: "pantoneclo.cz", lang: "cz", langId: 5, currency: 'CZK', currencySymbol: 'CZK' },
    { id: 11, name: "Germany", code: "DE", domain: "pantoneclo.de", lang: "de", langId: 3, currency: 'EUR', currencySymbol: '€' },
    { id: 2, name: "Greece", code: "GR", domain: "pantoneclo.gr", lang: "el", langId: 6, currency: 'EUR', currencySymbol: '€' },
    { id: 16, name: "Hungary", code: "HU", domain: "pantoneclo.hu", lang: "hu", langId: 9, currency: 'HUF', currencySymbol: 'HUF' },
    { id: 19, name: "India", code: "IN", domain: "pantoneclo.in", lang: "en", langId: 1, currency: 'INR', currencySymbol: '₹' },
    { id: 9, name: "Italy", code: "IT", domain: "pantoneclo.it", lang: "it", langId: 10, currency: 'EUR', currencySymbol: '€' },
    { id: 8, name: "Lithuania", code: "LT", domain: "pantoneclo.lt", lang: "lt", langId: 11, currency: 'EUR', currencySymbol: '€' },
    { id: 7, name: "Poland", code: "PL", domain: "pantoneclo.pl", lang: "pl", langId: 12, currency: 'PLN', currencySymbol: 'PLN' },
    { id: 6, name: "Portugal", code: "PT", domain: "pantoneclo.pt", lang: "pt", langId: 13, currency: 'EUR', currencySymbol: '€' },
    { id: 5, name: "Romania", code: "RO", domain: "pantoneclo.ro", lang: "ro", langId: 14, currency: 'RON', currencySymbol: 'RON' },
    { id: 4, name: "Slovakia", code: "SK", domain: "pantoneclo.sk", lang: "sk", langId: 15, currency: 'EUR', currencySymbol: '€' },
    { id: 3, name: "Slovenia", code: "SI", domain: "pantoneclo.si", lang: "sl", langId: 16, currency: 'EUR', currencySymbol: '€' },
    { id: 3, name: "Slovenia", code: "SI", domain: "pantoneclo.com", lang: "sl", langId: 16, currency: 'EUR', currencySymbol: '€' },
    { id: 3, name: "Slovenia", code: "SI", domain: "localhost", lang: "sl", langId: 16, currency: 'EUR', currencySymbol: '€' },
    { id: 10, name: "Spain", code: "ES", domain: "pantoneclo.es", lang: "es", langId: 7, currency: 'EUR', currencySymbol: '€' },
];

export const getDomainLang = (domain: string) => {
    return domainData.find(item => item.domain == domain)?.lang || 'en';
}

export const getDomaCountryId = (domain: string) => {
    return domainData.find(item => item.domain == domain)?.id || 1;
}

export const getDomaCountryCode = (domain: string) => {
    return domainData.find(item => item.domain == domain)?.code || "BD";
}

export const getDomaCountryCurrencySymbol = (domain: string) => {
    return domainData.find(item => item.domain == domain)?.currencySymbol || "€";
}

export const getDomaLanguageId = (domain: string) => {
    return domainData.find(item => item.domain == domain)?.langId || 1;
}

export const getDomaCountryDomain = (domain: string) => {
    return domainData.find(item => item.domain == domain)?.domain || "pantoneclo.com.bd";
}

export const getLanguageCodeByCountryId = (id: number) => {
    return domainData.find(item => item.id == id)?.lang || 'en';
}

