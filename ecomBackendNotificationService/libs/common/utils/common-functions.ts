import { CourierCodeEnum } from '../enums/courier-code.enum';
import { DeliveryTypeEnum } from '../enums/delivery-type.enum';
import { PaymentMethodEnum } from '../enums/payment-method.enum';
// import {
//   IProductVariantJsonb,
//   IVariantDetails,
// } from '../interfaces/product-variant-jsonb.interface';

import { has, isEmpty } from 'lodash';

export const generateInvoiceNumber = () => {
  const date = new Date();
  const year = date.getFullYear();
  const month = ('0' + (date.getMonth() + 1)).slice(-2); // Adding leading zero if necessary
  const day = ('0' + date.getDate()).slice(-2); // Adding leading zero if necessary
  const hours = ('0' + date.getHours()).slice(-2); // Adding leading zero if necessary
  const minutes = ('0' + date.getMinutes()).slice(-2); // Adding leading zero if necessary
  const seconds = ('0' + date.getSeconds()).slice(-2); // Adding leading zero if necessary
  const milliseconds = ('00' + date.getMilliseconds()).slice(-3); // Adding leading zeros if necessary

  // You can customize the format of the invoice number as per your requirements
  // const invoiceNumber = `${year}${month}${day}${hours}${minutes}${seconds}${milliseconds}`;
  const invoiceNumber = `${minutes}${seconds}${milliseconds}`;

  return invoiceNumber;
};

export const generateProductSlug = () => {
  const date = new Date();
  const year = date.getFullYear();
  const month = ('0' + (date.getMonth() + 1)).slice(-2); // Adding leading zero if necessary
  const day = ('0' + date.getDate()).slice(-2); // Adding leading zero if necessary
  const hours = ('0' + date.getHours()).slice(-2); // Adding leading zero if necessary
  const minutes = ('0' + date.getMinutes()).slice(-2); // Adding leading zero if necessary
  const seconds = ('0' + date.getSeconds()).slice(-2); // Adding leading zero if necessary
  const milliseconds = ('00' + date.getMilliseconds()).slice(-3); // Adding leading zeros if necessary

  // You can customize the format of the invoice number as per your requirements
  const invoiceNumber = `${minutes}${day}${year}${seconds}${milliseconds}`;

  return invoiceNumber;
};
export const createSlug = (name: string) => {
  let slug: string = '';
  const removeSpecial = name.replace(/[^a-zA-Z0-9\s]/g, '');
  slug = removeSpecial.trim().replace(/ /g, '-').toLowerCase();
  return slug;
};

export const isEmptyObject = (obj) => {
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      return false;
    }
  }
  return true;
};

export const sortPriceFromList = (
  variants: any, // IProductVariantJsonb[],
): number[] => {
  const price: number[] = [];
  const priceList: any[] = [];
  const unitPriceList: any[] = [];
  // variants?.forEach((v: IProductVariantJsonb) => {
  //   v.variantDetails?.forEach((vd: IVariantDetails) => {
  //     priceList.push(vd.unitPrice);
  //     unitPriceList.push(vd.discountPrice);
  //   });
  // });

  // Remove duplicates using Set
  // let uniquePrices = Array.from(new Set(priceList));
  // let uniqueDiscountPrices = Array.from(new Set(unitPriceList));

  // Sort the array
  // uniquePrices.sort((a, b) => a - b);
  // uniqueDiscountPrices.sort((a, b) => a - b);
  // price = (uniquePrices?.length>1) ? uniquePrices[0] + ' - ' + uniquePrices[uniquePrices.length - 1]:uniquePrices[0];
  // const unitPrice = (uniquePrices?.length>0) ? :0;
  // const discountPrice = (uniquePrices?.length>0) ? uniquePrices[0]:0;
  // price.push(parseInt(uniquePrices[0]));
  // price.push(parseInt(uniqueDiscountPrices[0]));
  const unitPrice =
    variants &&
      variants.length > 0 &&
      variants[0].variantDetails &&
      variants[0].variantDetails.length > 0
      ? variants[0].variantDetails[0].unitPrice
      : 0;
  const discountPrice =
    variants &&
      variants.length > 0 &&
      variants[0].variantDetails &&
      variants[0].variantDetails.length > 0
      ? variants[0].variantDetails[0].discountPrice
      : 0;
  price.push(unitPrice);
  price.push(discountPrice);
  return price;
};

export const formatDate = (dateString) => {
  const date = new Date(dateString);
  const day = String(date.getDate()).padStart(2, '0');
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const year = date.getFullYear();
  return `${day}-${month}-${year}`;
};

export const setPaymentMethod = (value: string) => {
  let paymentMethod: PaymentMethodEnum = PaymentMethodEnum.COD;
  if (PaymentMethodEnum.COD == value) {
    paymentMethod = PaymentMethodEnum.COD;
  } else if (PaymentMethodEnum.SSLCOMMERZ == value) {
    paymentMethod = PaymentMethodEnum.SSLCOMMERZ;
  } else if (PaymentMethodEnum.STRIPE == value) {
    paymentMethod = PaymentMethodEnum.STRIPE;
  } else if (PaymentMethodEnum.REVOLUT == value) {
    paymentMethod = PaymentMethodEnum.REVOLUT;
  }
  return paymentMethod;
};

export const setDeliveryType = (value: string) => {
  let deliveryType: DeliveryTypeEnum = DeliveryTypeEnum.HOME_DELIVERY;
  if (DeliveryTypeEnum.HOME_DELIVERY == value) {
    deliveryType = DeliveryTypeEnum.HOME_DELIVERY;
  } else if (DeliveryTypeEnum.WAREHOUSE_PICKUP == value) {
    deliveryType = DeliveryTypeEnum.WAREHOUSE_PICKUP;
  }
  return deliveryType;
};

export const getLocalizeValue = (
  languageId?: number,
  locale?: any,
  value?: any,
) => {
  if (!locale || !languageId) return value;

  const _localeTitle =
    !isEmpty(locale) && has(locale, languageId)
      ? locale[languageId]
      : undefined;

  return _localeTitle || value;
};

export const getLocalizeName = (
  languageId?: number,
  locale?: any,
  value?: any,
) => {
  if (!locale || !languageId) return value;

  const _localeTitle =
    !isEmpty(locale) && has(locale, languageId)
      ? locale[languageId]?.name
      : undefined;

  return _localeTitle || value;
};

export const getLocalizeDescription = (
  languageId?: number,
  locale?: any,
  value?: any,
) => {
  if (!locale || !languageId) return value;

  const _localeTitle =
    !isEmpty(locale) && has(locale, languageId)
      ? locale[languageId]?.description
      : undefined;

  return _localeTitle || value;
};

export const getCourierNameByCode = (courierServiceCode) => {
  let codeToName: string = null;
  switch (courierServiceCode) {
    case CourierCodeEnum.WP:
      codeToName = 'WareHouse Pickup';
      break;

    case CourierCodeEnum.HD:
      codeToName = 'Home Delivery';
      break;

    case CourierCodeEnum.GHD:
      codeToName = 'GLS Home Delivery';
      break;

    case CourierCodeEnum.MYGLS:
      codeToName = 'GLS Pickup Point';
      break;

    case CourierCodeEnum.OVERSEAS:
      codeToName = 'Overseas Pickup Point';
      break;

    case CourierCodeEnum.PHD:
      codeToName = 'Packeta Home Delivery';
      break;

    case CourierCodeEnum.PACKETA:
      codeToName = 'Packeta Pickup Point';
      break;

    case CourierCodeEnum.PACTIC:
      codeToName = 'Pactic Pickup Point';
      break;

    case CourierCodeEnum.PACTICHD:
      codeToName = 'Pactic Home Delivery';
      break;

    case CourierCodeEnum.REDX:
      codeToName = 'Redx Home Delivery';
      break;

    default:
      break;
  }

  return codeToName
};
