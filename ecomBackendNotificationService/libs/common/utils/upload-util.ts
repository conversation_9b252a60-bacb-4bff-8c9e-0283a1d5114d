import * as multer from 'multer';
import { BadRequestException } from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';
// import * as fs from 'fs/promises';
import sharp from 'sharp';
import * as fs from 'fs';
import { Express } from 'express';

let uploadDirectory = `/image`;
// let imagePath = '/images/';

export async function uploadImage(
  file: Express.Multer.File,
  folderName?: string,
): Promise<string> {
  if (!file) {
    throw new BadRequestException('File not provided');
  }
  const filename = `${uuidv4()}-${file.originalname.replace(/\s+/g, '_')}`;
  // const filename = `${Date.now()}_${file.originalname.replace(/\s+/g, '_')}`;
  const path = folderName
    ? `${uploadDirectory}/${folderName}/${filename}`
    : `${uploadDirectory}/${filename}`;

  fs.writeFileSync(`.${path}`, file.buffer);

  //  const fileUrl = `${process.env.BASE_URL || 'http://localhost:3000'}/uploads/${fileName}`;

  return path;
}
export async function uploadImage1(file: string): Promise<string> {
  if (!file) {
    throw new BadRequestException('File not provided');
  }

  const path = `${uploadDirectory}/${file}`;

  fs.writeFileSync(`.${uploadDirectory}`, file);

  //  const fileUrl = `${process.env.BASE_URL || 'http://localhost:3000'}/uploads/${fileName}`;

  return path;
}

export const multerOptions = {
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 1024 * 1024 * 5,
  },
  fileFilter: (req: any, file: Express.Multer.File, cb: any) => {
    const allowedMimes = ['image/jpeg', 'image/png'];

    if (allowedMimes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new BadRequestException('Invalid file type'));
    }
  },
};

export async function resizeAndConvert(
  image: Express.Multer.File,
  width: number,
  height: number
): Promise<Buffer> {
  let resizedImage = image.buffer;
  let targetSize = 200 * 1024;
  if(width && height){
    // Resize image
    resizedImage = await sharp(image.buffer)
      .resize({ width: width, height: height, fit:'inside', withoutEnlargement: true })
      // .resize({ width: 150, height: 100 })
      .toBuffer();
    }
  
    // Check file size
    // if (resizedImage.length > targetSize) {
    //   // 200KB in bytes
    //   // const resizedImage200KB = await sharp(resizedImage)
    //   //   .resize({ fit: 'inside', withoutEnlargement: true })
    //   //   .toBuffer();
    //   const resizedImage200KB = await sharp(resizedImage)
    //   .webp({ quality: 80 }) // Adjust quality as needed
    //   .toBuffer();
    //   if (resizedImage200KB.length > 200 * 1024) {
    //     throw new Error('Image cannot be resized to fit within 200KB');
    //   }
      return resizedImage;//sharp(resizedImage200KB).toFormat('webp').toBuffer();
    // }else{
    //   const webpImage = await sharp(resizedImage).toFormat('webp').toBuffer();
    //   return webpImage;
    // }

}

export async function saveImageLocally(imageBuffer: Buffer): Promise<string> {
  // Convert to webp
  const webpImage = await sharp(imageBuffer).toFormat('webp').toBuffer();

  // Save the image

  const filename = `image_${Date.now()}.webp`;
  fs.writeFileSync(filename, webpImage);

  // In a real application, you would typically save it to a storage service like AWS S3
  // and return the URL of the uploaded image instead of the filename.
  return filename;
}
