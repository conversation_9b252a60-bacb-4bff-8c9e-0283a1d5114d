const _languagesOptionArr = [
  {
    id: 1,
    name: 'English',
    code: 'en',
  },
  {
    id: 2,
    name: 'Bengali',
    code: 'bn',
  },
  {
    id: 3,
    name: 'German',
    code: 'de',
  },
  {
    id: 4,
    name: 'Bulgarian',
    code: 'bg',
  },
  {
    id: 5,
    name: 'Czech',
    code: 'cz',
  },
  {
    id: 6,
    name: 'Greek',
    code: 'el',
  },
  {
    id: 7,
    name: 'Spanish',
    code: 'es',
  },
  {
    id: 8,
    name: 'Croatian',
    code: 'hr',
  },
  {
    id: 9,
    name: 'Hungarian',
    code: 'hu',
  },
  {
    id: 10,
    name: 'Italian',
    code: 'it',
  },
  {
    id: 11,
    name: 'Lithuanian',
    code: 'lt',
  },
  {
    id: 12,
    name: 'Polish',
    code: 'pl',
  },
  {
    id: 13,
    name: 'Portuguese',
    code: 'pt',
  },
  {
    id: 14,
    name: 'Romanian',
    code: 'ro',
  },
  {
    id: 15,
    name: 'Slovak',
    code: 'sk',
  },
  {
    id: 16,
    name: 'Slovenian',
    code: 'sl',
  },
  {
    id: 17,
    name: 'Austrian',
    code: 'at',
  },
];

export const getLanguageCodeById = (id: number) =>
  _languagesOptionArr.find((item) => item.id == id)?.code;
