type TranslationKey = {
    key: string;
    translationValues: {
        locale: string;
        value: string;
    }[];
};

export function mapTranslations(keys: TranslationKey[]): Record<string, Record<string, string>> {
    const translations: Record<string, Record<string, string>> = {};

    for (const key of keys) {
        for (const val of key.translationValues) {
            if (!translations[val.locale]) {
                translations[val.locale] = {};
            }
            translations[val.locale][key.key] = val.value;
        }
    }

    return translations;
}
