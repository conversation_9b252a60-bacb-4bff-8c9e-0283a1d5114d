import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { ApiResponse } from '../utils/api-response';

export interface Response<T> {
  data: T;
}

@Injectable()
export class TransformInterceptor<T>
  implements NestInterceptor<T, Response<T>>
{
  intercept(
    context: ExecutionContext,
    next: CallHandler,
  ): Observable<Response<T>> {
    return next
      .handle()
      .pipe(
        map(
          (data) =>
            new ApiResponse(
              data?.data
                ? Array.isArray(data?.data) && data?.data?.length == 0
                  ? false
                  : true
                : false,
              data?.data,
              data?.msg,
              context.switchToHttp().getResponse().statusCode,
              data?.meta,
            ),
        ),
      );
  }
}
