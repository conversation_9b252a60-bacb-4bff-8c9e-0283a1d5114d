import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  Logger,
  HttpStatus,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { ApiResponse } from '../utils/api-response';
import { DataSource, TypeORMError } from 'typeorm';

@Catch(TypeORMError)
export class TypeORMExceptionFilter implements ExceptionFilter {
  constructor(private readonly dataSource: DataSource) {}

  private logger = new Logger(TypeORMExceptionFilter.name);
  async catch(exception: TypeORMError, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();
    const statusCode = HttpStatus.INTERNAL_SERVER_ERROR;

    const logFormat = `${new Date().toISOString()} - ${
      process.env.NODE_ENV
    } - ${request.method} ${request.url} ${statusCode}`;

    this.logger.error(
      `${logFormat} ${JSON.stringify(exception.message)} ${JSON.stringify(
        exception.stack,
      )}`,
    );

    // Optional: Check and destroy connection if necessary
    if (this.dataSource.isInitialized) {
      await this.dataSource.destroy();
      console.log('Database connection cleaned up');
    }

    const errorResponse = new ApiResponse(
      false,
      null,
      'Something went wrong, Please try again later',
      statusCode,
    );

    response.status(statusCode).json(errorResponse);
  }
}
