<!doctype html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>{{t "Invoice"}}</title>
  <style>
    /* Add your CSS styles here */
    /* @import url('https://fonts.googleapis.com/css2?family=Inter:wght@200;300;400;500;600;700;800&display=swap'); */
    @import url('https://fonts.googleapis.com/css?family=Lato');

    * {
      font-family: 'Lato', sans-serif;
    }

    body {
      /* font-family: 'Inter', sans-serif; */
      line-height: 1.6;
      /* font-weight: 500; */
      font-size: 14px;
      font-style: normal !important;
    }

    .invoice {
      padding: 20px;
      border: 1px solid #ccc;
      border-radius: 5px;
      padding: 5px 16px;
    }

    .invoice-header {
      text-align: center;
      background-color: #000;
      margin: 0;
      padding: 10px;
    }

    .order-title {
      color: #1ca9e1;
      text-transform: uppercase;
      margin-bottom: 3px;
      font-size: 18px;
      font-weight: 600;
      /* margin-top: 20px; */
    }

    /* .invoice-no{
            text-decoration: underline;
        } */

    .order-summary {
      text-transform: capitalize;
      font-size: 20px;
      /* font-weight: 600; */
    }

    .order-date {
      font-size: 12px;
      margin: 6px 0 24px 0;
    }

    .invoice-items {
      border-collapse: collapse;
      width: 100%;
    }

    .order-text {
      line-height: 1.8;
    }

    .invoice-items thead,
    .invoice-items thead tr,
    .invoice-items thead tr th {
      background-color: #e7e7e8 !important;
      font-size: 14px;
      padding-bottom: 10px;
      border-bottom: 1px solid #e7e7e8;
    }

    .invoice-items-table-header {
      background-color: #e7e7e8;
      font-weight: 600;
      display: table-cell;
    }

    .invoice-items tbody .table-border td {
      padding: 8px;
      border-bottom: 1px solid #e7e7e8;
    }

    .invoice-items tbody .table-total td {
      padding-top: 1.5rem;
    }

    .m-15 {
      margin: 15px;
    }

    .mt-15 {
      margin-top: 15px;
    }

    .invoice-items {
      font-size: 13px;
    }

    .text-left {
      text-align: left;
    }

    /* .................................. */
    table {
      width: 100%;
      border-collapse: collapse;
    }

    .header-title {
      font-size: 18px;
      font-weight: bold;
      color: #007bff;
    }

    .order-summary-title {
      font-size: 20px;
      font-weight: bold;
      color: #007bff;
    }

    th,
    td {
      /* border: 1px solid #ddd; */
      padding: 10px;
      text-align: left;
    }

    th {
      background-color: #f0f0f0;
    }

    .total-section td {
      padding: 5px 10px;
      text-align: right;
      white-space: nowrap;
    }

    /* tr:nth-child(even) {background-color: #f2f2f2;} */

    /* .................................. */
  </style>
</head>

<body>
  <div class="invoice-header">
    <img src="https://pantoneclosbucket.s3.eu-central-1.amazonaws.com/pantoneclo-img/logo-white.png" width="250"
      alt="pantoneclo logo" />
  </div>
  <div class="invoice">
    <table width="100%">
      <tr>
        <td>
          <div class="order-title">{{billingName}}, {{t "thanks"}}</div>
          <div class="order-text">
            {{t "orderReceived"}}
            <div>{{t "orderProcessing"}}</div>
          </div>
          <br>
          <div class="order-title">{{t "orderInformation"}}</div>
          <div>
            {{t "orderNumber"}}: <span class="invoice-no"> {{invoiceNo}}</span>
          </div>
          <div>
            {{t "orderDate"}}: <span class="invoice-no"> {{orderDate}}</span>
          </div>
        </td>
      </tr>
    </table>


    <!-- <div class="m-15">
            <button class="btn-track"> Track your order </button>
        </div> -->
    <!-- <div class="order-title mt-15">{{t "summary"}}</div> -->

    <!-- ----------------------------------------------- -->
    <table width="100%">
      <!-- Billing and Shipping Section -->
      <tbody>
        <tr>
          <td>
            <div class="order-title">{{t "billingAddress"}}</div>
            <div>
              {{billingName}}<br>{{billingAddress}}<br>{{billingEmail}}<br>{{billingPhone}}
            </div>
          </td>
          <td>
            {{#if shippingEmail}}
            <div class="order-title">{{t "shippingAddress"}}</div>
            <div>{{shippingName}}<br>{{shippingAddress}}<br>{{shippingEmail}}<br>{{shippingPhone}}</div>
            {{/if}}


          </td>
        </tr>

      </tbody>
    </table>


    <table width="100%">
      <!-- Order Summary Table -->
      <thead>
        <tr>
          <th style="width: 20px;">SN.</th>
          <th style="width: 50px;"></th>
          <th>{{t "productName"}}</th> <!--Description-->
          <th>{{t "total"}}</th>
        </tr>
      </thead>

      <tbody>
        {{#each items}}
        <tr style="border-bottom: 1px solid #e7e7e8;">
          <td>{{this.sn}}</td>
          <td>
            {{#if (eq this.name "COD")}}
            {{else if (eq this.name "Shipping")}}
            {{else}}
            {{#if this.selectedProductImageWeb}}
            <img src="{{this.selectedProductImageWeb}}" alt="{{this.name}}" width="50" style="vertical-align: middle;">
            {{/if}}
            {{/if}}
          </td>
          <td>
            {{#if (eq this.name "COD")}}
            <b>{{t "cashOnDelivery"}}</b>
            {{else if (eq this.name "Shipping")}}
            <b>{{t "shipping"}}</b>
            {{else if (eq this.name "Discount")}}
            <b>{{t "discount"}}</b><br />
            <small>
              <b>{{t "qty"}}:</b> {{this.qty}},
              <b>{{t "unitPrice"}}:</b> {{currencySymbol}} {{this.unitPrice}},
              <b>{{t "onlyVat"}}:</b> {{currencySymbol}} {{this.unitPriceTax}}
            </small>

            {{else}}
            <b>{{this.name}}</b><br />
            <small>
              {{#if this.size}} {{this.size}} {{#if this.color}} -
              {{this.color}} {{/if}} {{/if}}
            </small><br />
            <small>{{this.sku}}</small><br />
            <small>
              <b>{{t "qty"}}:</b> {{this.qty}},
              <b>{{t "unitPrice"}}:</b> {{currencySymbol}} {{this.unitPrice}},
              <b>{{t "onlyVat"}}:</b> {{currencySymbol}} {{this.unitPriceTax}}
            </small>

            {{/if}}
          </td>
          <td style="white-space: nowrap;">{{currencySymbol}} {{this.total}}</td>
        </tr>
        {{/each}}
      </tbody>
      <tfoot>
        <!-- Order Total Section -->
        <tr class="total-section">
          <td colspan="3" align="right">{{t "subTotal"}}</td>
          <td style="text-align: left;">{{currencySymbol}}&nbsp;{{totalAmount}}</td>
        </tr>
        <tr class="total-section">
          <td colspan="3" align="right">{{t "onlyVat"}}</td>
          <td style="text-align: left;">{{currencySymbol}}&nbsp;{{vatTax}}</td>
        </tr>
        <tr class="total-section" style="font-weight: bold;">
          <td colspan="3" align="right">{{t "grandTotal"}}</td>
          <td style="text-align: left;">{{currencySymbol}}&nbsp;{{totalAmount}}</td>
        </tr>
      </tfoot>
    </table>
    <!-- ----------------------------------------------- -->
    <div style="margin: 10px 0">{{t "thanksForOrder"}}</div>
    <!-- Replace the social media links section in your invoice.html with this: -->
    <div style="padding-top: 30px">
      <div style="
            text-align: center;
            font-size: 30px;
            color: #999999;
            font-weight: 600;
          ">
        {{t "followUs"}}
      </div>
      <div style="padding-top: 10px;">
        <table>
          <tr align="center">
            <td style="width: 33%;">
            </td>
            <td style="width: 33%;">
              <table>
                <tbody>
                  <tr>
                    <td>
                      <a href="{{getSocialLink 'facebook'}}" target="_blank"
                        style="margin-right: 10px;text-decoration: none">
                        <img src="https://pantoneclosbucket.s3.eu-central-1.amazonaws.com/pantoneclo-img/fb.png"
                          width="30" alt="pantoneclo logo" />
                      </a>
                    </td>
                    <td>
                      <a href="{{getSocialLink 'instagram'}}" target="_blank"
                        style="margin-right: 10px;text-decoration: none">
                        <img src="https://pantoneclosbucket.s3.eu-central-1.amazonaws.com/pantoneclo-img/insta.png"
                          width="30" alt="pantoneclo logo" />
                      </a>
                    </td>
                    <td>
                      <a href="{{getSocialLink 'linkedin'}}" target="_blank"
                        style="margin-right: 10px;text-decoration: none">
                        <img src="https://pantoneclosbucket.s3.eu-central-1.amazonaws.com/pantoneclo-img/in.png"
                          width="30" alt="pantoneclo logo" />
                      </a>
                    </td>
                    <td>
                      <a href="{{getSocialLink 'twitter'}}" target="_blank"
                        style="margin-right: 10px;text-decoration: none">
                        <img src="https://pantoneclosbucket.s3.eu-central-1.amazonaws.com/pantoneclo-img/x.png"
                          width="30" alt="pantoneclo logo" />
                      </a>
                    </td>
                    <td>
                      <a href="{{getSocialLink 'youtube' }}" target="_blank"
                        style="margin-right: 10px;text-decoration: none">
                        <img src="https://pantoneclosbucket.s3.eu-central-1.amazonaws.com/pantoneclo-img/youtube.png"
                          width="30" alt="pantoneclo logo" />
                      </a>
                    </td>
                    <td>
                      <a href="{{getSocialLink 'tiktok' }}" target="_blank" style="text-decoration: none">
                        <img src="https://pantoneclosbucket.s3.eu-central-1.amazonaws.com/pantoneclo-img/tiktok.png"
                          width="30" alt="pantoneclo logo" />
                      </a>
                    </td>
                  </tr>
                </tbody>
              </table>
            </td>
            <td style="width: 33%;">
            </td>
          </tr>
        </table>
      </div>

      <!-- Rest of the template remains the same -->
    </div>
  </div>
</body>

</html>