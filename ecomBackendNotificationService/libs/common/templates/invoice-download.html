<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>{{t "Invoice"}}</title>
    <style>
      /* Add your CSS styles here */
      @import url('https://fonts.googleapis.com/css2?family=Inter:wght@200;300;400;500;600;700;800&display=swap');
      body {
        font-family: 'Inter', sans-serif;
        /* font-family: Arial, sans-serif; */
        line-height: 1.6;
        font-weight: 500;
        font-size: 25px;
      }
      .invoice {
        padding: 30px;
        border: 1px solid #ccc;
        border-radius: 5px;
        text-align: center;
      }
      .order-title {
        color: #1ca9e1;
        text-transform: uppercase;
        margin-bottom: 3px;
        font-size: 22px;
        font-weight: 600;
      }
      .invoice-no {
        text-decoration: underline;
      }

      .order-summary {
        text-transform: capitalize;
        font-size: 20px;
        font-weight: 600;
      }
      .order-date {
        font-size: 12px;
        margin: 6px 0 24px 0;
      }
      .invoice-items,
      .invoice-table {
        border-collapse: collapse;
        width: 100%;
      }
      .order-text {
        line-height: 1.8;
      }
      .invoice-items thead,
      .invoice-items thead tr,
      .invoice-items thead tr th {
        background-color: #e7e7e8 !important;
        font-size: 14px;
        padding-bottom: 10px;
        border-bottom: 1px solid #e7e7e8;
      }
      .invoice-items-table-header {
        background-color: #e7e7e8;
        font-weight: 600;
        display: table-cell;
      }
      .invoice-items tbody .table-border td {
        padding: 8px;
        border-bottom: 1px solid #e7e7e8;
      }

      .invoice-items tbody .table-total td {
        padding-top: 1.5rem;
        text-align: left;
      }
      .m-15 {
        margin: 15px;
      }

      .mt-15 {
        margin-top: 15px;
      }

      .invoice-items {
        font-size: 13px;
      }
      .text-left {
        text-align: left;
      }
    </style>
  </head>
  <body>
    <div class="invoice" style="font-size: 40px">
      <table
        class="invoice-table"
        style="border-bottom: 2px solid #000; margin-bottom: 50px"
        border="0"
      >
        <tbody>
          <tr>
            <td
              style="
                width: 50%;
                text-align: left;
                padding-bottom: 15px;
                font-size: 60px;
                font-weight: 700;
              "
            >
              PANTONECLO
            </td>
            <td
              style="
                width: 50%;
                text-align: right;
                padding-bottom: 15px;
                font-size: 20px;
                border-left: 3px solid;
              "
            >
              <div style="font-size: 50px; font-weight: 600">
                {{t "Invoice"}}
              </div>
              <div>
                <div>{{t "invoiceId"}}: {{invoiceNo}}</div>
                <div>{{t "orderDate"}}: {{orderDate}}</div>
                <div>{{t "paymentMethod"}}: {{paymentMethod}}</div>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
      <table
        class="invoice-table"
        style="margin-bottom: 50px; font-size: 25px; background-color: #c9c9c9"
        border="0"
      >
        <tbody>
          <tr>
            <td
              style="
                width: 50%;
                text-align: left;
                padding-left: 35px;
                padding-top: 35px;
                padding-bottom: 35px;
              "
            >
              <b>{{t "shippingAddress"}}</b>
              <div>{{billingName}}</div>
              <div>{{billingAddress}}</div>
              <div>{{t "email"}}: {{billingEmail}}</div>
              <div>{{t "phone"}}: {{billingPhone}}</div>
            </td>
            <td
              style="
                width: 50%;
                text-align: left;
                padding-top: 35px;
                padding-bottom: 35px;
              "
            >
              {{#if shippingName}}
              <b>{{t "billingAddress"}}</b>
              <div>{{shippingName}}</div>
              <div>{{shippingAddress}}</div>
              <div>{{t "email"}}: {{shippingEmail}}</div>
              <div>{{t "phone"}}: {{shippingPhone}}</div>

              {{/if}}
            </td>
          </tr>
        </tbody>
      </table>
      <table class="invoice-items" border="0" style="font-size: 25px">
        <thead>
          <tr>
            <th
              style="
                font-size: 25px;
                padding: 10px 0;
                background: #e7e7e8 !important;
              "
            >
              {{t "sn"}}.
            </th>
            <th
              style="
                font-size: 25px;
                padding: 10px 0;
                background: #e7e7e8 !important;
              "
            >
              {{t "productName"}}
            </th>
            <th
              style="
                font-size: 25px;
                padding: 10px 0;
                background: #e7e7e8 !important;
              "
            >
              {{t "qty"}}
            </th>
            <th
              style="
                font-size: 25px;
                padding: 10px 0;
                background: #e7e7e8 !important;
              "
            >
              {{t "unitPrice"}}
            </th>
            <th
              style="
                font-size: 25px;
                padding: 10px 0;
                background: #e7e7e8 !important;
              "
            >
              {{t "vatTax"}}
            </th>
            <th
              style="
                font-size: 25px;
                padding: 10px 0;
                background: #e7e7e8 !important;
              "
            >
              {{t "total"}}
            </th>
          </tr>
        </thead>
        <tbody>
          {{#each items}}
          <tr class="table-border">
            <!-- <td>{{@index}}</td> -->
            <td>{{this.sn}}</td>
            <td style="text-align: left; max-width: 800px">
              <!-- <div>{{this.name}}</div> -->
              <div>
                {{#if (eq this.name "COD")}} {{t "cashOnDelivery"}} {{else if
                (eq this.name "Shipping")}} {{t "shipping"}} {{else}}
                {{this.name}} {{/if}}
              </div>
              <div>
                {{#if this.size}} {{this.size}} {{#if this.color}} -
                {{this.color}} {{/if}} {{/if}}
              </div>
              <div>{{this.sku}}</div>
            </td>
            <td>{{this.qty}}</td>
            <td>{{currencySymbol}} {{this.unitPrice}}</td>
            <td>{{currencySymbol}} {{this.unitPriceTax}}</td>
            <td>{{currencySymbol}} {{this.total}}</td>
          </tr>
          {{/each}}
          <!-- <tr class="table-border">
                    <td>1</td>
                    <td>product 1</td>
                    <td>Home Delivery</td>
                    <td>01</td>
                    <td>$10.25</td>
                    <td>$2.26</td>
                    <td>$12.51</td>
                </tr> -->
          <tr class="table-total">
            <td colspan="3"></td>
            <td colspan="2" style="padding-right: 20px">{{t "subTotal"}}</td>
            <td style="text-align: center">
              {{currencySymbol}} {{totalAmount}}
            </td>
          </tr>
          <tr class="text-left">
            <td colspan="3"></td>
            <td colspan="2" style="padding-right: 20px">{{t "Vat/Tax/Gst"}}</td>
            <td style="text-align: center">{{currencySymbol}} {{vatTax}}</td>
          </tr>
          <tr class="text-left">
            <td colspan="3"></td>
            <td colspan="2" style="padding-right: 20px">{{t "grandTotal"}}</td>
            <td style="text-align: center">
              {{currencySymbol}} {{totalAmount}}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </body>
</html>
