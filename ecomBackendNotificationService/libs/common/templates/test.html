<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice</title>
    <style>
        /* Add your CSS styles here */
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
        }
        .invoice {
            padding: 20px;
            border: 1px solid #ccc;
            border-radius: 5px;
        }
        .invoice-header {
            text-align: center;
            margin-bottom: 20px;
        }
        .invoice-details {
            margin-bottom: 20px;
        }
        .invoice-details p {
            margin: 5px 0;
        }
        .invoice-items {
            border-collapse: collapse;
            width: 100%;
        }
        .invoice-items th, .invoice-items td {
            border: 1px solid #ddd;
            padding: 8px;
        }
        .invoice-items th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <div class="invoice">
        <div class="invoice-header">
            <h2>Invoice</h2>
        </div>
        <div class="invoice-details">
            <p>Order Number: {{orderNumber}}</p>
            <p>Invoice Number: {{invoiceNumber}}</p>
            <p>Customer Name: {{customerName}}</p>
            <!-- Add more customer information fields here if needed -->
        </div>
        <!-- <table class="invoice-items">
            <thead>
                <tr>
                    <th>Item</th>
                    <th>Quantity</th>
                    <th>Price</th>
                    <th>Total</th>
                </tr>
            </thead>
            <tbody>
                {{orderDetails}}
            </tbody>
        </table> -->
    </div>
</body>
</html>
