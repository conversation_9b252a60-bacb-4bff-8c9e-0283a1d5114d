import {
  ValidatorConstraint,
  ValidatorConstraintInterface,
} from 'class-validator';
import { PasswordUtil } from '../utils/password.util';

@ValidatorConstraint({ name: 'checkPasswordStrength', async: false })
export class PasswordStrengthConstraint
  implements ValidatorConstraintInterface
{
  validate(value: string) {
    return PasswordUtil.checkPasswordStrength(value);
  }
  defaultMessage() {
    return 'Password is too weak';
  }
}
