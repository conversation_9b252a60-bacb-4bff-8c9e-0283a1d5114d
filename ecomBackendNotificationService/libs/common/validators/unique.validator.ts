import { EntityClassOrSchema } from '@nestjs/typeorm/dist/interfaces/entity-class-or-schema.type';
import { EntityManager, DataSource } from 'typeorm';
import {
  ValidationArguments,
  ValidatorConstraint,
  ValidatorConstraintInterface,
} from 'class-validator';
import { InjectDataSource } from '@nestjs/typeorm';

export interface IsUniqueOptions {
  entity: EntityClassOrSchema;
}

@ValidatorConstraint({ name: 'unique', async: true })
export class UniqueConstraint implements ValidatorConstraintInterface {
  private entityManager: EntityManager;
  constructor(
    @InjectDataSource()
    private readonly dataSource: DataSource,
  ) {
    this.entityManager = this.dataSource.createEntityManager();
  }

  async validate(value: any, args: ValidationArguments): Promise<boolean> {
    const data = this.entityManager
      .getRepository(args.constraints[0])
      .findOneBy({ [args.property]: value });
    return !data;
  }

  defaultMessage(args: ValidationArguments): string {
    return `The value for '${args.property}' already exists.`;
  }
}
