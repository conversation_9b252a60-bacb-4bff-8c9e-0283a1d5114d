import { EntityClassOrSchema } from '@nestjs/typeorm/dist/interfaces/entity-class-or-schema.type';
import { ValidationOptions, registerDecorator } from 'class-validator';
import { UniqueConstraint } from '../validators/unique.validator';

export const IsUnique = (
  entity: string | EntityClassOrSchema,
  validationOptions?: ValidationOptions,
) => {
  return (object: unknown, propertyName: string) => {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [entity],
      validator: UniqueConstraint,
    });
  };
};
