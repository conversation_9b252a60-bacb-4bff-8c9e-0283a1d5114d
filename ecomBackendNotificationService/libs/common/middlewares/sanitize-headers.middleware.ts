import {
  Injectable,
  NestMiddleware,
} from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';

@Injectable()
export class SanitizeHeadersMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction) {
    for (const header in req.headers) {
      if (typeof req.headers[header] === 'string') {
        req.headers[header] = req.headers[header].toString().replace(/[^\x20-\x7E]/g, '');
      }
    }
    next();
  }
}