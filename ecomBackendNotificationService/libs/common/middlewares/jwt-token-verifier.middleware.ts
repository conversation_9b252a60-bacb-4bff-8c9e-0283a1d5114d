import {
  HttpException,
  HttpStatus,
  Injectable,
  NestMiddleware,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { JwtUtil } from '../utils/jwt-util';
import { Request, Response, NextFunction } from 'express';
import { TokenManagementService } from '../providers/token-management/token-management.service';
import { JwtPayload } from 'src/auth/dto/jwt-payload.interface';
import { User } from 'src/user/entity/user.entity';
// import { JwtPayload } from 'src/application/auth/dto/jwt-payload.interface';

interface CustomRequest<T = any> extends Request {
  body: T;
  jwtPayload?: JwtPayload;
  user: User;
  method: string
}

@Injectable()
export class JwtTokenVerifierMiddleware implements NestMiddleware {
  constructor(
    private readonly jwtService: JwtService,
    // private tokenManagementService: TokenManagementService,
  ) { }

  use(request: CustomRequest<{ id?: number; createdBy?: number; updatedBy?: number }>, response: Response, next: NextFunction) {
    const token = JwtUtil.extractTokenFromHeader(request) as string;

    // if (this.tokenManagementService.isTokenBlacklisted(token)) {
    //   throw new HttpException('Invalid JWT token', HttpStatus.UNAUTHORIZED);
    // }

    if (!token) {
      throw new HttpException('Missing JWT token', HttpStatus.UNAUTHORIZED);
    }

    try {
      const payload: JwtPayload = this.jwtService.verify(token, {
        secret: 'secret@1234',
      });
      request.jwtPayload = payload; // Add the JWT payload to the request object

      request.jwtPayload;

      if (!request.body.id && request.method === 'POST') {
        request.body.createdBy = request.jwtPayload?.sub;
        // request.body.createdAt = Date.now();
      } else if (
        request.body.id &&
        (request.method === 'POST' || request.method === 'PUT')
      ) {
        request.body.updatedBy = request.jwtPayload?.sub;
        // request.body.updatedAt = Date.now();
      }

      return next();
    } catch (error) {
      throw new HttpException('Invalid JWT token', HttpStatus.UNAUTHORIZED);
    }
  }
}
