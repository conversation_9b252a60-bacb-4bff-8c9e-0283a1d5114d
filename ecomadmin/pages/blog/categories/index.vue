<template>
	<SharedBaseBreadcrumb :title="page.title" :breadcrumbs="breadcrumbs"></SharedBaseBreadcrumb>
	<v-row>
		<v-col cols="4">
			<SharedUiParentCardSolid :title="category.id ? 'Edit ' + category.name : 'Add New Category'">
				<v-form v-model="form" @submit.prevent="onSubmit" ref="categoryForm">
					<FormElementsCommonFieldContainer label="Image" :required="true">
						<FormElementsImageViewerSingle :image-id="category.imageGalleryId" :multiple="false"
							:image-rules="imageRules" :required-width="452" :required-height="450" :max-size="200000"
							image-model="category" @selected-image-id="handleSelectedImage" />
					</FormElementsCommonFieldContainer>
					<!-- <FormElementsCommonFieldContainer label="Banner Image" :required="true">
						<FormElementsImageViewerSingle :image-id="category.bannerImageId" :multiple="false"
							:image-rules="imageBannerRules" :required-width="1800" :required-height="250" :max-size="200000"
							image-model="category" @selected-image-id="handleSelectedBannerImage" />
					</FormElementsCommonFieldContainer> -->

					<FormElementsCommonFieldContainer label="Name" :required="true">
						<v-text-field v-model="category.name" :rules="rules" placeholder="Enter Name"
							density="compact"></v-text-field>
					</FormElementsCommonFieldContainer>

					<FormElementsCommonFieldContainer label="Parent Category">
						<v-select :items="parentCategories" item-title="name" item-value="id" v-model="category.parentId"
							placeholder="Select Parent Category" density="compact"></v-select>
					</FormElementsCommonFieldContainer>

					<FormElementsCommonFieldContainer label="Description" :required="false">
						<FormElementsEditor v-model="category.description" />
					</FormElementsCommonFieldContainer>

					<FormElementsCommonFieldContainer label="Sort Order">
						<v-text-field v-model="category.sortOrder" placeholder="Sort Order" density="compact"></v-text-field>
					</FormElementsCommonFieldContainer>

					<v-btn :disabled="!form" :loading="loading" color="primary" variant="flat" class="px-6" type="submit">
						{{
							category.id ?
								'Update' : 'Add New'
						}} Category
					</v-btn>

					<v-btn color="error" variant="flat" class="px-6 ms-2" @click="resetForm">Reset</v-btn>
				</v-form>
			</SharedUiParentCardSolid>
		</v-col>
		<v-col cols="8">
			<SharedUiParentCardSolid>
				<div class="d-flex justify-space-between align-center">
					<h2 class="text-h5 font-weight-bold">Categories</h2>
					<div class="w-50">
						<!-- <v-text-field append-inner-icon="mdi-magnify" density="compact" label="Search category" variant="outlined" 
              hide-details single-line @onChange:append-inner="onClick" v-model="options.search" clearable 
              @click:clear="clearSearch"></v-text-field> -->
						<v-text-field v-model="name" class="ma-1" density="compact" placeholder="Search category"
							hide-details></v-text-field>
					</div>
				</div>

				<v-data-table-server v-model:page="options.page" v-model:items-per-page="options.itemsPerPage"
					:headers="headers" :items="categories" :items-length="store.pagination.itemCount" :search="options.search"
					:loading="loading" item-value="name" :items-per-page-options="[10, 25, 50, 100]"
					@update:options="fetchCategories">
					<template v-slot:thead>
						<tr>
							<td colspan="2">
								<!-- <v-text-field v-model="name" class="ma-1" density="compact" placeholder="Search category" hide-details></v-text-field> -->
							</td>
						</tr>
					</template>
					<template v-slot:item.imageGalleryId="{ item }">
						<div v-if="item.imageGallery">
							<v-img :src="item.imageGallery.imageUrl" width="60" />
						</div>
					</template>
					<template v-slot:item.Order="{ item }">
						<span v-html="item?.sortOrder"></span>
					</template>
					<template v-slot:item.slug="{ item }">
						<span v-html="item?.slug"></span>
					</template>
					<template v-slot:item.parentId="{ item }">
						{{ item?.parent?.name }}
					</template>
					<template v-slot:item.status="{ item }">
						<v-chip :color="item?.isActive ? 'success' : 'error'" variant="flat" size="x-small">
							{{ item?.isActive ? 'Active' : 'Inactive' }}
						</v-chip>
					</template>
					<template v-slot:item.related-blog="{ item }">
						<!-- <v-btn icon="mdi-tag-outline" size="small" variant="text" color="primary"
							@click="bundleRelatedProductHandle(item)">
						</v-btn> -->
					</template>
					<template v-slot:item.action="{ item }">

						<div class="text-end">
							<v-menu>
								<template v-slot:activator="{ props }">
									<v-btn class="me-2" variant="tonal" size="x-small" icon="mdi-dots-vertical" v-bind="props"></v-btn>
								</template>

								<v-list>
									<v-list-item>
										<v-btn icon="mdi-pencil" color="warning" class="me-2" variant="flat" size="x-small"
											@click="editPayloadItem(item, category)">
										</v-btn>
									</v-list-item>
									<v-list-item>
										<v-btn icon="mdi-translate" color="info" class="me-2" variant="flat" size="x-small"
											@click="localizeCategory(item)">
										</v-btn>
									</v-list-item>
									<v-list-item>
										<v-btn icon="mdi-delete" color="error" class="me-2" variant="flat" size="x-small"
											@click="confirmDialog = true; itemToDelete = item.id">
										</v-btn>
									</v-list-item>

								</v-list>
							</v-menu>
						</div>
					</template>

				</v-data-table-server>

			</SharedUiParentCardSolid>
		</v-col>
	</v-row>
	<ConformationModal v-model:dialog="confirmDialog" @confirm-delete="handleDeleteConfirmation" />
	<BlogLocalizeCategoryLocalize ref="localizationDialog" />
	<!-- <ProductBundleProductRelatedTagCategoryForm ref="bundleProductRelatedTagCategoryFormRef" @reset="fetchProductList" /> -->

</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { useProductCategoriesStore } from "~/stores/products/categories";
import { editPayloadItem } from "~/utils/helpers/functions";
import { PencilIcon, TrashIcon, LanguageIcon } from "vue-tabler-icons";
import ConformationModal from "~/components/modals/ConformationModal.vue";
import { useRolePermissionsStore } from '~/stores/administration/permissions';
import { useProductsStore } from '~/stores/products';
import { useBlogCategoriesStore } from '~/stores/blog/categories';

const store = useBlogCategoriesStore();
const permissionStore = useRolePermissionsStore();
const productStore = useProductsStore();
const page = ref({ title: "Categories" });

const breadcrumbs = ref([
	{
		text: "Blog",
		disabled: false,
		to: "/blog/posts",
	},
	{
		text: "Category list",
		disabled: false,
		to: "/blog/categories",
	},
]);
const options = ref({
	page: 1,
	itemsPerPage: 20,
	sortBy: ['name'],
	sortDesc: [false],
	search: '',
});

const snackbar = useSnackbar();
const categories = ref([]);
const parentCategories = ref([]);
const name = ref('');
const headers = ref([
	{ title: 'Image', align: 'start', sortable: false, key: 'imageGalleryId' },
	{ title: 'Name', align: 'start', sortable: false, key: 'name' },
	{ title: 'Slug', align: 'start', sortable: false, key: 'slug' },
	{ title: 'Order', key: 'Order', sortable: false, align: 'start' },
	{ title: 'Status', key: 'status', sortable: false, align: 'start' },
	{ title: 'Parent Category', key: 'parentId', sortable: false, align: 'start' },
	{ title: "Related Blog", key: "related-blog", sortable: false, align: "start" },
	{ title: 'Action', key: 'action', align: 'center', sortable: false },
])

const bundleProductRelatedTagCategoryFormRef = ref(null);

const localizationDialog = ref(null);

const localizeCategory = (item: any) => {
	localizationDialog.value?.show(item);
};

function onClick() {
	if (!search) return;
	store.getCategories();
}

const clearSearch = () => {
	fetchCategories();
}
const itemsPerPage = ref('5');
const search = ref('');
const order = ref('ASC');
const imageRules = [
	{ text: 'Maximum image size is 200KB', icon: 'mdi-check' },
	{ text: 'Image dimension is 452 X 450', icon: 'mdi-check' },
	{ text: 'Image type should be WEBP', icon: 'mdi-check' },
]

const imageBannerRules = [
	{ text: 'Maximum image size is 200KB', icon: 'mdi-check' },
	{ text: 'Image dimension is 1800 X 250', icon: 'mdi-check' },
	{ text: 'Image type should be WEBP', icon: 'mdi-check' },
]

// const parentCategories = computed(() => {
//   let parentList: any[] = [];
//   parentList = store.categories.filter((item: any) => !item.parentId);
//   return parentList;
// });

const form = ref(false);
const categoryForm = ref();
const loading = ref(false);

const category = ref({
	id: null,
	name: '',
	description: '',
	parentId: null,
	imageGalleryId: null,
	bannerImageId: null,
	sortOrder: null
})

const rules = [
	(value: any) => {
		if (value) return true;
		return 'You must enter a name'
	}
];
const onSubmit = () => {

	if (!form.value) return;

	if (!category.value.sortOrder) {
		category.value.sortOrder = null;
	}
	loading.value = true
	setTimeout(() => {
		store.addUpdateCategory(category.value).then((res: any) => {
			snackbar.add({
				type: res.success ? 'success' : 'error',
				text: res.message,
			})

		}).catch(err => {
			console.log(err)
		})

		loading.value = false
		resetForm()

	}, 500);

}
const fetchCategories = async () => {
	const { page, itemsPerPage, sortBy, sortDesc, search } = options.value;
	const order = sortDesc[0] ? 'DESC' : 'ASC';
	const sortKey = sortBy[0] || 'name';
	loading.value = true;
	await store.getCategories(order, page, itemsPerPage, sortKey, search);
	categories.value = store.categories;
	loading.value = false;
}

const fetchParentCategories = async () => {
	await store.getParentCategories();
	parentCategories.value = store.parentCategories;
}

onMounted(fetchParentCategories);


const editItem = (item: any) => {
	category.value.id = item.id;
	category.value.name = item.name;
	category.value.description = item.description;
	category.value.parentId = item.parentId;
	category.value.sortOrder = item.sortOrder;
}


const deleteItem = (id: number) => {
	store.deleteCategory(id).then((res: any) => {
		snackbar.add({
			type: res.success ? 'success' : 'error',
			text: res.message,
		})
	}).catch(err => {
		console.log(err)
	})
}
const resetForm = () => {
	categoryForm.value.reset()
	category.value.id = null
	category.value.description = ''
	category.value.parentId = null;
	category.value.imageGalleryId = null
	category.value.bannerImageId = null
	category.value.sortOrder = null
}
const getParentCategoryName = (id: number) => {

	let parentCategoryName: string = ''

	if (id) {
		const parentCat: any = store.categories.find((obj: any) => obj.id === id);
		parentCategoryName = parentCat?.name;
	}
	return parentCategoryName
}
const handleSelectedImage = (val: any) => {
	category.value.imageGalleryId = val
}
const handleSelectedBannerImage = (val: any) => {
	category.value.bannerImageId = val
}

const confirmDialog = ref(false);
const itemToDelete = ref<number | null>(null);
const handleDeleteConfirmation = async () => {
	if (itemToDelete.value !== null) {
		const productId = itemToDelete.value;
		const res = await store.deleteCategory(productId);
		// Handle response
		if (res) {
			snackbar.add({
				type: res?.isSuccess ? 'success' : 'error',
				text: res?.messasge,
			})
			confirmDialog.value = false;
		} else {
			snackbar.add({
				type: 'error',
				text: 'Delete Failed',
			})
		}
	}
};

const bundleRelatedProductHandle = (item: Array<[]>) => {
	bundleProductRelatedTagCategoryFormRef.value?.showModal(item);
};


const fetchProductList = async () => {

	if (!permissionStore.hasModulePermission(`${DynamicModuleEnum.PRODUCT}:${DynamicPermissionEnum.READ_MANY}`)) {
		setTimeout(() => {
			snackbar.add({
				type: "warning",
				text: "You don't have permission to see product list",
			})
		}, 2000)
		return
	}

	loading.value = true;
	await productStore?.getProducts('ASC', 1, 40, '', '', '', '');
	loading.value = false;
};

watch(name, (val, oldValue) => {
	let arraydata = [];
	categories.value.filter((item) => {
		if (item.name.toLowerCase().includes(val)) {
			arraydata.push(item);
		}

	})
	if (val.length === 0) {
		arraydata = store.categories;
	}
	categories.value = arraydata;
})

</script>
