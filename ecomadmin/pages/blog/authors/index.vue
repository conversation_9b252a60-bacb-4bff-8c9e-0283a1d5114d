<template>
	<SharedBaseBreadcrumb :title="page.title" :breadcrumbs="breadcrumbs"></SharedBaseBreadcrumb>
	<v-row>
		<v-col cols="4">
			<SharedUiParentCardSolid :title="author.id ? 'Edit ' + author.name : 'Add New author'">
				<v-form v-model="form" @submit.prevent="onSubmit" ref="authorForm">
					<FormElementsCommonFieldContainer label="Image" :required="true">
						<FormElementsImageViewerSingle :image-id="author.imageGalleryId" :multiple="false"
							:image-rules="imageRules" :required-width="452" :required-height="450" :max-size="200000"
							image-model="author" @selected-image-id="handleSelectedImage" />
					</FormElementsCommonFieldContainer>
					<!-- <FormElementsCommonFieldContainer label="Banner Image" :required="true">
						<FormElementsImageViewerSingle :image-id="author.bannerImageId" :multiple="false"
							:image-rules="imageBannerRules" :required-width="1800" :required-height="250" :max-size="200000"
							image-model="author" @selected-image-id="handleSelectedBannerImage" />
					</FormElementsCommonFieldContainer> -->

					<FormElementsCommonFieldContainer label="Name" :required="true">
						<v-text-field v-model="author.name" :rules="rules" placeholder="Enter Name" density="compact"></v-text-field>
					</FormElementsCommonFieldContainer>
					<FormElementsCommonFieldContainer label="Email" :required="true">
						<v-text-field v-model="author.email" :rules="rules" placeholder="Enter Email" density="compact"></v-text-field>
					</FormElementsCommonFieldContainer>

					<!-- <FormElementsCommonFieldContainer label="Parent author">
						<v-select :items="parentAuthors" item-title="name" item-value="id" v-model="author.parentId"
							placeholder="Select Parent author" density="compact"></v-select>
					</FormElementsCommonFieldContainer> -->

					<FormElementsCommonFieldContainer label="Description" :required="false">
						<FormElementsEditor v-model="author.description" />
					</FormElementsCommonFieldContainer>

					<FormElementsCommonFieldContainer label="Sort Order">
						<v-text-field v-model="author.sortOrder" placeholder="Sort Order" density="compact"></v-text-field>
					</FormElementsCommonFieldContainer>

					<v-btn :disabled="!form" :loading="loading" color="primary" variant="flat" class="px-6" type="submit">
						{{
							author.id ?
								'Update' : 'Add New'
						}} Author
					</v-btn>

					<v-btn color="error" variant="flat" class="px-6 ms-2" @click="resetForm">Reset</v-btn>
				</v-form>
			</SharedUiParentCardSolid>
		</v-col>
		<v-col cols="8">
			<SharedUiParentCardSolid>
				<div class="d-flex justify-space-between align-center">
					<h2 class="text-h5 font-weight-bold">Authors</h2>
					<div class="w-50">
						<!-- <v-text-field append-inner-icon="mdi-magnify" density="compact" label="Search author" variant="outlined" 
              hide-details single-line @onChange:append-inner="onClick" v-model="options.search" clearable 
              @click:clear="clearSearch"></v-text-field> -->
						<v-text-field v-model="name" class="ma-1" density="compact" placeholder="Search author"
							hide-details></v-text-field>
					</div>
				</div>

				<v-data-table-server v-model:page="options.page" v-model:items-per-page="options.itemsPerPage"
					:headers="headers" :items="authors" :items-length="store.pagination.itemCount" :search="options.search"
					:loading="loading" item-value="name" :items-per-page-options="[10, 25, 50, 100]" @update:options="fetchAuthors">
					<template v-slot:thead>
						<tr>
							<td colspan="2">
								<!-- <v-text-field v-model="name" class="ma-1" density="compact" placeholder="Search author" hide-details></v-text-field> -->
							</td>
						</tr>
					</template>
					<template v-slot:item.imageGalleryId="{ item }">
						<!-- <div v-if="item.imageGallery">
							<v-img :src="item.imageGallery.imageUrl" width="60" />
						</div> -->
					</template>
					<template v-slot:item.Order="{ item }">
						<span v-html="item?.sortOrder"></span>
					</template>
					<template v-slot:item.email="{ item }">
						<span v-html="item?.email"></span>
					</template>
					<template v-slot:item.status="{ item }">
						<v-chip :color="item?.isActive ? 'success' : 'error'" variant="flat" size="x-small">
							{{ item?.isActive ? 'Active' : 'Inactive' }}
						</v-chip>
					</template>
					<template v-slot:item.action="{ item }">

						<div class="text-end">
							<v-menu>
								<template v-slot:activator="{ props }">
									<v-btn class="me-2" variant="tonal" size="x-small" icon="mdi-dots-vertical" v-bind="props"></v-btn>
								</template>

								<v-list>
									<v-list-item>
										<v-btn icon="mdi-pencil" color="warning" class="me-2" variant="flat" size="x-small"
											@click="editPayloadItem(item, author)">
										</v-btn>
									</v-list-item>
									<v-list-item>
										<v-btn icon="mdi-delete" color="error" class="me-2" variant="flat" size="x-small"
											@click="confirmDialog = true; itemToDelete = item.id">
										</v-btn>
									</v-list-item>

								</v-list>
							</v-menu>
						</div>
					</template>

				</v-data-table-server>

			</SharedUiParentCardSolid>
		</v-col>
	</v-row>
	<ConformationModal v-model:dialog="confirmDialog" @confirm-delete="handleDeleteConfirmation" />

</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { editPayloadItem } from "~/utils/helpers/functions";
import { PencilIcon, TrashIcon, LanguageIcon } from "vue-tabler-icons";
import ConformationModal from "~/components/modals/ConformationModal.vue";
import { useRolePermissionsStore } from '~/stores/administration/permissions';
import { useBlogAuthorsStore } from '~/stores/blog/authors';

const store = useBlogAuthorsStore();
const permissionStore = useRolePermissionsStore();
const page = ref({ title: "Authors" });

const breadcrumbs = ref([
	{
		text: "Blog",
		disabled: false,
		to: "/blog/posts",
	},
	{
		text: "Author list",
		disabled: false,
		to: "/blog/authors",
	},
]);
const options = ref({
	page: 1,
	itemsPerPage: 20,
	sortBy: ['name'],
	sortDesc: [false],
	search: '',
});

const snackbar = useSnackbar();
const authors = ref([]);
const parentAuthors = ref([]);
const name = ref('');
const headers = ref([
	{ title: 'Image', align: 'start', sortable: false, key: 'imageGalleryId' },
	{ title: 'Name', align: 'start', sortable: false, key: 'name' },
	{ title: 'Email', align: 'start', sortable: false, key: 'email' },
	{ title: 'Order', key: 'Order', sortable: false, align: 'start' },
	{ title: 'Status', key: 'status', sortable: false, align: 'start' },
	{ title: 'Action', key: 'action', align: 'center', sortable: false },
])

const bundleProductRelatedTagAuthorFormRef = ref(null);

function onClick() {
	if (!search) return;
	store.getAuthors();
}

const clearSearch = () => {
	fetchAuthors();
}
const itemsPerPage = ref('5');
const search = ref('');
const order = ref('ASC');
const imageRules = [
	{ text: 'Maximum image size is 200KB', icon: 'mdi-check' },
	{ text: 'Image dimension is 452 X 450', icon: 'mdi-check' },
	{ text: 'Image type should be WEBP', icon: 'mdi-check' },
]

const imageBannerRules = [
	{ text: 'Maximum image size is 200KB', icon: 'mdi-check' },
	{ text: 'Image dimension is 1800 X 250', icon: 'mdi-check' },
	{ text: 'Image type should be WEBP', icon: 'mdi-check' },
]

// const parentAuthors = computed(() => {
//   let parentList: any[] = [];
//   parentList = store.authors.filter((item: any) => !item.parentId);
//   return parentList;
// });

const form = ref(false);
const authorForm = ref();
const loading = ref(false);

const author = ref({
	id: null,
	name: '',
	email: '',
	description: '',
	parentId: null,
	imageGalleryId: null,
	bannerImageId: null,
	sortOrder: null
})

const rules = [
	(value: any) => {
		if (value) return true;
		return 'You must enter a name'
	}
];
const onSubmit = () => {

	if (!form.value) return;

	if (!author.value.sortOrder) {
		author.value.sortOrder = null;
	}
	loading.value = true
	setTimeout(() => {
		store.addUpdateAuthor(author.value).then((res: any) => {
			snackbar.add({
				type: res.success ? 'success' : 'error',

				text: res.message,
			})

		}).catch(err => {
			console.log(err)
		})

		loading.value = false
		resetForm()

	}, 500);

}
const fetchAuthors = async () => {
	const { page, itemsPerPage, sortBy, sortDesc, search } = options.value;
	const order = sortDesc[0] ? 'DESC' : 'ASC';
	const sortKey = sortBy[0] || 'name';
	loading.value = true;
	await store.getAuthors(order, page, itemsPerPage, sortKey, search);
	authors.value = store.authors;
	loading.value = false;
}

const fetchParentAuthors = async () => {
	// await store.getParentAuthors();
	// parentAuthors.value = store.parentAuthors;
}

onMounted(fetchParentAuthors);


const editItem = (item: any) => {
	author.value.id = item.id;
	author.value.name = item.name;
	author.value.email = item.email;
	author.value.description = item.description;
	author.value.parentId = item.parentId;
	author.value.sortOrder = item.sortOrder;
}


const deleteItem = (id: number) => {
	store.deleteAuthor(id).then((res: any) => {
		snackbar.add({
			type: res.success ? 'success' : 'error',
			text: res.message,
		})
	}).catch(err => {
		console.log(err)
	})
}
const resetForm = () => {
	authorForm.value.reset()
	author.value.id = null;
	author.value.name = "";
	author.value.email = "";
	author.value.description = '';
	author.value.parentId = null;
	author.value.imageGalleryId = null
	author.value.bannerImageId = null
	author.value.sortOrder = null
}
const getParentAuthorName = (id: number) => {

	let parentAuthorName: string = ''

	if (id) {
		const parentCat: any = store.authors.find((obj: any) => obj.id === id);
		parentAuthorName = parentCat?.name;
	}
	return parentAuthorName
}
const handleSelectedImage = (val: any) => {
	author.value.imageGalleryId = val
}
const handleSelectedBannerImage = (val: any) => {
	author.value.bannerImageId = val
}

const confirmDialog = ref(false);
const itemToDelete = ref<number | null>(null);
const handleDeleteConfirmation = async () => {
	if (itemToDelete.value !== null) {
		const productId = itemToDelete.value;
		const res = await store.deleteAuthor(productId);
		// Handle response
		if (res) {
			snackbar.add({
				type: res?.isSuccess ? 'success' : 'error',
				text: res?.messasge,
			})
			confirmDialog.value = false;
		} else {
			snackbar.add({
				type: 'error',
				text: 'Delete Failed',
			})
		}
	}
};

const bundleRelatedProductHandle = (item: Array<[]>) => {
	bundleProductRelatedTagAuthorFormRef.value?.showModal(item);
};


watch(name, (val, oldValue) => {
	let arraydata = [];
	authors?.value?.filter((item) => {
		if (item?.name?.toLowerCase().includes(val)) {
			arraydata.push(item);
		}

	})
	if (val.length === 0) {
		arraydata = store.authors;
	}
	authors.value = arraydata;
})

</script>
