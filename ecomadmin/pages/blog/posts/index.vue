<template>
  <SharedBaseBreadcrumb :title="page.title" :breadcrumbs="breadcrumbs"></SharedBaseBreadcrumb>
  <div class="d-flex justify-end">
    <v-btn to="/blog/posts/create" variant="flat" color="primary" density="compact">New Create</v-btn>
  </div>
  <SharedUiParentCardSolid body-class="pa-3" class="mt-3">
    <v-row>
      <v-col v-for="(obj, index) in blogStore?.blogs" :key="'blog_card_' + index" cols="12" sm="4" md="3">
        <v-card class="elevation-0 border">
          <v-img :src="obj?.featuredImageInfo?.imageUrl || 'https://via.placeholder.com/600x300?text=No+Image'"
            height="230px" cover></v-img>

          <v-card-title class="text-h4 text-truncate mt-2 font-weight-bold">{{ obj?.title }}</v-card-title>
          <v-card-subtitle class="text-caption grey--text">
            {{ formatDateTime(obj?.createdAt) }}
          </v-card-subtitle>

          <v-card-text class="pb-1">
            <v-sheet class="text-body-2 line-clamp-3" min-height="100">{{ obj.excerpt }}</v-sheet>
          </v-card-text>

          <v-card-actions>
            <v-btn :to="{ name: 'blog-posts-id', params: { id: obj?.id } }" variant="text" color="primary">Read More</v-btn>
            <v-menu>
              <template v-slot:activator="{ props }">
                <v-btn class="me-2" variant="tonal" size="x-small" icon="mdi-dots-vertical" v-bind="props"></v-btn>
              </template>

              <v-list>
                <v-list-item>
                  <v-btn icon="mdi-pencil" color="warning" class="me-2" variant="flat" size="x-small" :to="{
                    name: 'blog-posts-id-edit', params: {
                      id: obj?.id
                    }
                  }">
                  </v-btn>
                </v-list-item>
                <v-list-item>
                  <v-btn icon="mdi-translate" color="info" class="me-2" variant="flat" size="x-small"
                    @click="localizePost(obj)">
                  </v-btn>
                </v-list-item>
                <v-list-item>
                  <v-btn icon="mdi-delete" color="error" class="me-2" variant="flat" size="x-small"
                    @click="confirmDialog = true; itemToDelete = item.id">
                  </v-btn>
                </v-list-item>

              </v-list>
            </v-menu>
          </v-card-actions>
        </v-card>
      </v-col>
      <v-col cols="12">
        <v-pagination v-model="params.page" :length="blogStore?.pagination?.pageCount">

        </v-pagination>
      </v-col>
    </v-row>
  </SharedUiParentCardSolid>
  <ConformationModal v-model:dialog="confirmDialog" @confirm-delete="handleDeleteConfirmation" />
  <BlogLocalizePostLocalize ref="localizationDialog" />
</template>

<script setup lang="ts">
import { editPayloadItem, formatDateTime } from "~/utils/helpers/functions";
import { useBlogPostsStore } from '~/stores/blog/posts';
import ConformationModal from "~/components/modals/ConformationModal.vue";

const page = ref({ title: "Blog Posts" });
const breadcrumbs = ref([
  {
    text: "Blog",
    disabled: false,
  },
  {
    text: "Posts",
    disabled: true,
    to: "/blog/posts",
  },
]);

const blogStore = useBlogPostsStore();
const confirmDialog = ref(false);
const localizationDialog = ref(null);

const params = reactive({
  order: "ASC",
  page: 1, 
  take: 8, 
  sort: "", 
  search: ""
})

const localizePost = (item: any) => {
  localizationDialog.value?.show(item);
};

const handleDeleteConfirmation = async () => {

};
const handleFetchBlogs = () => {
  const { order, page, take, sort, search } = params;

  blogStore?.getAllPosts(order, page, take, sort, search);
};

watch(
  ()=> params?.page,
  (newVal, oldVal)=>{
    if(newVal && newVal != oldVal){
      handleFetchBlogs();
    }
  }
)

onMounted(() => {
  handleFetchBlogs();
});
</script>

<style scoped>
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
