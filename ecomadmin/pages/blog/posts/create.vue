<template>
	<div>
		<SharedBaseBreadcrumb :title="page.title" :breadcrumbs="breadcrumbs"></SharedBaseBreadcrumb>
		<v-container fluid class="px-0">
			<v-form @submit.prevent="onSubmit" ref="formRef">
				<v-row>
					<v-col cols="12" md="3">
						<SharedUiParentCardSolid body-class="px-3 py-5" class="d-flex flex-column gap-2">
							<v-text-field v-model="form.title" label="Title" density="compact"
								:rules="[rules.required]"></v-text-field>
							<v-select v-model="form.categories" :items="blogCategoryStore?.categories" item-title="name"
								item-value="id" label="Blog Category" density="compact" multiple :rules="[rules.required]"></v-select>
							<FormElementsImageViewerSingle :image-id="form.featuredImageInfo" :image-rules="PRODUCT_IMAGE_RULES"
								:required-height="2250" :required-width="1800"
								:image-folders="[{ folderSlug: 'blog', title: 'Blog', size: '1800x2250' }]" image-model="blog"
								@selected-image-id="selectedFeatureImage" :disableImageValidation="true" />
							<v-textarea label="Excerpt" v-model="form.excerpt" row="2"></v-textarea>

							<BlogFormFieldTag v-model:tags="form.tags" :rules="[rules.required]" />

							<v-select v-model="form.primaryAuthor" label="Primary Author" :items="authorStore?.authors"
								item-title="name" item-value="id" :rules="[rules.required]" />
							<v-select v-model="form.secondaryAuthors" label="Secondary Author" :items="authorStore?.authors"
								item-title="name" item-value="id" multiple :rules="[rules.required]" />
						</SharedUiParentCardSolid>
						<h4 class="text-h4 mt-3">Meta Setting</h4>
						<SharedUiParentCardSolid body-class="px-3 py-5" class="mt-3 d-flex flex-column gap-2">
							<v-text-field v-model="form.metaTitle" label="Title" density="compact"></v-text-field>
							<v-text-field v-model="form.metaDescription" label="Description" density="compact"></v-text-field>
						</SharedUiParentCardSolid>
					</v-col>
					<v-col cols="12" md="9">
						<SharedUiParentCardSolid body-class="pa-3">
							<h4 class="text-h4 mb-3">Content</h4>
							<FormElementsRichTextEditor v-model="form.content" style="min-height: 50vh;" :rules="[rules.required]" />
							<v-text color="error" v-if="!form.content">Content is required!</v-text>
						</SharedUiParentCardSolid>
						<v-btn type="submit" color="primary">Submit</v-btn>
					</v-col>
				</v-row>
			</v-form>
		</v-container>
	</div>
</template>

<script setup>
import { useBlogAuthorsStore } from '~/stores/blog/authors';
import { useBlogCategoriesStore } from '~/stores/blog/categories';
import { useBlogPostsStore } from '~/stores/blog/posts';

const page = ref({ title: "Blog Posts" });
const breadcrumbs = ref([
	{
		text: "Blog",
		disabled: false,
	},
	{
		text: "Posts",
		disabled: true,
		to: "/blog/posts",
	},
]);

const router = useRouter();
const blogCategoryStore = useBlogCategoriesStore();
const blogPostStore = useBlogPostsStore();
const authorStore = useBlogAuthorsStore();

const snackbar = useSnackbar();
const initialMeta = reactive({
	title: "",
	description: "",
	keywords: "",
	focusKeywords: [],
	isIncludeInSitemap: true,
	isAllowIndex: true,
	isAllowPageIndex: true,
	isAllowImageIndex: true,
	isAllowNoFollow: true,
	maxSnippet: 0,
	canonicalUrl: "",
	schemaMarkup: "",
	pageSeoScore: 0,
	keywordDensity: 0,
	altTextMatchKeywordCount: 0,
	internalLinkCount: 0,
	externalLinkCount: 0,
	ogUrl: "",
	pageContentMatchKeywordCount: 0,
	metaInfo: {}
});

const formRef = ref();
const form = reactive({
	primaryAuthor: null,
	secondaryAuthors: <AUTHORS>
	title: null,
	excerpt: null,
	content: null,
	featuredImageInfo: null,
	categories: [],
	status: false,
	publishedAt: null,
	metaTitle: null,
	metaDescription: null,
	metaInfo: { ...initialMeta },
	tags: [],
	products: [],
	comments: [],
	countryId: null,
});
const rules = {
	required: (v) => !!v || 'This field is required',
	maxLength: (length) => (v) => !v || v.length <= length || `Max ${length} characters`,
};

const selectedFeatureImage = (val) => {
	form.featuredImageInfo = val;
};

const handleFetchBlogCategory = () => {
	blogCategoryStore?.getAllActiveCategories();
}


const onSubmit = async () => {
	const { valid } = await formRef.value.validate();

	if (!valid) {
		return; // Do not proceed if invalid
	}

	if(!form?.content || form?.content.length === 0){
		snackbar.add({
			type: 'warning',
			text: 'Content is must required!',
		});
		return;
	}

	blogPostStore
		?.createSinglePost({ ...form })
		?.then((response) => {
			router.push('/blog/posts');
		})
}

onMounted(() => {
	handleFetchBlogCategory();
	authorStore.getAllActiveAuthors();

})

</script>