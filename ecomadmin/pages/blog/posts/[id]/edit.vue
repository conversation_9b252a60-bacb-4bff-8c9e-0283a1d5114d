<template>
  <div>
    <SharedBaseBreadcrumb :title="page.title" :breadcrumbs="breadcrumbs"></SharedBaseBreadcrumb>
    <v-container class="py-10">
      <v-row justify="center">
        <v-col cols="12" md="8">
          <v-card class="pa-6" elevation="0" rounded="lg">
            <v-card-title class="d-flex align-center justify-space-between">
              <h4 class="text-h4">Editing for <strong>{{ form?.title }}</strong></h4>
              <v-btn :to="{name: 'blog-posts-id', params:{ id: blog?.id}}" color="primary" flat>View</v-btn>
            </v-card-title>
            <v-form @submit.prevent="handleUpdateSingleBlog" v-if="blog" ref="formRef" class="mt-3">
              <v-text-field
                v-model="form.title"
                label="Title"
                variant="outlined"
                class="mb-4"
                required
                :rules="[rules.required]"
              ></v-text-field>
              <v-select v-model="form.categories" :items="blogCategoryStore?.categories" item-title="name" item-value="id"
                label="Blog Category" density="compact" multiple :rules="[rules.required]"></v-select>

              <!-- <v-img v-if="form?.featuredImageInfo" :src="form?.featuredImageInfo?.imageUrl"/> -->
              <FormElementsImageViewerSingle :image-id="form?.featuredImageInfo" :image-rules="PRODUCT_IMAGE_RULES"
                :required-height="2250" :required-width="1800"
                :image-folders="[{ folderSlug: 'blog', title: 'Blog', size: '1800x2250' }]" image-model="blog"
                @selected-image-id="selectedFeatureImage" :disableImageValidation="true" />

              <v-textarea
                v-model="form.excerpt"
                label="Excerpt"
                variant="outlined"
                class="mb-4"
                rows="3"
                :rules="[rules.required]"
              ></v-textarea>

							<v-select v-model="form.primaryAuthor" label="Primary Author" :items="authorStore?.authors"
								item-title="name" item-value="id" :rules="[rules.required]" />
							<v-select v-model="form.secondaryAuthors" label="Secondary Author" :items="authorStore?.authors"
								item-title="name" item-value="id" multiple :rules="[rules.required]" />

              <BlogFormFieldTag v-model:tags="form.tags" />
              <FormElementsRichTextEditor v-model="form.content" class="mb-4" />

              <v-text-field
                v-model="form.metaTitle"
                label="Meta Title"
                variant="outlined"
                class="mb-4"
                :rules="[rules.required]"
              ></v-text-field>

              <v-text-field
                v-model="form.metaDescription"
                label="Meta Description"
                variant="outlined"
                class="mb-6"
                :rules="[rules.required]"
              ></v-text-field>

              <v-btn type="submit" color="primary" class="text-none" size="large">
                Update Blog
              </v-btn>
            </v-form>
          </v-card>
        </v-col>
      </v-row>
    </v-container>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { useBlogAuthorsStore } from '~/stores/blog/authors';
import { useBlogCategoriesStore } from '~/stores/blog/categories';
import { useBlogPostsStore } from '~/stores/blog/posts';

const page = ref({ title: "Blog Posts" });
const breadcrumbs = ref([
  {
    text: "Blog",
    disabled: false,
  },
  {
    text: "Posts",
    disabled: true,
    to: "/blog/posts",
  },
]);
const route = useRoute();
const blogStore = useBlogPostsStore();
const blogCategoryStore = useBlogCategoriesStore();
const authorStore = useBlogAuthorsStore();

const formRef = ref();
const form = ref({
  id: null,
  title: null,
  slug: null,
  excerpt: null,
  content: null,
  metaTitle: null,
  metaDescription: null,
  featuredImageInfo: null,
  categories: [],
  tags: [],
  primaryAuthor: null,
  secondaryAuthors: <AUTHORS>
});

const blog = ref(null);
const snackbar = useSnackbar();

const rules = {
	required: (v) => !!v || 'This field is required',
	maxLength: (length) => (v) => !v || v.length <= length || `Max ${length} characters`,
};


const handleFetchSingleBlog = async () => {
  await blogStore.getSinglePost({ id: route?.params?.id });
  blog.value = blogStore?.singleBlog;

  if (blog.value) {
    form.value = {
      id: blog?.value?.id,
      title: blog?.value?.title,
      slug: blog?.value?.slug,
      excerpt: blog?.value?.excerpt,
      content: blog?.value?.content,
      metaTitle: blog?.value?.metaTitle,
      metaDescription: blog?.value?.metaDescription,
      featuredImageInfo: blog?.value?.featuredImageInfo?.id,
      categories: blog?.value?.categories,
      tags: blog?.value?.tags,
      primaryAuthor: blog?.value?.primaryAuthor?.id,
      secondaryAuthors: <AUTHORS>
    };
  }
};

const handleUpdateSingleBlog = async () => {
	const { valid } = await formRef.value.validate();

	if (!valid) {
		return; // Do not proceed if invalid
	}

	if(!form?.value?.content || form?.value?.content.length === 0){
		snackbar.add({
			type: 'warning',
			text: 'Content is must required!',
		});
		return;
	}

  const payload = { 
    ...form.value,
    primaryAuthor: form?.value?.primaryAuthor, 
    secondaryAuthors: <AUTHORS>
  };
  try {
    await blogStore?.updateSinglePost(payload);
    snackbar.add({
      type: 'success',
      text: 'Blog updated successfully!'
    });
		// router.push('/blog/posts')
  } catch (e) {
    snackbar.add({
      type: 'error',
      text: 'Error updating blog!'
    });
  }
};

const handleFetchBlogCategory = () => {
	blogCategoryStore?.getAllActiveCategories();
}

const selectedFeatureImage = (val) => {
	form.value.featuredImageInfo = val;
};

onMounted(() => {
	handleFetchBlogCategory();
  handleFetchSingleBlog();
	authorStore.getAllActiveAuthors();
});
</script>
