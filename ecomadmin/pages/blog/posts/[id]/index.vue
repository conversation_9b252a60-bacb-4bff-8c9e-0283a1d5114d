<template>
  <SharedBaseBreadcrumb :title="page.title" :breadcrumbs="breadcrumbs"></SharedBaseBreadcrumb>
  
  <v-container class="py-10">
    <v-row justify="center">
      <v-col cols="12" md="8">
        <v-card class="pa-6" elevation="0" rounded="lg">
          <v-btn :to="{name: 'blog-posts-id-edit', params:{ id: blog?.id}}" color="primary" flat>Edit</v-btn>
          <div class="mb-4 text-subtitle-2 text-grey">
            {{ formatDateTime(blog?.publishedAt) }}
          </div>

          <h1 class="text-h4 font-weight-bold mb-4">
            {{ blog?.title }}
          </h1>

          <div class="text-subtitle-1 text-grey mb-4">
            {{ blog?.excerpt }}
          </div>

          <v-img
            v-if="blog?.featuredImageInfo"
            :src="blog?.featuredImageInfo?.imageUrl"
            alt="Blog Image"
            class="mb-6 rounded-lg"
            height="400"
            cover
          ></v-img>

          <div v-html="blog?.content" class="blog-content mb-6"></div>

          <v-divider></v-divider>

          <div class="mt-6 text-body-2 text-grey">
            Meta: <strong>{{ blog?.metaTitle }}</strong> | Description: <em>{{ blog?.metaDescription }}</em>
          </div>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup>
import { onMounted, computed } from 'vue';
import { useRoute } from 'vue-router';
import { useBlogPostsStore } from '~/stores/blog/posts';
import { formatDateTime } from '~/utils/helpers/functions';

const page = ref({ title: "Blog Single" });
const breadcrumbs = ref([
  {
    text: "Blog",
    disabled: false,
  },
  {
    text: "Single",
    disabled: true,
    to: "/blog/posts",
  },
]);

const route = useRoute();
const blogStore = useBlogPostsStore();

const blog = computed(() => blogStore?.singleBlog);

const handleFetchSingleBlog = () => {
  blogStore?.getSinglePost({ id: route?.params?.id });
};

const selectedFeatureImage = (val) => {
	blog.value.featuredImageInfo = val;
};

onMounted(() => {
  handleFetchSingleBlog();
});
</script>

<style scoped>
.blog-content :deep(p) {
  margin-bottom: 1rem;
  line-height: 1.6;
  font-size: 1.1rem;
}
.blog-content :deep(h2) {
  margin-top: 2rem;
  margin-bottom: 1rem;
}
</style>
