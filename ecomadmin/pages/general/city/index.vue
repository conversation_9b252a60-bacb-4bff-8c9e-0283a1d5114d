<template>
  <v-row>
    <v-col cols="4">
      <SharedUiParentCard :title="city.id ? 'Edit ' + city.name : 'New City'">
        <v-form v-model="form" @submit.prevent="onSubmit">
          <FormElementsCommonFieldContainer label="Name" :required="true">
            <v-text-field v-model="city.name" :rules="[ val => REQUIRED_RULE(val) ]"></v-text-field>
          </FormElementsCommonFieldContainer>

          <FormElementsCommonFieldContainer label="Description" class="mb-4">
            <FormElementsEditor v-model="city.short" />
          </FormElementsCommonFieldContainer>

          <FormElementsCommonFieldContainer label="Code" >
            <v-text-field v-model="city.code"></v-text-field>
          </FormElementsCommonFieldContainer>
          <FormElementsCommonFieldContainer label="State" :required="true">
            <v-select :items="getStates?.itemList" item-title="name" item-value="id" v-model="city.stateId" placeholder="Select State"></v-select>
          </FormElementsCommonFieldContainer>

          <v-divider class="mb-3"></v-divider>
          <v-btn
              :disabled="!form"
              :loading="loading"
              color="primary"
              variant="tonal" class="px-6"
              type="submit"> {{ city.id ? 'Update' : 'Add New' }} City
          </v-btn>

          <v-btn
              color="error"
              variant="tonal" class="px-6 ms-2"
              @click="resetForm"
          > Reset
          </v-btn>
        </v-form>
      </SharedUiParentCard>
    </v-col>
    <v-col cols="8">
      <template v-if="stateStore.loading">
        <SharedUiLoader type="table-heading, table-thead, table-tbody, table-tfoot"/>
      </template>
      <SharedUiParentCard title="Cities" v-else>

        <template v-if="getCities?.itemList?.length > 0">
          <TableComponentsSimpleTable :theads="tableHeads">
            <template #default>
              <tr v-for="item in getCities?.itemList" :key="item.id" class="month-item">
                <td>
                  <h6 class="text-h6 font-weight-medium text-medium-emphasis text-no-wrap">{{ item.name }}</h6>
                </td>
                <td>
                  <span v-html="item.short"></span>
                </td>
                <td>
                  {{ item.code }}
                </td>
                <td>
                  {{ item.state.name }}
                </td>
                <td>
                  <v-btn icon color="warning" class="me-2" variant="tonal" size="small"
                         @click="editPayloadItem(item, city)">
                    <PencilIcon size="20"/>
                  </v-btn>
                  <v-btn icon color='error' variant="tonal" size="small" @click="deleteItem(item.id)">
                    <TrashIcon size="20"/>
                  </v-btn>
                </td>
              </tr>
            </template>
          </TableComponentsSimpleTable>
          <FormElementsPagination :pagination="stateStore.pagination"
                                  @get-event="fetchStates('ASC', stateStore.pagination.page)"/>
        </template>
        <template v-else>
          Sorry no Data Found.
        </template>
      </SharedUiParentCard>
    </v-col>
  </v-row>
</template>

<script setup lang="ts">
//***************************Imports******************//
import { PencilIcon, TrashIcon } from "vue-tabler-icons";
import {computed, onMounted, ref} from 'vue';
import {editPayloadItem} from "~/utils/helpers/functions";
import {REQUIRED_RULE} from "~/utils/formRules";
import {useStateStore} from "~/stores/others/state";
import {useCityStore} from "~/stores/others/city";

//***************************Variables******************//
const stateStore = useStateStore();
const cityStore = useCityStore();
const snackbar = useSnackbar();
const tableHeads = ['Name', 'Description', 'Code', 'State', 'Action'];

const form = ref(false);
const loading = ref(false);

const city = ref({
  id: null,
  name: '',
  short: '',
  code: '',
  stateId: null
})

const rules = [
  (value : any) => {
    if (value) return true;
    return 'You must enter a city name'
  }
];

//***************************Methods******************//
const onSubmit = () => {

  if (!form.value) return;
  loading.value = true

  cityStore.addUpdateCity(city.value).then((res: any )=> {
    snackbar.add({
      type: res.success ? 'success' : 'error',
      text: res.message
    })
  }).catch(error => {
    snackbar.add({
      type: 'error',
      text: error
    })
  })


  loading.value = false
  resetForm()
}

const fetchCities = (order = 'ASC', page = 1, take = 5) => {
  cityStore.getCities({order: order, page: page, take: take})
}

const deleteItem = (id: number) => {
  stateStore.deleteState(id).then(res => {
    snackbar.add({
      type: 'error',
      text: res
    })
  })
}

const resetForm = () => {
  city.value.id = null
  city.value.name = ''
  city.value.short = ''
  city.value.stateId = null;
  city.value.code = ''
}

//***************************Lifecycle Hooks******************//
const getStates = computed(() => stateStore.states);

const getCities = computed(() => cityStore.cities)

onMounted(() => {
  fetchCities();
  stateStore.getStates({})
});

</script>
