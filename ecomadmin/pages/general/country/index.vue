<template>
  <SharedBaseBreadcrumb
    :title="page.title"
    :breadcrumbs="breadcrumbs"
  ></SharedBaseBreadcrumb>

  <v-row>
    <v-col cols="12">
      <SharedUiParentCard
        :title="country.id ? 'Edit ' + country.name : 'Add New Country'"
      >
        <v-form v-model="form" @submit.prevent="onSubmit" ref="countryForm">
          <v-row>
            <v-col cols="3">
              <FormElementsCommonFieldContainer
                label="Country Image"
                :required="!country.id"
              >
                <FormElementsImageViewerSingle
                  :image-rules="imageRules"
                  @selected-image-id="handleImgUpload"
                  :image-id="country.imageGalleryId"
                  image-model="country"
                  :max-size="100000"
                  :required-height="153"
                  :required-width="255"
                  :multiple="false"
                />
              </FormElementsCommonFieldContainer>
            </v-col>
            <v-col cols="3">
              <FormElementsCommonFieldContainer label="Name" :required="true">
                <v-text-field
                  v-model="country.name"
                  :rules="[(val) => REQUIRED_RULE(val)]"
                ></v-text-field>
              </FormElementsCommonFieldContainer>
            </v-col>

            <v-col cols="3">
              <FormElementsCommonFieldContainer
                label="Country Code"
                :required="true"
              >
                <v-text-field
                  v-model="country.code"
                  :rules="[(val) => REQUIRED_RULE(val)]"
                ></v-text-field>
              </FormElementsCommonFieldContainer>
            </v-col>

            <v-col cols="6">
              <FormElementsCommonFieldContainer
                label="Primary Language"
                :required="true"
              >
                <v-select
                  :items="languagesOption"
                  item-title="name"
                  item-value="id"
                  :rules="[(val) => REQUIRED_RULE(val)]"
                  v-model="country.languageId"
                  placeholder="Select Language"
                ></v-select>
              </FormElementsCommonFieldContainer>
            </v-col>

            <v-col cols="6">
              <FormElementsCommonFieldContainer
                label="Languages"
                :required="true"
              >
                <v-select
                  :items="languagesOption"
                  item-title="name"
                  item-value="id"
                  :rules="[(val) => REQUIRED_RULE(val)]"
                  v-model="country.languagesId"
                  placeholder="Select Language"
                  multiple
                ></v-select>
              </FormElementsCommonFieldContainer>
            </v-col>

            <v-col cols="6">
              <FormElementsCommonFieldContainer label="Domain">
                <v-text-field v-model="country.domain"></v-text-field>
              </FormElementsCommonFieldContainer>
            </v-col>
            <v-col cols="6">
              <FormElementsCommonFieldContainer label="Gtm ID">
                <v-text-field v-model="country.gtm"></v-text-field>
              </FormElementsCommonFieldContainer>
            </v-col>

            <v-col cols="6">
              <FormElementsCommonFieldContainer label="Gtm Auth token">
                <v-text-field
                  v-model="country.gtm_auth"
                  :rules="[getValidation]"
                ></v-text-field>
              </FormElementsCommonFieldContainer>
            </v-col>

            <v-col cols="6">
              <FormElementsCommonFieldContainer label="TAT/AIT Percentage (%)">
                <v-text-field
                  v-model="country.taxPercentage"
                  type="number"
                ></v-text-field>
              </FormElementsCommonFieldContainer>
            </v-col>

            <v-col cols="3">
              <FormElementsCommonFieldContainer label="Shipping Charge">
                <v-text-field
                  v-model="country.shippingCharge"
                  type="number"
                ></v-text-field>
              </FormElementsCommonFieldContainer>
            </v-col>

            <v-col cols="3">
              <FormElementsCommonFieldContainer label="COD Charge">
                <v-text-field
                  v-model="country.codCharge"
                  type="number"
                ></v-text-field>
              </FormElementsCommonFieldContainer>
            </v-col>

            <v-col cols="3">
              <FormElementsCommonFieldContainer
                label="Currency"
                :required="true"
              >
                <v-select
                  :items="getCurrencies"
                  item-title="currency"
                  item-value="id"
                  :rules="[(val) => REQUIRED_RULE(val)]"
                  v-model="country.currencyId"
                  placeholder="Select Currency"
                ></v-select>
              </FormElementsCommonFieldContainer>
            </v-col>

            <v-col cols="3">
              <FormElementsCommonFieldContainer label="Shipping Charge Free">
                <v-text-field
                  v-model="country.shippingChargeFree"
                  type="number"
                ></v-text-field>
              </FormElementsCommonFieldContainer>
            </v-col>
            <!--  -->
            <v-col cols="12">
              <h5 class="text-h5 font-weight-bold">Social Media Link</h5>
            </v-col>
            <v-col cols="4">
              <FormElementsCommonFieldContainer label="Facebook">
                <v-text-field v-model="country.facebook"></v-text-field>
              </FormElementsCommonFieldContainer>
            </v-col>
            <v-col cols="4">
              <FormElementsCommonFieldContainer label="Instagram">
                <v-text-field v-model="country.instagram"></v-text-field>
              </FormElementsCommonFieldContainer>
            </v-col>
            <v-col cols="4">
              <FormElementsCommonFieldContainer label="X/Twitter">
                <v-text-field v-model="country.x"></v-text-field>
              </FormElementsCommonFieldContainer>
            </v-col>
            <v-col cols="4">
              <FormElementsCommonFieldContainer label="Linkedin">
                <v-text-field v-model="country.linkedin"></v-text-field>
              </FormElementsCommonFieldContainer>
            </v-col>
            <v-col cols="4">
              <FormElementsCommonFieldContainer label="TikTok">
                <v-text-field v-model="country.tiktok"></v-text-field>
              </FormElementsCommonFieldContainer>
            </v-col>
            <v-col cols="4">
              <FormElementsCommonFieldContainer label="YouTube">
                <v-text-field v-model="country.youtube"></v-text-field>
              </FormElementsCommonFieldContainer>
            </v-col>
            <v-col cols="12">
              <FormElementsCommonFieldContainer label="Description">
                <FormElementsEditor v-model="country.description" />
              </FormElementsCommonFieldContainer>
            </v-col>
            <v-col cols="12">
              <h5 class="text-h5 font-weight-bold">Chat Config</h5>
            </v-col>

            <v-col cols="4">
              <FormElementsCommonFieldContainer label="Messenger">
                <v-text-field
                  v-model="country.chatConfig.messenger"
                  hint="Ex. https://m.me/457167374152687"
                  persistent-hint
                ></v-text-field>
              </FormElementsCommonFieldContainer>
            </v-col>
            <v-col cols="4">
              <FormElementsCommonFieldContainer label="Whatsapp">
                <v-text-field
                  v-model="country.chatConfig.whatsapp"
                  hint="Ex. https://wa.me/8801886885667"
                  persistent-hint
                ></v-text-field>
              </FormElementsCommonFieldContainer>
            </v-col>
          </v-row>
          <v-divider class="my-5"></v-divider>
          <v-btn
            :disabled="!form"
            :loading="loading"
            color="primary"
            variant="tonal"
            class="px-6"
            type="submit"
          >
            {{ country.id ? "Update" : "Add New" }} Country
          </v-btn>
          <v-btn
            color="error"
            variant="tonal"
            class="px-6 ms-2"
            @click="resetForm"
          >
            Reset
          </v-btn>
        </v-form>
      </SharedUiParentCard>
    </v-col>
    <v-col cols="12">
      <SharedUiParentCard>
        <div class="d-flex justify-space-between align-center">
          <h2>Country</h2>
          <div class="w-50">
            <v-text-field
              :loading="store.searchLoading"
              append-inner-icon="mdi-magnify"
              density="compact"
              label="Search country"
              variant="outlined"
              hide-details
              single-line
              @onChange:append-inner="onClick"
              v-model="options.search"
              clearable
              @click:clear="clearSearch"
            ></v-text-field>
          </div>
        </div>

        <v-data-table-server
          v-model:page="options.page"
          v-model:items-per-page="options.itemsPerPage"
          :headers="headers"
          :items="countries"
          :items-length="store.pagination.itemCount"
          :search="options.search"
          :loading="loading"
          item-value="name"
          @update:options="fetchCountries"
        >
          <template v-slot:thead>
            <tr>
              <td></td>
              <td>
                <v-text-field
                  v-model="name"
                  class="ma-1"
                  density="compact"
                  placeholder="Search name..."
                  hide-details
                ></v-text-field>
              </td>
            </tr>
          </template>
          <template v-slot:item.image="{ item }">
            <div v-if="item?.imageGallery">
              <v-img :src="item?.imageGallery?.imageUrl" width="40" />
            </div>
          </template>

          <template v-slot:item.languageId="{ item }">
            {{
              item?.languageId
                ? languagesOption.find((it) => it.id == item?.languageId)?.name
                : ""
            }}
          </template>
          <template v-slot:item.languagesId="{ item }">
            <span
              v-html="item?.languagesId ? item.languagesId.map((lng: number) => languagesOption.find((it) => it.id == lng)?.name  || '').join(',<br/>') : ''"
            />
          </template>

          <template v-slot:item.status="{ item }">
            <v-switch
              v-model="item.isActive"
              :label="item.isActive"
              @change="updateStatus(item)"
              :color="item.isActive ? 'success' : 'error'"
            ></v-switch>
            <!-- <v-chip
              :color="item?.isActive ? 'success' : 'error'"
              variant="elevated"
            >
              {{ item?.isActive ? "Active" : "Inactive" }}
            </v-chip> -->
          </template>
          <template v-slot:item.currency="{ item }">
            {{ item.currency ? item.currency.currency : "-" }}
          </template>
          <template v-slot:item.action="{ item }">
            <div class="text-end">
              <v-menu>
                <template v-slot:activator="{ props }">
                  <v-btn
                    class="me-2"
                    variant="tonal"
                    size="small"
                    icon="mdi-dots-vertical"
                    v-bind="props"
                  ></v-btn>
                </template>

                <v-list>
                  <v-list-item>
                    <v-btn
                      icon
                      color="warning"
                      class="me-2"
                      variant="tonal"
                      size="x-small"
                      @click="country = { ...item }"
                    >
                      <PencilIcon size="20" />
                    </v-btn>
                  </v-list-item>
                  <v-list-item>
                    <v-btn
                      icon
                      color="error"
                      variant="tonal"
                      size="x-small"
                      @click="
                        confirmDialog = true;
                        itemToDelete = item.id;
                      "
                    >
                      <TrashIcon size="20" />
                    </v-btn>
                  </v-list-item>
                </v-list>
              </v-menu>
            </div>
          </template>
        </v-data-table-server>
      </SharedUiParentCard>
    </v-col>
  </v-row>
  <ConformationModal
    v-model:dialog="confirmDialog"
    @confirm-delete="handleDeleteConfirmation"
  />
</template>

<script setup lang="ts">
//***************************Imports******************//
import { PencilIcon, TrashIcon } from "vue-tabler-icons";
import { computed, onMounted, ref, watch } from "vue";
import { useCountryStore } from "~/stores/others/country";
import { useCurrencyStore } from "~/stores/others/currency";
import { editPayloadItem } from "~/utils/helpers/functions";
import { REQUIRED_RULE } from "~/utils/formRules";
import ConformationModal from "~/components/modals/ConformationModal.vue";
import { LanguagesOptionArr } from "~/utils/languages";

//***************************Variables******************//
const store = useCountryStore();
const currStore = useCurrencyStore();
const snackbar = useSnackbar();
const form = ref(false);
const countryForm = ref();
const loading = ref(false);
const page = ref({ title: "Manage Country" });

const breadcrumbs = ref([
  {
    text: "Dashboard",
    disabled: false,
    to: "/dashboard",
  },
  {
    text: "Country",
    disabled: true,
    to: "",
  },
]);
const country = ref({
  id: null,
  name: "",
  code: "",
  domain: "",
  gtm: "",
  gtm_auth: "",
  languageId: null,
  languagesId: null,
  currencyId: null,
  description: "",
  imageGalleryId: null,
  taxPercentage: 0,
  shippingCharge: 0,
  codCharge: 0,
  shippingChargeFree: 0,
  facebook: "",
  x: "",
  tiktok: "",
  linkedin: "",
  instagram: "",
  youtube: "",
  chatConfig: {
    whatsapp: null,
    messenger: null,
  },
});

const getValidation = () => {
  const isField1Empty = country.value.gtm.trim() === "";
  const isField2Empty = country.value.gtm_auth.trim() === "";

  if (isField1Empty && isField2Empty) {
    return true; // both fields are empty
  }
  if (!isField1Empty && !isField2Empty) {
    return true; // both fields have data
  }

  return "Both GTM ID and Auth Token must be either filled or empty"; // error message
};

const imageRules = [
  { text: "Maximum image size is 100KB", icon: "mdi-check" },
  { text: "Image dimension is 255 X 153", icon: "mdi-check" },
  { text: "Image type should be WEBP", icon: "mdi-check" },
];

const headers = ref([
  { title: "#", key: "image", align: "start", sortable: false },
  { title: "Name", key: "name", align: "start", sortable: false },
  { title: "Domain", key: "domain", sortable: false, align: "start" },
  { title: "Gtm", key: "gtm", sortable: false, align: "start" },
  {
    title: "Primary Language",
    key: "languageId",
    sortable: false,
    align: "start",
  },
  { title: "Languages", key: "languagesId", sortable: false, align: "start" },
  { title: "Code", key: "code", sortable: false, align: "start" },
  { title: "Currency", key: "currency", sortable: false, align: "start" },
  { title: "Tax", key: "taxPercentage", sortable: false, align: "start" },
  { title: "Shipping", key: "shippingCharge", sortable: false, align: "start" },
  { title: "COD", key: "codCharge", sortable: false, align: "start" },
  {
    title: "Shipping Charge Free",
    key: "shippingChargeFree",
    sortable: false,
    align: "start",
  },
  { title: "Status", key: "status", sortable: false, align: "start" },
  { title: "Action", key: "action", align: "center", sortable: false },
]);
const options = ref({
  page: 1,
  itemsPerPage: 20,
  sortBy: ["name"],
  sortDesc: [false],
  search: "",
});
const search = ref("");
const name = ref("");
const countries = ref([]);
function onClick() {
  if (!search) return;
  store.getCountries();
}

const clearSearch = () => {
  fetchCountries();
};

const fetchCountries = async () => {
  const { page, itemsPerPage, sortBy, sortDesc, search } = options.value;
  const order = sortDesc[0] ? "DESC" : "ASC";
  const sortKey = sortBy[0] || "name";
  loading.value = true;
  await store.getCountries(order, page, itemsPerPage, sortKey, search);
  countries.value = store.countries;
  loading.value = false;
};

// onMounted(fetchCountries);
// watch(search, () => {
//   fetchCountries();
// });
watch(name, (val, oldValue) => {
  let arraydata = [];
  countries.value.filter((item) => {
    if (item.name.toLowerCase().includes(val)) {
      arraydata.push(item);
    }
  });
  if (val.length === 0) {
    arraydata = store.countries;
  }
  countries.value = arraydata;
});

//***************************Methods******************//

const onSubmit = () => {
  if (!form.value) return;

  loading.value = true;
  store
    .addUpdateCountry(country.value)
    .then((res: any) => {
      snackbar.add({
        type: res.success ? "success" : "error",
        text: res.message,
      });
    })
    .catch((err) => {
      snackbar.add({
        type: "error",
        text: err,
      });
    });

  loading.value = false;
  resetForm();
};
const updateStatus = async (item: any) => {
  const country = countries.value.find((c: any) => c.id === item.id);
  if (country) {
    const itemData: any = {
      id: country.id,
      isActive: country.isActive,
      currencyId: item.currencyId,
      taxPercentage: item.taxPercentage,
      // isDefined: country.isDefined,s
    };
    store
      .addUpdateCountry(itemData)
      .then(async (res: any) => {
        snackbar.add({
          type: res.success ? "success" : "error",
          text: res.success
            ? "Country status updated successfully!"
            : "Failed to update country status",
        });
        await fetchCountries();
      })
      .catch((err) => {
        snackbar.add({
          type: "error",
          text: "Error! Failed to update country status",
        });
      });
  } else {
    // console.log("Courier not found");
  }
};

const handleImgUpload = (value: any) => {
  country.value.imageGalleryId = value;
};

const deleteItem = (id: number) => {
  store.deleteCountry(id).then((res) => {
    snackbar.add({
      type: "error",
      text: res,
    });
  });
};

const resetForm = () => {
  countryForm.value.reset();
  country.value.id = null;
  country.value.name = "";
  country.value.code = "";
  country.value.description = "";
  country.value.codCharge = 0;
  country.value.taxPercentage = 0;
  country.value.shippingCharge = 0;
  country.value.imageGalleryId = null;
  country.value.facebook = "";
  country.value.x = "";
  country.value.tiktok = "";
  country.value.linkedin = "";
  country.value.instagram = "";
  country.value.youtube = "";
};

//***************************Lifecycle Hooks******************//
const getCurrencies = computed(() => {
  return currStore.currencies;
});

const languagesOption = [...LanguagesOptionArr];

onMounted(() => {
  // fetchCountries();
  currStore.getCurrencies("ASC", 1, 100);
});

const confirmDialog = ref(false);
const itemToDelete = ref<number | null>(null);
const handleDeleteConfirmation = async () => {
  if (itemToDelete.value !== null) {
    const productId = itemToDelete.value;
    // Call the delete product API
    const res = await store.deleteCountry(productId);
    // Handle response
    if (res) {
      snackbar.add({
        type: "success",
        text: "Country deleted successfully!",
      });
      confirmDialog.value = false;
    } else {
      snackbar.add({
        type: "error",
        text: "Failed to delete Country.",
      });
    }
  }
};

watch(
  () => country.value.chatConfig,
  (newVal) => {
    if (!newVal) {
      country.value.chatConfig = { whatsapp: null, messenger: null };
    }
  },
  { immediate: true }
);
</script>
