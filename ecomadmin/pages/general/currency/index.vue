<template>
  <SharedBaseBreadcrumb
      :title="page.title"
      :breadcrumbs="breadcrumbs"
  ></SharedBaseBreadcrumb>

  <v-row>
    <v-col cols="4">
      <SharedUiParentCard :title="currency.id ? 'Edit ' + currency.currency : 'New Currency'">
        <v-form v-model="form" @submit.prevent="onSubmit" ref="currencyForm">
          <FormElementsCommonFieldContainer label="Currency" :required="true">
            <v-text-field v-model="currency.currency" :rules="[ val => REQUIRED_RULE(val) ]"></v-text-field>
          </FormElementsCommonFieldContainer>

          <FormElementsCommonFieldContainer label="Currency Symbol" :required="true">
            <v-text-field v-model="currency.currencySymbol" :rules="[ val => REQUIRED_RULE(val) ]"></v-text-field>
          </FormElementsCommonFieldContainer>

          <v-divider class="mb-3"></v-divider>
          <v-btn
              :disabled="!form"
              :loading="loading"
              color="primary"
              variant="tonal" class="px-6"
              type="submit"> {{ currency.id ? 'Update' : 'Add New' }} Currency
          </v-btn>

          <v-btn
              color="error"
              variant="tonal" class="px-6 ms-2"
              @click="resetForm"
          > Reset
          </v-btn>
        </v-form>
      </SharedUiParentCard>
    </v-col>
    <v-col cols="8">
      <SharedUiParentCard>

        <div class="d-flex justify-space-between align-center">
          <h2>Currency</h2>
          <div class="w-50">
            <v-text-field :loading="store.searchLoading" append-inner-icon="mdi-magnify" density="compact"
                          label="Search category" variant="outlined" hide-details single-line @onChange:append-inner="onClick"
                          v-model="options.search" clearable @click:clear="clearSearch"></v-text-field>
          </div>

        </div>

        <v-data-table-server
            v-model:page="options.page"
            v-model:items-per-page="options.itemsPerPage"
            :headers="headers"
            :items="currencies"
            :items-length="store.pagination.itemCount"
            :search="options.search"
            :loading="loading"
            item-value="name" @update:options="fetchCountries">
          <template v-slot:thead>

            <tr>
              <td>
                <v-text-field v-model="name" class="ma-1" density="compact" placeholder="Search curency..."
                              hide-details></v-text-field>
              </td>

            </tr>
          </template>
          <template v-slot:item.description="{ item }">
            <span v-html="item?.description"></span>
          </template>
          <template v-slot:item.status="{ item }">
            <v-chip :color="item?.isActive ? 'success' : 'error'" variant="elevated">
              {{ item?.isActive ? 'Active' : 'Inactive' }}
            </v-chip>
          </template>
          <template v-slot:item.action="{ item }">
            <div class="text-end">

              <v-menu>
                <template v-slot:activator="{ props }">
                  <v-btn class="me-2" variant="tonal" size="small" icon="mdi-dots-vertical" v-bind="props"></v-btn>
                </template>

                <v-list>
                  <v-list-item>
                    <v-btn icon color="warning" class="me-2" variant="tonal" size="small"
                           @click="editPayloadItem(item, currency)">
                      <PencilIcon size="20"/>
                    </v-btn>
                  </v-list-item>
                  <v-list-item>
                    <v-btn icon color='error' variant="tonal" size="small" @click="confirmDialog = true; itemToDelete = item.id">
                      <TrashIcon size="20"/>
                    </v-btn>
                  </v-list-item>

                </v-list>
              </v-menu>
            </div>
          </template>
        </v-data-table-server>

      </SharedUiParentCard>
    </v-col>
  </v-row>
  <ConformationModal v-model:dialog="confirmDialog" @confirm-delete="handleDeleteConfirmation" />

</template>

<script setup lang="ts">
//***************************Imports******************//
import { PencilIcon, TrashIcon } from "vue-tabler-icons";
import {computed, onMounted, ref, watch} from 'vue';
import {useCurrencyStore} from "~/stores/others/currency";
import {editPayloadItem} from "~/utils/helpers/functions";
import {REQUIRED_RULE} from "~/utils/formRules";
import ConformationModal from "~/components/modals/ConformationModal.vue";

//***************************Variables******************//
const store = useCurrencyStore();
const snackbar = useSnackbar();
const theads = ['Currency', 'CurrencySymbol', 'Status', 'Action'];
const page = ref({title: "Manage Currency"});

const breadcrumbs = ref([
  {
    text: "Dashboard",
    disabled: false,
    to: "/dashboard",
  },
  {
    text: "Currency",
    disabled: true,
    to: "",
  },
]);
const form = ref(false);
const currencyForm = ref();
const loading = ref(false);


const headers = ref([

  { title: 'Currency', key: 'currency', sortable: false, align: 'start' },
  { title: 'CurrencySymbol', key: 'currencySymbol', sortable: false, align: 'start' },
  { title: 'Status', key: 'status', sortable: false, align: 'start' },
  { title: 'Action', key: 'action', align: 'center', sortable: false },
])
const options = ref({
  page: 1,
  itemsPerPage: 20,
  sortBy: ['name'],
  sortDesc: [false],
  search: '',
});
const search = ref('');
const name = ref('');
const currencies = ref([]);
function onClick() {
  if (!search) return;
  store.getCurrencies();
}

const clearSearch = () => {
  fetchCountries();
}

const fetchCountries = async () => {

  const {page, itemsPerPage, sortBy, sortDesc, search} = options.value;
  const order = sortDesc[0] ? 'DESC' : 'ASC';
  const sortKey = sortBy[0] || 'name';
  loading.value = true;
  await store.getCurrencies(order, page, itemsPerPage, sortKey, search);
  currencies.value = store.currencies;
  loading.value = false;

}
// onMounted(fetchCountries);
// watch(search, () => {
//   fetchCountries();
// });
watch(name, (val, oldValue) => {
  let arraydata = [];
  currencies.value.filter((item) => {
    if (item.currency.toLowerCase().includes(val)) {
      arraydata.push(item);
    }

  })
  if (val.length === 0) {
    arraydata = store.currencies;
  }
  currencies.value = arraydata;
})


const currency = ref({
  id: null,
  currency: '',
  currencySymbol: ''
})

const rules = [
  value => {
    if (value) return true;
    return 'You must enter a currency name'
  }
];

//***************************Methods******************//
const onSubmit = () => {

  if (!form.value) return;
  loading.value = true

  store.addUpdateCurrency({
    id: currency.value.id,
    currency: currency.value.currency,
    currencySymbol: currency.value.currencySymbol
  }).then(res => {
    snackbar.add({
      type: res.success ? 'success' : 'error',
      text: res.message
    })
  }).catch(error => {
    snackbar.add({
      type: 'error',
      text: error
    })
  })

  loading.value = false
  resetForm()
}

// const fetchCountries = (order = 'ASC', page = 1, take = 5) => {
//   store.getCurrencies({order: order, page: page, take: take})
// }

const deleteItem = (id: number) => {
  store.deleteCurrency(id).then(res => {
    console.log('res', res)
    snackbar.add({
      type: res ? "success" : "error",
      text: res,
    })
    fetchCountries()
  })
}

const resetForm = () => {
  currencyForm.value.reset()
  currency.value.id = null
  currency.value.currency = ''
  currency.value.currencySymbol = ''
}

//***************************Lifecycle Hooks******************//
const getCurrencies = computed(() => {
  return store.currencies;
});

// onMounted(() => {
//   fetchCountries();
// });


const confirmDialog = ref(false);
const itemToDelete = ref<number | null>(null);
const handleDeleteConfirmation = async () => {
  if (itemToDelete.value !== null) {
    const productId = itemToDelete.value;
    // Call the delete product API
    const res = await store.deleteCurrency(productId);
    // Handle response
    if (res) {
      snackbar.add({
        type: 'success',
        text: 'Item deleted successfully!',
      });
      confirmDialog.value = false;

    } else {
      snackbar.add({
        type: 'error',
        text: 'Failed to delete Item.',
      });
    }
  }
};


</script>
