<template>
  <SharedBaseBreadcrumb
      :title="page.title"
      :breadcrumbs="breadcrumbs"
  ></SharedBaseBreadcrumb>

  <v-row>
    <v-col cols="6">
      <template v-if="store.loading">
        <SharedUiLoader
            type="table-heading, table-thead, table-tbody, table-tfoot"
        />
      </template>
      <SharedUiParentCard title="Quick Links" v-else>
        <template v-if="getQuickLinks.length > 0">
          <TableComponentsSimpleTable :theads="tableHeads">
            <template #default>
              <tr
                  v-for="item in getQuickLinks"
                  :key="item.id"
                  class="month-item"
              >
                <td>
                  <h6
                      class="text-h6 font-weight-medium text-medium-emphasis text-no-wrap"
                  >
                    {{ item.name }}
                  </h6>
                </td>
                <td>
                  {{item.slug}}
                </td>
                <td>
                  <v-btn
                      icon
                      color="warning"
                      class="me-2"
                      variant="tonal"
                      size="small"
                      @click="editItem(item)"
                  >
                    <PencilIcon size="20"/>
                  </v-btn>

                  <v-btn icon color="info" class="me-2" variant="tonal" size="small"
                                @click="localizeAttribute(item)">
                            <LanguageIcon size="20"/>
                          </v-btn>
                </td>
              </tr>
            </template>
          </TableComponentsSimpleTable>
        </template>
        <template v-else> Sorry no Data Found.</template>
      </SharedUiParentCard>
    </v-col>

    <v-col cols="6">
      <SharedUiParentCard :title="quickLinksForm.name">
        <v-form v-model="form" @submit.prevent="onSubmit">

          <FormElementsCommonFieldContainer label="Slug" class="mb-4">
            <v-text-field v-model="quickLinksForm.slug"></v-text-field>
          </FormElementsCommonFieldContainer>
          <FormElementsCommonFieldContainer label="Details" class="mb-4">
            <FormElementsRichTextEditor v-model="quickLinksForm.details" />
          </FormElementsCommonFieldContainer>


          <v-divider class="mb-3"></v-divider>
          <v-btn
              :disabled="!form"
              :loading="loading"
              color="primary"
              variant="tonal" class="px-6"
              type="submit"> Update
          </v-btn>
        </v-form>
      </SharedUiParentCard>
    </v-col>

  </v-row>
  <GeneralLocalizeQuickLinkLocalize ref="localizationDialog" />
</template>

<script setup lang="ts">
//***************************Imports******************//
import { PencilIcon, TrashIcon, LanguageIcon } from "vue-tabler-icons";
import {computed, onMounted, ref} from 'vue';
import {editPayloadItem} from "~/utils/helpers/functions";
import {useQuickLinksStore} from "~/stores/others/quick-links";

//***************************Variables******************//
const store = useQuickLinksStore();
const snackbar = useSnackbar();
const tableHeads = ['Name', 'Slug', 'Action'];
const page = ref({title: "Quick Links"});

const breadcrumbs = ref([
  {
    text: "Dashboard",
    disabled: false,
    to: "/dashboard",
  },
  {
    text: "Quick Links",
    disabled: true,
    to: "",
  },
]);
const form = ref(false);
const loading = ref(false);

const quickLinksForm = ref({
  id: null,
  name: '',
  slug: '',
  details: '',
})


//***************************Methods******************//
const onSubmit = () => {

  if (!form.value) return;
  loading.value = true

  store.addUpdateQuickLinks(quickLinksForm.value).then((res: any )=> {
    snackbar.add({
      type: res.success ? 'success' : 'error',
      text: res.message
    })

  store.getQuickLinks();
    loading.value = false;
  }).catch(error => {
    snackbar.add({
      type: 'error',
      text: error
    })
  })


  // resetForm()
}


const editItem = (item: any) => {
  // store.deleteCountry(id).then((res) => {
  //   snackbar.add({
  //     type: "error",
  //     text: res,
  //   });
  // });
  quickLinksForm.value.id = item.id;
  quickLinksForm.value.name = item.name;
  quickLinksForm.value.slug = item.slug;
  quickLinksForm.value.details = item.details;
};


// const fetchCities = (order = 'ASC', page = 1, take = 5) => {
//   cityStore.getCities({order: order, page: page, take: take})
// }

// const resetForm = () => {
//   city.value.id = null
//   city.value.name = ''
//   city.value.short = ''
//   city.value.stateId = null;
//   city.value.code = ''
// }

//***************************Lifecycle Hooks******************//
const getQuickLinks = computed(() => store.quickLinks);

// const getCities = computed(() => cityStore.cities)

onMounted(() => {
  // fetchCities();
  store.getQuickLinks();
});

const localizationDialog = ref(null);
const localizeAttribute = (item: any) => {
  item.description = item.details;
  localizationDialog.value?.show(item);
};

</script>
