<template>
  <SharedBaseBreadcrumb :title="page.title" :breadcrumbs="breadcrumbs">
  </SharedBaseBreadcrumb>

  <v-row>
    <v-col cols="12" md="6">
      <v-row>
        <v-col cols="12">
          <!-- :title="dataForm.id ? 'Edit ' + dataForm.title : 'Add New Tag Product'" -->
          <SharedUiParentCardSolid>
            <v-form v-model="dataForms" @submit.prevent="onSubmit" ref="dataForms">
              <h5 class="text-body-1 text-md-h5">
                {{ dataForm.id ? "Update" : "Add New" }} Coupon
              </h5>
              <v-row class="align-center mt-3">

                <v-col cols="6" sm="4" class="py-1">
                  <v-text-field v-model="dataForm.name" :rules="[ val => REQUIRED_RULE(val) ]" density="compact" label="Name">
                  </v-text-field>
                </v-col>

                <v-col cols="6" sm="4" class="py-1">
                  <v-text-field v-model="dataForm.maxUsage" density="compact" label="Max usage">
                  </v-text-field>
                </v-col>

                <v-col cols="6" sm="4" class="py-1">
                  <v-text-field v-model="dataForm.usagePerUser" density="compact" label="Usage per user">
                  </v-text-field>
                </v-col>
                
                <v-col cols="6" sm="4" class="py-1">
                  <v-btn :disabled="!dataForm" :loading="loading" color="primary" variant="flat" class="px-6" type="submit">
                    {{ dataForm.id ? "Update" : "Save" }}
                  </v-btn>
                </v-col>
              </v-row>
            </v-form>
          </SharedUiParentCardSolid>
        </v-col>
        <v-col cols="12">
          <SharedUiParentCardSolid>
            <div class="d-flex justify-space-between align-center">
              <h5 class="text-h5">Coupons</h5>
              <div class="w-50">
                <v-text-field :loading="store.searchLoading" append-inner-icon="mdi-magnify" density="compact"
                  label="Search coupons" variant="outlined" hide-details single-line @onChange:append-inner="onClick"
                  v-model="options.search" clearable @click:clear="clearSearch">
                </v-text-field>
              </div>
            </div>

            <div class="overflow-x-auto">
              <v-data-table-server v-model:page="options.page" v-model:items-per-page="options.itemsPerPage" :headers="headers" :items="coupons" :items-length="store.pagination.itemCount" :search="options.search" :loading="loading" item-value="name" 
                @update:options="fetchCouponList" style="min-width: 600px;">
                
                <template v-slot:thead>
                  <tr>
                    <td>
                      <v-text-field v-model="name" class="ma-1" density="compact" placeholder="Search curency..." hide-details>
                      </v-text-field>
                    </td>
                  </tr>
                </template>

                <template v-slot:item.description="{ item }">
                  <span v-html="item?.description"></span>
                </template>

                <template v-slot:item.status="{ item }">
                  <v-switch v-model="item.isActive" color="primary" defaults-target="success" 
                    density="compact"
                    hide-details flat
                    @update:model-value="onChangeStatus(item)">
                    <template #label>
                      <span class="text-caption ms-2">
                        {{item.isActive ? 'Active': 'Inactive'}}
                      </span>
                    </template>
                  </v-switch>
                </template>

                <template v-slot:item.action="{ item }">
                  <div class="text-end">
                    <v-menu>
                      <template v-slot:activator="{ props }">
                        <v-btn class="me-2" variant="flat" size="x-small" icon="mdi-dots-vertical" v-bind="props"></v-btn>
                      </template>

                      <v-list>
                        <v-list-item>
                          <v-btn icon color="warning" class="me-2" variant="flat" size="x-small" @click="editPayloadItem(item, dataForm)">
                            <PencilIcon size="20"/>
                          </v-btn>
                        </v-list-item>
                        <v-list-item>
                          <v-btn icon color="success" class="me-2" variant="flat" size="x-small" @click="tagItem(item)">
                            <TagIcon size="20"/>
                          </v-btn>
                        </v-list-item>
                        <v-list-item>
                          <v-btn icon color="error" variant="flat" size="x-small" @click="confirmDialog = true; itemToDelete = item.id">
                            <TrashIcon size="20"/>
                          </v-btn>
                        </v-list-item>
                      </v-list>
                    </v-menu>
                  </div>
                </template>

              </v-data-table-server>
            </div>

          </SharedUiParentCardSolid>
        </v-col>
      </v-row>
    </v-col>

    <v-col cols="12" md="6" v-if="isEnableTaggingCoupon">
      <template v-if="store.loading">
        <SharedUiLoader type="table-heading, table-thead, table-tbody, table-tfoot"/>
      </template>

      <SharedUiParentCardSolid v-else>
        <h5 class="text-body-1 text-md-h5">
          {{couponCountryDataForm?.id ? `Update country for` : 'Add new country for'}}
          <span v-if="store.getCoupon(couponCountryDataForm?.couponId)[0]?.name" class="text-body-2 font-weight-medium text-success mx-2">
            "{{store.getCoupon(couponCountryDataForm?.couponId)[0]?.name}}"
          </span> coupon
        </h5>
        <v-form v-model="couponCountryDataForms" @submit.prevent="onTagProduct" ref="couponCountryDataForms" class="mt-7">
          <v-row class="align-center">
            <!-- <v-col cols="3">
              <FormElementsCommonFieldContainer label="Select Coupon" :required="true">
              <v-select
              :items="getAllActiveCoupons"
              item-title="name"
              item-value="id"
              v-model="couponCountryDataForm.couponId"
              :rules="[ val => SELECT_REQUIRED_RULE(val) ]"
              placeholder="Select Coupon"></v-select>
            </FormElementsCommonFieldContainer>
            </v-col> -->
            
            <v-col cols="4" class="py-1">
              <v-select :items="getCountries" item-title="name" item-value="id" v-model="couponCountryDataForm.countryId" 
                :rules="[ val => SELECT_REQUIRED_RULE(val) ]" label="Select Country"
                placeholder="Select Country" density="compact">
              </v-select>
            </v-col>

            <v-col cols="4" class="py-1">
              <v-select :items="getTypeList" item-title="name" item-value="value" v-model="couponCountryDataForm.type"
                :rules="[ val => SELECT_REQUIRED_RULE(val) ]" label="Select Discount Type"
                placeholder="Select Discount Type" density="compact">
              </v-select>
            </v-col>

            <v-col cols="4" class="py-1">
              <v-text-field v-model="couponCountryDataForm.amount" :rules="[(val) => REQUIRED_RULE(val)]" density="compact" label="Amount">
              </v-text-field>
            </v-col>
            
            <v-col cols="6" class="py-1">
              <v-text-field type="datetime-local" v-model="couponCountryDataForm.startDate" @change="changeStartDate"
                :min="getDateForDatetimeLocal(new Date())" density="compact">
              </v-text-field>

              <!-- <v-menu :close-on-content-click="false" v-model="dateMenu" offset-y transition="scale-transition">
                
                <template #activator="{ props }">
                  <v-text-field v-model="couponCountryDataForm.startDate" v-bind="props" label="Start Date" prepend-inner-icon="mdi-calendar"
                    :rules="[(val) => REQUIRED_RULE(val)]" density="compact"
                    readonly>
                  </v-text-field>
                </template>

                <v-card>
                  <v-date-picker v-model="selectedStartDate"  no-title show-adjacent-months border :elevation="4">

                    <template #title class="d-none">
                    </template>

                    <template #header class="d-none">
                    </template>

                    <template #actions>
                      <v-btn @click="changeStartDate" color="primary" variant="tonal">
                        submit
                      </v-btn>
                      <v-btn @click="dateMenu=false">
                        close
                      </v-btn>
                    </template>

                  </v-date-picker> 
                </v-card>
              </v-menu>
               -->
            </v-col>
            <v-col cols="6" class="py-1">
              <v-text-field type="datetime-local" v-model="couponCountryDataForm.endDate" @change="changeEndDate"
                :min="getDateForDatetimeLocal(new Date())" density="compact">
              </v-text-field>
              <!-- <v-menu :close-on-content-click="false" v-model="dateEndMenu" offset-y transition="scale-transition">
                <template #activator="{ props }">
                  <v-text-field v-model="couponCountryDataForm.endDate" v-bind="props" label="End Date" prepend-inner-icon="mdi-calendar"
                    :rules="[(val) => REQUIRED_RULE(val)]" density="compact"
                    readonly>
                  </v-text-field>
                </template>

                <v-card>
                  <v-date-picker  v-model="selectedEndDate" no-title show-adjacent-months border :elevation="4">

                    <template #title class="d-none">
                    </template>

                    <template #header class="d-none">
                    </template>

                    <template #actions>
                      <v-btn @click="changeEndDate" color="primary" variant="flat">
                        submit
                      </v-btn>
                      <v-btn @click="dateEndMenu=false">
                        close
                      </v-btn>
                    </template>

                  </v-date-picker> 
                </v-card>
              </v-menu> -->
              <!-- <input type="date" v-model="selectedEndDate" @change="changeEndDate" class="border rounded pa-3"/> -->
            </v-col>
            <v-col cols="12" class="py-1">
              <v-btn :loading="loading" color="primary" variant="flat" class="px-6" type="submit">
                {{couponCountryDataForm?.id ? 'Update': 'Save' }}
              </v-btn>
              <v-btn @click="resetCouponCountryDataForm" class="ms-2">Reset</v-btn>
            </v-col>
          </v-row>
        </v-form>
        <v-divider class="mt-2"></v-divider>
        <template v-if="getCouponCountryList?.length> 0">
          <TableComponentsSimpleTable :theads="tableCouponCountryHeads">
            <template #default>
              <tr v-for="item in getCouponCountryList" :key="item.id" class="month-item">
                <td>
                    {{ item?.country?.name }}
                </td>
                <td>
                  {{ item?.coupon?.name }}
                </td>
                <td>
                  {{ item?.type }}
                </td>
                <td>
                  {{ formatDateTime(item?.startDate) }}
                </td>
                <td>
                  {{ formatDateTime(item?.endDate) }}
                </td>
                <td>
                  {{ item?.amount }}
                </td>
                <td>
                  <v-menu>
                    <template v-slot:activator="{ props }">
                      <v-btn class="me-2" variant="flat" size="x-small" icon="mdi-dots-vertical" v-bind="props"></v-btn>
                    </template>

                    <v-list>
                      <v-list-item>
                        <v-btn @click="editPayloadItem(item, couponCountryDataForm)" icon="mdi-pencil" color="warning" class="me-2" variant="flat" size="x-small">
                        </v-btn>
                      </v-list-item>
                      <v-list-item>
                        <v-btn @click="deleteCouponMetaItem(item?.id)" icon="mdi-delete" color="error" variant="flat" size="x-small">
                        </v-btn>
                      </v-list-item>
                    </v-list>
                  </v-menu>
                </td>
              </tr>
            </template>
          </TableComponentsSimpleTable>

          <!-- <FormElementsPagination
              :pagination="store.pagination"
              @get-event="fetchCouponsWithCountry(item?.id)"
          /> -->
        </template>
        <template v-else> Sorry no Data Found.</template>

      </SharedUiParentCardSolid>
    </v-col>
        
  </v-row>
  <ConformationModal v-model:dialog="confirmDialog" @confirm-delete="handleDeleteConfirmation" />

</template>

<script setup lang="ts">
import { PencilIcon, TrashIcon, TagIcon } from "vue-tabler-icons";
import {computed, onMounted, ref, watch} from "vue";
import {formatDateTime, editPayloadItem, getDateForDatetimeLocal} from "~/utils/helpers/functions";
import {REQUIRED_RULE} from "~/utils/formRules";
import { useCountryStore } from "~/stores/others/country";
import { useCouponStore } from "~/stores/others/coupon";
import ConformationModal from "~/components/modals/ConformationModal.vue";


const store = useCouponStore();
const countryStore = useCountryStore();
// const currStore = useCurrencyStore();
const page = ref({title: "Manage Coupon"});

const breadcrumbs = ref([
  {
    text: "Dashboard",
    disabled: false,
    to: "/dashboard",
  },
  {
    text: "Coupon",
    disabled: true,
    to: "",
  },
]);

const tableHeads = ["Coupon Name", "Status", "Action"];
const tableCouponCountryHeads = ["Country", "Coupon", "Type", "Start Date", "End Date", "Amount","Action"];

const getTypeList = ref([
  {
    value: "FLAT",
    name: 'FLAT',
  },
  {
    value: "PERCENTAGE",
    name: 'PERCENTAGE',
  },
]);

const isEnableTaggingCoupon = ref(false);

const snackbar = useSnackbar();
const dataForms = ref();
const couponCountryDataForms = ref();
const loading = ref(false);
const dateMenu = ref(false);
const dateEndMenu = ref(false);
const dateRange = ref([]);
const selectedStartDate = ref(null);
const selectedEndDate = ref(null);

const changeStartDate = () => {
  if (selectedStartDate.value) {
    const startDate = new Date(selectedStartDate.value);
    couponCountryDataForm.value.startDate = `${startDate.getFullYear()}-${(startDate.getMonth() + 1)
      .toString()
      .padStart(2, "0")}-${startDate
      .getDate()
      .toString()
      .padStart(2, "0")} ${startDate
      .getHours()
      .toString()
      .padStart(2, "0")}:${startDate
      .getMinutes()
      .toString()
      .padStart(2, "0")}:${startDate
      .getSeconds()
      .toString()
      .padStart(2, "0")}`;
  }
  dateMenu.value = false;
};

const changeEndDate = () => {
  console.log("===achange end date");
  if (selectedEndDate.value) {
    const endDate = new Date(selectedEndDate.value);
    couponCountryDataForm.value.endDate = `${endDate.getFullYear()}-${(endDate.getMonth() + 1)
      .toString()
      .padStart(2, "0")}-${endDate
      .getDate()
      .toString()
      .padStart(2, "0")} ${endDate
      .getHours()
      .toString()
      .padStart(2, "0")}:${endDate
      .getMinutes()
      .toString()
      .padStart(2, "0")}:${endDate
      .getSeconds()
      .toString()
      .padStart(2, "0")}`;
  }
  dateEndMenu.value = false;
};


const dataForm = ref({
  id: null,
  name: null,
  isActive: true,
  maxUsage: 0,
  usagePerUser: 0,
});

const couponCountryDataForm = ref({
  id: null,
  couponId: 0,
  countryId: 0 || undefined,
  amount: 0 || undefined,
  type: null,
  isActive: true,
  startDate: null,
  endDate: null,
});

// const imageRules = [
//   {text: 'Maximum image size is 200KB', icon: 'mdi-check'},
//   {text: 'Image dimension is 300 X 300', icon: 'mdi-check'},
//   {text: 'Image type should be WEBP', icon: 'mdi-check'},
// ]


const headers = ref([
  { title: 'Coupon Name', key: 'name', sortable: false, align: 'start' },
  { title: 'Status', key: 'status', sortable: false, align: 'start' },
  { title: 'Max Usages', key: 'maxUsage', sortable: false, align: 'start' },
  { title: 'Usage Per User', key: 'usagePerUser', sortable: false, align: 'start' },
  { title: 'Action', key: 'action', align: 'center', sortable: false },
])
const options = ref({
  page: 1,
  itemsPerPage: 20,
  sortBy: ['name'],
  sortDesc: [false],
  search: '',
});
const search = ref('');
const name = ref('');
const coupons = ref([]);
function onClick() {
  if (!search) return;
  store.getCurrencies();
}

const clearSearch = () => {
  fetchCouponList();
}

const fetchCouponList = async () => {

  const {page, itemsPerPage, sortBy, sortDesc, search} = options.value;
  const order = sortDesc[0] ? 'DESC' : 'ASC';
  const sortKey = sortBy[0] || 'name';
  loading.value = true;
  await store.getCoupons(order, page, itemsPerPage, sortKey, search);
  coupons.value = store.coupons;
  loading.value = false;

}
// onMounted(fetchCountries);
// watch(search, () => {
//   fetchCouponList();
// });
watch(name, (val, oldValue) => {
  let arraydata = [];
  coupons.value.filter((item) => {
    if (item.name.toLowerCase().includes(val)) {
      arraydata.push(item);
    }

  })
  if (val.length === 0) {
    arraydata = store.coupons;
  }
  coupons.value = arraydata;
})

watch(()=> couponCountryDataForm.value, (newVal, oldVal)=>{
  if(newVal && newVal?.id){
    if(newVal?.startDate){
      const startDate = new Date(newVal?.startDate);
      // Format to 'YYYY-MM-DDTHH:mm' (without timezone)
      couponCountryDataForm.value.startDate = `${startDate.getFullYear()}-${(startDate.getMonth() + 1).toString().padStart(2, '0')}-${startDate.getDate().toString().padStart(2, '0')}T${startDate.getHours().toString().padStart(2, '0')}:${startDate.getMinutes().toString().padStart(2, '0')}`;
    }
    
    if(newVal?.endDate){
      const endDate = new Date(newVal?.endDate);
      // Format to 'YYYY-MM-DDTHH:mm' (without timezone)
      couponCountryDataForm.value.endDate = `${endDate.getFullYear()}-${(endDate.getMonth() + 1).toString().padStart(2, '0')}-${endDate.getDate().toString().padStart(2, '0')}T${endDate.getHours().toString().padStart(2, '0')}:${endDate.getMinutes().toString().padStart(2, '0')}`;
    }

  }
}, {deep:true})


//***************************Methods******************//

const onSubmit = () => {
  if (!dataForms.value) return;

  loading.value = true;
  store.addUpdateCoupon(dataForm.value)
      .then((res: any) => {
        snackbar.add({
          type: (res && res?.success && res?.data) ? "success" : ((res && res?.success && !res?.data))? "warning":"error",
          text: res?.message,
        });

        resetForm();
      })
      .catch((err) => {
        snackbar.add({
          type: "error",
          text: err,
        });
      });

  loading.value = false;
};

const onTagProduct = async () => {
  const { valid } = await couponCountryDataForms.value.validate(); // Manually validate
  
  if (!valid) {
    // Stop if validation fails
    return; 
  }

  loading.value = true;

  store.addUpdateTaggingCouponCountry(couponCountryDataForm.value)
      .then((res: any) => {
        console.log('res',res);

        snackbar.add({
          type: (res && res?.success && res?.data) ? "success" : ((res && res?.success && !res?.data))? "warning":"error",
          text: res?.message,
        });

        resetCouponCountryDataForm();
      })
      .catch((err) => {
        snackbar.add({
          type: "error",
          text: err,
        });
      });

  loading.value = false;
};


// const fetchCouponList = (order = "ASC", page = 1, take = 10) => {
//   store.getCoupons({order: order, page: page, take: take});
// };

const fetchCouponsWithCountry = async (id: number) => {
  await store.fetchCouponsWithCountry({couponId: id,order: 'ASC', page: 1, take: 50});
};

const deleteItem = (id: number) => {
  store.deleteCoupon(id).then((res) => {
    snackbar.add({
      type: (res) ? "success" : "error",
      text: res,
    });
  });
};

const deleteCouponMetaItem = (id: number) => {
  store.deleteCouponMetaItem(id).then((res) => {
    snackbar.add({
      type: (res) ? "success" : "error",
      text: res,
    });
  });
};

const onChangeStatus = (item: any) => {
  store.changeStatusCoupon(item?.id, item?.isActive).then((res) => {
    snackbar.add({
        type: (res && res?.success && res?.data) ? "success" : ((res && res?.success && !res?.data))? "warning":"error",
        text: res?.message,
    });
  });
};

const tagItem = async (item: any) => {
    isEnableTaggingCoupon.value = true;
    couponCountryDataForm.value.couponId = item.id;
  fetchCouponsWithCountry(item?.id);
  countryStore.getAllCountries();
  store.getAllActiveCoupons();
  store.fetchCouponsWithCountry;
  // productDataForm.value.eventActivityHistoryId = item?.id;
  // productDataForm.value.countryId = item?.countryId;
};

const resetForm = () => {
  dataForms.value.reset();

  dataForm.value.id= null;
  dataForm.value.name= null,
  // dataForm.value.eventActivityId= null,
  // dataForm.value.startDate= null,
  // dataForm.value.endDate= null,
  dataForm.value.isActive= true;
};

const resetCouponCountryDataForm = () => {
  couponCountryDataForms.value.reset();

  couponCountryDataForm.value.id = null;
  couponCountryDataForm.value.type = null;
  couponCountryDataForm.value.amount = 0 || undefined;
  couponCountryDataForm.value.startDate = null;
  couponCountryDataForm.value.endDate = null;

};

//***************************Lifecycle Hooks******************//
// const getEventActivities = computed(() => {
//   return currStore.eventActivities;
// });

onMounted(async () => {
  // fetchCouponList();

  // await countryStore.getAllCountries();
  // await store.getAllEventActivities();
  // await store.getEventActivityHistory();
  // currStore.getEventActivities({});
});

const confirmDialog = ref(false);
const itemToDelete = ref<number | null>(null);
const handleDeleteConfirmation = async () => {
  if (itemToDelete.value !== null) {
    const productId = itemToDelete.value;
    // Call the delete product API
    const res = await store.deleteCoupon(productId);
    // Handle response
    if (res) {
      snackbar.add({
        type: 'success',
        text: 'Item deleted successfully!',
      });
      confirmDialog.value = false;

    } else {
      snackbar.add({
        type: 'error',
        text: 'Failed to delete Item.',
      });
    }
  }
};


const getCountries = computed(() => countryStore.countries);
const getCouponList = computed(() => store.coupons);
const getCouponCountryList = computed(() => store.countryCoupons);
const getAllActiveCoupons = computed(() => store.couponsDwn);

</script>

<style scoped>
.v-table__wrapper{
  width: inherit;
}
</style>
