<template>
  <SharedBaseBreadcrumb
    :title="page.title"
    :breadcrumbs="breadcrumbs"
  ></SharedBaseBreadcrumb>

  <v-row>
    <v-col cols="12">
      <SharedUiParentCard
        :title="courier.id ? 'Edit ' + courier.name : 'Add New Courier'"
      >
        <v-form v-model="form" @submit.prevent="onSubmit" ref="courierForm">
          <v-row>
            <v-col cols="4">
              <FormElementsCommonFieldContainer label="Name" :required="true">
                <v-text-field
                  v-model="courier.name"
                  :rules="[(val) => REQUIRED_RULE(val)]"
                ></v-text-field>
              </FormElementsCommonFieldContainer>
            </v-col>
            <v-col cols="4">
              <FormElementsCommonFieldContainer
                label="Country"
                :required="true"
              >
                <v-select
                  :items="getCountries"
                  item-title="name"
                  item-value="id"
                  :rules="[(val) => REQUIRED_RULE(val)]"
                  v-model="courier.countryIds"
                  placeholder="Select Country"
                  multiple
                  clearable
                ></v-select>
              </FormElementsCommonFieldContainer>
            </v-col>

            <v-col cols="4">
              <FormElementsCommonFieldContainer label="API Key">
                <v-text-field v-model="courier.apiKey"></v-text-field>
              </FormElementsCommonFieldContainer>
            </v-col>
            <v-col cols="4">
              <FormElementsCommonFieldContainer
                :label="`Code (Must be unique)`"
                :required="!courier.id"
              >
                <v-text-field
                  :disabled="!!courier.id"
                  v-model="courier.code"
                  :rules="[
                    (val) =>
                      !courier.id && !courier.code ? REQUIRED_RULE(val) : true, // Required only if no id and no code
                  ]"
                ></v-text-field>
              </FormElementsCommonFieldContainer>
            </v-col>

            <v-col cols="4">
              <FormElementsCommonFieldContainer label="Price ( inside city )">
                <v-text-field
                  v-model="courier.priceInsideCity"
                  type="number"
                ></v-text-field>
              </FormElementsCommonFieldContainer>
            </v-col>

            <v-col cols="4">
              <FormElementsCommonFieldContainer label="Price ( Outside City )">
                <v-text-field
                  v-model="courier.priceOutsideCity"
                  type="number"
                ></v-text-field>
              </FormElementsCommonFieldContainer>
            </v-col>

            <v-col cols="4">
              <FormElementsCommonFieldContainer label="COD Charge">
                <v-text-field
                  v-model="courier.codCharge"
                  type="number"
                ></v-text-field>
              </FormElementsCommonFieldContainer>
            </v-col>

            <v-col cols="4">
              <FormElementsCommonFieldContainer label="Tracking Link">
                <v-text-field
                  v-model="courier.trackingLink"
                  type="text"
                ></v-text-field>
              </FormElementsCommonFieldContainer>
            </v-col>
          </v-row>
          <v-divider class="my-5"></v-divider>
          <v-btn
            :disabled="!form"
            :loading="loading"
            color="primary"
            variant="tonal"
            class="px-6"
            type="submit"
          >
            {{ courier.id ? "Update" : "Add New" }} Courier
          </v-btn>
          <v-btn
            color="error"
            variant="tonal"
            class="px-6 ms-2"
            @click="resetForm"
          >
            Reset
          </v-btn>
        </v-form>
      </SharedUiParentCard>
    </v-col>
    <!-- All avilable courier showing here -->
    <v-col cols="12">
      <SharedUiParentCard>
        <div class="d-flex justify-space-between align-center">
          <h2>Courier</h2>
          <div class="w-50">
            <v-text-field
              :loading="countryStore.searchLoading"
              append-inner-icon="mdi-magnify"
              density="compact"
              label="Search courier"
              variant="outlined"
              hide-details
              single-line
              @onChange:append-inner="onClick"
              v-model="options.search"
              clearable
              @click:clear="clearSearch"
            ></v-text-field>
          </div>
        </div>

        <v-data-table-server
          v-model:page="options.page"
          v-model:items-per-page="options.itemsPerPage"
          :headers="headers"
          :items="couriers"
          :items-length="couriers.length"
          :search="options.search"
          :loading="loading"
          item-value="name"
          @update:options="fetchCouriers"
        >
          <template v-slot:item.status="{ item }">
            <v-switch
              v-model="item.isActive"
              :label="item.isActive"
              @change="updateStatus(item)"
              :color="item.isActive ? 'success' : 'error'"
            ></v-switch>
          </template>
          <template v-slot:item.countryIds="{ item }">
            {{ item.countryIds?.length ? item.countryIds : "-" }}
          </template>
          <template v-slot:item.apiKey="{ item }">
            {{ item.apiKey ? item.apiKey : "-" }}
          </template>
          <template v-slot:item.priceInsideCity="{ item }">
            {{ item.priceInsideCity ? item.priceInsideCity : "0" }}
          </template>
          <template v-slot:item.priceOutsideCity="{ item }">
            {{ item.priceOutsideCity ? item.priceOutsideCity : "0" }}
          </template>
          <template v-slot:item.action="{ item }">
            <div class="text-end">
              <v-menu>
                <template v-slot:activator="{ props }">
                  <v-btn
                    class="me-2"
                    variant="tonal"
                    size="small"
                    icon="mdi-dots-vertical"
                    v-bind="props"
                  ></v-btn>
                </template>

                <v-list>
                  <v-list-item>
                    <v-btn
                      icon
                      color="warning"
                      class="me-2"
                      variant="tonal"
                      size="x-small"
                      @click="
                        editPayloadItem(item, courier);
                        courier.countryIds = item.countries.filter((c) => c.id);
                      "
                    >
                      <PencilIcon size="20" />
                    </v-btn>
                  </v-list-item>
                  <v-list-item>
                    <v-btn
                      icon
                      color="info"
                      class="me-2"
                      variant="tonal"
                      size="small"
                      @click="localizeCategory(item)"
                    >
                      <LanguageIcon size="20" />
                    </v-btn>
                  </v-list-item>
                  <v-list-item>
                    <v-btn
                      icon
                      color="error"
                      variant="tonal"
                      size="x-small"
                      @click="
                        confirmDialog = true;
                        itemToDelete = item.id;
                      "
                    >
                      <TrashIcon size="20" />
                    </v-btn>
                  </v-list-item>
                </v-list>
              </v-menu>
            </div>
          </template>
        </v-data-table-server>
      </SharedUiParentCard>
    </v-col>
  </v-row>
  <ConformationModal
    v-model:dialog="confirmDialog"
    @confirm-delete="handleDeleteConfirmation"
  />
  <ProductLocalizeCourierLocalize ref="localizationDialog" />
</template>
  
  <script setup lang="ts">
//***************************Imports******************//
import { PencilIcon, TrashIcon, LanguageIcon } from "vue-tabler-icons";
import { computed, onMounted, ref, watch } from "vue";
import { useCountryStore } from "~/stores/others/country";
import { useCurrencyStore } from "~/stores/others/currency";
import { editPayloadItem } from "~/utils/helpers/functions";
import { REQUIRED_RULE } from "~/utils/formRules";
import ConformationModal from "~/components/modals/ConformationModal.vue";
import { useCourierStore } from "~/stores/others/courier";

//***************************Variables******************//
const countryStore = useCountryStore();
const store = useCourierStore();
const currStore = useCurrencyStore();
const snackbar = useSnackbar();
const form = ref(false);
const courierForm = ref();
const loading = ref(false);
const page = ref({ title: "Manage Courier" });

const breadcrumbs = ref([
  {
    text: "Dashboard",
    disabled: false,
    to: "/dashboard",
  },
  {
    text: "Courier",
    disabled: true,
    to: "",
  },
]);
const courier = ref({
  id: 0,
  name: "",
  countryIds: [],
  apiKey: "",
  priceInsideCity: 0,
  priceOutsideCity: 0,
  codCharge: 0,
  code: "",
  trackingLink: "",
});

const headers = ref([
  { title: "Name", key: "name", align: "start", sortable: false },
  {
    title: "Country",
    key: "countries",
    sortable: false,
    align: "start",
    value: (item: { countries: { code: string }[] }) => {
      return item.countries.map((c) => c.code).join(", ");
    },
  },
  { title: "Api Key", key: "apiKey", sortable: false, align: "start" },
  {
    title: "Price(inside city)",
    key: "priceInsideCity",
    sortable: false,
    align: "start",
  },
  {
    title: "Price(outside city)",
    key: "priceOutsideCity",
    sortable: false,
    align: "start",
  },
  {
    title: "Tracking Link",
    key: "trackingLink",
    sortable: false,
    align: "start",
  },
  { title: "COD", key: "codCharge", sortable: false, align: "start" },
  { title: "Code", key: "code", sortable: false, align: "start" },
  { title: "Status", key: "status", sortable: false, align: "start" },
  { title: "Action", key: "action", align: "center", sortable: false },
]);
const options = ref({
  page: 1,
  itemsPerPage: 20,
  sortBy: ["name"],
  sortDesc: [false],
  search: "",
});
const search = ref("");
function onClick() {
  if (!search) return;
  store.getCouriers();
}

const localizationDialog = ref(null);

const localizeCategory = (item: any) => {
  localizationDialog.value?.show(item);
};
const clearSearch = () => {
  fetchCouriers();
};
const couriers = ref([]);
const fetchCouriers = async () => {
  const { page, itemsPerPage, sortBy, sortDesc, search } = options.value;
  const order = sortDesc[0] ? "DESC" : "ASC";
  const sortKey = sortBy[0] || "name";
  loading.value = true;
  await store.getCouriers(order, page, itemsPerPage, sortKey, search);
  couriers.value = store.couriers;
  loading.value = false;
};
const validateUniqueCode = (val: string) => {
  if (!val) return "Code is required";

  const existingCourier = store.couriers.find(
    (courier) => courier.code === val
  );

  return existingCourier ? "Code must be unique" : true;
};

//***************************Methods******************//

const onSubmit = async () => {
  if (!form.value) return;
  loading.value = true;

  store
    .addUpdateCourier(courier.value)
    .then(async (res: any) => {
      snackbar.add({
        type: res.success ? "success" : "error",
        text: res.success
          ? "Courier update successfully!"
          : "Failed to update courier",
      });
      await fetchCouriers();
    })
    .catch((err) => {
      snackbar.add({
        type: "error",
        text: "Error! Failed to update courier",
      });
    });

  loading.value = false;
  resetForm();
};
const updateStatus = async (item: any) => {
  const courier = couriers.value.find((c: any) => c.id === item.id);
  if (courier) {
    const itemData: any = {
      id: courier.id,
      isActive: courier.isActive, // Assign the toggled value from courier
    };
    store
      .addUpdateCourier(itemData)
      .then(async (res: any) => {
        snackbar.add({
          type: res.success ? "success" : "error",
          text: res.success
            ? "Courier status updated successfully!"
            : "Failed to update courier status",
        });
        await fetchCouriers();
      })
      .catch((err) => {
        snackbar.add({
          type: "error",
          text: "Error! Failed to update courier status",
        });
      });
  } else {
    // console.log("Courier not found");
  }
};

const resetForm = () => {
  courierForm.value.reset();
  courier.value.id = 0;
  courier.value.name = "";
  courier.value.apiKey = "";
  courier.value.codCharge = 0;
  courier.value.country = [];
  courier.value.priceInsideCity = 0;
  courier.value.priceOutsideCity = 0;
  courier.value.code = "";
  courier.value.trackingLink = "";
};

//***************************Lifecycle Hooks******************//
// Fetch country list from the store
const getCountries = computed(() => {
  return countryStore.countries;
});
onMounted(() => {
  countryStore.getCountries("ASC", 1, 100);
  currStore.getCurrencies("ASC", 1, 100);
  store.getCouriers("ASC", 1, 100);
  fetchCouriers();
});

const confirmDialog = ref(false);
const itemToDelete = ref<number | null>(null);
const handleDeleteConfirmation = async () => {
  if (itemToDelete.value !== null) {
    const productId = itemToDelete.value;
    // Call the delete product API
    const res = await store.deleteCourier(productId);
    // Handle response
    if (res) {
      snackbar.add({
        type: "success",
        text: "Courier deleted successfully!",
      });
      await fetchCouriers();
      confirmDialog.value = false;
    } else {
      snackbar.add({
        type: "error",
        text: "Failed to delete Courier.",
      });
    }
  }
};
</script>
  