<template>
  <v-row>
    <v-col cols="4">
      <SharedUiParentCard :title="subscriptionPopForm.id ? 'Edit ' + subscriptionPopForm.title : 'Add New Subscription popup'">
        <v-form v-model="form" @submit.prevent="onSubmit">

          <FormElementsImageViewerSingle :image-id="subscriptionPopForm.imageGalleryId" :image-rules="imageRules" :max-size="100000000"
            :required-height="840" :required-width="672" image-model="SUBSCRIPTION_POPUP" :multiple="false"
            @selected-image-id="handleSelectedImage" />

          <FormElementsCommonFieldContainer label="Title" :required="true">
            <v-text-field v-model="subscriptionPopForm.title" :rules="[val => REQUIRED_RULE(val)]"></v-text-field>
          </FormElementsCommonFieldContainer>

          <FormElementsCommonFieldContainer label="Details">
            <FormElementsEditor v-model="subscriptionPopForm.details" />
            <!--            <FormElementsTextEditor v-model="subscriptionPopForm.description" />-->
          </FormElementsCommonFieldContainer>

          <FormElementsCommonFieldContainer label="Country" :required="true">
            <v-select :items="getCountries" item-title="name" item-value="id" v-model="subscriptionPopForm.countryId" placeholder="Select Country"></v-select>
          </FormElementsCommonFieldContainer>
          <v-divider class="my-5"></v-divider>

          <v-btn :disabled="!form" :loading="loading" color="primary" variant="tonal" class="px-6" type="submit"> {{
        subscriptionPopForm.id ? 'Update' : 'Add New' }}
          </v-btn>

          <v-btn color="error" variant="tonal" class="px-6 ms-2" @click="resetForm"> Reset
          </v-btn>

        </v-form>
      </SharedUiParentCard>
    </v-col>
    <v-col cols="8">

      <SharedUiParentCard>

        <div class="d-flex justify-space-between align-center">
          <h2>Subscription Pops</h2>
          <!-- <div class="w-50">
            <v-text-field :loading="store.searchLoading" append-inner-icon="mdi-magnify" density="compact"
              label="Search category" variant="outlined" hide-details single-line @onChange:append-inner="onClick"
                          v-model="options.search" clearable @click:clear="clearSearch"></v-text-field>
          </div> -->

        </div>

          <v-data-table-server
              v-model:page="options.page"
              v-model:items-per-page="options.itemsPerPage"
              :headers="headers"
              :items="subscriptionPopList"
              :items-length="store.pagination.itemCount"
              :search="options.search"
              :loading="loading"
              item-value="name" @update:options="fetchSubscriptionPops">
          <template v-slot:thead>

            <!-- <tr>
              <td>
                <v-text-field v-model="name" class="ma-1" density="compact" placeholder="Search name..."
                  hide-details></v-text-field>
              </td>

            </tr> -->
          </template>

          <template v-slot:item.image="{ item }">
            <div v-if="item?.imageGallery">
              <v-img :src="item?.imageGallery?.imageUrl" width="40"/>
            </div>
          </template>

          <template v-slot:item.title="{ item }">
            <span v-html="item?.title"></span>
          </template>
          <template v-slot:item.details="{ item }">
            <span :html="item?.details"></span>
          </template>
          <template v-slot:item.country="{ item }">
            <span v-html="item?.country?.name"></span>
          </template>
          <template v-slot:item.status="{ item }">
            <!-- <v-chip :color="item?.isActive ? 'success' : 'error'" variant="elevated">
              {{ item?.isActive ? 'Active' : 'Inactive' }}
            </v-chip> -->

            <v-switch
                  v-model="item.isActive"
                  color="primary"
                  defaults-target="success"
                  hide-details
                  inset
                  @update:model-value="onUpdateStatus(item)"
              ></v-switch>

          </template>
          <template v-slot:item.action="{ item }">
            <div class="text-end">

              <v-menu>
                <template v-slot:activator="{ props }">
                  <v-btn class="me-2" variant="tonal" size="small" icon="mdi-dots-vertical" v-bind="props"></v-btn>
                </template>

                <v-list>
                  <v-list-item>
                    <v-btn icon color="warning" class="me-2" variant="tonal" size="small"
                      @click="editSubscriptionPop(item, subscriptionPopForm)">
                      <PencilIcon size="20" />
                    </v-btn>
                  </v-list-item>
                  <v-list-item>
                    <v-btn icon color="danger" class="me-2" variant="tonal" size="small"  @click="confirmDialog = true; itemToDelete = item.id">
                      <TrashIcon size="20" />
                    </v-btn>
                  </v-list-item>

                </v-list>
              </v-menu>
            </div>
          </template>
        </v-data-table-server>

      </SharedUiParentCard>
    </v-col>
  </v-row>
  <ConformationModal v-model:dialog="confirmDialog" @confirm-delete="handleDeleteConfirmation" />

</template>

<script setup lang="ts">

import { PencilIcon, TrashIcon } from "vue-tabler-icons";
import {computed, onMounted, ref, watch} from 'vue';
import {useSubscriptionPopupStore} from "~/stores/others/subscription-popup";
import { editPayloadItem } from "~/utils/helpers/functions";
import { REQUIRED_RULE } from "~/utils/formRules";
import {useCountryStore} from "~/stores/others/country";
import ConformationModal from "~/components/modals/ConformationModal.vue";

const store = useSubscriptionPopupStore();
const snackbar = useSnackbar();
const countryStore = useCountryStore();

const itemsPerPage = ref(5)
const page = ref(1)
const order = ref('desc')
const name = ref('');
const subscriptionPopList = ref([]);
const search = ref('');

const options = ref({
  page: 1,
  itemsPerPage: 20,
  sortBy: ['name'],
  sortDesc: [false],
  search: '',
});


const imageRules = [
  { text: 'Maximum image size is 100KB', icon: 'mdi-check' },
  { text: 'Image dimension is 452 X 500', icon: 'mdi-check' },
  { text: 'Image type should be WEBP', icon: 'mdi-check' },
]

const headers = ref([
  { title: 'Image', align: 'start', sortable: false, key: 'image' },
  { title: 'Title', align: 'start', sortable: false, key: 'title' },
  { title: 'Details', align: 'start', sortable: false, key: 'details' },
  { title: 'Country', key: 'country', align: 'start', sortable: false },
  { title: 'Status', key: 'status', sortable: false, align: 'start' },
  { title: 'Action', key: 'action', align: 'center', sortable: false },
])

function onClick() {

  if (!search) return;
  store.getSubscriptionPops();
}
const clearSearch = () => {
  fetchSubscriptionPops();
}

const fetchSubscriptionPops = async() => {

  const { page, itemsPerPage, sortBy, sortDesc, search } = options.value;
  const order = sortDesc[0] ? 'DESC' : 'ASC';
  const sortKey = sortBy[0] || 'name';
  loading.value = true;
  await store.getSubscriptionPopup(order, page, itemsPerPage, sortKey, search);
  subscriptionPopList.value = store.subscriptionPopup;

  loading.value = false;

}
// onMounted(fetchBrands);
// watch(search, () => {
//   fetchBrands();
// });

const onUpdateStatus = (item:any) => {
  if (item.id) {
    loading.value = true;

    store.onUpdateStatus(item.id, item.isActive)
        .then((res: any) => {
          snackbar.add({
            type: res.success ? "success" : "error",
            text: res.message,
          });
        })
        .catch((err: any) => {
          console.log(err);
        });

    loading.value = false;
  }

};


const form = ref(false);

const loading = ref(false);

const subscriptionPopForm = ref({
  id: null,
  title: null,
  details: null,
  countryId: null,
  imageGalleryId: null
})

const onSubmit = () => {
  if (!form.value) {
    return;
  }

  loading.value = true

  store.addUpdateSubscriptionPopup(subscriptionPopForm.value).then((res: any) => {
    snackbar.add({
      type: res.success ? 'success' : 'error',
      text: res.message
    })

    if (res.success) {
      resetForm();
      fetchSubscriptionPops();
    }

  }).catch(err => {
    console.log(err)
  })

  loading.value = false
}

const deleteItem = (id: number) => {
  store.deleteSubscriptionPop(id).then((res:any) => {
    fetchSubscriptionPops();
    snackbar.add({
      type: res ? 'success' : 'error',
      text: res
    })
  })
}

const resetForm = () => {
  subscriptionPopForm.value.id = null;
  subscriptionPopForm.value.title = null;
  subscriptionPopForm.value.countryId = null;
  subscriptionPopForm.value.details = null;
  subscriptionPopForm.value.imageGalleryId = null
}

const handleSelectedImage = (value: any) => {
  subscriptionPopForm.value.imageGalleryId = value
}

const getCountries = computed(() => countryStore.countries);

onMounted(async () => {
  countryStore.getAllCountries();

});

const editSubscriptionPop = (item: any, subscriptionPop: any) => {
  editPayloadItem(item, subscriptionPop);
  fetchSubscriptionPops();
}


watch(name, (val, oldValue) => {
  console.log('called watch');

  let arraydata = [];
  subscriptionPopList.value.filter((item) => {
    if (item.title.toLowerCase().includes(val)) {
      arraydata.push(item);
    }

  })
  if (val.length === 0) {
    arraydata = store.subscriptionPopup;
  }
  subscriptionPopList.value = arraydata;
})

const confirmDialog = ref(false);
const itemToDelete = ref<number | null>(null);
const handleDeleteConfirmation = async () => {
  if (itemToDelete.value !== null) {
    const productId = itemToDelete.value;
    // Call the delete product API
    const res = await store.deleteSubscriptionPop(productId);
    // Handle response
    if (res) {
      snackbar.add({
        type: 'success',
        text: 'Item deleted successfully!',
      });
      confirmDialog.value = false;

    } else {
      snackbar.add({
        type: 'error',
        text: 'Failed to delete Item.',
      });
    }
  }
};


</script>
