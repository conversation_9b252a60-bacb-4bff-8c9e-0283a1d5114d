<template>
  <SharedBaseBreadcrumb
      :title="page.title"
      :breadcrumbs="breadcrumbs"
  ></SharedBaseBreadcrumb>
  <SharedUiParentCard body-class="pa-3">
    <div class="text-right">
      <v-btn  color="primary" variant="tonal" class="px-6" type="button"  @click="downloadExcel"> Export Excel</v-btn>
    </div>
    <v-data-table-server
        v-model:page="options.page"
        v-model:items-per-page="options.itemsPerPage"
        :headers="headers"
        :items="store.subscribeList"
        :items-length="store.pagination.itemCount"
        :search="options.search"
        :loading="loading"
        item-value="name" @update:options="fetchList">
      <template v-slot:item.isActive="{ item }">
        <v-chip :color="item?.isActive ? 'success' : 'error'" variant="elevated">
          {{ item?.isActive ? 'Active' : 'Inactive' }}
        </v-chip>
      </template>
    </v-data-table-server>

  </SharedUiParentCard>
</template>

<script setup lang="ts">
//***************************Imports******************//
import {PencilIcon} from "vue-tabler-icons";
import {ref, watch} from 'vue'
import {useSubscribeListStore} from "~/stores/others/subscribe-list";

//***************************Variables******************//
const store = useSubscribeListStore();
const page = ref({title: "Subscribe List"});

const breadcrumbs = ref([
  {
    text: "Dashboard",
    disabled: false,
    to: "/dashboard",
  },
  {
    text: "Subscribe List",
    disabled: true,
    to: "",
  },
]);
const invoice_no = ref('');
const order_list = ref([]);
const search = ref('');
const options = ref({
  page: 1,
  itemsPerPage: 20,
  sortDesc: [false],
  search: '',
});

const headers = ref([
  {title: 'Email', align: 'start', sortable: false, key: 'email'},
  {title: 'Status', align: 'start', sortable: false, key: 'isActive'},
])

const snackbar = useSnackbar();
const form = ref(false);

const loading = ref(false);

const fetchList = async () => {
  const { page, itemsPerPage } = options.value;
  const order = 'ASC';
  loading.value = true;
  await store.getSubscribeList(order, page, itemsPerPage);
  loading.value = false;
};

const downloadExcel  = async () => {
  await store.downloadSubscribeExcel();
}


</script>
