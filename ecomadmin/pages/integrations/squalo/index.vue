<template>
  <SharedBaseBreadcrumb
    :title="page.title"
    :breadcrumbs="breadcrumbs"
  ></SharedBaseBreadcrumb>
  <SharedUiParentCard body-class="pa-3" style="min-height: 300px">
    <v-row>
      <v-col>
        <v-tabs
          v-model="activeTab"
          bg-color="teal-darken-3"
          slider-color="teal-lighten-3"
          density="compact"
          show-arrows
        >
          <v-tab
            v-for="(tab, index) in integrationTabs"
            :key="'tab_' + index"
            :text="tab.label"
            :value="index + 1"
            density="compact"
            selected-class="bg-sidebarBg"
            class="mx-1"
          ></v-tab>
        </v-tabs>
        <v-window v-model="activeTab" class="mt-2">
          <v-window-item :value="1">
            <v-row>
              <v-col cols="6">
                <SqualoStoreManage
                  :countryStore="countryStore"
                  :squaloStore="squaloStore"
                />
              </v-col>
              <v-col cols="6">
                <v-sheet
                  class="d-flex justify-end "
                >
                  <SqualoStoreList :squaloStore="squaloStore" class="w-75" />
                </v-sheet>
              </v-col>
            </v-row>
          </v-window-item>
          <v-window-item :value="2">
            <SqualoCategoryManage
              :countryStore="countryStore"
              :squaloStore="squaloStore"
            />
          </v-window-item>
          <v-window-item :value="3">
            <SqualoProductManage
              :countryStore="countryStore"
              :squaloStore="squaloStore"
            />
          </v-window-item>
          <v-window-item :value="4">
            <SqualoCustomerManage
              :countryStore="countryStore"
              :squaloStore="squaloStore"
            />
          </v-window-item>
          <v-window-item :value="5">
            <SqualoCartManage
              :countryStore="countryStore"
              :squaloStore="squaloStore"
            />
          </v-window-item>
          <v-window-item :value="6">
            <SqualoOrderManage
              :countryStore="countryStore"
              :squaloStore="squaloStore"
            />
          </v-window-item>
        </v-window>
      </v-col>
    </v-row>
    <v-snackbar v-if="snackbarData" :color="snackbarData?.type">
      {{ snackbarData.message }}
    </v-snackbar>
  </SharedUiParentCard>
</template>

<script setup>
import { useCountryStore } from "~/stores/others/country";
import { useSqualoStore } from "~/stores/integrations/squalo";
const squaloStore = useSqualoStore();
const countryStore = useCountryStore();
var snackbarData = ref(null);

const page = ref({ title: "Integrations" });
const breadcrumbs = ref([
  {
    text: "Dashboard",
    disabled: false,
    to: "/dashboard",
  },
  {
    text: "Integrations",
    disabled: true,
  },
  {
    text: "Squalo",
    disabled: true,
    to: "",
  },
]);

const activeTab = ref(1);
const integrationTabs = [
  {
    label: "Store",
  },
  {
    label: "Category",
  },
  {
    label: "Products",
  },
  {
    label: "Customer",
  },
  {
    label: "Cart",
  },
  {
    label: "Order",
  },
];

const handleFetchCountries = async () => {
  await countryStore.getAllCountries();
};

onMounted(() => {
  handleFetchCountries();
});
</script>
