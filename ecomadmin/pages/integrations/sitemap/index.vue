<template>
  <SharedBaseBreadcrumb
    :title="page.title"
    :breadcrumbs="breadcrumbs"
  ></SharedBaseBreadcrumb>
  <SharedUiParentCard body-class="pa-3" style="min-height: 400px">
    <v-row justify="space-between">
      <v-col cols="4">
        <v-label>Select Country</v-label>
        <v-select
          :items="countryStore.countries"
          item-title="name"
          item-value="id"
          v-model="selectedCountry"
        >
        </v-select>
        <v-btn
          @click="handleCreateCountrySitemap"
          :loading="loading"
          color="primary"
          flat
          >Sync</v-btn
        >
      </v-col>
      <v-col cols="3">
        <v-list height="300" class="pnt-scrollbar border">
          <v-list-item-title class="font-weight-bold mb-3 text-center bg-sidebarBg">
            View Sitemap
          </v-list-item-title>
          <v-list-item
            v-for="(obj, index) in countryStore.countries"
            :key="'sitemap_country_' + index"
            :href="'https://' + obj?.domain + '/sitemap.xml'"
            target="_blank"
            color="primary"
          >
            <v-list-item-title
              ><v-icon icon="mdi-link-variant"></v-icon> {{ obj?.name }}
            </v-list-item-title>
          </v-list-item>
        </v-list>
      </v-col>
    </v-row>
  </SharedUiParentCard>
</template>

<script setup>
import { useCountryStore } from "~/stores/others/country";
import { useSeoStore } from "~/stores/integrations/seo";

const snackbar = useSnackbar();
const countryStore = useCountryStore();
const seoStore = useSeoStore();

const page = ref({ title: "Sitemap" });
const breadcrumbs = ref([
  {
    text: "Dashboard",
    disabled: false,
    to: "/dashboard",
  },
  {
    text: "Sitemap",
    disabled: true,
  },
]);
const selectedCountry = ref(null);
const loading = ref(false);
const handleFetchCountries = async () => {
  await countryStore.getAllCountries();
};

const handleCreateCountrySitemap = () => {
  let payload = {
    countryId: selectedCountry.value,
  };
  loading.value = true;
  seoStore
    .createCountrySitemap(payload)
    .then((res) => {
      let response = JSON.parse(res);
      snackbar.add({
        type: response?.isSuccess ? "success" : "error",
        text: response?.messasge,
      });
      loading.value = false;
    })
    .catch((e) => {
      console.log(e);
      snackbar.add({
        type: "error",
        text: res?.messasge || "Please contact with team",
      });
      loading.value = false;
    });
};

onMounted(() => {
  handleFetchCountries();
});
</script>
