<template>
  <SharedBaseBreadcrumb
      :title="page.title"
      :breadcrumbs="breadcrumbs"
  ></SharedBaseBreadcrumb>
  <SharedUiParentCard body-class="pa-3">
    
    <div class="d-flex justify-end align-center">
      <v-btn  color="primary" variant="tonal" class="px-6" type="button"  @click="downloadExcel"> Export Excel</v-btn>
    </div>

    <v-data-table-server
        v-model:page="options.page"
        v-model:items-per-page="options.itemsPerPage"
        :headers="headers"
        :items="order_list"
        :items-length="store.pagination.itemCount"
        :search="options.search"
        :loading="loading"
        item-value="name" @update:options="fetchReport">

      <template v-slot:thead>

        <!--        <tr>-->
        <!--          <td>-->
        <!--            <v-text-field v-model="invoice_no" class="ma-1" density="compact" placeholder="Search invoice no..."-->
        <!--                          hide-details></v-text-field>-->
        <!--          </td>-->

        <!--        </tr>-->
      </template>
      <template v-slot:item.invoiceNo="{ item }">
        <v-chip variant="tonal" size="x-small" color="error">
          {{ item?.invoiceNo }}
        </v-chip>
      </template>
      <template v-slot:item.createdAt="{ item }">
        {{ formatDate(item?.createdAt) }}
      </template>
      <template v-slot:item.country="{ item }">
        {{ item?.country?.name }}
      </template>
      <template v-slot:item.billingAddress="{ item }">
        {{ item?.billingAddress?.firstName + ' ' + item?.billingAddress?.lastName }}
      </template>
      <template v-slot:item.amount="{ item }">
        {{ item?.currency?.currencySymbol }}

        {{ item?.amount }}
      </template>
      <template v-slot:item.product="{ item }">
        {{ item?.product?.name }}
      </template>

    </v-data-table-server>

  </SharedUiParentCard>
</template>

<script setup lang="ts">
//***************************Imports******************//
import {PencilIcon} from "vue-tabler-icons";
import {ref, watch} from 'vue'

import {formatDate} from "~/utils/helpers/functions";
// import {useOrdersStore} from "~/stores/orders";
import { useReports } from "~/stores/reports";

//***************************Variables******************//
const store = useReports();
const page = ref({title: "Product Return Rates And Reasons Report"});

const breadcrumbs = ref([
  {
    text: "Dashboard",
    disabled: false,
    to: "/dashboard",
  },
  {
    text: "Product Return Rates And Reasons Report",
    disabled: true,
    to: "",
  },
]);
const invoice_no = ref('');
const order_list = ref([]);
const search = ref('');
const options = ref({
  page: 1,
  itemsPerPage: 20,
  sortBy: ['invoiceNo'],
  sortDesc: [false],
  search: '',
});

const headers = ref([
  // {title: 'Sl.', align: 'start', sortable: false, key: 'id'},
  {title: 'Product Name', align: 'start', sortable: false, key: 'product'},
  {title: 'Size', key: 'size', sortable: false, align: 'start'},
  {title: 'color', key: 'color', sortable: false, align: 'start'},
  // {title: 'Sold Quantity', key: 'amount', sortable: false, align: 'start'},
  {title: 'Return Quantity', key: 'quantity', sortable: false, align: 'start'},
  // {title: 'Return Rate (%)', key: 'paymentMethod', sortable: false, align: 'start'},
  {title: 'Reason(s)', key: 'refundReason', sortable: false, align: 'start'},
])

const snackbar = useSnackbar();
const form = ref(false);

const loading = ref(false);

const fetchReport = async () => {
  const { page, itemsPerPage, sortBy, sortDesc, search } = options.value;
  const order = sortDesc[0] ? 'DESC' : 'ASC';
  const sortKey = sortBy[0] || 'invoiceNo';
  loading.value = true;
  await store.getProductReturnListReport(order, page, itemsPerPage, sortKey, search);
  order_list.value = store.return_list;
  loading.value = false;
};

const onClick = () => {
  if (!search.value) return;
  fetchReport();
};

const clearSearch = () => {
  search.value = '';
  fetchReport();
};

const downloadExcel  = async () => {
  await store.downloadProductReturnRateReport();
}

</script>
