<template>
  <SharedBaseBreadcrumb
      :title="page.title"
      :breadcrumbs="breadcrumbs"
  ></SharedBaseBreadcrumb>
  <SharedUiParentCard body-class="pa-3">
    <div class="d-flex justify-end align-center">
      <v-btn  color="primary" variant="tonal" class="px-6" type="button"  @click="downloadExcel"> Export Excel</v-btn>
      <!--      <div class="w-50">-->
      <!--        <v-text-field  append-inner-icon="mdi-magnify" density="compact"-->
      <!--                       label="Search Invoice No" variant="outlined" hide-details single-line-->
      <!--                       @onChange:append-inner="onClick"-->
      <!--                       v-model="options.search" clearable @click:clear="clearSearch"></v-text-field>-->
      <!--      </div>-->
    </div>


    <v-data-table-server
        v-model:page="options.page"
        v-model:items-per-page="options.itemsPerPage"
        :headers="headers"
        :items="order_list"
        :items-length="store.pagination.itemCount"
        :search="options.search"
        :loading="loading"
        item-value="name" @update:options="fetchReport">

      <template v-slot:thead>

      </template>

      <template v-slot:item.firstName="{ item }">
        {{item?.firstName + ' ' + item?.lastName}}
      </template>
      <template v-slot:item.country="{ item }">
        {{ item?.country?.name }}
      </template>
      <template v-slot:item.address="{ item }">
        {{item?.address?.address ? item?.address?.address  + ', ' + item?.address?.city+ ', ' + item?.address?.code + ', ' + item?.address?.country?.name:'' }}
      </template>

    </v-data-table-server>

  </SharedUiParentCard>
</template>

<script setup lang="ts">
//***************************Imports******************//
import {PencilIcon} from "vue-tabler-icons";
import {ref, watch} from 'vue'

// import {useOrdersStore} from "~/stores/orders";
import { useReports } from "~/stores/reports";

//***************************Variables******************//
const store = useReports();
const page = ref({title: "Demographics Report"});

const breadcrumbs = ref([
  {
    text: "Dashboard",
    disabled: false,
    to: "/dashboard",
  },
  {
    text: "Demographics Report",
    disabled: true,
    to: "",
  },
]);
const invoice_no = ref('');
const order_list = ref([]);
const search = ref('');
const options = ref({
  page: 1,
  itemsPerPage: 20,
  sortBy: ['invoiceNo'],
  sortDesc: [false],
  search: '',
});

const headers = ref([
  // {title: 'Sl.', align: 'start', sortable: false, key: 'id'},
  {title: 'Customer Name', align: 'start', sortable: false, key: 'firstName'},
  {title: 'Mobile', key: 'phone', sortable: false, align: 'start'},
  {title: 'Email', key: 'email', sortable: false, align: 'start'},
  {title: 'Gender', key: 'gender', sortable: false, align: 'start'},
  {title: 'Address', key: 'address', sortable: false, align: 'start'},
  // {title: 'City', key: 'city', sortable: false, align: 'start'},
  // {title: 'Country', key: 'country', sortable: false, align: 'start'},
])

const snackbar = useSnackbar();
const form = ref(false);

const loading = ref(false);

const fetchReport = async () => {
  const { page, itemsPerPage, sortBy, sortDesc, search } = options.value;
  const order = sortDesc[0] ? 'DESC' : 'ASC';
  const sortKey = sortBy[0] || 'invoiceNo';
  loading.value = true;
  await store.getCustomerDemographicsReport(order, page, itemsPerPage, sortKey, search);
  order_list.value = store.demographics;
  loading.value = false;
};

const onClick = () => {
  if (!search.value) return;
  fetchReport();
};

const clearSearch = () => {
  search.value = '';
  fetchReport();
};

const downloadExcel  = async () => {
  await store.downloadCustomerDemographicsReport();
}


</script>
