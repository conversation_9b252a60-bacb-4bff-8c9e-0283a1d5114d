<template>
  <SharedBaseBreadcrumb
      :title="page.title"
      :breadcrumbs="breadcrumbs"
  ></SharedBaseBreadcrumb>
  <SharedUiParentCard body-class="pa-3">
    
    <div class="d-flex justify-end align-center">
      <v-btn  color="primary" variant="tonal" class="px-6" type="button"  @click="downloadExcel"> Export Excel</v-btn>
    </div>


    <v-data-table-server
        v-model:page="options.page"
        v-model:items-per-page="options.itemsPerPage"
        :headers="headers"
        :items="order_list"
        :items-length="store.pagination.itemCount"
        :search="options.search"
        :loading="loading"
        item-value="name" @update:options="fetchReport">

      <template v-slot:thead>

        <!--        <tr>-->
        <!--          <td>-->
        <!--            <v-text-field v-model="invoice_no" class="ma-1" density="compact" placeholder="Search invoice no..."-->
        <!--                          hide-details></v-text-field>-->
        <!--          </td>-->

        <!--        </tr>-->
      </template>
      <template v-slot:item.invoiceNo="{ item }">
        <v-chip variant="tonal" size="x-small" color="error">
          {{ item?.invoiceNo }}
        </v-chip>
      </template>
      <template v-slot:item.createdAt="{ item }">
        {{ formatDate(item?.createdAt) }}
      </template>
      <template v-slot:item.country="{ item }">
        {{ item?.country?.name }}
      </template>
      <template v-slot:item.customer_name="{ item }">
        {{ item?.billingAddress?.firstName + ' ' + item?.billingAddress?.lastName }}
      </template>
      <template v-slot:item.mobile="{ item }">
        {{ item?.billingAddress?.phone }}
      </template>
      <template v-slot:item.email="{ item }">
        {{ item?.billingAddress?.email }}
      </template>

      <template v-slot:item.productOrderDetails="{ item }">
       <div v-for="item in item?.productOrderDetails">
         <div class="border-bottom">
           <p>{{ item?.product?.name }}</p>
           <p>Size:{{ item?.size?.name }}</p>
           <p>Price:{{ item?.unitPrice }}</p>
           <p>Qty:{{ item?.quantity }}</p>
         </div>
       </div>
      </template>



    </v-data-table-server>

  </SharedUiParentCard>
</template>

<script setup lang="ts">
//***************************Imports******************//
import {PencilIcon} from "vue-tabler-icons";
import {ref} from 'vue'
import {formatDate} from "~/utils/helpers/functions";

// import {useOrdersStore} from "~/stores/orders";
import {useReports} from "~/stores/reports";
import {DynamicModuleEnum, DynamicPermissionEnum} from "~/utils/roleManagement";
import { useRolePermissionsStore } from "~/stores/administration/permissions";

//***************************Variables******************//
const store = useReports();
const permissionStore = useRolePermissionsStore();
const page = ref({title: "Purchase History Report"});

const breadcrumbs = ref([
  {
    text: "Dashboard",
    disabled: false,
    to: "/dashboard",
  },
  {
    text: "Purchase History Report",
    disabled: true,
    to: "",
  },
]);
const invoice_no = ref('');
const order_list = ref([]);
const search = ref('');
const options = ref({
  page: 1,
  itemsPerPage: 20,
  sortBy: ['invoiceNo'],
  sortDesc: [false],
  search: '',
});

const headers = ref([
  // {title: 'Sl.', align: 'start', sortable: false, key: 'id'},
  {title: 'Order No', key: 'invoiceNo', sortable: false, align: 'start'},
  {title: 'Order Date', align: 'start', sortable: false, key: 'createdAt'},
  {title: 'Customer Name', align: 'start', sortable: false, key: 'customer_name'},
  {title: 'Mobile', align: 'start', sortable: false, key: 'mobile'},
  {title: 'Email', align: 'start', sortable: false, key: 'email'},
  {title: 'Item Details', align: 'start', sortable: false, key: 'productOrderDetails'},
  {title: 'Country', align: 'start', sortable: false, key: 'country'},
])

const snackbar = useSnackbar();
const form = ref(false);

const loading = ref(false);

const fetchReport = async () => {
  const {page, itemsPerPage, sortBy, sortDesc, search} = options.value;
  const order = sortDesc[0] ? 'DESC' : 'ASC';
  const sortKey = sortBy[0] || 'invoiceNo';
  loading.value = true;
  await store.getPurchaseReport(order, page, itemsPerPage, sortKey, search);
  order_list.value = store.purchases;
  loading.value = false;
};

const onClick = () => {
  if (!search.value) return;
  fetchReport();
};

const clearSearch = () => {
  search.value = '';
  fetchReport();
};

const downloadExcel  = async () => {
  if(!permissionStore.hasModulePermission(`${DynamicModuleEnum.PRODUCT_ORDER}:${DynamicPermissionEnum.REPORT}`)){
    snackbar.add({
      type: "warning",
      text: "You don't have permission to download customer purchase report",
    })
    return;
  }
  
  await store.downloadCustomerPurchaseHistoryReport();
}


</script>
