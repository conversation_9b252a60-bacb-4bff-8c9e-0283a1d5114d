<template>
  <SharedBaseBreadcrumb
      :title="page.title"
      :breadcrumbs="breadcrumbs"
  ></SharedBaseBreadcrumb>
  <SharedUiParentCard body-class="pa-3">
  
    <div class="d-flex justify-end align-center">
      <v-btn  color="primary" variant="tonal" class="px-6" type="button"  @click="downloadExcel"> Export Excel</v-btn>
    </div>

    <v-data-table-server
        v-model:page="options.page"
        v-model:items-per-page="options.itemsPerPage"
        :headers="headers"
        :items="order_list"
        :items-length="store.pagination.itemCount"
        :search="options.search"
        :loading="loading"
        item-value="name" @update:options="fetchReport">

      <template v-slot:item.created_at="{ item }">
        {{ formatDate(item?.created_at) }}
      </template>
      <template v-slot:item.country="{ item }">
        {{ item?.country?.name }}
      </template>
      <template v-slot:item.billingAddress="{ item }">
        {{ item?.billingAddress?.firstName + ' ' + item?.billingAddress?.lastName }}
      </template>


    </v-data-table-server>

  </SharedUiParentCard>
</template>

<script setup lang="ts">
//***************************Imports******************//
import {PencilIcon} from "vue-tabler-icons";
import {ref, watch} from 'vue'

import {formatDate} from "~/utils/helpers/functions";
// import {useOrdersStore} from "~/stores/orders";
import { useReports } from "~/stores/reports";
import { useRolePermissionsStore } from "~/stores/administration/permissions";
import {DynamicModuleEnum, DynamicPermissionEnum} from "~/utils/roleManagement";

//***************************Variables******************//
const store = useReports();
const permissionStore = useRolePermissionsStore();
const page = ref({title: "Sales Report"});

const breadcrumbs = ref([
  {
    text: "Dashboard",
    disabled: false,
    to: "/dashboard",
  },
  {
    text: "Sales Report",
    disabled: true,
    to: "",
  },
]);
const invoice_no = ref('');
const order_list = ref([]);
const search = ref('');
const options = ref({
  page: 1,
  itemsPerPage: 20,
  sortBy: ['invoiceNo'],
  sortDesc: [false],
  search: '',
});

const headers = ref([
  // {title: 'Sl.', align: 'start', sortable: false, key: 'id'},
  {title: 'Order No', align: 'start', sortable: false, key: 'invoice_no'},
  {title: 'Order Date', align: 'start', sortable: false, key: 'created_at'},
  {title: 'SKU', align: 'start', sortable: false, key: 'sku'},
  {title: 'Product Name', align: 'start', sortable: false, key: 'product_name'},
  {title: 'Color', align: 'start', sortable: false, key: 'color'},
  {title: 'Size', align: 'start', sortable: false, key: 'size'},
  {title: 'Order Type', key: 'delivery_type', sortable: false, align: 'start'},
  {title: 'Delivery Type', key: 'delivery_type', sortable: false, align: 'start'},
  {title: 'Customer Name', key: 'customer_name', sortable: false, align: 'start'},
  {title: 'Email', key: 'email', sortable: false, align: 'start'},
  {title: 'Phone', key: 'phone', sortable: false, align: 'start'},
  {title: 'Price', key: 'unit_price', sortable: false, align: 'start'},
  {title: 'Shipping Charge', key: 'shipping_charge', sortable: false, align: 'start'},
  {title: 'Discount', key: 'discount', sortable: false, align: 'start'},
  {title: 'Payment Method', key: 'payment_method', sortable: false, align: 'start'},
  {title: 'Payment Status', key: 'payment_status', sortable: false, align: 'start'},
  {title: 'Delivery Status', key: 'delivery_status', sortable: false, align: 'start'},
])

const snackbar = useSnackbar();
const form = ref(false);

const loading = ref(false);

const fetchReport = async () => {
  const { page, itemsPerPage, sortBy, sortDesc, search } = options.value;
  const order = sortDesc[0] ? 'DESC' : 'ASC';
  const sortKey = sortBy[0] || 'invoiceNo';
  loading.value = true;
  await store.getSalesReport(order, page, itemsPerPage, sortKey, search);
  order_list.value = store.sales;
  loading.value = false;
};

const onClick = () => {
  if (!search.value) return;
  fetchReport();
};

const clearSearch = () => {
  search.value = '';
  fetchReport();
};


const downloadExcel  = async () => {
  if(!permissionStore.hasModulePermission(`${DynamicModuleEnum.PRODUCT_ORDER}:${DynamicPermissionEnum.REPORT}`)){
    snackbar.add({
      type: "warning",
      text: "You don't have permission to download sales report",
    })
    return;
  }
  
  await store.downloadSalesReport();
}

</script>
