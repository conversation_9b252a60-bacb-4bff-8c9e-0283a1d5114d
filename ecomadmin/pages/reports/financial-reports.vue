<template>
  <SharedBaseBreadcrumb
      :title="page.title"
      :breadcrumbs="breadcrumbs"
  ></SharedBaseBreadcrumb>
  <SharedUiParentCard body-class="pa-3">
    <div class="d-flex justify-end align-center">
      <v-btn  color="primary" variant="tonal" class="px-6" type="button"  @click="downloadExcel"> Export Excel</v-btn>
    </div>

    <v-data-table-server
        v-model:page="options.page"
        v-model:items-per-page="options.itemsPerPage"
        :headers="headers"
        :items="order_list"
        :items-length="store.pagination.itemCount"
        :search="options.search"
        :loading="loading"
        item-value="name" @update:options="fetchReport">

      <template v-slot:thead>

<!--        <tr>-->
<!--          <td>-->
<!--            <v-text-field v-model="invoice_no" class="ma-1" density="compact" placeholder="Search invoice no..."-->
<!--                          hide-details></v-text-field>-->
<!--          </td>-->

<!--        </tr>-->
      </template>
      <template v-slot:item.invoiceNo="{ item }">
        <v-chip variant="tonal" size="x-small" color="error">
          {{ item?.invoiceNo }}
        </v-chip>
      </template>
      <template v-slot:item.createdAt="{ item }">
        {{ formatDate(item?.createdAt) }}
      </template>
      <template v-slot:item.country="{ item }">
        {{ item?.country?.name }}
      </template>
      <template v-slot:item.billingAddress="{ item }">
        {{ item?.billingAddress?.firstName + ' ' + item?.billingAddress?.lastName }}
      </template>
      <template v-slot:item.amount="{ item }">
        {{ item?.currency?.currencySymbol }}

        {{ item?.amount }}
      </template>
      <template v-slot:item.deliveryStatus="{ item }">
        <v-chip size="x-small" variant="tonal" color="secondary">{{ item.deliveryStatus }}</v-chip>
      </template>
      <template v-slot:item.paymentStatus="{ item }">
        <v-chip size="x-small" variant="tonal" color="primary">{{ item.paymentStatus }}</v-chip>
      </template>
      <template v-slot:item.status="{ item }">
        <v-chip :color="item?.isActive ? 'success' : 'error'" variant="elevated">
          {{ item?.isActive ? 'Active' : 'Inactive' }}
        </v-chip>
      </template>
      <template v-slot:item.action="{ item }">
        <div class="text-end">

          <v-menu>
            <template v-slot:activator="{ props }">
              <v-btn class="me-2" variant="tonal" size="small" icon="mdi-dots-vertical" v-bind="props"></v-btn>
            </template>

            <v-list>
              <v-list-item>
                <v-btn icon color="warning" class="me-2" variant="tonal"
                       size="x-small"
                       @click="editOrder(item.id)">
                  <PencilIcon size="20"/>
                </v-btn>
              </v-list-item>

            </v-list>
          </v-menu>

        </div>
      </template>
    </v-data-table-server>

  </SharedUiParentCard>
</template>

<script setup lang="ts">
//***************************Imports******************//
import {PencilIcon} from "vue-tabler-icons";
import {ref, watch} from 'vue'
import {formatDate} from "~/utils/helpers/functions";

// import {useOrdersStore} from "~/stores/orders";
import { useReports } from "~/stores/reports";
import { useRolePermissionsStore } from "~/stores/administration/permissions";
import {DynamicModuleEnum, DynamicPermissionEnum} from "~/utils/roleManagement";

//***************************Variables******************//
const store = useReports();
const permissionStore = useRolePermissionsStore();
const page = ref({title: "Financial Report"});

const breadcrumbs = ref([
  {
    text: "Dashboard",
    disabled: false,
    to: "/dashboard",
  },
  {
    text: "Financial Report",
    disabled: true,
    to: "",
  },
]);
const invoice_no = ref('');
const order_list = ref([]);
const search = ref('');
const options = ref({
  page: 1,
  itemsPerPage: 20,
  sortBy: ['invoiceNo'],
  sortDesc: [false],
  search: '',
});

const headers = ref([
  // {title: 'Sl.', align: 'start', sortable: false, key: 'id'},
  {title: 'Date', align: 'start', sortable: false, key: 'createdAt'},
  {title: 'Order No', key: 'invoiceNo', sortable: false, align: 'start'},
  {title: 'Amount', key: 'amount', sortable: false, align: 'start'},
  {title: 'Payment Method', key: 'paymentMethod', sortable: false, align: 'start'},
])

const snackbar = useSnackbar();
const form = ref(false);

const loading = ref(false);

const fetchReport = async () => {
  if(!permissionStore.hasModulePermission(`${DynamicModuleEnum.PRODUCT_ORDER}:${DynamicPermissionEnum.REPORT}`)){
    snackbar.add({
      type: "warning",
      text: "You don't have permission to download inventory report",
    })
    return;
  }

  const { page, itemsPerPage, sortBy, sortDesc, search } = options.value;
  const order = sortDesc[0] ? 'DESC' : 'ASC';
  const sortKey = sortBy[0] || 'invoiceNo';
  loading.value = true;
  await store.getFinancialReport(order, page, itemsPerPage, sortKey, search);
  order_list.value = store.finances;
  loading.value = false;
};

const onClick = () => {
  if (!search.value) return;
  fetchReport();
};

const clearSearch = () => {
  search.value = '';
  fetchReport();
};

const downloadExcel  = async () => {
  if(!permissionStore.hasModulePermission(`${DynamicModuleEnum.PRODUCT_ORDER}:${DynamicPermissionEnum.REPORT}`)){
    snackbar.add({
      type: "warning",
      text: "You don't have permission to download financial report",
    })
    return;
  }

  await store.downloadFinancialReport();
}

</script>
