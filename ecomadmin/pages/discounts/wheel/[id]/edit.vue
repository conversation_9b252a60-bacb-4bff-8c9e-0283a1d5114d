<template>
	<WheelForm v-if="wheelData" :wheel-data="wheelData"/>
</template>

<script setup lang="ts">
import WheelForm from "~/components/product/discount/WheelForm.vue";
import { useFortunateWheelStore } from '~/stores/fortunate-wheel';
import type { Wheel } from '~/components/product/discount/types'

const route = useRoute();
const wheelStore = useFortunateWheelStore();
const wheelData = ref<Wheel>();

onMounted(async ()=>{
	await wheelStore
		?.getSingleWheel({id: route?.params?.id})
		?.then((response)=>{
			const _data = response?.item;
			wheelData.value = _data;
		})

})
</script>