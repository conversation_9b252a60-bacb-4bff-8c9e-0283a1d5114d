<template>
    <h1>Fortunate Wheels</h1>
    <v-btn to="/discounts/wheel/add" color="primary" variant="flat">add new</v-btn>
    <div class="d-flex flex-column gap-2 mt-2">
        <v-sheet
            v-for="(obj, index) in wheelStore?.rows"
            :key="'wheel_' + index"
            class="pa-4 border border-primary"
            >
            <div class="d-flex justify-space-between align-center">
                <div>
                    <nuxt-link
                        :to="{ name: 'discounts-wheel-id-edit', params: { id: obj?.id } }"
                        class="text-decoration-underline font-weight-bold"
                    >
                        {{ obj?.title }}
                    </nuxt-link>
                    <div class="text-caption mt-1">
                        {{ obj.isActive ? 'Active' : 'Inactive' }}
                    </div>
                    <div class="text-caption mt-1">
                        {{ getCountryName(obj.countryId) }}
                    </div>
                </div>
                <v-icon @click="deleteWheel(obj?.id)" icon="mdi-delete" class="cursor-pointer" color="error" size="large"></v-icon>
            </div>
        </v-sheet>
    </div>
</template>

<script setup>
import { useFortunateWheelStore } from '~/stores/fortunate-wheel';
import { useCountryStore } from '~/stores/others/country';

const wheelStore = useFortunateWheelStore();
const countryStore = useCountryStore();

const deleteWheel = async (id) => {
  await wheelStore.deleteWheel({id: id});
  await wheelStore.fetchWheels();
};

const getCountryName = (countryId) => {
  return countryStore?.countries?.find(item => item?.id == countryId)?.name
}

onMounted(()=>{
    wheelStore
        ?.fetchWheels();
    countryStore.getAllCountries();
})

</script>