<template>
  <SharedBaseBreadcrumb
      :title="page.title"
      :breadcrumbs="breadcrumbs"
  ></SharedBaseBreadcrumb>

  <SharedUiParentCardSolid>
    <v-sheet>
      <!-- Header -->
      <v-row class="justify-space-between align-center">
        <v-col cols="12" md="8">
          <v-card-title class="text-h5 font-weight-bold">
            {{ discountData?.title }} (ID: {{ discountData?.id }})
          </v-card-title>
        </v-col>
        <v-col cols="12" md="4" class="d-flex justify-end">
          <v-btn :to="{ name: 'discounts-id-edit', params: { id: route?.params?.id } }" variant="flat" color="primary">
            Edit Discount
          </v-btn>
        </v-col>
      </v-row>

      <v-divider class="mb-4"></v-divider>

      <!-- General Info -->
      <v-row dense>
        <v-col cols="12" md="6" v-for="field in generalFields" :key="field.label">
          <v-list-item>
            <v-list-item-title class="text-caption text-medium-emphasis">{{ field.label }}</v-list-item-title>
            <v-list-item-subtitle class="text-subtitle-1 font-weight-medium">
              <template v-if="field.type === 'chip'">
                <v-chip :color="field.color(discountData)" size="small" variant="flat">{{ field.value(discountData) }}</v-chip>
              </template>
              <template v-else>
                {{ field.value(discountData) }}
              </template>
            </v-list-item-subtitle>
          </v-list-item>
        </v-col>
      </v-row>

      <!-- Coupon Code -->
      <v-divider class="my-4" />
      <v-list-item>
        <v-list-item-title>Coupon Code</v-list-item-title>
        <v-list-item-subtitle>{{ discountData?.coupon || '—' }}</v-list-item-subtitle>
      </v-list-item>

      <!-- Product List -->
      <v-divider class="my-4" />
      <v-list-item>
        <v-list-item-title>Associated Products</v-list-item-title>
      </v-list-item>
      <v-row>
        <v-col cols="12" md="4" v-for="(product, i) in discountData?.products" :key="i">
          <v-card class="rounded-lg">
            <v-img :src="product?.featuredImage?.imageGalleryUrls?.medium" height="200px" />
            <v-card-title class="text-body-1 font-weight-bold">
              {{ product?.name }}
            </v-card-title>
            <v-card-subtitle class="text-caption">SKU: {{ product?.sku }}</v-card-subtitle>
          </v-card>
        </v-col>
      </v-row>

      <!-- Country Chips -->
      <v-divider class="my-4" />
      <v-list-item>
        <v-list-item-title>Available In Countries</v-list-item-title>
        <v-list-item-subtitle>
          <v-chip
            v-for="(country, i) in discountData?.countries"
            :key="i"
            class="ma-1"
            color="secondary"
            size="small"
            variant="flat"
          >
            {{ getCountryName(country?.countryId) }}
          </v-chip>
        </v-list-item-subtitle>
      </v-list-item>
    </v-sheet>
  </SharedUiParentCardSolid>
</template>

<script setup>
import { useRoute } from 'vue-router';
import { onMounted, ref } from 'vue';
import { useDiscountsStore } from '~/stores/discounts';
import { useCountryStore } from '~/stores/others/country';

const route = useRoute();
const discountStore = useDiscountsStore();
const countryStore = useCountryStore();
const discountData = ref(null);

const page = ref({ title: "Discounts" });

const breadcrumbs = ref([
  {
    text: "Discounts",
    disabled: false,
    href: "/discounts",
  },
  {
    text: "View",
    disabled: true,
  },
]);

const formatUnix = (unix) => {
  if (!unix) return '—';
  const date = new Date(Number(unix) * 1000);
  return date.toLocaleDateString();
};

const getCountryName = (countryId) => {
  return countryStore?.countries?.find((item) => item?.id == countryId)?.name || 'Unknown';
};

const generalFields = [
  { label: 'Amount', value: (d) => `${d?.amount || '0'} (${d?.amount_type})` },
  { label: 'Applies To', value: (d) => d?.applies_to || '—' },
  { label: 'Discount Type', value: (d) => d?.discount_type || '—', type: 'chip', color: () => 'primary' },
  { label: 'Eligibility Type', value: (d) => d?.eligibility_type ?? '—' },
  { label: 'Min Order Amount', value: (d) => d?.min_amount || '—' },
  { label: 'Start Date', value: (d) => formatUnix(d?.start_at) },
  { label: 'Used Count', value: (d) => d?.used_count },
  { label: 'Automatic', value: (d) => d?.is_automatic ? 'Yes' : 'No', type: 'chip', color: () => 'info' },
  { label: 'Status', value: (d) => d?.is_active ? 'Active' : 'Inactive', type: 'chip', color: (d) => d?.is_active ? 'success' : 'error' },
];

onMounted(() => {
  discountStore?.getSingleDiscount({ id: route?.params?.id })?.then((res) => {
    discountData.value = res?.item;
  });
  countryStore.getAllCountries();
});
</script>
