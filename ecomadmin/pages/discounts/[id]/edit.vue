<template>
	<SharedBaseBreadcrumb
		:title="page.title"
		:breadcrumbs="breadcrumbs"
	></SharedBaseBreadcrumb>
	<ProductDiscountForm v-if="discountData" :discountData="discountData"/>
</template>

<script setup>
import * as dayjs from 'dayjs'
import { useDiscountsStore } from '~/stores/discounts';

const route = useRoute();
const discountStore = useDiscountsStore();
const discountData = ref(null);
const page = ref({ title: "Discounts" });

const breadcrumbs = ref([
	{
		text: "Discounts",
		disabled: false,
		href: "/discounts",
	},
	{
		text: "Edit",
		disabled: true,
	},
]);

onMounted(async ()=>{
	await discountStore
		?.getSingleDiscount({id: route?.params?.id})
		?.then((response)=>{

			const _data = response?.item;
			if(_data.buy_products) {
				// _data.buyProductIds = _data.buy_products.map(item => item.id);
				_data.buyProductIds = _data.buy_products;
			}

			if(_data.get_products) {
				// _data.getProductIds = _data.get_products.map(item => item.id);
				_data.getProductIds = _data.get_products;
			}

			if(_data.products) {
				// _data.productIds = _data.products.map(item => item.id);
				_data.productIds = _data.products;
			}

			if(_data.countries) {
				_data.countries = _data.countries.map((item) => ({
					countryId: item.countryId,
					amount: item.amount,
					min_amount: item.min_amount || null,
					min_quantity: item.min_quantity || null,
					startAt: item.startAt || Math.floor(Date.now() / 1000),
					endAt: item.endAt || null,		
					isTimeScheduling: item.isTimeScheduling || false,			
				}))

				_data.countryIds = _data.countries.map((item) => item.countryId);
			}

			discountData.value = _data;

			console.log(discountData.value);
		})

})
</script>