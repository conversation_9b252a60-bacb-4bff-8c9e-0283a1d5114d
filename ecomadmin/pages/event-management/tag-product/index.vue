<template>
  <SharedBaseBreadcrumb
      :title="page.title"
      :breadcrumbs="breadcrumbs"
  ></SharedBaseBreadcrumb>
  <v-row>
    <v-col cols="12">
      <SharedUiParentCardSolid title="Add New Tag Product">
        <v-form v-model="dataForms" @submit.prevent="onSubmit" ref="dataForms">

          <v-row>
            <v-col cols="3">
              <FormElementsCommonFieldContainer label="Country" :required="true">
                <v-select :items="getCountries" item-title="name" item-value="id"
                          :rules="[ val => SELECT_REQUIRED_RULE(val) ]"
                          v-model="dataForm.countryId" placeholder="Select Country"/>
              </FormElementsCommonFieldContainer>
            </v-col>

            <v-col cols="3">
              <FormElementsCommonFieldContainer label="Event Activities" :required="true">
                <v-select :items="getEventActivities" item-title="title" item-value="id"
                          :rules="[ val => SELECT_REQUIRED_RULE(val) ]"
                          v-model="dataForm.eventActivityId"
                          placeholder="Select Events"/>
              </FormElementsCommonFieldContainer>
            </v-col>
            <v-col cols="3">
              <FormElementsCommonFieldContainer label="Start Date">
                <v-text-field

                    v-model="dataForm.startDate"
                    type="date"
                ></v-text-field>
              </FormElementsCommonFieldContainer>
            </v-col>
            <v-col cols="3">
              <FormElementsCommonFieldContainer label="End Date">
                <v-text-field

                    v-model="dataForm.endDate"
                    type="date"
                ></v-text-field>
              </FormElementsCommonFieldContainer>
            </v-col>
          </v-row>
          <div class="text-right">
            <v-btn
                :disabled="!dataForm"
                :loading="loading"
                color="primary"
                variant="flat"
                class="px-6"
                type="submit"
            >
              {{ dataForm.id ? "Update" : "Add New" }} Event
            </v-btn>
            <v-btn
                color="error"
                variant="flat"
                class="px-6 ms-2"
                @click="resetForm"
            >
              Reset
            </v-btn>
          </div>
        </v-form>
      </SharedUiParentCardSolid>
    </v-col>
    <v-col cols="12" sm="4">

      <SharedUiParentCardSolid>
        <div class="d-flex justify-space-between align-center">
          <h2>Event Activity History</h2>
          <div class="w-50">
            <v-text-field :loading="store.searchLoading" append-inner-icon="mdi-magnify" density="compact"
                          label="Search event history" variant="outlined" hide-details single-line
                          @onChange:append-inner="onClick"
                          v-model="options.search" clearable @click:clear="clearSearch"></v-text-field>
          </div>
        </div>
        <v-data-table-server
            v-model:page="options.page"
            v-model:items-per-page="options.itemsPerPage"
            :headers="headers"
            :items="allHistories"
            :search="options.search"
            :items-length="store.pagination ? store.pagination.itemCount : 0"
            :loading="loading"

            item-value="name" @update:options="fetchEventActivityHistory">
          <template v-slot:thead>

            <tr>
              <td>
                <v-text-field v-model="name" class="ma-1" density="compact" placeholder="Search name..."
                              hide-details></v-text-field>
              </td>

            </tr>
          </template>
          <template v-slot:item.country="{ item }">
            {{ item?.country?.name }}
          </template>
          <template v-slot:item.startDate="{ item }">
            {{ item?.startDate ? formatDate(item?.startDate) : '-' }}
          </template>
          <template v-slot:item.endDate="{ item }">
            {{ item?.endDate ? formatDate(item?.endDate) : '-' }}
          </template>

          <template v-slot:item.event="{ item }">
            {{ item?.eventActivity?.title }}

          </template>
          <template v-slot:item.status="{ item }">
            <v-switch
                v-model="item.isActive"
                color="primary"
                defaults-target="success"
                hide-details
                inset
                density="compact"
                @update:model-value="onChangeStatus(item)"
            ></v-switch>
          </template>
          <template v-slot:item.action="{ item }">
            <div class="text-end">

              <v-menu>
                <template v-slot:activator="{ props }">
                  <v-btn class="me-2" variant="tonal" size="small" icon="mdi-dots-vertical" v-bind="props"></v-btn>
                </template>


                <v-list>
                  <v-list-item>
                    <v-btn icon color="warning" class="me-2" variant="tonal" size="small"
                           @click="editPayloadItem(item, dataForm)">
                      <PencilIcon size="20"/>
                    </v-btn>
                  </v-list-item>
                  <v-list-item>
                    <v-btn icon color="success" class="me-2" variant="tonal" size="small" @click="tagItem(item)">
                      <TagIcon size="20"/>
                    </v-btn>
                  </v-list-item>
                  <v-list-item>
                    <v-btn icon color="error" variant="tonal" size="small"  @click="confirmDialog = true; itemToDelete = item.id">
                      <TrashIcon size="20"/>
                    </v-btn>
                  </v-list-item>

                </v-list>
              </v-menu>

            </div>
          </template>
        </v-data-table-server>
      </SharedUiParentCardSolid>
    </v-col>
    <v-col cols="12" sm="8" v-if="isEnableProductTagging">
      <template v-if="store.loading">
        <SharedUiLoader
            type="table-heading, table-thead, table-tbody, table-tfoot"
        />
      </template>
      <SharedUiParentCard title="Tagging Products" v-else>

        <v-form v-model="productDataForms" @submit.prevent="onTagProduct" ref="productDataForms">
          <v-row class="align-center">
            <!-- <v-col cols="5">
              <FormElementsCommonFieldContainer label="Select Product" :required="true">
                <v-select :items="getProductsName" item-title="name" item-value="id" v-model="productDataForm.productIds"
                          :rules="[ val => SELECT_REQUIRED_RULE(val) ]"
                          placeholder="Select Product" multiple/>
              </FormElementsCommonFieldContainer>
            </v-col> -->
            <v-col cols="5">
              <v-autocomplete
                  variant="outlined" hide-details
                  :rules="[ val => SELECT_REQUIRED_RULE(val) ]"
                  :items="getProductsName"
                  item-title="name"
                  item-value="id"
                  label="Select Products"
                  chips
                  closable-chips
                  multiple
                  v-model="productDataForm.productIds"
                  @update:search="searchForProducts"
                  density="compact"
              >
                <template v-slot:item="{ props, item }">
                  <v-list-item
                      v-bind="props"
                      :subtitle="item.raw.group"
                      :title="item.raw.name"
                      @click="selectProduct(item.raw)"
                  >
                    <template #prepend>
                      <v-avatar size="40">
                        <v-img :src="item.raw.image" alt="Avatar" cover />
                      </v-avatar>
                    </template>
                  </v-list-item>
                </template>
              </v-autocomplete>
            </v-col>
            <v-col cols="2">
              <!-- <v-btn
                  :disabled="!productDataForm.productIds || productDataForm.productIds.length === 0"
                  :loading="loading"
                  color="primary"
                  variant="tonal"
                  class="px-6"
                  type="submit"
              >
                Tag Products
              </v-btn> -->
              <v-btn @click="saveEventTaggingProduct" color="primary" variant="flat" class="px-6">Save</v-btn>
            </v-col>
          </v-row>
        </v-form>
        <v-divider class="my-5"></v-divider>
        <template v-if="store?.eventTaggingProducts?.length> 0">
          <draggable :itemKey="'event_product'" v-model="getEventTaggingProducts" tag="div" class="d-flex flex-wrap ga-2">
            <template #item="{ element }">
              <v-sheet class="position-relative pa-2">
                <v-avatar @click="confirmDialogFortagging = true; itemToTaggingDelete = element?.id" color="error" size="15" class="position-absolute cursor-pointer" style="z-index: 9999; top:-5px; right: -5px">
                  <v-icon icon="mdi-close" size="10">
                  </v-icon>
                </v-avatar>
                <div class="d-flex border flex-column align-center ga-1">
                  <v-img :src="element?.product?.featuredImage?.imageGalleryUrls?.medium || element?.product?.featuredImage?.imageUrl" width="80" height="50" cover/>
                  <p class="text-caption font-weight-bold text-truncate" style="width: 100px">{{ element?.product?.name }}</p>
                </div>
              </v-sheet>
            </template>
          </draggable>
        </template>
        <template v-else> 
          <h1 class="text-center text-h1">Sorry no Data Found.</h1>  
        </template>

      </SharedUiParentCard>
    </v-col>
  </v-row>
  <ConformationModal v-model:dialog="confirmDialog" @confirm-delete="handleDeleteConfirmation" />
  <ConformationModal v-model:dialog="confirmDialogFortagging" @confirm-delete="handleDeleteConfirmationTagging" />

</template>

<script setup lang="ts">
//***************************Imports******************//
import {PencilIcon, TagIcon, TrashIcon} from "vue-tabler-icons";
import {computed, onMounted, ref, watch} from "vue";
import {editPayloadItem, formatDate} from "~/utils/helpers/functions";
import {DynamicModuleEnum, DynamicPermissionEnum} from "~/utils/roleManagement";
import {useEventActivityStore} from "~/stores/others/event-activity";
import {useCountryStore} from "~/stores/others/country";
import {useProductsStore} from "~/stores/products";
import ConformationModal from "~/components/modals/ConformationModal.vue";
import { useRolePermissionsStore } from "~/stores/administration/permissions";

//***************************Variables******************//
const store = useEventActivityStore();
const storeProduct = useProductsStore();
const countryStore = useCountryStore();
// const currStore = useCurrencyStore();
const permissionStore = useRolePermissionsStore();

const page = ref({title: "Tagging Products"});

const breadcrumbs = ref([
  {
    text: "Dashboard",
    disabled: false,
    to: "/dashboard",
  },
  {
    text: "Tagging Products",
    disabled: true,
    to: "",
  },
]);

const headers = ref([
  {title: 'Country', align: 'start', sortable: false, key: 'country'},
  {title: 'Events', align: 'start', sortable: false, key: 'event'},
  {title: 'Start Date', align: 'start', sortable: false, key: 'startDate'},
  {title: 'End Date', align: 'start', sortable: false, key: 'endDate'},
  {title: 'Status', key: 'status', sortable: false, align: 'start'},
  {title: 'Action', key: 'action', align: 'center', sortable: false},
])
const options = ref({
  page: 1,
  itemsPerPage: 20,
  sortBy: ['name'],
  sortDesc: [false],
  search: '',
});
const search = ref('');
const name = ref('');
const allHistories = ref([]);

const tableHeads = ["Country", "Events", "Status", "Action"];
const tableProductHeads = ["Product Name", "Category", "Action"];

const isEnableProductTagging = ref(false);

const snackbar = useSnackbar();
const dataForms = ref();
const productDataForms = ref();
const loading = ref(false);
const countryCode = ref('');

const dataForm = ref({
  id: null,
  countryId: null,
  eventActivityId: null,
  startDate: null,
  endDate: null,
  isActive: true
});

const productDataForm = ref({
  id: null,
  productIds: null,
  countryId: null,
  eventActivityHistoryId: null,
  isActive: true
});



function onClick() {
  if (!search) return;
  store.getEventActivityHistory();
}

const clearSearch = () => {
  fetchEventActivityHistory();
}

const fetchEventActivityHistory = async () => {

  const {page, itemsPerPage, sortBy, sortDesc, search} = options.value;
  const order = sortDesc[0] ? 'DESC' : 'ASC';
  const sortKey = sortBy[0] || 'name';
  loading.value = true;
  await store.getEventActivityHistory(order, page, itemsPerPage, sortKey, search);
  allHistories.value = store.eventActivitiesHistory;
  loading.value = false;

}
// onMounted(fetchEventActivityHistory);
// watch(search, () => {
//   fetchEventActivityHistory();
// });
watch(name, (val, oldValue) => {
  let arraydata = [];
  allHistories.value.filter((item) => {
    if (item.title.toLowerCase().includes(val)) {
      arraydata.push(item);
    }

  })
  if (val?.length === 0) {
    arraydata = store.eventActivities;
  }
  allHistories.value = arraydata;
})


const searchForProducts = (value: any) => {
  if (value?.length > 3) {
    if(!permissionStore.hasModulePermission(`${DynamicModuleEnum.PRODUCT}:${DynamicPermissionEnum.READ_MANY}`)){
      snackbar.add({
        type: "warning",
        text: "You don't have permission to READ MANY",
      })
      return;
    }

    store.searchProduct(value, countryCode.value)
  }

  if (value?.length <= 3) {
    store.searchProduct('ALL', countryCode.value)
  }
}


//***************************Methods******************//

const onSubmit = () => {
  if (!dataForms.value) return;

  loading.value = true;
  store
      .addUpdateEventActivityHistory(dataForm.value)
      .then((res: any) => {
        snackbar.add({
          type: (res && res?.success && res?.data) ? "success" : ((res && res?.success && !res?.data)) ? "warning" : "error",
          text: res?.message,
        });
      })
      .catch((err) => {
        snackbar.add({
          type: "error",
          text: err,
        });
      });

  loading.value = false;
  resetForm();
};
const onTagProduct = () => {
  if (!productDataForms.value) return;

  loading.value = true;

  store.addUpdateEventActivityTagProduct(productDataForm.value)
      .then((res: any) => {
        console.log('tarif event', res);

        snackbar.add({
          type: (res && res?.success && res?.data) ? "success" : ((res && res?.success && !res?.data)) ? "warning" : "error",
          text: res?.message,
        });

        // If successful, refresh the tagged products list
        if (res && res?.success && res?.data && productDataForm.value.eventActivityHistoryId && productDataForm.value.countryId) {
          fetchTagProductWithEventActivity(productDataForm.value.eventActivityHistoryId, productDataForm.value.countryId);
        }

        resetProductForm();
      })
      .catch((err) => {
        snackbar.add({
          type: "error",
          text: err,
        });
      });

  loading.value = false;
};

// const fetchEventActivityHistory = (order = "ASC", page = 1, take = 10) => {
//   store.getEventActivityHistory({order: order, page: page, take: take});
// };

const fetchTagProductWithEventActivity = (id: number, countryId: number) => {
  store.fetchTagProductWithEventActivity({eventActivityHistoryId: id});
  storeProduct.getCountryWiseAllProducts({countryId: countryId});
};

const deleteItem = (id: number) => {
  store.deleteEventActivityHistory(id).then((res) => {
    snackbar.add({
      type: res ? "success" : "error",
      text: res,
    });
  });
};

const onChangeStatus = (item: any) => {
  store.changeStatusEventActivityHistory(item?.id, item?.isActive).then((res) => {
    console.log('res', res);

    snackbar.add({
      type: (res && res?.success && res?.data) ? "success" : ((res && res?.success && !res?.data)) ? "warning" : "error",
      text: res?.message,
    });
  });
};

const tagItem = async (item: any) => {
  isEnableProductTagging.value = true;
  console.log('tag item', item?.country?.code);
  await store.searchProduct('ALL', item?.country?.code);
  countryCode.value = item?.country?.code;
  fetchTagProductWithEventActivity(item?.id, item?.countryId);
  productDataForm.value.eventActivityHistoryId = item?.id;
  productDataForm.value.countryId = item?.countryId;
};

const onRemoveTaggingProduct = (id: number) => {
  if (id) {
    loading.value = true;
    store.deleteEventActivityHistory(id)
        .then((res: any) => {
          snackbar.add({
            type: res ? "success" : "error",
            text: res,
          });
        })
        .catch((err: any) => {
          console.log(err);
        });

    loading.value = false;
  }

};

const resetForm = () => {
  dataForms.value.reset();

  dataForm.value.id = null;
  dataForm.value.countryId = null,
      dataForm.value.eventActivityId = null,
      dataForm.value.startDate = null,
      dataForm.value.endDate = null,
      dataForm.value.isActive = true;
};

const resetProductForm = () => {
  productDataForms.value.reset();
  productDataForm.value.productIds = null;
};

//***************************Lifecycle Hooks******************//
// const getEventActivities = computed(() => {
//   return currStore.eventActivities;
// });

onMounted(async () => {
  // fetchEventActivityHistory();
  await countryStore.getAllCountries();
  await store.getAllEventActivities();

});

const getCountries = computed(() => countryStore.countries);
const getEventActivities = computed(() => store.eventActivities);
const getEventActivitiesHistory = computed(() => store.eventActivitiesHistory);
const getEventTaggingProducts = ref(store.eventTaggingProducts);
const getProductsName = computed(() => {
  // Get all search products
  const allProducts = store.searchProducts || [];

  // Get IDs of products already in the draggable list
  const taggedProductIds = (getEventTaggingProducts.value || [])
    .map((item: any) => item?.product?.id)
    .filter((id: any) => id !== undefined && id !== null);

  // Filter out products that are already tagged
  return allProducts.filter((product: any) => !taggedProductIds.includes(product.id));
});


const confirmDialog = ref(false);
const itemToDelete = ref<number | null>(null);
const handleDeleteConfirmation = async () => {
  if (itemToDelete.value !== null) {
    const id = itemToDelete.value;
    // Call the delete product API
    const res = await store.deleteEventActivityHistory(id);
    // Handle response
    if (res) {
      snackbar.add({
        type: 'success',
        text: 'Event deleted successfully!',
      });
      confirmDialog.value = false;

    } else {
      snackbar.add({
        type: 'error',
        text: 'Failed to delete event.',
      });
    }
  }
};
const confirmDialogFortagging = ref(false);
const itemToTaggingDelete = ref<number | null>(null);
const handleDeleteConfirmationTagging = async () => {
  if (itemToTaggingDelete.value !== null) {
    const id = itemToTaggingDelete.value;
    // Call the delete product API
    const res = await store.removeTaggingProduct(id);
    // Handle response
    if (res) {
      snackbar.add({
        type: 'success',
        text: 'Event deleted successfully!',
      });
      confirmDialog.value = false;

    } else {
      snackbar.add({
        type: 'error',
        text: 'Failed to delete event.',
      });
    }
  }
};

watch(
  ()=> store?.eventTaggingProducts, 
  () => {
    console.log("ASDASS");
    getEventTaggingProducts.value = store.eventTaggingProducts;
  }, 
  {deep: true}
);

const selectProduct = (product: any) => {
  let payload = {
    id: product?.id,
    product:{
      id:product?.id,
      name: product?.name,
      featuredImage:{
        imageUrl: product?.image
      }
    }
  }
  // If product is already inside the draggable list just return
  if(getEventTaggingProducts.value.some((item: any) => item.id === product.id)) return;
  getEventTaggingProducts.value.push(payload);
}

const saveEventTaggingProduct = ()=>{
  if(getEventTaggingProducts.value.length === 0){
    snackbar.add({
      type: 'warning',
      text: 'Please select at least one product',
    })
    return;
  }

  let payload = {
    countryId: productDataForm.value.countryId,
    eventActivityHistoryId: productDataForm.value.eventActivityHistoryId,
    eventActivityProductIds: getEventTaggingProducts.value.map((item, index) => {
      return {
        productId: item?.product?.id,
        sortOrder: index,
      }
    }) 
  }
  loading.value = true;

  store
    ?.addUpdateEventActivityTagProduct(payload)
    ?.then((res: any) => {

      snackbar.add({
        type: (res && res?.success && res?.data) ? "success" : ((res && res?.success && !res?.data)) ? "warning" : "error",
        text: res?.message,
      });

      // If successful, refresh the tagged products list
      if (res && res?.success && res?.data && productDataForm.value.eventActivityHistoryId && productDataForm.value.countryId) {
        fetchTagProductWithEventActivity(productDataForm.value.eventActivityHistoryId, productDataForm.value.countryId);
      }

      resetProductForm();
    })
    ?.catch((err) => {
      snackbar.add({
        type: "error",
        text: err,
      });
    })
    ?.finally(()=> {
      loading.value = false;
    });
}
</script>
