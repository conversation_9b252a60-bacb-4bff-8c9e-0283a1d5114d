<template>
  <SharedBaseBreadcrumb
      :title="page.title"
      :breadcrumbs="breadcrumbs"
  ></SharedBaseBreadcrumb>
  <v-row>
    <v-col cols="4">
      <SharedUiParentCard
          :title="eventActivity.id ? 'Edit ' + eventActivity.title : 'Add New Event Activity'"
      >
        <v-form v-model="eventActivityForm" @submit.prevent="onSubmit" ref="eventActivityForm">
          <FormElementsCommonFieldContainer label="Title" :required="true">
            <v-text-field
                v-model="eventActivity.title"
                :rules="[(val) => REQUIRED_RULE(val)]"
            ></v-text-field>
          </FormElementsCommonFieldContainer>
          <FormElementsCommonFieldContainer label="Description">
            <FormElementsEditor
                v-model="eventActivity.description"
            />
          </FormElementsCommonFieldContainer>

          <v-switch
              v-model="eventActivity.isActive"
              color="primary"
              defaults-target="success"
              :label="((eventActivity.isActive)? 'Active': 'Inactive')"
              hide-details
              inset
          ></v-switch>
          <v-divider class="my-5"></v-divider>
          <v-btn
              :disabled="!eventActivityForm"
              :loading="loading"
              color="primary"
              variant="tonal"
              class="px-6"
              type="submit"
          >
            {{ eventActivity.id ? "Update" : "Add New" }} Event
          </v-btn>
          <v-btn
              color="error"
              variant="tonal"
              class="px-6 ms-2"
              @click="resetForm"
          >
            Reset
          </v-btn>
        </v-form>
      </SharedUiParentCard>
    </v-col>
    <v-col cols="8">

      <SharedUiParentCard>
        <div class="d-flex justify-space-between align-center">
          <h2>Events</h2>
          <div class="w-50">
            <v-text-field :loading="store.searchLoading" append-inner-icon="mdi-magnify" density="compact"
                          label="Search category" variant="outlined" hide-details single-line
                          @onChange:append-inner="onClick"
                          v-model="options.search" clearable @click:clear="clearSearch"></v-text-field>
          </div>

        </div>

        <v-data-table-server
            v-model:page="options.page"
            v-model:items-per-page="options.itemsPerPage"
            :headers="headers"
            :items="allEvents"
            :items-length="store.pagination ? store.pagination.itemCount : 0"
            :search="options.search"
            :loading="loading"
            item-value="name"
            @update:options="fetchEventActivities">
          <template v-slot:thead>

            <tr>
              <td>
                <v-text-field v-model="name" class="ma-1" density="compact" placeholder="Search name..."
                              hide-details></v-text-field>
              </td>

            </tr>
          </template>
          <template v-slot:item.description="{ item }">
            <span v-html="item?.description"></span>
          </template>
          <template v-slot:item.status="{ item }">
            <v-chip :color="item?.isActive ? 'success' : 'error'" variant="elevated">
              {{ item?.isActive ? 'Active' : 'Inactive' }}
            </v-chip>
          </template>
          <template v-slot:item.action="{ item }">
            <div class="text-end">

              <v-menu>
                <template v-slot:activator="{ props }">
                  <v-btn class="me-2" variant="tonal" size="small" icon="mdi-dots-vertical" v-bind="props"></v-btn>
                </template>

                <v-list>
                  <v-list-item>
                    <v-btn
                        icon
                        color="warning"
                        class="me-2"
                        variant="tonal"
                        size="small"
                        @click="editPayloadItem(item, eventActivity)"
                    >
                      <PencilIcon size="20"/>
                    </v-btn>
                  </v-list-item>
                  <v-list-item>
                    <v-btn
                        icon
                        color="error"
                        variant="tonal"
                        size="small"
                        @click="confirmDialog = true; itemToDelete = item.id"
                    >
                      <TrashIcon size="20"/>
                    </v-btn>
                  </v-list-item>
                </v-list>
              </v-menu>

            </div>
          </template>
        </v-data-table-server>
      </SharedUiParentCard>
    </v-col>
  </v-row>
  <ConformationModal v-model:dialog="confirmDialog" @confirm-delete="handleDeleteConfirmation" />

</template>

<script setup lang="ts">
//***************************Imports******************//
import {PencilIcon, TrashIcon} from "vue-tabler-icons";
import {computed, onMounted, ref, watch} from "vue";
import {editPayloadItem} from "~/utils/helpers/functions";
import {REQUIRED_RULE} from "~/utils/formRules";
import {useEventActivityStore} from "~/stores/others/event-activity";
import ConformationModal from "~/components/modals/ConformationModal.vue";

//***************************Variables******************//
const store = useEventActivityStore();
// const currStore = useCurrencyStore();
const page = ref({title: "Events"});

const breadcrumbs = ref([
  {
    text: "Dashboard",
    disabled: false,
    to: "/dashboard",
  },
  {
    text: "Events",
    disabled: true,
    to: "",
  },
]);


const headers = ref([
  {title: 'Title', align: 'start', sortable: false, key: 'title'},
  {title: 'Slug', align: 'start', sortable: false, key: 'slug'},
  {title: 'description', key: 'description', sortable: false, align: 'start'},
  {title: 'Status', key: 'status', sortable: false, align: 'start'},
  {title: 'Action', key: 'action', align: 'center', sortable: false},
])
const options = ref({
  page: 1,
  itemsPerPage: 20,
  sortBy: ['name'],
  sortDesc: [false],
  search: '',
});
const search = ref('');
const name = ref('');
const allEvents = ref([]);


const getEventActivities = computed(() => {
  return store.eventActivities;
});

const snackbar = useSnackbar();
const eventActivityForm = ref();
const loading = ref(false);

const eventActivity = ref({
  id: null,
  title: '',
  description: '',
  isActive: true
});

function onClick() {
  if (!search) return;
  store.getAllEventActivities();
}

const clearSearch = () => {
  fetchEventActivities();
}

const fetchEventActivities = async () => {

  const {page, itemsPerPage, sortBy, sortDesc, search} = options.value;
  const order = sortDesc[0] ? 'DESC' : 'ASC';
  const sortKey = sortBy[0] || 'name';
  loading.value = true;
  await store.getEventActivities(order, page, itemsPerPage, sortKey, search);
  allEvents.value = store.eventActivities;
  loading.value = false;

}
// onMounted(fetchEventActivities);
// watch(search, () => {
//   fetchEventActivities();
// });
watch(name, (val, oldValue) => {
  let arraydata = [];
  allEvents.value.filter((item) => {
    if (item.title.toLowerCase().includes(val)) {
      arraydata.push(item);
    }

  })
  if (val.length === 0) {
    arraydata = store.eventActivities;
  }
  allEvents.value = arraydata;
})


//***************************Methods******************//

const onSubmit = () => {
  if (!eventActivityForm.value) return;

  loading.value = true;
  store
      .addUpdateEventActivity(eventActivity.value)
      .then((res: any) => {
        snackbar.add({
          type: res.success ? "success" : "error",
          text: res.message,
        });
      })
      .catch((err) => {
        snackbar.add({
          type: "error",
          text: err,
        });
      });

  loading.value = false;
  resetForm();
};

const deleteItem = (id: number) => {
  store.deleteEventActivity(id).then((res) => {
    snackbar.add({
      type: (res) ? "success" : "error",
      text: res,
    });
  });
};

const resetForm = () => {
  eventActivityForm.value.reset();
  eventActivity.value.id = null;
  eventActivity.value.title = null;
  eventActivity.value.description = '';
};


const confirmDialog = ref(false);
const itemToDelete = ref<number | null>(null);
const handleDeleteConfirmation = async () => {
  if (itemToDelete.value !== null) {
    const productId = itemToDelete.value;
    // Call the delete product API
    const res = await store.deleteEventActivity(productId);
    // Handle response
    if (res) {
      snackbar.add({
        type: 'success',
        text: 'Event deleted successfully!',
      });
      confirmDialog.value = false;

    } else {
      snackbar.add({
        type: 'error',
        text: 'Failed to delete event.',
      });
    }
  }
};


</script>
