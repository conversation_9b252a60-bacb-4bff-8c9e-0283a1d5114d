<template>
  <div>
    <v-btn @click="openDialog">Open Media Manager</v-btn>

    <v-dialog v-model="dialog" max-width="600">
      <v-card>
        <v-card-title>
          Media Manager
          <v-spacer></v-spacer>
          <v-btn icon @click="closeDialog">
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-card-title>
        <v-card-text>
          <!-- Your media manager content here -->
          <v-file-input v-model="selectedFile" label="Select file" accept="image/*" multiple></v-file-input>
          <v-img v-if="selectedFile" :src="selectedFile"></v-img>
        </v-card-text>
        <v-card-actions>
          <v-btn color="primary" @click="upload">Upload</v-btn>
          <v-btn @click="closeDialog">Cancel</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-row class="mt-5">
      <v-col v-for="image in imageStore.images" :key="image.id" cols="2">
        <v-img
            :src="image.imageUrl"
            aspect-ratio="1"
            class="bg-grey-lighten-2"
            cover
        >
        </v-img>
      </v-col>
    </v-row>
  </div>
</template>

<script setup lang="ts">
import {ref, onMounted} from 'vue';
import {useProductImagesStore} from "~/stores/products/product-images";

const imageStore = useProductImagesStore();

const dialog = ref(false);
const selectedFile = ref(null);
const selectedItem = ref(null);

const openDialog = () => {
  dialog.value = true;
};

const closeDialog = () => {
  dialog.value = false;
  selectedFile.value = null;
};

const upload = () => {
  // Handle file upload logic here
  console.log('File uploaded:', selectedFile.value);

  imageStore.addImage( selectedFile.value )

  closeDialog();
};

const handleChange = () => {
  console.log('Selected item:', selectedItem);
}

onMounted(() => {
  imageStore.getImages({})
});

</script>

<style>
/* Add custom styles here if needed */
</style>
