<template>
  <div>
    <BaseBreadcrumb :title="'Invoice No.' + refundStore.refundDetails.invoiceNo" :breadcrumbs="breadcrumbs"></BaseBreadcrumb>

    <template v-if="refundStore.loading">Loading data...</template>
    <template v-else>
      <h3 class="text-error mb-4 text-center">Status: {{ refundStatus }}</h3>
      <v-row>
        <v-col cols="6">
          <v-card>
            <v-card-title>Product Details for Return</v-card-title>
            <v-card-text>
              <div class="d-flex">
                <div>
                  <v-img :src="product?.featuredImage?.imageUrl" width="100"/>
                </div>
                <div class="ms-2">
                  <h2>{{ product?.name }}</h2>
                  <p v-if="refundStore.refundDetails?.sku">SKU: {{ refundStore.refundDetails?.sku }}</p>
                  <p v-if="refundStore.refundDetails?.color">Color: {{ refundStore.refundDetails?.color }}</p>
                  <p v-if="refundStore.refundDetails?.size">Size: {{ refundStore.refundDetails?.size }}</p>
                  <p v-if="refundStore.refundDetails?.quantity">Quantity: {{ refundStore.refundDetails?.quantity }}</p>
                  <p v-if="refundStore.refundDetails?.productRefundType?.type">Refund Type: {{ refundStore.refundDetails?.refundStatus }}</p>
                  <p v-if="refundStore.refundDetails?.refundReason">Refund Reason: {{ refundStore.refundDetails?.refundReason }}</p>
                </div>
              </div>
            </v-card-text>
          </v-card>
        </v-col>
        <v-col cols="6">
          <v-card>
            <v-card-title>Customer Uploaded Images</v-card-title>
            <v-card-text>
              <v-row>
                <v-col cols="3" v-for="image in refundImages" :key="image.id">
                  <a :href="image.imageUrl" target="_blank">
                  <v-img :src="image.imageUrl"></v-img>
                </a>
                </v-col>
              </v-row>
            </v-card-text>
          </v-card>
        </v-col>
        <v-col cols="6">
          <v-card>
            <v-card-title>Customer Details</v-card-title>
            <v-card-text>
              <p v-if="customer?.firstName">
                Name: {{ customer?.firstName + ' ' +customer?.lastName}}</p>
              <p v-if="customer?.email">Email: {{ customer?.email }}</p>
              <p v-if="customer?.phone">Phone: {{ customer?.phone }}</p>
            </v-card-text>
          </v-card>
        </v-col>
        <v-col cols="6">
          <v-card>
            <v-card-title>Payment Details</v-card-title>
            <v-card-text>
              <p v-if="productOrder?.paymentMethod">Payment Method: {{ productOrder?.paymentMethod }}</p>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
      <v-row>
        <v-col cols="6" offset="3">
          <v-card>
            <v-card-title>Change Return Status</v-card-title>
            <v-card-text>
              <v-form @submit.prevent="updateStatus">
                <v-select :items="refundStatusList" item-title="name" item-value="name" v-model="currentStatus"></v-select>
                <v-btn type="submit" color="primary" block :loading="refundStore.loading">Change Status</v-btn>
              </v-form>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
    </template>
  </div>
</template>
<script setup lang="ts">

import {useRefundStore} from "~/stores/refund";
import BaseBreadcrumb from "~/components/shared/BaseBreadcrumb.vue";
const route = useRoute();
const refundStore = useRefundStore();
const snackbar = useSnackbar();
const breadcrumbs = ref([
  {
    text: 'Dashboard',
    disabled: false,
    href: '/'
  },
  {
    text: 'Returns',
    disabled: false,
    href: '/return-list'
  },
  {
    text: route.params.id,
    disabled: true,
    href: ''
  }
]);

const refundImages = computed(() => refundStore.refundDetails.images);
const product = computed(() => refundStore.refundDetails?.product);
const customer = computed(() => refundStore.refundDetails?.productOrder?.billingAddress);
const productOrder = computed(() => refundStore.refundDetails?.productOrder)

const refundStatus = computed(() => refundStore?.refundDetails?.refundStatus)

const refundStatusList = ref([
  { type: 'Requested_For_Return', name: 'Requested For Return'},
  { type: 'Return_Processing', name: 'Return Processing'},
  { type: 'Return_Approved', name: 'Return Approved'},
  { type: 'Return_Rejected', name: 'Return Rejected'}
])

const currentStatus = ref();

const updateStatus = async () => {
  const response = await refundStore.updateRefundStatus(route.params.id, currentStatus.value)
  if( response?.isSuccess ) {
    snackbar.add({
      type: 'success',
      text: response.messasge
    })
  }
}

onMounted(async () => {
  const response = await refundStore.getRefund(route.params.id);

  if( response.isSuccess ) {
    currentStatus.value = refundStatus.value
  }

})

</script>