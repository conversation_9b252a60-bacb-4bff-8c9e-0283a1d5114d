<template>
  <div>
    <SharedBaseBreadcrumb :title=" `RedX Courier` " :breadcrumbs="items"/>
    <v-form @submit.prevent="onSubmit" ref="redxForm" v-model="courierForm">
      <v-row>
        <v-col cols="12">
          <v-card class="mx-auto my-8 pa-5">
            <v-card-title>Customer Information</v-card-title>
            <v-row dense>
              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field label="Phone Number *" :required="true"
                                v-model="serviceInfo.deliveryAddress.ContactPhone" :rules="rules"></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>
              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field label="First name *" :required="true"
                                v-model="serviceInfo.deliveryAddress.ContactName" :rules="rules"></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>
              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field label="Customer Address *" :required="true"
                                v-model="fullAddress" :rules="rules"></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-autocomplete
                  label="Select Area *" :items="redx_area" :rules="rules" :required="true" item-value="id" item-title="name"
                                              v-model="form.deliveryAreaId"
                  ></v-autocomplete>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-select label="Select Location *" :items="redx_location" :rules="rules" :required="true" item-value="id" item-title="label"
                            v-model="form.pickupStoreId"></v-select>
                </FormElementsCommonFieldContainer>
              </v-col>
            </v-row>
            <small class="text-caption text-medium-emphasis">*indicates required field</small>
            <v-divider></v-divider>
            <v-spacer></v-spacer>
          </v-card>
        </v-col>
      </v-row>
      <v-row>
        <v-col cols="12">
          <v-card class="mx-auto my-2 pa-5">
            <v-card-title> Delivery Information</v-card-title>
            <v-row dense>
              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field label="Invoice ID *" :required="true"
                                v-model="serviceInfo.order.invoiceNo" :rules="rules"></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>
              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field label="Parcel Weight *" :required="true"
                                v-model="form.parcelWeight" :rules="rules"></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>
              <v-col cols="12" md="4" sm="6" v-if="serviceInfo.order.paymentMethod == 'COD'">
                <FormElementsCommonFieldContainer>
                  <v-text-field label="Cash Collection Amount *" :required="true"
                                v-model="serviceInfo.order.amount" :rules="rules"></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>


              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field label="Value" :required="true" v-model="serviceInfo.order.amount" :rules="rules"
                                ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>
              <v-col cols="12">
                <FormElementsCommonFieldContainer>
                  <v-textarea label="Note"  v-model="form.instruction" rows="2" auto-grow
                  ></v-textarea>
                </FormElementsCommonFieldContainer>
              </v-col>
              
              <v-col cols="12" md="4" sm="6">
                <v-checkbox
                  v-model="form.isClosedBox"
                  color="primary"
                  label="Do not open the box?"
                  hide-details
                ></v-checkbox>
              </v-col>
            </v-row>
            <small class="ms-3 text-caption text-medium-emphasis">*indicates required field</small>
            <v-divider></v-divider>
            <v-spacer></v-spacer>
          </v-card>
        </v-col>
      </v-row>
      <v-btn
          text="Cancel"
          variant="plain"
      ></v-btn>
      <v-btn
          :disabled="!courierForm" :loading="loading"
          color="primary"
          text="Save"
          variant="tonal"
          @click="onSubmit"
      ></v-btn>
    </v-form>
  </div>
</template>
<script setup lang="ts">
import {useOrdersStore} from "~/stores/orders";
import {ref} from "vue";
import { useRolePermissionsStore } from "~/stores/administration/permissions";
import {DynamicModuleEnum, DynamicPermissionEnum} from "~/utils/roleManagement"

const orderStore = useOrdersStore()
const permissionStore = useRolePermissionsStore();
const snackbar = useSnackbar();

const form = ref({
  customerName: '',
  customerPhone: '',
  customerAddress: '',
  deliveryArea: '',
  deliveryAreaId: '',
  cashCollectionAmount: '',
  value: '',
  parcelWeight: '1',
  merchantInvoiceId: '',
  instruction: 'কাস্টমার প্যাকেট খুলে প্রোডাক্ট চেক করে দেখতে পারবে। কাস্টমার যদি প্রোডাক্ট রিটার্ন করতে চায়, সেক্ষেত্রে ১০০ টাকা ডেলিভারি চার্জ দিতে হবে।',
  parcelType: '',
  parcelDetailsJson: [],
  pickupStoreId: "",
  isClosedBox: false,
});
const courierForm = ref(false);
const route = useRoute();
const items = [
  {
    text: 'Dashboard',
    disabled: false,
    to: '/dashboard',
  },
  {
    text: 'Orders',
    disabled: false,
    to: '/order-list',
  },
  {
    text: route.params.id,
    disabled: true,
    to: '',
  },

];
const redx_area = ref([]);
const redx_location = ref([]);
const redxArea = ref('');
const loading = ref(false);
const serviceInfo = ref({
  deliveryAddress: {
    Name: '',
    Street: '',
    HouseNumber: '',
    HouseNumberInfo: '',
    City: '',
    ZipCode: '',
    CountryIsoCode: '',
    ContactName: '',
    ContactPhone: '',
    ContactEmail: ''
  },
  order: {
    invoiceNo: '',
    amount: '',
    parcel_weight: 1,
    id: '',
  }
});
const rules = [
  (value: any) => {
    if (value) return true;
    return 'You must enter a name'
  }
];

onMounted(async () => {
  await fetchRedXArea();
  await fetchRedXLocation();
  await fetchServiceInfo();
})

const fullAddress = computed({
  get() {
    const {HouseNumber, Street, City, ZipCode} = serviceInfo.value.deliveryAddress;

    // Handle null or undefined values by converting them to empty strings
    return `${HouseNumber || ''} ${Street || ''} ${City || ''} ${ZipCode || ''}`.trim();
  },
  set(newValue) {
    if (!newValue) {
      // If the newValue is null or undefined, reset all parts to empty strings
      serviceInfo.value.deliveryAddress.HouseNumber = '';
      serviceInfo.value.deliveryAddress.Street = '';
      serviceInfo.value.deliveryAddress.City = '';
      serviceInfo.value.deliveryAddress.ZipCode = '';
      return;
    }

    // Split the new value by spaces
    const parts = newValue.split(' ');

    serviceInfo.value.deliveryAddress.HouseNumber = parts[0] || '';
    serviceInfo.value.deliveryAddress.Street = parts.slice(1, parts.length - 2).join(' ') || '';
    serviceInfo.value.deliveryAddress.City = parts[parts.length - 2] || '';
    serviceInfo.value.deliveryAddress.ZipCode = parts[parts.length - 1] || '';
  }
});
const fetchRedXArea = async () => {
  if(!permissionStore.hasModulePermission(`${DynamicModuleEnum.COURIER}:${DynamicPermissionEnum.READ_MANY}`)){
    snackbar.add({
      type: "warning",
      text: "You don't have permission to see redx areas",
    })
    return;
  }

  await orderStore.getRedxArea();
  redx_area.value = orderStore.redxAreas;
};
const fetchRedXLocation = async () => {
  if(!permissionStore.hasModulePermission(`${DynamicModuleEnum.COURIER}:${DynamicPermissionEnum.READ_MANY}`)){
    snackbar.add({
      type: "warning",
      text: "You don't have permission to see redx pickup stores",
    })
    return;
  }

  await orderStore.getRedxLocation();
  redx_location.value = orderStore.redxLocation;
};

const fetchServiceInfo = async () => {
  await orderStore.getServiceInfo(route.params.id, 'redx');
  serviceInfo.value = orderStore.serviceInfo;
};

const saveParcelInfoToLocal = async (parcelInfo: any) => {
  try{
    let payload = {
      courierId: route?.query?.courierId,
      code: 'redx',
      parcelInfo: parcelInfo,
    }
    localStorage.setItem(`hd_parcelInfo_${route?.params?.id}`, JSON.stringify(payload));
  }catch(e){
    console.log("Parcel info saving to local issue: ", e);
  }
};

const onSubmit = async () => {
  if(!permissionStore.hasModulePermission(`${DynamicModuleEnum.COURIER}:${DynamicPermissionEnum.CREATE}`)){
    snackbar.add({
      type: "warning",
      text: "You don't have permission to create redx courier",
    })
    return;
  }

  loading.value = true;
  const deliveryArea = redx_area.value.find((item: any) => item.id == form.value.deliveryAreaId);
  const parcelInfo = {
    customerName: serviceInfo.value.deliveryAddress.ContactName,
    customerPhone: serviceInfo.value.deliveryAddress.ContactPhone,
    customerAddress: fullAddress.value,
    deliveryArea: deliveryArea?.name,
    deliveryAreaId: form.value.deliveryAreaId,
    cashCollectionAmount: (serviceInfo.value?.order?.paymentMethod == 'COD')? serviceInfo.value?.order?.amount:0,
    value: serviceInfo.value.order.amount,
    parcelWeight: form.value.parcelWeight,
    merchantInvoiceId: "" + serviceInfo.value.order.invoiceNo + "",
    orderId: "" + serviceInfo.value.order.id + "",
    pickupStoreId: form.value.pickupStoreId,
    isClosedBox: form.value.isClosedBox,
  };

  loading.value = true;
  await saveParcelInfoToLocal(parcelInfo)
    .finally(()=>{
      loading.value = false;
      navigateTo(`/order-list/${route.params.id}`);
    })

  // Note: create parcel API call is now off due to waiting 
  // for once the order confirmed
  // createRedxHomeDelivery(parcelInfo);

};

const createRedxHomeDelivery = async (parcelInfo: any)=>{
  
  loading.value = true;
  orderStore
    .createRedXParcel(parcelInfo)
    .then(async (res: any) => {
      if (res && res?.data) {
        snackbar.add({
          type: res?.success ? 'success' : 'error',
          text: res?.message || "",
        })
        navigateTo(`/order-list/${route.params.id}`);
      } else {
        snackbar.add({
          type: 'error',
          text: res?.message || "Something went wrong",
        })
      }
    })
    .catch((err: any) => {
      console.log(err);
    })
    .finally(()=>{
      loading.value = false;
    })
}

watch(()=> form.value.isClosedBox, (newVal, oldVal)=>{
  if(newVal === false){
    form.value.instruction = 'কাস্টমার প্যাকেট খুলে প্রোডাক্ট চেক করে দেখতে পারবে। কাস্টমার যদি প্রোডাক্ট রিটার্ন করতে চায়, সেক্ষেত্রে ১০০ টাকা ডেলিভারি চার্জ দিতে হবে।'
  }
  else{
    form.value.instruction = '';
  }
})

</script>
