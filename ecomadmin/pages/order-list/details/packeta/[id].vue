<template>
  <div>
    <SharedBaseBreadcrumb :title="`Courier`" :breadcrumbs="items" />
    <v-form @submit.prevent="onSubmit" ref="redxForm" v-model="courierForm">
      <v-row>
        <v-col cols="12">
          <v-card class="mx-auto my-8 pa-5">
            <v-card-title> Packeta Home Delivery</v-card-title>
            <v-row dense>
              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="Phone Number *"
                    :required="true"
                    v-model="serviceInfo.pickUpAddress.ContactPhone"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="Country *"
                    :required="true"
                    v-model="serviceInfo.pickUpAddress.CountryIsoCode"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="Zip Code *"
                    :required="true"
                    v-model="serviceInfo.pickUpAddress.ZipCode"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="City *"
                    :required="true"
                    v-model="serviceInfo.pickUpAddress.City"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="Street *"
                    :required="true"
                    v-model="serviceInfo.pickUpAddress.Street"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="House No. "
                    v-model="serviceInfo.pickUpAddress.HouseNumber"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="Building, Floor, etc. *"
                    :required="true"
                    v-model="serviceInfo.pickUpAddress.HouseNumberInfo"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="Contact Person *"
                    :required="true"
                    v-model="serviceInfo.pickUpAddress.ContactName"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="Contact *"
                    :required="true"
                    v-model="serviceInfo.pickUpAddress.ContactPhone"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>
            </v-row>
            <small class="text-caption text-medium-emphasis"
              >*indicates required field</small
            >
            <v-divider></v-divider>
            <v-spacer></v-spacer>
          </v-card>
        </v-col>
      </v-row>
      <v-row>
        <v-col cols="12">
          <v-card class="mx-auto my-2 pa-5">
            <v-card-title> Delivery to the following address</v-card-title>
            <v-row dense>
              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="Phone Number *"
                    :required="true"
                    v-model="serviceInfo.deliveryAddress.ContactPhone"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="Country *"
                    :required="true"
                    v-model="serviceInfo.deliveryAddress.CountryIsoCode"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="Zip Code *"
                    :required="true"
                    v-model="serviceInfo.deliveryAddress.ZipCode"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="City *"
                    :required="true"
                    v-model="serviceInfo.deliveryAddress.City"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="Street *"
                    :required="true"
                    v-model="serviceInfo.deliveryAddress.Street"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="House No. "
                    v-model="serviceInfo.deliveryAddress.HouseNumber"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="Building, Floor, etc. *"
                    :required="true"
                    v-model="serviceInfo.deliveryAddress.HouseNumberInfo"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="Contact *"
                    :required="true"
                    v-model="serviceInfo.deliveryAddress.ContactPhone"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="Contact Parson *"
                    :required="true"
                    v-model="serviceInfo.deliveryAddress.ContactName"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>
            </v-row>
            <small class="text-caption text-medium-emphasis"
              >*indicates required field</small
            >
            <v-divider></v-divider>
            <v-spacer></v-spacer>
          </v-card>
        </v-col>
      </v-row>
      <v-row>
        <v-col cols="12">
          <v-card class="mx-auto my-2 pa-5">
            <v-row dense>
              <!--              <v-col cols="12" md="4" sm="6">-->
              <!--                <FormElementsCommonFieldContainer>-->
              <!--                  <v-text-field label="Number of parcels *" :required="true"-->
              <!--                                v-model="form.parcelWeight" :rules="rules"></v-text-field>-->
              <!--                </FormElementsCommonFieldContainer>-->
              <!--              </v-col>-->

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="COO Amount"
                    type="number"
                    v-model="amount"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>
              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="Currency "
                    v-model="serviceInfo.order.currency.currency"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>
              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="Percel weight (kg)"
                    v-model="parcelWeight"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>
            </v-row>
            <small class="text-caption text-medium-emphasis"
              >*indicates required field</small
            >
            <v-divider></v-divider>
            <v-spacer></v-spacer>
          </v-card>
        </v-col>
      </v-row>

      <v-row>
        <v-col cols="6">
          <v-card class="mx-auto my-2 pa-5">
            <v-card-title>Select Courier</v-card-title>
            <v-select v-model="selectedPacketa" :items="availablePacketaCarriers" item-title="name" item-value="id">
            </v-select>
          </v-card>
        </v-col>
      </v-row>
      <v-btn text="Cancel" variant="plain"></v-btn>
      <v-btn
        :disabled="!courierForm"
        :loading="loading"
        color="primary"
        text="Save"
        variant="tonal"
        @click="onSubmit"
      ></v-btn>
    </v-form>
  </div>
</template>
  <script setup lang="ts">
import { ref } from "vue";
import { useRolePermissionsStore } from "~/stores/administration/permissions";
import { useOrdersStore } from "~/stores/orders";
import{ DynamicModuleEnum, DynamicPermissionEnum} from "~/utils/roleManagement";

const orderStore = useOrdersStore();
const permissionStore = useRolePermissionsStore();

const route = useRoute();
const items = [
  {
    text: "Dashboard",
    disabled: false,
    to: "/dashboard",
  },
  {
    text: "Orders",
    disabled: false,
    to: "/order-list",
  },
  {
    text: route.params.id,
    disabled: true,
    to: "",
  },
];
const rules = [
  (value: any) => {
    if (value) return true;
    return "You must enter a name";
  },
];
const amount = computed({
  get() {
    const amount =
      serviceInfo.value?.order?.paymentMethod === "COD"
        ? serviceInfo.value?.order?.amount
        : 0;
    return amount;
  },
  set(newValue) {
    if (!newValue) {
      serviceInfo.value.order.amount = 0;
      return;
    }
    serviceInfo.value.order.amount = newValue;
    return newValue;
  },
});
const snackbar = useSnackbar();

const loading = ref(false);
const serviceInfo = ref({
  deliveryAddress: {
    Name: "",
    Street: "",
    HouseNumber: "",
    HouseNumberInfo: "",
    City: "",
    ZipCode: "",
    CountryIsoCode: "",
    ContactName: "",
    ContactPhone: "",
    ContactEmail: "",
  },
  order: {
    invoiceNo: "",
    amount: "",
    parcel_weight: 1,
    id: "",
    currency: "",
  },
  pickUpAddress: {
    Name: "",
    Street: "",
    HouseNumber: "",
    HouseNumberInfo: "",
    City: "",
    ZipCode: "",
    Country: "",
    CountryIsoCode: "",
    ContactName: "",
  },
});
const courierForm = ref(false);
const form = ref({
  deliveryAreaId: 0,
  pickupStoreId: 0,
  parcelWeight: "1",
  instruction: "",
  codReference: "",
  weight: "",
});
const parcelWeight = computed({
  get() {
    return form.value.weight || 0.7;
  },
  set(newValue) {
    form.value.weight = newValue;
  },
});

const availablePacketaCarriers = ref([]);
const selectedPacketa = ref();

const fetchServiceInfo = async () => {
  await orderStore.getServiceInfo(route.params.id, "mygls");
  serviceInfo.value = orderStore.serviceInfo;
  findHomeDeliveryCarrier(serviceInfo?.value?.deliveryAddress?.CountryIsoCode)
};

const saveParcelInfoToLocal = async (parcelInfo: any) => {
  try{
    let payload = {
      courierId: route?.query?.courierId,
      code: 'phd',
      parcelInfo: parcelInfo,
    }
    localStorage.setItem(`hd_parcelInfo_${route?.params?.id}`, JSON.stringify(payload));
  }catch(e){
    console.log("Parcel info saving to local issue: ", e);
  }
};

const onSubmit = async () => {
  if(!permissionStore.hasModulePermission(`${DynamicModuleEnum.COURIER}:${DynamicPermissionEnum.CREATE}`)){
    snackbar.add({
      type: "warning",
      text: "You don't have permission to create packeta home delivery courier",
    })
    return;
  }

  loading.value = true;
  const parcelInfo = {
    id: serviceInfo.value.order.id,
    number: serviceInfo.value.order.invoiceNo,
    name: serviceInfo.value.deliveryAddress.Name,
    surname: serviceInfo.value.deliveryAddress.Name,
    email: serviceInfo.value.deliveryAddress.ContactEmail,
    phone: serviceInfo.value.deliveryAddress.ContactPhone,
    addressId: Number(serviceInfo.value.deliveryAddress.addressId),
    value: serviceInfo.value.order.amount,
    cashOnDelivery:
      serviceInfo.value.order.paymentMethod === "COD"
        ? serviceInfo.value.order.amount
        : 0,
    street: serviceInfo.value.deliveryAddress.Street,
    city: serviceInfo.value.deliveryAddress.City,
    zip: serviceInfo.value.deliveryAddress.ZipCode,
    country: serviceInfo.value.deliveryAddress.CountryIsoCode,
    weight: form.value.weight,
    // serviceProvider: courier.id,
    localCourierId: selectedPacketa.value || null
  };

  loading.value = true;
  await saveParcelInfoToLocal(parcelInfo)
    .finally(()=>{
      loading.value = false;
      navigateTo(`/order-list/${route.params.id}`);
    })

  // Note: create parcel API call is now off due to waiting 
  // for once the order confirmed
  // createPacketaHomeDelivery(parcelInfo);
};

const createPacketaHomeDelivery = async (parcelInfo: any)=>{
  loading.value = true;

  orderStore
  .createPacketaHomeDelivery(parcelInfo)
  .then(async (res: any) => {
    if (res.data.success) {
      snackbar.add({
        type: "success",
        text: "Successfully created parcel",
      });
      navigateTo(`/order-list/${route.params.id}`);
    } else {
      snackbar.add({
        type: "error",
        text: res.data.message,
      });
      loading.value = false;
    }
  })
  .catch((err: any) => {
    console.log(err);
    loading.value = false;
  })
  .finally(()=>{
    loading.value = false;
  })
}

const findHomeDeliveryCarrier = (countryCode: string) =>{
    if(!permissionStore.hasModulePermission(`${DynamicModuleEnum.COURIER}:${DynamicPermissionEnum.READ_MANY}`)){
      snackbar.add({
        type: "warning",
        text: "You don't have permission to create GLS courier",
      })
      return;
    }

    let countryCodeLower = countryCode.toLowerCase()
    orderStore.getPacketaArea({countryCode: countryCodeLower}).then((response:any)=>{
      // Filter carriers for home delivery in the specified country
      const availableCarriers = response?.items?.filter((carrier:any) => {
        return (
          carrier.country == countryCodeLower &&
          carrier.apiAllowed == 'true' &&
          carrier.pickupPoints == 'false'
        );
      });
      if (availableCarriers?.length === 0) {
        throw new Error(
          `No home delivery carriers found for country: ${countryCode}`,
        );
      }
      availablePacketaCarriers.value = availableCarriers;
    }).catch((e)=>{
      return [];
    })
}

onMounted(async () => {
  await fetchServiceInfo();
});
</script>
  