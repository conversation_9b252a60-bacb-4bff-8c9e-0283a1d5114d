<template>
  <div>
    <SharedBaseBreadcrumb :title="`Courier`" :breadcrumbs="items" />

    <v-row>
        <v-col cols="12">
          <v-card class="mx-auto my-8 pa-5">
            <h3 class="mb-2">Fan Courier</h3>


      <v-form @submit.prevent="submitForm" ref="formRef" v-model="courierForm">
        <!-- FROM Section -->
        <v-row>
          <v-col cols="12">
            <h3 class="mb-2">Sender Information (From)</h3>
          </v-col>
          <v-col cols="6" v-for="field in fromFields" :key="field.model">
            <v-text-field
              v-model="form[field.model]"
              :label="`${field.label}${field.required ? ' *' : ''}`"
              :type="field.type || 'text'"
              :required="true"
            />
          </v-col>
        </v-row>
  
        <!-- TO Section -->
        <v-row>
          <v-col cols="12">
            <h3 class="mb-2">Recipient Information (To)</h3>
          </v-col>
          <v-col cols="6" v-for="field in toFields" :key="field.model">
            <v-text-field
              v-model="form[field.model]"
              :label="`${field.label}${field.required ? ' *' : ''}`"
              :type="field.type || 'text'"
              :required="true"
            />
          </v-col>
        </v-row>
  
        <!-- Shipment Details -->
        <v-row>
          <v-col cols="12">
            <h3 class="mb-2">Shipment Details</h3>
          </v-col>
  
          <v-col cols="6">
            <v-select v-model="form.type" :items="['envelope', 'package']" label="Type" />
          </v-col>
  
          <v-col cols="6">
            <v-select
              v-model="form.service_type"
              :items="serviceTypes"
              item-title="name"
              item-value="value"
              label="Service Type"
            />
          </v-col>
  
          <v-col cols="6">
            <v-text-field v-model="form.cnt" label="Number of Packages" type="number" />
          </v-col>
  
          <v-col cols="6">
            <v-select
              v-model="form.payer"
              :items="['client', 'sender', 'recipient']"
              label="Who Pays for Delivery"
              disabled
            />
          </v-col>
  
          <v-col cols="6">
            <v-text-field v-model="form.weight" label="Weight (kg)" type="number" />
          </v-col>
  
          <v-col cols="6">
            <v-text-field v-model="form.insurance" label="Insured Value" type="number" />
          </v-col>
  
          <v-col cols="6">
            <v-text-field v-model="form.ramburs" label="Cash on Delivery Amount" type="number" />
          </v-col>
  
          <v-col cols="6" v-if="form.ramburs > 0">
            <v-select
              v-model="form.ramburs_type"
              :items="['cont', 'cash', 'instrumente_plata']"
              label="CoD Payment Method"
            />
          </v-col>
  

          <v-col cols="12">
            <v-row>
              <v-col cols="3">
                <v-checkbox v-model="form.retur" label="Return / Package Swap" />
              </v-col>
      
              <v-col cols="3" v-if="form.retur">
                <v-select
                  v-model="form.retur_type"
                  :items="['colet', 'document']"
                  label="Return Type"
                />
              </v-col>
            </v-row>
          </v-col>
  
          <v-col cols="4">
            <v-text-field v-model="form.length" label="Length (cm)" type="number" />
          </v-col>
          <v-col cols="4">
            <v-text-field v-model="form.width" label="Width (cm)" type="number" />
          </v-col>
          <v-col cols="4">
            <v-text-field v-model="form.height" label="Height (cm)" type="number" />
          </v-col>
  
          <v-col cols="6">
            <v-text-field v-model="form.content" label="Package Content" />
          </v-col>
  
          <v-col cols="6">
            <v-text-field v-model="form.customer_reference" label="Customer Reference" />
          </v-col>
  
          <v-col cols="12">
            <v-textarea v-model="form.comments" label="Comments" />
          </v-col>
  
          <!-- <v-col cols="12">
            <v-checkbox v-model="form.fragile" label="Fragile?" />
          </v-col> -->

          <v-col cols="12">
            <v-alert
              v-if="errorStr"
              type="error"
              closable
              @click:close="errorStr = null"
            >
              {{ errorStr }}
            </v-alert>
          </v-col>
  
          <v-col cols="12 text-right">
            <v-btn type="submit" color="primary" :disabled="!courierForm || loading"
            :loading="loading">Submit</v-btn>
          </v-col>
        </v-row>
      </v-form>

    </v-card>
      </v-col>
    </v-row>

      <v-overlay v-model="loading" persistent class="z-50 d-flex justify-center align-center">
      <v-progress-circular
        indeterminate
        color="primary"
        size="64"
      />
    </v-overlay>

  </div>

    <v-snackbar v-model="snackbar" :color="snackbarData.type">
    {{ snackbarData.message }}
  </v-snackbar>
  </template>
  
  <script setup>
  import { reactive, ref } from 'vue'
  import { useOrdersStore } from "~/stores/orders";
  import { useRolePermissionsStore } from "~/stores/administration/permissions";

  const permissionStore = useRolePermissionsStore();
  const orderStore = useOrdersStore();
  const route = useRoute();


  const courierForm = ref(false);

  const items = [
  {
    text: "Dashboard",
    disabled: false,
    to: "/dashboard",
  },
  {
    text: "Orders",
    disabled: false,
    to: "/order-list",
  },
  {
    text: route.params.id,
    disabled: true,
    to: "",
  },
];

  const loading = ref(false);

  const errorStr = ref("")
  const snackbar = ref(false)
  const snackbarData = ref({
    message: '',
    type: 'success'
  })

  const formRef = ref()

  const serviceTypes = ref([])
  
  const form = ref({
    type: 'package',
    service_type: '',
    cnt: 1, // number of parcels/envelopes
    payer: 'client',


    // Return label
    retur: false, // 0 | 1  -  Package swap
    retur_type: '',


    // COD
    ramburs: 0,  // <CoD amount>
    ramburs_type: '', // cont | cash | instrumente_plata  -  CoD type, with values meaning account, cash or check.

    weight: '',
    length: '',
    width: '',
    height: '',

    insurance: '', // insured value

    // fragile: false,

    content: '',
    comments: '',
    customer_reference: '',




  
    // FROM fields
    from_name: '',
    from_contact: '',
    from_str: '',
    from_nr: '',
    // from_bl: '',
    // from_sc: '',
    // from_et: '',
    // from_ap: '',
    // from_interfon: '',
    // from_sector: '',
    from_city: '',
    from_county: '',
    from_zipcode: '',
    from_country: '',
    from_phone: '',
    from_email: '',
  
    // TO fields
    to_name: '',
    to_contact: '',
    to_str: '',
    to_nr: '',
    // to_bl: '',
    // to_sc: '',
    // to_et: '',
    // to_ap: '',
    // to_interfon: '',
    // to_sector: '',
    to_city: '',
    to_county: '',
    to_country: '',
    to_zipcode: '',
    to_phone: '',
    to_email: '',
    to_extra: '',
    // to_cui: '',
    // to_regcom: '',
  })
  
  const fromFields = [
    { model: 'from_name', label: 'Sender Name', required: true },
    { model: 'from_contact', label: 'Sender Contact', required: false  },
    { model: 'from_str', label: 'Street Name', required: true  },
    { model: 'from_nr', label: 'Street Number', required: false  },
    // { model: 'from_bl', label: 'Building', required: false  },
    // { model: 'from_sc', label: 'Staircase', required: false  },
    // { model: 'from_et', label: 'Floor', required: false  },
    // { model: 'from_ap', label: 'Apartment', required: false  },
    // { model: 'from_interfon', label: 'Intercom', required: false  },
    // { model: 'from_sector', label: 'Sector', required: false  },
    { model: 'from_city', label: 'City', required: true  },
    { model: 'from_county', label: 'County', required: false  },
    { model: 'from_zipcode', label: 'Zipcode', required: true  },
    { model: 'from_country', label: 'Country', required: true  },
    { model: 'from_phone', label: 'Phone', required: true  },
    { model: 'from_email', label: 'Email', required: false  },
  ]
  
  const toFields = [
    { model: 'to_name', label: 'Recipient Name', required: true  },
    { model: 'to_contact', label: 'Recipient Contact', required: false  },
    { model: 'to_str', label: 'Street Name', required: true  },
    { model: 'to_nr', label: 'Street Number', required: false  },
    // { model: 'to_bl', label: 'Building', required: false  },
    // { model: 'to_sc', label: 'Staircase', required: false  },
    // { model: 'to_et', label: 'Floor', required: false  },
    // { model: 'to_ap', label: 'Apartment', required: false  },
    // { model: 'to_interfon', label: 'Intercom', required: false  },
    // { model: 'to_sector', label: 'Sector', required: false  },
    { model: 'to_city', label: 'City', required: true  },
    { model: 'to_county', label: 'County', required: false  },
    { model: 'to_country', label: 'Country', required: true  },
    { model: 'to_zipcode', label: 'Zipcode', required: true  },
    { model: 'to_phone', label: 'Phone', required: true  },
    { model: 'to_email', label: 'Email', required: false  },
    { model: 'to_extra', label: 'Extra Info', required: false  },
    // { model: 'to_cui', label: 'CUI', required: false  },
    // { model: 'to_regcom', label: 'Reg. Com.', required: false  },
  ]
  
  const submitForm = async () => {

    try {
      loading.value = true;
      errorStr.value = ""

      const params = new URLSearchParams()
    
      for (const [key, value] of Object.entries(form.value)) {
        if (value !== '' && value !== null && value !== false) {
          params.append(key, value)
        }
        if (typeof value === 'boolean') {
          params.append(key, value ? 1 : 0)
        }
      }
      const queryString = params.toString();
      const res = await orderStore.createFanShippment(route.params.id, {payload: queryString})
      if(res.data) {
        snackbar.value = true;
        snackbarData.value.message = res.message
        snackbarData.value.type = res.data ? "success" : "error"

        navigateTo(`/order-list/${route.params.id}`);
      }

      if(!res.data) {
        errorStr.value = res.message
      }
    } finally {
      loading.value = false;
    }
  }


  const loadServices = async () => {
    const _data = await orderStore.getFanService()
    if(_data?.items) {
      serviceTypes.value = _data.items || []
      form.value = {...form.value, service_type: serviceTypes.value[0].value}
    }
  }


  const fetchServiceInfo = async () => {
    await orderStore.getServiceInfo(route.params.id, "mygls");
    const _dbData = orderStore.serviceInfo;
    const _pickUpAddress = _dbData.pickUpAddress
    const formData = {
      from_name: _pickUpAddress.Name,
      from_contact: _pickUpAddress.ContactName,
      from_str: _pickUpAddress.Street,
      from_nr: _pickUpAddress.HouseNumber,
      from_city: _pickUpAddress.City,
      from_zipcode: _pickUpAddress.ZipCode,
      from_country: _pickUpAddress.CountryIsoCode,
      from_county: _pickUpAddress?.County,
      from_phone: _pickUpAddress.ContactPhone,
      from_email: _pickUpAddress.ContactEmail,
    }

    const _deliveryAddress = _dbData.deliveryAddress
    const toData = {
      to_name: _deliveryAddress.Name,
      to_contact: _deliveryAddress.ContactName,
      to_str: _deliveryAddress.Street,
      to_nr: _deliveryAddress.HouseNumber,
      to_city: _deliveryAddress.City,
      to_zipcode: _deliveryAddress.ZipCode,
      to_country: _deliveryAddress.CountryIsoCode,
      to_county: _dbData?.order?.billingAddress?.state ?? '',
      to_phone: _deliveryAddress.ContactPhone,
      to_email: _deliveryAddress.ContactEmail,
      to_extra: '',
    }


    const _order = _dbData.order

    const codData = {
      ramburs: 0,  // <CoD amount>
      ramburs_type: '', // cont | cash
    }

    if(_order?.paymentMethod == "COD") {
      codData.ramburs = parseFloat(_order?.amount),
      codData.ramburs_type = ''
    }

    form.value = {...form.value, customer_reference: _order.invoiceNo, weight: _order.parcel_weight || 1,  ...formData, ...toData, ...codData}
  }

  onMounted(async () => {
    await loadServices();
    await fetchServiceInfo();
  });


  </script>
  