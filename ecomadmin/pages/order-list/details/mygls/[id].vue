<template>
  <div>
    <SharedBaseBreadcrumb :title="`Courier`" :breadcrumbs="items" />
    <v-form @submit.prevent="onSubmit" ref="redxForm" v-model="courierForm">
      <v-row>
        <v-col cols="12">
          <v-card class="mx-auto my-8 pa-5">
            <v-card-title> MyGLS Courier</v-card-title>
            <v-row dense>
              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="Phone Number *"
                    :required="true"
                    v-model="serviceInfo.pickUpAddress.ContactPhone"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="Country *"
                    :required="true"
                    v-model="serviceInfo.pickUpAddress.CountryIsoCode"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="Zip Code *"
                    :required="true"
                    v-model="serviceInfo.pickUpAddress.ZipCode"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="City *"
                    :required="true"
                    v-model="serviceInfo.pickUpAddress.City"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="Street *"
                    :required="true"
                    v-model="serviceInfo.pickUpAddress.Street"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="House No. "
                    v-model="serviceInfo.pickUpAddress.HouseNumber"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="Building, Floor, etc. *"
                    :required="true"
                    v-model="serviceInfo.pickUpAddress.HouseNumberInfo"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="Contact Person *"
                    :required="true"
                    v-model="serviceInfo.pickUpAddress.ContactName"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="Contact *"
                    :required="true"
                    v-model="serviceInfo.pickUpAddress.ContactPhone"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>
            </v-row>
            <small class="text-caption text-medium-emphasis"
              >*indicates required field</small
            >
            <v-divider></v-divider>
            <v-spacer></v-spacer>
          </v-card>
        </v-col>
      </v-row>
      <v-row>
        <v-col cols="12">
          <v-card class="mx-auto my-2 pa-5">
            <v-card-title> Delivery to the following address</v-card-title>
            <v-row dense>
              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="Phone Number *"
                    :required="true"
                    v-model="serviceInfo.deliveryAddress.ContactPhone"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="Country *"
                    :required="true"
                    v-model="serviceInfo.deliveryAddress.CountryIsoCode"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="Zip Code *"
                    :required="true"
                    v-model="serviceInfo.deliveryAddress.ZipCode"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="City *"
                    :required="true"
                    v-model="serviceInfo.deliveryAddress.City"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="Street *"
                    :required="true"
                    v-model="serviceInfo.deliveryAddress.Street"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="House No. "
                    v-model="serviceInfo.deliveryAddress.HouseNumber"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="Building, Floor, etc. *"
                    :required="true"
                    v-model="serviceInfo.deliveryAddress.HouseNumberInfo"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="Contact *"
                    :required="true"
                    v-model="serviceInfo.deliveryAddress.ContactPhone"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="Contact Parson *"
                    :required="true"
                    v-model="serviceInfo.deliveryAddress.ContactName"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>
            </v-row>
            <small class="text-caption text-medium-emphasis"
              >*indicates required field</small
            >
            <v-divider></v-divider>
            <v-spacer></v-spacer>
          </v-card>
        </v-col>
      </v-row>
      <v-row>
        <v-col cols="12">
          <v-card class="mx-auto my-2 pa-5">
            <v-row dense>
              <!--              <v-col cols="12" md="4" sm="6">-->
              <!--                <FormElementsCommonFieldContainer>-->
              <!--                  <v-text-field label="Number of parcels *" :required="true"-->
              <!--                                v-model="form.parcelWeight" :rules="rules"></v-text-field>-->
              <!--                </FormElementsCommonFieldContainer>-->
              <!--              </v-col>-->

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="COO Account"
                    type="number"
                    v-model="amount"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>
              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="Currency "
                    v-model="serviceInfo.order.currency.currency"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>
              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="Cod Reference "
                    v-model="form.codReference"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>
            </v-row>
            <small class="text-caption text-medium-emphasis"
              >*indicates required field</small
            >
            <v-divider></v-divider>
            <v-spacer></v-spacer>
          </v-card>
        </v-col>
      </v-row>
      <v-btn text="Cancel" variant="plain"></v-btn>
      <v-btn
        :disabled="!courierForm || loading"
        :loading="loading"
        color="primary"
        text="Save"
        variant="tonal"
        @click="onSubmit"
      ></v-btn>
    </v-form>
    <v-overlay v-model="loading" persistent class="z-50 d-flex justify-center align-center">
      <v-progress-circular
        indeterminate
        color="primary"
        size="64"
      />
    </v-overlay>
  </div>
</template>
<script setup lang="ts">
import { ref } from "vue";
import { useRolePermissionsStore } from "~/stores/administration/permissions";
import { useOrdersStore } from "~/stores/orders";
import { glsLabelPrint } from "~/utils/helpers/functions";
import {DynamicModuleEnum, DynamicPermissionEnum} from "~/utils/roleManagement"

const permissionStore = useRolePermissionsStore();
const orderStore = useOrdersStore();
const snackbar = useSnackbar();

const route = useRoute();
const items = [
  {
    text: "Dashboard",
    disabled: false,
    to: "/dashboard",
  },
  {
    text: "Orders",
    disabled: false,
    to: "/order-list",
  },
  {
    text: route.params.id,
    disabled: true,
    to: "",
  },
];
const rules = [
  (value: any) => {
    if (value) return true;
    return "You must enter a name";
  },
];
const amount = computed({
  get() {
    const amount =
      serviceInfo.value?.order?.paymentMethod === "COD"
        ? serviceInfo.value?.order?.amount
        : 0;
    return amount;
  },
  set(newValue) {
    if (!newValue) {
      serviceInfo.value.order.amount = 0;
      return;
    }
    serviceInfo.value.order.amount = newValue;
    return newValue;
  },
});

const loading = ref(false);
const serviceInfo = ref({
  deliveryAddress: {
    Name: "",
    Street: "",
    HouseNumber: "",
    HouseNumberInfo: "",
    City: "",
    ZipCode: "",
    CountryIsoCode: "",
    ContactName: "",
    ContactPhone: "",
    ContactEmail: "",
  },
  order: {
    invoiceNo: "",
    amount: "",
    parcel_weight: 1,
    id: "",
    currency: "",
  },
  pickUpAddress: {
    Name: "",
    Street: "",
    HouseNumber: "",
    HouseNumberInfo: "",
    City: "",
    ZipCode: "",
    Country: "",
    CountryIsoCode: "",
    ContactName: "",
  },
});
const courierForm = ref(false);
const form = ref({
  deliveryAreaId: 0,
  pickupStoreId: 0,
  parcelWeight: "1",
  instruction: "",
  codReference: "",
});
const fetchServiceInfo = async () => {
  await orderStore.getServiceInfo(route.params.id, "mygls");
  serviceInfo.value = orderStore.serviceInfo;
};

const saveParcelInfoToLocal = async (parcelInfo: any) => {
  try{
    let payload = {
      courierId: route?.query?.courierId,
      code: 'ghd',
      parcelInfo: parcelInfo,
    }
    localStorage.setItem(`hd_parcelInfo_${route?.params?.id}`, JSON.stringify(payload));
  }catch(e){
    console.log("Parcel info saving to local issue: ", e);
  }
};

const onSubmit = async () => {
  if(!permissionStore.hasModulePermission(`${DynamicModuleEnum.COURIER}:${DynamicPermissionEnum.CREATE}`)){
    snackbar.add({
      type: "warning",
      text: "You don't have permission to create GLS courier",
    })
    return;
  }

  const parcelInfo = {
    orderId: serviceInfo.value.order.id,
    deliveryAddress: serviceInfo.value.deliveryAddress,
    pickUpAddress: serviceInfo.value.pickUpAddress,
    serviceProvider: "mygls",
    codAmount:
      serviceInfo.value?.order?.paymentMethod == "COD"
        ? parseFloat(serviceInfo.value?.order?.amount)
        : 0,
    codCurrency: serviceInfo.value?.order?.currency?.currency,
    codReference: form.value.codReference,
  };
  
  loading.value = true;
  await saveParcelInfoToLocal(parcelInfo)
    .finally(()=>{
      loading.value = false;
      navigateTo(`/order-list/${route.params.id}`);
    })

  // Note: create parcel API call is now off due to waiting 
  // for once the order confirmed
  // createParcel(parcelInfo);
};

const createParcel = async (parcelInfo: any) =>{

  loading.value = true;
  orderStore
  .createGSLParcel(parcelInfo)
  .then(async (res: any) => {

    if (res && res?.data?.parcelNumbers?.length) {
      snackbar.add({
        type: res.success ? "success" : "error",
        text: res.message || "",
      })

      glsLabelPrint(serviceInfo.value.order.id, res.data.label)

      try {
        let returnParcelInfo = {
          orderId: parcelInfo?.orderId,
          deliveryAddress: parcelInfo?.deliveryAddress,
          pickUpAddress: parcelInfo?.pickUpAddress,
          serviceProvider: parcelInfo?.serviceProvider
        }
        await createGSLParcelReturnLabel(returnParcelInfo)
      } catch (e) {
        console.log(e)
      }
    } else {
      snackbar.add({
        type: "error",
        text: res?.message?.data?.ErrorDescription || "Something went wrong! Please check your information",
      })
    }
  })
  .catch((err: any) => {
    console.log(err);
  })
  .finally(()=>{
    loading.value = false;
  })
}

const createGSLParcelReturnLabel = (parcelInfo: any) => {
  return orderStore
    .createGSLParcelReturnLabel(parcelInfo)
    .then(async (res: any) => {
      if (res && res?.data?.parcelNumbers?.length) {
        glsLabelPrint(serviceInfo.value.order.id, res.data.label, 'return_label')
      }
      loading.value = false;
    })
    .catch((err: any) => {
      loading.value = false;
      console.log(err);
    })
    .finally(()=>{
      loading.value = false;
      navigateTo(`/order-list/${route.params.id}`);
    })
}

onMounted(async () => {
  await fetchServiceInfo();
});
</script>
