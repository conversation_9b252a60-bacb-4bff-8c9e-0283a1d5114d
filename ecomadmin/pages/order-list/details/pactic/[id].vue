<template>
  <div>
    <SharedBaseBreadcrumb :title="`Courier`" :breadcrumbs="items" />
    <v-form @submit.prevent="onSubmit" ref="redxForm" v-model="courierForm">
      <v-row>
        <v-col cols="12">
          <v-card class="mx-auto my-8 pa-5">
            <v-card-title> Pactic Home Delivery</v-card-title>
            <v-row dense>
              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="Phone Number *"
                    :required="true"
                    v-model="serviceInfo.pickUpAddress.ContactPhone"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="Country *"
                    :required="true"
                    v-model="serviceInfo.pickUpAddress.CountryIsoCode"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="Zip Code *"
                    :required="true"
                    v-model="serviceInfo.pickUpAddress.ZipCode"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="City *"
                    :required="true"
                    v-model="serviceInfo.pickUpAddress.City"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="Street *"
                    :required="true"
                    v-model="serviceInfo.pickUpAddress.Street"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="House No. "
                    v-model="serviceInfo.pickUpAddress.HouseNumber"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="Building, Floor, etc. *"
                    :required="true"
                    v-model="serviceInfo.pickUpAddress.HouseNumberInfo"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="Contact Person *"
                    :required="true"
                    v-model="serviceInfo.pickUpAddress.ContactName"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="Contact *"
                    :required="true"
                    v-model="serviceInfo.pickUpAddress.ContactPhone"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>
            </v-row>
            <small class="text-caption text-medium-emphasis"
              >*indicates required field</small
            >
            <v-divider></v-divider>
            <v-spacer></v-spacer>
          </v-card>
        </v-col>
      </v-row>
      <v-row>
        <v-col cols="12">
          <v-card class="mx-auto my-2 pa-5">
            <v-card-title> Delivery to the following address</v-card-title>
            <v-row dense>
              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="Phone Number *"
                    :required="true"
                    v-model="serviceInfo.deliveryAddress.ContactPhone"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="Country *"
                    :required="true"
                    v-model="serviceInfo.deliveryAddress.CountryIsoCode"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="Zip Code *"
                    :required="true"
                    v-model="serviceInfo.deliveryAddress.ZipCode"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="City *"
                    :required="true"
                    v-model="serviceInfo.deliveryAddress.City"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="Street *"
                    :required="true"
                    v-model="serviceInfo.deliveryAddress.Street"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="House No. "
                    v-model="serviceInfo.deliveryAddress.HouseNumber"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="Building, Floor, etc. *"
                    :required="true"
                    v-model="serviceInfo.deliveryAddress.HouseNumberInfo"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="Contact *"
                    :required="true"
                    v-model="serviceInfo.deliveryAddress.ContactPhone"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="Contact Parson *"
                    :required="true"
                    v-model="serviceInfo.deliveryAddress.ContactName"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>
            </v-row>
            <small class="text-caption text-medium-emphasis"
              >*indicates required field</small
            >
            <v-divider></v-divider>
            <v-spacer></v-spacer>
          </v-card>
        </v-col>
      </v-row>
      <v-row>
        <v-col cols="12">
          <v-card class="mx-auto my-2 pa-5">
            <v-row dense>
              <!--              <v-col cols="12" md="4" sm="6">-->
              <!--                <FormElementsCommonFieldContainer>-->
              <!--                  <v-text-field label="Number of parcels *" :required="true"-->
              <!--                                v-model="form.parcelWeight" :rules="rules"></v-text-field>-->
              <!--                </FormElementsCommonFieldContainer>-->
              <!--              </v-col>-->

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="COO Amount"
                    type="number"
                    v-model="amount"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>
              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="Currency "
                    v-model="serviceInfo.order.currency.currency"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>
              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="Percel weight (kg)"
                    v-model="parcelWeight"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>
            </v-row>
            <small class="text-caption text-medium-emphasis"
              >*indicates required field</small
            >
            <v-divider></v-divider>
            <v-spacer></v-spacer>
          </v-card>
        </v-col>
      </v-row>

      <v-row>
        <v-col cols="6">
          <v-card class="mx-auto my-2 pa-5">
            <v-card-title>
              <div class="d-flex justify-space-between align-center">
                <span>Select Courier</span>
                <v-btn
                  @click="findHomeDeliveryCarrier"
                  :loading="loading"
                  color="primary"
                  variant="flat"
                  density="compact"
                >Reload</v-btn>
              </div>
            </v-card-title>
            <v-select v-model="selectedPactic" :items="availablePacticCarriers" :item-title="formatCarrierLabel" item-value="Service">
            </v-select>
          </v-card>
        </v-col>
      </v-row>
      <v-btn text="Cancel" variant="plain"></v-btn>
      <v-btn
        :disabled="!courierForm"
        :loading="loading"
        color="primary"
        text="Save"
        variant="tonal"
        @click="onSubmit"
      ></v-btn>
    </v-form>
    <v-overlay v-model="loading" persistent class="z-50 d-flex justify-center align-center">
      <v-progress-circular
        indeterminate
        color="primary"
        size="64"
      />
    </v-overlay>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useRolePermissionsStore } from "~/stores/administration/permissions";
import { useOrdersStore } from "~/stores/orders";
import{ DynamicModuleEnum, DynamicPermissionEnum} from "~/utils/roleManagement";
import { pacticLabelPrint } from "~/utils/helpers/functions";
import { LanguagesOptionArr } from "~/utils/languages";

const route = useRoute();
const orderStore = useOrdersStore();
const permissionStore = useRolePermissionsStore();
const snackbar = useSnackbar();

const loading = ref(false);
const availablePacticCarriers = ref([]);
const selectedPactic = ref();

const languagesOption = [...LanguagesOptionArr];

const items = [
  {
    text: "Dashboard",
    disabled: false,
    to: "/dashboard",
  },
  {
    text: "Orders",
    disabled: false,
    to: "/order-list",
  },
  {
    text: route.params.id,
    disabled: true,
    to: "",
  },
];
const rules = [
  (value: any) => {
    if (value) return true;
    return "You must enter a name";
  },
];

const serviceInfo = ref({
  deliveryAddress: {
    Name: "",
    Street: "",
    HouseNumber: "",
    HouseNumberInfo: "",
    City: "",
    ZipCode: "",
    CountryIsoCode: "",
    ContactName: "",
    ContactPhone: "",
    ContactEmail: "",
  },
  order: {
    invoiceNo: "",
    amount: "",
    parcel_weight: 1,
    id: "",
    currency: "",
  },
  pickUpAddress: {
    Name: "",
    Street: "",
    HouseNumber: "",
    HouseNumberInfo: "",
    City: "",
    ZipCode: "",
    Country: "",
    CountryIsoCode: "",
    ContactName: "",
  },
});
const courierForm = ref(false);
const form = ref({
  deliveryAreaId: 0,
  pickupStoreId: 0,
  parcelWeight: "1",
  instruction: "",
  codReference: "",
  weight: "",
});
const parcelWeight = computed({
  get() {
    return form.value.weight || 1;
  },
  set(newValue) {
    form.value.weight = newValue;
  },
});

const amount = computed({
  get() {
    const amount =
      serviceInfo.value?.order?.paymentMethod === "COD"
        ? serviceInfo.value?.order?.amount
        : 0;
    return amount;
  },
  set(newValue) {
    if (!newValue) {
      serviceInfo.value.order.amount = 0;
      return;
    }
    serviceInfo.value.order.amount = newValue;
    return newValue;
  },
});

const formatCarrierLabel = (item: any)=>{
  if (!item || !item.Service) return '';
    const { nmCarrier, nmService, amNet } = item.Service;
    const roundedAmNet = amNet.toFixed(2); 

    return `${nmCarrier} - ${nmService} (${roundedAmNet})`;
};

const fetchServiceInfo = async () => {
  await orderStore.getServiceInfo(route.params.id, "mygls");
  serviceInfo.value = orderStore.serviceInfo;
  findHomeDeliveryCarrier()
};

const saveParcelInfoToLocal = async (parcelInfo: any) => {
  try{
    let payload = {
      courierId: route?.query?.courierId,
      code: 'pactichd',
      parcelInfo: parcelInfo,
    }
    localStorage.setItem(`hd_parcelInfo_${route?.params?.id}`, JSON.stringify(payload));
  }catch(e){
    console.log("Parcel info saving to local issue: ", e);
  }
};

const onSubmit = async () => {
  if(!permissionStore.hasModulePermission(`${DynamicModuleEnum.COURIER}:${DynamicPermissionEnum.CREATE}`)){
    snackbar.add({
      type: "warning",
      text: "You don't have permission to create pactic home delivery courier",
    })
    return;
  }

  // if(!availablePacticCarriers.value || availablePacticCarriers?.value?.length === 0){
  //   snackbar.add({
  //     type: "warning",
  //     text: "No available pactic carriers!",
  //   })
  //   return;
  // }

  const bookParcelInfo = {
    ...serviceInfo.value,
    BOOK: {
      dtPickup: "",
      idCarrier: "",
      idService: "",
      flBookCheapest: true
    }
  }
  if(selectedPactic.value){
    let addressObj = availablePacticCarriers?.value?.find(
      (item: any)=> item?.Service?.idService === selectedPactic?.value?.idService && item?.Service?.idCarrier === selectedPactic?.value?.idCarrier);

    if(addressObj){
      let firstDate = addressObj?.Service?.lsPossiblePickupDates?.length > 0 ? addressObj?.Service?.lsPossiblePickupDates[0] : null;
      if(firstDate){
        bookParcelInfo.BOOK = {
          dtPickup: firstDate,
          idCarrier: Number(addressObj?.Service?.idCarrier),
          idService: Number(addressObj?.Service?.idService),
          flBookCheapest: false
        }
      }
    }
  }

  loading.value = true;
  await saveParcelInfoToLocal(bookParcelInfo)
    .finally(()=>{
      loading.value = false;
      navigateTo(`/order-list/${route.params.id}`);
    })

  // Note: create parcel API call is now off due to waiting
  // for once the order confirmed
  // handleCreatePacticParcel(bookParcelInfo);
};

const handleCreatePacticParcel = async (parcelInfo: any)=>{
  loading.value = true;
  orderStore
    .createPacticParcel(parcelInfo)
    .then(async (res: any) => {
      if (res?.data?.success) {
        if(res?.data?.data?.waybills){
          snackbar.add({
            type: "success",
            text: "Successfully created parcel",
          });

          let langCode = languagesOption?.find((item)=> item?.id === serviceInfo?.value?.order?.country?.languageId)
          let labelPayload = {
            trackingNumber: res?.data?.data?.waybills,
            countryCode: serviceInfo?.value?.order?.country?.code,
            lang: langCode?.code
          }
          orderStore
            .createPacticParcelLabel(labelPayload)
            .then((labelResponse)=>{
              pacticLabelPrint(serviceInfo?.value?.order?.id, labelResponse?.data?.Data)
            })
            .finally(()=>{
              setTimeout(() => {
                navigateTo(`/order-list/${route.params.id}`);
              }, 1000);
              loading.value = false;
            })
        }else{
          snackbar.add({
            type: "error",
            text: "Pactic waybills not created for this order!",
          }); 
        }
      } else {
        snackbar.add({
          type: "error",
          text: res?.data?.message,
        });
        loading.value = false;
      }
      loading.value = false;
    })
    .catch((err: any) => {
      console.log(err);
      loading.value = false;
    })
    .finally(()=>{
      loading.value = false;
    })
}

const findHomeDeliveryCarrier = () =>{
  if(!permissionStore.hasModulePermission(`${DynamicModuleEnum.COURIER}:${DynamicPermissionEnum.READ_MANY}`)){
    snackbar.add({
      type: "warning",
      text: "You don't have permission to create GLS courier",
    })
    return;
  }

  loading.value = true;
  orderStore
    .getPacticParcelServiceList(serviceInfo?.value)
    .then((response:any)=>{
        // Filter carriers for home delivery in the specified country
        if(!response?.data?.success){
          snackbar.add({
            type: "error",
            text: response?.data?.message || "No home delivery carriers found",
          });
        }
        else{
          const availableCarriers = response?.data?.data;
          snackbar.add({
            type: "success",
            text: response?.data?.message || "Pactic carriers available",
          });
          availablePacticCarriers.value = availableCarriers;
        }
      }).catch((e)=>{
        return [];
      }).finally(()=>{
        loading.value = false;
      })
}

onMounted(async () => {
  await fetchServiceInfo();
});
</script>
  