<template>
  <div>
    <SharedBaseBreadcrumb :title="`Courier`" :breadcrumbs="items" />
    <v-form @submit.prevent="onSubmit" ref="courierform" v-model="courierForm">
      <v-row>
        <v-col cols="12">
          <v-card class="mx-auto my-8 pa-5">
            <v-card-title> {{ courier.name }}</v-card-title>
            <v-row dense>
              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="Phone Number *"
                    :required="true"
                    v-model="serviceInfo.deliveryAddress.ContactPhone"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="Country *"
                    :required="true"
                    v-model="serviceInfo.pickUpAddress.CountryIsoCode"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="Zip Code *"
                    :required="true"
                    v-model="serviceInfo.pickUpAddress.ZipCode"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="City *"
                    :required="true"
                    v-model="serviceInfo.pickUpAddress.City"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="Street *"
                    :required="true"
                    v-model="serviceInfo.pickUpAddress.Street"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="House No. *"
                    :required="true"
                    v-model="serviceInfo.pickUpAddress.HouseNumber"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="Building, Floor, etc. *"
                    :required="true"
                    v-model="serviceInfo.pickUpAddress.HouseNumberInfo"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="Contact *"
                    :required="true"
                    v-model="serviceInfo.pickUpAddress.ContactPhone"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="Contact Parson *"
                    :required="true"
                    v-model="serviceInfo.pickUpAddress.ContactName"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>
            </v-row>
            <small class="text-caption text-medium-emphasis"
              >*indicates required field</small
            >
            <v-divider></v-divider>
            <v-spacer></v-spacer>
          </v-card>
        </v-col>
      </v-row>
      <v-row>
        <v-col cols="12">
          <v-card class="mx-auto my-2 pa-5">
            <v-card-title> Delivery to the following address</v-card-title>
            <v-row dense>
              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="Phone Number *"
                    :required="true"
                    v-model="serviceInfo.deliveryAddress.ContactPhone"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="Country *"
                    :required="true"
                    v-model="serviceInfo.deliveryAddress.CountryIsoCode"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="Zip Code *"
                    :required="true"
                    v-model="serviceInfo.deliveryAddress.ZipCode"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="City *"
                    :required="true"
                    v-model="serviceInfo.deliveryAddress.City"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="Street *"
                    :required="true"
                    v-model="serviceInfo.deliveryAddress.Street"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="House No. *"
                    :required="true"
                    v-model="serviceInfo.deliveryAddress.HouseNumber"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="Building, Floor, etc. *"
                    :required="true"
                    v-model="serviceInfo.deliveryAddress.HouseNumberInfo"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="Contact *"
                    :required="true"
                    v-model="serviceInfo.deliveryAddress.ContactPhone"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>

              <v-col cols="12" md="4" sm="6">
                <FormElementsCommonFieldContainer>
                  <v-text-field
                    label="Contact Parson *"
                    :required="true"
                    v-model="serviceInfo.deliveryAddress.ContactName"
                    :rules="rules"
                  ></v-text-field>
                </FormElementsCommonFieldContainer>
              </v-col>
            </v-row>
            <small class="text-caption text-medium-emphasis"
              >*indicates required field</small
            >
            <v-divider></v-divider>
            <v-spacer></v-spacer>
          </v-card>
        </v-col>
      </v-row>
      <v-btn text="Cancel" variant="plain"></v-btn>
      <!-- :disabled="!courierForm" -->
      <v-btn
        :disabled="!courierForm"
        :loading="loading"
        color="primary"
        text="Save"
        variant="tonal"
        @click="onSubmit"
      ></v-btn>
    </v-form>
    <v-snackbar v-model="snackbar" :color="snackbarData.type">
      {{ snackbarData.message }}
    </v-snackbar>
  </div>
</template>
<script setup lang="ts">
import { ref } from "vue";
import { useOrdersStore } from "~/stores/orders";
import { useCourierStore } from "~/stores/others/courier";

const orderStore = useOrdersStore();
const courier = ref({});
const config = useRuntimeConfig();

const route = useRoute();
const items = [
  {
    text: "Dashboard",
    disabled: false,
    to: "/dashboard",
  },
  {
    text: "Orders",
    disabled: false,
    to: "/order-list",
  },
  {
    text: route.params.id,
    disabled: true,
    to: "",
  },
];
const rules = [
  (value: any) => {
    if (value) return true;
    return "You must enter a name";
  },
];
const snackbar = ref(false);
const snackbarData = ref({
  message: "",
  type: "success",
});
const loading = ref(false);
const serviceInfo = ref({
  deliveryAddress: {
    Name: "",
    Street: "",
    HouseNumber: "",
    HouseNumberInfo: "",
    City: "",
    ZipCode: "",
    CountryIsoCode: "",
    ContactName: "",
    ContactPhone: "",
    ContactEmail: "",
  },
  order: {
    invoiceNo: "",
    amount: "",
    parcel_weight: 1,
    id: "",
  },
  pickUpAddress: {
    Name: "",
    Street: "",
    HouseNumber: "",
    HouseNumberInfo: "",
    City: "",
    ZipCode: "",
    Country: "",
    CountryIsoCode: "",
    ContactName: "",
  },
});
const courierForm = ref(false);
const form = ref({
  deliveryAreaId: 0,
  pickupStoreId: 0,
  parcelWeight: "1",
  instruction: "",
});
const fetchServiceInfo = async () => {
  await orderStore.getServiceInfo(route.params.id, "mygls");
  serviceInfo.value = orderStore.serviceInfo;
};
const fetchCourierInfo = async () => {
  try {
    await fetch(`${config.public?.apiUrl}courierServices/${route.params.id}`)
      .then((res) => res.json())
      .then((data) => {
        courier.value = data.data;
      });
  } catch (error) {
    console.log("🚀 ~ fetchCourierInfo ~ error:", error);
  }
};

const onSubmit = () => {
  loading.value = true;
  console.log("🚀 ~ onSubmit ~ courierForm:", serviceInfo.value);
  loading.value = false;
};

onMounted(async () => {
  await fetchServiceInfo();
  await fetchCourierInfo();
});
</script>
