<template>
  <SharedBaseBreadcrumb
      :title="page.title"
      :breadcrumbs="breadcrumbs"
  ></SharedBaseBreadcrumb>
  <v-row>
    <CountComponent title="Total Customer" counter="10"/>
    <CountComponent title="Total Category" counter="10"/>
    <CountComponent title="Total Brand" counter="10"/>
    <CountComponent title="Total Products" counter="10"/>
    <CountComponent title="Total Orders" counter="10"/>
    <CountComponent title="Total Sale" counter="10"/>
  </v-row>
  <v-row>
    <v-col cols="6">
      <h3>Best Selling Category</h3>
        <v-table density="compact">
          <thead>
          <tr>
            <th class="text-left">
              Name
            </th>
            <th class="text-left">
              Category
            </th>
            <th class="text-left">
              Number OF Product
            </th>
            <th class="text-left">
              Total Sale
            </th>
          </tr>
          </thead>
          <tbody>
          <tr>
            <td>Product One</td>
            <td>Men</td>
            <td>30</td>
            <td>30000</td>
          </tr>
          <tr>
            <td>Product One</td>
            <td>Men</td>
            <td>30</td>
            <td>30000</td>
          </tr>
          <tr>
            <td>Product One</td>
            <td>Men</td>
            <td>30</td>
            <td>30000</td>
          </tr>
          <tr>
            <td>Product One</td>
            <td>Men</td>
            <td>30</td>
            <td>30000</td>
          </tr>
          <tr>
            <td>Product One</td>
            <td>Men</td>
            <td>30</td>
            <td>30000</td>
          </tr>

          </tbody>
        </v-table>
    </v-col>
    <v-col cols="6">
      <h3>Top Buying Customers</h3>
      <v-table density="compact">
        <thead>
        <tr>
          <th class="text-left">
            Name
          </th>
          <th class="text-left">
            Category
          </th>
          <th class="text-left">
            Number OF Product
          </th>
          <th class="text-left">
            Total Sale
          </th>
        </tr>
        </thead>
        <tbody>
        <tr>
          <td>Product One</td>
          <td>Men</td>
          <td>30</td>
          <td>30000</td>
        </tr>
        <tr>
          <td>Product One</td>
          <td>Men</td>
          <td>30</td>
          <td>30000</td>
        </tr>
        <tr>
          <td>Product One</td>
          <td>Men</td>
          <td>30</td>
          <td>30000</td>
        </tr>
        <tr>
          <td>Product One</td>
          <td>Men</td>
          <td>30</td>
          <td>30000</td>
        </tr>
        <tr>
          <td>Product One</td>
          <td>Men</td>
          <td>30</td>
          <td>30000</td>
        </tr>

        </tbody>
      </v-table>
    </v-col>
  </v-row>
</template>
<script setup lang="ts">
import CountComponent from "~/components/dashboard/CountComponent.vue";
import {ref} from "vue";
const page = ref({title: "Welcome to Dashboard"});

const breadcrumbs = ref([
  {
    text: "Dashboard",
    disabled: true,
    to: "",
  },
]);
</script>
