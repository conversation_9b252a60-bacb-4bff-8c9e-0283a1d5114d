<template>
  <v-sheet color="#081226" class="pa-5 mb-2" rounded="md">
    <SharedBaseBreadcrumb
      :title="page.title"
      :breadcrumbs="breadcrumbs"
      class="text-white"
    ></SharedBaseBreadcrumb>
    <v-row>
      <CountComponent
        title="Total Category"
        :counter="card_data.total_categories"
      />
      <CountComponent title="Total Brand" :counter="card_data.total_brands" />
      <CountComponent title="Total Color" :counter="card_data.total_color" />
      <CountComponent title="Total Size" :counter="card_data.total_size" />
      <CountComponent
        title="Total Product"
        :counter="card_data.total_products"
      />
      <CountComponent title="Total Sale" :counter="card_data.total_order" />
    </v-row>
  </v-sheet>
  <v-row>
    <v-col cols="12" sm="6">
      <v-card>
        <v-card-text>
          <div class="d-flex justify-space-between align-center">
            <h3>Top 5 Best Selling Products</h3>
            <v-btn
              color="primary"
              to="/reports/best-selling-products-reports"
              flat
              >View Details
            </v-btn>
          </div>
          <v-table density="compact">
            <thead>
              <tr>
                <th class="text-left ps-0">Product Name</th>
                <th class="text-left">Color</th>
                <th class="text-left">Size</th>
                <th class="text-left">Quantity</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="item in best_selling_products" :key="item.id">
                <td class="ps-0">{{ item.product_name }}</td>
                <td>{{ item.color ? item.color.name : "-" }}</td>
                <td>{{ item.size ? item.size.name : "-" }}</td>
                <td>{{ item.total_quantity ? item.total_quantity : "-" }}</td>
              </tr>
            </tbody>
          </v-table>
        </v-card-text>
      </v-card>
    </v-col>
    <v-col cols="12" sm="6">
      <v-card>
        <v-card-text>
          <div class="d-flex justify-space-between align-center">
            <h3>Last 5 Sales</h3>
            <v-btn
              color="primary"
              to="/reports/sales-reports"
              flat
              >View Details
            </v-btn>
          </div>
          <v-table density="compact">
            <thead>
              <tr>
                <th class="text-left ps-0">Order No</th>
                <th class="text-left">Order Date</th>
                <th class="text-left">SKU</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="item in sales_reports" :key="item.id">
                <td class="ps-0">{{ item.invoice_no }}</td>
                <td>{{ formatDate(item.created_at) }}</td>
                <td>{{ item.sku }}</td>
              </tr>
            </tbody>
          </v-table>
        </v-card-text>
      </v-card>
    </v-col>
    <!-- <v-col cols="4" sm="3">
      <apexchart width="300" type="donut" :options="options" :series="series"></apexchart>
    </v-col> -->
    <v-col cols="12" sm="6">
      <v-card>
        <v-card-text>
          <div class="d-flex justify-space-between align-center">
            <h3>Financial Report</h3>
            <v-btn
              color="primary"
              to="/reports/financial-reports"
              flat
              >View Details
            </v-btn>
          </div>
          <v-table density="compact">
            <thead>
              <tr>
                <th class="text-left ps-0">Date</th>
                <th class="text-left">Order No</th>
                <th class="text-left">Amount</th>
                <th class="text-left">Payment Method</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="item in financial_reports" :key="item.id">
                <td class="ps-0">{{ formatDate(item.createdAt) }}</td>
                <td>{{ item.invoiceNo }}</td>
                <td>{{ item.amount }}</td>
                <td>{{ item.paymentMethod }}</td>
              </tr>
            </tbody>
          </v-table>
        </v-card-text>
      </v-card>
    </v-col>
    <v-col cols="12" sm="6">
      <v-card>
        <v-card-text>
          <div class="d-flex justify-space-between align-center">
            <h3>Last 5 Product Return</h3>
            <v-btn
              color="primary"
              to="/reports/product-return-rates-and-reasons-reports"
              flat
              >View Details
            </v-btn>
          </div>
          <v-table density="compact">
            <thead>
              <tr>
                <th class="text-left ps-0">Date</th>
                <th class="text-left">Order No</th>
                <th class="text-left">Amount</th>
                <th class="text-left">Payment Method</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="item in financial_reports" :key="item.id">
                <td class="ps-0">{{ formatDate(item.createdAt) }}</td>
                <td>{{ item.invoiceNo }}</td>
                <td>{{ item.amount }}</td>
                <td>{{ item.paymentMethod }}</td>
              </tr>
            </tbody>
          </v-table>
        </v-card-text>
      </v-card>
    </v-col>
  </v-row>
</template>
<script setup lang="ts">
import CountComponent from "~/components/dashboard/CountComponent.vue";
import { ref } from "vue";
import { useReports } from "~/stores/reports";
import { useOrdersStore } from "~/stores/orders";
import { formatDate } from "~/utils/helpers/functions";
import { useRolePermissionsStore } from "~/stores/administration/permissions";
import {DynamicModuleEnum, DynamicPermissionEnum} from "~/utils/roleManagement";

const options= ref({});
const series= ref([44, 55, 41, 17, 15]);
const page = ref({ title: "Welcome to Dashboard" });

//***************************Variables******************//
const store = useReports();
const orderStore = useOrdersStore();
const permissionStore = useRolePermissionsStore();
const breadcrumbs = ref([
  {
    text: "Dashboard",
    disabled: true,
    to: "",
  },
]);

const snackbar = useSnackbar();
const loading = ref(false);
const best_selling_products = ref([]);
const sales_reports = ref([]);
const financial_reports = ref([]);
const return_product_lists = ref([]);
const card_data = ref([]);

onMounted(async () => {
  await fetchBestSellingReport();
  await fetchCustomerReports();
  await fetchFinancialReport();
  await fetchReturnListReport();
  await fetchDashboardCards();
});
const fetchBestSellingReport = async () => {
  if(!permissionStore.hasModulePermission(`${DynamicModuleEnum.PRODUCT_ORDER}:${DynamicPermissionEnum.REPORT}`)){
    snackbar.add({
      type: "warning",
      text: "You don't have permission to download inventory report",
    })
    return;
  }
  
  loading.value = true;
  await store.getBestsellingProductReport("ASC", 1, 5, "", "");
  best_selling_products.value = store.best_selling_products;
  loading.value = false;
};
const fetchCustomerReports = async () => {
  if(!permissionStore.hasModulePermission(`${DynamicModuleEnum.PRODUCT_ORDER}:${DynamicPermissionEnum.REPORT}`)){
    snackbar.add({
      type: "warning",
      text: "You don't have permission to download inventory report",
    })
    return;
  }

  loading.value = true;
  await store.getSalesReport("ASC", 1, 5, "", "");
  sales_reports.value = store.sales;
  loading.value = false;
};
const fetchFinancialReport = async () => {
  if(!permissionStore.hasModulePermission(`${DynamicModuleEnum.PRODUCT_ORDER}:${DynamicPermissionEnum.REPORT}`)){
    snackbar.add({
      type: "warning",
      text: "You don't have permission to download inventory report",
    })
    return;
  }

  loading.value = true;
  await store.getFinancialReport("ASC", 1, 5, "", "");
  financial_reports.value = store.finances;
  loading.value = false;
};
const fetchReturnListReport = async () => {
  loading.value = true;
  await store.getProductReturnListReport("ASC", 1, 5, "", "");
  return_product_lists.value = store.return_list;
  loading.value = false;
};

const fetchDashboardCards = async () => {
  loading.value = true;
  await store.fetchDashboardCards();
  card_data.value = store.cards;
  console.log("card_data.value", card_data.value);

  loading.value = false;
};
</script>
