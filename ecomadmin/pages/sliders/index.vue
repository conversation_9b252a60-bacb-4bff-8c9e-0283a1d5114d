<template>
  <SharedBaseBreadcrumb
    :title="page.title"
    :breadcrumbs="breadcrumbs"
  ></SharedBaseBreadcrumb>

  <v-row>
    <v-col cols="4">
      <SharedUiParentCardSolid body-class="pa-3">
        <h3>🖼️ {{selectedSliderItem ? `Update ${selectedSliderItem?.name}`: 'Create New'}}</h3>
        <v-form @submit.prevent="onSubmit" ref="formRef" class="mt-5">
          <v-text-field v-model="form.name" density="compact" placeholder="Slider Title" :rules="[formRules.required, formRules.minLength]">
          </v-text-field>
          <v-text-field v-model="form.description" density="compact" placeholder="Slider Description"></v-text-field>
          <v-select v-model="form.type" :items="mediaTypes" item-title="state" item-value="value" density="compact" placeholder="Media Type" :rules="[formRules.required]" ></v-select>
          <v-select v-model="form.usagesType" :items="usagesTypes" density="compact" placeholder="Usages Type" :rules="[formRules.required]"></v-select>
          <v-switch v-model="form.isActive" color="success" density="compact" label="Active Status"></v-switch>
          <v-btn type="submit" class="border" color="primary" flat>{{selectedSliderItem ? 'Update': 'Save'}}</v-btn>
          <v-btn @click="handleResetForm" class="border ms-3" color="error" flat>Reset</v-btn>
        </v-form>
      </SharedUiParentCardSolid>
    </v-col>
    <v-col cols="8">
      <SharedUiParentCardSolid body-class="pa-3">
        <v-data-table-server
          v-model:page="options.page"
          v-model:items-per-page="options.itemsPerPage"
          :headers="headers"
          :items="getSliders"
          :search="options?.search"
          :loading="loading"
          item-value="name"
          :items-length="sliderStore?.pagination?.itemCount"
          @update:options="handleFetchSliders"
        >
          <template v-slot:item.action="{ item }">
            <div class="text-end">
              <v-menu>
                <template v-slot:activator="{ props }">
                  <v-btn
                    class="me-2"
                    variant="flat"
                    color="grey200"
                    size="x-small"
                    icon="mdi-dots-vertical"
                    v-bind="props"
                  ></v-btn>
                </template>
                <v-list>
                  <v-list-item>
                    <v-btn
                      icon="mdi-pencil"
                      color="warning"
                      class="me-2"
                      variant="flat"
                      size="x-small"
                      @click="handleEditItem(item)"
                    >
                    </v-btn>
                  </v-list-item>
                  <v-list-item>
                    <v-btn
                      icon="mdi-eye"
                      color="success"
                      class="me-2"
                      variant="flat"
                      size="x-small"
                      :to="{ name: 'sliders-id', params: { id: item?.id } }"
                    >
                    </v-btn>
                  </v-list-item>
                  <v-list-item>
                    <v-btn
                      icon="mdi-delete"
                      color="error"
                      class="me-2"
                      variant="flat"
                      size="x-small"
                      @click="handleDeleteItem(item)"
                    >
                    </v-btn>
                  </v-list-item>
                </v-list>
              </v-menu>
            </div>
          </template>
        </v-data-table-server>
      </SharedUiParentCardSolid>
    </v-col>
  </v-row>
</template>

<script setup lang="ts">
import { useSliderStore } from "~/stores/settings/sliders";

const page = ref({ title: "Sliders List" });
const breadcrumbs = ref([
  {
    text: "Sliders",
    disabled: false,
    to: "/dashboard",
  },
  {
    text: "List",
    disabled: true,
    to: "",
  },
]);

const snackbar = useSnackbar();
const sliderStore = useSliderStore();
const { getSliders } = storeToRefs(sliderStore);
const loading = ref(false);
const selectedSliderItem = ref(null);
const formRef = ref();
const defaultForm = {
  id: null,
  name: null,
  description: null,
  type: null,
  usagesType: null,
  isActive: false,
};
const form = reactive({...defaultForm});

const formRules = {
  required: (v: any)=> !!v || 'This field is required',
  minLength: (v: string)=> (v && v?.length >=3 ) || 'Minimum 3 characters',
}
const options = ref({
  page: 1,
  itemsPerPage: 20,
  sortBy: ["name"],
  sortDesc: [false],
  search: "",
});
const headers = ref([
  { title: "Name", align: "start", sortable: false, key: "name" },
  { title: "Usages Types", align: "start", sortable: false, key: "usagesType" },
  { title: "Media Type", align: "start", sortable: false, key: "type" },
  { title: "Active Status", align: "start", sortable: false, key: "isActive" },
  { title: "Action", align: "end", sortable: false, key: "action" },
]);
const mediaTypes = [
  {state: 'Image', value: 'image'},
  {state:'Video', value: 'video'},
];
const usagesTypes = ['HOMEPAGE_MAIN_BANNER', 'HOMEPAGE_TOP_BOTTOM_BANNER', "BLOG_SLIDER_HERO_BANNER"];

function updateFormFields(data: any){
  form.id = data?.id || null;
  form.name = data?.name;
  form.description = data?.description;
  form.type = data?.type;
  form.usagesType = data?.usagesType;
  form.isActive = data?.isActive || false;
}

const handleEditItem = (item: any)=>{
  selectedSliderItem.value = item;
  updateFormFields(item);
}

const handleFetchSliders = () => {
  sliderStore?.fetchAllSliders();
};

const onSubmit = async ()=>{
  const { valid } = await formRef?.value?.validate();
  if(!valid){
    snackbar.add({
      type: "error",
      text: "Form has errors",
    })
    return;
  }
  sliderStore?.saveSingleSlider({...form})
    .then((response)=>{
      updateFormFields(defaultForm);
      selectedSliderItem.value = null;
      handleFetchSliders();
    })
}

const handleResetForm = ()=>{
  selectedSliderItem.value = null;
  updateFormFields(defaultForm);
}

const handleDeleteItem = (item: any)=>{
  sliderStore?.deleteSingleSlider(item?.id, getSliders?.value)
}

onMounted(() => {
  handleFetchSliders();
});
</script>
