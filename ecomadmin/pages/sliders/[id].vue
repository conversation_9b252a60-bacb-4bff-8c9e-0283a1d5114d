<template>
  <SharedBaseBreadcrumb :title="page.title" :breadcrumbs="breadcrumbs"></SharedBaseBreadcrumb>

  <SharedUiParentCardSolid body-class="pa-3">
    <div class="d-flex ga-4 justify-space-between align-center">
      <h4 class="text-h5">Slider Title: {{sliderData?.name}}</h4>
      <div class="d-flex ga-2">
        <v-btn @click="editDialog=true" density="compact" color="primary" flat>Add New</v-btn>
        <v-btn @click="handleLocalizeSlideSerial" density="compact" color="success" flat>Country Serial</v-btn>
      </div>
    </div>
  </SharedUiParentCardSolid>
  
  <SharedUiParentCardSolid body-class="pa-3" class="mt-3">
    <v-btn v-if="sliderData?.sliderMedias?.length > 0" @click="handleSerialSlides" density="compact" color="primary" flat>Save</v-btn>
		<v-row class="mt-3">
			<v-col v-if="sliderData" cols="12">
        <draggable v-model="sliderData.sliderMedias" :element="'div'" :itemKey="'slider_item'" :options="dragOptions"
          class="v-row">
          <template #item="{ element }">
            <v-col cols="6" sm="3">
              <SlidersCardView :slideData="{...element, parentId: element?.id}" @editItem="handleEditItem" 
                @localizeItem="handleLocalizeItem" @deleteItem="handleDeleteItem"/>
            </v-col>
          </template>
        </draggable>
			</v-col>
		</v-row>
    <v-overlay v-model="editDialog" class="d-flex justify-center align-center">
      <v-sheet class="px-2 py-4 rounded-lg" width="400">
        <h6 class="text-h6 text-center">📝 Slide Media Form</h6>
        <v-form @submit.prevent="singleMediaSubmit" ref="formRef" class="mt-5 cutom-form">
          <v-text-field v-model="form.title" density="compact" placeholder="Slide Title">
          </v-text-field>
          <v-text-field v-model="form.subTitle" density="compact" placeholder="Slide Sub-title" :rules="[]">
          </v-text-field>
          <v-text-field v-model="form.description" density="compact" placeholder="Slide Description" :rules="[]"></v-text-field>
          <v-text-field v-model="form.subDescription" density="compact" placeholder="Slide Sub-description" :rules="[]"></v-text-field>
          <v-text-field v-model="form.buttonText" density="compact" placeholder="Slide button text" :rules="[]"></v-text-field>
          <v-text-field v-model="form.buttonLink" density="compact" placeholder="Button Link Ex. /collection/custom-url" :rules="[]"></v-text-field>
          <v-text-field v-model="form.mediaCustomUrl" density="compact" placeholder="Media Link Ex. /media/custom-url" :rules="[]"></v-text-field>
          <v-select v-model="form.type" :items="mediaTypes" item-title="state" item-value="value" density="compact" placeholder="Media Type" :rules="[formRules.required]" ></v-select>
          <v-switch v-model="form.isActive" color="success" density="compact" label="Active Status"></v-switch>
          <template v-if="form.type === 'image'">
            <FormElementsImageViewerSingle
              :image-id="form?.media?.id ?? form?.media"
              :image-rules="SLIDER_IMAGE_RULES"
              :required-height="2250"
              :required-width="1800"
              :image-folders="[
                {folderSlug: 'SLIDER_IMAGE', title: 'Slider Image', size: '',  mediaType: 'image'}, 
                {folderSlug:'home', title: 'Home(Old)', size: '1808x2000', mediaType: 'image'},
              ]"
              image-model="slider_image"
              :disableImageValidation="true"
              @selected-image-id="selectedHoverImage"
              :key="form.type"
            />
          </template>
          <template v-else-if="form.type === 'video'">
            <FormElementsImageViewerSingle
              :image-id="form?.media?.id ?? form?.media"
              :image-rules="SLIDER_IMAGE_RULES"
              :required-height="2250"
              :required-width="1800"
              :image-folders="[
                {folderSlug:'SLIDER_VIDEO', title: 'Slider Video', size: '', mediaType: 'video'},
              ]"
              image-model="SLIDER_VIDEO"
              :disableImageValidation="true"
              @selected-image-id="selectedHoverImage"
              :key="form.type"
            />
          </template>
          
          <div class="d-flex ga-3 align-center">
            <v-btn type="submit" class="border w-50" color="primary" flat>{{selectedItem ? 'Update': 'Save'}}</v-btn>
            <v-btn @click="handleResetForm" class="border" color="error" flat>Reset</v-btn>
          </div>
        </v-form>
      </v-sheet>
    </v-overlay>
	</SharedUiParentCardSolid>
  <SlidersLocalizeSlide :key="selectedItem?.id" ref="slideLocalizeRef" @fetchSingleSlider="handleFetchSingleSlider"/>
  <SlidersLocalizeSlideSerial v-if="sliderData" ref="slideLocalizeSerialRef" :sliderData="sliderData" @fetchSingleSlider="handleFetchSingleSlider"/>

</template>
<script setup lang="ts">
import {SLIDER_IMAGE_RULES, SLIDER_VIDEO_RULES} from "~/utils/imageRules";
import { useSliderStore } from "~/stores/settings/sliders";

const page = ref({ title: "Sliders List" });
const breadcrumbs = ref([
  {
    text: "Sliders",
    disabled: false,
    to: "/dashboard",
  },
  {
    text: "List",
    disabled: true,
    to: "",
  },
]);

const snackbar = useSnackbar();
const route = useRoute();
const sliderStore = useSliderStore();
const { getSliders } = storeToRefs(sliderStore);
const loading = ref(false);
const sliderId = route.params.id;
const sliderData = ref();
const selectedItem = ref(null);
const selectedLocalizeItem = ref();
const editDialog = ref(false);
const slideLocalizeRef = ref();
const slideLocalizeSerialRef = ref(null);
const formRef = ref();
const defaultForm = reactive({
  slider: Number(route?.params?.id),
  id: null,
  title: null,
  subTitle: null,
  description: null,
  subDescription: null,
  type: null,
  buttonLink: null,
  buttonText: null,
  mediaCustomUrl: null,
  media: null,
  usagesType: sliderData?.value?.usagesType,
  isActive: false,
});
const form = reactive({...defaultForm})

const formRules = {
  required: (v: any)=> !!v || 'This field is required',
  minLength: (v: string)=> (v && v?.length >=3 ) || 'Minimum 3 characters',
}

const mediaTypes = [
  {state: 'Image', value: 'image'},
  {state:'Video', value: 'video'},
];

const dragOptions = {
  animation: 150
};

const handleEditItem = (item: any)=>{
  editDialog.value = true;
  selectedItem.value = item;
  updateFormFields(item);
}

const handleLocalizeItem = (item: any)=>{
  slideLocalizeRef?.value?.show(item);
  selectedLocalizeItem.value = item;
}

const handleDeleteItem = (item: any)=>{
  sliderStore?.deleteSliderMedia(item?.id, sliderData?.value?.sliderMedias);
}

const handleLocalizeSlideSerial = ()=>{
  slideLocalizeSerialRef?.value?.show();
}

const handleResetForm = ()=>{
  selectedItem.value = null;
  updateFormFields(defaultForm);
}

function updateFormFields(data: any){
  form.id = data?.id || null;
  form.title = data?.title;
  form.subTitle = data?.subTitle;
  form.description = data?.description;
  form.subDescription = data?.subDescription;
  form.type = data?.type;
  form.buttonLink = data?.buttonLink;
  form.buttonText = data?.buttonText;
  form.mediaCustomUrl = data?.mediaCustomUrl;
  form.media = data?.media || null;
  form.isActive = data?.isActive || false;
}

const selectedHoverImage = (val: any) => {
  form.media = val;
}

const handleSerialSlides = async ()=>{
	sliderData?.value?.sliderMedias?.forEach((item: any, index: number) => {
		item.sortOrder = index;
	});
	sliderStore?.saveSliderMediaSerial(sliderData?.value?.sliderMedias)
		.then((response: any)=>{
			snackbar.add({
				type: 'success',
				text: 'Successfully updated the serial',
			})
      handleResetForm();
		})
		.catch((e)=>{
			snackbar.add({
				type: 'error',
				text: 'Failed to update the serial',
			})
			console.log(e);
		})
		.finally(()=>{
		})
}

const singleMediaSubmit = async ()=>{
  const { valid } = await formRef?.value?.validate();
  if(!valid){
    snackbar.add({
      type: "error",
      text: "Form has errors",
    })
    return;
  }
  let payload = {
    ...form,
    usagesType: sliderData?.value?.usagesType,
    sortOrder: sliderData?.value?.sliderMedias?.length,
    isActive: form?.isActive || false,
  }
  sliderStore?.saveSingleSlideMedia(payload)
    .then((response)=>{
      updateFormFields(defaultForm);
      editDialog.value = false;
      selectedItem.value = null;
      handleFetchSingleSlider();
    })
}

const handleFetchSingleSlider = () => {
  sliderStore?.fetchSingleSlider({ id: sliderId })
		.then((response: any) => {
			sliderData.value = response?.items;
      sliderData.value.sliderMedias = sliderData?.value?.sliderMedias.sort((a: any, b: any)=> (a?.sortOrder ?? 0) - (b?.sortOrder ?? 0));
		});
};

onMounted(()=>{
	handleFetchSingleSlider();
})
</script>