<template>
  <SharedBaseBreadcrumb
    :title="page.title"
    :breadcrumbs="breadcrumbs"
  ></SharedBaseBreadcrumb>
  <template v-if="!permissionStore.hasModulePermission(`${DynamicModuleEnum.ROLES}:${DynamicPermissionEnum.READ_MANY}`)">
    <SharedUiParentCardSolid class="mt-6" title="Product Details">
      <h3 class="text-h3 text-error border border-dotted pa-5 text-center">You don't have permission to see role list</h3>
    </SharedUiParentCardSolid>
  </template>

  <template v-else>
    <SharedUiLoader
      v-if="loading"
      type="table-heading, table-thead, table-tbody, table-tfoot"
    />
    <template v-else>
      <SharedUiParentCardSolid body-class="pa-3" style="min-height: 300px">
        <div class="d-flex justify-space-between ga-3">
          <h6 class="text-h5">✊ Roles</h6>
          <v-btn @click="handleActionModal('add', null)" color="primary" variant="flat"
            >Add new role</v-btn
          >
        </div>
        <SharedUiParentCardSolid
          v-if="!roleList || roleList?.length < 1"
          body-class="pa-3"
          class="mt-3"
        >
          <div class="h-100 d-flex justify-center align-center">
            <v-sheet
              class="w-md-50 pa-2 pa-md-8 border border-error border-dotted rounded"
            >
              <h3 class="text-h3 text-center">Role Empty</h3>
            </v-sheet>
          </div>
        </SharedUiParentCardSolid>
        <v-row v-else class="mt-3">
          <v-col
            v-for="(role, index) in roleList"
            :key="'role_' + index"
            cols="12"
            sm="6"
            md="3"
          >
            <v-sheet class="pa-4 border">
              <div class="d-flex justify-space-between align-center">
                <div class="d-flex align-center ga-2">
                  <h6 class="text-body-1 font-weight-bold">{{ role?.name }}</h6>
                  <v-chip
                    class="me-2"
                    size="x-small"
                    variant="flat"
                    :color="role?.isActive ? 'success' : 'error'"
                    >{{ role?.isActive ? "Active" : "Inactive" }}
                  </v-chip>
                </div>
                <div>           
                  <v-menu>
                    <template v-slot:activator="{ props }">
                      <v-btn
                        variant="flat"
                        size="x-small"
                        icon="mdi-dots-vertical"
                        v-bind="props"
                      ></v-btn>
                    </template>

                    <v-list>
                      <v-list-item>
                        <v-btn
                          icon="mdi-pencil"
                          color="warning"
                          class="me-2"
                          variant="flat"
                          size="x-small"
                          @click="handleActionModal('edit', role)"
                        >
                        </v-btn>
                      </v-list-item>
                      <v-list-item>
                        <v-btn
                          icon="mdi-shield-key"
                          color="primary"
                          class="me-2"
                          variant="flat"
                          size="x-small"
                          :to="{
                            name: 'administration-permissions-id',
                            params: {
                              id: role.id,
                            },
                          }"
                        >
                        </v-btn>
                      </v-list-item>
                      <!-- <v-list-item>
                        <v-btn
                          icon="mdi-translate"
                          color="info"
                          class="me-2"
                          variant="tonal"
                          size="x-small"
                          @click="localizeCategory(item)"
                        >
                        </v-btn>
                      </v-list-item> -->
                      <v-list-item>
                        <v-btn
                          icon="mdi-delete"
                          color="error"
                          class="me-2"
                          variant="flat"
                          size="x-small"
                          @click="
                            confirmDialogDelete = true;
                            itemToDelete = role;
                          "
                        >
                        </v-btn>
                      </v-list-item>
                    </v-list>
                  </v-menu>
                </div>
              </div>
              <p
                class="mt-2 font-weight-light text-body-2 text-truncate"
                style="height: 50px; max-width: 100px"
              >
                {{ role?.description }}
              </p>
              <p>Created: {{ formatDateTime(role?.createdAt) }}</p>
            </v-sheet>
          </v-col>
          <v-col cols="12">
            <v-pagination v-model="options.page" :length="options.pageCount" :total-visible="3"
              class="mt-3" @update:modelValue="onChangePagination">
            </v-pagination>
          </v-col>
        </v-row>
      </SharedUiParentCardSolid>
    </template>
    <ConformationModal
      v-model:dialog="confirmDialogDelete"
      @confirm-delete="handleDeleteConfirmation"
    />
    <ConformationModal
      v-model:dialog="confirmDialogActiveInactive"
      @confirm-delete="handleActiveInactiveConfirmation"
      :confirmButton="'confirm'"
    />

    <v-dialog v-model="editRoleModal" max-width="400" persistent>
      <v-card>
        <v-card-title class="d-flex justify-space-between align-center"
          ><div class="w-50">{{ selectEditRoleItem ? "Edit" : "Add" }} Role</div>
          <v-switch
            v-model="selectEditRoleItem.isActive"
            @update:modelValue="handleRoleStatus"
            v-if="selectEditRoleItem"
            label="active"
            color="primary"
            hide-details
          ></v-switch
        ></v-card-title>
        <v-card-text>
          <v-form>
            <v-text-field
              v-model="formData.name"
              label="Role name"
              density="compact"
            ></v-text-field>
            <v-textarea
              v-model="formData.description"
              label="Role Description"
              rows="3"
              density="compact"
            ></v-textarea>
          </v-form>
        </v-card-text>
        <v-card-actions class="text-right">
          <v-btn @click="editRoleModal = false">Cancel</v-btn>
          <v-btn color="error" @click="saveRole">Save</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </template>
</template>


<script setup lang="ts">
import ConformationModal from "~/components/modals/ConformationModal.vue";
import { useRolePermissionsStore } from "~/stores/administration/permissions";
import { formatDateTime } from "~/utils/helpers/functions";
import{ DynamicModuleEnum, DynamicPermissionEnum} from "~/utils/roleManagement";

const page = ref({ title: "Role Management" });
const breadcrumbs = ref([
  {
    text: "Administration",
    disabled: true,
  },
  {
    text: "Role Management",
    disabled: false,
    to: "/administration/role-management",
    description: "a sndansdnas dasndanskdnasd",
  },
]);

const router = useRouter();
const route = useRoute();
const snackbar = useSnackbar();
const permissionStore = useRolePermissionsStore();
const roleList = ref([]);
const confirmDialogDelete = ref(false);
const confirmDialogActiveInactive = ref(false);
const itemToDelete = ref(null);
const editRoleModal = ref(false);
const selectEditRoleItem = ref(null);
const loading = ref(true);
const initialState = {
  name: null,
  description: null,
};
var formData = reactive(initialState);
var options = ref({
  order: "ASC",
  page: 1,
  take: 10,
  hasNextPage: false,
  hasPreviousPage: false,
  itemCount: 0,
  pageCount: 1,
})

function resetFormData() {
  formData = reactive({ ...initialState });
}

const handleActionModal = (type: String, item: any) => {
  if(type === 'add' && !permissionStore.hasModulePermission(`${DynamicModuleEnum.ROLES}:${DynamicPermissionEnum.CREATE}`)){
    snackbar.add({
      type: "warning",
      text: "You don't have permission to create role",
    })
    return;
  }
  if(type === 'edit' && !permissionStore.hasModulePermission(`${DynamicModuleEnum.ROLES}:${DynamicPermissionEnum.UPDATE}`)){
    snackbar.add({
      type: "warning",
      text: "You don't have permission to edit role",
    })
    return;
  }

  editRoleModal.value = true;
  if (type === "add") {
    selectEditRoleItem.value = null;
    resetFormData();
  } else if (type === "edit") {
    selectEditRoleItem.value = item;
    formData.name = selectEditRoleItem?.value?.name;
    formData.description = selectEditRoleItem?.value?.description;
  }
};

const handleConcatNewRoleList = (roleItem: any) => {
  let payload = {
    id: roleItem?.id,
    name: roleItem?.name,
    description: roleItem?.description,
    isActive: roleItem?.isActive,
    createdAt: roleItem?.createdAt,
  };
  roleList?.value?.unshift(payload);
};

const handleRemoveRole = (roleItem: any) => {
  roleList.value = roleList?.value?.filter(
    (el) => el?.id != roleItem?.id
  );
};

const handleRoleStatus = (val) => {
  
  if(!permissionStore.hasModulePermission(`${DynamicModuleEnum.ROLES}:${DynamicPermissionEnum.UPDATE}`)){
    snackbar.add({
      type: "warning",
      text: "You don't have permission to edit role status",
    })
    return;
  }

  if (selectEditRoleItem.value) {
    confirmDialogActiveInactive.value = true;
  }
};

const handleActiveInactiveConfirmation = () => {
  
  let params = {
    id: selectEditRoleItem?.value?.id,
    status: selectEditRoleItem?.value?.isActive,
  };
  permissionStore
    ?.activeInactiveRole(params)
    .then((response) => {
      snackbar.add({
        type: "success",
        text: `Changed ${selectEditRoleItem?.value?.name} status!`,
      });
      confirmDialogActiveInactive.value = false;
      editRoleModal.value = false;
    })
    .catch((e) => {
      snackbar.add({
        type: "error",
        text: `Changed ${selectEditRoleItem?.value?.name} status failed!`,
      });
      confirmDialogActiveInactive.value = false;
      editRoleModal.value = false;
    });
};

const handleDeleteConfirmation = async () => {
  try {
    if(!permissionStore.hasModulePermission(`${DynamicModuleEnum.ROLES}:${DynamicPermissionEnum.SOFT_DELETE}`)){
      snackbar.add({
        type: "warning",
        text: "You don't have permission to delete role",
      })
      return;
    }

    if (itemToDelete?.value !== null) {
      permissionStore
        ?.deleteRole({ id: itemToDelete?.value?.id })
        .then((response) => {
          snackbar.add({
            type: "success",
            text: response?.messasge || "Role deleted successfully!",
          });
          handleRemoveRole(itemToDelete?.value);
          itemToDelete.value = null;
        })
        .catch((e) => {
          snackbar.add({
            type: "error",
            text: "Role deletion failed!",
          });
        });
    }
  } catch (error) {
    console.log("delete new role failed: page level");
  }
};

const addNewRoleItem = () => {
  try {
    permissionStore
      .createRole(formData)
      .then((response) => {
        if (response?.isSuccess) {
          snackbar.add({
            type: "success",
            text: "New role added successfully!",
          });
          handleConcatNewRoleList(response?.data);
          editRoleModal.value = false;
        }
      })
      .catch((e) => {
        snackbar.add({
          type: "error",
          text: "failed new role adding!",
        });
        editRoleModal.value = false;
      });
  } catch (error) {
    console.log("add new role failed: page level");
  }
};

const editRoleItem = () => {
  try {
    permissionStore
      .updateRole({ ...formData, id: selectEditRoleItem?.value?.id })
      .then((response) => {
        if (response?.isSuccess) {
          snackbar.add({
            type: "success",
            text: response?.messasge || "Updated role successfully!",
          });
          let getEditedRoleItemIndex = roleList?.value?.findIndex(
            (roleItem) => roleItem?.id === selectEditRoleItem?.value?.id
          );
          if(getEditedRoleItemIndex!=-1){
            roleList.value[getEditedRoleItemIndex].name = formData?.name; 
            roleList.value[getEditedRoleItemIndex].description = formData?.description;
          }
          editRoleModal.value = false;
          selectEditRoleItem.value = null;
        }
      })
      .catch((e) => {
        snackbar.add({
          type: "error",
          text: "failed update role!",
        });
        console.log(e);
        editRoleModal.value = false;
        selectEditRoleItem.value = null;
      });
  } catch (error) {
    console.log("Update role failed: page level", error);
  }
};

const setRouteQueryParams = ()=>{
  router.push({
    query: { ...route.query, page: String(options.value.page)}
  })
}

const getRouteQueryParams = ()=>{
  options.value.page = Number(route?.query?.page) || 1;
  options.value.order = String(route?.query?.order || 'ASC');
}

const onChangePagination = ()=>{
  setRouteQueryParams();
  handleFetchRoles();
}

const handleFetchRoles = async() => {

  let params = {
    order: options?.value?.order ,
    page: options?.value?.page || 1,
    take: options?.value?.take,
  }
  
  await permissionStore.fetchRoles(params).then((response)=>{
    roleList.value = response?.data;
    if(response?.meta){
      options.value.hasNextPage = response?.meta?.hasNextPage;
      options.value.hasPreviousPage = response?.meta?.hasPreviousPage;
      options.value.itemCount = response?.meta?.itemCount;
      options.value.page = response?.meta?.page;
      options.value.pageCount = response?.meta?.pageCount;
      options.value.take = response?.meta?.take;
    }
  }).catch((e)=>{
  })

};



const handleFetchRolesMounted = async() => {

  getRouteQueryParams();

  let params = {
    order: options?.value?.order ,
    page: options?.value?.page || 1,
    take: options?.value?.take,
  }
  
  await permissionStore.fetchRoles(params).then((response)=>{
    roleList.value = response?.data;
    if(response?.meta){
      options.value.hasNextPage = response?.meta?.hasNextPage;
      options.value.hasPreviousPage = response?.meta?.hasPreviousPage;
      options.value.itemCount = response?.meta?.itemCount;
      options.value.page = response?.meta?.page;
      options.value.pageCount = response?.meta?.pageCount;
      options.value.take = response?.meta?.take;
    }
  }).catch((e)=>{
  })

};

const saveRole = () => {
  if (selectEditRoleItem.value) {
    editRoleItem();
  } else {
    addNewRoleItem();
  }
};

watch(()=> route.query, (newVal, oldVal)=>{
  if(newVal != oldVal){
    getRouteQueryParams();
    handleFetchRolesMounted();
  }
})

onMounted(async () => {
  if(!permissionStore.hasModulePermission(`${DynamicModuleEnum.ROLES}:${DynamicPermissionEnum.READ_MANY}`)){
    snackbar.add({
      type: "warning",
      text: "You don't have permission to see role list",
    })
    return;
  }

  loading.value = true;
  await handleFetchRolesMounted();
  loading.value = false;
});
</script>
