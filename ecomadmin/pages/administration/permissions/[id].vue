<template>
  <SharedBaseBreadcrumb
    :title="page.title"
    :breadcrumbs="breadcrumbs"
  ></SharedBaseBreadcrumb>

  <template v-if="!rolePermissionStore?.hasModulePermission(`${DynamicModuleEnum.ROLES}:${DynamicPermissionEnum.READ_ONE}`)">
    <SharedUiParentCardSolid class="mt-6" title="Product Details">
      <h3 class="text-h3 text-error border border-dotted pa-5 text-center">You don't have permission to see action list</h3>
    </SharedUiParentCardSolid>
  </template>
  <template v-else>
    <SharedUiLoader
      v-if="loading"
      type="table-heading, table-thead, table-tbody, table-tfoot"
    />
    <template v-else>
      <SharedUiParentCardSolid
        v-if="!singleRoleDetail || singleRoleDetail.length <= 1"
        body-class="pa-3"
      >
        <div class="h-100 d-flex justify-center align-center" id="strickyHeader">
          <v-sheet
            class="w-md-50 pa-2 pa-md-8 border border-error border-dotted rounded"
          >
            <h3 class="text-h3 text-center">Invalid Role Permissions</h3>
          </v-sheet>
        </div>
      </SharedUiParentCardSolid>
      <SharedUiParentCardSolid v-else body-class="pa-3" style="min-height: 300px">
        <div class="d-flex flex-column overflow-x-auto">
          <div :class="{ 'sticky-permission-header pa-3': isSticky }" class="mb-3 d-flex flex-column flex-sm-row justify-space-between">

            <div class="d-flex ga-2 align-center">
              <p class="text-uppercase font-weight-bold text-body-1">
                <v-icon icon="mdi-cog" size="20" class="me-1"></v-icon>Modules
                <span class="text-capitalize text-error">({{ singleRoleDetail?.name }})</span>
              </p>
              <div class="d-flex ga-2">
                <div>
                  <input v-model="selectedAllPermission" type="checkbox" style="width: 15px; height: 15px" 
                      class="ms-4 cursor-pointer" id="selectAllPermission"
                      @change="(event) => {handleAllPermissionSelect(event.target.checked)}"/>
                  <label for="selectAllPermission" class="ms-2 cursor-pointer">Select All</label>
                </div>
                <p>({{totalActiveInactive.active}}/{{ grandTotalPermissions }})</p>
              </div>
            </div>

            <v-btn @click="savePermission" :loading="loading" color="primary">Save</v-btn>

          </div>

          <div v-for="(optionModule, index) in getModules" :key="'module_' + index" class="pa-2 d-flex ga-1 align-center rounded"
            :class="{ 'bg-grey100': index % 2 == 0 }" style="width: max-content">
            <h5 class="font-weight-medium text-caption" style="min-width: 200px">{{ beautifyPermissionName(optionModule) }}</h5>
            <div class="d-flex ga-1">
              <div v-for="(permission, index) in getDefaultPermissions" :key="'action_' + index" 
                class="action-box d-flex flex-column justify-center align-center border rounded">
                <input :id="`${optionModule}:permission${index}`" type="checkbox" style="width: 15px; height: 15px" class="cursor-pointer" 
                  :name="`${optionModule}:permission${index}`" :checked="isActivePermission({module: optionModule,action: permission,})"
                  @change="
                    (event) => {
                      handlePermissionToggle(
                        {
                          module: optionModule,
                          action: permission,
                        },
                        event.target.checked
                      );
                    }
                  "
                />
                <label :for="`${optionModule}:permission${index}`" class="text-center text-caption">
                  {{ beautifyPermissionName(permission) }}
                </label>
              </div>
            </div>
            <div class="d-flex ga-1">
              <div
                v-for="(additionalPermission, index) in getAdditionalPermissions[optionModule]" :key="'action_' + index"
                class="action-box d-flex flex-column justify-center align-center border rounded"
              >
                <input :id="`${optionModule}:additionalPermission${index}`" type="checkbox" style="width: 15px; height: 15px"
                  class="cursor-pointer" :name="`${optionModule}:permission${index}`" 
                  :checked="
                    isActivePermission({
                      module: optionModule,
                      action: additionalPermission,
                    })
                  "
                  @change="
                    (event) => {
                      handlePermissionToggle(
                        {
                          module: optionModule,
                          action: additionalPermission,
                        },
                        event.target.checked
                      );
                    }
                  "/>
                <label :for="`${optionModule}:additionalPermission${index}`" class="text-center text-caption">
                  {{ beautifyPermissionName(additionalPermission) }}
                </label>
              </div>
            </div>
          </div>

        </div>
      </SharedUiParentCardSolid>
    </template>
  </template>
</template>


<script setup>
import { useRolePermissionsStore } from "~/stores/administration/permissions.ts";
import { beautifyPermissionName } from "~/utils/roleManagement";
import{ DynamicModuleEnum, DynamicPermissionEnum} from "~/utils/roleManagement";
import { getCountActiveInactive } from "~/utils/helpers/functions";

const page = ref({ title: "Role Permissions" });
const breadcrumbs = ref([
  {
    text: "Administration",
    disabled: true,
  },
  {
    text: "Role Management",
    disabled: false,
    to: "/administration/role-management",
  },
  {
    text: "Permissions",
    disabled: true,
    to: "/administration/permissions",
  },
]);
const route = useRoute();
const rolePermissionStore = useRolePermissionsStore();
const { getModules, getDefaultPermissions, getAdditionalPermissions } =
  storeToRefs(rolePermissionStore);
const snackbar = useSnackbar();
const singleRoleDetail = ref([]);
const loading = ref(true);
const selectedAllPermission = ref(false);
const totalActiveInactive = reactive({
  active: 0,
  inactive: 0,
})
const grandTotalPermissions = computed(()=>{
  let totalAdditionalPermissions = Object.keys(getAdditionalPermissions?.value)?.reduce(
    (acc, item)=> {
      return acc + getAdditionalPermissions?.value[item].length;
    }, 0) || 0;
  return (getModules?.value?.length * getDefaultPermissions?.value?.length) + totalAdditionalPermissions;
})

const isActivePermission = (permissionItem) => {
  if (permissionItem) {
    let permissionObj = singleRoleDetail?.value?.permissions?.find(
      (item) =>
        `${item?.module}${item?.action}` ===
        `${permissionItem?.module}${permissionItem?.action}`
    );
    return permissionObj?.isActive ? permissionObj?.isActive : false;
  }
};

const addPermissionToModule = (permissionItem) => {
  if (!permissionItem || !singleRoleDetail.value?.permissions) {
    snackbar.add({
      type: "error",
      text: "No permission selected. team contact!",
    });
  }

  let existedPermissionId = singleRoleDetail?.value?.permissions?.findIndex(
    (item) =>
      `${item?.module}${item?.action}` ===
      `${permissionItem?.module}${permissionItem?.action}`
  );
  if (existedPermissionId != -1) {
    singleRoleDetail.value.permissions[existedPermissionId].isActive = true;
  } else {
    singleRoleDetail?.value?.permissions.push({ ...permissionItem });
  }
};

const removePermissionToModule = (permissionItem) => {
  if (!permissionItem || !singleRoleDetail.value?.permissions) {
    snackbar.add({
      type: "error",
      text: "No permission selected. team contact!",
    });
  }

  let existedPermissionId = singleRoleDetail?.value?.permissions?.findIndex(
    (item) =>
      `${item?.module}${item?.action}` ===
      `${permissionItem?.module}${permissionItem?.action}`
  );
  if (existedPermissionId != -1) {
    singleRoleDetail.value.permissions[existedPermissionId].isActive = false;
  } else {
    singleRoleDetail.value.permissions =
      singleRoleDetail?.value?.permissions?.filter(
        (item) =>
          `${item?.module}${item?.action}` !=
          `${permissionItem?.module}${permissionItem?.action}`
      );
  }
};

const handlePermissionToggle = (permissionItem, checked) => {
  if (checked === true) {
    addPermissionToModule(permissionItem);
  } else if (checked === false) {
    removePermissionToModule(permissionItem);
  }
};

const findObjectIndexInsideArray = (itemList, objectItem)=>{
  return itemList?.findIndex(
    (item) =>
      `${item?.module}${item?.action}` ===
      `${objectItem?.module}${objectItem?.action}`
  );
}

const toggleAllPermissions = async (checked)=>{
  if(checked){
    getModules?.value?.forEach(optionModule => {
      
      getDefaultPermissions?.value?.forEach((defaultPermission)=>{
        let existedPermissionId = findObjectIndexInsideArray(singleRoleDetail?.value?.permissions, {module:optionModule, action: defaultPermission});
        if (existedPermissionId != -1) {
          singleRoleDetail.value.permissions[existedPermissionId].isActive = true;
        } else {
          singleRoleDetail?.value?.permissions.push({ module: optionModule, action: defaultPermission, isActive: true });
        }
      })

      getAdditionalPermissions[optionModule]?.value?.forEach((additionalPermission)=>{
        let existedPermissionId = findObjectIndexInsideArray(singleRoleDetail?.value?.permissions, {module:optionModule, action: additionalPermission});
        if (existedPermissionId != -1) {
          singleRoleDetail.value.permissions[existedPermissionId].isActive = true;
        } else {
          singleRoleDetail?.value?.permissions.push({ module: optionModule, action: additionalPermission, isActive: true });
        }
      })
    });
  }else{
    // singleRoleDetail.value.permissions = [];
    getModules?.value?.forEach(optionModule => {
      getDefaultPermissions?.value?.forEach((defaultPermission)=>{
        let existedPermissionId = findObjectIndexInsideArray(singleRoleDetail?.value?.permissions, {module:optionModule, action: defaultPermission})
        if (existedPermissionId != -1) {
          singleRoleDetail.value.permissions[existedPermissionId].isActive = false;
        } else {
          singleRoleDetail?.value?.permissions.push({ module: optionModule, action: defaultPermission, isActive: false });
        }
      })

      getAdditionalPermissions[optionModule]?.value?.forEach((additionalPermission)=>{
        let existedPermissionId = findObjectIndexInsideArray(singleRoleDetail?.value?.permissions, {module:optionModule, action: additionalPermission})
        if (existedPermissionId != -1) {
          singleRoleDetail.value.permissions[existedPermissionId].isActive = false;
        } else {
          singleRoleDetail?.value?.permissions.push({ module: optionModule, action: additionalPermission, isActive: false });
        }
      })
    });
  }
}

const handleAllPermissionSelect = async(checked)=>{
  loading.value = true;
  await toggleAllPermissions(checked);
  loading.value = false;
}

const handleFetchRolePermissions = () => {
  rolePermissionStore.fetchModulePermissions();
};

const handleUpdateActiveInactiveCount = (itemList)=>{
  if(itemList){
    let obj = getCountActiveInactive(itemList);
    totalActiveInactive.active = obj?.totalActive;
    totalActiveInactive.inactive = obj?.totalInactive;
  }
}

const handleFetchSingleRoleDetail = async () => {
  const id = route?.params?.id;
  try {
    if (id) {
      await rolePermissionStore
        ?.fetchSingleRole({ id: id })
        .then((response) => {
          if (response?.isSuccess) {
            singleRoleDetail.value = response?.data;
            handleUpdateActiveInactiveCount(response?.data?.permissions);
          }
        })
        .catch((e) => {});
    }
  } catch (error) {
    console.log("single role data error: page level");
  }
};


const savePermission = async () => {
  
  let payload = {
    roleId: route?.params?.id,
    permissions: singleRoleDetail?.value?.permissions,
  };
  loading.value = true;
  console.log
  rolePermissionStore
    ?.updatePermissionByRole(payload)
    .then(async (response) => {
      snackbar.add({
        type: "success",
        text: response?.messasge || "Permissions added successfully",
      });
      loading.value = false;

      // TODO(Optimization): Need to do more efficient without calling this API.
      await handleFetchSingleRoleDetail();
    })
    .catch((e) => {
      loading.value = false;
    });
};

const isSticky = ref(false);
var offset = ref(92);

const handleScroll = () => {
  isSticky.value = window.scrollY >= offset.value;
}

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll);
})

onMounted(async () => {
  if(!rolePermissionStore?.hasModulePermission(`${DynamicModuleEnum.ROLES}:${DynamicPermissionEnum.READ_ONE}`)){
    snackbar.add({
      type: "warning",
      text: "You don't have permission to see permission list",
    })
    return;
  }

  loading.value = true;
  handleFetchRolePermissions();
  await handleFetchSingleRoleDetail();
  loading.value = false;
  window.addEventListener('scroll', handleScroll);
});
</script>


<style scoped>
.action-box {
  width: 140px;
  height: 65px;
}

#stickyHeader{
  position: relative;
}

.sticky-permission-header{
  position: fixed;
  top: 50px;
  left: 16px;
  right: 16px;
  background: white;
  border-bottom: 2px solid #000;
}
</style>