<template>
  <v-row>
    <v-col cols="4">
      <SharedUiParentCard :title="attribute.id ? 'Edit ' + attribute.name : 'Add New Attribute'">
        <v-form @submit.prevent="onSubmit" ref="attributeForm" v-model="form">

          <v-label class="mb-2 font-weight-medium">Name</v-label>
          <v-text-field v-model="attribute.name" :rules="rules" hide-details></v-text-field>

          <!--<v-label class="my-3">Slug</v-label>
          <v-text-field v-model="attribute.slug" hide-details></v-text-field>
          -->
          <v-label class="my-3">Description</v-label>
          <FormElementsEditor v-model="attribute.description"/>

          <v-divider class="my-5"></v-divider>
          <v-btn
              :disabled="!form"
              :loading="loading"
              color="primary"
              variant="tonal" class="px-6"
              type="submit"> {{ attribute.id ? 'Update' : 'Add New' }} Attribute
          </v-btn>
          <v-btn
              color="error"
              variant="tonal" class="px-6 ms-2"
              @click="resetForm"
          > Reset
          </v-btn>
        </v-form>
      </SharedUiParentCard>
    </v-col>
    <v-col cols="8">


      <LazySharedGerenalTableSkeleton :loading="store.loading" :data="getAttributes">
        <template #default>
          <SharedUiParentCard title="Attributes">
            <TableComponentsSimpleTable :theads="theads">
              <template #default>
                <tr v-for="item in getAttributes" :key="item.id" class="month-item">
                  <td>
                    <h6 class="text-h6 font-weight-medium text-medium-emphasis text-no-wrap">{{ item.name }}</h6>
                  </td>
                  <td>
                    <span v-html="item.slug"></span>
                  </td>
                  <td>
                    <span v-html="item.description"></span>
                  </td>
                  <td>
                    <v-chip :color="item.isActive ? 'success' : 'error' " variant="elevated">
                      {{ item.isActive ? 'Active' : 'Inactive' }}
                    </v-chip>
                  </td>
                  <td>
                    <ModalsAttributeValuesShowModal :id="item.id" :name="item.name"/>
                    <v-btn icon color="warning" class="me-2" variant="tonal" size="small"
                           @click="editPayloadItem( item, attribute ), store.getAttributes({})">
                      <PencilIcon size="20"/>
                    </v-btn>
                    <v-btn icon color='error' variant="tonal" size="small" @click="deleteItem(item.id)">
                      <TrashIcon size="20"/>
                    </v-btn>
                  </td>
                </tr>
              </template>
            </TableComponentsSimpleTable>
          </SharedUiParentCard>
        </template>
      </LazySharedGerenalTableSkeleton>
    </v-col>
  </v-row>

</template>
<script setup lang="ts">

/************Imports***************/
import {PencilIcon, TrashIcon} from "vue-tabler-icons";
import {computed, onMounted, ref} from 'vue';
import {useProductAttributesStore} from "~/stores/products/attributes";
import {editPayloadItem} from "~/utils/helpers/functions";

/************Variables***************/
const snackbar = useSnackbar();

const theads = ['Name', 'Slug', 'Description', 'Status', 'Action'];

const store = useProductAttributesStore();

let dialog = ref(false)

const rules = [
  value => {
    if (value) return true;
    return 'You must enter a name'
  }
];

const form = ref(false)
const attributeForm = ref(false);
const loading = ref(false);

const attribute = ref({
  id: null,
  name: '',
  description: '',
  slug: ''
})

/************Methods***************/
const onSubmit = () => {

  if (!attributeForm.value) return;

  loading.value = true

  setTimeout(() => {
    store.addUpdateAttribute({
      id: attribute.value.id,
      name: attribute.value.name,
      description: attribute.value.description,
      slug: attribute.value.slug
    }).then(res => {
      store.getAttributes({})
      snackbar.add({
        type: res.success ? 'success' : 'error',
        text: res.message
      })
    }).catch(err => {
      console.log(err)
    })

    loading.value = false
    resetForm()

  }, 500);

}

const deleteItem = (id: number) => {

  store.deleteAttribute(id).then(res => {
    store.getAttributes({})
    snackbar.add({
      type: 'error',
      text: res
    })
  })

}

const resetForm = () => {
  attributeForm.value.reset()
  attribute.value.id = null
  attribute.value.name = ''
  attribute.value.description = ''
  attribute.value.slug = ''
}


/************Life Cycle Hooks***************/

const getAttributes = computed(() => {
  return store.attributes;
});

onMounted(() => {
  store.getAttributes({})
});


</script>