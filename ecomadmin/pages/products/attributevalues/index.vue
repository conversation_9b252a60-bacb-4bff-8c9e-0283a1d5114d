<template>
  <SharedBaseBreadcrumb
      :title="page.title"
      :breadcrumbs="breadcrumbs"
  ></SharedBaseBreadcrumb>

  <v-row>
    <v-col md="4">
      <SharedUiParentCard :title="attributeValue.id ? 'Edit ' + attributeValue.name : 'Add New Value'">
        <v-form @submit.prevent="onSubmit" ref="attributeValueForm" v-model="form">

          <FormElementsCommonFieldContainer label="Name" :required="true">
            <v-text-field v-model="attributeValue.name" :rules="[val => REQUIRED_RULE(val)]"></v-text-field>
          </FormElementsCommonFieldContainer>

          <FormElementsCommonFieldContainer label="Attribute" :required="true">
            <v-select :items="getAttributes" item-title="name" item-value="id"
                      :rules="[val => SELECT_REQUIRED_RULE(val)]" v-model="attributeValue.attributeId"
                      placeholder="Select Attribute"></v-select>
          </FormElementsCommonFieldContainer>

          <FormElementsCommonFieldContainer label="Sort Order">
            <v-text-field v-model="attributeValue.sortOrder" placeholder="Sort Order"></v-text-field>
          </FormElementsCommonFieldContainer>

          <v-divider class="my-5"></v-divider>

          <v-btn :disabled="!form" :loading="loading" color="primary" variant="tonal" class="px-6" type="submit"> {{
              attributeValue.id ? 'Update' : 'Add New'
            }} Attribute value
          </v-btn>
          <v-btn color="error" variant="tonal" class="px-6 ms-2" @click="resetForm"> Reset
          </v-btn>
        </v-form>
      </SharedUiParentCard>

    </v-col>
    <v-col md="8">
      <SharedGerenalTableSkeleton>
        <v-card>
          <v-card-text class="pa-2">


            <template #default>

              <div class="d-flex justify-space-between align-center">
                <h2>Attribute Values</h2>
                <div class="w-50">
                  <v-text-field :loading="store.searchLoading" append-inner-icon="mdi-magnify" density="compact"
                                label="Search category" variant="outlined" hide-details single-line
                                @onChange:append-inner="onClick"
                                v-model="options.search" clearable @click:clear="clearSearch"
                  ></v-text-field>
                </div>

              </div>

              <v-data-table-server
                  v-model:page="options.page"
                  v-model:items-per-page="options.itemsPerPage"
                  :headers="headers"
                  :items="attribute_values"
                  :items-length="store.pagination.itemCount"
                  :search="options.search"
                  :loading="loading"
                  item-value="name" @update:options="fetchAttributeValues">
                <template v-slot:thead>

                  <tr>
                    <td>
                      <v-text-field v-model="name" class="ma-1" density="compact" placeholder="Search name..."
                                    hide-details></v-text-field>
                    </td>

                  </tr>
                </template>
                <template v-slot:item.description="{ item }">
                  <span v-html="item?.description"></span>
                </template>
                <template v-slot:item.status="{ item }">
                  <v-chip :color="item?.isActive ? 'success' : 'error'" variant="elevated">
                    {{ item?.isActive ? 'Active' : 'Inactive' }}
                  </v-chip>
                </template>
                <template v-slot:item.action="{ item }">
                  <div class="text-end">

                    <v-menu>
                      <template v-slot:activator="{ props }">
                        <v-btn class="me-2" variant="tonal" size="small" icon="mdi-dots-vertical"
                               v-bind="props"></v-btn>
                      </template>

                      <v-list>
                        <v-list-item>
                          <v-btn icon color="warning" class="me-2" variant="tonal" size="small"
                                 @click="editPayloadItem(item, attributeValue)">
                            <PencilIcon size="20"/>
                          </v-btn>
                        </v-list-item>
                        <v-list-item>
                          <v-btn icon color="info" class="me-2" variant="tonal" size="small"
                                @click="localizeAttribute(item)">
                            <LanguageIcon size="20"/>
                          </v-btn>
                        </v-list-item>
                        <v-list-item>
                          <v-btn icon color='error' variant="tonal" size="small" @click="deleteItem( item.id )">
                            <TrashIcon size="20"/>
                          </v-btn>
                        </v-list-item>

                      </v-list>
                    </v-menu>

                  </div>
                </template>
              </v-data-table-server>
              <!-- <FormElementsPagination :pagination="store.pagination"
                @get-event="fetchAttributeValues('ASC', store.pagination.page)" /> -->
            </template>
          </v-card-text>
        </v-card>
      </SharedGerenalTableSkeleton>
    </v-col>
  </v-row>

  <ProductLocalizeAttributeLocalize ref="localizationDialog"/>
</template>
<script setup lang="ts">
/************Imports***************/
import {PencilIcon, TrashIcon, LanguageIcon} from "vue-tabler-icons";
import {computed, ref, watch} from 'vue';
import {useAttrValueStore} from "~/stores/products/attribute-values";
import {useProductAttributesStore} from "~/stores/products/attributes";
import {REQUIRED_RULE, SELECT_REQUIRED_RULE} from "~/utils/formRules";
import {editPayloadItem} from "~/utils/helpers/functions";

/************Variables***************/
const snackbar = useSnackbar();
const dialog = ref(false)
const form = ref(false);
const attributeValueForm = ref();
const loading = ref(false);
const theads = ['Name', 'Slug', 'Parent', 'actions'];
const attribute_values = ref([]);
const store = useAttrValueStore();
const attrStore = useProductAttributesStore();
const page = ref({title: "Attribute Values"});

const breadcrumbs = ref([
  {
    text: "Dashboard",
    disabled: false,
    to: "/dashboard",
  },
  {
    text: "Attribute value list",
    disabled: true,
    to: "",
  },
]);

const name = ref('');
const search = ref('');
const options = ref({
  page: 1,
  itemsPerPage: 20,
  sortBy: ['name'],
  sortDesc: [false],
  search: '',
});

const headers = ref([
  {title: 'Name', align: 'start', sortable: false, key: 'name'},
  {title: 'Slug', align: 'start', sortable: false, key: 'slug'},
  {title: 'Sort Order', align: 'start', sortable: false, key: 'sortOrder'},
  {title: 'Action', key: 'action', align: 'center', sortable: false},
])

const attributeValue = ref({
  id: null,
  name: '',
  slug: '',
  sortOrder: null,
  attributeId: null
})

function onClick() {
  if (!search) return;
  store.getAllAttributeValues();
}

const clearSearch = () => {
  fetchAttributeValues();
}
/************Methods***************/
const onSubmit = () => {
  
  if(!attributeValue.value.sortOrder){
    attributeValue.value.sortOrder = null;
  }

  store.addUpdateValue({
    id: attributeValue.value.id,
    name: attributeValue.value.name,
    slug: attributeValue.value.slug,
    sortOrder: attributeValue.value.sortOrder,
    attributeId: attributeValue.value.attributeId
  }).then(res => {
    snackbar.add({
      type: (res.success && res.data) ? 'success' : 'error',
      text: res.message
    })
  }).catch(err => {
    console.log(err)
  })

  loading.value = false
  resetForm()
}

const deleteItem = (id: number) => {

  store.deleteValue(id).then(res => {
    snackbar.add({
      type: res ? "success" : "error",
      text: res,
    })
  })

}

const resetForm = () => {
  attributeValue.value.id = null;
  attributeValue.value.name = '';
  attributeValue.value.slug = '';
  attributeValue.value.sortOrder = '';
  attributeValue.value.attributeId = null
}

/************Life Cycle Hooks***************/
const getAttributes = computed(() => {
  return attrStore.attributes
});

// const fetchAttributeValues = async(order = 'ASC', page = 1, take = 10) => {
//   await store.getValues({ order: order, page: page, take: take })
//   attribute_values.value = store.attrValues;
//   loading.value = false
// }
const fetchAttributeValues = async () => {

  const {page, itemsPerPage, sortBy, sortDesc, search} = options.value;
  const order = sortDesc[0] ? 'DESC' : 'ASC';
  const sortKey = sortBy[0] || 'name';
  loading.value = true;
  await store.getValues(order, page, itemsPerPage, sortKey, search);
  attribute_values.value = store.attrValues;
  loading.value = false;

}

watch(name, (val, oldValue) => {
  let arraydata = [];
  attribute_values.value.filter((item) => {
    if (item.name.toLowerCase().includes(val)) {
      arraydata.push(item);
    }
  })
  if (val.length === 0) {
    arraydata = store.attrValues;
  }
  attribute_values.value = arraydata;
})

onMounted(async () => {
  await attrStore.getAttributes({order: 'ASC', page: 1, take: 10});
});



const localizationDialog = ref(null);
const localizeAttribute = (item: any) => {
  localizationDialog.value?.show(item);
};
</script>
