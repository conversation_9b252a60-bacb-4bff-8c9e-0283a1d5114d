<template>
  <SharedBaseBreadcrumb
      :title="page.title"
      :breadcrumbs="breadcrumbs"
  ></SharedBaseBreadcrumb>
  <v-row>
    <v-col cols="4">
      <SharedUiParentCard :title="brand.id ? 'Edit ' + brand.name : 'Add New Brand'">
        <v-form v-model="form" @submit.prevent="onSubmit">

          <FormElementsImageViewerSingle :image-id="brand.imageGalleryId" :image-rules="imageRules" :max-size="100000"
                                         :required-height="450" :required-width="452" image-model="brand"
                                         :multiple="false"
                                         @selected-image-id="handleSelectedImage" :disableImageValidation="true"/>

          <FormElementsCommonFieldContainer label="Name" :required="true">
            <v-text-field v-model="brand.name" :rules="[val => REQUIRED_RULE(val)]"></v-text-field>
          </FormElementsCommonFieldContainer>

          <FormElementsCommonFieldContainer label="Description">
            <FormElementsEditor v-model="brand.description"/>
            <!--            <FormElementsTextEditor v-model="brand.description" />-->
          </FormElementsCommonFieldContainer>

          <v-divider class="my-5"></v-divider>

          <v-btn :disabled="!form" :loading="loading" color="primary" variant="tonal" class="px-6" type="submit"> {{
              brand.id ? 'Update' : 'Add New'
            }} Brand
          </v-btn>

          <v-btn color="error" variant="tonal" class="px-6 ms-2" @click="resetForm"> Reset
          </v-btn>

        </v-form>
      </SharedUiParentCard>
    </v-col>
    <v-col cols="8">

      <SharedUiParentCard>

        <div class="d-flex justify-space-between align-center">
          <h2>Brands</h2>
          <div class="w-50">
            <v-text-field :loading="store.searchLoading" append-inner-icon="mdi-magnify" density="compact"
                          label="Search category" variant="outlined" hide-details single-line
                          @onChange:append-inner="onClick"
                          v-model="options.search" clearable @click:clear="clearSearch"></v-text-field>
          </div>

        </div>

        <v-data-table-server
            v-model:page="options.page"
            v-model:items-per-page="options.itemsPerPage"
            :headers="headers"
            :items="brands"
            :items-length="store.pagination.itemCount"
            :search="options.search"
            :loading="loading"
            item-value="name" @update:options="fetchBrands">
          <template v-slot:thead>

            <tr>
              <td>
                <v-text-field v-model="name" class="ma-1" density="compact" placeholder="Search name..."
                              hide-details></v-text-field>
              </td>

            </tr>
          </template>
          <template v-slot:item.description="{ item }">
            <span v-html="item?.description"></span>
          </template>
          <template v-slot:item.imageGalleryId="{ item }">
            <div v-if="item.imageGallery">
              <v-img :src="item.imageGallery.imageUrl" width="80"/>
            </div>
          </template>
          <template v-slot:item.status="{ item }">
            <v-chip :color="item?.isActive ? 'success' : 'error'" variant="elevated">
              {{ item?.isActive ? 'Active' : 'Inactive' }}
            </v-chip>
          </template>
          <template v-slot:item.action="{ item }">
            <div class="text-end">

              <v-menu>
                <template v-slot:activator="{ props }">
                  <v-btn class="me-2" variant="tonal" size="small" icon="mdi-dots-vertical" v-bind="props"></v-btn>
                </template>

                <v-list>
                  <v-list-item>
                    <v-btn icon color="warning" class="me-2" variant="tonal" size="small"
                           @click="editBrand(item, brand)">
                      <PencilIcon size="20"/>
                    </v-btn>
                  </v-list-item>
                  <v-list-item>
                    <v-btn icon color='error' class="me-2" variant="tonal" size="small"  @click="confirmDialog = true; itemToDelete = item.id">
                      <TrashIcon size="20"/>
                    </v-btn>
                  </v-list-item>

                </v-list>
              </v-menu>

            </div>
          </template>
        </v-data-table-server>
      </SharedUiParentCard>
    </v-col>
    <ConformationModal v-model:dialog="confirmDialog" @confirm-delete="handleDeleteConfirmation" />

  </v-row>
</template>

<script setup lang="ts">

import {PencilIcon, TrashIcon} from "vue-tabler-icons";
import {ref, watch} from 'vue';
import {useProductBrandsStore} from "~/stores/products/brands";
import {editPayloadItem} from "~/utils/helpers/functions";
import {REQUIRED_RULE} from "~/utils/formRules";
import ConformationModal from "~/components/modals/ConformationModal.vue";

const store = useProductBrandsStore();
const snackbar = useSnackbar();

const page = ref({title: "Brand"});

const breadcrumbs = ref([
  {
    text: "Dashboard",
    disabled: false,
    to: "/dashboard",
  },
  {
    text: "Brand list",
    disabled: true,
    to: "",
  },
]);
const name = ref('');
const brands = ref([]);
const search = ref('');

const options = ref({
  page: 1,
  itemsPerPage: 20,
  sortBy: ['name'],
  sortDesc: [false],
  search: '',
});


const imageRules = [
  {text: 'Maximum image size is 200KB', icon: 'mdi-check'},
  {text: 'Image dimension is 452 X 450', icon: 'mdi-check'},
  {text: 'Image type should be WEBP', icon: 'mdi-check'},
]

const headers = ref([
  {title: 'Image', align: 'start', sortable: false, key: 'imageGalleryId'},
  {title: 'Name', align: 'start', sortable: false, key: 'name'},
  {title: 'Description', align: 'start', sortable: false, key: 'description'},
  {title: 'Status', key: 'status', sortable: false, align: 'start'},
  {title: 'Action', key: 'action', align: 'center', sortable: false},
])

function onClick() {
  if (!search) return;
  store.getBrands();
}

const clearSearch = () => {
  fetchBrands();
}

const fetchBrands = async () => {

  const {page, itemsPerPage, sortBy, sortDesc, search} = options.value;
  const order = sortDesc[0] ? 'DESC' : 'ASC';
  const sortKey = sortBy[0] || 'name';
  loading.value = true;
  await store.getBrands(order, page, itemsPerPage, sortKey, search);
  brands.value = store.brands;
  loading.value = false;

}

const form = ref(false);

const loading = ref(false);

const brand = ref({
  id: null,
  name: '',
  description: '',
  imageGalleryId: null
})

const onSubmit = () => {
  if (!form.value) {
    return;
  }

  loading.value = true

  store.addUpdateBrand(brand.value).then((res: any) => {
    snackbar.add({
      type: res.success ? 'success' : 'error',
      text: res.message
    })

    if (res.success) {
      resetForm();
    }

  }).catch(err => {
    console.log(err)
  })

  loading.value = false
}


const deleteItem = (id: number) => {
  store.deleteBrand(id).then(res => {
    snackbar.add({
      type: res ? "success" : "error",
      text: res,
    })
  })
}

const confirmDialog = ref(false);
const itemToDelete = ref<number | null>(null);
const handleDeleteConfirmation = async () => {
  if (itemToDelete.value !== null) {
    const productId = itemToDelete.value;
    // Call the delete product API
    const res = await store.deleteBrand(productId);
    // Handle response
    if (res) {
      snackbar.add({
        type: 'success',
        text: 'Brand deleted successfully!',
      });
      confirmDialog.value = false;

    } else {
      snackbar.add({
        type: 'error',
        text: 'Failed to delete brand.',
      });
    }
  }
};

const resetForm = () => {
  brand.value.id = null;
  brand.value.name = '';
  brand.value.description = '';
  brand.value.imageGalleryId = null
}

const handleSelectedImage = (value: any) => {
  brand.value.imageGalleryId = value
}

const editBrand = (item: any, brand: any) => {
  editPayloadItem(item, brand);
}


watch(name, (val, oldValue) => {
  let arraydata = [];
  brands.value.filter((item) => {
    if (item.name.toLowerCase().includes(val)) {
      arraydata.push(item);
    }

  })
  if (val.length === 0) {
    arraydata = store.brands;
  }
  brands.value = arraydata;
})

</script>
