<template>
  <v-row>
    <v-col md="4">
      <SharedUiParentCard :title="productTags.id ? 'Edit ' + productTags.tagName : 'Add New Value'">
        <v-form @submit.prevent="onSubmit" ref="productTagsForm" v-model="form">

          <FormElementsCommonFieldContainer label="Name" :required="true">
            <v-text-field v-model="productTags.tagName" :rules="[val => REQUIRED_RULE(val)]"></v-text-field>
          </FormElementsCommonFieldContainer>

          <v-divider class="my-5"></v-divider>

          <v-btn :disabled="!form" :loading="loading" color="primary" variant="tonal" class="px-6" type="submit"> {{
              productTags.id ? 'Update' : 'Add New'
            }} Tag
          </v-btn>
          <v-btn color="error" variant="tonal" class="px-6 ms-2" @click="resetForm"> Reset
          </v-btn>
        </v-form>
      </SharedUiParentCard>

    </v-col>
    <v-col md="8">
      <SharedGerenalTableSkeleton>
        <v-card>
          <v-card-text class="pa-2">


            <template #default>

              <div class="d-flex justify-space-between align-center">
                <h2>Product Tags</h2>
                <div class="w-50">
                  <v-text-field :loading="store.searchLoading" append-inner-icon="mdi-magnify" density="compact"
                                label="Search category" variant="outlined" hide-details single-line
                                @onChange:append-inner="onClick"
                                v-model="options.search" clearable @click:clear="clearSearch"
                  ></v-text-field>
                </div>

              </div>

              <v-data-table-server
                  v-model:page="options.page"
                  v-model:items-per-page="options.itemsPerPage"
                  :headers="headers"
                  :items="tag_values"
                  :items-length="store.pagination.itemCount"
                  :search="options.search"
                  :loading="loading"
                  item-value="name" @update:options="fetchProductTags">
                <template v-slot:thead>

                  <tr>
                    <td>
                      <v-text-field v-model="name" class="ma-1" density="compact" placeholder="Search name..."
                                    hide-details></v-text-field>
                    </td>

                  </tr>
                </template>
                <template v-slot:item.isActive="{ item }">
                  <v-chip :color="item?.isActive ? 'success' : 'error'" variant="elevated">
                    {{ item?.isActive ? 'Active' : 'Inactive' }}
                  </v-chip>
                </template>
                <template v-slot:item.action="{ item }">
                  <div class="text-end">

                    <v-menu>
                      <template v-slot:activator="{ props }">
                        <v-btn class="me-2" variant="tonal" size="small" icon="mdi-dots-vertical"
                               v-bind="props"></v-btn>
                      </template>

                      <v-list>
                        <v-list-item>
                          <v-btn icon color="warning" class="me-2" variant="tonal" size="small"
                                 @click="editPayloadItem(item, productTags)">
                            <PencilIcon size="20"/>
                          </v-btn>
                        </v-list-item>
                        <v-list-item>
                          <v-btn icon color="info" class="me-2" variant="tonal" size="small"
                                @click="localizeAttribute(item)">
                            <LanguageIcon size="20"/>
                          </v-btn>
                        </v-list-item>
                        <v-list-item>
                          <v-btn icon color='error' variant="tonal" size="small" @click="confirmDialog = true; itemToDelete = item.id">
                            <TrashIcon size="20"/>
                          </v-btn>
                        </v-list-item>

                      </v-list>
                    </v-menu>

                  </div>
                </template>
              </v-data-table-server>
              <!-- <FormElementsPagination :pagination="store.pagination"
                @get-event="fetchproductTagss('ASC', store.pagination.page)" /> -->
            </template>
          </v-card-text>
        </v-card>
      </SharedGerenalTableSkeleton>
    </v-col>
  </v-row>
  <ConformationModal v-model:dialog="confirmDialog" @confirm-delete="handleDeleteConfirmation" />
  <ProductLocalizeTagLocalize ref="localizationDialog"/>

</template>
<script setup lang="ts">
/************Imports***************/
import {PencilIcon, TrashIcon, LanguageIcon} from "vue-tabler-icons";
import {computed, onMounted, ref, watch} from 'vue';
import {useProductTagsStore} from "~/stores/products/product-tags";
// import {REQUIRED_RULE, SELECT_REQUIRED_RULE} from "~/utils/formRules";
import {editPayloadItem} from "~/utils/helpers/functions";
import ConformationModal from "~/components/modals/ConformationModal.vue";

/************Variables***************/
const snackbar = useSnackbar();
const dialog = ref(false)
const form = ref(false);
const productTagsForm = ref();
const loading = ref(false);
const theads = ['Name', 'Slug', 'Active', 'actions'];
const tag_values = ref([]);
const store = useProductTagsStore();
// const attrStore = useProductAttributesStore();
const itemsPerPage = ref(5)
const page = ref(1)
const order = ref('desc')
const name = ref('');
const search = ref('');
const options = ref({
  page: 1,
  itemsPerPage: 20,
  sortBy: ['name'],
  sortDesc: [false],
  search: '',
});

const headers = ref([
  {title: 'Name', align: 'start', sortable: false, key: 'tagName'},
  {title: 'Slug', align: 'start', sortable: false, key: 'slug'},
  {title: 'Status', align: 'start', sortable: false, key: 'isActive'},
  {title: 'Action', key: 'action', align: 'center', sortable: false},
])

const productTags = ref({
  id: null,
  tagName: '',
  slug: '',
})

function onClick() {
  if (!search) return;
  store.getAllProductTags();
}

const clearSearch = () => {
  fetchProductTags();

}
/************Methods***************/
const onSubmit = () => {
  store.addUpdateValue({
    id: productTags.value.id,
    tagName: productTags.value.tagName,
    slug: productTags.value.slug,
  }).then(res => {
    snackbar.add({
      type: (res.success && res.data) ? 'success' : 'error',
      text: res.message
    })
  }).catch(err => {
    console.log(err)
  })

  loading.value = false
  resetForm()
}

const deleteItem = (id: number) => {

  store.deleteValue(id).then(res => {
    snackbar.add({
      type: res ? "success" : "error",
      text: res,
    })
  })

}

const resetForm = () => {
  productTags.value.id = null;
  productTags.value.tagName = '';
  productTags.value.slug = '';
}


const fetchProductTags = async () => {

  const {page, itemsPerPage, sortBy, sortDesc, search} = options.value;
  const order = sortDesc[0] ? 'DESC' : 'ASC';
  const sortKey = sortBy[0] || 'name';
  loading.value = true;
  await store.getValues(order, page, itemsPerPage, sortKey, search);
  tag_values.value = store.productTags;
  loading.value = false;

}



const confirmDialog = ref(false);
const itemToDelete = ref<number | null>(null);
const handleDeleteConfirmation = async () => {
  if (itemToDelete.value !== null) {
    const productId = itemToDelete.value;
    // Call the delete product API
    const res = await store.deleteValue(productId);
    // Handle response
    if (res) {
      snackbar.add({
        type: 'success',
        text: 'Item deleted successfully!',
      });
      confirmDialog.value = false;

    } else {
      snackbar.add({
        type: 'error',
        text: 'Failed to delete Item.',
      });
    }
  }
};


watch(name, (val, oldValue) => {
  let arraydata = [];
  tag_values.value.filter((item) => {
    console.log('value', item);

    if (item.tagName.toLowerCase().includes(val)) {
      arraydata.push(item);
    }

  })
  if (val.length === 0) {
    arraydata = store.productTags;
  }
  tag_values.value = arraydata;
})


const localizationDialog = ref(null);
const localizeAttribute = (item: any) => {
  item.name = item.tagName;
  localizationDialog.value?.show(item);
};

</script>
