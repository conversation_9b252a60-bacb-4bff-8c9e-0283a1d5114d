<template>
  <SharedBaseBreadcrumb :title="page.title" :breadcrumbs="PRODUCT_ADD_EDIT_PAGE_BREADCRUMB"></SharedBaseBreadcrumb>
  <template v-if="!permissionStore?.hasModulePermission(`${DynamicModuleEnum.PRODUCT}:${DynamicPermissionEnum.READ_ONE}`)">
    <SharedUiParentCardSolid class="mt-6" title="Product Details">
      <h3 class="text-h3 text-error border border-dotted pa-5 text-center">You don't have permission to edit product</h3>
    </SharedUiParentCardSolid>
  </template>
  <template v-else>
    <template v-if="store.loading">
      <v-row>
        <v-col cols="8">
          <v-skeleton-loader type="card"></v-skeleton-loader>
          <v-skeleton-loader type="card"></v-skeleton-loader>
          <v-skeleton-loader type="card"></v-skeleton-loader>
          <v-skeleton-loader type="card"></v-skeleton-loader>
          <v-skeleton-loader type="card"></v-skeleton-loader>
        </v-col>
        <v-col cols="4">
          <v-skeleton-loader type="card"></v-skeleton-loader>
          <v-skeleton-loader type="card"></v-skeleton-loader>
          <v-skeleton-loader type="card"></v-skeleton-loader>
          <v-skeleton-loader type="card"></v-skeleton-loader>
        </v-col>
      </v-row>
    </template>
    <template v-else>

      <SharedUiParentCard class="mt-6" title="Product Details">
        <v-form v-model="form" @submit.prevent="onSubmit" ref="productForm">
          <v-row>
            <v-col cols="8">
              <FormElementsCommonTextFieldContainer v-model="product.name" label="Product Name" density="compact" hide-details/>
              <v-row class="mt-3">
                <v-col cols="6">
                  <!-- <v-col :cols="product.isMultiVariantProduct ? 12 : 6"> -->
                  <FormElementsCommonTextFieldContainer v-model="product.sku" label="SKU" density="compact" hide-details/>
                </v-col>
                <v-col cols="6">
                  <FormElementsCommonTextFieldContainer v-model="product.code" label="Code" density="compact" hide-details/>
                </v-col>
              </v-row>
              <v-row>
                <v-col cols="6">
                  <ProductCategory v-model="product.categoryId" density="compact" hide-details/>
                </v-col>
                <v-col cols="6">
                  <ProductBrands v-model="product.brandId" density="compact" hide-details/>
                </v-col>
              </v-row>
              <SharedUiParentCard class="mt-6" title="Product Description">
                <FormElementsRichTextEditor v-model="product.description"/>
              </SharedUiParentCard>
              <SharedUiParentCard class="mt-6" title="Product Material & Care">
                <FormElementsRichTextEditor v-model="product.materialCare"/>
              </SharedUiParentCard>
              <SharedUiParentCard class="mt-6" title="Product Shipping & Return">
                <FormElementsRichTextEditor v-model="product.shippingReturn"/>
              </SharedUiParentCard>
              <SharedUiParentCard class="mt-6" title="The WOW Factors">
                <FormElementsRichTextEditor v-model="product.wowFactors"/>
              </SharedUiParentCard>

            <v-row>
              <v-col cols="12">
                <SharedUiParentCard class="mt-6" title="Tagline">
                  <FormElementsCommonTextFieldContainer class="" v-model="product.tagLine1" label="Tagline 1"/>
                  <v-row>
                    <v-col md="5">
                      <v-select :items="tagLine2TypeList" item-title="name" item-value="name"
                                v-model="product.tagLine2Type" placeholder="Select Parent Category"></v-select>
                    </v-col>
                    <v-col md="7">
                      <FormElementsCommonTextFieldContainer v-model="product.tagLine2" label="Tagline 2"/>
                    </v-col>
                  </v-row>
                </SharedUiParentCard>
              </v-col>
            </v-row>
            <v-row v-if="product?.countryIds?.length > 0 && !product?.isMultiVariantProduct">
              <v-col>
                <SharedUiParentCard
                    v-for="price in product.productPrice"
                    :key="price"
                    :title="
                  countryStore.getCountry(parseInt(price?.countryId))[0]
                    ?.name + ' Product Price'
                "
                    class="mt-3"
                >
                  <!-- <SharedUiParentCard class="mt-6" title="Product Price">  -->
                  <v-row>
                    <v-col cols="4">
                      <v-text-field
                          label="Price"
                          v-model="price.unitPrice"
                          type="number"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="4">
                      <v-text-field
                          label="Discount Price"
                          v-model="price.discountPrice"
                          type="number"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="4">
                      <v-text-field
                          label="Quantity"
                          v-model="price.quantity"
                          type="number"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                </SharedUiParentCard>
              </v-col>
            </v-row>
          </v-col>
          
            <v-col cols="4">
              <SharedUiParentCardSolid title="Product Status">
                <v-switch
                    v-model="product.isPublish"
                    color="primary"
                    defaults-target="success"
                    :label="'Status ' + ((product.isPublish)? 'Published': 'Not Published')"
                    hide-details
                    inset
                    @update:model-value="onUpdatePublish"
                ></v-switch>
              </SharedUiParentCardSolid>
              <SharedUiParentCardSolid class="mt-6" title="Product featured image">

                <FormElementsImageViewerSingle
                    :image-id="product.featuredImageId"
                    :image-rules="PRODUCT_IMAGE_RULES"
                    :required-height="2250"
                    :required-width="1800"
                    :image-folders="[{ folderSlug: 'product_1800_2250', title: 'New', size: '1800x2250'}, {folderSlug:'product', title: 'Old', size: '1808x2000'}]"
                    image-model="product_1800_2250"
                    @selected-image-id="selectedFeaturedImage"
                />
              </SharedUiParentCardSolid>

              <SharedUiParentCardSolid class="mt-6" title="Product hover image">
                <FormElementsImageViewerSingle
                    :image-id="product.hoverImageId"
                    :image-rules="PRODUCT_IMAGE_RULES"
                    :required-height="2250"
                    :required-width="1800"
                    :image-folders="[{ folderSlug: 'product_1800_2250', title: 'New', size: '1800x2250'}, {folderSlug:'product', title: 'Old', size: '1808x2000'}]"
                    image-model="product_1800_2250"
                    @selected-image-id="selectedHoverImage"
                />
              </SharedUiParentCardSolid>

              <SharedUiParentCardSolid class="mt-6" title="Product gallery">
                <!-- :max-size="200000" -->
                <FormElementsImageViewer
                    :image-ids="product.imageGalleryIds"
                    :image-rules="PRODUCT_IMAGE_RULES"
                    :multiple="true"
                    :required-height="2250"
                    :required-width="1800"
                    :image-folders="[
                      {folderSlug: 'product_1800_2250', title: 'New', size: '1800x2250', mediaType: 'image'}, 
                      {folderSlug:'product', title: 'Old', size: '1808x2000', mediaType: 'image'},
                      {folderSlug:'PRODUCT_VIDEO', title: 'Video', size: '1800x2250', mediaType: 'video'}
                    ]"
                    image-model="product_1800_2250"
                    @selected-image-id="selectedGalleryImage"
                    @remove-image-id="removeGalleryImage"
                    @orderImageIds="orderGalleryImages"
                />
              </SharedUiParentCardSolid>


              <ProductSizeChart v-model="product.sizeChartId"/>

              <!-- <SharedUiParentCard class="mt-6" title="Product Size Chart">
                <FormElementsImageViewerSingle
                    :image-id="product.sizeChart"
                    image-model="SIZE_CHART"
                    :image-rules="PRODUCT_SIZE_CHART_RULES"
                    :max-size="200000"
                    :required-height="1300"
                    :required-width="1700"
                    :multiple="false"
                    @selected-image-id="selectedSizeChart"
                />
              </SharedUiParentCard> -->

              <SharedUiParentCard class="mt-6" title="Product Manage">

                <v-checkbox
                    v-model="product.isRefundable"
                    label="Is Refundable"
                    hide-details
                />
                <v-checkbox
                    v-model="product.isMultiVariantProduct"
                    label="Has Attributes?"
                    @update:model-value="onUpdateIsMultiVariantProduct"
                    hide-details
                ></v-checkbox>


                <h3>Just Arrived/ Best value </h3>
                <v-checkbox
                    v-model="product.bestValue"
                    label="Best Value"
                    hide-details
                />
                <v-checkbox
                    v-model="product.justArrived"
                    label="Just Arrived"
                    hide-details
                />
                <!-- <v-radio-group @update:modelValue="onChangeJustArrivedBestValue" inline v-model="selectedJustArrivedBestValue">
                  <v-radio label="Just Arrived" value="justArrived"></v-radio>
                  <v-radio label="Best Value" value="bestValue"></v-radio>
                </v-radio-group> -->

                <hr class="mb-3"/>
                <h3>Limited Edition / Customer Favorite</h3>

                <v-checkbox
                    v-model="product.limitedEdition"
                    label="Limited Edition"
                    hide-details
                />
                <v-checkbox
                    v-model="product.customerFavorite"
                    label="Customer Favorite"
                    hide-details
                />
                <!-- <v-radio-group @update:modelValue="onChangeLimitedFav" inline v-model="selectedLimitedFavorite">
                  <v-radio label="Limited Edition" value="limitedEdition"></v-radio>
                  <v-radio label="Customer Favorite" value="customerFavorite"></v-radio>
                </v-radio-group> -->
                <hr class="mb-3"/>
                <h3>Popular/ Special Offer</h3>

                <v-checkbox
                    v-model="product.mostPopular"
                    label="Most Popular"
                    hide-details
                />
                <v-checkbox
                    v-model="product.specialOffer"
                    label="Special Offer"
                    hide-details
                />
                <!-- <v-radio-group @update:modelValue="onChangePopularSpecial" inline v-model="selectedPopularSpecial">
                  <v-radio label="Popular" value="mostPopular"></v-radio>
                  <v-radio label="Special Offer" value="specialOffer"></v-radio>
                </v-radio-group> -->


                <!-- combo is temporary off  -->
                <!-- <v-checkbox
                    v-model="product.isComboPackProduct"
                    label="Has Combo Pack?"
                    @update:model-value="onUpdateIsMultiVariantProduct"
                    hide-details
                ></v-checkbox> -->

                <FormElementsCommonTextFieldContainer class="mt-6" type="number" v-model="product.sortOrder"
                                                      label="Set Order"/>

                <!-- <v-text-field
                            label="Set Order"
                            type="number"
                            hide-details
                            v-model="product.sortOrder"
                        ></v-text-field> -->

              </SharedUiParentCard>
              <SharedUiParentCard class="mt-6" title="Country">
                <v-autocomplete
                    v-model="product.countryIds"
                    label="Select Country"
                    multiple
                    :items="getCountries"
                    item-title="name"
                    item-value="id"
                    @update:model-value="onUpdateCountry"
                    chips closable-chips
                />

              </SharedUiParentCard>
            </v-col>
          </v-row>
          <v-row>
            <v-col>
              <v-btn color="primary" variant="tonal" class="px-12 py-6 mt-5 ml-auto d-flex" @click="saveProduct">Save
                Product
              </v-btn>
            </v-col>
          </v-row>
        </v-form>
      </SharedUiParentCard>

      <!-- Product Tags-->
      <SharedUiParentCard class="mt-6" title="Product Tags">
        <v-row>
          <v-col>
            <v-autocomplete :items="productTagsList" item-title="tagName" item-value="id" multiple chips
                            closable-chips
                            v-model="product.productTags" placeholder="Select Tags"/>

            <v-btn color="primary" variant="tonal" class="px-12 py-6 mt-5 ml-auto d-flex" @click="saveTags">Save Tags
            </v-btn>
          </v-col>
        </v-row>
      </SharedUiParentCard>

      <!-- Product Tags-->
      <SharedUiParentCard class="mt-5" v-if="product.isMultiVariantProduct">
        <v-row>
          <v-col>
            <v-form v-model="form" @submit.prevent="onSubmit" ref="productVariantForm">
              <ProductAttributes :selected-attributes="productVariant.selectedAttributes"
                                :generating="productVariant?.countryWiseProductVariant && productVariant?.countryWiseProductVariant?.length > 0 || !productVariant.countryWiseProductVariant[0]?.productVariations"
                                :reset="productVariant?.countryWiseProductVariant && productVariant?.countryWiseProductVariant?.length > 0 && productVariant.countryWiseProductVariant[0]?.productVariations"
                                @generate="createProductVariations"
                                @reset="resetProductVariations"
                                @selectedValues="getSelectedValues"
              />
              <SharedUiParentCard class="mt-5" title="Product Variations"
                                  v-if="productVariant.countryWiseProductVariant && productVariant.countryWiseProductVariant.length > 0">
                <SharedUiParentCard title="Color Images"
                                    v-if="productVariant.attributeImages && productVariant.attributeImages.length>0">

                  <v-expansion-panels>
                    <v-expansion-panel
                        :title=" getAttributeName(attrObject.attributeValueId)[0] ? getAttributeName(attrObject.attributeValueId)[0].name : ''"
                        v-for="(attrObject, index) in productVariant.attributeImages" :key="index">
                      <template #text>

                        <FormElementsImageViewer
                            :image-ids="productVariant.attributeImages[index].images"
                            :image-rules="PRODUCT_IMAGE_RULES"
                            :multiple="true"
                            :required-height="2250"
                            :required-width="1800"
                            :image-folders="[
                              {folderSlug: 'product_1800_2250', title: 'New', size: '1800x2250'},
                              {folderSlug:'product', title: 'Old', size: '1808x2000'},
                            ]"
                            image-model="product_1800_2250"
                            @selected-image-id="selectedColorImages($event, attrObject.attributeValueId)"
                            @remove-image-id="removeColorImages"
                            @orderImageIds="orderColorImages"
                            :componentExecutedObject="{id: attrObject?.attributeValueId, from: 'colorVariant'}"
                        />
                      </template>
                    </v-expansion-panel>
                  </v-expansion-panels>
                </SharedUiParentCard>
                <SharedUiParentCard title="">
                  <v-sheet color="" class="py-2 px-4 rounded">
                    <p class="text-body-2 font-weight-medium text-decoration-underline">Don't fill and apply below field if you don't want to set price, sku, quantity for <span class="text-h6">all country same</span></p>
                    <v-checkbox v-model="enableAllCountrySet" class="text-caption" density="compact" hide-details>
                      <template #label>
                        <p class="text-body-1 font-weight-medium">Want to fill all at a time now?</p>
                      </template>
                    </v-checkbox>
                    <!-- <table class="w-100">
                      <tbody>
                      <tr>
                        <td>
                          <v-text-field v-model="unitPriceForAllCountries" :disabled="!enableAllCountrySet" label="Price" type="number" density="compact" hide-details></v-text-field>
                          <v-btn :disabled="!enableAllCountrySet" color="primary" density="compact" class="text-caption mt-1" @click="ApplyValuesToAllCountryPriceField()">Apply to All</v-btn>
                        </td>
                        <td>
                          <v-text-field v-model="discountPriceForAllCountries" :disabled="!enableAllCountrySet" label="Promo Price" type="number" density="compact" hide-details></v-text-field>
                          <v-btn :disabled="!enableAllCountrySet" color="primary" density="compact" class="text-caption mt-1" @click="ApplyValuesToAllCountryDiscountPriceField()">Apply to All</v-btn>
                        </td>
                        <td>
                          <v-text-field v-model="quantityForAllCountries" :disabled="!enableAllCountrySet" label="Quantity" type="number" density="compact" hide-details></v-text-field>
                          <v-btn :disabled="!enableAllCountrySet" color="primary" density="compact" class="text-caption mt-1" @click="ApplyValuesToAllCountryQuantityField()" >Apply to All</v-btn>
                        </td>
                        <td>
                          <v-text-field v-model="skuForAllCountries"  :disabled="!enableAllCountrySet" label="SKU" hide-details density="compact"></v-text-field>
                          <v-btn :disabled="!enableAllCountrySet" color="primary" density="compact" class="text-caption mt-1" @click="ApplyValuesToAllCountrySKUField()">Apply to All</v-btn>
                        </td>
                        <td>
                        </td>
                      </tr>
                      </tbody>
                    </table> -->

                    <v-row class="mt-3">
                      <v-col v-for="(variantDetails, index) in generalPriceAndStockForEachVariantAllCountries" :key="'color_obj_'+index" cols="2" class="pa-1">
                        <v-sheet color="sidebarBg" class="pa-3 border rounded">
                          <p class="text-caption">
                            {{variantDetails?.color?.name}} (<span class="font-weight-bold text-h6">Size: {{variantDetails?.size?.name}}</span>)
                          </p>
                          <p class="mt-2">
                            <v-text-field v-model.trim="generalPriceAndStockForEachVariantAllCountries[index].sku" :disabled="!enableAllCountrySet" label="SKU" hide-details density="compact"></v-text-field>
                            <v-btn :disabled="!enableAllCountrySet" color="primary" density="compact" class="text-caption mt-1" @click="ApplyValuesToAllCountrySKUFieldForEachVariant(index, variantDetails?.size?.id, variantDetails?.color?.id)">Apply to All</v-btn>
                          </p>
                        </v-sheet>
                      </v-col>
                    </v-row>
                </v-sheet>
                <h3 class="text-h5 font-weight-bold mt-3">Price, SKU, Quantity, Status</h3>
                <v-expansion-panels variant="accordion">
                  <v-expansion-panel v-for="(country, index) in productVariant.countryWiseProductVariant" :key="country"
                                    :title="countryStore.getCountry(country.countryId)[0]?.name">
                    <template #text>
                      <v-row v-if="product.isComboPackProduct">
                        <v-col>
                          <SharedUiParentCard
                              title="Combo Pack"
                              class="mt-3"
                          >
                            <!-- <SharedUiParentCard class="mt-6" title="Product Price">  -->


                              <v-card class="border" elevation="0">
                                <v-card-text>
                                  <!-- <h3 class="mb-3">Price & Stock</h3> -->
                                  <v-table>
                                    <tbody>
                                    <tr>
                                      <td>
                                        <!-- v-model="product.brandId" -->
                                        <v-select
                                            :items="comboTypeList"
                                            item-title="name"
                                            item-value="value"
                                            placeholder="Select Combo Type"
                                        ></v-select>
                                      </td>
                                      <td>

                                        <v-text-field
                                            label="Price"
                                            type="number"
                                        ></v-text-field>
                                      </td>
                                      <td>
                                        <v-btn
                                            color="primary"
                                        >Add Combo
                                        </v-btn>
                                      </td>
                                    </tr>
                                    </tbody>
                                  </v-table>
                                </v-card-text>
                              </v-card>
                              <v-card class="border mt-3" elevation="0">
                                <v-table>
                                  <thead>
                                  <tr>
                                    <th class="text-h6 text-no-wrap">Combo Type</th>
                                    <th class="text-h6 text-no-wrap">Discount Amount</th>
                                  </tr>
                                  </thead>
                                  <tbody>
                                  <!-- <tr
                                      v-for="(comboType, index) in country.comboType"
                                      :key="country.id"
                                  > -->
                                  <tr>
                                    <td>
                                      single pack
                                    </td>
                                    <td>
                                      $0
                                    </td>
                                  </tr>
                                  <tr>
                                    <td>
                                      2 pack
                                    </td>
                                    <td>
                                      $50
                                    </td>
                                  </tr>
                                  </tbody>
                                </v-table>
                              </v-card>
                            </SharedUiParentCard>
                          </v-col>
                        </v-row>
                        <v-sheet class="border pa-5" elevation="0">
                          <v-switch
                              v-model="country.isActive"
                              color="primary"
                              defaults-target="success"
                              :label="'Status ' + ((country.isActive)? 'Active': 'Inactive')"
                              density="compact"
                              inset
                              hide-details
                              @update:model-value="onCountryWiseActiveInactive(country?.countryId, country.isActive)"
                          ></v-switch>
                          <v-sheet>
                            <h3>Price & Stock</h3>
                            <v-table density="compact" class="mt-3">
                              <tbody>
                              <tr>
                                <td>
                                  <v-text-field label="Price" type="number" density="compact" hide-details v-model="generalPriceAndStockByCountryId[index].unitPrice" class="py-2"></v-text-field>
                                  <v-btn color="primary" @click="ApplyValuesToCountryPriceField(country?.countryId)" density="compact" class="text-caption mt-1" >Apply to All</v-btn>
                                </td>
                                <td>
                                  <v-text-field label="Promo Price" type="number" density="compact" hide-details v-model="generalPriceAndStockByCountryId[index].discountPrice"></v-text-field>
                                  <v-btn color="primary" @click="ApplyValuesToCountryDiscountPriceField(country?.countryId)" density="compact" class="text-caption mt-1">Apply to All</v-btn>
                                </td>
                                <td>
                                  <v-text-field label="Quantity" type="number" density="compact" hide-details v-model="generalPriceAndStockByCountryId[index].quantity"></v-text-field>
                                  <v-btn color="primary" @click="ApplyValuesToCountryQuantityField(country?.countryId)" density="compact" class="text-caption mt-1">Apply to All</v-btn>
                                </td>
                                <td>
                                  <v-text-field label="SKU" density="compact" v-model.trim="generalPriceAndStockByCountryId[index].sku" hide-details ></v-text-field>
                                  <v-btn color="primary" @click="ApplyValuesToCountrySKUField(country?.countryId)" density="compact" class="text-caption mt-1">Apply to All</v-btn>
                                </td>
                                <td>
                                  <!-- <v-btn
                                      color="primary"
                                      @click="
                                  ApplyValuesToCountryFields(country?.countryId)
                                "
                                  >Apply to All
                                  </v-btn> -->
                                </td>
                              </tr>
                              </tbody>
                            </v-table>
                          </v-sheet>
                        </v-sheet>
                        <v-card class="border mt-3" elevation="0">
                          <v-card-text>
                            <template v-for="(variant, index2) in country.productVariations" :key="index2">
                              <SharedUiParentCard :title="'Size : ' + variant.size.name" class="mt-3">
                                <v-card class="border" elevation="0">
                                  <v-card-text>
                                    <v-table>
                                      <thead>
                                      <tr>
                                        <!-- <th class="text-h6 text-no-wrap">Color</th> -->
                                        <th v-if="variant?.variantDetails && variant?.variantDetails?.length>0 && variant?.variantDetails[0].color"
                                            class="text-h6 text-no-wrap">Color
                                        </th>
                                        <th class="text-h6 text-no-wrap">Price</th>
                                        <th class="text-h6 text-no-wrap">Discount Price</th>
                                        <th class="text-h6 text-no-wrap">Quantity</th>
                                        <th class="text-h6 text-no-wrap" style="width: 250px">SKU</th>
                                        <th class="text-h6 text-no-wrap">Status</th>
                                      </tr>
                                      </thead>
                                      <tbody>
                                      <template
                                          v-if="variant?.variantDetails && variant?.variantDetails?.length>0 && !variant?.variantDetails[0].color">
                                        <tr v-for="(detail, index3) in variant.variantDetails" :key="index3">
                                          <td>
                                            <v-text-field placeholder="Unit Price" type="number"
                                                          v-model="detail.unitPrice"></v-text-field>
                                          </td>
                                          <td>
                                            <v-text-field placeholder="Discount Price" type="number"
                                                          v-model="detail.discountPrice"></v-text-field>
                                          </td>
                                          <td>
                                            <v-text-field placeholder="Quantity" type="number"
                                                          v-model="detail.quantity"></v-text-field>
                                          </td>
                                          <td>
                                            <v-text-field placeholder="SKU" v-model.trim="detail.sku"></v-text-field>
                                          </td>
                                          <td>
                                            <v-switch
                                                color="primary"
                                                hide-details
                                                v-model="detail.isActive"
                                            ></v-switch>
                                          </td>
                                        </tr>
                                      </template>
                                      <template
                                          v-if="variant?.variantDetails && variant?.variantDetails?.length>0 && variant?.variantDetails[0].color">
                                        <tr v-for="(detail, index3) in variant.variantDetails" :key="index3">
                                          <td>
                                            {{ 'Color: ' + detail.color.name }}
                                          </td>
                                          <td>
                                            <v-text-field placeholder="Unit Price" type="number"
                                                          v-model="detail.unitPrice"></v-text-field>
                                          </td>
                                          <td>
                                            <v-text-field placeholder="Discount Price" type="number"
                                                          v-model="detail.discountPrice"></v-text-field>
                                          </td>
                                          <td>
                                            <v-text-field placeholder="Quantity" type="number"
                                                          v-model="detail.quantity"></v-text-field>
                                          </td>
                                          <td>
                                            <v-text-field placeholder="SKU" v-model.trim="detail.sku"></v-text-field>
                                          </td>
                                          <td>
                                            <v-switch
                                                color="primary"
                                                hide-details
                                                v-model="detail.isActive"
                                            ></v-switch>
                                          </td>
                                        </tr>
                                      </template>
                                      </tbody>
                                    </v-table>
                                  </v-card-text>
                                </v-card>
                              </SharedUiParentCard>
                            </template>
                          </v-card-text>

                          <!--                <v-table>-->
                          <!--                  <thead>-->
                          <!--                  <tr>-->
                          <!--                    <th class="text-h6 text-no-wrap">Variation</th>-->
                          <!--                    <th class="text-h6 text-no-wrap">Price</th>-->
                          <!--                    <th class="text-h6 text-no-wrap">Special Price</th>-->
                          <!--                    <th class="text-h6 text-no-wrap">Quantity</th>-->
                          <!--                    <th class="text-h6 text-no-wrap">Seller SKU</th>-->
                          <!--                    <th class="text-h6 text-no-wrap">Status</th>-->
                          <!--                  </tr>-->
                          <!--                  </thead>-->
                          <!--                  <tbody>-->
                          <!--                  <tr-->
                          <!--                      v-for="(variant, index) in country.productVariations"-->
                          <!--                      :key="country.id"-->
                          <!--                  >-->
                          <!--                    <td>-->
                          <!--                      -->
                          <!--                      <template-->
                          <!--                          v-for="(id, index) in variant.variantDetails"-->
                          <!--                          :key="id"-->
                          <!--                      >-->
                          <!--                        {{ getAttributeValueNameFromAttrId(id) }}-->
                          <!--                      </template>-->
                          <!--                    </td>-->
                          <!--                    <td>-->
                          <!--                      <v-text-field-->
                          <!--                          placeholder="Price"-->
                          <!--                          hide-details-->
                          <!--                          :model-value="variant.unitPrice"-->
                          <!--                          v-model="variant.unitPrice"-->
                          <!--                      ></v-text-field>-->
                          <!--                    </td>-->
                          <!--                    <td>-->
                          <!--                      <v-text-field-->
                          <!--                          placeholder="Special price"-->
                          <!--                          hide-details-->
                          <!--                          :model-value="0"-->
                          <!--                          v-model="variant.discountPrice"-->
                          <!--                      ></v-text-field>-->
                          <!--                    </td>-->
                          <!--                    <td>-->
                          <!--                      <v-text-field-->
                          <!--                          placeholder="Quantity"-->
                          <!--                          hide-details-->
                          <!--                          :model-value="0"-->
                          <!--                          v-model="variant.quantity"-->
                          <!--                      ></v-text-field>-->
                          <!--                    </td>-->
                          <!--                    <td>-->
                          <!--                      <v-text-field-->
                          <!--                          placeholder="Seller SKU"-->
                          <!--                          hide-details-->
                          <!--                          :model-value="variant.sku"-->
                          <!--                          v-model="variant.sku"-->
                          <!--                      ></v-text-field>-->
                          <!--                    </td>-->
                          <!--                    <td>-->
                          <!--                      <v-switch-->
                          <!--                          color="primary"-->
                          <!--                          hide-details-->
                          <!--                          :model-value="variant.isActive"-->
                          <!--                          v-model="variant.isActive"-->
                          <!--                      ></v-switch>-->
                          <!--                    </td>-->
                          <!--&lt;!&ndash;                  </tr>&ndash;&gt;-->
                          <!--                  </tbody>-->
                          <!--                </v-table>-->
                        </v-card>
                      </template>
                    </v-expansion-panel>
                  </v-expansion-panels>
                </SharedUiParentCard>
                <v-btn color="primary" variant="flat" class="px-12 py-6 mt-5 ml-auto d-flex" type="submit"
                      @click="saveVariant">
                  Save Variant
                </v-btn>
              </SharedUiParentCard>
            </v-form>
          </v-col>
        </v-row>
      </SharedUiParentCard>

      
      <!--  Meta Starts-->
      <!-- <LazyProductMeta @onSaveMeta="saveProductMeta" :meta-meta-title="meta.metaTitle" :meta-meta-description="meta.metaDescription" :meta-gallery-image-id="meta.galleryImageId" /> -->
      
      <LazyProductMeta @onSaveMeta="saveProductMeta"
                        :id="meta.id"
                        :meta-title="meta.metaTitle"
                        :metaDescription="meta.metaDescription"
                        :galleryImageId="meta.galleryImageId"
                        :meta-type="meta.metaType" />
      <!--    Meta Ends-->
      <v-row>
          <v-col v-if="product.id">
            <ProductLocalize :product-id="product.id" :locale-data="product.locale"/>
          </v-col>
        </v-row>


    </template>
  </template>
</template>

<script setup lang="ts">
// import {computed, onMounted, ref} from "vue";
import {useCountryStore} from "~/stores/others/country";
import {useAttrValueStore} from "~/stores/products/attribute-values";
import {useProductImagesStore} from "~/stores/products/product-images";
import {useProductsStore} from "~/stores/products";
import {PRODUCT_IMAGE_RULES, PRODUCT_SIZE_CHART_RULES} from "~/utils/imageRules";
import {PRODUCT_ADD_EDIT_PAGE_BREADCRUMB} from "~/utils/breadCrumbs";
import {useProductTagsStore} from "~/stores/products/product-tags";
import {homePageSettings} from "~/stores/settings/HomePage";
import ProductLocalize from "~/components/product/localize/ProductLocalize.vue";
import { useRolePermissionsStore } from "~/stores/administration/permissions";
import {DynamicModuleEnum, DynamicPermissionEnum} from "~/utils/roleManagement";

const countryStore = useCountryStore();
const attrValStore = useAttrValueStore();
const imageStore = useProductImagesStore();
const store = useProductsStore();
const permissionStore = useRolePermissionsStore();

const settingsStore = homePageSettings();

// theme breadcrumb
const page = ref({title: "Edit Product"});

const snackbar = useSnackbar();

const tagLine2TypeList = ref([
  {
    name: 'Special',
  },
  {
    name: 'Promotional',
  },
  {
    name: 'Fire',
  },
  {
    name: 'Low',
  },
]);
const comboTypeList = ref([
  {
    value: "1",
    name: 'Single PCS Pack',
  },
  {
    value: "2",
    name: '2 PCS Pack',
  },
  {
    value: "3",
    name: '3 PCS Pack',
  },
  {
    value: "4",
    name: '4 PCS Pack',
  },
  {
    value: "5",
    name: '5 PCS Pack',
  },
]);

// const product: Ref<MainProduct> = ref({});
const product = ref({
  id: null,
  isActive: true,
  isPublish: false,
  isMultiVariantProduct: false,
  isComboPackProduct: false,
  name: null,
  code: null,
  sku: null,
  description: null,
  materialCare: null,
  shippingReturn: null,
  wowFactors: null,
  slug: null,
  isRefundable: false,
  limitedEdition: false,
  justArrived: false,
  mostPopular: false,
  customerFavorite: false,
  bestValue: false,
  specialOffer: false,
  brandId: null,
  categoryId: [],
  countryIds: [],
  featuredImageId: null,
  hoverImageId: null,
  imageGalleryIds: [],
  productPrice: [],
  sortOrder: null,
  sizeChart: null,
  tagLine1: null,
  tagLine2: null,
  tagLine2Type: null,
  productTags: [],
  locale: undefined,
  sizeChartId: null,
});

const productVariant = reactive({
  productId: null,
  selectedAttributes: [],
  countryWiseProductVariant: [],
  attributeImages: [],
});

const generalPriceAndStockByCountryId: Ref<any[]> = ref([]);

const hasAttributesModelUpdate = () => {
  product.value.countryIds.forEach((item, index) => {
    generalPriceAndStockByCountryId.value.push({
      countryId: item,
      unitPrice: 0,
      discountPrice: 0,
      quantity: 0,
      sku: "PAN2022",
    });
  });
};

const form = ref(false);
const productVariantForm = ref();
const loading = ref(false);
const route = useRoute();

const onSubmit = () => {
  console.log(product.value);
};

const saveTags = () => {
  if(!permissionStore.hasModulePermission(`${DynamicModuleEnum.PRODUCT_TAGS}:${DynamicPermissionEnum.CREATE}`)){
    snackbar.add({
      type: "warning",
      text: "You don't have permission to save product tags",
    })
    return;
  }

  loading.value = true;
  if (!product.value.id && product.value?.productTags.length == 0) {
    return;
  }
  let payload: any = {};
  payload.productId = product.value.id
  payload.productTags = product.value.productTags;
  productTagStore?.tagWithProducts(payload)
      .then(async (res: any) => {
        snackbar.add({
          type: (res.success && res.data) ? "success" : "error",
          text: res.message,
        });
      })
      .catch((err: any) => {
        console.log(err);
      });

  loading.value = false;
};
const saveProduct = () => {
  if(!permissionStore.hasModulePermission(`${DynamicModuleEnum.PRODUCT}:${DynamicPermissionEnum.UPDATE}`)){
    snackbar.add({
      type: "warning",
      text: "You don't have permission to save product",
    })
    return;
  }

  loading.value = true;
  if (!product.value.sortOrder) {
    product.value.sortOrder = null;
  }
  store
      .addUpdateProduct(product.value)
      .then(async (res: any) => {
        productVariant.productId = res?.data?.id;
        snackbar.add({
          type: (res.success && res.data) ? "success" : "error",
          text: res.message,
        });
        // if (res.success) {

        //   await navigateTo({path: `/products/product`});
        // }
      })
      .catch((err: any) => {
        console.log(err);
      });

  loading.value = false;
};

const saveVariant = () => {
  loading.value = true;

  if(!permissionStore.hasModulePermission(`${DynamicModuleEnum.PRODUCT_META}:${DynamicPermissionEnum.CREATE}`)){
    snackbar.add({
      type: "warning",
      text: "You don't have permission to save product variant",
    })
    return;
  }
    

  store.addUpdateProductVariant(productVariant)
      .then((res: any) => {
        snackbar.add({
          type: res.success ? "success" : "error",
          text: res.message,
        });
      })
      .catch((err: any) => {
        console.log(err);
      });

  loading.value = false;
  // resetForm()
};

const onUpdateCountry = (countryIds: any) => {

  if (!product.value.isMultiVariantProduct) {

    product.value.countryIds.forEach((id: number) => {
      let priceObj: VariantPriceObject = {};
      priceObj.countryId = id;
      priceObj.unitPrice = 0;
      priceObj.discountPrice = 0;
      priceObj.quantity = 0;

      const index = product.value?.productPrice.findIndex((item: any) => item.countryId === id);

      if (index === -1) {
        product.value?.productPrice?.push(priceObj);
      }
    });

    product.value.productPrice = product.value?.productPrice.filter((item: any) => product.value.countryIds.includes(item?.countryId));
  }

  if (productVariant.selectedAttributes?.length > 0 && product.value.isMultiVariantProduct) {
    createProductVariations();
  }
};

const getSelectedValues = (val: any) => {
  productVariant.selectedAttributes = val;
}

const onUpdatePublish = () => {
  if(!permissionStore?.hasModulePermission(`${DynamicModuleEnum.PRODUCT}:${DynamicPermissionEnum.ACTIVE_DEACTIVE}`)){
    snackbar.add({
      type: "warning",
      text: "You don't have permission to change product status",
    })
    return;
  }

  if (product.value.id) {
    loading.value = true;

    store.publishProduct(product.value.id, product.value.isPublish)
        .then((res: any) => {
          if(product?.value?.isPublish){
            snackbar.add({
              type: "success",
              text: "Product published successfully",
            });            
          }else{
            snackbar.add({
              type: 'warning',
              text: "Product unpublished successfully",
            });   
          }
        })
        .catch((err: any) => {
          console.log(err);
        });

    loading.value = false;
  }

};

const onCountryWiseActiveInactive = (countryId: number, status: boolean) => {

  if(!permissionStore.hasModulePermission(`${DynamicModuleEnum.PRODUCT}:${DynamicPermissionEnum.ACTIVE_DEACTIVE}`)){
    snackbar.add({
      type: "warning",
      text: "You don't have permission to update variant status",
    })
    return;
  }

  if (product.value.id) {
    loading.value = true;
    store.onCountryWiseActiveInactive(product.value.id, countryId, status)
        .then((res: any) => {
          snackbar.add({
            type: res.success ? "success" : "error",
            text: res.message,
          });
        })
        .catch((err: any) => {
          console.log(err);
        });

    loading.value = false;
  }

};

const onUpdateIsMultiVariantProduct = (value: any) => {
  if (product.value.isMultiVariantProduct) {
    product.value.productPrice = [];
  } else {
    onUpdateCountry(product.value.countryIds);
  }
};

const getCountries = computed(() => countryStore.countries);

const createProductVariations = () => {

  hasAttributesModelUpdate();

  productVariant.selectedAttributes?.forEach((item: any) => {
    if (item.attributeId == 2) {
      if (productVariant?.attributeImages) {
        productVariant?.attributeImages?.forEach((attrImg, attrIndex) => {
          if (!item.values.includes(attrImg?.attributeValueId)) {
            productVariant?.attributeImages.splice(attrIndex, 1)
          }
        });
      }

      if (!productVariant.attributeImages) {
        productVariant.attributeImages = [];
      }
      item.values.forEach((id: any) => {

        const attributeImageObject: AttributeImageObject = {};
        attributeImageObject.attributeValueId = id;
        attributeImageObject.attributeValueName = getAttributeValueNameFromAttrId(id);
        attributeImageObject.images = [];

        const index = productVariant?.attributeImages?.findIndex(item => item?.attributeValueId === id)

        if (index === -1) {
          productVariant.attributeImages.push(attributeImageObject);
        }

      });
    }
  })

  productVariant.countryWiseProductVariant.forEach((country, countryIndex) => {
    if (!product.value.countryIds.includes(country?.countryId)) {
      productVariant.countryWiseProductVariant.splice(countryIndex, 1)
    }
  })

  product.value.countryIds.forEach((id, index) => {

    let variantList: any = [];
    const countryVariantSetNew: any = {};
    countryVariantSetNew.countryId = id;
    countryVariantSetNew.isActive = true;

    let isColorVariantAvailable = productVariant?.selectedAttributes?.some((obj: any) => obj.attributeId === 2 && (obj?.values && obj?.values.length > 0));
    productVariant.selectedAttributes?.forEach((item: any) => {
      if (item.attributeId == 1) {

        item.values.forEach((id: any) => {

          let variantObj: any = {};
          variantObj.size = {
            id: id,
            name: getAttributeName(id)[0]?.name
          }
          variantObj.variantDetails = [];

          if (!isColorVariantAvailable) {
            let variantDetails: any = {};
            variantDetails.color = null;
            variantDetails.unitPrice = 0;
            variantDetails.discountPrice = 0;
            variantDetails.quantity = 0;
            variantDetails.sku = "PAN2022";
            variantDetails.isActive = true;
            variantObj.variantDetails.push(variantDetails);
          }
          variantList.push(variantObj);

        });


        //new
        let existSizeIds: any = [];

        productVariant.countryWiseProductVariant.forEach((countryObj: any) => {
          countryObj?.productVariations.forEach((proVariants: any) => {
            existSizeIds.push(proVariants?.size?.id);
          });
        });

        const addedSizeVariant = item?.values.filter((item: any) => !existSizeIds.includes(item));
        const arr: any[] = item?.values;
        const removedSizeVariant = existSizeIds.filter((item: any) => !arr.includes(item));

        if (removedSizeVariant && removedSizeVariant?.length > 0) {

          productVariant.countryWiseProductVariant.forEach((countryObj: any) => {
            const index = countryObj?.productVariations.findIndex((country: any) => country.size?.id === removedSizeVariant[0]);
            countryObj?.productVariations.splice(index, 1);
          });
        }

        if (addedSizeVariant && addedSizeVariant?.length > 0) {
          addedSizeVariant.forEach((uId: number) => {

            productVariant.countryWiseProductVariant.forEach((countryObj: any) => {
              let variantObj: any = {};
              variantObj.size = {
                id: uId,
                name: getAttributeName(uId)[0]?.name
              }
              variantObj.variantDetails = [];

              if (!isColorVariantAvailable) {
                let variantDetails: any = {};
                variantDetails.color = null;
                variantDetails.unitPrice = 0;
                variantDetails.discountPrice = 0;
                variantDetails.quantity = 0;
                variantDetails.sku = "PAN2022";
                variantDetails.isActive = true;
                variantObj.variantDetails.push(variantDetails);
              } else {
                productVariant.selectedAttributes?.forEach((item: any) => {
                  if (item.attributeId == 2) {
                    item?.values.forEach((colorId: any) => {
                      let variantDetails: any = {};
                      let colorsObj: any = {};
                      colorsObj.id = colorId;
                      colorsObj.name = getAttributeValueNameFromAttrId(colorId);
                      variantDetails.color = colorsObj;
                      variantDetails.unitPrice = 0;
                      variantDetails.discountPrice = 0;
                      variantDetails.quantity = 0;
                      variantDetails.sku = "PAN2022";
                      variantDetails.isActive = true;
                      variantObj.variantDetails.push(variantDetails);
                    });
                  }
                });

              }
              countryObj?.productVariations.push(variantObj);
            });

          });
        }
        //new


      }
      if (item.attributeId == 2) {
        item.values.forEach((id: any) => {
          variantList?.forEach((v: any) => {
            let variantDetails: any = {};
            let colorsObj: any = {};
            colorsObj.id = id;
            colorsObj.name = getAttributeValueNameFromAttrId(id);
            variantDetails.color = colorsObj;
            variantDetails.unitPrice = 0;
            variantDetails.discountPrice = 0;
            variantDetails.quantity = 0;
            variantDetails.sku = "PAN2022";
            variantDetails.isActive = true;
            v.variantDetails.push(variantDetails);
          });
        });


        //new
        let existColorIds: any = [];

        if (productVariant?.countryWiseProductVariant[0]?.productVariations[0]?.variantDetails[0].color) {
          existColorIds = productVariant?.countryWiseProductVariant[0]?.productVariations[0]?.variantDetails.map(obj => obj.color?.id);

        }
        const addedColorVariant = item?.values.filter((item: any) => !existColorIds.includes(item));
        const arr: any[] = item?.values;
        const removedColorVariant = existColorIds.filter((item: any) => !arr.includes(item));
        if (removedColorVariant && removedColorVariant?.length > 0) {

          productVariant.countryWiseProductVariant.forEach((countryObj: any) => {
            countryObj?.productVariations.forEach((country: any) => {

              const index = country.variantDetails.findIndex((colorList: any) => colorList?.color?.id == removedColorVariant[0]);

              country.variantDetails.splice(index, 1);
            })
          });
        }

        if (addedColorVariant && addedColorVariant?.length > 0) {
          addedColorVariant.forEach((uId: number) => {

            productVariant.countryWiseProductVariant.forEach((countryObj: any) => {

              if (!isColorVariantAvailable) {
                let variantDetails: any = {};
                variantDetails.color = null;
                variantDetails.unitPrice = 0;
                variantDetails.discountPrice = 0;
                variantDetails.quantity = 0;
                variantDetails.sku = "PAN2022";
                variantDetails.isActive = true;
                countryObj?.productVariations.forEach((varList: any) => {
                  varList?.variantDetails.push(variantDetails)
                })
              } else {
                productVariant.selectedAttributes?.forEach((item: any) => {
                  if (item.attributeId == 2) {
                    let variantDetails: any = {};
                    let colorsObj: any = {};
                    colorsObj.id = uId;
                    colorsObj.name = getAttributeValueNameFromAttrId(uId);
                    variantDetails.color = colorsObj;
                    variantDetails.unitPrice = 0;
                    variantDetails.discountPrice = 0;
                    variantDetails.quantity = 0;
                    variantDetails.sku = "PAN2022";
                    variantDetails.isActive = true;
                    if (!countryObj?.productVariations[0]?.variantDetails[0]?.color) {
                      countryObj.productVariations = [];
                    }
                    countryObj?.productVariations.forEach((varList: any) => {
                      varList?.variantDetails.push(variantDetails)
                    });
                  }
                });

              }

            });

          });
        }
        //new


      }
    });

    countryVariantSetNew.productVariations = [];
    countryVariantSetNew.productVariations = variantList;

    const countryIndex = productVariant.countryWiseProductVariant.findIndex(country => country.countryId === id);

    if (countryIndex === -1) {
      productVariant.countryWiseProductVariant?.push(countryVariantSetNew);
    }
  });

};

const ApplyValuesToCountryFields = (id: number) => {

  const values: any = generalPriceAndStockByCountryId.value;
  productVariant?.countryWiseProductVariant?.forEach((item1, index1) => {
    if (item1.countryId === id) {
      item1.productVariations?.forEach((item2, index2) => {
        item2.variantDetails?.forEach((detail: any, index3) => {
          detail.unitPrice = parseFloat(values[index1].unitPrice);
          detail.discountPrice = parseFloat(values[index1].discountPrice);
          detail.quantity = parseInt(values[index1].quantity);
          detail.sku = values[index1].sku;
        });
      });
    }
  });
};

const unitPriceForAllCountries = ref(0);
const discountPriceForAllCountries = ref(0);
const quantityForAllCountries = ref(0);
const skuForAllCountries = ref("");
const enableAllCountrySet = ref(false);

const generalPriceAndStockForEachVariantAllCountries: Ref<any[]> = ref([]);

const productVariantsGeneralPriceAndStockUpdate = ()=>{
  productVariant?.countryWiseProductVariant[0]?.productVariations.forEach((variantObj, index1)=>{
    variantObj?.variantDetails.forEach((variantDetails, index2)=>{
      generalPriceAndStockForEachVariantAllCountries.value.push({
        size: {
          ...variantObj?.size,
        },
        color: {
          ...variantDetails?.color,
        },
        unitPrice: 0,
        discountPrice: 0,
        quantity: 0,
        sku: ""
      })
    })
  })

  // sort by color first with sizes
  generalPriceAndStockForEachVariantAllCountries.value.sort((a, b) => {
    if (a.color.id !== b.color.id) {
      return a.color.id - b.color.id; // Sort by color ID first (ascending)
    }
    return a.size.id - b.size.id; // If same color ID, sort by size ID (ascending)
  });
}


const ApplyValuesToCountryPriceField = (id: number) => {
  // This function sets *price for single/one individual countries *price field!
  const values: any = generalPriceAndStockByCountryId.value;
  productVariant?.countryWiseProductVariant?.forEach((item1, index1) => {
    if (item1.countryId === id) {
      item1.productVariations?.forEach((item2, index2) => {
        item2.variantDetails?.forEach((detail: any, index3) => {
          detail.unitPrice = parseFloat(values[index1].unitPrice);
        });
      });
    }
  });
};

const ApplyValuesToAllCountryPriceField = () => {
  // This function sets *price for all countries *price field!
  productVariant?.countryWiseProductVariant?.forEach((item1, index1) => {
    item1.productVariations?.forEach((item2, index2) => {
      item2.variantDetails?.forEach((detail: any, index3) => {
        detail.unitPrice = parseFloat(unitPriceForAllCountries?.value);
      });
    });
  });
};

const ApplyValuesToCountryDiscountPriceField = (id: number) => {
  // This function sets *discount price for single/one individual countries *discount price field!
  const values: any = generalPriceAndStockByCountryId.value;
  productVariant?.countryWiseProductVariant?.forEach((item1, index1) => {
    if (item1.countryId === id) {
      item1.productVariations?.forEach((item2, index2) => {
        item2.variantDetails?.forEach((detail: any, index3) => {
          detail.discountPrice = parseFloat(values[index1].discountPrice);
        });
      });
    }
  });
};

const ApplyValuesToAllCountryDiscountPriceField = () => {
  // This function sets *discount price for all countries *discount price field!
  productVariant?.countryWiseProductVariant?.forEach((item1, index1) => {
    item1.productVariations?.forEach((item2, index2) => {
      item2.variantDetails?.forEach((detail: any, index3) => {
        detail.discountPrice = parseFloat(discountPriceForAllCountries.value);
      });
    });
  });
};


const ApplyValuesToCountryQuantityField = (id: number) => {
  // This function sets *quantity for single/one individual countries *quantity field!
  const values: any = generalPriceAndStockByCountryId.value;
  productVariant?.countryWiseProductVariant?.forEach((item1, index1) => {
    if (item1.countryId === id) {
      item1.productVariations?.forEach((item2, index2) => {
        item2.variantDetails?.forEach((detail: any, index3) => {
          detail.quantity = parseInt(values[index1].quantity);
        });
      });
    }
  });
};

const ApplyValuesToAllCountryQuantityField = () => {
  // This function sets *quantity for all countries *quantity field!
  productVariant?.countryWiseProductVariant?.forEach((item1, index1) => {
    item1.productVariations?.forEach((item2, index2) => {
      item2.variantDetails?.forEach((detail: any, index3) => {
        detail.quantity = parseInt(quantityForAllCountries.value);
      });
    });
  });
};

const ApplyValuesToCountrySKUField = (id: number) => {
  // This function sets *sku for single/one individual countries *sku field!
  const values: any = generalPriceAndStockByCountryId.value;
  productVariant?.countryWiseProductVariant?.forEach((item1, index1) => {
    if (item1.countryId === id) {
      item1.productVariations?.forEach((item2, index2) => {
        item2.variantDetails?.forEach((detail: any, index3) => {
          detail.sku = values[index1].sku;
        });
      });
    }
  });
};

const ApplyValuesToAllCountrySKUField = () => {
  // This function sets *sku for all countries *sku field!
  productVariant?.countryWiseProductVariant?.forEach((item1, index1) => {
    item1.productVariations?.forEach((item2, index2) => {
      item2.variantDetails?.forEach((detail: any, index3) => {
        detail.sku = skuForAllCountries.value;
      });
    });
  });
};

const ApplyValuesToAllCountrySKUFieldForEachVariant = (clickedIndex:number, sizeId:number, colorId: number) => {
  // This function sets *sku for all countries *sku  field!
  const values: any = generalPriceAndStockForEachVariantAllCountries.value;
  productVariant?.countryWiseProductVariant?.forEach((item1, index1) => {
    item1.productVariations?.forEach((item2, index2) => {
      if(item2?.size?.id === sizeId){
        item2.variantDetails?.forEach((detail: any, index3) => {
          if(detail?.color?.id === colorId){
            if(values[clickedIndex]?.sku?.length > 0){
              detail.sku = values[clickedIndex].sku;
              // values[clickedIndex].sku = "";
              snackbar.add({
                type: "success",
                text: `SKU set for ${item2?.size?.name}-${detail?.color?.name}!. Don't forget to save variant!`,
              });
            }
          }
        });
      }
    });
  });
};

const getAttributeName = (id: number) => attrValStore.attrValues.filter((item: any) => item.id === id);

const getAttributeValueNameFromAttrId = (id: number) => getAttributeName(id)[0] ? getAttributeName(id)[0].name : "";

const selectedFeaturedImage = (val: any) => {
  product.value.featuredImageId = val;
}

const selectedHoverImage = (val: any) => {
  product.value.hoverImageId = val;
}

const selectedGalleryImage = (val: any) => {
  if(val){
    product.value.imageGalleryIds = [...val, ...product.value.imageGalleryIds];
    product.value.imageGalleryIds = product.value.imageGalleryIds.filter(el=> el!=null); // to keep safe from null

  }
}

const removeGalleryImage = (val: any) => {
  product.value.imageGalleryIds = product.value.imageGalleryIds.filter((id:any)=> id !== val)
}

const orderGalleryImages = (imageList: any, executedObject: any) => {
  if(imageList && imageList?.length > 0){
    product.value.imageGalleryIds = imageList.map((el: any) => el.id);
  }
}

const selectedSizeChart = (val: any) => {
  product.value.sizeChart = val
}

const selectedColorImages = (val: any, id: any) => {
  const elementPosition = productVariant.attributeImages?.map((x: any) => x.attributeValueId).indexOf(parseInt(id));
  if (elementPosition !== -1) {
    const filteredVal = val.filter((image: any) => image !== null && image !== undefined);
    productVariant.attributeImages[elementPosition].images = [
      ...filteredVal,
      ...productVariant.attributeImages[elementPosition].images,
    ];
  }
}

const removeColorImages = (val: any) => {
  const elementPosition = productVariant.attributeImages?.findIndex((x: any) => x.images.includes(parseInt(val)));
  productVariant.attributeImages[elementPosition].images = productVariant.attributeImages[elementPosition].images.filter((id:any)=> id !== val)
}

const orderColorImages = (imageList: any, executedObject: any) => {
  if(imageList && imageList?.length > 0){
    const variantIndex = executedObject?.id;
    const elementPosition = productVariant.attributeImages?.findIndex((x: any) => x?.attributeValueId === parseInt(variantIndex));
    productVariant.attributeImages[elementPosition].images = imageList.map((el: any) => el.id);
  }
}


const resetProductVariations = () => {
  productVariant.countryWiseProductVariant = [];
  productVariant.selectedAttributes = [];
  productVariant.attributeImages = [];
}


const productTagStore = useProductTagsStore();
const productTagsList = computed(() => productTagStore.productTags);

const saveProductMeta = async( val: any ) => {

  loading.value = true;
  if (!product.value.id && val?.metaTitle && val?.metaDescription) {
    return;
  }
  let payload: any = {};
  payload.id = val?.id;
  payload.metaTitle = val?.metaTitle;
  payload.metaDescription = val?.metaDescription;
  payload.galleryImageId = val.galleryImageId;
  payload.metaType= 'product';
  payload.referenceId = product.value?.id;
  store?.saveSeoMetaData(payload)
      .then(async (res: any) => {
        snackbar.add({
          type: (res.success && res.data) ? "success" : "error",
          text: res.message,
        });
      })
      .catch((err: any) => {
        console.log(err);
      });

  loading.value = false;
}

const fetchSeoMeta = async () => {
  const response = await store.getSeoMetaData('product', product.value.id);
  console.log('response', response);
  
  if( response?.isSuccess ) {
    meta.id = response?.data?.id;
    meta.metaTitle = response?.data?.metaTitle;
    meta.metaDescription = response?.data?.metaDescription;
    meta.galleryImageId = response?.data?.galleryImageId;
  }
};

const meta = reactive({
  id: 0,
  metaTitle : null,
  metaDescription: null,
  galleryImageId: 0
})

onMounted(async () => {
  if(!permissionStore?.hasModulePermission(`${DynamicModuleEnum.PRODUCT}:${DynamicPermissionEnum.READ_ONE}`)){
    snackbar.add({
      type: "warning",
      text: "You don't have permission to see product detail",
    })
    return;
  }

  await countryStore.getAllCountries();

  store.getProduct(parseInt(route?.params?.id.toString())).then((res: any) => {

    product.value = res.item?.product;
    product.value.productTags = res.item?.productTags;
    createProductVariations();
    const productVariantResponse = res?.item?.productVariant;

    productVariant.productId = res?.item?.product?.id;
    productVariant.selectedAttributes = productVariantResponse?.selectedAttributes;
    productVariant.countryWiseProductVariant = productVariantResponse?.countryWiseProductVariant;
    productVariant.attributeImages = productVariantResponse?.attributeImages;

    if (product.value.featuredImageId) {
      imageStore.getImage(product?.value?.featuredImageId, 'featured')
    }

    if (product.value.hoverImageId) {
      imageStore.getImage(product?.value?.hoverImageId, 'hover')
    }
    product.value.imageGalleryIds = product.value.imageGalleryIds.filter(el=> el!=null); // to keep safe from null

    if (product.value.imageGalleryIds && product.value.imageGalleryIds[0]) {
      imageStore.getProductGalleryImages(product.value.imageGalleryIds)
    }

    if (product.value.id) {
      fetchSeoMeta();
    }

    store.loading = false;
    productVariantsGeneralPriceAndStockUpdate();
  });

  await productTagStore.getAllProductTags()


});

</script>
