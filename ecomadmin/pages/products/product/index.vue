<template>
  <SharedBaseBreadcrumb
    :title="page.title"
    :breadcrumbs="breadcrumbs"
  ></SharedBaseBreadcrumb>
  <v-row>
    <v-col cols="12" md="12">
      <SharedUiParentCardSolid>
        <div class="w-100 d-flex justify-space-between align-center">
          <v-row>
            <v-col cols="12" sm="3">
              <v-select
                append-inner-icon="mdi-list"
                density="compact"
                label="Search ..."
                variant="outlined"
                hide-details
                :items="categoryStore?.categories"
                item-title="name"
                item-value="id"
                multiple
                v-model="options.categoryIds"
                clearable
              ></v-select>
            </v-col>
            <v-col cols="12" sm="3">
              <v-text-field
                append-inner-icon="mdi-magnify"
                density="compact"
                label="Search ..."
                variant="outlined"
                hide-details
                single-line
                @onChange:append-inner="onClick"
                v-model="options.search"
                clearable
                @click:clear="clearSearch"
              ></v-text-field>
            </v-col>
            <v-col cols="12" sm="3">
              <v-menu :close-on-content-click="false" v-model="dateMenu" offset-y transition="scale-transition">
                <template #activator="{ props }">
                  <v-text-field
                    v-model="options.startDate"
                    v-bind="props"
                    label="Start Date"
                    prepend-inner-icon="mdi-calendar"
                    density="compact"
                    readonly
                  ></v-text-field>
                </template>

                <v-card>
                  <v-date-picker
                    v-model="selectedStartDate"
                    no-title
                    show-adjacent-months
                    border
                    :elevation="4"
                    :max="new Date()"
                  >
                    <template #title class="d-none">
                    </template>
                    <template #header class="d-none">
                    </template>
                    <template #actions>
                      <v-btn @click="changeStartDate" color="primary" variant="tonal">
                        submit
                      </v-btn>
                      <v-btn @click="dateMenu=false">
                        close
                      </v-btn>
                    </template>
                  </v-date-picker> 
                </v-card>
              </v-menu>
            </v-col>
            <v-col cols="3">
              <v-menu :close-on-content-click="false" v-model="dateEndMenu" offset-y transition="scale-transition">
                <template #activator="{ props }">
                  <v-text-field
                    v-model="options.endDate"
                    v-bind="props"
                    label="End Date"
                    prepend-inner-icon="mdi-calendar"
                    density="compact"
                    readonly
                  ></v-text-field>
                </template>

                <v-card>
                  <v-date-picker
                    v-model="selectedEndDate"
                    no-title
                    show-adjacent-months
                    border
                    :elevation="4"
                    :max="new Date()"
                  >
                    <template #title class="d-none">
                    </template>
                    <template #header class="d-none">
                    </template>
                    <template #actions>
                      <v-btn @click="changeEndDate" color="primary" variant="tonal">
                        submit
                      </v-btn>
                      <v-btn @click="dateEndMenu=false">
                        close
                      </v-btn>
                    </template>
                  </v-date-picker> 
                </v-card>
              </v-menu>
            </v-col>
            <v-col cols="3">
              <v-btn color="success" @click="downloadProductExcel">Export CSV</v-btn>
              <v-btn @click="resetSelection" class="ms-2 text-caption" color="error" flat>Reset Selection</v-btn>
            </v-col>
          </v-row>
        </div>
      </SharedUiParentCardSolid>

      <!-- Bulk action -->
      <v-menu v-if="!!selected.length">
        <template v-slot:activator="{ props }">
          <v-btn color="primary" v-bind="props">
            Bulk action ({{ selected.length }})
          </v-btn>
        </template>
        <v-list>
          <v-list-item
            v-for="(item, index) in bulkActions"
            :key="index"
            :value="index"
          >
            <v-list-item-title @click="bunkActionHandler(item.action)">{{
              item.title
            }}</v-list-item-title>
          </v-list-item>
        </v-list>
      </v-menu>
      <!-- Bulk action end -->

      <v-data-table-server
        v-model:page="options.page"
        v-model:items-per-page="options.itemsPerPage"
        :headers="headers"
        :items="products"
        :items-length="store?.pagination?.itemCount"
        :search="options?.search"
        :name-search="options?.nameSearch"
        :sku-search="options?.skuSearch"
        :loading="loading"
        :options.sync="options"
        v-model="selected"
        show-select
        item-value="id"
        @update:options="fetchProductList"
        class="mt-4 border-sm border-opacity-100"
      >
        <template v-slot:thead>
          <tr>
            <td colspan="4">
              <v-text-field
                append-inner-icon="mdi-magnify"
                density="compact"
                class="ma-3"
                label="Search sku..."
                variant="outlined"
                hide-details
                single-line
                @input="onClickSkuSearch"
                v-model="options.skuSearch"
                clearable
                @click:clear="clearSkuSearch"
              ></v-text-field>
            </td>
          </tr>
        </template>
        <template v-slot:item.image="{ item }">
          <div style="padding: 5px">
            <v-img
              :src="item?.featuredImage?.imageGalleryUrls?.thump"
              width="50"
            />
          </div>
        </template>
        <template v-slot:item.brand="{ item }">
          <v-chip variant="tonal" label size="x-small">
            {{ item?.brand?.name }}
          </v-chip>
        </template>

        <template v-slot:item.category="{ item }">
          {{ item?.categories?.map((c) => c.name)?.join(", ") }}
        </template>

        <template v-slot:item.name="{ item }">
          <p style="width: 250px; text-wrap: wrap">{{ item?.name }}</p>
        </template>

        <template v-slot:item.sku="{ item }">
          {{ item?.sku }}
        </template>
        <template v-slot:item.status="{ item }">
          <v-chip
            size="x-small"
            :color="item.isPublish ? 'success' : 'error'"
            variant="elevated"
          >
            {{ item.isPublish ? "Published" : "Not Published" }}
          </v-chip>
        </template>

        <template v-slot:item.bundle="{ item }">
          <v-btn
            size="small"
            variant="text"
            color="success"
            v-if="item?.suggestions?.length > 0"
            @click="bundleHandle(item)"
          >
            <SquareCheckIcon />
          </v-btn>
          <v-btn
            size="small"
            variant="text"
            color="primary"
            v-else
            @click="bundleHandle(item)"
          >
            <SquarePlusIcon />
          </v-btn>
        </template>
        <template v-slot:item.related-tag="{ item }">
          <v-btn
            size="small"
            variant="text"
            color="success"
            v-if="item?.productRelated?.length > 0"
            @click="bundleRelatedTagHandle(item)"
          >
            <SquareCheckIcon />
          </v-btn>
          <v-btn
            size="small"
            variant="text"
            color="primary"
            v-else
            @click="bundleRelatedTagHandle(item)"
          >
            <SquarePlusIcon />
          </v-btn>
        </template>
        <template v-slot:item.action="{ item }">
          <div class="text-end">
            <v-menu>
              <template v-slot:activator="{ props }">
                <v-btn
                  class="me-2"
                  variant="tonal"
                  size="small"
                  icon="mdi-dots-vertical"
                  v-bind="props"
                ></v-btn>
              </template>

              <v-list>
                <v-list-item>
                  <!-- <a
                    :href="`/products/product/${item?.id}`"
                    class="v-btn v-btn&#45;&#45;icon v-btn&#45;&#45;text v-btn&#45;&#45;small v-btn&#45;&#45;tonal v-btn&#45;&#45;error me-1"
                    style="color: orange; font-size: small"
                    :title="`Edit ${item?.name}`"
                    @click.prevent="editProduct(item?.id)"
                  >
                    <PencilIcon size="20" />
                  </a> -->
                  <v-btn
                    icon
                    color="error"
                    variant="tonal"
                    size="x-small"
                    :to="`/products/product/${item?.id}`"
                    title="Edit"
                  >
                    <PencilIcon size="20" />
                  </v-btn>
                </v-list-item>
                <v-list-item>
                  <v-btn
                    icon
                    color="error"
                    variant="tonal"
                    size="x-small"
                    :href="`https://pantoneclo.com.bd/product/${item?.slug}`"
                    target="_blank"
                    title="View Live Url"
                  >
                    <EyeIcon size="20" />
                  </v-btn>
                </v-list-item>
                <v-list-item>
                  <v-btn
                    icon
                    color="error"
                    variant="tonal"
                    size="x-small"
                    class="ms-1"
                    @click="
                      confirmCopy = true;
                      itemToCopy = item.id;
                    "
                  >
                    <CopyIcon size="20" />
                  </v-btn>
                </v-list-item>

                <v-list-item>
                  <v-btn
                    icon
                    color="error"
                    variant="tonal"
                    size="x-small"
                    class="ms-1"
                    @click="
                      confirmDialog = true;
                      itemToDelete = item.id;
                    "
                  >
                    <TrashIcon size="20" />
                  </v-btn>
                </v-list-item>
              </v-list>
            </v-menu>
          </div>
        </template>
      </v-data-table-server>
    </v-col>
  </v-row>
  <ConformationModal
    v-model:dialog="confirmDialog"
    @confirm-delete="handleDeleteConfirmation"
  />
  <ConformationModal
    v-model:dialog="confirmCopy"
    @confirm-delete="handleCopyConfirmation"
    message="Are you sure to copy?"
    confirm-button="Yes"
  />
  <FeedTransfer ref="feedTransferRef" @reset="resetSelection" />
  <CollectionTransfer ref="collectionTransferRef" @reset="resetSelection" />
  <BundleProductForm ref="bundleProductFormRef" @reset="fetchProductList" />
  <BundleProductRelatedTagForm ref="bundleProductRelatedTagFormRef" @reset="fetchProductList" />
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import {
  EyeIcon,
  PencilIcon,
  TrashIcon,
  SquareCheckIcon,
  SquarePlusIcon,
  CopyIcon,
} from "vue-tabler-icons";
import { useProductsStore } from "~/stores/products";
import ConformationModal from "~/components/modals/ConformationModal.vue";
import FeedTransfer from "~/components/product/feed/FeedTransfer.vue";
import CollectionTransfer from "~/components/product/collection/CollectionTransfer.vue";
import BundleProductForm from "~/components/product/bundle/BundleProductForm.vue";
import BundleProductRelatedTagForm from "~/components/product/bundle/BundleProductRelatedTagForm.vue";
import { buildParams } from "~/utils/helpers/functions";
import { useReports } from "~/stores/reports";
import {DynamicModuleEnum, DynamicPermissionEnum} from "~/utils/roleManagement"
import { useRolePermissionsStore } from "~/stores/administration/permissions";
import { useProductCategoriesStore } from "~/stores/products/categories";

const route = useRoute();
const router = useRouter();
const snackbar = useSnackbar();
const store = useProductsStore();
const reportStore = useReports();
const permissionStore = useRolePermissionsStore();
const page = ref({ title: "Products" });
const categoryStore = useProductCategoriesStore();

const breadcrumbs = ref([
  {
    text: "Dashboard",
    disabled: false,
    to: "/dashboard",
  },
  {
    text: "Product list",
    disabled: true,
    to: "",
  },
]);

const confirmCopy = ref(false);
const confirmDialog = ref(false);
const options = ref({
  page: route?.query?.page ?? 1,
  itemsPerPage: 20,
  sortBy: ["id"],
  sortDesc: [false],
  search: "",
  nameSearch: "",
  skuSearch: "",
  startDate: "",
  endDate: "",
  categoryIds: []
});
const loading = ref(false);
const products = ref([]);

const selected = ref<number[]>([]);
const bulkActions = [
  { title: "Send to feed", action: "SEND_TO_FEED" },
  { title: "Transfer to Collection", action: "SEND_TO_COLLECTION" },
];

const resetSelection = () => {
  selected.value = [];
};

const bundleProductFormRef = ref(null);
const bundleProductRelatedTagFormRef = ref(null);
const feedTransferRef = ref(null);
const collectionTransferRef = ref(null);
const bunkActionHandler = (action: string) => {
  if (action === "SEND_TO_FEED") {
    feedTransferRef.value?.show(selected.value);
  } else if (action === "SEND_TO_COLLECTION") {
    collectionTransferRef.value?.show(selected.value);
  }
};

const dateMenu= ref(false);
const dateEndMenu= ref(false);
const dateRange= ref([]);
const selectedStartDate= ref(null);
const selectedEndDate= ref(null);
const selectedOrders = ref([]);

const changeStartDate=()=>{
  if(selectedStartDate.value){
    const startDate = new Date(selectedStartDate.value);
    options.value.startDate = `${startDate.getFullYear()}-${(startDate.getMonth() + 1).toString().padStart(2, "0")}-${startDate.getDate().toString().padStart(2, "0")} 00:00:00`;
  }
  dateMenu.value = false;
}

const changeEndDate=()=>{
  if(selectedEndDate.value){
    const endDate = new Date(selectedEndDate.value);
    options.value.endDate = `${endDate.getFullYear()}-${(endDate.getMonth() + 1).toString().padStart(2, "0")}-${endDate.getDate().toString().padStart(2, "0")} 23:59:59`;
  }
  dateEndMenu.value = false;
}

const downloadProductExcel = async ()=>{
  if(!permissionStore?.hasModulePermission(`${DynamicModuleEnum.PRODUCT}:${DynamicPermissionEnum.EXPORT_STOCK_REPORT}`)){
    snackbar.add({
      type: "warning",
      text: "You don't have permission for product report",
    })
    return;
  }

  if(!options?.value?.startDate || !options?.value?.endDate){
    snackbar.add({
      type: "warning",
      text: "Please fill start and end date!",
    })
    return;
  }

  let params = {
    startDate : options?.value?.startDate || "",
    endDate: options?.value?.endDate || "",
    exportTo: 'excel',
  }
  let url = `product/report/excelDownload/product/stockReport${buildParams(params)}`;
  try{
    await reportStore.downloadProductListReport(url)
      .then((response)=>{
        snackbar.add({
          type: "warning",
          text: "Successfully exported product details",
        })
      })
  }catch(e){
    console.log(e);
  }
}

watch(()=>options.value.page, (newVal, oldVal) => {
  if (newVal && newVal!=oldVal) {
    router.push(
      {
        query:{...route.query, page: String(newVal)}
      }
    );
  }
}, {deep:true});

watch(()=>options.value.categoryIds, (newVal, oldVal) => {
  if (newVal && newVal!=oldVal) {
    router.push(
      {
        query:{...route.query, categoryIds: String(newVal)}
      }
    );
  }  
},{
  deep: true,
})

const headers = ref([
  { title: "Image", align: "start", sortable: false, key: "image" },
  { title: "Name", align: "start", sortable: false, key: "name" },
  { title: "Brand", key: "brand", sortable: false, align: "start" },
  { title: "Category", key: "category", sortable: false, align: "start" },
  { title: "SKU", key: "sku", sortable: false, align: "start" },
  { title: "Status", key: "status", sortable: false, align: "start" },
  { title: "Bundle", key: "bundle", sortable: false, align: "start" },
  { title: "Related Tag", key: "related-tag", sortable: false, align: "start" },
  { title: "Action", key: "action", align: "center", sortable: false },
]);
const editProduct = async (id: number) => {
  await navigateTo({ path: `/products/product/${id}` });
};

const bundleHandle = (item) => {
  bundleProductFormRef.value?.show(item);
};
const bundleRelatedTagHandle = (item) => {
  bundleProductRelatedTagFormRef.value?.show(item);
};

const onClick = () => {
  console.log("onClick");
  fetchProductList();
};

const onClickNameSearch = () => {
  console.log("onClickNameSearch");
  fetchProductList();
};

const clearNameSearch = () => {
  options.value = { ...options.value, nameSearch: "" };
  fetchProductList();
};

const onClickSkuSearch = () => {
  fetchProductList();
};

const clearSkuSearch = () => {
  options.value = { ...options.value, skuSearch: "" };
  fetchProductList();
};

const clearSearch = () => {
  options.value = { ...options.value, search: "" };
  fetchProductList();
};

const fetchProductList = async () => {

  if(!permissionStore.hasModulePermission(`${DynamicModuleEnum.PRODUCT}:${DynamicPermissionEnum.READ_MANY}`)){
    setTimeout(()=>{
      snackbar.add({
        type: "warning",
        text: "You don't have permission to see product list",
      })
    }, 2000)
    return 
  }

  const {
    page,
    itemsPerPage,
    sortBy,
    sortDesc,
    search,
    skuSearch,
    nameSearch,
    categoryIds,
  } = options.value;
  const order = sortDesc[0] ? "DESC" : "ASC";
  const sortKey = sortBy[0] || "id";
  let categoryIdString = categoryIds.join(",");
  loading.value = true;
  await store.getProducts(
    order,
    page,
    itemsPerPage,
    sortKey,
    search,
    skuSearch,
    nameSearch,
    categoryIdString
  );
  products.value = store.products;
  loading.value = false;
};

const config = useRuntimeConfig();
const frontEndUrl = computed(() => config.public.frontEndUrl);

const removeProduct = async (productId: number) => {
  confirmDialog.value = true;
  store.deleteProduct(productId).then((res) => {
    if (res) {
      snackbar.add({
        type: res ? "success" : "error",
        text: res,
      });
      fetchProductList();
    }
  });
};

const itemToDelete = ref<number | null>(null);
const handleDeleteConfirmation = async () => {
  if (itemToDelete.value !== null) {
    
    if(!permissionStore.hasModulePermission(`${DynamicModuleEnum.PRODUCT}:${DynamicPermissionEnum.SOFT_DELETE}`)){
      snackbar.add({
        type: "warning",
        text: "You don't have permission to see product list",
      })
      return;
    }
    
    const productId = itemToDelete.value;
    // Call the delete product API
    const res = await store.deleteProduct(productId);
    // Handle response
    if (res) {
      snackbar.add({
        type: "success",
        text: "Product deleted successfully!",
      });
      confirmDialog.value = false;
    } else {
      snackbar.add({
        type: "error",
        text: "Failed to delete product.",
      });
    }
  }
};

const itemToCopy = ref<number | null>(null);
const handleCopyConfirmation = async () => {
  if (itemToCopy.value !== null) {
    const productId = itemToCopy.value;
    // Call the delete product API
    const res = await store.copyProduct(productId);
    // Handle response
    if (res?.success) {
      snackbar.add({
        type: "success",
        text: "Product copied successfully!",
      });
      confirmDialog.value = false;
      fetchProductList();
    } else {
      snackbar.add({
        type: "error",
        text: "Failed to copy product.",
      });
    }
  }
};

watch(()=> route.query, (newVal, oldval) => {
  if(newVal){
    if(newVal?.page) options.value.page = newVal?.page;
    if(newVal?.categoryIds){
      options.value.categoryIds = newVal?.categoryIds?.split(",")?.map((id: any) => Number(id));
    };
    fetchProductList();
  }
}, {deep: true})

const setRouteQueryParams = ()=>{
  options.value.page = Number(route?.query?.page || 1);
}


const fetchCategories = async () => {
  if(categoryStore?.categories?.length <= 0){
    const page = 1;
    const order = 'ASC';
    const sortKey = 'name';
    const itemsPerPage = 100;
    await categoryStore.getCategories(order, page, itemsPerPage, sortKey, search);
    loading.value = false;    
  }
}

onMounted(()=>{
  fetchCategories();
  setRouteQueryParams();
})
</script>
