<template>
  <SharedBaseBreadcrumb :title="page.title" :breadcrumbs="breadcrumbs" />

  <SharedUiParentCard class="mt-6" title="Product Details">
    <v-row>
      <v-col>
        <v-form v-model="form" @submit.prevent="onSubmit" ref="productForm">
          <v-row>
            <v-col cols="8">
              <v-text-field
                  v-model="product.name"
                  label="Product Name"
                  hide-details
              ></v-text-field>
              <SharedUiParentCard class="mt-6" title="Product Data">
                <v-row>
                  <v-col cols="6">
                    <!-- <v-col :cols="product.isMultiVariantProduct ? 12 : 6"> -->
                    <v-text-field v-model="product.sku" label="SKU"/>
                  </v-col>
                  <v-col cols="6">
                    <v-text-field v-model="product.code" label="Code"/>
                  </v-col>
                </v-row>
              </SharedUiParentCard>

              <v-row>
                <v-col cols="6">
                  <!-- <SharedUiParentCard class="mt-6" title="Product Category">
                    <v-select
                        v-model="product.categoryId"
                        label="Select Category"
                        :items="flattenCategories"
                        item-title="name"
                        item-value="id"
                        return-object
                    >
                      <template #item="{ props, item }">
                        <v-list-item v-bind="props" :style="{ paddingLeft: (item.raw.level * 20) + 'px' }"></v-list-item>
                      </template>
                    </v-select>
                  </SharedUiParentCard> -->

                  <ProductCategory v-model="product.categoryId"/>
                </v-col>
                <v-col cols="6">

                  <ProductBrands v-model="product.brandId"/>
                  <!-- <SharedUiParentCard class="mt-6" title="Product Brand">
                    <v-select
                        :items="getBrands"
                        item-title="name"
                        item-value="id"
                        v-model="product.brandId"
                        placeholder="Select Brand"
                    ></v-select>
                  </SharedUiParentCard> -->
                </v-col>
              </v-row>

              <SharedUiParentCard class="mt-6" title="Product Description">
                <FormElementsRichTextEditor v-model="product.description"/>
              </SharedUiParentCard>
              <SharedUiParentCard class="mt-6" title="Product Material & Care">
                <FormElementsRichTextEditor v-model="product.materialCare"/>
              </SharedUiParentCard>
              <SharedUiParentCard
                  class="mt-6"
                  title="Product Shipping & Return"
              >
                <FormElementsRichTextEditor v-model="product.shippingReturn"/>
              </SharedUiParentCard>
              <SharedUiParentCard class="mt-6" title="The WOW Factors">
                <FormElementsRichTextEditor v-model="product.wowFactors"/>
              </SharedUiParentCard>

              <v-row>
              <v-col cols="12">
                <SharedUiParentCard class="mt-6" title="Tagline">
                  <!-- <FormElementsEditor v-model="product.tagLine1"/> -->

                <FormElementsCommonTextFieldContainer class="mt-6" v-model="product.tagLine1" label="Tagline 1"/>

                <!-- <FormElementsEditor v-model="product.tagLine2"/> -->

                <FormElementsCommonTextFieldContainer class="mt-6" v-model="product.tagLine2" label="Tagline 2"/>

              </SharedUiParentCard>
              </v-col>
            </v-row>
              <v-row v-if="product?.countryIds?.length > 0">
                <v-col>
                  <SharedUiParentCard
                      v-for="price in product.productPrice"
                      :key="price"
                      :title="
                      countryStore.getCountry(parseInt(price?.countryId))[0]
                        ?.name + ' Product Price'
                    "
                      class="mt-3"
                  >
                    <!-- <SharedUiParentCard class="mt-6" title="Product Price">  -->
                    <v-row>
                      <v-col cols="4">
                        <v-text-field
                            label="Price"
                            v-model="price.unitPrice"
                            type="number"
                        ></v-text-field>
                      </v-col>
                      <v-col cols="4">
                        <v-text-field
                            label="Discount Price"
                            v-model="price.discountPrice"
                            type="number"
                        ></v-text-field>
                      </v-col>

                      <v-col cols="4">
                        <v-text-field
                            label="Quantity"
                            v-model="price.quantity"
                            type="number"
                        ></v-text-field>
                      </v-col>
                    </v-row>
                  </SharedUiParentCard>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="4">

              <SharedUiParentCard title="Product Status" v-if="product.id">
                <v-switch
                    v-model="product.isPublish"
                    :label="'Status ' + ((product.isPublish)? 'Published': 'Not Published')"
                    hide-details
                    inset
                    @update:model-value="onUpdatePublish"
                ></v-switch>
              </SharedUiParentCard>

              <SharedUiParentCard class="mt-6" title="Product featured image">

                <FormElementsImageViewerSingle
                    :image-id="product.featuredImageId"
                    :image-rules="PRODUCT_IMAGE_RULES"
                    :required-height="2250"
                    :required-width="1800"
                    :image-folders="[{ folderSlug: 'product_1800_2250', title: 'New', size: '1800x2250'}, {folderSlug:'product', title: 'Old', size: '1808x2000'}]"
                    image-model="product_1800_2250"
                    @selected-image-id="selectedFeaturedImage"
                    :multiple="false"
                />
              </SharedUiParentCard>

              <SharedUiParentCard class="mt-6" title="Product hover image">

                <FormElementsImageViewerSingle
                    :image-id="product.hoverImageId"
                    :image-rules="PRODUCT_IMAGE_RULES"
                    :required-height="2250"
                    :required-width="1800"
                    :image-folders="[{ folderSlug: 'product_1800_2250', title: 'New', size: '1800x2250'}, {folderSlug:'product', title: 'Old', size: '1808x2000'}]"
                    image-model="product_1800_2250"
                    @selected-image-id="selectedHoverImage"
                    :multiple="false"
                />
              </SharedUiParentCard>

              <SharedUiParentCard class="mt-6" title="Product gallery">

                <FormElementsImageViewer
                    :image-ids="product.imageGalleryIds"
                    :image-rules="PRODUCT_IMAGE_RULES"
                    :multiple="true"
                    :required-height="2250"
                    :required-width="1800"
                    :image-folders="[{ folderSlug: 'product_1800_2250', title: 'New', size: '1800x2250'}, {folderSlug:'product', title: 'Old', size: '1808x2000'}]"
                    image-model="product_1800_2250"
                    @selected-image-id="selectedGalleryImage"
                />
              </SharedUiParentCard>

              <v-col>
                <ProductSizeChart v-model="product.sizeChartId"/>
              </v-col>

              <SharedUiParentCard class="mt-6" title="Product Size Chart">

                <FormElementsImageViewerSingle
              :image-id="product.sizeChart"
              image-model="SIZE_CHART"
              :image-rules="PRODUCT_SIZE_CHART_RULES"
              :max-size="200000"
              :required-height="499"
              :required-width="969"
              :multiple="false"
              @selected-image-id="handleSizeChart"
          />

              </SharedUiParentCard>

              <SharedUiParentCard class="mt-6" title="Global Stock Management">
                <!-- <v-checkbox label="Manage Stock?" v-model="manageStock" hide-details></v-checkbox> -->
                <v-checkbox
                    v-model="product.isRefundable"
                    label="Is Refundable"
                    hide-details
                />
                <v-checkbox
                    v-model="product.isMultiVariantProduct"
                    label="Has Attributes?"
                    @update:model-value="onUpdateIsMultiVariantProduct"
                    hide-details
                ></v-checkbox>

                <FormElementsCommonTextFieldContainer class="mt-6" type="number" v-model="product.sortOrder" label="Set Order"/>
                <!-- @update:model-value="onUpdateIsMultiVariantProduct" -->
              </SharedUiParentCard>

              <SharedUiParentCard class="mt-6" title="Country">
                <v-autocomplete
                    v-model="product.countryIds"
                    label="Select Country"
                    multiple
                    :items="getCountries"
                    item-title="name"
                    item-value="id"
                    @update:model-value="onUpdateIsMultiVariantProduct"
                      chips closable-chips
                />
              </SharedUiParentCard>
            </v-col>
          </v-row>
          <v-row>
            <v-col>
              <v-btn
                  color="primary"
                  variant="tonal"
                  class="px-6"
                  @click="saveProduct"
              >
                Save Product
              </v-btn
              >
            </v-col>
          </v-row>
        </v-form>
      </v-col>
    </v-row>
  </SharedUiParentCard>
  <!-- <SharedUiParentCard class="mb-5" v-if="product.isMultiVariantProduct">
    <v-row>
      <v-col>
        <v-form
            v-model="form"
            @submit.prevent="onSubmit"
            ref="productVariantForm"
        >
          <SharedUiParentCard title="Product Attributes">
            <v-row>
              <v-col
                  cols="6"
                  v-for="(attribute, index) in getAttributes"
                  :key="attribute.id"
              >
                <v-select
                    :label="'Select ' + attribute.name"
                    multiple
                    :items="getAttsValues(attribute.id)"
                    item-title="name"
                    item-value="id"
                    v-model="productVariant.selectedAttributes[attribute.id]"
                ></v-select>
              </v-col>
            </v-row>
            <v-btn
                color="primary"
                @click="createProductVariations"
                v-if="!productVariant.countryWiseProductVariant.length > 0"
            >
              Generate Variations
            </v-btn>
            <v-btn
                color="error"
                v-if="productVariant.countryWiseProductVariant.length > 0"
                @click="
                (productVariant.countryWiseProductVariant = []),
                  (productVariant.selectedAttributes = []),
                  (productVariant.attributesImages = [])
              "
            >
              Reset Variations
            </v-btn>
          </SharedUiParentCard>
          <SharedUiParentCard
              title="Product Variations"
              v-if="productVariant.countryWiseProductVariant.length > 0"
          >
            <SharedUiParentCard title="Color Images">
              <v-table>
                <tbody>
                <tr
                    v-for="(
                      attrObject, index
                    ) in productVariant.attributesImages"
                    :key="index"
                >
                  <td>
                    {{
                      getAttributeName(attrObject.id)[0]
                          ? getAttributeName(attrObject.id)[0].name
                          : ""
                    }}
                  </td>
                  <td>
                    {{ attrObject }}
                    <FormElementsImageViewer
                        :image-ids="productVariant.attributeImages[index].images"
                        :image-rules="PRODUCT_IMAGE_RULES"
                        :multiple="true"
                        :required-height="2000"
                        :required-width="1808"
                        image-model="product"
                        @selected-image-id="selectedColorImages($event, attrObject.attributeValueId)"
                    />
                  </td>
                </tr>
                </tbody>
              </v-table>
            </SharedUiParentCard>
            <SharedUiParentCard
                v-for="(country, index) in productVariant.countryWiseProductVariant"
                :key="country"
                :title="countryStore.getCountry(country.countryId)[0].name"
                class="mt-3"
            >
              <v-card class="border" elevation="0">
                <v-card-text>
                  <h3 class="mb-3">Price & Stock</h3>
                  <v-table>
                    <tbody>
                    <tr>
                      <td>
                        <v-text-field
                            label="Price"
                            hide-details
                            v-model="
                              generalPriceAndStockByCountryId[index].price
                            "
                        ></v-text-field>
                      </td>
                      <td>
                        <v-text-field
                            label="Promo Price"
                            hide-details
                            v-model="
                              generalPriceAndStockByCountryId[index]
                                .discountPrice
                            "
                        ></v-text-field>
                      </td>
                      <td>
                        <v-text-field
                            label="Quantity"
                            type="number"
                            hide-details
                            v-model="
                              generalPriceAndStockByCountryId[index].quantity
                            "
                        ></v-text-field>
                      </td>
                      <td>
                        <v-text-field
                            label="Seller SKU"
                            v-model="generalPriceAndStockByCountryId[index].sku"
                            hide-details
                        ></v-text-field>
                      </td>
                      <td>
                        <v-btn
                            color="primary"
                            @click="
                              ApplyValuesToCountryFields(country.countryId)
                            "
                        >Apply to All
                        </v-btn>
                      </td>
                    </tr>
                    </tbody>
                  </v-table>
                </v-card-text>
              </v-card>
              <v-card class="border mt-3" elevation="0">
                <v-table>
                  <thead>
                  <tr>
                    <th class="text-h6 text-no-wrap">Variation</th>
                    <th class="text-h6 text-no-wrap">Price</th>
                    <th class="text-h6 text-no-wrap">Special Price</th>
                    <th class="text-h6 text-no-wrap">Quantity</th>
                    <th class="text-h6 text-no-wrap">Seller SKU</th>
                    <th class="text-h6 text-no-wrap">Status</th>
                  </tr>
                  </thead>
                  <tbody>
                  <tr
                      v-for="(variant, index) in country.productVariations"
                      :key="country.id"
                  >
                    <td>
                      <template
                          v-for="(id, index) in variant.variantName"
                          :key="id"
                      >
                        {{
                          getAttributeName(id)[0]
                              ? getAttributeName(id)[0].attribute.name +
                              " - " +
                              getAttributeName(id)[0].name +
                              " | "
                              : ""
                        }}
                      </template>
                    </td>
                    <td>
                      <v-text-field
                          placeholder="Price"
                          hide-details
                          :model-value="variant.price"
                          v-model="variant.price"
                      ></v-text-field>
                    </td>
                    <td>
                      <v-text-field
                          placeholder="Special price"
                          hide-details
                          :model-value="0"
                          v-model="variant.discountPrice"
                      ></v-text-field>
                    </td>
                    <td>
                      <v-text-field
                          placeholder="Quantity"
                          hide-details
                          :model-value="0"
                          v-model="variant.quantity"
                      ></v-text-field>
                    </td>
                    <td>
                      <v-text-field
                          placeholder="Seller SKU"
                          hide-details
                          :model-value="variant.sku"
                          v-model="variant.sku"
                      ></v-text-field>
                    </td>
                    <td>
                      <v-switch
                          color="primary"
                          hide-details
                          :model-value="variant.isActive"
                          v-model="variant.isActive"
                      ></v-switch>
                    </td>
                  </tr>
                  </tbody>
                </v-table>
              </v-card>
            </SharedUiParentCard>
            <v-btn
                color="primary"
                variant="tonal"
                class="px-6"
                type="submit"
                @click="saveVariant"
            >
              Save Variant
            </v-btn
            >
          </SharedUiParentCard>
        </v-form>
      </v-col>
    </v-row>
  </SharedUiParentCard> -->
</template>

<script setup lang="ts">
import {ref, computed, onMounted} from "vue";
import {useProductBrandsStore} from "~/stores/products/brands";
import {useProductAttributesStore} from "~/stores/products/attributes";
import {useProductCategoriesStore} from "~/stores/products/categories";
import {useCountryStore} from "~/stores/others/country";
import {useAttrValueStore} from "~/stores/products/attribute-values";

import {cartesian, flatten} from "~/utils/helpers/functions";
import {useProductImagesStore} from "~/stores/products/product-images";
import {useProductsStore} from "~/stores/products";
import {PRODUCT_IMAGE_RULES, PRODUCT_SIZE_CHART_RULES} from "~/utils/imageRules";
import {DynamicModuleEnum, DynamicPermissionEnum} from "~/utils/roleManagement";
import { useRolePermissionsStore } from "~/stores/administration/permissions";

const brandStore = useProductBrandsStore();
const attributesStore = useProductAttributesStore();
const catStore = useProductCategoriesStore();
const countryStore = useCountryStore();
const attrValStore = useAttrValueStore();
const imageStore = useProductImagesStore();
const store = useProductsStore();
const permissionStore = useRolePermissionsStore();

const manageStock = ref(false);

// theme breadcrumb
const page = ref({title: "Add New Product"});

const snackbar = useSnackbar();

const breadcrumbs = ref([
  {
    text: "Dashboard",
    disabled: false,
    to: "/dashboard",
  },
  {
    text: "Products",
    disabled: false,
    to: "/products/product",
  },
  {
    text: "Add New",
    disabled: true,
    to: "",
  },
]);
const valid = ref(false);

// const product: Ref<MainProduct> = ref({});
const product = ref({
  id: null,
  isActive: true,
  isPublish: false,
  isMultiVariantProduct: false,
  isComboPackProduct: false,
  name: null,
  code: null,
  sku: null,
  description: null,
  materialCare: null,
  shippingReturn: null,
  wowFactors: null,
  slug: null,
  isRefundable: false,
  brandId: null,
  categoryId: [],
  countryIds: [],
  featuredImageId: null,
  hoverImageId: null,
  imageGalleryIds: [],
  productPrice: [],
  sortOrder: null,
  sizeChart: null,
  tagLine1: null,
  tagLine2: null,
  sizeChartId: null,
});
const productVariant = ref({
  productId: null,
  selectedAttributes: [],
  countryWiseProductVariant: [],
  attributesImages: [],
});

interface defaultAssign {
  countryId: number;
  price?: number;
  discountPrice?: number;
  quantity?: number;
  sku?: string;
}

const generalPriceAndStockByCountryId: Ref<defaultAssign[]> = ref([]);

const hasAttributesModelUpdate = (value: any) => {
  if (value) {
    product.value.countryIds.forEach((item, index) => {
      generalPriceAndStockByCountryId.value.push({
        countryId: item,
        price: 0,
        discountPrice: 0,
        quantity: 0,
        sku: "PAN2022",
      });
    });
  }
};

const form = ref(false);
// const productForm = ref();
const productVariantForm = ref();
const loading = ref(false);

const getAttsValues = (id) => {
  return getAttrValues.value.filter((item) => item.attributeId === id);
};

const onSubmit = () => {
  console.log(product.value);
};
const saveProduct = () => {
  if(!permissionStore.hasModulePermission(`${DynamicModuleEnum.PRODUCT}:${DynamicPermissionEnum.CREATE}`)){
    snackbar.add({
      type: "warning",
      text: "You don't have permission to CREATE PRODUCT",
    })
    return;
  }


  loading.value = true;

  store
      .addUpdateProduct(product.value)
      .then(async (res: any) => {
        console.log("productVariant.value", res.data);
        productVariant.value.productId = res?.data?.id;
        snackbar.add({
          type: (res.success && res.data) ? "success" : "error",
          text: res.message,
        });
        if(res.success && res?.data){
          // if(!product.value.isMultiVariantProduct){
            await navigateTo({ path: `/products/product` });
          // }
        }
      })
      .catch((err: any) => {
        console.log(err);
      });

  loading.value = false;
  // resetForm()

  //   }, 500);
};
const saveVariant = () => {
  loading.value = true;
  console.log("productVariant.value", productVariant.value);

  store.addUpdateProductVariant(productVariant.value)
    .then((res: any) => {
      snackbar.add({
        type: res.success ? "success" : "error",
        text: res.message,
      });
    })
    .catch((err: any) => {
      console.log(err);
    });

  loading.value = false;
  // resetForm()
};

const handleImgUpload = (value) => {
};

const handleAttributeImgUpload = (value, id) => {
  // const index = product.value.attributesImages.findIndex(item => item.id === id);
  // console.log(index)
  // product.value.attributesImages[index].images.push(event.target)
  // console.log(event)
};

const handleAttributeImgDeleted = (event) => {
  console.log(event);
};

onMounted(() => {
  // brandStore.getBrands({});
  // catStore.getCategories({});
  // attributesStore.getAttributes({});
  countryStore.getAllCountries();
  attrValStore.getAllAttributeValues();
});

const onUpdateCountry = (countryIds: any) => {
  if (!product.value.isMultiVariantProduct) {
    product.value.countryIds.forEach((id: number) => {
      let priceObj: any = {};
      priceObj.countryId = id;
      priceObj.unitPrice = 0;
      priceObj.discountPrice = 0;
      priceObj.quantity = 0;

      const index = product.value?.productPrice.findIndex(
          (item: any) => item.countryId === id
      );

      if (index === -1) {
        product.value?.productPrice.push(priceObj);
      }
    });

    product.value.productPrice = product.value?.productPrice.filter(
        (item: any) => product.value.countryIds.includes(item.countryId)
    );
  }
};
const onUpdateIsMultiVariantProduct = (value: any) => {
  if (product.value.isMultiVariantProduct) {
    product.value.productPrice = [];
  } else {
      if(!product.value?.productPrice){
        product.value.productPrice = [];
      }

    onUpdateCountry(product.value.countryIds);
  }
};

const onUpdatePublish = () => {
  if(product.value.id){
  loading.value = true;
  store.publishProduct(product.value.id, product.value.isPublish)
      .then((res: any) => {
        snackbar.add({
          type: res.success ? "success" : "error",
          text: res.message,
        });
      })
      .catch((err: any) => {
        console.log(err);
      });

  loading.value = false;
  }
};

const getBrands = computed(() => {
  return brandStore.brands;
});

const getCategories = computed(() => {
  return catStore.categories;
});

const getAttributes = computed(() => {
  return attributesStore.attributes;
});

const getCountries = computed(() => {
  return countryStore.countries;
});

const getAttrValues = computed(() => {
  return attrValStore.attrValues;
});

const createProductVariations = () => {
  hasAttributesModelUpdate(true);
  const variationValues:any[] = [];

  productVariant.value.selectedAttributes.forEach(
      (values: any, attributeId) => {
        console.log(`value${values}, attribute ${attributeId}`);

        if(attributeId == 2){
        values.forEach((id) => {
          const attributeImageObject:any = {};
          attributeImageObject.id = id;
          attributeImageObject.images = [];
          productVariant.value.attributesImages.push(attributeImageObject);
        });
        }

        variationValues.push(values);
      }
  );

  product.value.countryIds.forEach((id, index) => {
    const countrySet = {};

    countrySet.countryId = id;

    countrySet.productVariations = cartesian(...variationValues).map((e) => ({
      variantId: null,
      variantName: e,
      unitPrice: 0,
      discountPrice: 0,
      quantity: 0,
      sku: "PAN2022",
      isActive: true,
    }));

    productVariant.value.countryWiseProductVariant.push(countrySet);
  });
};

const ApplyValuesToCountryFields = (id: number) => {
  const values = generalPriceAndStockByCountryId.value;
  const conId = id;
  //
  productVariant.value.countryWiseProductVariant.forEach((item1, index1) => {
    if (item1.countryId === conId) {
      item1.productVariations.forEach((item2, index2) => {
        item2.price = values[index1].price;
        item2.discountPrice = values[index1].discountPrice;
        item2.quantity = values[index1].quantity;
        item2.sku = values[index1].sku;
      });
    }
  });
};

const getAttributeName = (id: number) => {
  return attrValStore.attrValues.filter((item) => item.id === id);
};

const selectedFeaturedImage = (val: any) => {
  product.value.featuredImageId = val;
}

const selectedHoverImage = (val: any) => {
  product.value.hoverImageId = val;
}

const selectedGalleryImage = (val: any) => {
  product.value.imageGalleryIds = val
}

const handleSizeChart = (val: any) => {
  console.log('size chart', val);

  product.value.sizeChart = val
}
const populateAllProductImages = (val) => {
  if (val) {
    imageStore.getImages({});
  }
};


const flattenCategories = computed(() => flatten( catStore.getCategoriesTree, 0 ))

const selectedColorImages = (val: any, id: any) => {

  const elementPosition = productVariant.value.attributeImages?.map((x: any) => x.attributeValueId).indexOf(parseInt(id));
  productVariant.value.attributeImages[elementPosition].images = val

}

</script>
