<template>
    <SharedBaseBreadcrumb
        :title="page.title"
        :breadcrumbs="breadcrumbs"
    ></SharedBaseBreadcrumb>
    <v-row>
      <v-col cols="12" md="12">
        <div class="d-flex justify-space-between align-center">

          <div>
            <h2>Collection List</h2>
            <v-btn @click="addNew"><CirclePlusIcon size="20"/> Add new</v-btn>
          </div>
          

          <div class="w-50">
            <v-text-field append-inner-icon="mdi-magnify" density="compact"
                          label="Search ..." variant="outlined" hide-details single-line
                          @onChange:append-inner="onClick"
                          v-model="options.search" clearable @click:clear="clearSearch"></v-text-field>
          </div>
        </div>
  
        <v-data-table-server
            v-model:page="options.page"
            v-model:items-per-page="options.itemsPerPage"
            :headers="headers"
            :items="products"
            :items-length="store.pagination.itemCount"
            :search="options.search"
            :loading="loading"
            item-value="id" @update:options="fetchProductList">
  
          <template v-slot:item.title="{ item }">
            <p style="width: 250px;text-wrap: wrap">{{ item?.title }}</p>
          </template>

          <template v-slot:item.country="{ item }">
            <p style="width: 100px;text-wrap: wrap">{{ item?.countryName }}</p>
          </template>

          <template v-slot:item.isActive="{ item }">
            <p style="width: 87px;text-wrap: wrap">{{ item.isActive ? 'Yes' : 'No' }}</p>
          </template>

          <template v-slot:item.product="{ item }">
            <p style="width: 100px;text-wrap: wrap">{{ item?.productCount }}</p>
          </template>

          <template v-slot:item.url="{ item }">
            <a :href="`https://${item.domain}/collection/${item.slug}`" target="_blank">View</a>
          </template>

          <template v-slot:item.action="{ item }">
            <div class="text-end">
  
              <v-menu>
                <template v-slot:activator="{ props }">
                  <v-btn class="me-2" variant="tonal" size="small" icon="mdi-dots-vertical" v-bind="props"></v-btn>
                </template>
  
                <v-list>
                  <v-list-item>
                    <v-btn
                        icon
                        color="error"
                        variant="tonal"
                        size="x-small"
                        @click="editCollectionHandler(item)"
                        title="Edit"
                    >
                      <PencilIcon size="20"/>
                    </v-btn>
                  </v-list-item>
                  <v-list-item>
                    <v-btn
                        icon
                        color="error"
                        variant="tonal"
                        size="x-small"
                        @click="productEditHandler(item.id)"
                        title="Edit products"
                    >
                      <ShoppingCartIcon size="20"/>
                    </v-btn>
                  </v-list-item>
                  <v-list-item>
                    <v-btn icon color="error" variant="tonal" size="x-small" class="ms-1"
                           @click="confirmDialog = true; itemToDelete = item.id">
                      <TrashIcon size="20"/>
                    </v-btn>
                  </v-list-item>
  
                </v-list>
              </v-menu>
            </div>
          </template>
        </v-data-table-server>
      </v-col>
    </v-row>
    <ConformationModal v-model:dialog="confirmDialog" @confirm-delete="handleDeleteConfirmation" />
    <CollectionProduct ref="collectionProductRef" @reset="resetHandler"/>
    <CollectionForm ref="collectionFromRef" @reset="resetHandler"/>
  
  </template>
  
  <script setup lang="ts">
  import {ref, watch} from "vue";
  import {deleteItem} from "~/utils/helpers/functions";
  import {ShoppingCartIcon, CirclePlusIcon, PencilIcon, TrashIcon} from "vue-tabler-icons";
  import ConformationModal from "~/components/modals/ConformationModal.vue";
  import { useProductCollectionStore } from "~/stores/products/product-collection";
  import CollectionProduct from "~/components/product/collection/CollectionProduct.vue";
  import CollectionForm from "~/components/product/collection/CollectionForm.vue";
  
  const snackbar = useSnackbar();
  const store = useProductCollectionStore();
  const page = ref({title: "Collections"});
  
  const breadcrumbs = ref([
    {
      text: "Dashboard",
      disabled: false,
      to: "/dashboard",
    },
    {
      text: "Collection list",
      disabled: true,
      to: "",
    },
  ]);
  const confirmDialog = ref(false)
  const options = ref({
    page: 1,
    itemsPerPage: 20,
    sortBy: ['id'],
    sortDesc: [false],
    search: '',
  });
  const loading = ref(false);
  const products = ref([]);

  
  const headers = ref([
    {title: 'Name', align: 'start', sortable: false, key: 'title'},
    {title: 'Country', key: 'country', sortable: false, align: 'start'},
    {title: 'Published', key: 'isActive', sortable: false, align: 'start'},
    {title: 'Products', key: 'product', sortable: false, align: 'start'},
    {title: 'URL', key: 'url', sortable: false, align: 'start'},
    {title: 'Action', key: 'action', align: 'center', sortable: false},
  ])

  const collectionFromRef = ref<InstanceType<typeof CollectionForm>>();

  const addNew = () => {
    collectionFromRef.value?.show();
  };

  const editCollectionHandler = (collectionItem) => {
    collectionFromRef.value?.edit(collectionItem.id, {
      slug: collectionItem.slug,
    title: collectionItem.title,
    countryId: collectionItem.countryId,
    fullContent: collectionItem.fullContent,
    leftHalfContent: collectionItem.leftHalfContent,
    rightHalfContent: collectionItem.rightHalfContent,
    metaImageId: null,
    metaTitle: collectionItem.metaTitle,
    metaDescription: collectionItem.metaDescription,
    isActive: collectionItem.isActive,
    });
  };

  const resetHandler = () => {
    fetchProductList();
  };

  const collectionProductRef = ref<InstanceType<typeof CollectionProduct>>();
  const productEditHandler = (collectionId: number) => {
    collectionProductRef.value?.show(collectionId);
  };
  
  
  const search = ref('')
  const name = ref('')
  const sku = ref('')
  
  
  const onClick = () => {
    if (!search.value) return;
    fetchProductList();
  };
  
  const clearSearch = () => {
    search.value = '';
    fetchProductList();
  };
  
  const fetchProductList = async () => {
    const {page, itemsPerPage, sortBy, sortDesc, search} = options.value;
    const order = sortDesc[0] ? 'DESC' : 'ASC';
    const sortKey = sortBy[0] || 'id';
    loading.value = true;
    await store.getCollections(order, page, itemsPerPage, sortKey, search);
    products.value = store.collections;
    loading.value = false;
  };
  
  const fakeReceiver = ref([]);
  const removeCollection = (id: number) => {
  
    confirmDialog.value = true;

    deleteItem(`product-collection/`, id, fakeReceiver.value)
    .then(res => {
        if (res) {
            snackbar.add({
            type: 'success',
            text: 'Product deleted successfully!',
            });
  
        } else {
            snackbar.add({
            type: 'error',
            text: 'Failed to delete product.',
            });
        }
        fetchProductList();
    }).finally(() => {
        confirmDialog.value = false;
    });
  }
  
  const itemToDelete = ref<number | null>(null);
  const handleDeleteConfirmation = async () => {
    if (itemToDelete.value !== null) {
      const productId = itemToDelete.value;
      removeCollection(productId);
    }
  };
  
  const filterProducts = (filterKey, value, listKey) => {
    const normalizedValue = value.toLowerCase();
    let filteredData = [];
  
    if (normalizedValue.length > 0) {
      filteredData = store.products.filter(item => {
        const itemValue = filterKey(item).toLowerCase();
        return itemValue.includes(normalizedValue);
      });
    } else {
      filteredData = store.products;
    }
  
    products.value = filteredData;
  };
  
  watch(name, (val) => {
    filterProducts(item => item.name, val, 'products');
  });
  watch(sku, (val) => {
    filterProducts(item => item.sku, val, 'products');
  });
  
  </script>
  