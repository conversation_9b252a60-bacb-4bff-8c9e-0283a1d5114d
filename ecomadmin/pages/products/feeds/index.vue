<template>
    <SharedBaseBreadcrumb
        :title="page.title"
        :breadcrumbs="breadcrumbs"
    ></SharedBaseBreadcrumb>
    <v-row>
      <v-col cols="12" md="12">
        <div class="d-flex justify-space-between align-center">
          <h2>Feed List</h2>
          <div class="w-50">
            <v-text-field append-inner-icon="mdi-magnify" density="compact"
                          label="Search ..." variant="outlined" hide-details single-line
                          @onChange:append-inner="onClick"
                          v-model="options.search" clearable @click:clear="clearSearch"></v-text-field>
          </div>
        </div>
  
        <v-data-table-server
            v-model:page="options.page"
            v-model:items-per-page="options.itemsPerPage"
            :headers="headers"
            :items="products"
            :items-length="store.pagination.itemCount"
            :search="options.search"
            :loading="loading"
            item-value="id" @update:options="fetchProductList">
  
          <template v-slot:item.name="{ item }">
            <p style="width: 250px;text-wrap: wrap">{{ item?.name }}</p>
          </template>

          <template v-slot:item.country="{ item }">
            <p style="width: 250px;text-wrap: wrap">{{ item?.countryName }}</p>
          </template>

          <template v-slot:item.product="{ item }">
            <p style="width: 250px;text-wrap: wrap">{{ item?.productCount }}</p>
          </template>



          <template v-slot:item.url="{ item }">

            <div class="d-flex flex-wrap ga-3">

            <v-menu>
      <template v-slot:activator="{ props }">
        <v-btn
          color="primary"
          dark
          v-bind="props"
        >
          Facebook
        </v-btn>
      </template>

      <v-list>
        <v-list-item
          v-for="(i, index) in ['csv', 'xlsx']"
          :key="index"
        >
        <a :href="`${apiUrl}product-feed/data/facebook/feed-${item.slug}.${i}`" target="_blank">{{ i }}</a>
        </v-list-item>
      </v-list>
    </v-menu>


    <v-menu class="ms-2">
      <template v-slot:activator="{ props }">
        <v-btn
          color="primary"
          dark
          v-bind="props"
        >
          Google
        </v-btn>
      </template>

      <v-list>
        <v-list-item
          v-for="(i, index) in ['csv', 'xlsx']"
          :key="index"
        >
          <a :href="`${apiUrl}product-feed/data/google/feed-${item.slug}.${i}`" target="_blank">Google Marchent: {{ i }}</a>
        </v-list-item>
        <v-divider color="success" :thickness="2" inset class="mt-3"></v-divider>

        <v-list-item
          v-for="(i, index) in ['csv', 'xlsx']"
          :key="index"
        >
          <a :href="`${apiUrl}product-feed/data/google-custom/feed-${item.slug}.${i}`" target="_blank">Google Custom: {{ i }}</a>
        </v-list-item>
      </v-list>
    </v-menu>
  </div>
          </template>



          <template v-slot:item.action="{ item }">
            <div class="text-end">
  
              <v-menu>
                <template v-slot:activator="{ props }">
                  <v-btn class="me-2" variant="tonal" size="small" icon="mdi-dots-vertical" v-bind="props"></v-btn>
                </template>
  
                <v-list>
                  <v-list-item>
                    <v-btn
                        icon
                        color="error"
                        variant="tonal"
                        size="x-small"
                        @click="editFeedHandler(item)"
                        title="Edit"
                    >
                      <PencilIcon size="20"/>
                    </v-btn>
                  </v-list-item>
                  <v-list-item>
                    <v-btn
                        icon
                        color="error"
                        variant="tonal"
                        size="x-small"
                        @click="productEditHandler(item)"
                        title="Edit products"
                    >
                      <ShoppingCartIcon size="20"/>
                    </v-btn>
                  </v-list-item>
                  <v-list-item>
                    <v-btn icon color="error" variant="tonal" size="x-small" class="ms-1"
                           @click="confirmDialog = true; itemToDelete = item.id">
                      <TrashIcon size="20"/>
                    </v-btn>
                  </v-list-item>
  
                </v-list>
              </v-menu>
            </div>
          </template>
        </v-data-table-server>
      </v-col>
    </v-row>
    <ConformationModal v-model:dialog="confirmDialog" @confirm-delete="handleDeleteConfirmation" />
    <FeedEdit ref="feedEditRef" @reset="resetHandler"/>
    <FeedProduct ref="feedProductRef" @reset="resetHandler"/>
  
  </template>
  
  <script setup lang="ts">
  import {ref, watch} from "vue";
  import {deleteItem} from "~/utils/helpers/functions";
  import {ShoppingCartIcon, PencilIcon, TrashIcon} from "vue-tabler-icons";
  import ConformationModal from "~/components/modals/ConformationModal.vue";
  import { useProductFeedStore } from "~/stores/products/product-feed";
  import FeedEdit from "~/components/product/feed/FeedEdit.vue";
  import FeedProduct from "~/components/product/feed/FeedProduct.vue";
  
  const snackbar = useSnackbar();
  const store = useProductFeedStore();
  const page = ref({title: "Feeds"});
  
  const breadcrumbs = ref([
    {
      text: "Dashboard",
      disabled: false,
      to: "/dashboard",
    },
    {
      text: "Feed list",
      disabled: true,
      to: "",
    },
  ]);
  const confirmDialog = ref(false)
  const options = ref({
    page: 1,
    itemsPerPage: 20,
    sortBy: ['id'],
    sortDesc: [false],
    search: '',
  });
  const loading = ref(false);
  const products = ref([]);

  
  const headers = ref([
    {title: 'Name', align: 'start', sortable: false, key: 'name'},
    {title: 'Country', key: 'country', sortable: false, align: 'start'},
    {title: 'Products', key: 'product', sortable: false, align: 'start'},
    {title: 'URL', key: 'url', sortable: false, align: 'start'},
    {title: 'Action', key: 'action', align: 'center', sortable: false},
  ])

  const feedEditRef = ref(null);
  const editFeedHandler = (feedItem) => {
    feedEditRef.value?.show(feedItem);
  };

  const resetHandler = () => {
    fetchProductList();
  };


  const feedProductRef = ref(null);
  const productEditHandler = (feedItem) => {
    feedProductRef.value?.show(feedItem);
  };
  
  
  const search = ref('')
  const name = ref('')
  const sku = ref('')
  
  
  const onClick = () => {
    if (!search.value) return;
    fetchProductList();
  };
  
  const clearSearch = () => {
    search.value = '';
    fetchProductList();
  };
  
  const fetchProductList = async () => {
    const {page, itemsPerPage, sortBy, sortDesc, search} = options.value;
    const order = sortDesc[0] ? 'DESC' : 'ASC';
    const sortKey = sortBy[0] || 'id';
    loading.value = true;
    await store.getFeeds(order, page, itemsPerPage, sortKey, search);
    products.value = store.feeds;
    loading.value = false;
  };
  
  
  const config = useRuntimeConfig();
  const apiUrl = computed(() => config.public.apiUrl.toString())
  
  const toFrontendUrl = (slug: any) => {
    window.open(config.public.frontEndUrl + 'product/' + slug, '_blank');
  }
  
  
  const fakeReceiver = ref([]);
  const removeFeed = (id: number) => {
  
    confirmDialog.value = true;

    deleteItem(`product-feed/`, id, fakeReceiver.value)
    .then(res => {
        if (res) {
            snackbar.add({
            type: 'success',
            text: 'Product deleted successfully!',
            });
  
        } else {
            snackbar.add({
            type: 'error',
            text: 'Failed to delete product.',
            });
        }
        fetchProductList();
    }).finally(() => {
        confirmDialog.value = false;
    });
  }
  
  const itemToDelete = ref<number | null>(null);
  const handleDeleteConfirmation = async () => {
    if (itemToDelete.value !== null) {
      const productId = itemToDelete.value;
      removeFeed(productId);
    }
  };
  
  const filterProducts = (filterKey, value, listKey) => {
    const normalizedValue = value.toLowerCase();
    let filteredData = [];
  
    if (normalizedValue.length > 0) {
      filteredData = store.products.filter(item => {
        const itemValue = filterKey(item).toLowerCase();
        return itemValue.includes(normalizedValue);
      });
    } else {
      filteredData = store.products;
    }
  
    products.value = filteredData;
  };
  
  watch(name, (val) => {
    filterProducts(item => item.name, val, 'products');
  });
  watch(sku, (val) => {
    filterProducts(item => item.sku, val, 'products');
  });
  
  </script>
  