<template>
  <SharedBaseBreadcrumb
      :title="page.title"
      :breadcrumbs="breadcrumbs"
  ></SharedBaseBreadcrumb>
  <v-row>
    <v-col cols="12" md="12">
      <div class="d-flex justify-space-between align-center">
        <v-btn @click="addNew"><CirclePlusIcon size="20"/> Add new</v-btn>
        <div class="w-50">
          <v-text-field append-inner-icon="mdi-magnify" density="compact" label="Search ..." 
            variant="outlined" hide-details single-line @onChange:append-inner="onClick"
            v-model="options.search" clearable @click:clear="clearSearch">
          </v-text-field>
        </div>
      </div>

      <v-data-table-server
          v-model:page="options.page"
          v-model:items-per-page="options.itemsPerPage"
          :headers="headers"
          :items="products"
          :items-length="store.pagination.itemCount"
          :search="options.search"
          :loading="loading"
          item-value="id" @update:options="fetchProductList">

        <template v-slot:item.name="{ item }">
          <p style="width: 250px;text-wrap: wrap">{{ item?.name }}</p>
        </template>

        <template v-slot:item.action="{ item }">
          <div class="text-end">

            <v-menu>
              <template v-slot:activator="{ props }">
                <v-btn class="me-2" variant="tonal" size="small" icon="mdi-dots-vertical" v-bind="props"></v-btn>
              </template>

              <v-list>
                <v-list-item>
                  <v-btn
                      icon
                      color="error"
                      variant="tonal"
                      size="x-small"
                      @click="editHandler(item)"
                      title="Edit"
                  >
                    <PencilIcon size="20"/>
                  </v-btn>
                </v-list-item>
                <v-list-item>
                  <v-btn icon color="info" class="me-2" variant="tonal" size="small"
                         @click="copyChartItem(item)">
                    <CopyIcon size="20"/>
                  </v-btn>
                </v-list-item>
                <v-list-item>
                  <v-btn icon color="info" class="me-2" variant="tonal" size="small"
                         @click="translateItem(item)">
                    <LanguageIcon size="20"/>
                  </v-btn>
                </v-list-item>
                <v-list-item>
                  <v-btn icon color="error" variant="tonal" size="x-small" class="ms-1"
                         @click="confirmDialog = true; itemToDelete = item.id">
                    <TrashIcon size="20"/>
                  </v-btn>
                </v-list-item>

              </v-list>
            </v-menu>
          </div>
        </template>
      </v-data-table-server>
    </v-col>
  </v-row>
  <ConformationModal v-model:dialog="confirmDialog" @confirm-delete="handleDeleteConfirmation" />
  <CollectionProduct ref="collectionProductRef" @reset="resetHandler"/>
  <CollectionForm ref="collectionFromRef" @reset="resetHandler"/>
  <SizeChartLocalize ref="sizeChartLocalizeRef"/>
  <SizeChartCopy ref="sizeChartCopyRef" @saved="resetHandler"/>

</template>

<script setup lang="ts">
import {ref, watch} from "vue";
import {deleteItem} from "~/utils/helpers/functions";
import {CirclePlusIcon, PencilIcon, TrashIcon, LanguageIcon, CopyIcon} from "vue-tabler-icons";
import ConformationModal from "~/components/modals/ConformationModal.vue";
import CollectionProduct from "~/components/product/collection/CollectionProduct.vue";
import CollectionForm from "~/components/product/size-chart/CollectionForm.vue";

import SizeChartLocalize from "~/components/product/size-chart/SizeChartLocalize.vue";
import SizeChartCopy from "~/components/product/size-chart/SizeChartCopy.vue";
import { useProductChartStore } from "~/stores/products/product-chart";


const snackbar = useSnackbar();
const store = useProductChartStore();
const page = ref({title: "Size Chart"});

const breadcrumbs = ref([
  {
    text: "Dashboard",
    disabled: false,
    to: "/dashboard",
  },
  {
    text: "Size Chart",
    disabled: true,
    to: "",
  },
]);
const confirmDialog = ref(false)
const options = ref({
  page: 1,
  itemsPerPage: 20,
  sortBy: ['id'],
  sortDesc: [false],
  search: '',
});
const loading = ref(false);
const products = ref([]);


const headers = ref([
  {title: 'Name', align: 'start', sortable: false, key: 'name'},
  {title: 'Action', key: 'action', align: 'end', sortable: false},
])

const collectionFromRef = ref<InstanceType<typeof CollectionForm>>();

const sizeChartLocalizeRef = ref<InstanceType<typeof SizeChartLocalize>>();

const translateItem = (item) => {
  sizeChartLocalizeRef.value?.show(item);
}

const addNew = () => {
  collectionFromRef.value?.show();
};

const editHandler = (item) => {
  collectionFromRef.value?.edit(item.id, {
    title: item.name,
    note: item.note,
    payload: item.values,
    chartImages: item?.chartImages,
  },
  item.imageId || undefined,
);
};

const sizeChartCopyRef = ref<InstanceType<typeof SizeChartCopy>>();
const copyChartItem = (item) => {
  sizeChartCopyRef.value?.show(item);
}

const resetHandler = () => {
  fetchProductList();
};


const search = ref('')
const name = ref('')
const sku = ref('')


const onClick = () => {
  if (!search.value) return;
  fetchProductList();
};

const clearSearch = () => {
  search.value = '';
  fetchProductList();
};

const fetchProductList = async () => {
  const {page, itemsPerPage, sortBy, sortDesc, search} = options.value;
  const order = sortDesc[0] ? 'DESC' : 'ASC';
  const sortKey = sortBy[0] || 'id';
  loading.value = true;
  await store.getCharts(order, page, itemsPerPage, sortKey, search);
  products.value = store.charts;
  loading.value = false;
};

const fakeReceiver = ref([]);
const removeCollection = (id: number) => {

  confirmDialog.value = true;

  deleteItem(`size-chart/`, id, fakeReceiver.value)
  .then(res => {
      if (res) {
          snackbar.add({
          type: 'success',
          text: 'Chart deleted successfully!',
          });

      } else {
          snackbar.add({
          type: 'error',
          text: 'Failed to delete Chart.',
          });
      }
      fetchProductList();
  }).finally(() => {
      confirmDialog.value = false;
  });
}

const itemToDelete = ref<number | null>(null);
const handleDeleteConfirmation = async () => {
  if (itemToDelete.value !== null) {
    const productId = itemToDelete.value;



    removeCollection(productId);
  }
};

const filterProducts = (filterKey, value, listKey) => {
  const normalizedValue = value.toLowerCase();
  let filteredData = [];

  if (normalizedValue.length > 0) {
    filteredData = store.charts.filter(item => {
      const itemValue = filterKey(item).toLowerCase();
      return itemValue.includes(normalizedValue);
    });
  } else {
    filteredData = store.charts;
  }

  products.value = filteredData;
};

watch(name, (val) => {
  filterProducts(item => item.name, val, 'products');
});
watch(sku, (val) => {
  filterProducts(item => item.sku, val, 'products');
});

</script>
