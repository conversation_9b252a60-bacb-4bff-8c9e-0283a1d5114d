<template>
  <SharedBaseBreadcrumb
      :title="page.title"
      :breadcrumbs="breadcrumbs"
  ></SharedBaseBreadcrumb>
  <v-row>
    <v-col cols="12">
      <SharedUiParentCard>
        <v-data-table-server
            v-model:page="options.page"
            v-model:items-per-page="options.itemsPerPage"
            :headers="headers"
            :items="items"
            :search="options.search"
            :loading="loading"
            item-value="name"
            :items-length="store.pagination.itemCount"
            @update:options="fetchItems">
          <template v-slot:item.imageGalleryId="{ item }">
            <div v-if="item.imageGallery">
              <v-img :src="item.imageGallery.imageUrl" width="80"/>
            </div>
          </template>
          <template v-slot:item.country="{ item }">
            {{ item.country ? item.country.code : '-' }}
          </template>
          <template v-slot:item.action="{ item }">
            <div class="text-end">
              <v-menu>
                <template v-slot:activator="{ props }">
                  <v-btn class="me-2" variant="tonal" size="small" icon="mdi-dots-vertical" v-bind="props"></v-btn>
                </template>
                <v-list>
                  <v-list-item>
                    <v-btn icon color="warning" class="me-2" variant="tonal" size="small"
                           @click="editItem(item, banner)">
                      <PencilIcon size="20"/>
                    </v-btn>
                  </v-list-item>

                </v-list>
              </v-menu>
            </div>
          </template>
        </v-data-table-server>
        <v-dialog
            v-model="dialog"
            max-width="600"
        >
          <v-card title="Edit Sticky message">
            <v-card-text>
              <v-divider></v-divider>
              <v-form v-model="form" @submit.prevent="onSubmit" class="mt-5 mb-5">


                <FormElementsImageViewerSingle :image-id="banner.imageGalleryId" :image-rules="imageRules"
                                               :max-size="200000"
                                               :required-height="817" :required-width="1800" image-model="home"
                                               :multiple="false"
                                               @selected-image-id="handleSelectedImage"/>


                <v-divider class="my-5"></v-divider>

                <v-btn :disabled="!form" :loading="loading" color="primary" variant="tonal" class="px-6" type="submit">
                  Update
                </v-btn>

                <v-btn color="error" variant="tonal" class="px-6 ms-2" @click="resetForm"> Close
                </v-btn>

              </v-form>
            </v-card-text>

          </v-card>
        </v-dialog>
      </SharedUiParentCard>
    </v-col>
  </v-row>
</template>

<script setup lang="ts">

import {PencilIcon} from "vue-tabler-icons";
import {ref} from 'vue';
import {homePageSettings} from "~/stores/settings/HomePage";
import {editPayloadItem} from "~/utils/helpers/functions";

const store = homePageSettings();
const snackbar = useSnackbar();

const page = ref({title: "Main Banner"});

const breadcrumbs = ref([
  {
    text: "Dashboard",
    disabled: false,
    to: "/dashboard",
  },
  {
    text: "Main Banner",
    disabled: true,
    to: "",
  },
]);


const items = ref([]);
const form = ref(false);
const loading = ref(false);

const options = ref({
  page: 1,
  itemsPerPage: 20,
  sortBy: ['name'],
  sortDesc: [false],
  search: '',
  type: 'MAIN_BANNER'
});

const dialog = ref(false);


const imageRules = [
  {text: 'Maximum image size is 200KB', icon: 'mdi-check'},
  {text: 'Image dimension is 1800 x 817', icon: 'mdi-check'},
  {text: 'Image type should be WEBP', icon: 'mdi-check'},
]

const headers = ref([
  {title: 'Image', align: 'start', sortable: false, key: 'imageGalleryId'},
  {title: 'Country', align: 'start', sortable: false, key: 'country'},
  {title: 'Action', key: 'action', align: 'center', sortable: false},
])

const fetchItems = async () => {
  const {page, itemsPerPage, sortBy, sortDesc, search, type} = options.value;
  const order = sortDesc[0] ? 'DESC' : 'ASC';
  const sortKey = sortBy[0] || 'name';
  loading.value = true;
  await store.getSettingData(order, page, itemsPerPage, sortKey, search, type);
  items.value = store.settings;
  loading.value = false;
}


const banner = ref({
  id: null,
  title: '',
  subTitle: '',
  redirectLink: '',
  imageGalleryId: null
})

const onSubmit = () => {
  if (!form.value) {
    return;
  }
  loading.value = true
  store.addUpdateSetting(banner.value).then((res: any) => {
    snackbar.add({
      type: res.success ? 'success' : 'error',
      text: res.message
    })

    if (res.success) {
      resetForm();
      fetchItems();
    }

  }).catch(err => {
    console.log(err)
  })

  loading.value = false
}

const resetForm = () => {
  banner.value.id = null;
  banner.value.title = '';
  banner.value.redirectLink = '';
  banner.value.imageGalleryId = null;
  dialog.value = false;
}

const handleSelectedImage = (value: any) => {
  banner.value.imageGalleryId = value
}

const editItem = (item: any, banner: any) => {
  editPayloadItem(item, banner);
  dialog.value = true;
}


</script>
