<template>
  <SharedBaseBreadcrumb
      :title="page.title"
      :breadcrumbs="breadcrumbs"
  ></SharedBaseBreadcrumb>
  <v-row>
    <v-col cols="6">
      <SharedUiParentCard title="Manage Logo">
        <v-form v-model="logo_form" @submit.prevent="onSubmitLogoForm">


          <FormElementsImageViewerSingle :image-id="manageLogo.imageGalleryId" :image-rules="logoImageRules"
                                         :max-size="100000"
                                         :required-height="56" :required-width="288" image-model="pantoneclo-img"
                                         :multiple="false"
                                         @selected-image-id="handleSelectedLogo"/>

          <v-divider class="my-5"></v-divider>

          <v-btn :disabled="!logo_form" :loading="loading" color="primary" variant="tonal" class="px-6" type="submit">
            Update
          </v-btn>

        </v-form>
      </SharedUiParentCard>
    </v-col>

    <v-col cols="6">
      <SharedUiParentCard title="Manage Youtube Video">
        <v-form v-model="youtube_link_form" @submit.prevent="onSubmitYoutubeForm">


          <FormElementsCommonFieldContainer label="Video Link" :required="true">
            <v-text-field v-model="manageYoutubeLink.redirectLink"></v-text-field>
          </FormElementsCommonFieldContainer>


          <v-btn :disabled="!youtube_link_form" :loading="loading" color="primary" variant="tonal" class="px-6" type="submit">
            Update
          </v-btn>

        </v-form>
      </SharedUiParentCard>
    </v-col>
    <v-col cols="6">
      <SharedUiParentCard title="Return Policy">
        <v-form v-model="return_policy_form" @submit.prevent="onSubmitReturnPolicyForm">


          <FormElementsCommonFieldContainer label="Return Policy" :required="true">
            <v-text-field v-model="manageReturnPolicy.value" :rules="[val => REQUIRED_RULE(val)]"></v-text-field>
          </FormElementsCommonFieldContainer>


          <v-btn :disabled="!return_policy_form" :loading="loading" color="primary" variant="tonal" class="px-6" type="submit">
            Update
          </v-btn>

        </v-form>
      </SharedUiParentCard>
    </v-col>
    <v-col cols="6" v-if="metaReady">
      <LazyProductMeta @onSaveMeta="saveHomeMeta"
                       :id="meta.id"
                       :meta-title="meta.metaTitle"
                       :metaDescription="meta.metaDescription"
                       :galleryImageId="meta.galleryImageId"
                       :meta-type="meta.metaType"
                       :locale="meta.locale"
                       showLocale />

    </v-col>
  </v-row>
</template>

<script setup lang="ts">

import {onMounted, ref} from 'vue';
import {homePageSettings} from "~/stores/settings/HomePage";
import {REQUIRED_RULE} from "~/utils/formRules";
import {reach} from "yup";
import {saveOrUpdateData} from "~/utils/helpers/functions";

const store = homePageSettings();
const snackbar = useSnackbar();

const page = ref({title: "Setting"});

const breadcrumbs = ref([
  {
    text: "Dashboard",
    disabled: false,
    to: "/dashboard",
  },
  {
    text: "Setting",
    disabled: true,
    to: "",
  },
]);
// header message start
const headerMessage = ref({
  id: null,
  title: '',
  type: 'HEADER_STICKY'
})
const header_message_form = ref(false);
const loading = ref(false);
const headerStickyMessage = ref([]);

const fetchHeaderSticky = async () => {
  loading.value = true;
  await store.getSettingData('ASC', 1, 5, '', '', 'HEADER_STICKY');
  headerStickyMessage.value = store.settings;
  if (headerStickyMessage.value.length > 0) {
    headerMessage.value.title = headerStickyMessage.value[0].title;
    headerMessage.value.id = headerStickyMessage.value[0].id;
  }
  loading.value = false;
};

const onSubmitHeaderMessageFrom = () => {
  if (!header_message_form.value) {
    return;
  }
  loading.value = true
  store.addUpdateSetting(headerMessage.value).then((res: any) => {
    snackbar.add({
      type: res.success ? 'success' : 'error',
      text: res.message
    })

  }).catch(err => {
    console.log(err)
  })
  loading.value = false
}

// logo start
const manageLogo = ref({
  id: null,
  type: 'LOGO',
  title: 'Logo',
  imageGalleryId: null
})
const logo_form = ref(false);
const logoItems = ref([]);

const fetchLogo = async () => {
  loading.value = true;
  await store.getSettingData('ASC', 1, 5, '', '', 'LOGO');
  logoItems.value = store.settings;
  if (logoItems.value.length > 0) {
    manageLogo.value.imageGalleryId = logoItems.value[0].imageGalleryId;
    manageLogo.value.id = logoItems.value[0].id;
  }
  loading.value = false;
};

const logoImageRules = [
  {text: 'Maximum image size is 200KB', icon: 'mdi-check'},
  {text: 'Image dimension is 288 x 56', icon: 'mdi-check'},
  {text: 'Image type should be WEBP', icon: 'mdi-check'},
]

const handleSelectedLogo = (value: any) => {
  manageLogo.value.imageGalleryId = value
}

const onSubmitLogoForm = () => {
  if (!logo_form.value) {
    return;
  }
  loading.value = true
  store.addUpdateSetting(manageLogo.value).then((res: any) => {
    snackbar.add({
      type: res.success ? 'success' : 'error',
      text: res.message
    })

  }).catch(err => {
    console.log(err)
  })
  loading.value = false
}
// header message end


// banner start
const manageBanner = ref({
  id: null,
  type: 'MAIN_BANNER',
  title: 'MAIN_BANNER',
  imageGalleryId: null
})

const banner_form = ref(false);
const bannerItems = ref([]);

const fetchBanner = async () => {
  loading.value = true;
  await store.getSettingData('ASC', 1, 5, '', '', 'MAIN_BANNER');
  bannerItems.value = store.settings;
  if (bannerItems.value.length > 0) {
    manageBanner.value.imageGalleryId = bannerItems.value[0].imageGalleryId;
    manageBanner.value.id = bannerItems.value[0].id;
  }
  loading.value = false;
};

const bannerImageRules = [
  {text: 'Maximum image size is 200KB', icon: 'mdi-check'},
  {text: 'Image dimension is 452 X 450', icon: 'mdi-check'},
  {text: 'Image type should be WEBP', icon: 'mdi-check'},
]

const handleSelectedBanner = (value: any) => {
  manageBanner.value.imageGalleryId = value
}

const onSubmitBannerForm = () => {
  if (!banner_form.value) {
    return;
  }
  loading.value = true
  store.addUpdateSetting(manageBanner.value).then((res: any) => {
    snackbar.add({
      type: res.success ? 'success' : 'error',
      text: res.message
    })

  }).catch(err => {
    console.log(err)
  })
  loading.value = false
}
// header message end


// youtube link start
const manageYoutubeLink = ref({
  id: null,
  type: 'YOUTUBE',
  title: 'YOUTUBE',
  redirectLink: null
})
const youtube_link_form = ref(false);
const youtubeLinkItems = ref([]);
const fetchYoutubeLink = async () => {
  loading.value = true;
  await store.getSettingData('ASC', 1, 5, '', '', 'YOUTUBE');
  youtubeLinkItems.value = store.settings;
  if (youtubeLinkItems.value.length > 0) {
    manageYoutubeLink.value.redirectLink = youtubeLinkItems.value[0].redirectLink;
    manageYoutubeLink.value.id = youtubeLinkItems.value[0].id;
  }
  loading.value = false;
};

const onSubmitYoutubeForm = () => {
  if (!youtube_link_form.value) {
    return;
  }
  loading.value = true
  store.addUpdateSetting(manageYoutubeLink.value).then((res: any) => {
    snackbar.add({
      type: res.success ? 'success' : 'error',
      text: res.message
    })

  }).catch(err => {
    console.log(err)
  })
  loading.value = false
}
// youtube link end

// return policy start
const manageReturnPolicy = ref({
  id: null,
  key: 'RETURN_POLICY',
  value: null,
})

const return_policy_form = ref(false);

const fetchReturnPolicy = async () => {
  loading.value = true;
  await store.getReturnPolicyData('RETURN_POLICY');
  if (store?.returnPolicy) {
    manageReturnPolicy.value.value = store?.returnPolicy.value;
    manageReturnPolicy.value.id = store?.returnPolicy.id;
  }
  loading.value = false;
};

const onSubmitReturnPolicyForm = () => {
  if (!return_policy_form.value) {
    return;
  }
  loading.value = true;
  store.addUpdateReturnPolicy(manageReturnPolicy.value).then((res: any) => {
    snackbar.add({
      type: res.success ? 'success' : 'error',
      text: res.message
    })

  }).catch(err => {
    console.log(err)
  })
  loading.value = false
}
// return policy end

const meta = reactive({
  id : 0,
  metaTitle : '',
  metaDescription: '',
  galleryImageId: 0,
  metaType: 'home',
  locale: {},
})

const saveHomeMeta = async ( val: any ) => {
  const response = await saveOrUpdateData(val, 'seoMeta');
    snackbar.add({
      type: response?.isSuccess ? 'success' : 'error',
      text: response?.messasge
    })
}

const metaReady = ref(false);
const fetchSeoMeta = async () => {
  const response = await store.getSeoMetaData('home');
  if( response.isSuccess ) {
    meta.id = response?.data?.id;
    meta.metaTitle = response?.data?.metaTitle;
    meta.metaDescription = response?.data?.metaDescription;
    meta.galleryImageId = response?.data?.galleryImageId;
    meta.locale = response?.data?.locale;
  }

  metaReady.value = true;
};

onMounted(() => {
  // fetchHeaderSticky();
  fetchLogo();
  // fetchBanner();
  fetchYoutubeLink();
  fetchReturnPolicy();
  fetchSeoMeta();
});
</script>
