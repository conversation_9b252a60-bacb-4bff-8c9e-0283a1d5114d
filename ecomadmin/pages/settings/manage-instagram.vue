<template>
  <SharedBaseBreadcrumb
      :title="page.title"
      :breadcrumbs="breadcrumbs"
  ></SharedBaseBreadcrumb>
  <v-row>
    <v-col cols="4">
      <SharedUiParentCard :title="partner.id ? 'Edit ' + partner.title : 'Add New Instagram'">
        <v-form v-model="form" @submit.prevent="onSubmit">

          <FormElementsImageViewerSingle :image-id="partner.imageGalleryId" :image-rules="imageRules" :max-size="100000"
                                         :required-height="300" :required-width="263" image-model="instagram"
                                         :multiple="false"
                                         @selected-image-id="handleSelectedImage"/>

          <FormElementsCommonFieldContainer label="Title">
            <v-text-field v-model="partner.title" :rules="[ val => REQUIRED_RULE(val) ]"></v-text-field>
          </FormElementsCommonFieldContainer>

          <FormElementsCommonFieldContainer label="URL">
            <v-text-field v-model="partner.redirectLink"></v-text-field>
          </FormElementsCommonFieldContainer>


          <v-divider class="my-5"></v-divider>

          <v-btn :disabled="!form" :loading="loading" color="primary" variant="tonal" class="px-6" type="submit"> {{
              partner.id ? 'Update' : 'Add New'
            }} Instagram
          </v-btn>

          <v-btn color="error" variant="tonal" class="px-6 ms-2" @click="resetForm"> Reset
          </v-btn>

        </v-form>
      </SharedUiParentCard>
    </v-col>
    <v-col cols="8">
      <SharedUiParentCard>

        <div class="d-flex justify-space-between align-center">
          <h2>Instagram Posts</h2>
        </div>

        <v-data-table-server
            v-model:page="options.page"
            v-model:items-per-page="options.itemsPerPage"
            :headers="headers"
            :items="ins_partners"
            :items-length="store.pagination.itemCount"
            :search="options.search"
            :loading="loading"
            item-value="name" @update:options="fetchItems">


          <template v-slot:item.status="{ item }">
            <v-chip :color="item?.isActive ? 'success' : 'error'" variant="elevated">
              {{ item?.isActive ? 'Active' : 'Inactive' }}
            </v-chip>
          </template>
          <template v-slot:item.imageGalleryId="{ item }">
            <div v-if="item.imageGallery">
              <v-img :src="item.imageGallery.imageUrl" width="80"/>
            </div>
          </template>
          <template v-slot:item.action="{ item }">
            <div class="text-end">

              <v-menu>
                <template v-slot:activator="{ props }">
                  <v-btn class="me-2" variant="tonal" size="small" icon="mdi-dots-vertical" v-bind="props"></v-btn>
                </template>
                <v-list>
                  <v-list-item>
                    <v-btn icon color="warning" class="me-2" variant="tonal" size="small"
                           @click="editItem(item, partner)">
                      <PencilIcon size="20"/>
                    </v-btn>
                  </v-list-item>
                  <v-list-item>
                    <v-btn icon color="error" class="me-2" variant="tonal" size="small" @click="confirmDialog = true; itemToDelete = item.id">
                      <TrashIcon size="20"/>
                    </v-btn>
                  </v-list-item>
                </v-list>
              </v-menu>
            </div>
          </template>
        </v-data-table-server>

      </SharedUiParentCard>
    </v-col>
  </v-row>
  <ConformationModal v-model:dialog="confirmDialog" @confirm-delete="handleDeleteConfirmation" />

</template>

<script setup lang="ts">

import {PencilIcon, TrashIcon} from "vue-tabler-icons";
import {ref, watch} from 'vue';
import {editPayloadItem} from "~/utils/helpers/functions";
import {REQUIRED_RULE} from "~/utils/formRules";
import {homePageSettings} from "~/stores/settings/HomePage";
import ConformationModal from "~/components/modals/ConformationModal.vue";

const store = homePageSettings();
const snackbar = useSnackbar();
const page = ref({title: "Manage Instagram"});

const breadcrumbs = ref([
  {
    text: "Dashboard",
    disabled: false,
    to: "/dashboard",
  },
  {
    text: "Instagram Posts",
    disabled: true,
    to: "",
  },
]);

const snackbarData = ref({
  message: '',
  type: 'success'
})

const form = ref(false);
const loading = ref(false);
const options = ref({
  page: 1,
  itemsPerPage: 20,
  sortBy: ['name'],
  sortDesc: [false],
  search: '',
  type: 'INSTAGRAM'
});
const ins_partners = ref([]);
const partner = ref({
  id: null,
  title: '',
  subTitle: '',
  redirectLink: '',
  imageGalleryId: null,
  type: 'INSTAGRAM'
})
const imageRules = [
  {text: 'Maximum image size is 200KB', icon: 'mdi-check'},
  {text: 'Image dimension is 263 x 300', icon: 'mdi-check'},
  {text: 'Image type should be WEBP', icon: 'mdi-check'},
]

const headers = ref([
  {title: 'Image', align: 'start', sortable: false, key: 'imageGalleryId'},
  {title: 'Title', align: 'start', sortable: false, key: 'title'},
  {title: 'URL', align: 'start', sortable: false, key: 'redirectLink'},
  {title: 'Status', key: 'status', sortable: false, align: 'start'},
  {title: 'Action', key: 'action', align: 'center', sortable: false},
])

const fetchItems = async () => {
  const {page, itemsPerPage, sortBy, sortDesc, search, type} = options.value;
  const order = sortDesc[0] ? 'DESC' : 'ASC';
  const sortKey = sortBy[0] || 'name';
  loading.value = true;
  await store.getInstagramPartner(order, page, itemsPerPage, sortKey, search, type);
  ins_partners.value = store.ins_partners;
  loading.value = false;
}


const onSubmit = () => {
  if (!form.value) {
    return;
  }

  loading.value = true

  store.addUpdateInstagramPartner(partner.value).then((res: any) => {
    snackbar.add({
      type: res.success ? 'success' : 'error',
      text: res.message
    })
    if (res.success) {
      resetForm();
      fetchItems();
    }
  }).catch(err => {
    console.log(err)
  })

  loading.value = false
}

const deleteItem = (id: number) => {
  store.deletePartnerInstragram(id).then((res: any) => {
    console.log('res', res)
    snackbar.add({
      type: res.success ? 'success' : 'error',
      text: res.message
    })
    fetchItems();
  }).catch(err => {
    console.log(err)
  })
}



const resetForm = () => {
  partner.value.id = null;
  partner.value.title = '';
  partner.value.redirectLink = '';
  partner.value.imageGalleryId = null
}

const handleSelectedImage = (value: any) => {
  partner.value.imageGalleryId = value
}

const editItem = (item: any, brand: any) => {
  editPayloadItem(item, brand);
  fetchItems();
}

const confirmDialog = ref(false);
const itemToDelete = ref<number | null>(null);
const handleDeleteConfirmation = async () => {
  if (itemToDelete.value !== null) {
    const productId = itemToDelete.value;
    // Call the delete product API
    const res = await store.deletePartnerInstragram(productId);
    // Handle response
    if (res) {
      snackbar.add({
        type: 'success',
        text: 'Instagram uct deleted successfully!',
      });
      confirmDialog.value = false;

    } else {
      snackbar.add({
        type: 'error',
        text: 'Failed to delete Instagram.',
      });
    }
  }
};




</script>
