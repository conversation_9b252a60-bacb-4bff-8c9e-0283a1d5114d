<template>
  <div>
    <div class="d-flex justify-space-between align-center">
      <h3>Users</h3>
      <v-btn class="ma-2" color="primary" @click="dialog = true">
        Create New
        <v-icon icon="mdi-checkbox-marked-circle" end></v-icon>
      </v-btn>
    </div>
    <div>
      <v-data-table-server v-model:items-per-page="itemsPerPage" :headers="headers" :items="users"
        :items-length="store.pagination.itemCount"  @click="sortBy(header.key)" :loading="loading" :search="search" item-value="name"
        @update:options="fetchUserList">

        <template v-slot:thead>

          <tr>
            <td>
              <v-text-field v-model="firstName" class="ma-1" density="compact" placeholder="Search name..."
                hide-details></v-text-field>
            </td>
            <td>
              <v-text-field v-model="lastName" class="ma-1" density="compact" placeholder="Search name..."
                hide-details></v-text-field>
            </td>
            <td>
              <v-text-field v-model="email" class="ma-1" density="compact" placeholder="Search Email..." type="text"
                hide-details></v-text-field>
            </td>
            <td>
              <v-text-field v-model="phone_number" class="ma-1" density="compact" placeholder="Search phone number..."
                type="number" hide-details></v-text-field>
            </td>
          </tr>
        </template>
        <template v-slot:item.action="{ item }">
          <div class="text-end">
            <v-btn icon color="warning" class="me-2" variant="tonal" size="small" @click="editItem(item)">
              <PencilIcon size="20" />
            </v-btn>
            <v-btn icon color="error" class="me-2" variant="tonal" size="small" @click="deleteItem(item)">
              <TrashIcon size="20" />
            </v-btn>
            <v-btn icon color="danger" class="me-2" variant="tonal" size="small" @click="viewItem(item)">
              <EyeCheckIcon size="20" />
            </v-btn>

          </div>
        </template>
      </v-data-table-server>
    </div>
    <v-dialog v-model="dialog" max-width="800">
      <v-form>

        <v-card prepend-icon="mdi-account" title="Create User">
          <v-card-text>
            <v-row dense>
              <v-col cols="12" md="6" sm="6">
                <v-text-field label="First name*" v-model="form.name" required></v-text-field>
              </v-col>

              <v-col cols="12" md="6" sm="6">
                <v-text-field label="Phone Number*" v-model="form.phone_number" required></v-text-field>
              </v-col>



              <v-col cols="12" md="6" sm="6">
                <v-text-field label="Email*" v-model="form.email" required></v-text-field>
              </v-col>

              <v-col cols="12" md="6" sm="6">
                <v-text-field label="Password*" v-model="form.password" type="password" required></v-text-field>
              </v-col>

              <v-col cols="12" md="6" sm="6">
                <v-text-field label="Confirm Password*" v-model="form.confirm_password" type="password"
                  required></v-text-field>
              </v-col>




            </v-row>

            <small class="text-caption text-medium-emphasis">*indicates required field</small>
          </v-card-text>

          <v-divider></v-divider>

          <v-card-actions>
            <v-spacer></v-spacer>

            <v-btn text="Close" variant="plain" @click="dialog = false"></v-btn>

            <v-btn color="primary" v-if="is_view" text="Save" variant="tonal" @click="dialog = false"></v-btn>
          </v-card-actions>
        </v-card>
      </v-form>
    </v-dialog>
    <v-dialog v-model="delete_dialog" max-width="400" persistent>
      <v-card prepend-icon="mdi-delete-empty-outline" text="Are you sure you want to delete this item?"
        title="Are you sure?">
        <template v-slot:actions>
          <v-spacer></v-spacer>

          <v-btn @click="delete_dialog = false">
            No
          </v-btn>

          <v-btn @click="delete_dialog = false">
            Yes
          </v-btn>
        </template>
      </v-card>
    </v-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { PencilIcon, TrashIcon, EyeCheckIcon } from 'vue-tabler-icons'
import { useUserStore } from "~/stores/users";
const store = useUserStore();
const getUsers = computed(() => store.users);
const users = ref([]);
const dialog = ref(false)
const delete_dialog = ref(false)
const is_view = ref(true)

const form = ref({
  name: '',
  phone_number: '',
  email: '',
  password: '',
  confirm_password: '',
})

const sortKey = ref('');
const sortDirection = ref('asc');

const itemsPerPage = ref(5)
const page = ref(1)
const order = ref('desc')

const headers = ref([
  { title: 'First Name', align: 'start', sortable: true, key: 'firstName' },
  { title: 'Last Name', align: 'start', sortable: true, key: 'lastName' },
  { title: 'Email', key: 'email', sortable: true, align: 'start' },
  { title: 'Phone Number', key: 'phone', sortable: true, align: 'start' },
  { title: 'User Type', key: 'userType', sortable: true, align: 'start' },
  { title: 'Action', key: 'action', align: 'center', sortable: false },
])

const loading = ref(true)
const firstName = ref('')
const lastName = ref('')
const email = ref('')
const phone_number = ref('')
const search = ref('')
const itemToDelete = ref(null)

const fetchUserList = async (page = 1, order = "ASC", take = 5) => {
  await store.getUsers(order, page.page, take);
  users.value = store.users;
  loading.value = false;
};


const sortBy = (key: string) => {
  if (!sortKey.value || sortKey.value !== key) {
    sortKey.value = key;
    sortDirection.value = 'asc';
  } else {
    sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc';
  }


  // Sort the data based on sortKey and sortDirection
  users.value = users.value.sort((a, b) => {
    if (sortDirection.value === 'asc') {
      return a[key] < b[key] ? -1 : a[key] > b[key] ? 1 : 0;
    } else {
      return a[key] > b[key] ? -1 : a[key] < b[key] ? 1 : 0;
    }
  });
};



const items = ref(getUsers);
onMounted(async () => {
  await fetchUserList();
  users.value = store.users;
});



const openCreateDialog = () => {
  resetForm()
  dialog.value = true
  is_view.value = false
}

const closeDialog = () => {
  dialog.value = false
  resetForm()
}

const resetForm = () => {
  form.value = {
    name: '',
    phone_number: '',
    email: '',
    password: '',
    confirm_password: '',
  }
  is_view.value = true
}

const searchTerm = ref('');

const filteredCollection = computed(() => {
  return items.value.filter(item => {
    const searchTextLower = searchTerm.value.toLowerCase();
    return (
      item.firstname.toLowerCase().includes(searchTextLower) ||
      item.email.toLowerCase().includes(searchTextLower) ||
      item.phone.toLowerCase().includes(searchTextLower)
    );
  });
});


const editItem = (item: any) => {
  form.value = { ...item }
  dialog.value = true
  is_view.value = false
}



const viewItem = (item: any) => {
  form.value = { ...item }
  dialog.value = true
  is_view.value = true
}

const deleteItem = (item: any) => {
  itemToDelete.value = item
  delete_dialog.value = true
}

const confirmDelete = () => {
  // Handle delete logic here
  delete_dialog.value = false
  itemToDelete.value = null
}

const saveUser = () => {
  // Handle save logic here
  console.log('User saved:', form.value)
  dialog.value = false
  resetForm()
}


watch(firstName, (val, oldValue) => {
  let arraydata = [];
  users.value.filter((item) => {
    if (item.firstName.toLowerCase().includes(val)) {
      arraydata.push(item);
    }

  })
  if (val.length === 0) {
    arraydata = store.users;
  }
  users.value = arraydata;
})

watch(lastName, (val, oldValue) => {
  let arraydata = [];
  users.value.filter((item) => {
    if (item.lastName.toLowerCase().includes(val)) {
      arraydata.push(item);
    }
  })
  if (val.length === 0) {
    arraydata = store.users;
  }
  users.value = arraydata;
})

watch(email, (val, oldValue) => {
  let arraydata = [];
  users.value.filter((item) => {
    if (item.email.toLowerCase().includes(val)) {
      arraydata.push(item);
    }
  })
  if (val.length === 0) {
    arraydata = store.users;
  }
  users.value = arraydata;
})


watch(phone_number, (val, oldValue) => {
  let arraydata = [];
  users.value.filter((item) => {
    if (item.phone.toLowerCase().includes(val)) {
      arraydata.push(item);
    }
  })
  if (val.length === 0) {
    arraydata = store.users;
  }
  users.value = arraydata;
})


watch(dialog, (val) => {
  if (!val) {
    resetForm()
  }
})

</script>
