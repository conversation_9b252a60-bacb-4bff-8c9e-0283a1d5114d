<template>
  <SharedBaseBreadcrumb
      :title="page.title"
      :breadcrumbs="breadcrumbs"
  ></SharedBaseBreadcrumb>
  <v-row>
    <v-col cols="6">
      <SharedUiParentCard>
        <v-form @submit.prevent="onSubmitYoutubeForm" ref="changePasswordForm" v-model="changePasswordForm">
          <FormElementsCommonFieldContainer label="Old Password" :required="true">
            <v-text-field v-model="form.oldPassword" type="password" :rules="[val => REQUIRED_RULE(val)]"></v-text-field>
          </FormElementsCommonFieldContainer>

          <FormElementsCommonFieldContainer label="New Password" :required="true">
            <v-text-field v-model="form.password" type="password" :rules="[val => REQUIRED_RULE(val)]"></v-text-field>
          </FormElementsCommonFieldContainer>
          <br>
          <v-btn :disabled="!changePasswordForm" :loading="loading" color="primary" variant="tonal" class="px-6" type="submit">
            Update
          </v-btn>

        </v-form>
      </SharedUiParentCard>
    </v-col>

  </v-row>
</template>

<script setup lang="ts">

import {ref} from 'vue';
import {REQUIRED_RULE} from "~/utils/formRules";
import { useUserStore } from "~/stores/users";
const store = useUserStore();
const snackbar = useSnackbar();

const currentUser = useCookie<CurrentAdminUser>('PantonecloAdminUser');

const page = ref({title: "Change Password"});
const breadcrumbs = ref([
  {
    text: "Dashboard",
    disabled: false,
    to: "/dashboard",
  },
  {
    text: "Change Password",
    disabled: true,
    to: "",
  },
]);
const changePasswordForm = ref(false);
const loading = ref(false);
const form = ref({
  oldPassword: '',
  password: '',
})

const onSubmitYoutubeForm = () => {
  console.log('ddd', currentUser.value?.id)
  const changePass ={
    id: currentUser.value?.id,
    oldPassword: form.value.oldPassword,
    password: form.value.password
  }
  loading.value = true
  store.changePassword(changePass).then((res: any) => {
    snackbar.add({
      type: res.data.isSuccess ? 'success' : 'error',
      text: res.data.messasge
    })
    if (res.data.isSuccess) {
      navigateTo(`/dashboard`);
    }
  }).catch(err => {
    console.log(err)
  })
  loading.value = false
}

</script>
