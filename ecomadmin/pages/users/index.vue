<template>
  <div>
    
    <SharedBaseBreadcrumb :title="breadcrumb_page.title" :breadcrumbs="breadcrumbs">
    </SharedBaseBreadcrumb>
    <template v-if="!permissionStore.hasModulePermission(`${DynamicModuleEnum.USER}:${DynamicPermissionEnum.READ_MANY}`)">
      <SharedUiParentCardSolid class="mt-6" title="Product Details">
        <h3 class="text-h3 text-error border border-dotted pa-5 text-center">You don't have permission to see user list</h3>
      </SharedUiParentCardSolid>
    </template>
    <template v-else>
      <div class="d-flex justify-space-between align-center">
        <div class="w-50">
          <v-text-field :loading="store.searchLoading" append-inner-icon="mdi-magnify" density="compact" label="Search users"
              variant="outlined" hide-details single-line @click:append-inner="onClick" v-model="options.search"
              clearable @click:clear="clearSearch">
          </v-text-field>
        </div>
        <v-btn class="ma-2" color="primary" @click="dialog = true">
          Create New
          <v-icon icon="mdi-checkbox-marked-circle" end></v-icon>
        </v-btn>
      </div>

      <div>
        <v-data-table-server :page="options.page" v-model:items-per-page="options.itemsPerPage" :headers="headers" :items="users"
          :items-length="store.pagination.itemCount" :loading="loading" :search="options.search"
          item-value="name" @update:page="handleTablePageChange">

          <template v-slot:thead>
            <tr>
              <td>
                <v-text-field v-model="firstName" class="ma-1" density="compact" placeholder="Search name..." hide-details>
                </v-text-field>
              </td>
              <td>
                <v-text-field v-model="lastName" class="ma-1" density="compact" placeholder="Search name..." hide-details>
                </v-text-field>
              </td>
              <td>
                <v-text-field v-model="email" class="ma-1" density="compact" placeholder="Search Email..." type="text" hide-details>
                </v-text-field>
              </td>
              <td>
                <v-text-field v-model="phone_number" class="ma-1" density="compact" placeholder="Search phone number..." type="number" hide-details>
                </v-text-field>
              </td>
              <td>
                <v-text-field v-model="user_type" class="ma-1" density="compact" placeholder="Search user type..." hide-details>
                </v-text-field>
              </td>
            </tr>
          </template>

          <template v-slot:item.action="{ item }">
            <div class="text-end">
              
              <v-menu>
                <template v-slot:activator="{ props }">
                  <v-btn class="me-2" variant="flat" size="x-small" icon="mdi-dots-vertical" v-bind="props"></v-btn>
                </template>

                <v-list>
                  <v-list-item>
                    <v-btn icon="mdi-pencil" color="warning" class="me-2" variant="flat" size="x-small" @click="editItem(item)">
                    </v-btn>
                  </v-list-item>
                  <v-list-item v-if="roleList.length > 0">
                    <v-btn icon="mdi-shield-crown" color="warning" class="me-2" variant="flat" size="x-small" @click="roleEditItem(item)">
                    </v-btn>
                  </v-list-item>
                  <v-list-item v-if="permissionStore.hasModulePermission(`${DynamicModuleEnum.USER}:${DynamicPermissionEnum.SOFT_DELETE}`)">
                    <v-btn icon="mdi-delete" color="error" class="me-2" variant="flat" size="x-small" @click="deleteItem(item)">
                    </v-btn>
                  </v-list-item>
                  <!-- <v-list-item>
                    <v-btn icon color="danger" class="me-2" variant="flat" size="small" @click="viewItem(item)">
                      <EyeCheckIcon size="20"/>
                    </v-btn>
                  </v-list-item> -->

                </v-list>
              </v-menu>

            </div>
          </template>

          <template v-slot:item.userType="{ item }">
            {{ item?.userType?.userType }}
          </template>
          
          <template v-slot:item.role="{ item }">
            <v-chip v-if="item?.role" size="x-small" color="primary" variant="flat">{{ item?.role?.name }}</v-chip>
          </template>
          
        </v-data-table-server>
      </div>
      <v-dialog v-model="dialog" max-width="800">

        <v-form @submit.prevent="onSubmit">

          <v-card prepend-icon="mdi-account" title="Create User">
            <v-card-text>
              <v-row dense>
                <v-col cols="12" md="6" sm="6">
                  <FormElementsCommonFieldContainer label="First Name" :required="true">
                    <v-text-field v-model="form.firstName" :rules="[val => REQUIRED_RULE(val)]" density="compact"></v-text-field>
                  </FormElementsCommonFieldContainer>
                  <!--                <v-text-field label="First name*" v-model="form.firstName" required></v-text-field>-->
                </v-col>

                <v-col cols="12" md="6" sm="6">
                  <FormElementsCommonFieldContainer label="Last Name" :required="true">
                    <v-text-field v-model="form.lastName" :rules="[val => REQUIRED_RULE(val)]" density="compact"></v-text-field>
                  </FormElementsCommonFieldContainer>
                </v-col>

                <v-col cols="12" md="6" sm="6">
                  <FormElementsCommonFieldContainer label="Country Code" :required="true">
                    <v-text-field v-model="form.countryCode" :rules="[val => REQUIRED_RULE(val)]" density="compact"></v-text-field>
                  </FormElementsCommonFieldContainer>
                </v-col>
                <v-col cols="12" md="6" sm="6">
                  <FormElementsCommonFieldContainer label="Phone Number" :required="true">
                    <v-text-field v-model="form.phone" :rules="[val => REQUIRED_RULE(val)]" density="compact"></v-text-field>
                  </FormElementsCommonFieldContainer>
                </v-col>

                <v-col cols="12" md="6" sm="6">
                  <FormElementsCommonFieldContainer label="Email" :required="true">
                    <v-text-field v-model="form.email" :rules="[val => REQUIRED_RULE(val)]" density="compact" type="email"></v-text-field>
                  </FormElementsCommonFieldContainer>
                </v-col>

                <v-col cols="12" md="6" sm="6">
                  <FormElementsCommonFieldContainer label="Password" :required="true">
                    <v-text-field v-model="form.password" :rules="[val => REQUIRED_RULE(val)]" density="compact" type="password"></v-text-field>
                  </FormElementsCommonFieldContainer>
                </v-col>

                <v-col cols="12" md="6" sm="6">
                  <FormElementsCommonFieldContainer label="Select Type *">
                    <v-select :items="userTypes" :rules="[val => REQUIRED_RULE(val)]" :required="true" item-value="id" item-title="userType"
                              v-model="form.userTypeId" ></v-select>
                  </FormElementsCommonFieldContainer>
                </v-col>

              </v-row>

              <small class="text-caption text-medium-emphasis">*indicates required field</small>
            </v-card-text>

            <v-divider></v-divider>

            <v-card-actions>
              <v-spacer></v-spacer>

              <v-btn text="Close" variant="plain" @click="dialog = false"></v-btn>

              <!-- <v-btn color="primary" :disabled="!userForm" text="Save" variant="tonal" @click="onSubmit"></v-btn> -->
              <v-btn  :loading="loading" @click="onSubmit" :text="selectedUser? 'update': 'Save'" variant="flat" color="primary" density="compact"></v-btn>
            </v-card-actions>
          </v-card>
        </v-form>
      </v-dialog>

      <v-dialog v-model="roleDialog" max-width="400">
        <v-form @submit.prevent="onSubmitRole">
          
          <v-card prepend-icon="mdi-account" title="User Administration Role">
            
            <v-card-text>
              <v-row>
                <v-col cols="12">
                    <v-select  :items="roleList" :rules="[val => REQUIRED_RULE(val)]" :required="true" item-value="id" item-title="name"
                      v-model="roleForm.roleId" density="compact" label="Select Role Type *">
                    </v-select>
                </v-col>
              </v-row>
            </v-card-text>
            
            <v-card-actions>
              <v-spacer></v-spacer>
              <v-btn text="Close" variant="plain" @click="roleDialog = false"></v-btn>
              <v-btn :loading="loading" type="submit" color="primary" variant="flat" density="compact">Save</v-btn>
            </v-card-actions>

          </v-card>
          
        </v-form>
      </v-dialog>

      <v-dialog v-model="deleteDialog" max-width="400" persistent>
        <v-card prepend-icon="mdi-delete-empty-outline" text="Are you sure you want to delete this item?" title="Are you sure?">
          
          <template v-slot:actions>
            <v-spacer></v-spacer>
            <v-btn @click="deleteDialog = false">
              No
            </v-btn>
            <v-btn @click="confirmDelete">
              Yes
            </v-btn>
          </template>
          
        </v-card>
      </v-dialog>
    </template>
  </div>
</template>

<script setup lang="ts">
import {ref, watch} from 'vue'
import {EyeCheckIcon, PencilIcon, TrashIcon} from 'vue-tabler-icons'
import { useRolePermissionsStore } from '~/stores/administration/permissions';
import {useUserStore} from "~/stores/users";
import {REQUIRED_RULE} from "~/utils/formRules";
import{ DynamicModuleEnum, DynamicPermissionEnum} from "~/utils/roleManagement";

const router = useRouter();
const route = useRoute();
const store = useUserStore();
const permissionStore = useRolePermissionsStore();
const getUsers = computed(() => store.users);
const users = ref([]);
const dialog = ref(false)
const roleDialog = ref(false)
const deleteDialog = ref(false)
const is_view = ref(true)
const location = ref('end')
// const search = ref('')
const breadcrumb_page = ref({title: "Users"});
const breadcrumbs = ref([
  {
    text: "Dashboard",
    disabled: false,
    to: "/dashboard",
  },

  {
    text: "Users",
    disabled: true,
    to: "",
  },
]);
const snackbar = useSnackbar();
const selectedUser = ref(null);
const form = ref({
  firstName: '',
  lastName: '',
  phone: '',
  email: '',
  password: '',
  countryCode: null,
  gender: '',
  // dob: '',
  userTypeId: 2,
  roleId: null,
})

const roleForm = ref({
  roleId: null,
  userId: null,
})

const userTypes = ref([])
const sortKey = ref('');
const sortDirection = ref('asc');
const order = ref('desc')

const headers = ref([
  {title: 'First Name', align: 'start', sortable: false, key: 'firstName'},
  {title: 'Last Name', align: 'start', sortable: false, key: 'lastName'},
  {title: 'Email', key: 'email', sortable: false, align: 'start'},
  {title: 'Phone Number', key: 'phone', sortable: false, align: 'start'},
  {title: 'User Type', key: 'userType', sortable: false, align: 'start'},
  {title: 'Role Type', key: 'role', sortable: false, align: 'start'},
  {title: 'Action', key: 'action', align: 'center', sortable: false},
])

const mountLoading = ref(true)
const loading = ref(true)
const firstName = ref('')
const lastName = ref('')
const email = ref('')
const phone_number = ref('')
const search = ref('')
const itemToDelete = ref(null)
const userForm = ref(false)
const user_type = ref('')
const options = ref({
  itemsPerPage: 10,
  sortBy: 'ASC',
  search: '',
});
const roleList = ref([]);

function onClick() {
  if (!search) return;
// store.getUsers(search.value, 1, 'ASC', 10)
  store.getUsers('ASC', options?.value?.page, options?.value?.itemsPerPage, options?.value?.search);
}
const fetchUserTypes = async () => {
  await store.getUserTypes();
  userTypes.value = store.userTypes;
};
const clearSearch = () => {
  fetchUserList();
}

const sortBy = (key: string) => {
  if (!sortKey.value || sortKey.value !== key) {
    sortKey.value = key;
    sortDirection.value = 'asc';
  } else {
    sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc';
  }


  // Sort the data based on sortKey and sortDirection
  users.value = users.value.sort((a, b) => {
    if (sortDirection.value === 'asc') {
      // Debug: Check data types for comparison
      console.log('Comparing:', a[key], 'with', b[key]);
      return a[key] < b[key] ? -1 : a[key] > b[key] ? 1 : 0;
    } else {
      return a[key] > b[key] ? -1 : a[key] < b[key] ? 1 : 0;
    }
  });
};


const items = ref(getUsers);

const openCreateDialog = () => {
  resetForm()
  dialog.value = true
  is_view.value = false
}

const closeDialog = () => {
  dialog.value = false
  resetForm()
}

const resetForm = () => {
  form.value = {
    name: '',
    phone_number: '',
    email: '',
    password: '',
    confirm_password: '',
  }
  is_view.value = true
}

const searchTerm = ref('');

const filteredCollection = computed(() => {
  return items.value.filter(item => {
    const searchTextLower = searchTerm?.value?.toLowerCase();
    return (
        item?.firstname.toLowerCase().includes(searchTextLower) ||
        item?.email.toLowerCase().includes(searchTextLower) ||
        item?.phone.toLowerCase().includes(searchTextLower)
    );
  });
});


const editItem = (item: any) => {
  if(!permissionStore.hasModulePermission(`${DynamicModuleEnum.USER}:${DynamicPermissionEnum.READ_ONE}`)){
    snackbar.add({
      type: "warning",
      text: "You don't have permission to edit user",
    })
    return;
  }

  selectedUser.value = item;
  form.value = {...item}
  dialog.value = true
  is_view.value = false
}

const roleEditItem = (item: any) => {
  if(!permissionStore.hasModulePermission(`${DynamicModuleEnum.ROLES}:${DynamicPermissionEnum.ASSIGN}`)){
    snackbar.add({
      type: "warning",
      text: "You don't have permission to edit user role",
    })
    return;
  }

  roleForm.value = {roleId: item?.role?.id, userId: item?.id}
  roleDialog.value = true
  is_view.value = false
}

const viewItem = (item: any) => {
  
  form.value = {...item}
  dialog.value = true
  is_view.value = true
}

const deleteItem = (item: any) => {
  if(!permissionStore.hasModulePermission(`${DynamicModuleEnum.USER}:${DynamicPermissionEnum.SOFT_DELETE}`)){
    snackbar.add({
      type: "warning",
      text: "You don't have permission to delete",
    })
    return;
  }
  itemToDelete.value = item
  deleteDialog.value = true
}

const confirmDelete = () => {
  store.deleteUser(itemToDelete?.value)
    .then((response)=>{
      snackbar.add({
        type: response?.isSuccess ? "success" : "error",
        text: response?.messasge || "Something went wrong with deletion",
      });

      if(response?.isSuccess){
        handleFetchUserListApi();
      }
    })
    .catch((e)=>{
      snackbar.add({
        type: "error",
        text: "Something went wrong with deletion",
      });
    })
    .finally(()=>{
      itemToDelete.value = null;
      deleteDialog.value = false;
    })
}

const onSubmit = () => {

  if(!permissionStore.hasModulePermission(`${DynamicModuleEnum.USER}:${DynamicPermissionEnum.CREATE}`)){
    snackbar.add({
      type: "warning",
      text: "You don't have permission to create user",
    })
    return;
  }

  loading.value = true;

  if(selectedUser.value){
    store
      .updateUserProfile({...form?.value})
      .then((res: any) => {
        snackbar.add({
          type: res.isSuccess ? 'success' : 'error',
          text: res.messasge
        })
        fetchUserList();
        dialog.value = false
        loading.value = false;
        selectedUser.value = null;
      })
      .catch((err: any) => {
        console.log(err);
        loading.value = false;
        selectedUser.value = null;
      });
  }
  else{
  store
      .createUser(form.value)
      .then(async (res: any) => {
        snackbar.add({
          type: res.data.isSuccess ? 'success' : 'error',
          text: res.data.messasge
        })
        dialog.value = false
        fetchUserList();
        loading.value = false;
      })
      .catch((err: any) => {
        console.log(err);
        loading.value = false;
      });
  }
};

const onSubmitRole = async ()=>{

  if(!permissionStore.hasModulePermission(`${DynamicModuleEnum.ROLES}:${DynamicPermissionEnum.ASSIGN}`)){
    snackbar.add({
      type: "warning",
      text: "You don't have permission to edit user role",
    })
    return;
  }

  if(!roleForm?.value?.roleId || !roleForm?.value?.userId){
    return;
  }
  loading.value = true;
  const response = await permissionStore?.assignRoleUser(roleForm.value)
  if(response && response?.isSuccess){
    store.users.map((el)=>{
      if(el.id === response?.data?.id){
        el.role = response?.data?.role;
      }
    })
    snackbar.add({
      type: "success",
      text: "Assigned role to user successfully!"
    })
    loading.value = false;
    roleDialog.value = false;
  }
  else{
    snackbar.add({
      type: "error",
      text: "Assigned role to user failed!"
    })
    loading.value = false;
    roleDialog.value = false;
  }
}

const filterUsers = (filterKey, value, listKey) => {
  const normalizedValue = value.toLowerCase();
  let filteredData = [];

  if (normalizedValue.length > 0) {
    filteredData = store.users.filter(item => {
      const itemValue = filterKey(item).toLowerCase();
      return itemValue.includes(normalizedValue);
    });
  } else {
    filteredData = store.users;
  }

  users.value = filteredData;
};
watch(firstName, (val) => {
  filterUsers(item => item.firstName, val, 'users');
});

watch(lastName, (val) => {
  filterUsers(item => item.lastName, val, 'users');
});

watch(email, (val) => {
  filterUsers(item => item.email, val, 'users');
});

watch(phone_number, (val) => {
  filterUsers(item => item.phone, val, 'users');
});
watch(user_type, (val) => {
  filterUsers(item => item.userType.userType, val, 'users');
});

const getRouteQueryParams = ()=>{
  options.value.page = Number(route?.query?.page || options.value.page || 1);
  options.value.itemsPerPage = Number(route?.query?.itemsPerPage || options?.value?.itemsPerPage);
  options.value.sortBy = String(route?.query?.sortBy || options?.value?.sortBy);
  options.value.search = String(route?.query?.search || options?.value?.search);
}
const setRouteQueryParams = ()=>{
  router.push({query: options.value})
}

watch(() => dialog.value, (newVal, oldVal) => {
  if (!newVal) resetForm();
})

watch(() => options.value, (newVal, oldVal) => {
  if (newVal){
    setRouteQueryParams();
    handleFetchUserListApi();
  }
}, {deep:true})


const handleTablePageChange = (val: Number)=>{
  if(!mountLoading?.value){
    if(val) options.value.page =  val;
  }
}

const handleFetchUserListApi = async () => {
  if(!permissionStore.hasModulePermission(`${DynamicModuleEnum.USER}:${DynamicPermissionEnum.READ_MANY}`)){
    snackbar.add({
      type: "warning",
      text: "You don't have permission to see user list",
    })
    return;
  }
  
  const {page, sortBy, itemsPerPage, search } = options.value;
  await store.getUsers(sortBy, page, itemsPerPage, search).then((response)=>{
    // setRouteQueryParams();
    users.value = store.users;
    loading.value = false;
  }).catch((e)=>{
    loading.value = false;    
  })
};

const fetchUserList = async () => {
  if(!permissionStore.hasModulePermission(`${DynamicModuleEnum.USER}:${DynamicPermissionEnum.READ_MANY}`)){
    snackbar.add({
      type: "warning",
      text: "You don't have permission to see user list",
    })
    return;
  }

  const _page = Number(route?.query?.page ||  options?.value?.page || 1);
  const _itemsPerPage = Number(route?.query?.itemsPerPage || options?.value?.itemsPerPage);
  const _order = String(route?.query?.sortBy || options?.value?.sortBy);
  const _search = String(route?.query?.search || options?.value?.search);
  
  getRouteQueryParams();
  await store.getUsers(_order, _page, _itemsPerPage, _search).then((response)=>{
    users.value = store.users;
    loading.value = false;
  }).catch((e)=>{
    loading.value = false;    
  })
};

const fetchAdminRoles = ()=>{
  permissionStore.fetchRoles().then((response)=>{
    if(response?.isSuccess){
      roleList.value = response?.data || [];
    }
  })
}

onMounted(async () => {

  if(!permissionStore.hasModulePermission(`${DynamicModuleEnum.USER}:${DynamicPermissionEnum.READ_MANY}`)){
    snackbar.add({
      type: "warning",
      text: "You don't have permission to see user list",
    })
    return;
  }
  
  mountLoading.value = true;
  fetchUserTypes();
  await fetchUserList();

  if(permissionStore.hasModulePermission(`${DynamicModuleEnum.ROLES}:${DynamicPermissionEnum.READ_MANY}`)){
    fetchAdminRoles();
  }
  mountLoading.value = false;
})
</script>
