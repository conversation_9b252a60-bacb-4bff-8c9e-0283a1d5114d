<template>
    <div class="d-flex gap-2 justify-center align-center flex-column flex-md-row fill-height">
        <v-btn size="x-small" variant="outlined" color="primary" flat>
            Extra small
        </v-btn>

        <v-btn size="small" variant="outlined" color="primary" flat>
            Small
        </v-btn>

        <v-btn color="primary" variant="outlined" flat>
            Normal
        </v-btn>

        <v-btn color="primary" variant="outlined" size="large" flat>
            Large
        </v-btn>

        <v-btn size="x-large" variant="outlined" color="primary" flat>
            Extra large
        </v-btn>
    </div>
</template>