<template>
  <div
    class="pa-1 rounded-md min-w-25 h-100 border overflow-hidden cursor-grab"
  >
    <v-sheet
      class="slide-header px-3 d-flex align-center justify-space-between"
      height="60"
    >
      <div class="d-flex ga-1 align-center">
        <h3 class="text-h3">S{{props?.slideData?.parentId}}</h3>
        <h6 class="text-caption font-weight-bold">{{ props?.slideData?.title }}</h6>
      </div>
      <div v-if="props?.actionRequired" class="d-flex ga-2">
        <v-menu>
          <template v-slot:activator="{ props }">
            <v-btn
              color="primary"
              v-bind="props"
              icon="mdi-dots-vertical"
              size="25"
              flat
            >
            </v-btn>
          </template>
          <v-list>
            <v-list-item>
              <v-avatar @click="handleEditItem(props?.slideData)" class="cursor-pointer" color="warning" size="23" flat>
                <v-icon
                  icon="mdi-pencil"
                  size="13"
                ></v-icon>
              </v-avatar>
            </v-list-item>
            <v-list-item>
              <v-avatar @click="handleLocalizeItem(props?.slideData)" class="cursor-pointer" color="success" size="23" flat>
                <v-icon
                  icon="mdi-translate"
                  size="13"
                ></v-icon>
              </v-avatar>
            </v-list-item>
            <v-list-item>
              <v-avatar @click="handleDeleteItem(props?.slideData)" class="cursor-pointer" color="error" size="23" flat>
                <v-icon
                  icon="mdi-delete"
                  size="13"
                ></v-icon>
              </v-avatar>
            </v-list-item>
          </v-list>
        </v-menu>
      </div>
    </v-sheet>
    <div v-if="props?.slideData?.media">
      <v-img
        v-if="props?.slideData?.type === 'image'"
        :src="props?.slideData?.media?.imageUrl"
        :alt="props?.slideData?.media?.name"
      />
      <video
        v-else-if="props?.slideData?.type === 'video'"
        muted
        loop
        controls
        playsinline
        class="w-100"
      >
        <source :src="props?.slideData?.media?.imageUrl" type="video/mp4" />
      </video>
    </div>
    <div v-else-if="props?.slideData?.parentMedia">
      <v-img
        v-if="props?.slideData?.type === 'image'"
        :src="props?.slideData?.parentMedia?.imageUrl"
        :alt="props?.slideData?.parentMedia?.name"
      />
      <video
        v-else-if="props?.slideData?.type === 'video'"
        muted
        loop
        controls
        playsinline
        class="w-100"
      >
        <source :src="props?.slideData?.parentMedia?.imageUrl" type="video/mp4" />
      </video>
    </div>
    <v-sheet class="slide-footer px-3" height="40">
      <p class="text-caption font-weight-normal mb-0">
        {{ props?.slideData?.subTitle }}
      </p>
      <p class="text-caption font-weight-normal text-truncate w-50 mb-0">
        {{ props?.slideData?.description }}
      </p>
    </v-sheet>
  </div>
</template>

<script setup lang="ts">

const emits = defineEmits(["editItem", "localizeItem"]);
const props = defineProps({
	slideData:{
		type: Object,
		required: true,
	},
	actionRequired:{
		type: Boolean,
		required: false,
		default: ()=>{
			return true;
		}
	}
})

const handleEditItem = (item: any)=>{
	emits('editItem', item);
}
const handleLocalizeItem = (item: any)=>{
	emits('localizeItem', item);
}

const handleDeleteItem = (item: any)=>{
  emits('deleteItem', item);
}
</script>
