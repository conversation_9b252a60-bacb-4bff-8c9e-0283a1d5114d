<template>
  <v-overlay v-model="dialog" class="d-flex justify-center align-center">
      <v-sheet class="px-2 py-4 rounded-lg" max-width="90vw" min-width="400">
        <v-tabs bg-color="teal-darken-3" slider-color="teal-lighten-3" show-arrows v-model="activeTab" class="w-100"
          @update:modelValue="changeActiveTab">
          <v-tab
            v-for="country in countryStore?.countries"
            :key="country.id"
            :text="country.name"
            :value="country"
          ></v-tab>
        </v-tabs>
        <v-sheet v-if="activeTab" class="mt-5">
          <div class="text-h6 text-center d-flex ga-2 justify-space-between align-center">
            <span>📷🎥 Serial Slide Media</span>
            <v-chip color="primary" variant="flat">
              <span class="me-1">{{activeTab?.name}}</span> 
              <img v-if="activeTab?.imageGallery?.imageUrl" :src="activeTab?.imageGallery?.imageUrl" :alt="'flat_'+activeTab?.name" 
                width="30" height="20" />
            </v-chip>
          </div>
					<v-sheet v-if="props?.sliderData" :key="activeTab.id" class="mt-5 overflow-auto" min-height="400" max-height="500">
						<draggable v-model="countrySliderMedias" :element="'div'" :itemKey="'slider_item'" :options="dragOptions"
							class="v-row">
							<template #item="{ element }">
								<v-col cols="6" sm="3">
									<SlidersCardView :slideData="{...element?.locale, media:element?.media, parentId: element?.parentId, parentMedia: element?.parentMedia }" :actionRequired="false"/>
								</v-col>
							</template>
						</draggable>
					</v-sheet>
					<slot name="action">
						<div class="d-flex ga-2 mt-2">
							<v-btn @click="handleLocalizeSerialSlides" density="compact" color="primary" flat>Save</v-btn>
						</div>
					</slot>
        </v-sheet>
      </v-sheet>
  </v-overlay>
</template>

<script setup lang="ts">
import { useCountryStore } from "~/stores/others/country";
import { useSliderStore } from "~/stores/settings/sliders";

const props = defineProps({
	sliderData:{
		type: Object,
		required: true,
	}  
})
const emits = defineEmits(['fetchSingleSlider']);

const route = useRoute();
const snackbar = useSnackbar();
const sliderStore = useSliderStore();
const countryStore = useCountryStore();
const dialog = ref(false);
const activeTab = ref();
const countrySliderData = computed(()=>{
	return props?.sliderData;
});
const countrySliderMedias = ref();

const dragOptions = {
  animation: 150
};

const show = (item: any)=>{
  dialog.value = true;
}

const hide = ()=>{
  dialog.value = false;
  emits('fetchSingleSlider');
}

const changeActiveTab = ()=>{
	let mediaList = countrySliderData?.value?.sliderMedias
		?.flatMap((mediaItem: any) =>{
			return mediaItem?.sliderCountries
				?.filter((countryItem: any) => countryItem?.country?.id === activeTab?.value?.id)
				?.map((countryItem: any) => ({
					...countryItem,
					parentId: mediaItem?.id,
					parentMedia: mediaItem?.media
				})) || [];
  })?.sort((a: any, b: any)=> (a.sortOrder ?? 0) - (b.sortOrder ?? 0)) || [];
  countrySliderMedias.value = mediaList;
}

const handleLocalizeSerialSlides = async ()=>{
	countrySliderMedias?.value.forEach((item: any, index: number) => {
		item.sortOrder = index;
	});

	sliderStore?.saveSingleCountrySlideMediaSerial(countrySliderMedias?.value)
		.then((response: any)=>{
			snackbar.add({
				type: 'success',
				text: 'Successfully updated the serial',
			})
		})
		.catch((e)=>{
			snackbar.add({
				type: 'error',
				text: 'Failed to update the serial',
			})
			console.log(e);
		})
		.finally(()=>{
			hide();
		})
}

onMounted(()=>{
  countryStore?.getAllCountries();
  // activeTab.value = countryStore?.countries[0];
})

defineExpose({show});
</script>
