<template>
  <v-overlay v-model="localizeDialog" class="d-flex justify-center align-center">
      <v-sheet class="px-2 py-4 rounded-lg" max-width="750" min-width="400">
        <v-tabs bg-color="teal-darken-3" slider-color="teal-lighten-3" show-arrows v-model="activeTab" class="w-100"
          @update:modelValue="changeActiveTab">
          <v-tab
            v-for="country in countryStore?.countries"
            :key="country.id"
            :text="country.name"
            :value="country"
          ></v-tab>
        </v-tabs>
        <v-sheet v-if="activeTab" class="mt-5">
          <div class="text-h6 text-center d-flex ga-2 justify-space-between align-center">
            <span>📝 Slide Media Form</span>
            <v-switch
              v-model="form.isActive"
              :label="`isActive`"
              :false-value="false"
              :true-value="true"
              color="success"
              density="compact"
              hide-details
            ></v-switch>
            <v-chip color="primary" variant="flat">
              <span class="me-1">{{activeTab?.name}}</span> 
              <img v-if="activeTab?.imageGallery?.imageUrl" :src="activeTab?.imageGallery?.imageUrl" :alt="'flat_'+activeTab?.name" 
                width="30" height="20" />
            </v-chip>
          </div>
          
          <v-form @submit.prevent="singleMediaSubmit" ref="formRef" class="mt-5">
            <v-text-field
              v-model="form.title"
              density="compact"
              placeholder="Slide Title"
            />
            <v-text-field
              v-model="form.subTitle"
              density="compact"
              placeholder="Slide Sub-title"
            />
            <v-text-field
              v-model="form.description"
              density="compact"
              placeholder="Slide Description"
            />
            <v-text-field
              v-model="form.subDescription"
              density="compact"
              placeholder="Slide Sub-description"
            />
            <v-text-field v-model="form.buttonText" density="compact" placeholder="Slide button text" :rules="[]"></v-text-field>
            <v-text-field v-model="form.buttonLink" density="compact" placeholder="Button Link Ex. /collection/custom-url" :rules="[]"></v-text-field>
            <v-text-field v-model="form.mediaCustomUrl" density="compact" placeholder="Media Link Ex. /media/custom-url" :rules="[]"></v-text-field>

            <v-select
              v-model="form.type"
              :items="mediaTypes"
              item-title="state"
              item-value="value"
              density="compact"
              placeholder="Media Type"
              :rules="[formRules.required]"
            />
            <template v-if="form.type === 'image'">
              <FormElementsImageViewerSingle
                :image-id="form.mediaId"
                :image-rules="SLIDER_IMAGE_RULES"
                :required-height="2250"
                :required-width="1800"
                :image-folders="[
                  { folderSlug: 'SLIDER_IMAGE', title: 'Slider Image', size: '' },
                  { folderSlug: 'home', title: 'Home', size: '1808x2000' },
                ]"
                image-model="slider_image"
                :disableImageValidation="true"
                @selected-image-id="selectedImage"
              />
            </template>
            <template v-else-if="form.type === 'video'">
              <FormElementsImageViewerSingle
                :image-id="form.mediaId"
                :image-rules="SLIDER_IMAGE_RULES"
                :required-height="2250"
                :required-width="1800"
                :image-folders="[
                  {folderSlug:'SLIDER_VIDEO', title: 'Slider Video', size: '', mediaType: 'video'},
                ]"
                image-model="SLIDER_VIDEO"
                :disableImageValidation="true"
                @selected-image-id="selectedImage"
              />
            </template>

            <div class="d-flex ga-3 align-center">
              <v-btn type="submit" class="border w-50" color="primary" flat>{{
                form?.id ? "Update" : "Save"
              }}</v-btn>
              <v-btn @click="handleResetForm" class="border" color="error" flat
                >Reset</v-btn
              >
            </div>
          </v-form>
        </v-sheet>
      </v-sheet>
  </v-overlay>
</template>

<script setup lang="ts">
import { useCountryStore } from "~/stores/others/country";
import { useSliderStore } from "~/stores/settings/sliders";
import { normalizedForm } from "~/utils/helpers/functions";

const emits = defineEmits(['fetchSingleSlider']);

const route = useRoute();
const snackbar = useSnackbar();
const sliderStore = useSliderStore();
const countryStore = useCountryStore();
const localizeDialog = ref(false);
const activeTab = ref();
const selectedItem = ref(null);
const formRef = ref();
const defaultForm = {
  id: null,
  title: null,
  subTitle: null,
  description: null,
  subDescription: null,
  type: null,
  buttonLink: null,
  buttonText: null,
  mediaCustomUrl: null,
  mediaId: null,
  isActive: false,
}
const form = reactive({...defaultForm});

const formRules = {
  required: (v: any)=> !!v || 'This field is required',
  minLength: (v: string)=> (v && v?.length >=3 ) || 'Minimum 3 characters',
}

const mediaTypes = [
  {state: 'Image', value: 'image'},
  {state:'Video', value: 'video'},
];

const show = (item: any)=>{
  localizeDialog.value = true;
  selectedItem.value = item;
}

const hide = ()=>{
  selectedItem.value = null;
  handleResetForm();
  localizeDialog.value = false;
  emits('fetchSingleSlider');
}

function updateFormFields(data: any){
  form.id = data?.id || null;
  form.title = data?.title || null;
  form.subTitle = data?.subTitle || null;
  form.description = data?.description || null;
  form.subDescription = data?.subDescription || null;
  form.type = data?.type || null;
  form.buttonLink = data?.buttonLink || null;
  form.buttonText = data?.buttonText || null;
  form.mediaCustomUrl = data?.mediaCustomUrl || null;
  form.isActive = data?.isActive || false;
  form.mediaId = data?.mediaId || null;
}

const selectedImage = (val: any) => {
  form.mediaId = val;
}

const handleResetForm = ()=>{
  selectedItem.value = null;
  updateFormFields(defaultForm);
}

const changeActiveTab = ()=>{
  let countrySlideItem = selectedItem?.value?.sliderCountries?.find((element: any)=> element?.country?.id === activeTab?.value?.id);
  updateFormFields({...countrySlideItem?.locale, id: countrySlideItem?.id, mediaId: countrySlideItem?.media?.id, isActive: countrySlideItem?.isActive});
}

const singleMediaSubmit = async ()=>{
  const { valid } = await formRef?.value?.validate();
  if(!valid){
    snackbar.add({
      type: "error",
      text: "Form has errors",
    })
    return;
  }

  let payload = {
    id: form?.id,
    locale: {...normalizedForm(form)},
    sliderMedia: selectedItem?.value?.id,
    country: activeTab?.value?.id,
    isActive: form?.isActive,
  }
  sliderStore?.saveSingleCountrySlideMedia({...payload})
    .then((response)=>{
      updateFormFields(defaultForm);
      snackbar.add({
        type: 'success',
        text: 'Save country based slide media!',
      })
      hide();
    })
}

onMounted(()=>{
  countryStore?.getAllCountries();
  // activeTab.value = countryStore?.countries[0];
})

watch(()=> localizeDialog.value, (newVal, oldVal)=>{
  if(newVal === false){
    activeTab.value = null;
    selectedItem.value = null;
  }
})

defineExpose({show});
</script>
