<template>
  <v-btn color="primary" size="small" @click="openDialog(id)">Configure Values</v-btn>
  <v-dialog v-model="dialog" width="80%">
    <v-card>
      <v-card-title>Add Attribute Value for {{ name }}</v-card-title>
      <v-card-text>
        <v-row>
          <v-col md="4">
            <v-form @submit.prevent="onSubmit" ref="attributeValueForm" v-model="form">

              <v-label class="mb-2 font-weight-medium">Name</v-label>
              <v-text-field v-model="attributeValue.name" :rules="rules"></v-text-field>
              <v-label class="my-3">Slug</v-label>
              <v-text-field v-model="attributeValue.slug"></v-text-field>
              <v-divider class="my-5"></v-divider>
              <v-btn
                  :disabled="!form"
                  :loading="loading"
                  color="primary"
                  variant="tonal" class="px-6"
                  type="submit"> {{ attributeValue.id ? 'Update' : 'Add New' }} Attribute value
              </v-btn>
              <v-btn
                  color="error"
                  variant="tonal" class="px-6 ms-2"
                  @click="resetForm"
              > Reset
              </v-btn>
            </v-form>
          </v-col>
          <v-col md="8">
            <template v-if="getAttributeValues.length <= 0">
              No Values found!
            </template>
            <template v-else>
              <TableComponentsSimpleTable :theads="theads">
                <tr v-for="item in getAttributeValues" :key="item.id" class="month-item">
                  <td> {{ item.name }} </td>
                  <td>  {{ item.slug }} </td>
                  <td>
                    <v-btn icon color="warning" class="me-2" variant="tonal" size="small">
                      <PencilIcon size="20"/>
                    </v-btn>
                    <v-btn icon color='error' variant="tonal" size="small" >
                      <TrashIcon size="20"/>
                    </v-btn>
                  </td>
                </tr>
              </TableComponentsSimpleTable>
            </template>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>
<script setup lang="ts">
import {computed, ref} from 'vue';
const snackbar = useSnackbar();
const dialog = ref(false)
const form = ref(false);
const attributeValueForm = ref();
const loading = ref(false);
const theads = ['name', 'slug', 'actions']
import { useProductAttributesStore } from "~/stores/products/attributes";

const store = useProductAttributesStore();

const props = defineProps({
  id: Number,
  name: String
});

const openDialog = ( id: number ) => {
  dialog.value = true
}

const rules = [
  value => {
    if (value) return true;
    return 'You must enter a name'
  }
];

const attributeValue = ref({
  id: null,
  name: '',
  slug: '',
  attributeId: props.id
})

const onSubmit = () => {
  store.addAttributeValue( {
    id: attributeValue.value.id,
    name: attributeValue.value.name,
    slug: attributeValue.value.slug,
    attributeId: attributeValue.value.attributeId
  } ).then(res => {
    snackbar.add({
      type: res.success ? 'success' : 'error',
      text: res.message
    })
  }).catch(err => {
    console.log(err)
  })

  loading.value = false
  resetForm()
}

const resetForm = () => {
  attributeValue.value.name = '';
  attributeValue.value.slug = '';
}

const getAttributeValues = computed(() => {
  return store.attributeValuesByAttrId;
});



</script>