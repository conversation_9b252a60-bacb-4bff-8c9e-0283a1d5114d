<template>
  <v-dialog v-model="internalDialog" max-width="400" persistent>
    <v-card>
      <v-card-title>Confirmation</v-card-title>
      <v-card-text>
        {{ message || `Are you sure you want to ${confirmButton || 'delete'} this item?` }}
      </v-card-text>
      <v-card-actions class="text-right">
        <v-btn @click="closeDialog(false)">Cancel</v-btn>
        <v-btn color="error" @click="confirmDelete">{{confirmButton || 'Delete' }}</v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { defineProps, defineEmits } from 'vue';

const props = defineProps<{
  dialog: boolean;
  message?: string;
  confirmButton?: string;
}>();

const emit = defineEmits(['update:dialog', 'confirm-delete']);
const internalDialog = ref(props.dialog);

watch(() => props.dialog, (newVal) => {
  internalDialog.value = newVal;
});

const closeDialog = (value: boolean) => {
  emit('update:dialog', value);
};

const confirmDelete = () => {
  emit('update:dialog', false); // Close dialog
  emit('confirm-delete'); // Signal confirmation
};
</script>
