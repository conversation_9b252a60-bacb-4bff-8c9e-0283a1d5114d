<template>
  <v-btn color="primary" size="small" variant="flat" @click="MediaDialog" title="">Upload /Select Media</v-btn>
  <v-dialog v-model="uploadMediaDialog" width="80%">
    <v-card>
      <v-card-text>
        <!-- To upload image by this component -->
        <FormElementsImagesUpload @image-uploaded="mediaHandle" :multiple="multiple" :image-upload-rules="imageUploadRules" :max-size="maxSize"
            :required-height="requiredHeight"
            :required-width="requiredWidth"
            :disableImageValidation="disableImageValidation"
        />

        <!-- To see all images under a folder. This folder is image store server folder -->
        <div class="d-flex flex-column flex-sm-row ga-5">
          <div v-if="props?.imageFolders" class="d-flex ga-2">
            <v-btn v-for="(folder, index) in props?.imageFolders" :key="'folder_image_'+ index" :class="{'bg-primary': props?.modalType === folder?.folderSlug}"
              @click="folderChange(folder?.folderSlug)"
              height="55"
            >
              <p class="d-flex flex-column"> 
                <span>{{folder?.title}}</span>
                <span class="text-caption">{{folder?.size}}</span>
              </p>
            </v-btn>
          </div>
          <v-text-field label="Search Image" @update:modelValue="searchImage" v-model="searchTerm" class="mt-3" 
            append-inner-icon="mdi-magnify" density="compact" clearable/>
        </div>
        <v-sheet min-height="300" class="mt-2">
          <v-row>
            <v-col cols="2" sm="1" v-for="(image) in imageGalleries" :key="image?.id" class="position-relative">
                <v-checkbox v-model="selected" :true-value="image.id" class="custom-checkbox" hide-details>
                  <template #label>
                    <v-img v-if="isImageType(image, 'image')" :src="image?.imageGalleryUrls?.thump || image?.imageUrl"  width="100%" />
                    <video v-if="isVideoType(image, 'video')"  width="50" height="70" class="mb-0" autoplay preload="auto" 
                      muted playsinline loop>
                      <source :src="image?.imageUrl" :type="image?.imageMimeType"/>
                    </video>
                  </template>

                </v-checkbox>
                <div class="text-center text-caption text-truncate mt-1">
                  {{image?.name}}
                </div>
                <!-- <v-avatar color="error" size="18" @click="deleteImage(image?.id)" class="mt-2 position-absolute" style="right: -3%; top: -5%;">
                  <v-icon icon="mdi-close" size="14" />
                </v-avatar> -->
            </v-col>
            <v-col v-if="loading" cols="2" sm="1">
              <div class="border rounded h-100 d-flex justify-center align-center" style="width:68px;">
                <v-progress-circular model-value="20" size="15" indeterminate></v-progress-circular>
              </div>
            </v-col>
          </v-row>
        </v-sheet>
        <v-chip color="error" v-if="error">
          <QuestionMarkIcon/>
          {{ error }}
        </v-chip>
        <v-btn color="primary" variant="outlined" @click="imageSelected" class="mt-2">Set Image</v-btn>
      </v-card-text>
      <v-pagination :length="imageGalleryStore?.meta?.pageCount" :total-visible="7" size="x-small" class="my-2" @update:modelValue="onChangePage"></v-pagination>
    </v-card>
  </v-dialog>
</template>

<script setup lang="js">

import { QuestionMarkIcon } from "vue-tabler-icons";

import {ref, computed} from 'vue';
import {useImageGalleryStore} from "~/stores/image-gallery";
import { useMediaType } from "~/composables/useMediaType";

const { isImageType, isVideoType, rawUploadedIsImageType, rawUploadedIsVideoType} = useMediaType();
const imageGalleryStore = useImageGalleryStore();

const selected = ref([]);
const error = ref('');

const searchTerm = ref('')

const props = defineProps({
  modalId: {
    type: Number
  },
  modalType: {
    type: String
  },
  multiple: {
    type: Boolean,
    default: false
  },
  imageUploadRules: {
    type: Array,
    required: false
  },
  requiredWidth: {
    type: Number
  },
  requiredHeight: {
    type: Number
  },
  maxSize: {
    type: Number
  },
  imageFolders:{
    type: Array,
    required: false
  },
  disableImageValidation:{
    type: Boolean,
    required: false
  }
});

const emit = defineEmits(['imageSelected', 'dialogBtnClicked', 'imageFolderChange'])

const uploadMediaDialog = ref(false)
const loading = ref(false);
const snackbar = useSnackbar();

const MediaDialog = () => {
  uploadMediaDialog.value = true;
  imageGalleryStore.getImages(props.modalType);
}

const imageGalleries = computed(() => imageGalleryStore.galleries)

const getFolderMediaType = (type)=>{
  if(type){
    return props?.imageFolders?.find((el)=> el?.folderSlug === props?.modalType)
  }
}

const mediaHandle = (val) => {
  if(props?.modalType==='product'){
    snackbar.add({
      type:"error",
      text: "Uploading closed for 1808x2000 now. Contact with developer team!",
    })
    return;
  }
  
  if(!val || val?.length === 0){
    snackbar.add({
      type:"error",
      text: "Uploaded media issue. Contact with developer team!",
    })
    return;
  }

  let currentFolder = getFolderMediaType(props?.modalType);

  if(currentFolder?.mediaType === 'video'){
    if(!rawUploadedIsVideoType(val[0], 'video')){
      snackbar.add({
        type: 'warning',
        text: "You are in video type folder",
      })
      return;
    }

    loading.value = true;
    imageGalleryStore
      .addVideo(val, props.modalType)
      .then((response)=>{
      })
      .catch((e)=>{
        snackbar.add({
          type:"success",
          text: "Failed to image upload",
        })
      })
      .finally(()=>{
        loading.value = false;
      })

    return;
  }

  // need to make restriction after add this to parent component
  // if(currentFolder?.mediaType === 'image'){
    // if(!rawUploadedIsVideoType(val[0], 'image')){
    //   snackbar.add({
    //     type: 'warning',
    //     text: "You are in video type folder",
    //   })
    //   return;
    // }

    loading.value = true;
    imageGalleryStore
      .addImage(val, props.modalType)
      .then((response)=>{
        loading.value = false;
      })
      .catch((e)=>{
        snackbar.add({
          type:"success",
          text: "Failed to image upload",
        })
        loading.value = false;
      })
  // }
}

const imageSelected = () => {
  if (selected.value.length > 0) {
    error.value = '';
    emit('imageSelected', {values: selected.value, type: props.modalType ? props.modalType : props.modalId});
    uploadMediaDialog.value = false;

  } else {
    error.value = 'Please select an image first to set.'
  }
}

const onChangePage = (val) => {
  imageGalleryStore.getImages(props.modalType,'ASC',val, 18, searchTerm.value);
}

const searchImage = async (val) => {
  if( val && val.length > 1 ) {
    await imageGalleryStore.getImages(props.modalType,'ASC',1,18, val);
  }else  {
    await imageGalleryStore.getImages(props.modalType,'ASC',1,18);
  }
}

const deleteImage = async( imageId ) => {

  const response =  await imageGalleryStore.removeImage(imageId)

  console.log( response )

}

const handleResetSelected = ()=>{
    selected.value = [];
}

watch(()=> uploadMediaDialog.value, (newVal, oldVal)=>{
  if(newVal === false){
    handleResetSelected();
  }
})

watch(()=> props?.modalType, (newVal, oldVal)=>{
  imageGalleryStore.getImages(newVal);
  handleResetSelected();
})

const folderChange = (folderSlug)=>{
  emit('imageFolderChange', folderSlug)
}

</script>

<style>
  .custom-checkbox .v-label {
    width: 100%;
  }

  .custom-checkbox .v-selection-control--dirty{
    border: 3px solid #000000;
  }
  .custom-checkbox .v-selection-control__wrapper {
    display: none;
  }
</style>

