<template>
  <v-btn icon color="primary" class="me-2" variant="tonal" size="small" @click="openDialog2(id)">
    <EyeIcon size="20"/>
  </v-btn>
  <v-dialog v-model="valuesDialog" width="500">
    <v-card>
      <v-card-title>{{ name }} Values</v-card-title>
      <v-card-text>
        <template v-if="getAttributeValues.length <= 0">
          No Values found!
        </template>
        <template v-else>
          <TableComponentsSimpleTable :theads="theads">
            <tr v-for="item in getAttributeValues" :key="item.id" class="month-item">
              <td> {{ item.name }} </td>
              <td>  {{ item.slug }} </td>
            </tr>
          </TableComponentsSimpleTable>
        </template>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>
<script setup lang="ts">

import { EyeIcon } from "vue-tabler-icons";

import {computed, ref} from 'vue';

const theads = ['Name', 'Slug']

const valuesDialog = ref(false);

import { useProductAttributesStore } from "~/stores/products/attributes";

const store = useProductAttributesStore();

const props = defineProps({
  id: Number,
  name: String
});

const openDialog2 = ( id: number ) => {
  valuesDialog.value = true;
  store.getAttributeValues( id );
}

const getAttributeValues = computed(() => {
  return store.attributeValuesByAttrId;
});


</script>