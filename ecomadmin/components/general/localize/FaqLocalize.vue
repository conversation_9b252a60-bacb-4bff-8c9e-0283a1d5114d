<template>
    <v-dialog v-model="dialog" transition="dialog-bottom-transition" max-width="75%">
        <v-card  elevation="10" :title="`Localize: ${attributeName ? ' (' +attributeName+ ')' : ''}`">
            <v-card-item class="py-4 px-6">
                <v-card>
                    <v-tabs bg-color="teal-darken-3" slider-color="teal-lighten-3" show-arrows v-model="activeTab">
                        <v-tab
                            v-for="c in countries"
                            :key="c.id"
                            :text="c.label"
                            :value="c.id"
                        ></v-tab>
                    </v-tabs>
                </v-card>
                <v-container>
                    <v-row no-gutters>
                        <v-col v-if="countries.length">
                            <template v-for="country in countries" :key="country.id">
                                <v-form v-model="form" @submit.prevent="onSubmit" v-slot="{ reset, isValid }" v-if="country.id == activeTab">
                                    <FormElementsCommonFieldContainer label="Name" :required="true">
                                        <v-text-field v-model="country.name" :rules="rules" placeholder="Enter Name"></v-text-field>
                                    </FormElementsCommonFieldContainer>
                                    <FormElementsCommonFieldContainer label="Description" :required="false">
                                        <FormElementsRichTextEditor v-model="country.description"/>
                                    </FormElementsCommonFieldContainer>
                                    <v-divider class="my-5"></v-divider>
                                    <v-card-actions>
                                        <v-spacer></v-spacer>
                                        <v-btn :disabled="!isValid?.value" :title="isValid.value" :loading="loading" color="primary" variant="tonal" class="px-6" type="submit"> Save</v-btn>
                                        <v-btn color="error" variant="tonal" class="px-6 ms-2" @click="reset"> Reset</v-btn>
                                    </v-card-actions>
                                </v-form>
                            </template>
                        </v-col>
                    </v-row>
                </v-container>
          </v-card-item>
        </v-card>
    </v-dialog>
    <v-snackbar v-model="snackbar" :color="snackbarData.type">
    {{ snackbarData.message }}
  </v-snackbar>
</template>
<script setup lang="ts">
import {useFaqStore} from "~/stores/others/faq";
import { ref } from 'vue';
import { getLocaLizeLanguages } from "~/utils/languages";
  
  // Types for Country
  interface Country {
    id: number;
    label: string;
    name: string;
    description: string;
  }

  interface AttributeLocale {
    name: string; 
    description: string|null;
  }


  type LocaleAttribute = Record<number, AttributeLocale>;


  const rules = [
  (value: any) => {
    if (value) return true;
    return 'You must enter a name'
  }
];

const store = useFaqStore();
  

const snackbar = ref(false)
const snackbarData = ref({
  message: '',
  type: 'success'
})

  // Reactive state
  const dialog = ref(false);
  const activeTab = ref(2); // Controls the active tab


  const form = ref(false);
  const loading = ref(false);

const countries = ref<Country[]>([]);

const activeAttributeId = ref<number>();
const attributeName = ref<string>();

const show = async (attributeItem: any) => {
    activeTab.value = 2;
    activeAttributeId.value = attributeItem.id;
    attributeName.value = attributeItem.name;

    const locale: LocaleAttribute = attributeItem?.locale ? attributeItem.locale as LocaleAttribute : {};

    let countryData:Country[] = [];
    const list = [...getLocaLizeLanguages];

    for(const data of list)
    {
      const countryId = data.id as number;
        const row = Object.hasOwn(locale, countryId) ? locale[countryId] : null;

        countryData.push({
            id: countryId,
            label: data.name,
            name: row?.name || '',
            description: row?.description || '',
        });
    }

    countries.value = [...countryData];
    dialog.value = true;
};


const onSubmit = () => {
    if (!form.value) return;
    const payload = countries.value.map(item => ({
        country_id: item.id,
        name: item.name,
        description: item.description
    })).filter(item => item.name?.length);

    const id = activeAttributeId.value;

    if(!payload.length || !id) return;

    const locale:Record<number, AttributeLocale> = {};

    payload.forEach(item => {
      locale[item.country_id] = { name: item.name, description: item.description } ;
    });

loading.value = true
setTimeout(() => {
  store.localizeFaq(id, {locale}).then((res: any) => {
    snackbar.value = true;
    snackbarData.value.message = res.message
    snackbarData.value.type = (res.success ? 'success' : 'error');

    if(res.success) {
      dialog.value = false;
    }

  }).catch(err => {
    console.log(err)
  })

  loading.value = false

}, 500);

}

  defineExpose({show})
  </script>
  

