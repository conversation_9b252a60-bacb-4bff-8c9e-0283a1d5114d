<template>
  <div>
    <SharedUiParentCard class="mt-6" title="SEO Meta">
      <FormElementsImageViewerSingle
          :image-id="metaData.galleryImageId"
          image-model="SEO"
          :image-rules="PRODUCT_SEO_META_IMAGE_RULES"
          :max-size="200000"
          :required-height="630"
          :required-width="1200"
          :multiple="false"
          @selected-image-id="handleImage"
      />
      <v-text-field label="Meta Title" v-model="metaData.metaTitle"/>
      <v-textarea label="Meta Description" v-model="metaData.metaDescription" rows="3"/>
      <v-card  elevation="10" :title="`Localize`" v-if="showLocale">
            <v-card-item class="py-4 px-6">
                <v-card>
                    <v-tabs bg-color="teal-darken-3" slider-color="teal-lighten-3" show-arrows v-model="activeTab">
                        <v-tab
                            v-for="c in countries"
                            :key="c.id"
                            :text="c.label"
                            :value="c.id"
                        ></v-tab>
                    </v-tabs>
                </v-card>
                <v-container>
                    <v-row no-gutters>
                        <v-col v-if="countries.length">
                            <template v-for="country in countries" :key="country.id">
                                <div v-if="country.id == activeTab">
                                    <FormElementsCommonFieldContainer label="Meta Title" :required="true">
                                        <v-text-field v-model="country.name" :rules="[val => REQUIRED_RULE(val)]" placeholder="Enter Title"></v-text-field>
                                    </FormElementsCommonFieldContainer>
                                    <FormElementsCommonFieldContainer label="Meta Description" :required="true">
                                      <v-textarea v-model="country.description" :rules="[val => REQUIRED_RULE(val)]" placeholder="Enter Description" rows="3"/>
                                    </FormElementsCommonFieldContainer>
                                </div>
                            </template>
                        </v-col>
                    </v-row>
                </v-container>
          </v-card-item>
        </v-card>


      <v-btn color="primary" variant="tonal" class="px-12 py-6 mt-5 ml-auto d-flex" @click="saveMeta">Save Meta</v-btn>
    </SharedUiParentCard>
  </div>
</template>
<script setup lang="ts">
import {PRODUCT_SEO_META_IMAGE_RULES} from "~/utils/imageRules";
import {REQUIRED_RULE} from "~/utils/formRules";
import { getLocaLizeLanguages } from "~/utils/languages";

  // Types for Country
  interface Country {
    id: number;
    label: string;
    name: string;
    description: string;
  }

const emit = defineEmits(['onSaveMeta'])

const props = defineProps<{
  id?: number,
  metaTitle?: string,
  metaDescription?: string,
  galleryImageId?: number
  metaType?: string
  locale?: any
  showLocale?: boolean
}>()

const activeTab = ref(2); // Controls the active tab
const countries = ref<Country[]>([]);

const generateLocale = () => {

  if(!props.showLocale) return;

  let countryData:Country[] = [];
  const list = [...getLocaLizeLanguages];
  const oldLocales = props.locale || {};
  for(const data of list)
  {
    const old = oldLocales[data.id] ?? {};
    const countryId = data.id as number;
    countryData.push({
      id: countryId,
      label: data.name,
      name: old.name || '',
      description: old.description || '',
    });
  }

  countries.value = [...countryData];
}

generateLocale();

const metaData = reactive({
  id: 0,
  metaTitle: null,
  metaDescription: null,
  metaType: null,
  galleryImageId: 0,
  locale: {},
})

const handleImage = (val: any) => {
  metaData.galleryImageId = val
}

const saveMeta = () => {

  if (!metaData.metaTitle) {
    return;
  }

  if (!metaData.metaDescription) {
    return;
  }

  if(props.showLocale && countries.value?.length) {

    const locale: Record<number, {name: string, description: string}> = {};

    countries.value.forEach(item => {

      if(item.name && item.description) {
        locale[item.id] = { name: item.name, description: item.description } ;
      }
    });
    metaData.locale = locale;
  } else if(props.locale) {
    metaData.locale = props.locale;
  }

  emit("onSaveMeta", metaData);

}

watch(() => props, () => {

  if (props.id) {
    metaData.id = props.id;
  }
  if (props.metaTitle) {
    metaData.metaTitle = props.metaTitle;
  }

  if (props.galleryImageId) {
    metaData.galleryImageId = props.galleryImageId;
  }

  if (props.metaDescription) {
    metaData.metaDescription = props.metaDescription;
  }
  if (props.metaType) {
    metaData.metaType = props.metaType;
  }
}, {immediate: true, deep: true})

</script>