<template>
  <SharedUiParentCard class="mt-6" title="Product Brand">
    <v-select
        :model-value="props.modelValue"
        label="Select Brand"
        :items="getBrands"
        item-title="name"
        item-value="id"
        @update:modelValue="updateValue"
        density="compact"
    ></v-select>
<!--    <v-autocomplete-->
<!--        :items="getBrands"-->
<!--        :filter="customFilter"-->
<!--        color="white"-->
<!--        item-title="name"-->
<!--        item-text="id"-->
<!--        item-value="id"-->
<!--        label="Select Brand"-->
<!--        @update:modelValue="updateValue"-->
<!--        :model-value="props.modelValue"-->
<!--    ></v-autocomplete>-->
  </SharedUiParentCard>
</template>

<script lang="ts" setup>
import {computed, onMounted} from "vue";
import {useProductBrandsStore} from "~/stores/products/brands";

interface Props {
  modelValue?: number
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: 0
})
const brands = ref([]);
const customFilter = (item, queryText, itemText) => {
  const textOne = item.name.toLowerCase()
  const searchText = queryText.toLowerCase()
  return textOne.includes(searchText)
}

const brandStore = useProductBrandsStore();
const getBrands = computed(() => brandStore.brands);

const emit = defineEmits(['update:modelValue']);

const updateValue = (event: any) => {
  emit('update:modelValue', event);
}
// const updateValue = (value) => {
//   // const selectedBrand = getBrands.value.find((brand) => brand.id === value);
//   emit('update:modelValue', selectedBrand ? selectedBrand.id : null);
// };

onMounted(async () => {
  await brandStore.getAllActiveBrands();
})

</script>
