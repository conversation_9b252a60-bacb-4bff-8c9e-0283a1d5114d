<template>
  <SharedUiParentCard class="mt-6" title="Product Description">
    <FormElementsEditor v-model="props.description"/>
  </SharedUiParentCard>
  <SharedUiParentCard class="mt-6" title="Product Material & Care">
    <FormElementsEditor v-model="props.materialCare"/>
  </SharedUiParentCard>
  <SharedUiParentCard class="mt-6" title="Product Shipping & Return">
    <FormElementsEditor v-model="props.shippingReturn"/>
  </SharedUiParentCard>
  <SharedUiParentCard class="mt-6" title="The WOW Factors">
    <FormElementsEditor v-model="props.wowFactors"/>
  </SharedUiParentCard>
</template>
<script setup lang="ts">
interface Props {
  description ?: string | undefined
  materialCare ?: string | undefined
  shippingReturn ?: string| undefined
  wowFactors ?:  string| undefined
}

const props = withDefaults(defineProps<Props>(), {
  description: '',
  materialCare: '',
  shippingReturn: '',
  wowFactors: ''
})

</script>