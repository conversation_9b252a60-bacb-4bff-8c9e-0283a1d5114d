<template>
    <v-dialog v-model="dialog" scrollable eager :retain-focus="false" transition="dialog-bottom-transition" style="max-width: 95%;" persistent>


    <v-card  elevation="10">

        <v-card-title class="d-flex justify-space-between align-center pa-4">
          Collection
          <v-spacer></v-spacer>
          <v-btn icon size="small" @click="dialog = false">
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-card-title>
            <v-card-item class="py-4 px-6">
                <v-form v-model="form" @submit.prevent="onSubmit" v-slot="{ isValid }">

                    <v-row>
                        <FormElementsCommonFieldContainer label="Country" :required="true" class="mb-3 v-col-8">
                            <v-select
                            :items="getCountries"
                            v-model="fromPayload.countryId"
                            label="Select Country"
                            item-title="name"
                            item-value="id"
                            hide-details
                            ></v-select>
                        </FormElementsCommonFieldContainer>

                        <v-col class="d-flex flex-row-reverse">
                            <v-switch
                                v-model="fromPayload.isActive"
                                color="primary"
                                defaults-target="success"
                                :label="'Status ' + ((fromPayload.isActive)? 'Published': 'Not Published')"
                                hide-details
                                inset
                            ></v-switch>
                        </v-col>
                    </v-row>

                    <FormElementsCommonFieldContainer label="Title" :required="true" class="mb-3">
                        <v-text-field v-model="fromPayload.title" :rules="[val => REQUIRED_RULE(val)]" placeholder="Enter Title"></v-text-field>
                    </FormElementsCommonFieldContainer>

                    <SharedUiParentCard v-if="dialog" class="mt-6" title="Full area content">
                        <v-card-text>
                        <RichTextEditor v-model="fromPayload.fullContent" :height="350"/>
                    </v-card-text>
                    </SharedUiParentCard>
                    <v-row v-if="dialog">
                        <v-col md="6">
                            <SharedUiParentCard class="mt-6" title="Left half area content">
                                <v-card-text>
                                <RichTextEditor v-model="fromPayload.leftHalfContent" :height="350"/>
                            </v-card-text>
                            </SharedUiParentCard>
                        </v-col>
                        <v-col md="6">
                            <SharedUiParentCard class="mt-6" title="Right half area content">
                                <v-card-text>
                                <RichTextEditor v-model="fromPayload.rightHalfContent" :height="350"/>
                            </v-card-text>
                            </SharedUiParentCard>
                        </v-col>
                    </v-row>

                    <SharedUiParentCard class="mt-6" title="SEO Meta">
                    <FormElementsImageViewerSingle
                        :image-id="fromPayload.metaImageId"
                        image-model="SEO"
                        :image-rules="PRODUCT_SEO_META_IMAGE_RULES"
                        :max-size="200000"
                        :required-height="630"
                        :required-width="1200"
                        :multiple="false"
                        @selected-image-id="handleImage"
                    />
                    <v-text-field label="Meta Title" v-model="fromPayload.metaTitle"/>
                    <v-textarea label="Meta Description" v-model="fromPayload.metaDescription" rows="3"/>
                    </SharedUiParentCard>

                    <v-card-actions>
                        <v-spacer></v-spacer>
                        <v-btn @click="preview" color="secondary" variant="outlined">Preview</v-btn>
                        <v-btn :disabled="!isValid?.value" :title="isValid.value" :loading="loading" color="primary" variant="tonal" class="px-6" type="submit"> Save</v-btn>
                    </v-card-actions>
                </v-form>
          </v-card-item>
        </v-card>
    </v-dialog>

    <CollectionPreview ref="collectionPreview" v-bind="fromPayload"/>
    <v-snackbar v-model="snackbar" :color="snackbarData.type">
    {{ snackbarData.message }}
  </v-snackbar>
</template>

<script lang="ts" setup>
import {REQUIRED_RULE} from "~/utils/formRules";
import {PRODUCT_SEO_META_IMAGE_RULES} from "~/utils/imageRules";
import {useCountryStore} from "~/stores/others/country";
import { ref } from 'vue';
import {addEditApiItem} from "~/utils/helpers/functions";
import CollectionPreview from "~/components/product/collection/CollectionPreview.vue";
import RichTextEditor from "~/components/form-elements/RichTextEditor.vue";

type Props = {
    slug: string;
    title: string;
    countryId: number;
    fullContent?: string|null;
    leftHalfContent?: string|null;
    rightHalfContent?: string|null;
    metaImageId?: number|null;
    metaTitle?: string|null;
    metaDescription?: string|null;
    isActive?: boolean;
}

const defaultValue = {
    slug: '',
    title: '',
    countryId: 1,
    fullContent: '',
    leftHalfContent: '',
    rightHalfContent: '',
    metaImageId: null,
    metaTitle: '',
    metaDescription: '',
    isActive: false,
};

const fromPayload = ref<Props>({...defaultValue});

const dialog = ref(false);
const snackbar = ref(false)
const snackbarData = ref({
  message: '',
  type: 'success'
})



const collectionPreview = ref<InstanceType<typeof CollectionPreview>>();
const preview = () => {
    collectionPreview.value?.show();
};

const form = ref(false);
const loading = ref(false);
const collectionId = ref<number|null>(null);

const countryStore = useCountryStore();
const getCountries = computed(() => countryStore.countries);

const handleImage = (val: any) => {
    fromPayload.value.metaImageId = val;
}

const show = async () => {
    collectionId.value = null;
    fromPayload.value = {...defaultValue};

    if(!countryStore?.countries?.length)
    {
        await countryStore.getAllCountries();
    }
    
    dialog.value = true;
};


const edit = async (id: number, payload: Props) => {
    collectionId.value = id;
    fromPayload.value = {...defaultValue, ...payload};
    if(!countryStore?.countries?.length)
    {
        await countryStore.getAllCountries();
    }
    dialog.value = true;
};

const emit = defineEmits(['reset']);
const fakeReceiver = ref([]);
const onSubmit = () => {
    if (!form.value) return;

    loading.value = true
    addEditApiItem(`product-collection`, collectionId.value || null, '', {
        ...fromPayload.value,
    }, fakeReceiver.value)
    .then(res => {
        snackbar.value = true;
        snackbarData.value.message = res.message
        snackbarData.value.type = (res.success ? 'success' : 'error');
        if(res.success) {
            dialog.value = false;
        }
        emit('reset');

    }).finally(() => {
        loading.value = false;
    })
};

defineExpose({show, edit});

</script>