<template>
    <v-dialog v-model="quickEditDialog" max-width="500px">
    <v-card>
        <v-form v-model="form" @submit.prevent="onSubmit" v-slot="{ isValid }">
            <v-card-text>
                <v-text-field
                label="Enter a number"
                type="number"
                v-model="numberValue"
                ></v-text-field>
            </v-card-text>
            <v-card-actions>
                <v-spacer></v-spacer>
                <v-btn :disabled="!isValid?.value" :title="isValid.value" :loading="loading" color="primary" variant="tonal" class="px-6" type="submit"> Save</v-btn>
            </v-card-actions>
        </v-form>
    </v-card>
  </v-dialog>
  <v-snackbar v-model="snackbar" :color="snackbarData.type">
    {{ snackbarData.message }}
  </v-snackbar>
</template>

<script lang="ts" setup>
import {addEditApiItem} from "~/utils/helpers/functions";

const quickEditDialog = ref(false);
const numberValue = ref();


const form = ref(false);
const loading = ref(false);
const snackbar = ref(false)
const snackbarData = ref({
  message: '',
  type: 'success'
});

const collectionId = ref();
const productId = ref();

const show = (_collectionId: number, _productId: number, _value?: number | null) => {
    collectionId.value = _collectionId;
    productId.value = _productId;
    numberValue.value = _value;
    quickEditDialog.value = true;
};

const emit = defineEmits(['saved']);
const fakeReceiver = ref([]);
const onSubmit = () => {
    if (!form.value) return;

    loading.value = true
    addEditApiItem(`product-collection/${collectionId.value}/sort`, productId.value || null, '', {
        sortOrder: numberValue.value,
    }, fakeReceiver.value)
    .then(res => {
        snackbar.value = true;
        snackbarData.value.message = res.message
        snackbarData.value.type = (res.success ? 'success' : 'error');
        if(res.success) {
            quickEditDialog.value = false;
        }
        emit('saved', {collectionId: collectionId.value, productId: productId.value, sortOrder: numberValue.value});

    }).finally(() => {
        loading.value = false;
    })
};


defineExpose({show});
</script>