<template>
<v-dialog v-model="dialog" transition="dialog-bottom-transition" style="max-width: 97%;">
    <v-card  elevation="10" style="min-height: 300px;">
        <div v-html="collection.fullContent"></div>
        <v-container fluid>
            <v-row>
                <v-col v-if="!!collection.leftHalfContent" md="6" sm="12" class="mb-3">
                    <div v-html="collection.leftHalfContent"></div>
                </v-col>

                <v-col v-if="!!collection.rightHalfContent" md="6" sm="12" class="mb-3">
                    <div v-html="collection.rightHalfContent"></div>
                </v-col>
            </v-row>
        </v-container>
    </v-card>

</v-dialog>
</template>

<script lang="ts" setup>
type Props = {
    slug: string;
    title: string;
    countryId: number;
    fullContent?: string|null;
    leftHalfContent?: string|null;
    rightHalfContent?: string|null;
    metaImageId?: number|null;
    metaTitle?: string|null;
    metaDescription?: string|null;
    isActive?: boolean;
}
const collection = defineProps<Props>();

const dialog = ref(false);

const show = () => {
    dialog.value = true;
}

defineExpose({show});
</script>