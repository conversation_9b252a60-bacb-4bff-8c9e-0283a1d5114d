<template>
        <SharedUiParentCard class="mt-6" title="Localize">
          <v-row>
            <v-col>
              <v-tabs bg-color="teal-darken-3" slider-color="teal-lighten-3" show-arrows v-model="activeTab">
                        <v-tab
                            v-for="c in countries"
                            :key="c.id"
                            :text="c.label"
                            :value="c.id"
                        ></v-tab>
                    </v-tabs>
            </v-col>
          </v-row>

          <div v-if="countries.length">
            <div v-for="country in countries" :key="country.id">
              <v-row v-show="activeTab == country.id" >
                <v-col>
                  <ProductLocalizeForm :country-id="activeTab" :product-id="productId" :product-data="country.product"/>
                </v-col>
              </v-row>
          </div>
          </div>
 
        </SharedUiParentCard>
</template>
<script setup lang="ts">
import ProductLocalizeForm from "~/components/product/localize/ProductLocalizeForm.vue";
import { ref } from 'vue';
import { getLocaLizeLanguages } from "~/utils/languages";
  
  // Types for Country
  interface Country {
    id: number;
    label: string;
    product: ProductLocalizeInput;
  }

  type LocaleAttribute = Record<number, any>;

  const props = defineProps<{
    productId: number;
    localeData?: LocaleAttribute;
  }>()


  const rules = [
  (value: any) => {
    if (value) return true;
    return 'You must enter a name'
  }
];

const activeTab = ref(2); // Controls the active tab
const countries = ref<Country[]>([]);

const show = async () => {
    activeTab.value = 2;

    const locale: LocaleAttribute = props.localeData ? props.localeData as LocaleAttribute : {};

    let countryData:Country[] = [];
    const list = [...getLocaLizeLanguages];
    for(const data of list)
    {
      const countryId = data.id as number;
      const row = Object.hasOwn(locale, countryId) ? locale[countryId] : null;

      countryData.push({
        id: countryId,
        label: data.name,
        product: {
          name: row?.name || '',
          description: row?.description || '',
          materialCare: row?.materialCare || '',
          tagLine1: row?.tagLine1 || '',
          tagLine2: row?.tagLine2 || '',
          tagLine2Type: row?.tagLine2Type || '',
          shippingReturn: row?.shippingReturn || '',
          wowFactors: row?.wowFactors || '',
          metaTitle: row?.metaTitle || '',
          metaDescription: row?.metaDescription || '',
        }
      });

    }

    countries.value = [...countryData];
};


onMounted(() => {
  show();
})

  defineExpose({show})
  </script>
  

