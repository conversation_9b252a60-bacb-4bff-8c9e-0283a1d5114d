<template>
     <v-form v-model="form" @submit.prevent="onSubmit" v-slot="{ isValid }">
        <v-row>
            <v-col>
              <FormElementsCommonTextFieldContainer v-model="product.name" label="Product Name"/>
            </v-col>
          </v-row>
          
        <SharedUiParentCard class="mt-6" title="Product Description">
                  <FormElementsRichTextEditor v-model="product.description"/>
                </SharedUiParentCard>
                <SharedUiParentCard class="mt-6" title="Product Material & Care">
                  <FormElementsRichTextEditor v-model="product.materialCare"/>
                </SharedUiParentCard>
                <!-- <SharedUiParentCard class="mt-6" title="Tagline">
                  <FormElementsRichTextEditor v-model="product.tagLine1"/>
                </SharedUiParentCard> -->

                <SharedUiParentCard class="mt-6" title="Tagline">
                  <FormElementsCommonTextFieldContainer class="" v-model="product.tagLine1" label="Tagline 1"/>
                  <v-row>
                    <v-col md="5">
                      <v-select :items="tagLine2TypeList" item-title="name" item-value="name"
                                v-model="product.tagLine2Type" placeholder="Select Parent Category"></v-select>
                    </v-col>
                    <v-col md="7">
                      <FormElementsCommonTextFieldContainer v-model="product.tagLine2" label="Tagline 2"/>
                    </v-col>
                  </v-row>
                </SharedUiParentCard>

                <SharedUiParentCard class="mt-6" title="Product Shipping & Return">
                  <FormElementsRichTextEditor v-model="product.shippingReturn"/>
                </SharedUiParentCard>
                <SharedUiParentCard class="mt-6" title="The WOW Factors">
                  <FormElementsRichTextEditor v-model="product.wowFactors"/>
                </SharedUiParentCard>


                <SharedUiParentCard class="mt-6" title="SEO Meta">
          <v-text-field label="Meta Title" v-model="product.metaTitle"/>
      <v-textarea label="Meta Description" v-model="product.metaDescription" rows="3"/>
        </SharedUiParentCard>

        <v-btn color="primary" variant="tonal" :disabled="!isValid?.value" type="submit" class="px-12 py-6 mt-5 ml-auto d-flex" >Save Localize</v-btn>
     </v-form>
</template>

<script lang="ts" setup>
import {REQUIRED_RULE} from "~/utils/formRules";
import {useProductsStore} from "~/stores/products";
import { useRolePermissionsStore } from "~/stores/administration/permissions";

const props = defineProps<{
    productId: number;
    countryId: number;
    productData: ProductLocalizeInput;
}>();

const tagLine2TypeList = ref([
  {
    name: 'Special',
  },
  {
    name: 'Promotional',
  },
  {
    name: 'Fire',
  },
  {
    name: 'Low',
  },
]);

const store = useProductsStore();
const permissionStore = useRolePermissionsStore();
const snackbar = useSnackbar();

const product = reactive<ProductLocalizeInput>({ ...(props.productData || {}) });

const form = ref(false);
const loading = ref(false);

const onSubmit = () => {
  if(!permissionStore.hasModulePermission(`${DynamicModuleEnum.PRODUCT_LOCALIZATION}:${DynamicPermissionEnum.UPDATE}`)){
    snackbar.add({
      type: "warning",
      text: "You don't have permission to see product list",
    })
    return;
  }
    const payload = {...product, ...{languageId: props.countryId}};

    loading.value = true
setTimeout(() => {
  store.updateProductLocalize(props.productId, payload).then((res: any) => {
    snackbar.add({
      type: res.success ? 'success' : 'error',
      text: res?.message?.data?.messasge,
    })

  }).catch(err => {
    console.log(err)
  })

  loading.value = false

}, 500);
}

</script>