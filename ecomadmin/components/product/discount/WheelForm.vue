<template>
  <v-form @submit.prevent="submit" ref="form" v-model="valid" lazy-validation>
    <v-card>
      <v-card-title>{{route?.params?.id ? "Update" : "Create"}} Wheel</v-card-title>
      <v-card-text>
        <!-- Basic Information -->
        <v-row>
          <v-col cols="12">
            <v-text-field
              v-model="wheel.title"
              label="Title"
              :rules="[REQUIRED_RULE]"
              required
            ></v-text-field>
          </v-col>
          <v-col cols="12" md="6">
            <v-combobox
              v-model="wheel.countryId"
              :items="countryStore?.countries || []"
              item-title="name"
              item-value="id"
              label="Available Country"
              :rules="[REQUIRED_RULE]"
              :return-object="false"
              :multiple="false"
              chips
            />
          </v-col>
          <v-col cols="12" md="3">
            <v-switch
              v-model="wheel.isActive"
              label="Active"
            ></v-switch>
          </v-col>
          <v-col cols="12" md="3">
            <v-text-field
              v-model="wheel.maxRetry"
              label="Max Retry"
              type="number"
              :min="1"
              :max="10"
              hint="Users can spin the wheel at most this many times."
              persistent-hint
            />
          </v-col>
          <v-col cols="12" md="6">
            <v-text-field
              v-model="wheel.backgroundImage"
              label="Background Image URL"
              placeholder="https://..."
              type="url"
              clearable
            >
              <template v-slot:prepend>
                <span>
                  <v-img v-if="!!wheel.backgroundImage"
                    :src="wheel.backgroundImage"
                    width="60"
                    height="60"
                  />
                </span>
              </template>
            </v-text-field>
          </v-col>
        </v-row>

        <v-card class="mb-5">
          <v-table
  >
    <thead>
      <tr>
        <th class="text-left">
          Label
        </th>
        <th class="text-left">
          Image URL
        </th>
        <th class="text-left">
          Color
        </th>
        <th class="text-left">
          Coupon
        </th>
        <th class="text-left">
          Weight
        </th>
      </tr>
    </thead>
    <draggable v-model="wheel.slices" tag="tbody" handle=".drag-handle">

      <template #item="{ element: item, index: i }">
      <tr
        class="position-relative"
      >
        <td>
          <v-text-field
              v-model="item.label"
              label="Label"
              :rules="[REQUIRED_RULE]"
              required
              class="my-1"
            >
              <template v-slot:prepend>
                <span class="drag-handle" style="cursor: move;">
                  <v-icon
                    icon="mdi-drag"
                    size="x-large"
                  />
                </span>
              </template>
              <template v-slot:append>
                <v-btn class="mt-2" icon="mdi-translate" @click="labelTranslateDialogueOpen(i, item)"></v-btn>
              </template>
          </v-text-field>
        </td>
        <td style="width: 250px;">
          <v-text-field
            v-model="item.imageUrl"
            label="Image URL"
            placeholder="https://..."
            type="url"
            clearable
          >
          <template v-slot:prepend>
                <span>
                  <v-img v-if="!!item.imageUrl"
                    :src="item.imageUrl"
                    width="45"
                    height="45"
                  />
                </span>
              </template>
        </v-text-field>
        </td>
        <td style="width: 300px;">
          <v-menu
            :close-on-content-click="false"
            location="top"
          >
            <template v-slot:activator="{ props }">
              <v-text-field
                v-model="item.color"
                label="Select Color"
                :rules="[REQUIRED_RULE]"
                readonly
                v-bind="props"
              >
              <template v-slot:append>
                <v-icon
                  :color="item.color"
                  icon="mdi-square"
                  size="x-large"
                />
              </template>
            </v-text-field>
            </template>
            <v-color-picker
              v-model="item.color"
            />
          </v-menu>
        </td>
        <td style="width: 300px;">
          <v-combobox
              v-model="item.coupon_id"
              :items="discountStore?.discounts || []"
              item-title="title"
              item-value="id"
              label="Coupon"
              :return-object="false"
              :multiple="false"
            />
        </td>
        <td style="width: 200px;">
          <div class="d-flex ga-2">
            <v-text-field
              v-model="item.weight"
              label="Weight"
              :rules="[REQUIRED_RULE]"
              required
              class="my-1"
              type="number"
              :min="0"
              :max="100"
            ></v-text-field>
            <v-btn class="mt-2" color="error" icon="mdi-delete" @click="removeSlice(i)"></v-btn>
          </div>
        </td>
      </tr>
      </template>
    </draggable>
    <tbody>
      <tr>
        <td colspan="5">
          <v-btn block prepend-icon="mdi-plus-circle" @click="addSlice">Add slice</v-btn>
        </td>
      </tr>
    </tbody>
  </v-table>
        </v-card>

      </v-card-text>

      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn
          color="primary"
          type="submit"
          flat
          :disabled="!valid"
          :loading="loading"
        >
          {{route?.params?.id ? "Update" : "Create"}} Wheel
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-form>
  <WheelLocalize ref="localizationDialog" @saved="sliceLocalizeSaved"/>
</template>
  
<script setup lang="ts">
import { ref, onMounted, withDefaults } from 'vue'
import { useCountryStore } from '~/stores/others/country';
import {SELECT_REQUIRED_RULE_MULTIPLE, REQUIRED_RULE} from "~/utils/formRules";
import type { Wheel, WheelSlice, WheelSliceLocale } from './types'
import { useDiscountsStore } from '~/stores/discounts';
import { useFortunateWheelStore } from '~/stores/fortunate-wheel';
import WheelLocalize from "./WheelLocalize.vue";
import draggable from 'vuedraggable';

const props = defineProps<{
  wheelData?: Wheel
}>();

const route = useRoute();
const router = useRouter();
// Form ref and validation
const form = ref()
const valid = ref(true)
const loading = ref(false)

const countryStore = useCountryStore();

const discountStore = useDiscountsStore();
const wheelStore = useFortunateWheelStore();

const defaultSlice: WheelSlice = {
    label: "",
    color: "#C62C2C",
    coupon_id: null,
    weight: 10,
    locale: {},
    imageUrl: '',
  }

// Wheel data
const wheel = ref<Wheel>({
  title: '',
  countryId: undefined,
  isActive: true,
  maxRetry: 1,
  slices: [defaultSlice],
  backgroundImage: null,
})

const activeTranslationIndex = ref<number>()

const submit = async () => {
  const { valid } = await form.value.validate()
  if (!valid) {
    return
  }

  loading.value = true

  try {
    const payload = wheel.value;
    if(payload?.id){
      // update an existing discount
      wheelStore
        ?.updateWheel(payload)
        ?.then((response: any)=>{
          if(response?.success){
            router.push('/discounts/wheel');
          }
        })
      return;
    }

    wheelStore
      ?.createWheel(payload)
      ?.then((response: any)=>{
        if(response?.success){
          router.push('/discounts/wheel');
        }
      })

  } catch (error) {
  //   toast.error(error.response?.data?.message || 'Failed to create discount')
  } finally {
    loading.value = false
  }
}

const updateWheelData = (data: Wheel)=>{
  wheel.value = {
    id: data.id,
    title: data.title,
    countryId: data.countryId,
    isActive: data.isActive,
    slices: data.slices || [],
    maxRetry: data.maxRetry,
    backgroundImage: data.backgroundImage,
  }
}

const addSlice = () => {
  const slices = wheel.value.slices || [];
  wheel.value = {...wheel.value, slices: [...slices, {
    label: "",
    color: "#C62C2C",
    coupon_id: null,
    weight: 10,
    locale: {},
  }]};
}

const removeSlice = (index: number) => {
  const slices = wheel.value.slices || [];
  slices.splice(index, 1);
  wheel.value = {...wheel.value, slices: slices};
}


const localizationDialog = ref<InstanceType<typeof WheelLocalize>>();
const labelTranslateDialogueOpen = (index: number, slice: WheelSlice) => {
  activeTranslationIndex.value = index;
  localizationDialog.value?.show(slice);
}

const sliceLocalizeSaved = (locale: WheelSliceLocale) => {

  const slices = wheel.value.slices || [];

  const _slices = slices.map((slice, i) => {
    if(i === activeTranslationIndex.value) {
      slice.locale = locale || {};
    }
    return slice;
  })

  wheel.value = {...wheel.value, slices: _slices};
  activeTranslationIndex.value = undefined;
}

// Lifecycle hooks
onMounted(async () => {
  discountStore?.fetchDiscounts();

  console.log('sync wheel 3', route?.params?.id, props)

  if(route?.params?.id && props?.wheelData){
    updateWheelData(props.wheelData);
  }

  countryStore.getAllCountries();
})
</script>