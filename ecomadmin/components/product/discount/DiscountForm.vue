<template>
  <v-form @submit.prevent="submit" ref="form" v-model="valid" lazy-validation>
    <v-row>
      <v-col cols="12" md="12">
        <SharedUiParentCardSolid :title="route?.params?.id ? 'Update' : 'Create'">
          <!-- Basic Information -->
          <v-row>
            <v-col cols="12" md="6">
              <v-text-field
                v-model="discount.title"
                label="Title"
                :rules="[REQUIRED_RULE]"
                required
                density="compact"
                hide-details
              ></v-text-field>
            </v-col>

            <v-col cols="12" md="6">
              <v-select
                v-model="discount.discount_type"
                :items="discountTypes"
                label="Discount Type"
                :rules="[REQUIRED_RULE]"
                :disabled="!!route?.params?.id"
                required
                @update:modelValue="resetDiscountSpecificFields"
                density="compact"
              ></v-select>
            </v-col>
            <v-col cols="12" md="12">
              <v-select
                v-model="discount.amount_type"
                :items="amountTypes"
                label="Amount Type"
                :rules="[REQUIRED_RULE]"
              ></v-select>
            </v-col>
            <v-col cols="12" md="4">
              <v-switch
                v-model="discount.is_active"
                label="Active"
                variant="flat"
                color="primary"
                density="compact"
                hide-details
              ></v-switch>
            </v-col>
            <v-col cols="12" md="4">
              <v-switch
                v-model="discount.is_automatic"
                label="Automatic"
                variant="flat"
                color="secondary"
                density="compact"
                hide-details
              ></v-switch>
            </v-col>
            <v-col cols="12" md="12" v-if="!discount.is_automatic">
              <v-text-field
                v-model="discount.coupon"
                label="Coupon Code"
                density="compact"
                hide-details
              ></v-text-field>
            </v-col>
            <template v-if="discount.discount_type == 'moneyOffProduct'">
              <v-col cols="12" md="6">
                <v-select
                  v-model="discount.applies_to"
                  :items="appliesToOptions"
                  label="Applies To"
                  :rules="[REQUIRED_RULE]"
                  required
                  @update:modelValue="resetProductSelection"
                ></v-select>
              </v-col>
            </template>
          </v-row>
        </SharedUiParentCardSolid>
      </v-col>
      <v-col cols="12" md="12">
        <SharedUiParentCardSolid title="Products">
          <template v-if="discount.discount_type == 'moneyOffProduct'">
            <!-- Specific Products -->
            <v-row v-if="discount.applies_to === 'specific'">
              <v-col cols="12" md="4">
                <div class="d-flex ga-1">
                  <v-btn color="primary" variant="flat" @click="handleImportProductsFromCategory(productSection='specific')">Import</v-btn>
                </div>
                <v-sheet height="200" class="py-3 overflow-y-auto">
                  <template v-for="category in categoryStore?.categories" :key="category.id">
                    <v-checkbox
                      v-model="selectedSpecificProductCategories"
                      :label="category.name"
                      :value="category.id"
                      hide-details
                      density="compact"
                    >
                      <template v-slot:prepend>
                        <v-avatar size="40">
                          <v-img :src="category?.imageGallery?.imageUrl" alt="Avatar" cover />
                        </v-avatar>
                      </template>
                    </v-checkbox>
                  </template>
                </v-sheet>
              </v-col>
              <v-col cols="12" md="8">
                <v-autocomplete
                  variant="outlined" 
                  hide-details
                  :rules="[SELECT_REQUIRED_RULE_MULTIPLE]"
                  :items="getProductsName"
                  item-title="name"
                  item-value="id"
                  label="Select Products"
                  multiple
                  v-model="selectedProducts"
                  @update:search="searchForProducts"
                  return-object
                  density="compact"
                >
                  <template v-slot:selection="{}"></template>
                  <template v-slot:item="{ props, item }">
                    <v-list-item
                      v-bind="props"
                      :prepend-avatar="item.raw.featuredImage?.imageUrl"
                      :subtitle="item.raw.group"
                      :title="item.raw.name"
                    >
                      <template #prepend>
                        <v-avatar size="40">
                          <v-img :src="item?.raw?.featuredImage?.imageUrl" alt="Avatar" cover />
                        </v-avatar>
                      </template>
                    </v-list-item>
                  </template>
                </v-autocomplete>

                <!-- Showing small size of selectedBuyProducts with price -->
                <v-sheet class="mt-3 d-flex ga-1 flex-wrap pa-3" color="#F08080" max-height="300" min-height="150">
                  <div v-for="item in selectedProducts" style="width: 70px;">
                    <v-tooltip :text="item?.name">
                      <template v-slot:activator="{ props }">
                        <div v-bind="props">
                          <v-avatar size="15" @click="handleRemoveSpecificProduct(item?.id)" class="cursor-pointer">
                            <v-icon icon="mdi-close"></v-icon>
                          </v-avatar>
                          <v-img :src="item?.featuredImage?.imageUrl" width="50" height="50" cover />
                          <p> {{ item?.discountPrice }} {{ item?.unitPrice }}</p>
                        </div>
                      </template>
                    </v-tooltip>
                  </div>
                </v-sheet>
                <v-btn color="primary" variant="flat" @click="selectedProducts = []" class="mt-2">Reset</v-btn> ({{ selectedProducts?.length }})
              </v-col>
            </v-row>
          </template>
          <!-- Buy X Get Y Fields -->
          <template v-if="discount.discount_type === 'buyXgetY'">
            <v-row>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="discount.buy_x_min_qty"
                  label="Buy Quantity (X)"
                  type="number"
                  :rules="[REQUIRED_RULE]"
                  required
                  density="compact"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="discount.buy_y_min_qty"
                  label="Get Quantity (Y)"
                  type="number"
                  :rules="[REQUIRED_RULE]"
                  required
                  density="compact"
                ></v-text-field>
              </v-col>
            </v-row>
            <v-row>
              <v-col cols="12" md="6">
                <v-row>
                  <v-col cols="12">
                    <div class="d-flex ga-1">
                      <h3 class="text-h4">Buy Products</h3>
                      <v-btn color="primary" variant="flat" @click="handleImportProductsFromCategory(productSection='buy')">Import</v-btn>
                    </div>
                    <v-sheet height="200" class="py-3 overflow-y-auto">
                      <template v-for="category in categoryStore?.categories" :key="category.id">
                        <v-checkbox
                          v-model="selectedBuyCategories"
                          :label="category.name"
                          :value="category.id"
                          hide-details
                          density="compact"
                        >
                          <template v-slot:prepend>
                            <v-avatar size="40">
                              <v-img :src="category?.imageGallery?.imageUrl" alt="Avatar" cover />
                            </v-avatar>
                          </template>
                        </v-checkbox>
                      </template>
                    </v-sheet>

                    <v-autocomplete
                      class="mt-5"
                      variant="outlined" 
                      hide-details
                      :rules="[SELECT_REQUIRED_RULE_MULTIPLE]"
                      :items="getBuyProductsName"
                      item-title="name"
                      item-value="id"
                      label="Buy Products"
                      multiple
                      v-model="selectedBuyProducts"
                      @update:search="searchForProducts"
                      density="compact"
                      return-object
                    >

                    <!-- HIDE selection completely -->
                    <template v-slot:selection="{}"></template>

                    <template v-slot:item="{ props, item }">
                      <v-list-item
                        v-bind="props"
                        :prepend-avatar="item.raw.featuredImage?.imageUrl"
                        :subtitle="item.raw.group"
                        :title="item.raw.name"
                      >
                        <template #prepend>
                          <v-avatar size="40">
                            <v-img :src="item?.raw?.featuredImage?.imageUrl" alt="Avatar" cover />
                          </v-avatar>
                        </template>
                      </v-list-item>
                    </template>
                  </v-autocomplete>
                  </v-col>
                  <v-col cols="12">
                      <!-- Showing small size of selectedBuyProducts with price -->
                    <v-sheet class="d-flex ga-1 flex-wrap pa-3 overflow-y-auto" color="#E1BEE7" max-height="300" min-height="150">
                      <div v-for="item in selectedBuyProducts" style="width: 70px; height: 60px;">
                        <v-tooltip :text="item?.name">
                          <template v-slot:activator="{ props }">
                            <div v-bind="props">
                              <v-avatar size="15" @click="handleRemoveBuyProduct(item?.id)" class="cursor-pointer">
                                <v-icon icon="mdi-close"></v-icon>
                              </v-avatar>
                              <v-img :src="item?.featuredImage?.imageUrl" width="50" height="50" cover />
                              <p> {{ item?.discountPrice }} {{ item?.unitPrice }}</p>
                            </div>
                          </template>
                        </v-tooltip>
                      </div>
                    </v-sheet>
                    <v-btn color="primary" variant="flat" @click="selectedBuyProducts = []">Reset</v-btn> ({{ selectedBuyProducts?.length }})
                  </v-col>
                </v-row>
              </v-col>
              <v-col cols="12" md="6">
                <v-row>
                  <v-col cols="12">
                    <div class="d-flex ga-1">
                      <h3 class="text-h4">Get Products</h3>
                      <v-btn color="primary" variant="flat" @click="handleImportProductsFromCategory(productSection='get')">Import</v-btn>
                    </div>
                    <v-sheet height="200" class="py-3 overflow-y-auto">
                      <template v-for="category in categoryStore?.categories" :key="category.id">
                        <v-checkbox
                          v-model="selectedGetCategories"
                          :label="category.name"
                          :value="category.id"
                          hide-details
                          density="compact"
                        >
                          <template v-slot:prepend>
                            <v-avatar size="40">
                              <v-img :src="category?.imageGallery?.imageUrl" alt="Avatar" cover />
                            </v-avatar>
                          </template>
                        </v-checkbox>
                      </template>
                    </v-sheet>

                    <v-autocomplete
                      class="mt-5"
                      variant="outlined" 
                      :rules="[SELECT_REQUIRED_RULE_MULTIPLE]"
                      :items="getGetProductsName"
                      item-title="name"
                      item-value="id"
                      label="Get Products"
                      multiple
                      v-model="selectedGetProducts"
                      @update:search="searchForProducts"
                      density="compact"
                      return-object
                    >
                    <template v-slot:selection="{}"></template>
                    <template v-slot:item="{ props, item }">
                      <v-list-item
                        v-bind="props"
                        :prepend-avatar="item.raw.featuredImage?.imageUrl"
                        :subtitle="item.raw.group"
                        :title="item.raw.name"
                      >
                        <template #prepend>
                          <v-avatar size="40">
                            <v-img :src="item?.raw?.featuredImage?.imageUrl" alt="Avatar" cover />
                          </v-avatar>
                        </template>
                      </v-list-item>
                    </template>
                  </v-autocomplete>
                  </v-col>
                  <v-col cols="12">
                      <!-- Showing small size of selectedBuyProducts with price -->
                    <v-sheet class="d-flex ga-1 flex-wrap pa-3" color="#B2EBF2" max-height="300" min-height="150">
                      <div v-for="item in selectedGetProducts" style="width: 70px;">
                        <v-tooltip :text="item?.name">
                          <template v-slot:activator="{ props }">
                            <div v-bind="props">
                              <v-avatar size="15" @click="handleRemoveGetProduct(item?.id)" class="cursor-pointer">
                                <v-icon icon="mdi-close"></v-icon>
                              </v-avatar>
                              <v-img :src="item?.featuredImage?.imageUrl" width="50" height="50" cover />
                              <p> {{ item?.discountPrice }} {{ item?.unitPrice }}</p>
                            </div>
                          </template>
                        </v-tooltip>
                      </div>
                    </v-sheet>
                    <v-btn color="primary" variant="flat" @click="selectedGetProducts = []">Reset</v-btn> ({{ selectedGetProducts?.length }})
                  </v-col>
                </v-row>
              </v-col>
            </v-row>
          </template>
        </SharedUiParentCardSolid>
      </v-col>
    </v-row>
      
    <v-row>
      <v-col cols="12">
        <SharedUiParentCardSolid title="Countries">
          <!-- Countries Selection -->
          <v-row>
            <v-col cols="12">
              <v-combobox
                v-model="selectedCountries"
                :items="countryStore?.countries || []"
                item-title="name"
                item-value="id"
                label="Available Countries"
                :rules="[SELECT_REQUIRED_RULE_MULTIPLE]"
                :return-object="false"
                multiple
                chips
                @update:modelValue="syncCountySelectionHandler"
                variant="outlined"
                density="compact"
              >
              </v-combobox>
            </v-col>
          </v-row>

          <v-table variant="outlined">
            <thead>
              <tr>
                <th class="text-left">
                  Country
                </th>
                <th class="text-left">
                  Amount
                </th>
                <th class="text-left">
                  Min Amount
                </th>
                <th class="text-left">
                  Min Quantity
                </th>
                <th class="text-left">
                  Start Date
                </th>
                <th class="text-left">
                  End Date
                </th>
                <th class="text-left text-h6">
                  ☃ Open Scheduling
                </th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="item in discount?.countries || []"
                :key="item.countryId"
              >
                <td>{{ getCountryName(item.countryId) }}</td>
                <td>
                  <v-text-field
                    v-model="item.amount"
                    label=""
                    type="number"
                    hide-details
                    :rules="[REQUIRED_RULE, discount.amount_type == 'percentage' ? PERCENTANGE_MAX_100_RULE : () => true]"
                    required
                    :min="0"
                    :max="discount.amount_type == 'percentage' ? 100 : 9999999"
                    class="my-1"
                    :suffix="discount.amount_type == 'fixed' ? getCountryCurrency(item.countryId) : '%'"
                    density="compact"
                  ></v-text-field>
                </td>
                <td>
                  <v-text-field
                    v-model="item.min_amount"
                    label=""
                    hide-details
                    type="number"
                    class="my-1"
                    density="compact"
                  ></v-text-field>
                </td>
                <td>
                  <v-text-field
                    v-model="item.min_quantity"
                    label=""
                    hide-details
                    type="number"
                    class="my-1"
                    density="compact"
                  ></v-text-field>
                </td>
                <td>
                  <!-- <v-menu :close-on-content-click="false" v-model="item.startDateMenu">
                    <template v-slot:activator="{ props }">
                      <v-text-field
                        v-model="item.start_at"
                        label="Start Date"
                        prepend-icon="mdi-calendar-clock"
                        readonly
                        :rules="[REQUIRED_RULE]"
                        required
                        v-bind="props"
                        density="compact"
                      ></v-text-field>
                    </template>
                    <v-date-picker
                      v-model="item.start_at"
                      :min="new Date()"
                      @update:modelValue="(val) => {
                        item.startDateMenu = false;
                        item.start_at = new Date(val)
                      }"
                      >
                    </v-date-picker>
                  </v-menu> -->
                  <input type="datetime-local" v-model="item.start_at" :min="new Date().toISOString().slice(0, 16)" />
                </td>
                <td>
                  <!-- <v-menu :close-on-content-click="false" v-model="item.endDateMenu">
                      <template v-slot:activator="{ props }">
                        <v-text-field
                          v-model="item.end_at"
                          label="End Date"
                          prepend-icon="mdi-calendar"
                          readonly
                          v-bind="props"
                          density="compact"
                        ></v-text-field>
                      </template>
                      <v-date-picker
                        v-model="item.end_at"
                        :min="item.start_at"
                        @update:modelValue="(val) => {
                          item.endDateMenu = false;
                          item.end_at = new Date(val)
                        }"
                      >
                    </v-date-picker>
                  </v-menu> -->
                  <input type="datetime-local" v-model="item.end_at" />
                </td>
                <td>
                  <v-checkbox v-model="item.isTimeScheduling" label="active"></v-checkbox>
                </td>
              </tr>
            </tbody>
          </v-table>
        </SharedUiParentCardSolid>
      </v-col>
    </v-row>


    <v-row>
      <v-col cols="12">
        <SharedUiParentCardSolid title="Maximum Usages">
        <v-row>
          <v-col cols="12" md="6">
            <v-text-field
              v-model="discount.max_customer_use"
              label="Maximum usages for customer"
              type="number"
              required
            ></v-text-field>
          </v-col>
          <v-col cols="12" md="6">
            <v-text-field
              v-model="discount.max_use"
              label="Maximum customers"
              type="number"
              required
            ></v-text-field>
          </v-col>
        </v-row>
        <v-btn
          color="primary"
          type="submit"
          variant="flat"
          :disabled="!valid"
          :loading="loading"
        >
          {{route?.params?.id ? "Update" : "Create"}} Discount
        </v-btn>
        </SharedUiParentCardSolid>
      </v-col>
    </v-row>
  </v-form>
</template>
  
<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import { useCountryStore } from '~/stores/others/country';
import {SELECT_REQUIRED_RULE_MULTIPLE, REQUIRED_RULE, PERCENTANGE_MAX_100_RULE} from "~/utils/formRules";
import type { Discount } from './types'
import { useRolePermissionsStore } from "~/stores/administration/permissions";
import { useDiscountsStore } from '~/stores/discounts';
import { useProductsStore } from '~/stores/products';
import { SharedUiParentCardSolid } from '#components';
import { useProductCategoriesStore } from '~/stores/products/categories';
import { debounce } from 'perfect-debounce';
import { useSnackbar } from 'vue3-snackbar';

const props = defineProps({
  discountData: null,
})
const route = useRoute();
const router = useRouter();
const snackbar = useSnackbar();

// Form ref and validation
const form = ref()
const valid = ref(true)
const loading = ref(false)

const productStore = useProductsStore();
const countryStore = useCountryStore();
const permissionStore = useRolePermissionsStore();
const categoryStore = useProductCategoriesStore();
const discountStore = useDiscountsStore();

const getProductsName = computed(() => {
  return productStore.products
});
const getBuyProductsName = computed(() => {
  return productStore.products;
});
const getGetProductsName = computed(() => {
  return productStore.products;
});

// Discount data
const discount = ref<Discount>({
  title: '',
  discount_type: 'moneyOffProduct',
  amount_type: 'fixed',
  applies_to: 'all',
  is_active: true,
  is_automatic: true,
  coupon: '',
  buy_x_min_qty: null,
  buy_y_min_qty: null,
  productIds: [],
  buyProductIds: [],
  getProductIds: [],
  countries: [],
  max_customer_use: null,
  max_use: null,
})

// Select options
const discountTypes = [
  { title: 'Product Discount', value: 'moneyOffProduct' },
  { title: 'Buy X Get Y', value: 'buyXgetY' },
  { title: 'Order Discount', value: 'moneyOffOrder' },
  { title: 'Shipping Discount', value: 'shipping' }
]

const amountTypes = [
  { title: 'Fixed Amount', value: 'fixed' },
  { title: 'Percentage', value: 'percentage' }
]

const appliesToOptions = [
  { title: 'All Products', value: 'all' },
  { title: 'Specific Products', value: 'specific' },
]

type Product = {
  id: number
}
  
// Product and country selections
const selectedProducts = ref<number[]>(props.discountData?.productIds || [])
const selectedBuyProducts = ref<number[]>(props.discountData?.buyProductIds || [])
const selectedGetProducts = ref<number[]>(props.discountData?.getProductIds || [])
const selectedCountries = ref<number[]>(props.discountData?.countryIds || [])
const products = ref<Product[]>([])

const searchForProducts = debounce(async (search: any, categoryIds: any) => {

    if(!permissionStore.hasModulePermission(`${DynamicModuleEnum.PRODUCT}:${DynamicPermissionEnum.READ_MANY}`)){
      throw createError({
        statusCode: 404,
        statusMessage: "You don't have permission to READ MANY",
      })
    }
    
    await productStore.getProducts(
      "ASC",
      1,
      20,
      '',
      search,
      "",
      "",
      categoryIds || "",
      false,
      true
    );
  },
  1000
)

const hanldeCategoryBasedProductSearch = async (search: any, categoryIds: any) => {

  if(!permissionStore.hasModulePermission(`${DynamicModuleEnum.PRODUCT}:${DynamicPermissionEnum.READ_MANY}`)){
    throw createError({
      statusCode: 404,
      statusMessage: "You don't have permission to READ MANY",
    })
  }
  
  await productStore.getProducts(
    "ASC",
    1,
    20,
    '',
    search,
    "",
    "",
    categoryIds || "",
    false,
    true
  );
}
  
// Watchers
// watch(selectedProducts, (newVal) => {
//   discount.value.productIds = [...newVal];
// })

const syncCountySelectionHandler = () => {

  const old = discount.value?.countries || [];
  const newIds = selectedCountries.value || [];

  const _new = newIds.map((countryId) => {

    const existing = old.find(o => o.countryId == countryId);

    if(existing && existing.countryId) {
      return existing;
    }

    return {
      countryId: countryId,
      amount: 0,
      min_amount: null,
      min_quantity: null,
      startAt: Math.floor(Date.now() / 1000), // Current timestamp
      endAt: null,
      start_at: new Date().toISOString().slice(0, 16), // For date picker
      end_at: null, // For date picker
      startDateMenu: false,
      endDateMenu: false,
      isTimeScheduling: false,
    };
  }) || []

  discount.value = {...discount.value, countries: _new};
}

const resetDiscountSpecificFields = () => {
  selectedProducts.value = []
  selectedBuyProducts.value = []
  selectedGetProducts.value = []
  discount.value.buy_x_min_qty = null
  discount.value.buy_y_min_qty = null
}

const resetProductSelection = () => {
  selectedProducts.value = []
}

const handleRemoveSpecificProduct = (id: number) => {
  selectedProducts.value = selectedProducts.value.filter((item) => item?.id !== id);
}

const handleRemoveBuyProduct = (id: number) => {
  selectedBuyProducts.value = selectedBuyProducts.value.filter((item) => item?.id !== id);
}

const handleRemoveGetProduct = (id: number) => {
  selectedGetProducts.value = selectedGetProducts.value.filter((item) => item?.id !== id);
}

function parseLocalToUtcTimestamp(datetimeLocal) {
  const [date, time] = datetimeLocal.split('T'); // "2025-07-17", "01:59"
  const [year, month, day] = date.split('-').map(Number);
  const [hour, minute] = time.split(':').map(Number);
  return Math.floor(Date.UTC(year, month - 1, day, hour, minute) / 1000);
}

const submit = async () => {
  const { valid } = await form.value.validate()
  if (!valid) {
    snackbar.add({
      type: 'warning',
      text: 'Please fill all required fields',
    })
    return
  }

  loading.value = true
  try {
    // Convert date strings to timestamps for each country
    const countriesWithTimestamps = discount.value.countries.map(country => ({
      countryId: country.countryId,
      amount: country.amount,
      min_amount: country.min_amount,
      min_quantity: country.min_quantity,
      startAt: country.start_at ? parseLocalToUtcTimestamp(country.start_at) : Math.floor(Date.now() / 1000),
      endAt: country.end_at ? parseLocalToUtcTimestamp(country.end_at) : null,
      isTimeScheduling: country?.isTimeScheduling || false
    }));
    // update an existing discount
    if(discount.value?.discount_type === 'buyXgetY'){
      discount.value.buyProductIds = selectedBuyProducts.value.map((item) => item?.id);
      discount.value.getProductIds = selectedGetProducts.value.map((item) => item?.id);      
    }
    discount.value.productIds = selectedProducts?.value?.map(item => item?.id);

    const payload = {
      ...discount.value,
      countries: countriesWithTimestamps,
    }
    if(payload?.id){
      discountStore
        ?.updateDiscount(payload)
        ?.then((response: any)=>{
          snackbar.add({
            type: 'success',
            text: 'Discount updated successfully',
          })
          if(response?.success){
            router.push('/discounts');
          }
        }).catch((error: any)=>{
          snackbar.add({
            type: 'error',
            text: 'Discount update failed',
          })
          console.log(error);
        })
      return;
    }
    discountStore
      ?.createDiscount(payload)
      ?.then((response: any)=>{
        snackbar.add({
          type: 'success',
          text: 'Discount created successfully',
        })
        if(response?.success){
          router.push('/discounts');
        }
      }).catch((error: any)=>{
        snackbar.add({
          type: 'error',
          text: 'Discount creation failed',
        })
        console.log(error);
      })

  } catch (error) {
  } finally {
    loading.value = false
  }
}

function formatForDatetimeLocal(ts: number): string {
  const date = new Date(ts * 1000); // Convert seconds to milliseconds
  const offset = -date.getTimezoneOffset();
  const pad = (n: number) => n.toString().padStart(2, '0');

  const year = date.getUTCFullYear();
  const month = pad(date.getUTCMonth() + 1);
  const day = pad(date.getUTCDate());
  const hours = pad(date.getUTCHours());
  const minutes = pad(date.getUTCMinutes());

  return `${year}-${month}-${day}T${hours}:${minutes}`;
}

const updateDiscountData = (data:any)=>{
  // Convert countries with timestamps to include UI date fields
  const countriesWithDates = (data?.countries || []).map((country: any) => {
      return {
        ...country,
        start_at: country.startAt ? formatForDatetimeLocal(country.startAt) : formatForDatetimeLocal(Date.now() / 1000),
        end_at: country.endAt ? formatForDatetimeLocal(country.endAt) : null,
        startDateMenu: false,
        endDateMenu: false,
        isTimeScheduling: country?.isTimeScheduling || false,
      }
    }
  );

  discount.value = {
    id: data?.id,
    title: data?.title,
    discount_type: data?.discount_type,
    amount_type: data?.amount_type,
    applies_to: data?.applies_to,
    is_active: data?.is_active,
    is_automatic: data?.is_automatic,
    coupon: data?.coupon,
    buy_x_min_qty: data?.buy_x_min_qty,
    buy_y_min_qty: data?.buy_y_min_qty,
    productIds: data?.productIds,
    buyProductIds: data?.buyProductIds,
    getProductIds: data?.getProductIds,
    countries: countriesWithDates,
    max_customer_use: data?.max_customer_use || null,
    max_use: data?.max_use || null,
  }
}


const getCountryName = (countryId) => {
  return countryStore?.countries?.find(item => item?.id == countryId)?.name
}

const getCountryCurrency = (countryId) => {
  return countryStore?.countries?.find(item => item?.id == countryId)?.currency?.currency || ''
}

const fetchCategories = async () => {
  if(categoryStore?.categories?.length <= 0){
    const page = 1;
    const order = 'ASC';
    const sortKey = 'name';
    const itemsPerPage = 100;
    const search = '';
    await categoryStore.getCategories(order, page, itemsPerPage, sortKey, search);
  }
}

const selectedSpecificProductCategories = ref([]);
const selectedBuyCategories = ref([]);
const selectedGetCategories = ref([]);

const handleImportProductsFromCategory = async (productSection: string)=>{
  if(productSection === 'buy'){
    productStore.products = [];
    let selectedBuyIds = selectedBuyProducts?.value?.map((item) => item?.id);
    await hanldeCategoryBasedProductSearch('', selectedBuyCategories?.value?.join(','));
    let products = productStore?.products?.filter((item: any) => !selectedBuyIds.includes(item.id));
    selectedBuyProducts.value = [...selectedBuyProducts.value, ...products];
    productStore.products = [];
  }
  else if(productSection === 'get'){
    productStore.products = [];
    let selectedGetIds = selectedGetProducts?.value?.map((item) => item?.id);
    await hanldeCategoryBasedProductSearch('', selectedGetCategories?.value?.join(','));
    let products = productStore?.products?.filter((item: any) => !selectedGetIds.includes(item.id));
    selectedGetProducts.value = [...selectedGetProducts.value, ...products];
    productStore.products = [];
  }
  else if(productSection === 'specific'){
    productStore.products = [];
    let selectedSpecificProductIds = selectedProducts?.value?.map((item) => item?.id);
    await hanldeCategoryBasedProductSearch('', selectedSpecificProductCategories?.value?.join(','));
    let products = productStore?.products?.filter((item: any) => !selectedSpecificProductIds.includes(item.id));
    selectedProducts.value = [...selectedProducts.value, ...products];
    productStore.products = [];
  }
}

const handleFetchAllCountries = ()=>{
  if(countryStore?.countries?.length <=0){
    countryStore.getAllCountries();
  }
}

onMounted(async () => {
  fetchCategories();
  if(route?.params?.id){
    updateDiscountData(props?.discountData);
  }
  handleFetchAllCountries();
})
</script>