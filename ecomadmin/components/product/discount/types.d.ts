export type DiscountType = 'moneyOffProduct' | 'buyXgetY' | 'moneyOffOrder' | 'shipping';
export type AmountType = 'fixed' | 'percentage';
export type AppliesTo = 'all' | 'specific' | 'segment';

export type DiscountCountry = {
  countryId: number
  amount: number
  min_amount?: null | number
  min_quantity?: null | number
  startAt: number // Unix timestamp
  endAt?: null | number // Unix timestamp
  // UI-specific fields for date pickers
  start_at?: string // ISO date string for date picker
  end_at?: string | null // ISO date string for date picker
  startDateMenu?: boolean
  endDateMenu?: boolean
  isTimeScheduling?: boolean
}

export interface Discount {
    id?: number;
    title: string;
    discount_type: DiscountType;
    amount_type: AmountType;
    applies_to: AppliesTo;
    is_active: boolean;
    is_automatic: boolean;
    coupon: string | null;
    buy_x_min_qty: number | null;
    buy_y_min_qty: number | null;
    productIds: number[];
    buyProductIds: number[];
    getProductIds: number[];
    eligibility_type?: number;
    max_customer_use?: number | null;
    max_use?: number | null;
    countries: DiscountCountry[];
}

type WheelSliceLocale = Record<string, string | null>
export interface WheelSlice {
  label: string;
  color: string;
  coupon_id?: number | null;
  weight: number;
  locale: WheelSliceLocale;
  imageUrl?: string;
}
export interface Wheel {
  id?: number;
  title: string;
  isActive: boolean;
  countryId?: number;
  slices: WheelSlice[];
  maxRetry: 1;
  backgroundImage?: string | null;
}