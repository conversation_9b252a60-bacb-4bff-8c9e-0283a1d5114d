<template>
  <SharedUiParentCardSolid class="mt-1" title="Product Size Chart">
    <v-autocomplete
      :model-value="props.modelValue"
      label="Select Size Chart"
      :items="getBrands"
      item-title="name"
      item-value="id"
      clearable
      density="compact"
      variant="outlined"
      @update:modelValue="updateValue"
    ></v-autocomplete>
  </SharedUiParentCardSolid>
</template>
  
<script lang="ts" setup>
import { computed, onMounted } from "vue";
import { useProductChartStore } from "~/stores/products/product-chart";

interface Props {
  modelValue?: number;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: undefined,
});
const brands = ref([]);
const customFilter = (item, queryText, itemText) => {
  const textOne = item.name.toLowerCase();
  const searchText = queryText.toLowerCase();
  return textOne.includes(searchText);
};

const brandStore = useProductChartStore();
const getBrands = computed(() => brandStore.charts);

const emit = defineEmits(["update:modelValue"]);

const updateValue = (event: any) => {
  emit("update:modelValue", event);
};

onMounted(async () => {
  await brandStore.getAllActiveChart();
});
</script>
  