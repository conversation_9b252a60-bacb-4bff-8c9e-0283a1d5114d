<template>

  <SharedUiParentCard class="mt-6" title="Product Category">
    <v-autocomplete variant="outlined"
        :model-value="props.modelValue"
        label="Select Category"
        :items="flattenCategories"
        item-title="name"
        item-value="id"
        multiple
        @update:modelValue="updateValue"
        density="compact"
    >
      <template #item="{ props, item }">
        <v-list-item v-bind="props"
                     :style="{ paddingLeft: (item.raw.level * 20) + 'px' }"></v-list-item>
      </template>
    </v-autocomplete>
  </SharedUiParentCard>

</template>

<script lang="ts" setup>
import {computed, onMounted, ref} from "vue";
import {flatten} from "~/utils/helpers/functions";
import {useProductCategoriesStore} from "~/stores/products/categories";

const categoryStore = useProductCategoriesStore();

interface Props {
  modelValue: number[]
}

const options = ref({
  page: 1,
  itemsPerPage: 100,
  sortBy: ['name'],
  sortDesc: [false],
  search: '',
});

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => [] as number[],
})

const emit = defineEmits(['update:modelValue']);

const flattenCategories= computed(() => flatten(categoryStore.getCategoriesTree, 0))
const updateValue = async (event: any) => {

  emit('update:modelValue', event);


}

onMounted(async () => {

  await categoryStore.getAllActiveCategories();

})

</script>
