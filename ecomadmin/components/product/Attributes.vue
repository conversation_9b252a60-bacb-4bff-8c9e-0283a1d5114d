<template>
  <SharedUiParentCard title="Product Attributes">
    <v-row>
      <v-col cols="6" v-for="(attribute, index) in attributesList" :key="index">
<!--        <v-select :label="'Select ' + attribute.name" multiple :items="getAttributeValues(attribute.id)"-->
<!--                  item-title="name" item-value="id"-->
<!--                  :model-value="(selectedAttributes && selectedAttributes.length>0 && attribute.id == 1)? selectedAttributes[0]?.values:selectedAttributes[1]?.values"-->
<!--                  @update:modelValue="selected($event, attribute.id)"-->
<!--        >-->
<!--        </v-select>-->
        <v-autocomplete :label="'Select ' + attribute.name" :items="getAttributeValues(attribute.id)"
                        :model-value="(selectedAttributes && selectedAttributes.length>0 && attribute.id == 1)? selectedAttributes[0]?.values:selectedAttributes[1]?.values"
                        @update:modelValue="selected($event, attribute.id)"
                        chips closable-chips
                        item-title="name" item-value="id" multiple />
      </v-col>
    </v-row>
<!--    <v-btn color="primary" @click="generateVariation" v-if="generating">Generate Variations</v-btn>-->
<!--    <v-btn color="error" class="ms-2"  @click="resetVariation" v-if="reset">Reset Variations</v-btn>-->

  </SharedUiParentCard>
</template>
<script lang="ts" setup>
interface Props {
  generating?: boolean,
  reset?: any,
  selectedAttributes: any[]
}

const props = defineProps<Props>()

const emit = defineEmits(['selectedValues', 'generate', 'reset']);

import {useProductAttributesStore} from "~/stores/products/attributes";
import {computed, onMounted} from "vue";
import {useAttrValueStore} from "~/stores/products/attribute-values";

const attributesStore = useProductAttributesStore();
const attrValStore = useAttrValueStore();

const getAttrValues = computed(() => attrValStore.attrValues);
const attributesList = computed(() => attributesStore.attributes);
const getAttributeValues = (id: any) => getAttrValues.value.filter((item: any) => item.attributeId === id);

const selected = (event: any, attributeId: any) => {
  const selectedItem: SelectedAttributeValues = {};
  selectedItem.attributeId = attributeId;
  selectedItem.values = event;
  // console.log(' props.selectedAttributes', props.selectedAttributes);
  
  const index = props.selectedAttributes.findIndex((item: any) => item.attributeId === attributeId);

  if( index === -1 ) {
    props.selectedAttributes.push(selectedItem)
  }else {
    props.selectedAttributes[index].values = event
  }

  emit('selectedValues', props.selectedAttributes);

}
const generateVariation = () => {
  emit('generate', props.selectedAttributes)
}
const resetVariation = () => {
  while(props.selectedAttributes.length>0){
    props.selectedAttributes.pop();
  }
  emit('selectedValues', props.selectedAttributes);
  emit('reset')
}

onMounted(async () => {
  await attributesStore.getAttributes({});
  await attrValStore.getAllAttributeValues();
  // await attrValStore.getValues({});
});

watch(() => props.selectedAttributes, (first) => {
  emit('generate', first)
}, { deep: true });



</script>