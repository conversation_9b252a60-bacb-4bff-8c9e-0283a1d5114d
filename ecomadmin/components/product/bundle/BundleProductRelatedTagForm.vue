<template>
    <v-dialog v-model="dialog" scrollable eager :retain-focus="false" transition="dialog-bottom-transition" style="max-width: 85%;" persistent>


    <v-card  elevation="10">
        <v-card-title class="d-flex justify-space-between align-center pa-4">
          Bundle: {{ mainProduct?.name }} ({{ mainProduct?.sku }})
          <v-spacer></v-spacer>
          <v-btn icon size="small" @click="dialog = false">
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-card-title>
            <v-card-item class="py-4 px-6">
                <draggable :itemKey="'tag_product'" v-model="selectedItems" tag="div" class="d-flex flex-wrap ga-2">
                    <template #item="{ element }">
                            <v-sheet class="pa-2 position-relative">
                                <v-avatar @click="handleDeleteRelatedProductsApi(element)" class="position-absolute cursor-pointer" color="error" size="15" style="top: 1px; right: 1px;">
                                    <v-icon icon="mdi-close" size="10"></v-icon>
                                </v-avatar>
                                <div class="cursor-move pa-1 border d-flex flex-column">
                                    <v-img :src="element?.image" width="70" height="70" />
                                    <h6 class="text-caption font-weight-bold text-truncate" style="width: 100px">{{ element.name }}</h6>
                                </div>
                            </v-sheet>
                    </template>
                </draggable>
                <v-form v-model="form" @submit.prevent="onSubmit" v-slot="{ isValid }">
                    <SharedUiParentCard v-if="dialog" class="mt-1 mb-5" title="Select products">
                        <v-autocomplete
                            variant="outlined" 
                            hide-details
                            :items="getProductsName"
                            item-title="name"
                            item-value="id"
                            label="Select Products"
                            clearable
                            chips
                            closable-chips
                            multiple
                            v-model="selectedIds"
                            @update:search="searchForProducts"
                            @update:modelValue="updateSelectedItems"
                            class="pa-1"
                        >
                            <template #item="{props, item}">
                                <v-list-item v-bind="props" color="info" class="my-1">
                                    <template #prepend>
                                        <img :width="40" :height="40" :src="item?.raw?.image"/>
                                    </template>
                                    <v-list-item-title>{{ item?.raw?.name }}</v-list-item-title>
                                </v-list-item>
                            </template>
                        </v-autocomplete> 
                    </SharedUiParentCard>

                    <v-card-actions>
                        <v-btn :disabled="!isValid?.value" :title="isValid.value" :loading="loading" color="primary" variant="tonal" class="px-6" type="submit"> Save</v-btn>
                    </v-card-actions>
                </v-form>
          </v-card-item>
        </v-card>
    </v-dialog>

    <v-snackbar v-model="snackbar" :color="snackbarData.type">
    {{ snackbarData.message }}
  </v-snackbar>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import {addEditApiItem, deleteApiItem, getApiItems} from "~/utils/helpers/functions";

const dialog = ref(false);
const snackbar = ref(false)
const snackbarData = ref({
  message: '',
  type: 'success'
})


const getProductsName = ref([]);

const form = ref(false);
const loading = ref(false);

const mainProduct = ref();


const selectedIds = ref(null);
const selectedItems = ref([]);

const show = async (item: any) => {
    mainProduct.value = item;
    selectedIds.value = null;
    selectedItems.value = [];

    handleFetchRelatedProductsApi();

    // searchForProducts('ALL');
    dialog.value = true;
};

watch(selectedIds, (newVal, oldVal)=>{
    if(newVal){
        let unselectedIds = oldVal?.filter(function(obj) { return newVal?.indexOf(obj) == -1; });
        let newItems = getProductsName.value.filter((el)=> selectedIds?.value?.includes(el?.id));
        console.log(">>>", unselectedIds);
        selectedItems.value = selectedItems.value.filter(item => !unselectedIds?.includes(item.id));
        console.log(unselectedIds);
        if (newItems?.length > 0) {
            // Merge existing and new items, ensuring uniqueness based on `id`
            selectedItems.value = Array.from(
                new Map([...selectedItems?.value, ...newItems].map((item) => [item?.id, item])).values()
            );
        }
    }
})

const updateSelectedItems = (newSelectedIds:any)=>{
    // const existingIds = selectedItems.value.map((el) => el?.id);
    // let filterSelected  = newSelectedIds.filter((el)=> !existingIds.includes(el?.id))
    // console.log(existingIds, filterSelected);
    
}

const handleFetchRelatedProductsApi= async()=>{
    const response = await getApiItems(`product-related/admin/getRelatedProductsById`, {page:1, take:50, countryId:1, productId: mainProduct?.value?.id} );
    selectedItems.value = response?.items?.map((item) => ({
        id: item?.id,
        name: item?.name,
        image: item.featuredImage?.imageUrl,
    })) || [];
    // getProductsName.value = selectedItems.value;
    // selectedIds.value = response?.items?.map((item) => item.id) || [];
}
const handleDeleteRelatedProductsApi= async(deleteItem:any )=>{
    const response = await deleteApiItem(`product-related/${mainProduct?.value?.id}/`, deleteItem?.id, selectedItems.value);
    // selectedIds.value = response?.items?.map((item) => item.id) || [];
}

const searchForProducts = async (value: any) => {
    if(!mainProduct.value?.id) return;
    loading.value = true;   

    try {
        const response = await getApiItems(`product-suggestion/${mainProduct.value.id}`, { name: value?.length <= 0 ? 'ALL' : value } );
        if(response?.items) {
            // Assuming selectedItems is an array of existing items
            const existingIds = new Set(selectedItems.value.map(item => item?.id));
            // Add only new items (skip duplicates)
            const newItems = response?.items.filter(item => !existingIds.has(item.id));
            getProductsName.value = [...getProductsName.value, ...newItems];
        }
    } catch(err) {
        console.log(err);

    } finally {
        loading.value = false;
    }
}

const emit = defineEmits(['reset']);
const fakeReceiver = ref([]);
const onSubmit = () => {
    if (!form.value) return;
    if(!mainProduct.value?.id) return;
    loading.value = true;
    let payload = {
        productRelatedIds: selectedItems?.value?.map((item, index)=> ({
                productRelated: item?.id,
                sortOrder: index,
        }))
    }
    addEditApiItem(`product-related/${mainProduct.value.id}`, 0, '', payload, fakeReceiver.value)
    .then(res => {
        snackbar.value = true;
        snackbarData.value.message = res.message
        snackbarData.value.type = (res.success ? 'success' : 'error');
        if(res.success) {
            dialog.value = false;
        }
        emit('reset');

    }).finally(() => {
        loading.value = false;
    })
};

defineExpose({show});

onUnmounted(()=>{
    console.log("unmounting");
})

onMounted(()=>{
    console.log("-mounting");

})
</script>