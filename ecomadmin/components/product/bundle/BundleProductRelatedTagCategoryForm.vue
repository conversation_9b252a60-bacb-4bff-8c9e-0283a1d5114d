<template>
  <v-dialog v-model="dialog" scrollable eager :retain-focus="false" transition="dialog-bottom-transition"
    style="max-width: 85%;" persistent>

    <SharedUiParentCardSolid v-if="!permissionStore?.hasModulePermission(`${DynamicModuleEnum.CATEGORY_PRODUCT_RELATED}:${DynamicPermissionEnum.READ_MANY}`)" 
      class="mt-6" title="Product Tag details">
      <v-btn color="error" @click="dialog = false">Close</v-btn>
      <h3 class="text-h3 text-error border border-dotted pa-5 text-center">You don't have permission to see role list</h3>
    </SharedUiParentCardSolid>

    <v-sheet v-else elevation="10" class="px-4">
      <div class="d-flex justify-space-between align-center pa-4">
        <div class="d-flex ga-2">
          <h4 class="text-body-1 text-md-h5 font-weight-medium">Products ({{selectedItems.length}}) Tag for <span class="text-primary">{{mainProduct?.name}}</span></h4>
          <!-- <v-btn :loading="loading" height="35" density="compact" color="primary" @click="handleSyncOldProduct">Sync (Old Product)</v-btn> -->
        </div>
        <v-btn icon="mdi-close" size="x-small" color="error" @click="dialog = false"></v-btn>
      </div>
      <div>
        <draggable :itemKey="'tag_product'" v-model="selectedItems" tag="div" class="d-flex flex-wrap ga-2">
          <template #item="{ element }">
            <v-sheet class="pa-2 position-relative">
              <v-avatar @click="handleDeleteRelatedProductsApi(element)" class="position-absolute cursor-pointer" color="error" size="15" style="top: 1px; right: 1px;">
                  <v-icon icon="mdi-close" size="10"></v-icon>
              </v-avatar>
              <div class="cursor-move pa-1 border d-flex flex-column">
                  <v-img :src="element?.image" width="70" height="70" />
                  <h6 class="text-caption font-weight-bold text-truncate" style="width: 70px">{{ element.name }}</h6>
              </div>
            </v-sheet>
          </template>
        </draggable>
        <v-form v-model="form" @submit.prevent="onSubmit" v-slot="{ isValid }">
          <SharedUiParentCardSolid v-if="dialog" class="mt-1 mb-5" title="Select products">
            <v-autocomplete :items="getProductsName" item-title="name" item-value="id" label="Select Products" variant="outlined" hide-details 
              density="compact" chips closable-chips multiple v-model="selectedIds" @update:search="searchForProducts" class="pa-1">
              <template #item="{props, item}">
                <v-list-item v-bind="props" color="info" class="my-1">
                  <template #prepend>
                    <img :width="40" :src="item?.raw?.image"/>
                  </template>
                  <template #title><span class="ms-2">{{ item?.raw?.name }}</span></template>
                </v-list-item>
              </template>
            </v-autocomplete> 
          </SharedUiParentCardSolid>

          <v-card-actions>
            <v-btn :disabled="!isValid?.value" :title="isValid.value" :loading="loading" color="primary" density="compact" 
              variant="flat" class="px-6" type="submit" height="35">Save</v-btn>
          </v-card-actions>
        </v-form>
      </div>
    </v-sheet>

  </v-dialog>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { useRolePermissionsStore } from '~/stores/administration/permissions';
import {addEditApiItem, deleteApiItem, getApiItems} from "~/utils/helpers/functions";
import {DynamicModuleEnum, DynamicPermissionEnum} from "~/utils/roleManagement";

const emit = defineEmits(['reset']);


const permissionStore = useRolePermissionsStore();
const snackbar = useSnackbar();

const fakeReceiver = ref([]);
const dialog = ref(false);
const getProductsName = ref([]);
const form = ref(false);
const loading = ref(false);
const mainProduct = ref();
const selectedIds = ref(null);
const selectedItems = ref([]);

const showModal = async (item: any) => {
  // Invoke in parent component to open and get main parent data
  mainProduct.value = item;
  selectedIds.value = null;
  selectedItems.value = [];
  handleFetchRelatedProductsApi(); 
  dialog.value = true;
};
defineExpose({showModal});

const handleFetchRelatedProductsApi= async()=>{
  const response = await getApiItems(`category-product-related/${mainProduct?.value?.id}`, {categoryId: mainProduct?.value?.id, name: ''} );
  selectedItems.value = response?.items?.map((item) => ({
    id: item?.categoryProductRelated?.id,
    name: item?.categoryProductRelated?.name,
    image: item?.categoryProductRelated?.featuredImage?.imageUrl,
  })) || [];
}

const handleDeleteRelatedProductsApi= async(deleteItem:any )=>{
  if(!permissionStore?.hasModulePermission(`${DynamicModuleEnum.CATEGORY_PRODUCT_RELATED}:${DynamicPermissionEnum.SOFT_DELETE}`)){
    snackbar.add({
      type: "warning",
      text: "You don't have permission to delete product tags for category",
    })
    return;
  }

  await deleteApiItem(`category-product-related/${mainProduct?.value?.id}/`, deleteItem?.id, selectedItems.value);
  handleFetchRelatedProductsApi();
}

const searchForProducts = async (value: any) => {
  if(!mainProduct.value?.id) return; // if the main parent thing is not found 

  if(value?.length <= 0) return;
  loading.value = true;
  
  try {
    const response = await getApiItems(
      `category-product-related/suggestions/${mainProduct.value.id}`, 
      { name: value?.length <= 0 ? 'ALL' : value } 
    );
    if(response?.items && response?.items?.length > 0) {
      // Assuming selectedItems is an array of existing items
      const existingIds = new Set([
        ...selectedItems.value.map(item => item?.id),
        ...getProductsName.value.map(item => item?.id)
      ]);
      // Add only new items (skip duplicates)
      const newItems = response?.items?.filter(item => !existingIds.has(item.id));
      getProductsName.value = [...getProductsName.value, ...newItems];
    }
  } catch(error) {
    console.log(error);
  } finally {
    loading.value = false;
  }
}

const onSubmit = () => {
  if(!permissionStore?.hasModulePermission(`${DynamicModuleEnum.CATEGORY_PRODUCT_RELATED}:${DynamicPermissionEnum.CREATE}`)){
    snackbar.add({
      type: "warning",
      text: "You don't have permission to create product tags for category",
    })
    return;
  }
  
  if (!form.value) return;
  
  if(!mainProduct.value?.id) return;

  loading.value = true;
  let payload = {
    categoryProductRelatedIds: selectedItems?.value?.map(
      (item, index)=> ({
        categoryProductRelated: item?.id,
        sortOrder: index,
    }))
  }
  
  addEditApiItem(`category-product-related/${mainProduct.value.id}`, 0, '', payload, fakeReceiver.value)
  .then(res => {
    snackbar.add({
      type: res?.success ? 'success' : 'error',
      text: res?.message,
    })
    if(res?.success) {
      dialog.value = false;
    }
    emit('reset');
  }).finally(() => {
    loading.value = false;
  })
};

const handleSyncOldProduct = async ()=>{
  if(!permissionStore?.hasModulePermission(`${DynamicModuleEnum.CATEGORY_PRODUCT_RELATED}:${DynamicPermissionEnum.SYNC_SUGGESTION}`)){
    snackbar.add({
      type: "warning",
      text: "You don't have permission to sync product tags",
    })
    return;
  }

  loading.value = true;
  // api call for getting old product for this category
  const response = await getApiItems(`category-product-related/getCategoryWiseProducts/for/sync`, {categoryId: mainProduct?.value?.id});
  // api/category-product-related/getCategoryWiseProducts/for/sync?categoryId=1
  // const response = {data: []}

  const existingIds = new Set([
      ...selectedItems.value.map((item:any) => item?.id)
  ])

  let newItems = response?.items?.filter((item:any) => !existingIds?.has(item?.id));
  let modifiedNewItems = newItems?.map((item:any) => ({
    id: item?.id,
    name: item?.name,
    image: item?.image,
  })) || [];

  selectedItems.value = [...selectedItems.value, ...modifiedNewItems]
  loading.value = false;
}

watch(selectedIds, (newVal, oldVal)=>{
  if(newVal){
    let unselectedIds = oldVal?.filter(function(obj) { return newVal?.indexOf(obj) == -1; });
    let newItems = getProductsName.value.filter((el)=> selectedIds?.value?.includes(el?.id));
    selectedItems.value = selectedItems.value.filter(item => !unselectedIds?.includes(item.id));
    if (newItems?.length > 0) {
      // Merge existing and new items, ensuring uniqueness based on `id`
      selectedItems.value = Array.from(
        new Map([...selectedItems?.value, ...newItems].map((item) => [item?.id, item])).values()
      );
    }
  }
})
</script>