<template>
    <v-dialog v-model="dialog" scrollable eager :retain-focus="false" transition="dialog-bottom-transition" style="max-width: 85%;" persistent>


    <v-card  elevation="10">
        <v-card-title class="d-flex justify-space-between align-center pa-4">
          Bundle: {{ mainProduct?.name }} ({{ mainProduct?.sku }})
          <v-spacer></v-spacer>
          <v-btn icon size="small" @click="dialog = false">
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-card-title>
            <v-card-item class="py-4 px-6">
                <v-form v-model="form" @submit.prevent="onSubmit" v-slot="{ isValid }">
                    <SharedUiParentCard v-if="dialog" class="mt-1 mb-5" title="Select products">
                        <v-autocomplete
                        variant="outlined" hide-details
                        :items="getProductsName"
                        item-title="name"
                        item-value="id"
                        label="Select Products"
                        chips
                        closable-chips
                        multiple
                        v-model="selectedIds"
                        @update:search="searchForProducts"
                        >
                            <template v-slot:item="{ props, item }">
                            <v-list-item
                                v-bind="props"
                                :prepend-avatar="item.raw.image"
                                :subtitle="item.raw.group"
                                :title="item.raw.name"
                            ></v-list-item>
                            </template>
                        </v-autocomplete> 
                    </SharedUiParentCard>

                    <v-card-actions>
                        <v-btn :disabled="!isValid?.value" :title="isValid.value" :loading="loading" color="primary" variant="tonal" class="px-6" type="submit"> Save</v-btn>
                    </v-card-actions>
                </v-form>
          </v-card-item>
        </v-card>
    </v-dialog>

    <v-snackbar v-model="snackbar" :color="snackbarData.type">
    {{ snackbarData.message }}
  </v-snackbar>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import {addEditApiItem, getApiItems} from "~/utils/helpers/functions";

const dialog = ref(false);
const snackbar = ref(false)
const snackbarData = ref({
  message: '',
  type: 'success'
})


const getProductsName = ref([]);

const form = ref(false);
const loading = ref(false);

const mainProduct = ref();


const selectedIds = ref([]);
const selectedItems = ref([]);

const show = async (item: any) => {
    mainProduct.value = item;

    console.log(selectedIds, item.suggestions)

    selectedItems.value = item.suggestions?.map((item) => ({
        id: item.suggestedProduct.id,
        name: item.suggestedProduct.name,
        image: item.suggestedProduct.featuredImage?.imageUrl,
    })) || [];

    getProductsName.value = selectedItems.value;
    selectedIds.value = item.suggestions?.map((item) => item.suggestedProduct.id) || [];

    searchForProducts('ALL');
    dialog.value = true;
};


const searchForProducts = async (value: any) => {
    if(!mainProduct.value?.id) return;
    loading.value = true;

    try {
        const response = await getApiItems(`product-suggestion/${mainProduct.value.id}`, { name: value?.length <= 0 ? 'ALL' : value } );
        if(response.items) {
            // Assuming selectedItems is an array of existing items
            const existingIds = new Set(selectedItems.value.map(item => item.id));

            // Add only new items (skip duplicates)
            const newItems = response.items.filter(item => !existingIds.has(item.id));

            getProductsName.value = [...getProductsName.value, ...newItems];
        }
    } catch(err) {
        console.log(err);

    } finally {
        loading.value = false;
    }
}

const emit = defineEmits(['reset']);
const fakeReceiver = ref([]);
const onSubmit = () => {
    if (!form.value) return;
    if(!mainProduct.value?.id) return;
    loading.value = true;

    addEditApiItem(`product-suggestion`, mainProduct.value.id, '', {
        suggestedProductIds: selectedIds.value,
    }, fakeReceiver.value)
    .then(res => {
        snackbar.value = true;
        snackbarData.value.message = res.message
        snackbarData.value.type = (res.success ? 'success' : 'error');
        if(res.success) {
            dialog.value = false;
        }
        emit('reset');

    }).finally(() => {
        loading.value = false;
    })
};

defineExpose({show});

</script>