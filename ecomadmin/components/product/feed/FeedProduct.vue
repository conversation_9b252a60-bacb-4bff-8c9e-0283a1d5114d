<template>
  <v-dialog v-model="dialog" transition="dialog-bottom-transition">
    <v-card  elevation="10" title="Feed Products">
      <v-card-item class="py-4 px-6">
    <v-row>


      <v-col cols="12" md="6">
        <!-- Bulk action -->
        <v-menu v-if="!!selected.length">
          <template v-slot:activator="{ props }">
            <v-btn
              color="primary"
              v-bind="props"
            >
              Bulk action ({{ selected.length }})
            </v-btn>
          </template>
          <v-list>
            <v-list-item
              v-for="(item, index) in bulkActions"
              :key="index"
              :value="index"
            >
              <v-list-item-title @click="bunkActionHandler(item.action)">{{ item.title }}</v-list-item-title>
            </v-list-item>
          </v-list>
        </v-menu>
        <!-- Bulk action end -->
      </v-col>

      <v-col cols="12" md="6">
        <div class="d-flex">
          <v-btn class="mr-5" @click="showCollection = true">Load from collection</v-btn>
          <div style="width: 100%;">
            <v-text-field append-inner-icon="mdi-magnify" density="compact"
              label="Search ..." variant="outlined" hide-details single-line
              @onChange:append-inner="onClick"
              v-model="options.search" clearable @click:clear="clearSearch"></v-text-field>
          </div>
        </div>

        </v-col>

      <v-col cols="12" md="12">
        <v-data-table-server
            v-model:page="options.page"
            v-model:items-per-page="options.itemsPerPage"
            :headers="headers"
            :items="products"
            :items-length="store.pagination.itemCount"
            :search="options.search"
            :loading="loading"
            show-select
            v-model="selected"
            item-value="id" @update:options="fetchProductList">

          <template v-slot:item.image="{ item }">
          <div style="padding:5px;" v-if="!!item?.image">
            <v-img :src="item.image" width="50"/>
          </div>
        </template>

        <template v-slot:item.brand="{ item }">
            <v-chip variant="tonal" label size="x-small">
              {{ item?.brand }}
            </v-chip>
          </template>
  
          <template v-slot:item.name="{ item }">
            <p style="width: 250px;text-wrap: wrap">{{ item?.name }}</p>
          </template>          
  
          <template v-slot:item.action="{ item }">
            <div class="text-end">
  
              <v-menu>
                <template v-slot:activator="{ props }">
                  <v-btn class="me-2" variant="tonal" size="small" icon="mdi-dots-vertical" v-bind="props"></v-btn>
                </template>
  
                <v-list>
                  <v-list-item>
                    <v-btn
                        icon
                        color="error"
                        variant="tonal"
                        size="x-small"
                        :to="`/products/product/${item.id}`"
                        title="Edit"
                    >
                      <PencilIcon size="20"/>
                    </v-btn>
                  </v-list-item>
                  <v-list-item>
                    <v-btn icon color="error" variant="tonal" size="x-small" class="ms-1"
                           @click="confirmDialog = true; itemToDelete = item.id">
                      <TrashIcon size="20"/>
                    </v-btn>
                  </v-list-item>
  
                </v-list>
              </v-menu>
            </div>
          </template>
        </v-data-table-server>
        
      </v-col>
    </v-row>

  </v-card-item>
</v-card>
  </v-dialog>
    <ConformationModal v-model:dialog="confirmDialog" @confirm-delete="handleDeleteConfirmation" /> 
    <ConformationModal v-model:dialog="confirmBulkDialog" @confirm-delete="handleBulkDeleteConfirmation" /> 


<v-dialog v-model="showCollection" scrollable eager :retain-focus="false" transition="dialog-bottom-transition" style="max-width: 50%;" persistent>
<v-card  elevation="10">
  <v-card-title class="d-flex justify-space-between align-center pa-4">
      Collection
      <v-spacer></v-spacer>
      <v-btn icon size="small" @click="showCollection = false">
        <v-icon>mdi-close</v-icon>
      </v-btn>
    </v-card-title>
        <v-card-item class="py-4 px-6">
          <v-form v-model="collectionForm" @submit.prevent="saveCollection" v-slot="{ isValid }">
              <v-row>
                  <FormElementsCommonFieldContainer label="Collection" :required="true" class="mb-3 v-col-12">
                      <v-select
                      :items="collections"
                      v-model="selectedCollection"
                      label="Select Collection"
                      item-title="title"
                      item-value="id"
                      hide-details
                      multiple
                      :rules="[val => SELECT_REQUIRED_RULE_MULTIPLE(val)]"
                      :required="true"
                      ></v-select>
                  </FormElementsCommonFieldContainer>
              </v-row>
              <v-card-actions>
                  <v-spacer></v-spacer>
                  <v-btn :disabled="!isValid?.value" :title="isValid.value" color="primary" variant="tonal" class="px-6" type="submit"> Save</v-btn>
              </v-card-actions>
          </v-form>
      </v-card-item>
    </v-card>
</v-dialog>


  </template>
  
  <script setup lang="ts">
  import {ref, watch} from "vue";
  import {deleteItem, sendDeleteRequest, getApiItems, addEditApiItem} from "~/utils/helpers/functions";
  import {EyeIcon, PencilIcon, TrashIcon} from "vue-tabler-icons";
  import ConformationModal from "~/components/modals/ConformationModal.vue";
  import { useProductFeedItemStore } from "~/stores/products/product-feed-items";
  import {SELECT_REQUIRED_RULE_MULTIPLE} from "~/utils/formRules";
  
  const snackbar = useSnackbar();
  const store = useProductFeedItemStore();
  
  const dialog = ref(false);

const collectionForm = ref(false);
const showCollection = ref<boolean>(false);
const selectedCollection = ref<number[]>([]);
const collections = ref([]);

const selectedFeedId = ref();
const show = (feedItem) => {
  selectedFeedId.value = feedItem.id;
  dialog.value = true;
  getApiItems(`/product-collection/country/${feedItem.countryId}`, null, null)
  .then(r => {
    collections.value = r?.items || [];
  }).catch(e => {
    collections.value = [];
  })

};

const selected = ref<number[]>([]);
const bulkActions = [
        { title: 'Remove from feed', action: 'REMOVE_FROM_FEED' },
      ];

const resetSelection = () => {
  selected.value = [];
};


const saveCollection = () => {
  if (!collectionForm.value || !selectedCollection.value) return;

  addEditApiItem(`/product-feed/${selectedFeedId.value}/collections`, null, null, {
    collectionIds: selectedCollection.value,
  }, [])
  .then(res => {
    if(res?.success) {

      showCollection.value = false;
      selectedCollection.value = [];
      resetSelection();
      fetchProductList();
      emit('reset');

        

      snackbar.add({
            type: 'success',
            text: res.message,
      });
    } else {
      snackbar.add({
            type: 'error',
            text: 'Failed to save product.',
      });
    }
  }).catch(e => {
    snackbar.add({
      type: 'error',
      text: 'Failed to save product.',
    });
  })

}

const confirmDialog = ref(false)
const confirmBulkDialog = ref(false)

const bunkActionHandler = (action: string) => {
  if(!selected.value?.length) return;


if(action === 'REMOVE_FROM_FEED' )
{
  confirmBulkDialog.value = true;
}
};


const handleBulkDeleteConfirmation = () => {
  if(!selected.value?.length) return;
  removeProduct(selected.value);
};


  const options = ref({
    page: 1,
    itemsPerPage: 20,
    sortBy: ['id'],
    sortDesc: [false],
    search: '',
  });
  const loading = ref(false);
  const products = ref([]);

  
  const headers = ref([
    {title: 'Image', align: 'start', sortable: false, key: 'image'},
    {title: 'Name', align: 'start', sortable: false, key: 'name'},
    {title: 'Brand', align: 'start', sortable: false, key: 'brand'},
    {title: 'Action', key: 'action', align: 'center', sortable: false},
  ])

  const emit = defineEmits(['reset']);
  
  const search = ref('')
  const name = ref('')
  const sku = ref('')
  
  
  const onClick = () => {
    if (!search.value) return;
    fetchProductList();
  };
  
  const clearSearch = () => {
    search.value = '';
    fetchProductList();
  };
  
  const fetchProductList = async () => {

    if(!selectedFeedId.value) return;
    const {page, itemsPerPage, sortBy, sortDesc, search} = options.value;
    const order = sortDesc[0] ? 'DESC' : 'ASC';
    const sortKey = sortBy[0] || 'id';
    loading.value = true;
    await store.getProducts(selectedFeedId.value, order, page, itemsPerPage, sortKey, search);
    products.value = store.products;
    loading.value = false;
  };
  
  const removeProduct = (items: number[]) => {
    if(!selectedFeedId.value || !items.length) return;

    sendDeleteRequest(`product-feed/${selectedFeedId.value}/products`, {
      productIds: items,
    })
    .then(res => {
        if (res) {
            snackbar.add({
            type: 'success',
            text: 'Product deleted successfully!',
            });
  
        } else {
            snackbar.add({
            type: 'error',
            text: 'Failed to delete product.',
            });
        }
        resetSelection();
        fetchProductList();
        emit('reset');
    }).finally(() => {
        confirmDialog.value = false;
        confirmBulkDialog.value = false;
    });
  }
  
  const itemToDelete = ref<number | null>(null);
  const handleDeleteConfirmation = async () => {
    if (itemToDelete.value !== null) {
      const productId = itemToDelete.value;
      removeProduct([productId]);
    }
  };
  
  const filterProducts = (filterKey, value, listKey) => {
    const normalizedValue = value.toLowerCase();
    let filteredData = [];
  
    if (normalizedValue.length > 0) {
      filteredData = store.products.filter(item => {
        const itemValue = filterKey(item).toLowerCase();
        return itemValue.includes(normalizedValue);
      });
    } else {
      filteredData = store.products;
    }
  
    products.value = filteredData;
  };
  
  watch(name, (val) => {
    filterProducts(item => item.name, val, 'products');
  });
  watch(sku, (val) => {
    filterProducts(item => item.sku, val, 'products');
  });


  defineExpose({show});
  
  </script>
  