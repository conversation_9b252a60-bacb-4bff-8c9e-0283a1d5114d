<template>
    <v-dialog v-model="dialog" transition="dialog-bottom-transition" style="max-width: 65%;">
        <v-card  elevation="10" title="Feed">
            <v-card-item class="py-4 px-6">
                <v-form v-model="form" @submit.prevent="onSubmit" v-slot="{ isValid, validate }">
                    <FormElementsCommonFieldContainer label="Country" :required="true" class="mb-3">
                        <v-select
                        :items="getCountries"
                        v-model="countryId"
                        label="Select Country"
                        item-title="name"
                        item-value="id"
                        hide-details
                        ></v-select>
                    </FormElementsCommonFieldContainer>

                    <FormElementsCommonFieldContainer label="Name" :required="true" class="mb-3">
                        <v-text-field v-model="feedName" :rules="[val => REQUIRED_RULE(val)]" placeholder="Enter Name"></v-text-field>
                    </FormElementsCommonFieldContainer>

                    <FormElementsCommonFieldContainer label="Delimiter" :required="true" class="mb-5">
                        <v-select
                        :items="[',', ';', '|']"
                        v-model="selectedDelimiter"
                        label="Select Delimiter"
                        item-title="slug"
                        item-value="id"
                        hide-details
                        ></v-select>
                    </FormElementsCommonFieldContainer>

                    <v-divider :thickness="2" color="success" inset></v-divider>
                    
                    <v-row>
                        <v-col cols="6">
                            <FormElementsCommonFieldContainer label="Username" class="mb-3">
                                <v-text-field type="text" :name="`feed_username_${selectedFeedId}`" v-model="feedUsername" @update:model-value="validate" :rules="[val => DEPENDENT_REQUIRED_OPTIONAL(val, feedPassword, 'Fill-up both username and password')]" placeholder="Enter user name"></v-text-field>
                            </FormElementsCommonFieldContainer>
                        </v-col>
                        <v-col cols="6">
                            <FormElementsCommonFieldContainer label="Password" class="mb-3">
                                <v-text-field :name="`feed_password_${selectedFeedId}`" v-model="feedPassword" @update:model-value="validate" :type="passwordVisible ? 'text' : 'password'" :append-inner-icon="passwordVisible ? EyeOffIcon : EyeIcon" @click:append-inner="passwordVisible = !passwordVisible" :rules="[val => DEPENDENT_REQUIRED_OPTIONAL(val, feedUsername, 'Fill-up both username and password')]" placeholder="Enter password"></v-text-field>
                            </FormElementsCommonFieldContainer>
                        </v-col>
                    </v-row>

                    <v-card-actions>
                        <v-spacer></v-spacer>
                        <v-btn :disabled="!isValid?.value" :title="isValid.value" :loading="loading" color="primary" variant="tonal" class="px-6" type="submit"> Save</v-btn>
                    </v-card-actions>
                </v-form>
          </v-card-item>
        </v-card>
    </v-dialog>
    <v-snackbar v-model="snackbar" :color="snackbarData.type">
    {{ snackbarData.message }}
  </v-snackbar>
</template>
<script setup lang="ts">
import {REQUIRED_RULE, DEPENDENT_REQUIRED_OPTIONAL} from "~/utils/formRules";
import {useCountryStore} from "~/stores/others/country";
import {EyeOffIcon, EyeIcon} from "vue-tabler-icons";
import { ref } from 'vue';
import {addEditApiItem} from "~/utils/helpers/functions";

const feedName = ref('');
const countryId = ref();
const selectedFeedId = ref();
const selectedDelimiter = ref(';');

const feedUsername = ref('');
const feedPassword = ref('');


const passwordVisible = ref(false);
  
// Types for Country
interface Country {
    id: number;
    label: string;
    name: string;
}

const rules = [
  (value: any) => {
    if (value) return true;
    return 'You must enter a name'
  }
];

const countryStore = useCountryStore();
const snackbar = ref(false)
const snackbarData = ref({
  message: '',
  type: 'success'
})

// Reactive state
const dialog = ref(false);
const form = ref(false);
const loading = ref(false);

const getCountries = computed(() => countryStore.countries);

const show = async (feedItem) => {
    selectedFeedId.value = feedItem.id;
    countryId.value = feedItem.countryId;
    feedName.value = feedItem.name;
    selectedDelimiter.value = feedItem.delimiter;
    feedUsername.value = feedItem.username,
    feedPassword.value = feedItem.password,
    await countryStore.getAllCountries();
    dialog.value = true;
};

const emit = defineEmits(['reset']);

const fakeReceiver = ref([]);
const onSubmit = () => {
    if (!form.value || !selectedFeedId.value) return;

    loading.value = true
    addEditApiItem(`product-feed`, selectedFeedId.value, '', {
        name: feedName.value,
        countryId: countryId.value,
        id: selectedFeedId.value,
        delimiter: selectedDelimiter.value,
        username: feedUsername.value,
        password: feedPassword.value,
    }, fakeReceiver.value)
    .then(res => {
        snackbar.value = true;
        snackbarData.value.message = res.message
        snackbarData.value.type = (res.success ? 'success' : 'error');
        if(res.success) {
            dialog.value = false;
        }
        emit('reset');

    }).finally(() => {
        loading.value = false;
    })
}

  defineExpose({show})
  </script>
  

