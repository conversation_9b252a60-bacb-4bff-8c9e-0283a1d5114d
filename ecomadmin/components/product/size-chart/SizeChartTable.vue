<template>
  <v-sheet fluid class="py-2">
    <v-form v-model="form" @submit.prevent="onSubmit" v-slot="{ isValid }">
      <v-row>
        <v-col cols="12" md="4">
          <v-text-field label="Title" v-model="chartTitle" :rules="[val => REQUIRED_RULE(val)]" density="compact"/>
          <!-- <v-textarea label="*Note" v-model="chartNote" rows="1" density="compact"/> -->
          <!-- <v-text-field label="*Note" v-model="chartNote" density="compact"></v-text-field> -->
           <v-select
              label="*Note"
              v-model="chartNote"
              :items="translateNote"
              density="compact"
              :clearable="true"
              placeholder="Select a note"
            ></v-select>
        </v-col>

        <v-col cols="12" md="8">
          <!-- <div class="position-relative">
            <FormElementsImageViewerSingle
              :image-id="imageIdP"
              image-model="SIZE_CHART"
              :image-rules="PRODUCT_SIZE_CHART_RULES"
              :max-size="200000"
              :required-height="1300"
              :required-width="1700"
              :multiple="false"
              @selected-image-id="selectedSizeChart"
            />
            <v-avatar v-if="imageIdP" color="error" size="18"  @click="resetSizeChartImage" class="cursor-pointer position-absolute" style="left:18%; top:0">
              <v-icon size="15" icon="mdi-close">
              </v-icon>
            </v-avatar>
          </div> -->
          <h5 class="text-h5">Product Chart Remarks</h5>
          <FormElementsRichTextEditor v-model="chartRemarks"/>
        </v-col>
      </v-row>

      <h5 class="text-h5">Chart Table Images</h5>
      <v-row class="mt-3">
        <v-col cols="3" v-for="(obj, index) in chartImages" :key="'table_image_' + index">
          <div class="d-flex flex-column ga-1">
            <v-select v-model="chartImages[index].chartType" :items="sizeChartTypeList" item-title="title" item-value="slug"  type="text" label="table type" density="compact"></v-select>
            <div class="position-relative">
              <FormElementsImageViewerSingle
                :image-id="chartImages[index].imageId"
                image-model="SIZE_CHART"
                :image-rules="PRODUCT_SIZE_CHART_RULES"
                :max-size="200000"
                :required-height="1300"
                :required-width="1700"
                @selected-image-id="selectedChartsImage"
                :componentExecutedObject="{id: index, from: 'chartTypeList'}"
              />
              <v-avatar v-if="chartImages[index].imageId" color="error" size="18"  @click="deleteChartsImage(index)" class="cursor-pointer position-absolute" style="right: -10px; top:-10px">
                <v-icon size="15" icon="mdi-close">
                </v-icon>
              </v-avatar>
            </div>
          </div>
        </v-col>
        <v-col cols="1" class="d-flex align-center">
          <v-avatar @click="handleNewChartsImage" color="primary" size="22" class="cursor-pointer d-flex align-center">
            <v-icon icon="mdi-plus" size="18"></v-icon>
          </v-avatar>
        </v-col>
      </v-row>
      

      <div v-for="(item, i) in charts" :key="i" class="pa-1">
        <MeasurementItem :payload="item" :sizeChartTypeList="sizeChartTypeList" @delete="deleteChartIndex(i)" @changed="(val) => updateChart(val, i)"/>
      </div>

      <v-row>
        <v-col cols="6">
          <v-btn
            color="primary"
            class="mt-3"
            @click="addNewChart"
          >
            <v-icon left>mdi-plus</v-icon>
            Add New Chart
          </v-btn>
        </v-col>
        <v-col cols="6">
          <v-btn  color="success" :loading="loading" block class="mt-3" :disabled="!isValid?.value" type="submit">
            Save Size Chart
          </v-btn>
        </v-col>
      </v-row>
    </v-form>

  </v-sheet>

</template>

<script setup lang="ts">
import MeasurementItem from "~/components/product/size-chart/MeasurementItem.vue";
import {addEditApiItem} from "~/utils/helpers/functions";
import {REQUIRED_RULE} from "~/utils/formRules";
import {PRODUCT_SIZE_CHART_RULES} from "~/utils/imageRules";
import {ChartImage} from "~/types/products";
import {translateNote} from "~/utils/sizeGuide";

const props = defineProps({
  chartId: {
    type: Number,
    required: false, 
  },
  countryId: {
    type: Number,
    required: false, 
  },
  payload: {
    type: Object,
    required: false,
  },
  imageId: {
    type: Number,
    required: false,
  },
  basePath: {
    type: String,
    required: true,
  }
})

const emit = defineEmits(["saved"]);
const snackbar = useSnackbar();
const form = ref(false);
const loading = ref(false);
const imageIdP = ref(props.imageId);

const chartTitle = ref(props?.payload?.title || "");
const chartNote = ref(props?.payload?.note?.noteData || "");
const chartRemarks = ref(props?.payload?.note?.remarks || "");
const charts = ref(props?.payload?.payload || []);
const fakeReceiver = ref([]);
const sizeChartTypeList = ref([
  {slug: 'product', title: 'Product'},
  {slug: 'body', title: 'Body'}
])
const chartImages = ref<ChartImage[]>(props?.payload?.chartImages || []);
const defaultChartsImage = reactive({
  id: null,
  chartType: "",
  imageId: null,
});

const addNewChart = () => {
  charts.value = [...charts.value, {label: "", data: [], sizes: [], order: charts?.value?.length || 0 + 1, type: ""}];
}

const deleteChartIndex = (index: number) => {
  charts.value.splice(index, 1);
}

const updateChart = (value: any, index: number) => {
  charts.value.splice(index, 1, value);
}

const selectedSizeChart = (val: any) => {
  imageIdP.value = val;
}

const selectedChartsImage = (val: any, executedObject: any)=>{
  if(executedObject){
    chartImages.value[executedObject?.id] = {...chartImages?.value[executedObject?.id], imageId: val};
  }
}

const resetSizeChartImage = ()=>{
  imageIdP.value = undefined;
}

const handleNewChartsImage = ()=>{
  chartImages.value.push({
    ...defaultChartsImage,
    id: chartImages?.value?.length + 1,
  })
}
const deleteChartsImage = (listIndex: number)=>{
  chartImages.value = chartImages?.value?.filter((item, index)=> listIndex!=index);
}

const filterChartImagesNull = ()=>{
  return chartImages?.value?.filter((item)=> item?.id !=null && item?.chartType != "" && item?.chartType != null);
}

const onSubmit = () => {

  if (!form.value) return;
  loading.value = true
  
  // Prepare sizeChart data for the database
  // Sorting table charts by order
  charts.value.sort((a: any, b: any) => a.order - b.order)

  const sizeChart = {
    title: chartTitle.value,
    note: {
      noteData: chartNote.value,
      remarks: chartRemarks.value,
    },
    payload: charts.value,
    imageId: imageIdP.value || null,
    countryId: props.countryId,
    chartImages: filterChartImagesNull(),
  }

  addEditApiItem(props.basePath, props.chartId || null, '', sizeChart, fakeReceiver.value)
    .then(res => {
      snackbar.add({
        type: res?.success ? 'success' : 'error',
        text: res?.message
      })
      emit('saved');
    }).finally(() => {
      loading.value = false;
    })
}
</script>
