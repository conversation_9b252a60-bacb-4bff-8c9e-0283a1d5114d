<template>
    <v-dialog v-model="quickEditDialog" max-width="500px">
    <v-card>
        <v-card-title class="d-flex justify-space-between align-center pa-4">
            Clone size chart: ({{ chartName }})
            <v-spacer></v-spacer>
            <v-btn icon size="small" @click="quickEditDialog = false">
                <v-icon>mdi-close</v-icon>
            </v-btn>
            </v-card-title>
        <v-form v-model="form" @submit.prevent="onSubmit" v-slot="{ isValid }">
            <v-card-text>
                <v-text-field label="Title" v-model="chartTitle" :rules="[val => REQUIRED_RULE(val)]"/>
            </v-card-text>
            <v-card-actions>
                <v-spacer></v-spacer>
                <v-btn :disabled="!isValid?.value" :title="isValid.value" :loading="loading" color="primary" variant="tonal" class="px-6" type="submit"> Save</v-btn>
            </v-card-actions>
        </v-form>
    </v-card>
  </v-dialog>
  <v-snackbar v-model="snackbar" :color="snackbarData.type">
    {{ snackbarData.message }}
  </v-snackbar>
</template>

<script lang="ts" setup>
import {REQUIRED_RULE} from "~/utils/formRules";
import {addEditApiItem} from "~/utils/helpers/functions";

const quickEditDialog = ref(false);

const form = ref(false);
const loading = ref(false);
const snackbar = ref(false)
const snackbarData = ref({
  message: '',
  type: 'success'
});

const chartId = ref<number>();
const chartTitle = ref<string>("");

const chartName = ref<string>("");

const show = (item) => {
    chartId.value = item.id;
    chartTitle.value = "";
    chartName.value = item.name;
    quickEditDialog.value = true;
};

const emit = defineEmits(['saved']);
const fakeReceiver = ref([]);
const onSubmit = () => {
    if (!form.value) return;

    loading.value = true
    addEditApiItem(`size-chart/clone`, chartId.value || null, '', {
        title: chartTitle.value,
    }, fakeReceiver.value)
    .then(res => {
        snackbar.value = true;
        snackbarData.value.message = res.message
        snackbarData.value.type = (res.success ? 'success' : 'error');
        if(res?.success) {
            quickEditDialog.value = false;
        }
        emit('saved');

    }).finally(() => {
        loading.value = false;
    })
};


defineExpose({show});
</script>