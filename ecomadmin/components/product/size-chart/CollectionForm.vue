<template>
    <v-dialog v-model="dialog" scrollable eager :retain-focus="false" transition="dialog-bottom-transition" style="max-width: 95%;" persistent>
        <v-card  elevation="10">
            <v-card-title class="d-flex justify-space-between align-center pa-4">
            Size Chart
            <v-spacer></v-spacer>
            <v-btn icon size="small" @click="dialog = false">
                <v-icon>mdi-close</v-icon>
            </v-btn>
            </v-card-title>
            <v-card-item class="py-4 px-6">
                <SizeChartTable v-if="dialog" base-path="size-chart" :chart-id="collectionId" :payload="fromPayload"
                :image-id="imageId"
                @saved="resetHandler"/>
          </v-card-item>
        </v-card>
    </v-dialog>

    <v-snackbar v-model="snackbar" :color="snackbarData.type">
    {{ snackbarData.message }}
  </v-snackbar>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import SizeChartTable from "~/components/product/size-chart/SizeChartTable.vue";
import type { CreateSizeChartDto } from "~/types/products";

const defaultValue: CreateSizeChartDto = {
    title: '',
    note: '',
    payload: [],
    chartImages: [],
};

const fromPayload = ref<CreateSizeChartDto>({...defaultValue});
const imageId = ref<number>();

const dialog = ref(false);
const snackbar = ref(false)
const snackbarData = ref({
  message: '',
  type: 'success'
})

const collectionId = ref<number>();

const show = async () => {
    collectionId.value = undefined;
    imageId.value = undefined;
    fromPayload.value = {...defaultValue};
    dialog.value = true;
};


const edit = async (id: number, payload: CreateSizeChartDto, _imageId ?: number) => {
    collectionId.value = id;
    fromPayload.value = {...defaultValue, ...payload};
    imageId.value = _imageId;
    dialog.value = true;
};

const resetHandler = () => {
    dialog.value = false;
    emit('reset');
}

const emit = defineEmits(['reset']);

defineExpose({show, edit});
</script>