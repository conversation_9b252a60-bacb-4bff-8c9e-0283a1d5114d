<template>
    <v-dialog v-model="dialog" transition="dialog-bottom-transition" max-width="75%">
        <v-card elevation="10">

            <v-card-title class="d-flex justify-space-between align-center pa-4">
            {{`Localize: ${attributeName ? ' (' +attributeName+ ')' : ''}`}}
            <v-spacer></v-spacer>
            <v-btn icon size="small" @click="dialog = false">
                <v-icon>mdi-close</v-icon>
            </v-btn>
            </v-card-title>
            <v-card-item class="py-4 px-4">
                <v-card>
                    <v-tabs bg-color="teal-darken-3" slider-color="teal-lighten-3" show-arrows v-model="activeTab">
                        <v-tab
                            v-for="c in countryStore?.countries"
                            :key="c.id"
                            :text="c.name"
                            :value="c.id"
                        ></v-tab>
                    </v-tabs>
                </v-card>
                <v-container>
                    <v-row no-gutters>
                        <v-col v-if="dialog && countries.length">
                            <template v-for="country in countryStore?.countries" :key="country.id">
                              <SizeChartTable v-if="country.id == activeTab" :chart-id="activeChartId" :country-id="country.id"  base-path="size-chart/chart-values"
                              :payload="{
                                title: store?.values?.get(activeTab)?.name || activeChart?.name ||'',
                                payload: store?.values?.get(activeTab)?.values || activeChart?.values || [],
                                note: store?.values?.get(activeTab)?.note || activeChart?.note || '',
                                chartImages: store?.values?.get(activeTab)?.chartImages || [], 
                              }"
                              :image-id="store?.values?.get(activeTab)?.imageId || activeChart?.imageId"
                              @saved="() => savedHandler()"
                              />
                            </template>
                        </v-col>
                    </v-row>
                </v-container>
          </v-card-item>
        </v-card>
    </v-dialog>
    <v-snackbar v-model="snackbar" :color="snackbarData.type">
    {{ snackbarData.message }}
  </v-snackbar>
</template>
<script setup lang="ts">
import { ref } from 'vue';
import { getLocaLizeLanguages } from "~/utils/languages";
import SizeChartTable from "~/components/product/size-chart/SizeChartTable.vue";
import type { PayloadItem } from "~/types/products";
import { useProductChartStore } from "~/stores/products/product-chart";
import { useCountryStore } from '~/stores/others/country';

// Types for Country
interface Country {
  id: number;
  label: string;
  name: string;
  description: string;
}
interface AttributeLocale {
  name: string; 
  description: string|null;
}

const rules = [
  (value: any) => {
    if (value) return true;
    return 'You must enter a name'
  }
];

const countryStore = useCountryStore();
const store = useProductChartStore();
const snackbar = ref(false)
const snackbarData = ref({
  message: '',
  type: 'success'
})

// Reactive state
const dialog = ref(false);
const activeTab = ref(2); // Controls the active tab
const loading = ref(false);
const countries = ref<Country[]>([]);

const attributeName = ref<string>();
const activeChartId = ref<number|null>(null);
const activeChart = ref(null);

const show = async (chartItem: any) => {
  await countryStore.getAllCountries();

  if(!chartItem?.id) return;
  activeChart.value = chartItem;
  activeChartId.value = chartItem.id;

  activeTab.value = 2;
  let countryData:Country[] = [];
  const list = [...getLocaLizeLanguages];
  for(const data of list)
  {
    const countryId = data.id as number;
    countryData.push({
      id: countryId,
      label: data.name,
      name: '',
      description: '',
    });
  }

  countries.value = [...countryData];


  loading.value = true;
  await store.getChartValue(activeChartId.value);
  loading.value = false;

  dialog.value = true;
};

const savedHandler = async () => {
  if(activeChartId.value) {
    await store.getChartValue(activeChartId.value);
  }
}

defineExpose({show})
</script>


