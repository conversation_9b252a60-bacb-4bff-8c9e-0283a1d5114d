<template>
  <v-sheet fluid>
    <v-card elevation="10" class="mt-5">
      <v-card-text>
        <v-row class="">
          <v-col cols="6">
            <v-text-field label="Measurement Label" v-model="chartTitle" :rules="[val => REQUIRED_RULE(val)]" density="compact"/>
          </v-col>
          <v-col cols="2">
            <v-text-field v-model="chartOrder" type="number" label="chart order" density="compact" :min="1"></v-text-field>
          </v-col>
          <v-col cols="2">
            <v-select v-model="chartType" :items="props?.sizeChartTypeList" item-title="title" item-value="slug"  type="text" label="table type" density="compact"></v-select>
          </v-col>
          <v-col cols="2">
            <v-btn
              color="error"
              icon
              @click="emit('delete')"
              density="compact"
            >
              <v-icon size="15">mdi-delete</v-icon>
            </v-btn>
          </v-col>
        </v-row>
      
        <!-- Size Management -->
        <h6 class="text-h6">Manage Sizes</h6>
        <v-row class="mt-2">
          <v-col cols="12">
            <v-combobox
              v-model="sizes"
              :items="sizeItems"
              multiple
              chips
              label="Available Sizes"
              hint="Press enter to add new sizes"
              persistent-hint
              density="compact"
              variant="outlined"
              closable-chips
            ></v-combobox>
          </v-col>
        </v-row>


        <!-- Measurements Management -->
        <h6 class="text-h6">Manage Measurements</h6>
        <v-row v-for="(_measurement, mIndex) in measurements" :key="mIndex" class="mt-2">
          <v-col cols="10">
            <v-text-field
              v-model="_measurement.name"
              label="Measurement Name"
              :rules="[val => REQUIRED_RULE(val)]"
              hide-details
              density="compact"
            ></v-text-field>
          </v-col>
          <v-col cols="2">
            <v-btn
              color="error"
              icon
              @click="removeMeasurement(mIndex)"
              density="compact"
            >
              <v-icon size="15">mdi-delete</v-icon>
            </v-btn>
          </v-col>

          <v-col cols="12">
            <p class="mb-2">*Note: drag and drop for serialing sizes</p>
              <draggable v-model="sizes" :element="'div'" :itemKey="'serial_size'" :options="dragOptions" class="d-flex flex-wrap ga-2">
                <template #item="{ element:size }">

                  <v-sheet class="pa-2 border-md border-primary cursor-grab" min-width="300">
                    <div class="text-caption">{{ size }}</div>
                    <v-row no-gutters>
                      <v-col cols="6" class="pr-1">
                        <v-text-field
                          v-model="_measurement.values[size].cm"
                          label="cm"
                          dense
                          outlined
                          hide-details
                          :rules="[val => REQUIRED_RULE(val)]"
                          density="compact"
                        ></v-text-field>
                      </v-col>
                      <v-col cols="6" class="pl-1">
                        <v-text-field
                          v-model="_measurement.values[size].in"
                          label="in"
                          dense
                          outlined
                          hide-details
                          :rules="[val => REQUIRED_RULE(val)]"
                          density="compact"
                        ></v-text-field>
                      </v-col>
                    </v-row>
                  </v-sheet>
                </template>
              </draggable>
          </v-col>
        </v-row>


        <v-row>
          <v-col cols="6">
            <v-btn
              color="primary"
              class="mt-3"
              @click="addMeasurement"
            >
              <v-icon left>mdi-plus</v-icon>
              Add Measurement
            </v-btn>
          </v-col>
        </v-row>

      </v-card-text>
    </v-card>

  </v-sheet>

</template>
  
<script setup lang="ts">
import {REQUIRED_RULE} from "~/utils/formRules";
import draggable from "vuedraggable";

const emit = defineEmits(["saved", "delete", "changed"]);
const props = defineProps({
  payload: {
    type: Object,
    required: false,
  },
  sizeChartTypeList:{
    type: Object,
    required: false,
  }
})
const dragOptions = {
  animation: 150
};

const snackbar = useSnackbar();
const chartTitle = ref(props?.payload?.label || "");
const init = ref(false);
const sizeItems = ["XS", "S", "M", "L", "XL", "XXL", "XXXL"];
const sizes = ref(props?.payload?.sizes || []);
const chartOrder = ref(Number(props?.payload?.order) || 1);
const chartType = ref(props?.payload?.type || "");

const createEmptyMeasurement = () => {
  if(!init.value && props.payload?.data){
    init.value = true;
    return props.payload.data;
  }
  
  if(!init.value) {
    init.value = true;
  }
  
  const values = {}
  sizes.value.forEach(size => {
    values[size] = {
      cm: '',
      in: ''
    }
  })
  
  return [{
    name: '',
    values
  }]
}
const measurements = ref([...createEmptyMeasurement()]);

const getCurrentValue = () => {
  if(!chartType?.value){
    snackbar.add({
      type: 'warning',
      text: `Please select ${chartTitle.value}'s chart type!`,
    })
    return;
  }
  return {data: measurements.value, sizes: sizes.value, label: chartTitle.value, order: Number(chartOrder.value) < 0 ? 1 : Number(chartOrder.value), type: chartType?.value};
}
const addMeasurement = () => {
  measurements.value = [...measurements.value, ...createEmptyMeasurement()]
}
const removeMeasurement = (index) => {
  measurements.value.splice(index, 1);
}
const adjustMeasurementSize = () => {
  const _sizes = sizes.value;

  measurements.value.forEach(measurement => {
    // Add new size entries
    _sizes.forEach(size => {
      if (!measurement.values[size]) {
        measurement.values[size] = {
          cm: '',
          in: ''
        }
      }
    });

    let _values = measurement.values;
    // Remove deleted sizes
    Object.keys(_values)
    .forEach(size => {
      if (!_sizes.includes(size)) {
        const { [size]: _, ...rest } = _values;
        _values = rest
      }
    });

    measurement.values = _values;
  });

}

watch(
    () => measurements.value,
    () => emit("changed", getCurrentValue()),
    { deep: true }
)
watch(
    () => sizes.value,
    () => emit("changed", getCurrentValue()),
    { deep: true }
)
watch(
    () => chartTitle.value,
    () => emit("changed", getCurrentValue()),
    { deep: true }
)
watch(
    () => chartOrder.value,
    () => emit("changed", getCurrentValue()),
    { deep: true }
)
watch(
    () => chartType.value,
    () => emit("changed", getCurrentValue()),
    { deep: true }
)

watch(
  () => sizes.value,
  () => {
    adjustMeasurementSize();
  },
  { deep: true }
);
</script>
  