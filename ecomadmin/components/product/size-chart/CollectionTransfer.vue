<template>
    <v-dialog v-model="dialog" transition="dialog-bottom-transition" style="max-width: 65%;">
        <v-card  elevation="10" title="Transfer to Collection">
            <v-card-item class="py-4 px-6">
                <v-form v-model="form" @submit.prevent="onSubmit" v-slot="{ isValid }">
                    <FormElementsCommonFieldContainer label="Country" :required="true" class="mb-3">
                        <v-select
                        :items="getCountries"
                        v-model="countryId"
                        label="Select Country"
                        item-title="name"
                        item-value="id"
                        hide-details
                        @update:modelValue="onCountryChange"
                        ></v-select>
                    </FormElementsCommonFieldContainer>

                    <FormElementsCommonFieldContainer label="Collection" :required="false" class="mb-3">
                        <v-select
                        :items="collectionItems"
                        v-model="selectedCollectionId"
                        label="Select collection"
                        item-title="title"
                        item-value="id"
                        hide-details
                        clearable
                        :rules="[val => REQUIRED_RULE(val)]"
                        ></v-select>
                    </FormElementsCommonFieldContainer>

                    <v-card-actions>
                        <v-spacer></v-spacer>
                        <v-btn :disabled="!isValid?.value" :title="isValid.value" :loading="loading" color="primary" variant="tonal" class="px-6" type="submit"> Transfer</v-btn>
                    </v-card-actions>
                </v-form>
          </v-card-item>
        </v-card>
    </v-dialog>
    <v-snackbar v-model="snackbar" :color="snackbarData.type">
    {{ snackbarData.message }}
  </v-snackbar>
</template>
<script setup lang="ts">
import {REQUIRED_RULE} from "~/utils/formRules";
import {useCountryStore} from "~/stores/others/country";
import { ref } from 'vue';
import {addEditApiItem, deleteApiItem, formDataManipulation, getAllApiItem, getApiItems} from "~/utils/helpers/functions";

const countryId = ref();
const selectedCollectionId = ref();
  
// Types for Country
interface Country {
    id: number;
    label: string;
    name: string;
}

const rules = [
  (value: any) => {
    if (value) return true;
    return 'You must enter a name'
  }
];

const countryStore = useCountryStore();
const snackbar = ref(false)
const snackbarData = ref({
  message: '',
  type: 'success'
})

// Reactive state
const dialog = ref(false);
const form = ref(false);
const loading = ref(false);

const getCountries = computed(() => countryStore.countries);

const selectedProductsId = ref<number[]>([]);

const collectionItems = ref([]);
const loadCollections = async () => {
    await getAllApiItem(`product-collection/country/${countryId.value}`)
            .then(res => {
                collectionItems.value = res.item;
            }).finally(() => {
                loading.value = false;
            })
}

const onCountryChange = () => {
    selectedCollectionId.value = undefined;
    loadCollections();
};

const show = async (productsId: number[]) => {
    countryId.value = 1;
    selectedProductsId.value = productsId;

    await countryStore.getAllCountries();
    await loadCollections();

    dialog.value = true;
};

const emit = defineEmits(['reset']);

const fakeReceiver = ref([]);
const onSubmit = () => {
    if (!form.value) return;
    
    loading.value = true
    addEditApiItem(`product-collection/${selectedCollectionId.value}/products`, null, '', {
        productsId: selectedProductsId.value,
    }, fakeReceiver.value)
    .then(res => {
        snackbar.value = true;
        snackbarData.value.message = res.message
        snackbarData.value.type = (res.success ? 'success' : 'error');

        if(res.success) {
            dialog.value = false;
        }

        emit('reset');

    }).finally(() => {
        loading.value = false;
    })
}

  defineExpose({show})
  </script>
  

