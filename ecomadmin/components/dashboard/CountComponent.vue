<template>
  <v-col cols="6" sm="4" md="4" lg="2">
    <v-sheet class="pa-3 rounded-md border-sm border-opacity-100">
      <div class="d-flex">
        <h3 class="text-h3 textPrimary">{{ counter }}</h3>
        <div class="mt-1 ml-1">
          <ArrowUpRightIcon stroke-width="1.5" size="22" class="text-success" />
        </div>
      </div>
      <div class="text-subtitle-1 textPrimary mt-1">{{ title }}</div>
    </v-sheet>
  </v-col>
</template>
<script setup lang="ts">
import { ArrowUpRightIcon } from "vue-tabler-icons";

interface Props {
  counter?: string | undefined;
  title?: string | undefined;
  description?: string | undefined;
}

const props = withDefaults(defineProps<Props>(), {
  counter: "0",
  title: "Default Title",
  description: "Description",
});
</script>
