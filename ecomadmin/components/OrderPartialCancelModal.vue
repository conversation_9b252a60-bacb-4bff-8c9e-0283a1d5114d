<template>
  <v-dialog v-model="open" max-width="600">
    <v-card>
      <v-card-title>Select Products to Cancel</v-card-title>
      <v-card-text>
        <div v-for="product in products" :key="product.id" style="margin-bottom: 12px;">
          <div>
            <del v-if="!availableQty(product)">{{ product.product?.name }}</del>
            <strong v-else>{{ product.product?.name }}</strong>
            <span> | Ordered: {{ product.quantity }} | Credited: {{ product.creditQty || 0 }} | Available: {{ availableQty(product) }}</span>
          </div>
          <v-row>
            <v-col cols="6">
              <v-checkbox
                v-model="selected"
                :label="'Select for credit'"
                :value="product.id"
                :disabled="availableQty(product) === 0"
              />
            </v-col>
            <v-col cols="6">
              <v-text-field
                v-model.number="creditQuantities[product.id]"
                :label="'Credit Qty'"
                type="number"
                :min="1"
                :max="availableQty(product)"
                :disabled="!selected.includes(product.id) || availableQty(product) === 0"
                @blur="validateQty(product)"
              />
            </v-col>
          </v-row>
        </div>
      </v-card-text>
      <v-card-actions>
        <v-spacer />
        <v-btn color="grey" @click="emit('close')">Cancel</v-btn>
        <v-btn color="primary" @click="confirm" :disabled="!canConfirm">Confirm</v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
const props = defineProps<{
  show: boolean,
  products: Array<{ id: number, product?: { name?: string }, quantity: number, creditQty?: number }>,
}>();

const open = ref(props.show);
watch(props, (val) => {
  open.value = val.show;
});

const emit = defineEmits(['close', 'confirm']);

const selected = ref<number[]>([]);
const creditQuantities = ref<{ [id: number]: number }>({});

function availableQty(product: any) {
  return product.quantity - (product.creditQty || 0);
}

function validateQty(product: any) {
  const max = availableQty(product);
  const val = creditQuantities.value[product.id];
  if (val < 1) creditQuantities.value[product.id] = 1;
  if (val > max) creditQuantities.value[product.id] = max;
}

const canConfirm = computed(() => {
  return selected.value.some(
    id =>
      creditQuantities.value[id] &&
      creditQuantities.value[id] > 0 &&
      creditQuantities.value[id] <= availableQty(props.products.find(p => p.id === id))
  );
});

function confirm() {
  const payload = selected.value
    .map(id => ({
      id,
      creditQty: creditQuantities.value[id]
    }))
    .filter(item => item.creditQty > 0 && item.creditQty <= availableQty(props.products.find(p => p.id === item.id)));
  emit('confirm', payload);
}
</script>