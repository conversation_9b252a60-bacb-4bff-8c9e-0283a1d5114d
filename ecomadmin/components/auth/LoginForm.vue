<template>
  <Form @submit="login" v-slot="{ isSubmitting }" class="mt-5">
    <v-label class="text-subtitle-1 font-weight-semibold mb-2 text-lightText"
      >Email</v-label
    >
    <VTextField
      v-model="email"
      :rules="emailRules"
      class="mb-2"
      required
      type="email"
    ></VTextField>

    <v-label class="text-subtitle-1 font-weight-semibold mb-2 text-lightText"
      >Password</v-label
    >
    <VTextField
      v-model="password"
      :rules="passwordRules"
      required
      type="password"
      class="mb-2"
    />

    <v-btn
      size="large"
      :loading="isSubmitting"
      color="primary"
      :disabled="!password"
      block
      type="submit"
      flat
      >Sign In</v-btn
    >

    <v-snackbar :timeout="1000" v-model="snackbar.show" :color="snackbar.color">
      {{ snackbar.message }}
    </v-snackbar>
  </Form>
</template>


<script setup lang="ts">
import { ref } from "vue";
import { Form } from "vee-validate";

import { useAuthStore } from "~/stores/auth";
import { useRolePermissionsStore } from "~/stores/administration/permissions";

const authStore = useAuthStore();
const permissionStore = useRolePermissionsStore();

const snackbar = ref({
  show: false,
  message: "",
  color: "success",
});

const isSubmitting = ref(false);
const password = ref<any>("");
const email = ref<any>("");
const passwordRules = ref([(v: string) => !!v || "Password is required"]);

const emailRules = ref([
  (v: string) => !!v || "E-mail is required",
  (v: string) => /.+@.+\..+/.test(v) || "E-mail must be valid",
]);

// const snackbar = useSnackbar();

const login = async () => {
  isSubmitting.value = true;
  const response: any = await authStore.authenticateUser({
    email,
    password,
    userTypeId: 1,
  });

  if (response.isSuccess) {
    snackbar.value = {
      show: true,
      message: `Welcome ${response.data?.firstName} ${response.data?.lastName}`,
      color: "success",
    };
    permissionStore.rolesFindByToken();
    setTimeout(async () => {
      isSubmitting.value = false;
      await navigateTo({ path: `/dashboard` });
    }, 300);
  } else {
    snackbar.value = {
      show: true,
      message: response.message || "Authentication failed",
      color: "error",
    };
    isSubmitting.value = false;
  }
};
</script>