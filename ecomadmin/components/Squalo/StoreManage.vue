<template>
  <v-form @submit.prevent="onSubmit" class="pa-2">
    <v-row>
      <v-col cols="12" sm="6">
        <v-select
          v-model="selectedCountry"
          label="Country"
          density="compact"
          :error="errors.countryId != null"
          :error-messages="errors.countryId"
          @blur="validateField('countryId')"
          @update="values.countryId = selectedCountry.id"
          :items="countryStore.countries"
          item-title="name"
          item-value="id"
          return-object
          required
        ></v-select>
      </v-col>
      <v-col cols="12" sm="6">
        <v-text-field
          v-model="values.name"
          label="Name"
          density="compact"
          :error="errors.name != null"
          :error-messages="errors.name"
          @blur="validateField('name')"
          required
        ></v-text-field>
      </v-col>
      <v-col cols="12" sm="6">
        <v-text-field
          v-model="values.domain"
          label="Domain"
          density="compact"
          :error="errors.domain != null"
          :error-messages="errors.domain"
          required
          disabled
        ></v-text-field>
      </v-col>
      <v-col cols="12" sm="6">
        <v-text-field
          v-model="values.email"
          label="Email"
          density="compact"
          :error="errors.email != null"
          :error-messages="errors.email"
          required
        ></v-text-field>
      </v-col>
      <v-col cols="12" sm="6">
        <v-text-field
          v-model="values.currency"
          label="Currency"
          density="compact"
          :error="errors.currency != null"
          :error-messages="errors.currency"
          required
          disabled
        ></v-text-field>
      </v-col>
      <v-col cols="12" class="d-flex ga-2">
        <v-btn
          v-if="props?.squaloStore?.currentSqualoStore"
          type="submit"
          color="primary"
          :disabled="props.squaloStore.isSynchronizing"
          flat
          >Update Store</v-btn
        >
        <v-btn
          v-else
          type="submit"
          color="primary"
          :disabled="props.squaloStore.isSynchronizing"
          flat
          >Sync</v-btn
        >
        <v-btn @click="resetFormValues" color="primary" flat>Reset</v-btn>
      </v-col>
    </v-row>
  </v-form>
</template>

<script setup lang="ts">
import { useField, useForm } from "vee-validate";
import * as yup from "yup";

const snackbar = useSnackbar();
const props = defineProps({
  countryStore: null,
  squaloStore: null,
});
const selectedCountry = ref(null);
const loading = ref(false);
const schema = yup.object({
  countryId: yup.string().required("Country is required"),
  name: yup.string().required("Name is required"),
  domain: yup.string().required("Domain is required"),
  email: yup.string().email("Invalid email").required("Email is required"),
  currency: yup.string().required("Currency is required"),
});

// Form initialization with useForm
const { handleSubmit, errors, validateField, values, resetForm } = useForm({
  validationSchema: schema,
  initialValues: {
    countryId: "",
    name: "",
    domain: "",
    email: "",
    currency: "",
  },
  validateOnMount: false,
});

useField("countryId");
useField("name");
useField("domain");
useField("email");
useField("currency");

const resetFormValues = () => {
  props.squaloStore.currentSqualoStore = null;
  selectedCountry.value = null;
  resetForm({
    values: {
      countryId: "",
      name: "",
      domain: "",
      email: "",
      currency: "",
    },
  });
};

const onSubmit = handleSubmit((values) => {
  let payload = {
    id: values?.countryId,
    list_id: values?.countryId,
    name: values?.name,
    platform: "pantoneclo",
    domain: values?.domain,
    is_syncing: true,
    email_address: values?.email,
    currency_code: values?.currency,
    primary_locale: "string",
    timezone: "string",
  };
  props.squaloStore.isSynchronizing = true;
  if (props?.squaloStore?.currentSqualoStore) {
    props?.squaloStore
      .updateSqualoStore(payload)
      .then((res: any) => {
        snackbar.add({
          type: res?.isSuccess ? "success" : "error",
          text: res?.messasge,
        });
        resetFormValues();
        props.squaloStore.currentSqualoStore = null;
        // Update the object with new value from API
        const index = props.squaloStore.squaloStoreList.findIndex(
          (item: any) => item.id === res.id
        );
        if (index !== -1) {
          props.squaloStore.squaloStoreList[index] = {
            ...props.squaloStore.squaloStoreList[index],
            ...res,
          };
        }
        props.squaloStore.isSynchronizing = false;
      })
      .catch((e: any) => {
        snackbar.add({
          type: "error",
          text: JSON.stringify(e),
        });
        props.squaloStore.isSynchronizing = false;
      });
  } else {
    props?.squaloStore
      .addSqualoStore(payload)
      .then((res: any) => {
        snackbar.add({
          type: res?.isSuccess ? "success" : "error",
          text: res?.messasge,
        });
        resetFormValues();
        props.squaloStore.currentSqualoStore = null;
        // Add new object to squalo list from API
        props?.squaloStore?.squaloStoreList?.push(res?.data);
        props.squaloStore.isSynchronizing = false;
      })
      .catch((e: any) => {
        snackbar.add({
          type: "error",
          text: JSON.stringify(e),
        });
        props.squaloStore.isSynchronizing = false;
      });
  }
});

watch(
  () => selectedCountry.value,
  (newVal, oldVal) => {
    if (newVal && !props?.squaloStore?.currentSqualoStore) {
      const country = props?.countryStore?.countries?.find(
        (el) => el.id === Number(newVal?.id)
      );
      values.countryId = country?.id;
      values.domain = country?.domain;
      values.email = country?.email;
      values.currency = country?.currency?.currency;
    }
  }
);

watch(
  () => props?.squaloStore?.currentSqualoStore,
  (newVal, oldVal) => {
    if (newVal != oldVal) {
      const country = props?.countryStore?.countries?.find(
        (el) => el.id === Number(newVal?.id)
      );
      resetForm({
        values: {
          countryId: newVal?.id,
          name: newVal?.name,
          domain: newVal?.domain,
          email: newVal?.email_address,
          currency: newVal?.currency_code,
        },
      });
      selectedCountry.value = country;
    }
  }
);
</script>
