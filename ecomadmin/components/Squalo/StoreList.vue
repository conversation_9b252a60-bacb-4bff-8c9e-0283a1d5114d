<template>
  <v-sheet>
    <h5 class="text-h5 font-weight-bold">
      <v-icon icon="mdi-layers"></v-icon> All Squalo Store ({{
        props?.squaloStore?.totalSqualoStore
      }})
    </h5>
    <div v-if="!loading">
      <v-list
        lines="one"
        class="px-2 overflow-y-auto pnt-scrollbar"
        max-height="300"
      >
        <v-list-item
          v-for="(storeItem, index) in props?.squaloStore?.squaloStoreList"
          :key="'store_' + index"
          class="px-2 my-2 border border-b"
        >
          <v-list-item-title class="text-body-1 font-weight-bold"
            >{{ storeItem?.name }}
          </v-list-item-title>
          <v-list-item-subtitle class="text-body-2 font-weight-light"
            >Domain: {{ storeItem?.domain }}, Currency:
            {{ storeItem?.currency_code }}</v-list-item-subtitle
          >
          <div class="d-flex ga-2">
            <v-btn
              @click="handleEditSqualoStore(storeItem)"
              class="text-caption"
              density="compact"
              color="primary"
              flat
              >edit</v-btn
            >
            <v-btn
              @click="handleDeleteSqualoStore(storeItem)"
              class="text-caption"
              density="compact"
              color="error"
              flat
              >delete</v-btn
            >
          </div>
        </v-list-item>
      </v-list>
      <div
        v-if="props?.squaloStore?.squaloStoreList?.length === 0"
        class="border-dotted rounded d-flex justify-center align-center"
        style="height: 100px"
      >
        <h5 class="text-center text-h6">No Stores</h5>
      </div>
    </div>
    <v-skeleton-loader v-else type="card"></v-skeleton-loader>
  </v-sheet>
</template>

<script setup lang="ts">
const props = defineProps({
  squaloStore: null,
});
const loading = ref(false);

const handleFetchSqualoStores = () => {
  loading.value = true;
  props?.squaloStore
    ?.fetchSqualoStores()
    .then((res: any) => {
      if (res?.isSuccess) {
        props.squaloStore.squaloStoreList = res?.data?.stores;
        props.squaloStore.totalSqualoStore = res?.data?.total_items;
      }
      loading.value = false;
    })
    .catch((e: any) => {
      loading.value = false;
    });
};

const handleAddItemSqualoStoreList = (newStoreItem) => {
  props.squaloStore.squaloStoreList.push(newStoreItem);
};
const handleEditSqualoStore = (storeItem: any) => {
  props.squaloStore.currentSqualoStore = storeItem;
};

const handleDeleteSqualoStore = (storeItem: any) => {
  loading.value = true;
  props.squaloStore
    .deleteSqualoStore(storeItem)
    .then((response: any) => {
      props.squaloStore.squaloStoreList =
        props.squaloStore.squaloStoreList.filter(
          (el: any) => Number(el?.id) != Number(storeItem?.id)
        );
      loading.value = false;
    })
    .catch((e: Error) => {
      loading.value = false;
    });
};

onMounted(() => {
  handleFetchSqualoStores();
});
</script>
