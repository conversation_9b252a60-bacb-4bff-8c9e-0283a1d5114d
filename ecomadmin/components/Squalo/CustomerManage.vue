<template>
  <v-form @submit.prevent="onSubmit" class="pa-2">
    <v-row>
      <v-col cols="12" md="6">
        <v-select
          v-model="values.storeId"
          label="Squalo Store"
          density="compact"
          :error="errors.storeId"
          :error-messages="errors.storeId"
          @blur="validateField('storeId')"
          :items="squaloStore?.squaloStoreList"
          item-title="name"
          item-value="id"
          required
        ></v-select>
      </v-col>
      <v-col cols="12" md="6">
        <v-select
          v-model="selectedCountry"
          label="Country"
          density="compact"
          :error="errors.countryId != null"
          :error-messages="errors.countryId"
          @blur="validateField('countryId')"
          @update:modelValue="values.countryId = selectedCountry.id"
          :items="countryStore?.countries"
          item-title="name"
          item-value="id"
          return-object
          required
        ></v-select>
      </v-col>
      <!-- <v-col cols="12" md="6">
        <v-checkbox
          v-model="values.isAllSubscriber"
          label="All Subscriber list"
          density="compact"
          :error="errors.title"
          :error-messages="errors.title"
          required
        ></v-checkbox>
      </v-col> -->
      <v-col cols="12">
        <v-btn
          type="submit"
          color="primary"
          :disabled="props?.squaloStore?.isSynchronizing"
          flat
          >Sync</v-btn
        >
        <v-btn
          @click="handleDeleteCustomerStore"
          color="error"
          class="ms-2"
          :disabled="props?.squaloStore?.isSynchronizing"
          flat
          >Delete</v-btn
        >
      </v-col>
    </v-row>
  </v-form>
</template>

<script setup>
import { useField, useForm } from "vee-validate";
import * as yup from "yup";

const snackbar = useSnackbar();
const props = defineProps({
  countryStore: null,
  squaloStore: null,
});

const selectedCountry = ref(null);
const loading = ref(false);

const schema = yup.object({
  storeId: yup.string().required("Store is required"),
  countryId: yup.string().required("Country is required"),
  // isAllSubscriber: yup.boolean().oneOf([true], "Subcribe customers must need!"),
});

// Form initialization with useForm
const { handleSubmit, errors, validateField, values, resetForm } = useForm({
  validationSchema: schema,
  initialValues: {
    storeId: "",
    countryId: "",
    // isAllSu/bscriber: false,
  },
  validateOnMount: false,
});

useField("storeId");
useField("countryId");
// useField("isAllSubscriber");

const resetFormValues = () => {
  selectedCountry.value = null;
  resetForm({
    values: {
      storeId: "",
      countryId: "",
      // isAllSubscriber: false,
    },
  });
};

const onSubmit = handleSubmit((values) => {
  let params = {
    storeId: values?.storeId,
    countryId: values?.countryId,
  };
  props.squaloStore.isSynchronizing = true;
  props?.squaloStore
    ?.getCustomerSqualoStore(params)
    .then((response) => {
      snackbar.add({
        type: response?.isSuccess ? "success" : "error",
        text: response?.messasge,
      });
      const delay = props?.squaloStore?.calculateDelay(response);
      setTimeout(() => {
        props.squaloStore.isSynchronizing = false;
      }, delay);
    })
    .catch((e) => {
      props.squaloStore.isSynchronizing = false;
    });
  resetFormValues();
});

const handleDeleteCustomerStore = () => {
  let params = {
    storeId: values?.storeId,
    countryId: values?.countryId,
  };
  if (!params?.storeId || !params?.countryId) {
    snackbar.add({
      type: "error",
      text: "Select store and country properly!",
    });
    return;
  }
  props.squaloStore.isSynchronizing = true;
  props?.squaloStore
    ?.deleteCustomerSqualoStore(params)
    .then((response) => {
      snackbar.add({
        type: response?.isSuccess ? "success" : "error",
        text: response?.messasge,
      });
      const delay = props?.squaloStore?.calculateDelay(response);
      setTimeout(() => {
        props.squaloStore.isSynchronizing = false;
      }, delay);
    })
    .catch((e) => {
      props.squaloStore.isSynchronizing = false;
    });
  resetFormValues();
};
</script>
