<template>
	<v-autocomplete v-model="tags" label="Add a tag" multiple chips closable-chips :items="tagStore.tags"
		item-title="name" item-value="id" :search-input.sync="tagOptions.search" @update:search-input="" hide-no-data
		hide-selected variant="outlined" density="compact" />
</template>

<script setup>
import { useBlogTagsStore } from '~/stores/blog/tags';

const props = defineProps({
	tags:{
		type: Array,
		default: [],
	}
});

const emits = defineEmits(['update:tags'])

const tagStore = useBlogTagsStore();
const tags = ref(props?.tags);

const handleUpdateTags = ()=>{
	emits('update:tags', tags.value);
}

const tagOptions = reactive({
	order: 'ASC',
	take: 20,
	page: 1,
	search: '',
})

const handleFetchTags = () => {
	let params = { ...tagOptions };
	tagStore.getTags(params);
}

watch(
	()=> tags.value,
	(newVal, oldVal)=>{
		if(newVal != oldVal){
			handleUpdateTags();
		}
	}
)

onMounted(() => {
	handleFetchTags();
});
</script>