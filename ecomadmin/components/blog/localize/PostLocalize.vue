<template>
	<v-overlay v-model="dialog" transition="dialog-bottom-transition" persistent style="z-index: 99;" class="d-flex justify-center align-center" >
		<v-card elevation="10" :title="`Localize: ${attributeName ? ' (' + attributeName + ')' : ''}`" max-width="1000" max-height="700" class="position-relative overflow-auto">
			<v-btn icon="mdi-close" size="x-small" color="error" @click="dialog = false" class="position-absolute" style="top: 5px; right: 5px;"></v-btn>
			<v-card-item class="py-4 px-6">
				<v-card>
					<v-tabs bg-color="teal-darken-3" slider-color="teal-lighten-3" show-arrows v-model="activeTab">
						<v-tab v-for="c in countries" :key="c.languageId" :text="c.label" :value="c.languageId"></v-tab>
					</v-tabs>
				</v-card>
				<v-container>
					<v-row no-gutters>
						<v-col v-if="countries.length">
							<template v-for="country in countries" :key="country.languageId">
								<v-form v-model="form" @submit.prevent="onSubmit" v-slot="{ reset, isValid }"
									v-if="country?.languageId === activeTab">
									<v-switch v-model="country.visible" label="active" color="success" @change="handleCountryActive(country?.countryId)"></v-switch>
									<FormElementsCommonFieldContainer label="Title" :required="true">
										<v-text-field v-model="country.title" :rules="rules" placeholder="Enter Title"></v-text-field>
									</FormElementsCommonFieldContainer>
									<v-img v-if="country?.featuredImageInfo" :src="country?.featuredImageInfo?.imageUrl"/>
									<FormElementsImageViewerSingle :image-id="country.featuredImageInfo" :image-rules="PRODUCT_IMAGE_RULES"
										:required-height="2250" :required-width="1800"
										:image-folders="[{ folderSlug: 'blog', title: 'Blog', size: '1800x2250' }]" image-model="blog"
										@selected-image-id="selectedFeatureImage" :disableImageValidation="true" />
										<v-textarea label="Excerpt" v-model="country.excerpt" row="2"></v-textarea>
									<h4 class="text-h4 mt-3">Meta Setting</h4>
									<SharedUiParentCardSolid body-class="px-3 py-5" class="mt-3 d-flex flex-column gap-2">
										<v-text-field v-model="country.metaTitle" label="Title" density="compact"></v-text-field>
										<v-text-field v-model="country.metaDescription" label="Description" density="compact"></v-text-field>
									</SharedUiParentCardSolid>

									<FormElementsCommonFieldContainer label="Description" :required="false">
										<FormElementsRichTextEditor v-model="country.content" />
									</FormElementsCommonFieldContainer>
									<v-divider class="my-5"></v-divider>
									<v-card-actions>
										<v-spacer></v-spacer>
										<v-btn :disabled="!isValid?.value" :title="isValid.value" :loading="loading" color="primary"
											variant="tonal" class="px-6" type="submit"> Save</v-btn>
										<v-btn color="error" variant="tonal" class="px-6 ms-2" @click="reset">
											Reset</v-btn>
									</v-card-actions>
								</v-form>
							</template>
						</v-col>
					</v-row>
				</v-container>
			</v-card-item>
		</v-card>
	</v-overlay>
</template>
<script setup lang="ts">
import { ref } from 'vue';
import { getLocaLizeLanguages } from "~/utils/languages";
import { useBlogTagsStore } from "~/stores/blog/tags";
import { useBlogPostsStore } from '~/stores/blog/posts';

const rules = [
	(value: any) => {
		if (value) return true;
		return 'You must enter a name'
	}
];

const store = useBlogPostsStore();
const snackbar = useSnackbar();

// Reactive state
const dialog = ref(false);
const activeTab = ref(2); // Controls the active tab

const form = ref(false);
const loading = ref(false);

const countries = ref([]);

const activeAttributeId = ref();
const attributeName = ref();

const countryVisibility = ref();

const handleCountryActive = (countryId: number)=>{
	if(countryId){
		handleUpdateCountryVisibility();
	}
}

const isVisibleCountry = (countryId: number) => {
	if (countryVisibility?.value?.hasOwnProperty(countryId) && countryVisibility.value[countryId]) {
		return true;
	}
	return false;
}

const show = async (attributeItem: any) => {
	activeAttributeId.value = attributeItem.id;
	attributeName.value = attributeItem.title;

	const locale = attributeItem?.localizations ? attributeItem.localizations : {};
	countryVisibility.value = attributeItem?.metaInfo?.countryVisibility ?? {};

	let countryData = [];
	const list = [...getLocaLizeLanguages];

	for (const data of list) {
		const languageId = data?.id;
		const row = locale?.find((item: any)=> item?.languageId === languageId);
		countryData.push({
			blogPost: attributeItem?.id,
			languageId: languageId,
			countryId: data?.countryId,
			label: data.name,
			id: row?.id ?? null,
			title: row?.title || '',
			excerpt: row?.excerpt ?? '',
			content: row?.content || '',
			featuredImageInfo: row?.featuredImageInfo ?? null,
			metaTitle: row?.metaTitle ?? '',
			metaDescription: row?.metaDescription ?? '',
			metaInfo:{
				title: row?.metaInfo?.title ?? "",
				description: row?.metaInfo?.description ?? "",
			},
			status: "published",
			visible: isVisibleCountry(data?.countryId)
		});
	}

	countries.value = [...countryData];
	dialog.value = true;
};


const handleAddLocalize = async (payload:any)=>{
	loading.value = true;
	const response = await store?.createSinglePostLocalize({ blogPost: payload?.blogPost, ...payload })
	snackbar.add({
		type: response?.success ? 'success' : 'error',
		text: response?.message,
	});
	loading.value = false;
	store.getAllPosts();
	dialog.value = false;
}

const handleUpdateLocalize = async (payload:any)=>{
	loading.value = true;
	const response = await store?.updateSinglePostLocalize({ ...payload, featuredImageInfo: payload?.featuredImageInfo?.id ?? payload?.featuredImageInfo });
	snackbar.add({
		type: response?.success ? 'success' : 'error',
		text: response?.message,
	});
	loading.value = false;
	store.getAllPosts();
	dialog.value = false;
}

const handleUpdateCountryVisibility = async () => {
	let payload = {
		id: activeAttributeId.value,
		metaInfo:{
			countryVisibility: {

			}
		}
	};
	countries.value.forEach((country:any)=>{
		payload.metaInfo.countryVisibility[country.countryId] = country.visible;
	})

  try {
    await store?.updateSinglePost(payload);
    snackbar.add({
      type: 'success',
      text: 'Blog country status updated successfully!'
    });
		// router.push('/blog/posts')
  } catch (e) {
    snackbar.add({
      type: 'error',
      text: 'Error updating blog country status!'
    });
  }
};

const selectedFeatureImage = (val) => {
	countries.value.forEach((country)=>{
		if(country.languageId === activeTab.value){
			country.featuredImageInfo = val;
		}
	})
};

const onSubmit = async () => {
	if (!form.value) return;

	let payload = countries?.value?.find((item: any)=> item?.languageId === activeTab.value);

	if (!payload) return;

	if(payload?.id){
		handleUpdateLocalize(payload);
	}
	else{
		handleAddLocalize(payload);
	}
}

defineExpose({ show })
</script>
