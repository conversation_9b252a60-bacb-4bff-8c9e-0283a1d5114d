<template>

  <v-menu :close-on-content-click="false">
    <template v-slot:activator="{ props }">
      <v-btn class="custom-hover-primary" variant="text" v-bind="props" icon>
        <v-avatar size="35">
          <v-img :src="userDefaultImg" width="35" />
        </v-avatar>
      </v-btn>
    </template>
    <v-sheet rounded="md" width="360" elevation="10">
      <div class="px-8 pt-6">
        <h6 class="text-h5 font-weight-medium">User Profile</h6>
        <div class="d-flex align-center mt-4 pb-6">
          <v-avatar size="80">
            <v-img :src="userDefaultImg" width="80" />
          </v-avatar>
          <div class="ml-3">
            <h6 class="text-h6 mb-n1"></h6>
            <span class="text-subtitle-1 font-weight-regular textSecondary"
              >

              {{ currentUser?.firstName + ' ' + currentUser?.lastName}}
            </span
            >
            <div class="d-flex align-center mt-1">
              <MailIcon size="18" stroke-width="1.5" />
              <span
                class="text-subtitle-1 font-weight-regular textSecondary ml-2"
                >
                {{ currentUser?.email }}
              </span
              >
            </div>
          </div>
        </div>
        <v-divider></v-divider>
      </div>
      <div class="pt-4 pb-6 px-8 text-center">
        <v-btn color="primary" variant="outlined" block @click="logout"
          >Logout</v-btn
        >
      </div>
    </v-sheet>
  </v-menu>
</template>

<script setup lang="ts">
import { MailIcon } from "vue-tabler-icons";
import userDefaultImg from '/images/profile/user-1.jpg'
import {useAuthStore} from "~/stores/auth";

const authStore = useAuthStore();
const router = useRouter();

const currentUser = useCookie<CurrentAdminUser>('PantonecloAdminUser');

const logout = () => {
  authStore.logUserOut();
  router.push('/auth/login');
};

</script>
