<script setup lang="ts">
import { ref, watch } from 'vue';
import { useCustomizerStore } from '@/stores/customizer';
// Icon Imports
import { GridDotsIcon, Menu2Icon } from 'vue-tabler-icons';
const customizer = useCustomizerStore();

const appsdrawer = ref(false);
const priority = ref(customizer.setHorizontalLayout ? 0 : 0);

watch(priority, (newPriority) => {
    // yes, console.log() is a side effect
    priority.value = newPriority;
});

import {useAuthStore} from "~/stores/auth";

const authStore = useAuthStore();
const router = useRouter();

const currentUser = useCookie<CurrentAdminUser>('PantonecloAdminUser');

</script>

<template>
    <v-app-bar elevation="0" :priority="priority" height="50" class="border-b border-opacity-100">
        <v-btn class="hidden-md-and-down custom-hover-primary" variant="text"
            @click.stop="customizer.SET_MINI_SIDEBAR(!customizer.mini_sidebar)">
            <v-icon size="20" icon="mdi-menu"/>
        </v-btn>
        <v-btn class="hidden-lg-and-up ms-3" icon variant="flat" @click.stop="customizer.SET_MINI_SIDEBAR(!customizer.mini_sidebar)"
            size="small">
            <v-icon size="20" icon="mdi-menu" />
        </v-btn>

        <v-spacer />
        <div class="text-caption d-flex flex-column align-end ga-0 me-2 pe-2 border-e-md">
            <p class="font-weight-bold">Hey, {{currentUser.firstName}}</p>
            <p>{{currentUser.email}}</p>
        </div>
        <div class="me-3">
            <LcFullVerticalHeaderProfileDD />
        </div>
    </v-app-bar>

</template>
