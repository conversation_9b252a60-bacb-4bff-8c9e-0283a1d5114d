<script setup lang="ts">

import { useTheme } from 'vuetify';
const theme = useTheme();

</script>

<!------------------------------------->
<!-- Customizer -->
<!------------------------------------->
<template>
    
<!--        <div class="pa-6">-->
<!--            <h5 class="text-h5">Settings</h5>-->
<!--        </div>-->
<!--        <v-divider></v-divider>-->
<!--        <perfect-scrollbar style="height: calc(100vh - 90px)">-->
<!--            <div class="pa-6">-->
<!--                <h6 class="text-h6 mb-2">Sidebar Layout</h6>-->
<!--                <v-btn-toggle v-model="customizer.setHorizontalLayout" color="primary" class="my-2 btn-group-custom gap-3" rounded="0" group>-->
<!--                    <v-btn :value="false" variant="text" elevation="9" class="rounded-md">-->
<!--                        <LayoutColumnsIcon stroke-width="1.5" size="21" class="mr-2" /> Vertical-->
<!--                    </v-btn>-->
<!--                    <v-btn :value="true" variant="text" elevation="9" class="rounded-md ">-->
<!--                        <LayoutNavbarIcon stroke-width="1.5" size="21" class="mr-2" /> Horizontal-->
<!--                    </v-btn>-->
<!--                </v-btn-toggle>-->

<!--                &lt;!&ndash;&#45;&#45;&#45;&#45;Theme Direction&#45;&#45;&#45;&#45;&ndash;&gt;-->
<!--                <h6 class="text-h6 mt-8 mb-5">Theme Direction</h6>-->
<!--                <v-btn-toggle v-model="customizer.setRTLLayout" color="primary" class="my-2 btn-group-custom gap-3" rounded="0" group>-->
<!--                    <v-btn :value="false" variant="text" elevation="9" class="rounded-md">-->
<!--                        <TextDirectionLtrIcon stroke-width="1.5" size="21" class="mr-2" /> LTR-->
<!--                    </v-btn>-->
<!--                    <v-btn :value="true" variant="text" elevation="9" class="rounded-md">-->
<!--                        <TextDirectionRtlIcon stroke-width="1.5" size="21" class="mr-2" /> RTL-->
<!--                    </v-btn>-->
<!--                </v-btn-toggle>-->


<!--                <h6 class="text-h6 mt-8 mb-5">Theme Color</h6>-->
<!--                <v-item-group mandatory v-model="customizer.actTheme" class="ml-n2 v-row">-->
<!--                    <v-col cols="4" v-for="theme in themeColors" :key="theme.name" class="pa-2">-->
<!--                        <v-item v-slot="{ isSelected, toggle }" :value="theme.name">-->
<!--                            <v-sheet-->
<!--                                rounded="md"-->
<!--                                class="border cursor-pointer d-block text-center px-5 py-4 hover-btns"-->
<!--                                elevation="9"-->
<!--                                @click="toggle"-->
<!--                            >-->
<!--                                <v-avatar :class="theme.bg" size="25">-->
<!--                                    <CheckIcon color="white" size="18" v-if="isSelected" />-->
<!--                                </v-avatar>-->
<!--                            </v-sheet>-->
<!--                        </v-item>-->
<!--                    </v-col>-->
<!--                </v-item-group>-->
<!--                <h6 class="text-h6 mt-11 mb-5">Theme Dark Color</h6>-->
<!--                <v-item-group mandatory v-model="customizer.actTheme" class="ml-n2 v-row">-->
<!--                    <v-col cols="4" v-for="theme in DarkthemeColors" :key="theme.name" class="pa-2">-->
<!--                        <v-item v-slot="{ isSelected, toggle }" :value="theme.name">-->
<!--                            <v-sheet-->
<!--                                rounded="md"-->
<!--                                class="border cursor-pointer d-block text-center px-5 py-4 hover-btns"-->
<!--                                elevation="9"-->
<!--                                @click="toggle"-->
<!--                            >-->
<!--                                <v-avatar :class="theme.bg" size="25">-->
<!--                                    <CheckIcon color="white" size="18" v-if="isSelected" />-->
<!--                                </v-avatar>-->
<!--                            </v-sheet>-->
<!--                        </v-item>-->
<!--                    </v-col>-->
<!--                </v-item-group>-->
<!--                <h6 class="text-h6 mt-11 mb-2">Container Option</h6>-->
<!--                <v-btn-toggle v-model="customizer.boxed" color="primary" class="my-2 btn-group-custom gap-3" rounded="0" group>-->
<!--                    <v-btn :value="true" variant="text" elevation="9" class="rounded-md">-->
<!--                        <LayoutDistributeVerticalIcon stroke-width="1.5" size="21" class="mr-2" />-->
<!--                        Boxed-->
<!--                    </v-btn>-->
<!--                    <v-btn :value="false" variant="text" elevation="9" class="rounded-md">-->
<!--                        <LayoutDistributeHorizontalIcon stroke-width="1.5" size="21" class="mr-2" />-->
<!--                        Full-->
<!--                    </v-btn>-->
<!--                </v-btn-toggle>-->
<!--                &lt;!&ndash;-Horizontal demo hide this option -&ndash;&gt;-->
<!--                <v-sheet v-if="customizer.setHorizontalLayout != true">-->
<!--                <h6 class="text-h6 mt-11 mb-2">Sidebar Type</h6>-->
<!--                <v-btn-toggle v-model="customizer.mini_sidebar" color="primary" class="my-2 btn-group-custom gap-3" rounded="0" group>-->
<!--                    <v-btn :value="false" variant="text" elevation="9" class="rounded-md">-->
<!--                        <LayoutSidebarIcon stroke-width="1.5" size="21" class="mr-2" />-->
<!--                        Full-->
<!--                    </v-btn>-->
<!--                    <v-btn :value="true" variant="text" elevation="9" class="rounded-md ">-->
<!--                        <LayoutSidebarLeftCollapseIcon stroke-width="1.5" size="21" class="mr-2" />-->
<!--                        Collapse-->
<!--                    </v-btn>-->
<!--                </v-btn-toggle>-->
<!--                </v-sheet>-->
<!--                <h6 class="text-h6 mt-11 mb-2">Card with</h6>-->
<!--                <v-btn-toggle v-model="customizer.setBorderCard" color="primary" class="my-2 btn-group-custom gap-3" rounded="0" group>-->
<!--                    <v-btn :value="false" variant="text" elevation="9" class="rounded-md">-->
<!--                        <LayoutSidebarLeftCollapseIcon stroke-width="1.5" size="21" class="mr-2" />-->
<!--                        Shadow-->
<!--                    </v-btn>-->
<!--                    <v-btn :value="true" variant="text" elevation="9" class="rounded-md">-->
<!--                        <LayoutSidebarIcon stroke-width="1.5" size="21" class="mr-2" />-->
<!--                        Border-->
<!--                    </v-btn>-->
<!--                </v-btn-toggle>-->
<!--            </div>-->
<!--        </perfect-scrollbar>-->
   
</template>

<style lang="scss"></style>
