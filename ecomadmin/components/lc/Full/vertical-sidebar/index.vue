<script setup lang="ts">
import { ref, shallowRef } from "vue";
import { useCustomizerStore } from "@/stores/customizer";
import sidebarItems from "./sidebarItem";
const customizer = useCustomizerStore();
const sidebarMenu = shallowRef(sidebarItems);
</script>

<template>
  <v-navigation-drawer v-model="customizer.mini_sidebar" left temporary color="sidebarBg" class="pnt-scrollbar">
    <div class="pa-3">
      <LcFullLogoPantonecloLogo />
    </div>

    <!-- ---------------------------------------------- -->
    <!---Navigation -->
    <!-- ---------------------------------------------- -->
    <div class="scrollnavbar">
      <v-list class="pa-6" density="compact" bg-color="sidebarBg">
        <!---Menu Loop -->
        <template v-for="(item, index) in sidebarMenu">
          <!---Item Sub Header -->
          <LcFullVerticalSidebarNavGroup
            v-if="item.header"
            :item="item"
            :key="item.title"
          />
          <!---If Has Child -->
          <LcFullVerticalSidebarNavCollapse
            v-else-if="item.children"
            class="leftPadding"
            :item="item"
            :level="0"
            :key="item.title"
          />
          <!---Single Item-->
          <LcFullVerticalSidebarNavItem
            v-else
            :item="item"
            class="leftPadding"
            :key="item.title"
          />
          <!---End Single Item-->
        </template>
      </v-list>
      <!-- <div class="pa-6 userbottom">
                <LcFullVerticalSidebarProfile/>
            </div> -->
    </div>
  </v-navigation-drawer>
</template>


<style scoped lang="scss">
:deep(.v-navigation-drawer__content::-webkit-scrollbar) {
  width: 6px !important;
  height: 6px !important;
}
:deep(.v-navigation-drawer__content::-webkit-scrollbar-track) {
  background: #e0e0e0 !important;
}
:deep(.v-navigation-drawer__content::-webkit-scrollbar-button) {
  display: none !important;
}
:deep(.v-navigation-drawer__content::-webkit-scrollbar-thumb) {
  background: #a0a0a0 !important;
  border-radius: 10px !important;
}
:deep(.v-navigation-drawer__content::-webkit-scrollbar-thumb:hover) {
  background: #808080 !important;
}
</style>