import {
  BasketIcon,
  BorderStyle2Icon,
  CurrencyIcon,
  FlagIcon,
  PointIcon,
  ShoppingCartIcon,
  TruckIcon,
} from "vue-tabler-icons";

export interface menu {
  header?: string;
  title?: string;
  icon?: any;
  to?: string;
  chip?: string;
  chipBgColor?: string;
  chipColor?: string;
  chipVariant?: string;
  chipIcon?: string;
  children?: menu[];
  disabled?: boolean;
  type?: string;
  subCaption?: string;
}

const sidebarItem: menu[] = [
  {
    title: "Dashboard",
    icon: ShoppingCartIcon,
    to: "/dashboard",
  },

  {
    title: "Products",
    icon: BasketIcon,
    to: "/",
    children: [
      {
        title: "All Products",
        icon: PointIcon,
        to: "/products/product",
      },
      {
        title: "Add New",
        icon: PointIcon,
        to: "/products/product/add",
      },
      {
        title: "Categories",
        icon: PointIcon,
        to: "/products/categories",
      },
      {
        title: "Attribute Values",
        icon: PointIcon,
        to: "/products/attributevalues",
      },
      {
        title: "Brands",
        icon: PointIcon,
        to: "/products/brands",
      },
      {
        title: "Tags",
        icon: PointIcon,
        to: "/products/tags",
      },
      {
        title: "Size Chart",
        icon: PointIcon,
        to: "/products/size-chart",
      },
      {
        title: "Feeds",
        icon: PointIcon,
        to: "/products/feeds",
      },
      {
        title: "Collection",
        icon: PointIcon,
        to: "/products/collections",
      },
    ],
  },
  {
    title: "Discounts",
    icon: BorderStyle2Icon,
    to: "/",
    children: [
      {
        title: "Discounts",
        icon: PointIcon,
        to: "/discounts",
      },
      {
        title: "Fortunate wheels",
        icon: PointIcon,
        to: "/discounts/wheel",
      }
    ]
  },
  {
    title: "Order List",
    icon: BorderStyle2Icon,
    to: "/order-list",
  },
  {
    title: "Return List",
    icon: BorderStyle2Icon,
    to: "/return-list",
  },
  {
    title: "Peoples",
    icon: BorderStyle2Icon,
    to: "/",
    children: [
      {
        title: "Users",
        icon: FlagIcon,
        to: "/users",
      },
      {
        title: "Change Password",
        icon: FlagIcon,
        to: "/users/change-password",
      },
    ],
  },
  {
    title: "Event Management",
    icon: BorderStyle2Icon,
    to: "/",
    children: [
      {
        title: "Events",
        icon: FlagIcon,
        to: "/event-management/events",
      },
      {
        title: "Product Tagging",
        icon: FlagIcon,
        to: "/event-management/tag-product",
      },
    ],
  },
  {
    title: "General",
    icon: BorderStyle2Icon,
    to: "/",
    children: [
      {
        title: "Country",
        icon: FlagIcon,
        to: "/general/country",
      },
      {
        title: "Courier",
        icon: TruckIcon,
        to: "/general/courier",
      },
      // {
      //     title: "State",
      //     icon: FlagIcon,
      //     to: "/general/state",
      // },
      // {
      //     title: "City",
      //     icon: FlagIcon,
      //     to: "/general/city",
      // },
      {
        title: "Currency",
        icon: CurrencyIcon,
        to: "/general/currency",
      },
      {
        title: "Coupon",
        icon: CurrencyIcon,
        to: "/general/coupon",
      },
      {
        title: "Quick Links",
        icon: CurrencyIcon,
        to: "/general/quick-links",
      },
      {
        title: "Subscription Popup",
        icon: CurrencyIcon,
        to: "/general/subscription-popup",
      },
      {
        title: "Subscribe List",
        icon: CurrencyIcon,
        to: "/general/subscribe-list",
      },
      {
        title: "FAQ",
        icon: CurrencyIcon,
        to: "/general/faq",
      },
    ],
  },
  {
    title: "Home Page Settings",
    icon: BorderStyle2Icon,
    to: "/",
    children: [
      {
        title: "Settings",
        icon: CurrencyIcon,
        to: "/settings",
      },
      {
        title: "Banner Section",
        icon: FlagIcon,
        to: "/settings/banner-section",
      },
      {
        title: "Manage Sticky message",
        icon: FlagIcon,
        to: "/settings/header-sticky-message",
      },
      {
        title: "Manage Main banner",
        icon: FlagIcon,
        to: "/settings/main-banner",
      },
      // {
      //     title: "Header Sticky Message",
      //     icon: CurrencyIcon,
      //     to: "/settings/header-sticky-message",
      // },
      {
        title: "Manage Partner",
        icon: CurrencyIcon,
        to: "/settings/manage-partner",
      },
      {
        title: "Manage Instagram Post",
        icon: CurrencyIcon,
        to: "/settings/manage-instagram",
      },
      {
        title: "Manage Social Media Link",
        icon: CurrencyIcon,
        to: "/settings/manage-social-media",
      },
    ],
  },
  {
    title: "Reports",
    icon: BorderStyle2Icon,
    to: "/",
    children: [
      {
        title: "Sales Reports",
        icon: CurrencyIcon,
        to: "/reports/sales-reports",
      },
      {
        title: "Customer Demographics Reports",
        icon: FlagIcon,
        to: "/reports/customer-demographics-reports",
      },
      {
        title: "Customer Purchase History Reports",
        icon: FlagIcon,
        to: "/reports/customer-purchase-history-reports",
      },
      {
        title: "Best-Selling Products Reports",
        icon: FlagIcon,
        to: "/reports/best-selling-products-reports",
      },
      {
        title: "Slow-Moving Inventory Reports",
        icon: FlagIcon,
        to: "/reports/slow-moving-inventory-reports",
      },
      {
        title: "Product Return Rates and Reasons Reports",
        icon: FlagIcon,
        to: "/reports/product-return-rates-and-reasons-reports",
      },
      {
        title: "Financial Reports",
        icon: FlagIcon,
        to: "/reports/financial-reports",
      },

      // {
      //     title: "Order and Fulfillment Reports",
      //     icon: CurrencyIcon,
      //     to: "/reports/order-and-fulfillment-report",
      // },
      // {
      //     title: "VAT Report",
      //     icon: CurrencyIcon,
      //     to: "/reports/vat-report",
      // }
    ],
  },
  {
    title: "Integrations",
    icon: BorderStyle2Icon,
    to: "/",
    children: [
      {
        title: "Squalo",
        icon: CurrencyIcon,
        to: "/integrations/squalo",
      },
      {
        title: "Sitemap",
        icon: CurrencyIcon,
        to: "/integrations/sitemap",
      },
    ]
  },
  {
    title: "Administration",
    icon: BorderStyle2Icon,
    to: "/",
    children: [
      {
        title: "Role Management",
        icon: CurrencyIcon,
        to: "/administration/role-management",
      },
      // {
      //   title: "Permissions",
      //   icon: CurrencyIcon,
      //   to: "/administration/permissions",
      // },
    ]
  },
  {
    title: "Sliders",
    icon: BorderStyle2Icon,
    to: "/",
    children: [
      {
        title: "List",
        icon: CurrencyIcon,
        to: "/sliders",
      },
    ]
  },
  {
    title: "Blogs",
    icon: BorderStyle2Icon,
    to: "/",
    children: [
      {
        title: "Categories",
        icon: CurrencyIcon,
        to: "/blog/categories",
      },
      {
        title: "Posts",
        icon: CurrencyIcon,
        to: "/blog/posts",
      },
      {
        title: "Tags",
        icon: CurrencyIcon,
        to: "/blog/tags",
      },
      {
        title: "Authors",
        icon: CurrencyIcon,
        to: "/blog/authors",
      },
    ]
  }
];

export default sidebarItem;
