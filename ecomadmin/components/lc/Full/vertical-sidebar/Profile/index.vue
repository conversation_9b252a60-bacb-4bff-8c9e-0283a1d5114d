<template>
  <v-sheet rounded="md" color="lightsecondary" class="px-4 py-3 ExtraBox">
    <div class="d-flex align-center hide-menu">
      <v-avatar size="40">
        <v-img :src="userDefaultImg" width="40" />
      </v-avatar>
      <div class="ml-4">
        <h4 class="mb-n1 text-h6 textPrimary">{{ userInfo?.firstName + ' ' + userInfo?.lastName }}</h4>
      </div>
      <div class="ml-auto">
        <v-btn
          variant="text"
          icon
          rounded="md"
          color="primary"
          @click="logout"
        >
          <PowerIcon />
          <v-tooltip activator="parent" location="top">Logout</v-tooltip>
        </v-btn>
      </div>
    </div>
  </v-sheet>
</template>
<script setup lang="ts">
import { PowerIcon } from "vue-tabler-icons";
import userDefaultImg from '/images/profile/user-1.jpg'
import { useAuthStore } from '~/stores/auth';
const router = useRouter();

const authStore = useAuthStore();

const pantonecloAdminUser = useCookie<CurrentAdminUser>('PantonecloAdminUser');

const userInfo = pantonecloAdminUser.value

const logout = async () => {
  authStore.logUserOut();
  if( !authStore.authenticated ) {
    await navigateTo({ path: `/auth/login` });
  }
};


</script>
<style lang="scss">
.ExtraBox {
  position: relative;
  overflow: hidden;
}
.line-height-none {
  line-height: normal;
}
</style>
