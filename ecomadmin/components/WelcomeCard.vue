<script setup lang="ts">
import { ArrowUpRightIcon } from 'vue-tabler-icons';
import defaultUserImg from '/images/profile/user-1.jpg';
const authStore = useCookie<CurrentAdminUser>('PantonecloAdminUser');

</script>
<template>
    <v-card elevation="0" rounded="md" class="bg-lightprimary border-0">
        <v-card-item>
            <v-row>
                <v-col cols="12" sm="6">
                    <div class="d-flex align-center">
                        <v-avatar size="40">
                          <v-img :src="defaultUserImg" height="40" />
                        </v-avatar>
                        <h5 class="text-h5 ml-4">Welcome back {{ authStore?.firstName +' '+ authStore?.lastName}}!</h5>
                    </div>
                    <div class="mt-8">
                        <div class="d-flex">
                            <div>
                                <div class="d-flex">
                                    <h2 class="text-h2 textPrimary">$2,340 </h2>
                                    <div class="mt-1 ml-1">
                                        <ArrowUpRightIcon stroke-width="1.5" size="22" class="text-success" />
                                    </div>
                                </div>
                                <div class="text-subtitle-1 textPrimary mt-1">Today’s Sales</div>
                            </div>
                            <v-divider vertical class="mx-5"></v-divider>
                            <div>
                                <div class="d-flex">
                                    <h2 class="text-h2 textPrimary">35%</h2>
                                    <div class="mt-1 ml-1">
                                        <ArrowUpRightIcon stroke-width="1.5" size="22" class="text-success" />
                                    </div>
                                </div>
                                <div class="text-subtitle-1 textPrimary mt-1">Performance</div>
                            </div>
                        </div>
                    </div>
                </v-col>
                <v-col cols="12" sm="6" class="text-sm-right">
<!--                    <div class="mb-n2 ">-->
<!--                        <img src="/images/backgrounds/welcome-bg.svg" class="" alt="image" width="340" />-->
<!--                    </div>-->
                </v-col>
            </v-row>
        </v-card-item>
    </v-card>
</template>
