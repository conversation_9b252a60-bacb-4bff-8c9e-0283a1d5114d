<script setup lang="ts">
const props = defineProps({
  title: String,
  actions: Boolean,
  bodyClass: String
});
</script>

// ===============================|| Ui Parent Card||=============================== //
<template>
  <v-card elevation="10">
    <v-card-item class="py-4 px-6" v-if="title">
      <div class="d-sm-flex align-center justify-space-between">
        <v-card-title class="text-h5">{{ title }}</v-card-title>
        <!-- <template v-slot:append> -->
        <slot name="action"></slot>
        <!-- </template> -->
      </div>
    </v-card-item>
    <v-divider v-if="title"></v-divider>
    <v-card-text :class="bodyClass">
      <slot/>
    </v-card-text>
    <v-divider v-if="actions"></v-divider>
    <v-card-actions v-if="actions">
      <slot name="submit-button"></slot>
    </v-card-actions>
  </v-card>
</template>
