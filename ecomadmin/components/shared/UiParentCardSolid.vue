<template>
  <v-card elevation="0" class="border">
    <v-card-item class="pb-2 px-6" v-if="title">
      <div class="d-sm-flex align-center justify-space-between">
        <v-card-title class="text-body-1 text-md-h5">{{ title }}</v-card-title>
        <slot name="action"></slot>
      </div>
    </v-card-item>
    <v-divider v-if="title"></v-divider>
    <v-card-text :class="bodyClass" class="pt-2">
      <slot/>
    </v-card-text>
    <v-divider v-if="actions"></v-divider>
    <v-card-actions v-if="actions">
      <slot name="submit-button"></slot>
    </v-card-actions>
  </v-card>
</template>
<script setup lang="ts">
const props = defineProps({
  title: String,
  actions: Boolean,
  bodyClass: String
});
</script>
