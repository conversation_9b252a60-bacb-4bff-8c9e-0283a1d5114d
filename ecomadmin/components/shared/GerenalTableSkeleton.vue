<template>
  <div v-if="loading">
    <SharedUiLoader type="table-heading, table-thead, table-tbody, table-tfoot"/>
  </div>
  <div v-else-if="data?.length <= 0">
    <v-card>
      <v-card-text> Sorry no Data Found....</v-card-text>
    </v-card>
  </div>
  <div v-else>
    <slot></slot>
  </div>
</template>
<script setup lang="ts">
const props = defineProps({
  loading: Boolean,
  data: Array
})
</script>