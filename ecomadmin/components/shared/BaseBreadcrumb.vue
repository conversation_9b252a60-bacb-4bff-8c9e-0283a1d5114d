<script setup lang="ts">
const props = defineProps({
  title: String,
  breadcrumbs: Array as any,
  icon: String,
});
const route = useRoute();
</script>

<template>
  <v-sheet class="mb-3" style="background-color: transparent">
    <div class="d-flex flex-row ga-5">
      <h4 class="text-h4 mb-n1 border-s-lg border-lightprimary border-opacity-100 ps-2">{{ title }}</h4>
      <v-breadcrumbs
        :items="breadcrumbs"
        class="text-h6 font-weight-medium pa-0 ml-n1 align-end"
      >
        <template v-slot:divider>
          <div class="d-flex align-center text-h3 mt-n4">.</div>
        </template>
        <template v-slot:title="{ item }">
          <h6 class="text-subtitle-2">
            {{ item.text }}
          </h6>
        </template>
      </v-breadcrumbs>
    </div>
  </v-sheet>
</template>

<style lang="scss">
.page-breadcrumb {
  .v-toolbar {
    background: transparent;
  }
}
</style>
