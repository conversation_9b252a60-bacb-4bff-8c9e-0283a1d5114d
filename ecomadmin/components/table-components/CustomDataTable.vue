<template>
    <div>
        <v-data-table-server v-model:items-per-page="itemsPerPage" :headers="headers" :items="serverItems"
            :items-length="totalItems" :loading="loading" :search="search" item-value="name"
            @update:options="loadItems">
            <template v-slot:thead>
                <tr>
                    <td>
                        <v-text-field v-model="name" class="ma-1" density="compact" placeholder="Search name..."
                            hide-details></v-text-field>
                    </td>
                    <td>
                        <v-text-field v-model="email" class="ma-1" density="compact" placeholder="Search Email..."
                            type="text" hide-details></v-text-field>
                    </td>
                    <td>
                        <v-text-field v-model="phone_number" class="ma-1" density="compact"
                            placeholder="Search phone number..." type="number" hide-details></v-text-field>
                    </td>
                </tr>
            </template>
            <template v-slot:item.action="{ item }">
                <div class="text-end">
                    <v-btn
                      icon
                      color="warning"
                      class="me-2"
                      variant="tonal"
                      size="small"
                      
                  >
                    <PencilIcon size="20"/>
                  </v-btn>
                  <v-btn
                      icon
                      color="danger"
                      class="me-2"
                      variant="tonal"
                      size="small"
                      
                  >
                    <TrashIcon size="20"/>
                  </v-btn>
                  <v-btn
                      icon
                      color="danger"
                      class="me-2"
                      variant="tonal"
                      size="small"
                      
                  >
                    <EyeCheckIcon size="20"/>
                  </v-btn>
                  
                </div>
            </template>
        </v-data-table-server>    
    </div>
</template>

<script>
import {PencilIcon, TrashIcon, EyeCheckIcon} from "vue-tabler-icons";

const desserts = [
    {
        name: 'Frozen Yogurt',
        email: '<EMAIL>',
        phone_number: '0185664566',
        
    },
    {
        name: 'Test One',
        email: '<EMAIL>',
        phone_number: '01856634566',
    },
    {
        name: 'Test Two',
        email: '<EMAIL>',
        phone_number: '0195664566',
    },
    {
        name: 'Test Three',
        email: '<EMAIL>',
        phone_number: '0165664566',
    },
]

const FakeAPI = {
    async fetch({ page, itemsPerPage, sortBy, search }) {
        return new Promise(resolve => {
            setTimeout(() => {
                const start = (page - 1) * itemsPerPage
                const end = start + itemsPerPage
                const items = desserts.slice().filter(item => {
                    if (search.name && !item.name.toLowerCase().includes(search.name.toLowerCase())) {
                        return false
                    }

                    if (search.email && !item.email.toLowerCase().includes(search.email.toLowerCase())) {
                        return false
                    }

                    if (search.phone_number && !item.phone_number.toLowerCase().includes(search.phone_number.toLowerCase())) {
                        return false
                    }

                    return true
                })

                if (sortBy.length) {
                    const sortKey = sortBy[0].key
                    const sortOrder = sortBy[0].order
                    items.sort((a, b) => {
                        const aValue = a[sortKey]
                        const bValue = b[sortKey]
                        return sortOrder === 'desc' ? bValue - aValue : aValue - bValue
                    })
                }

                const paginated = items.slice(start, end)

                resolve({ items: paginated, total: items.length })
            }, 500)
        })
    },
}

export default {
    name: 'CustomDataTable',
    components: { PencilIcon, TrashIcon, EyeCheckIcon },
    data() {
        return {
            itemsPerPage: 5,
            headers: [
                {
                    title: 'Name',
                    align: 'start',
                    sortable: true,
                    key: 'name',
                },
                { title: 'Email', key: 'email', align: 'start' },
                { title: 'Phone number', key: 'phone_number', align: 'start' },
                { title: 'Action', key: 'action', align: 'start',sortable: false },
            ],
            serverItems: [],
            loading: true,
            totalItems: 0,
            name: '',
            email: '',
            phone_number: '',
            search: '',
           

        }
    },
    computed: {
        filteredAndSortedRows() {
            let filteredRows = this.rows.filter(row =>
                row.some(value => value.toString().toLowerCase().includes(this.filterQuery.toLowerCase()))
            );

            if (this.sortKey !== null) {
                filteredRows.sort((a, b) => {
                    let result = 0;
                    if (a[this.sortKey] > b[this.sortKey]) result = 1;
                    if (a[this.sortKey] < b[this.sortKey]) result = -1;
                    return this.sortOrder === 'asc' ? result : -result;
                });
            }
            return filteredRows;
        }
    },
    methods: {

        loadItems({ page, itemsPerPage, sortBy }) {
            this.loading = true
            FakeAPI.fetch({ page, itemsPerPage, sortBy, search: { name: this.name, email: this.email, phone_number: this.phone_number } }).then(({ items, total }) => {
                this.serverItems = items
                this.totalItems = total
                this.loading = false
            })
        },
    },
    watch: {
        name() {
            this.search = String(Date.now())
        },
        email() {
            this.search = String(Date.now())
        },
        phone_number() {
            this.search = String(Date.now())
        }
    },
}
</script>

<style scoped>
table {
    width: 100%;
    border-collapse: collapse;
}

th,
td {
    border: 1px solid #ddd;
    padding: 8px;
}

th {
    background-color: #f4f4f4;
}
</style>