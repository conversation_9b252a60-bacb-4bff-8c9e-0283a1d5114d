<template>
  <v-img v-if="isImageExtension(getExtension(imageUrl))" :src="imageUrl" width="140" class="mb-4"/>
  <video v-else-if="isVideoExtension(getExtension(imageUrl))" width="50" height="70" class="mb-0" autoplay preload="auto" loop playsinline muted>
    <source :src="imageUrl" type="video/mp4"/>
  </video>

  <div class="mb-3">
    <ModalsUploadImageModal @image-selected="selectedImage" @image-folder-change="handleImageFolderChange" :multiple="multiple" :image-upload-rules="imageRules" :modal-type="imageModelType" :max-size="maxSize" :required-width="requiredWidth" :required-height="requiredHeight" :image-folders="props?.imageFolders" 
      :disableImageValidation="disableImageValidation"/>
  </div>
</template>
<script setup lang="ts">
import {ref, watch} from 'vue';
import {useProductImagesStore} from "~/stores/products/product-images";

const imageStore = useProductImagesStore();

const props = defineProps({
  imageId: {
    type: Number,
    required: false
  },

  imageModel: {
    type: String
  },

  imageRules: {
    type: Array,
    required: false
  },
  requiredWidth: {
    type: Number
  },
  requiredHeight: {
    type: Number
  },
  maxSize: {
    type: Number
  },
  multiple: {
    type: Boolean,
    default: false
  },
  imageFolders:{
    type: Array,
    required: false
  },
  componentExecutedObject:{
    type: Object,
    required: false
  },
  disableImageValidation:{
    type: Boolean,
    required: false,
  }
});

const emit = defineEmits(['selectedImageId'])

const {isImageExtension, isVideoExtension, getExtension} = useMediaType();
const imageUrl = ref('');
const imageModelType = ref();

const selectedImage = (val: any) => {
  if(val.values[0]){
    imageStore.getAnyImageById(val.values[0]).then(res => {
      if (res) {
        imageUrl.value = res.imageUrl;
        emit('selectedImageId', res.id, props?.componentExecutedObject)
      }
    });
  }
}

watch(() => props.imageId, (newVal, OldVal) => {

  if( newVal && props.imageId ) {
    imageStore.getAnyImageById(props.imageId).then(res => {
      imageUrl.value = res.imageUrl
    })
  }else {
    imageUrl.value = ''
  }

}, { immediate: true })


const handleImageFolderChange = (folderSlug:String)=>{
  imageModelType.value = folderSlug;
}

onMounted(()=>{
  imageModelType.value = props?.imageModel;
})

</script>