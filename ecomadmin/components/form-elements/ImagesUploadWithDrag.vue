<template>
  <div>
    <div
        class="drop-area"
        @dragover.prevent
        @dragenter.prevent
        @drop="handleDrop"
    >
      <p>Drag and drop images here, or click to upload</p>
      <input
          type="file"
          multiple
          accept="image/*"
          style="display: none;"
          ref="fileInput"
          @change="handleFileInput"
      />
      <button @click="openFileInput">Browse</button>
    </div>

    <div class="image-preview">
      <draggable v-model="imagePreviews" :element="'div'" class="preview-item" :options="dragOptions">
        <div v-for="(image, index) in imagePreviews" :key="index" class="preview-item">
          <img :src="image.url" alt="Preview" />
          <button @click="deleteImage(index)">Delete</button>
        </div>
      </draggable>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import draggable from 'vuedraggable';

const imagePreviews = ref([]);
const dragOptions = {
  animation: 150
};

const handleFileInput = (event) => {
  const files = event.target.files;
  previewImages(files);
};

const handleDrop = (event) => {
  event.preventDefault();
  const files = event.dataTransfer.files;
  previewImages(files);
};

const openFileInput = () => {
  const fileInput = document.querySelector('input[type="file"]');
  fileInput.click();
};

const previewImages = (files) => {
  for (let i = 0; i < files.length; i++) {
    const reader = new FileReader();
    reader.onload = (e) => {
      const imageData = {
        url: e.target.result,
        file: files[i]
      };
      imagePreviews.value.push(imageData);
    };
    reader.readAsDataURL(files[i]);
  }
};

const deleteImage = (index) => {
  imagePreviews.value.splice(index, 1);
};
</script>

<style>
.drop-area {
  border: 2px dashed #ccc;
  padding: 20px;
  text-align: center;
}

.image-preview {
  margin-top: 20px;
}

.preview-item {
  margin-bottom: 10px;
}

.preview-item img {
  width: 100px;
  height: auto;
}

.preview-item button {
  margin-top: 5px;
}
</style>
