<template>
  <div>
    <v-row>
      <v-col cols="8">
        <div class="drop-area" @dragover.prevent @dragenter.prevent @drop="handleDrop">
          <p>Drag and drop images here, or click to upload</p>
          <template v-if="multiple">
            <input type="file" multiple accept="image/*" style="display: none;" ref="fileInput" @change="handleFileInput"/>
          </template>
          <template>
<!--                    <input type="file" accept="image/*" style="display: none;" ref="fileInput" @change="handleFileInput"  />-->
            <v-file-input @change="handleFileInput" />
          </template>
          <button @click="openFileInput">Browse</button>
        </div>
        <v-alert :type="'error'" :text="imageSizeError" v-if="imageSizeError"></v-alert>
        <v-alert :type="'error'" :text="imageDimensionError" v-if="imageDimensionError" class="mt-2"></v-alert>
      </v-col>
      <v-col cols="4">
        <v-sheet v-if="imageUploadRules">
          <h6 class="text-subtitle-1 font-weight-bold mb-2">Image Upload Rules</h6>
          <div
              v-for="(item, index) in imageUploadRules"
              :key="'rule_' + index"
              :value="item"
              color="primary"
              class="d-flex ga-2"
          >
              <v-icon :icon="item.icon" size="15"></v-icon>
            <p class="text-caption">{{item?.text}}</p>
          </div>
        </v-sheet>
      </v-col>
    </v-row>

  </div>
</template>

<script setup>
import {ref} from 'vue';

const props = defineProps({
  multiple: Boolean,
  imageUploadRules: {
    type: Array,
    require: false
  },
  requiredWidth: {
    type: Number
  },
  requiredHeight: {
    type: Number
  },
  maxSize: {
    type: Number
  },
  disableImageValidation:{
    type: Boolean,
    required: false,
  }
})

const { rawUploadedIsImageType, rawUploadedIsVideoType } = useMediaType();
const imagePreviews = ref([]);
const emit = defineEmits(['imageUploaded', 'imageDeleted']);

const imageSizeError = ref('');
const imageDimensionError = ref('');

const handleFileInput = async (event) => {
  imageSizeError.value = '';
  imageDimensionError.value = '';
  const imageWidth = props.requiredWidth;
  const imageHeight = props.requiredHeight;
  if(!props?.disableImageValidation && (!imageWidth || !imageHeight)){
    return;
  }
  const files = event.target.files;
  for (let i = 0; i < files.length; i++) {
    if(rawUploadedIsVideoType(files[i], 'video')){
      if((files[i]?.size/(1024*1024)) > 15){
        imageSizeError.value = "Video Size must be less than 15MB";
        return;
      }
      continue; // development phase
    }

    if(!files[i]) {
      return;
    }

    if(!props?.disableImageValidation && props.maxSize && files[i].size > props.maxSize ) {
      imageSizeError.value = "Image Size must be less than or equal to "+ props.maxSize;
      return;
    }
    else {
      imageSizeError.value = '';
    }

    const reader = new FileReader();
    await new Promise((resolve) => {
      reader.onload = (e) => {
        const image = new Image();
        image.src = e.target.result;

        image.onload = ()=>{
          if(!props?.disableImageValidation && (image.width !== imageWidth || image.height !== imageHeight) ) {
            imageDimensionError.value = 'Image Dimension must be ' + imageWidth + ' X ' + imageHeight + `but your image: w:${image.width}, h:${image.height}`;

            event.target.value = '';
          }else {
            imageDimensionError.value = '';
          }
          resolve();
        }
      }
      reader.readAsDataURL(files[i]);
    })
  }
  
  if(!imageSizeError.value && !imageDimensionError.value){
    emit('imageUploaded', files);
  }

};

const handleDrop = (event) => {
  event.preventDefault();
  const files = event.dataTransfer.files;
  // previewImages(files);
};

const openFileInput = () => {
  const fileInput = document.querySelector('input[type="file"]');
  fileInput.click();
};

// const previewImages = (files) => {
//   for (let i = 0; i < files.length; i++) {
//     const reader = new FileReader();
//     reader.onload = (e) => {
//       const imageData = {
//         url: e.target.result,
//         file: files[i],
//       };
//
//       imagePreviews.value.push(imageData);
//     };
//     reader.readAsDataURL(files[i]);
//   }
// };
const deleteImage = (index) => {
  imagePreviews.value.splice(index, 1);
  emit('imageDeleted', imagePreviews.value);
};

const checkImagesHeightWidth = ( files ) => {
  for (let i = 0; i < files.length; i++) {
    const reader = new FileReader();

    reader.onload = (e) => {
      const image = new Image();
      image.src = e.target.result;

      image.onload = () => {
        console.log(this.width)
      }

    }
  }
}

</script>

<style scoped>
.drop-area {
  border: 2px dashed #ccc;
  padding: 20px;
  text-align: center;
  margin-bottom: 20px;
}

.image-preview {
  display: flex;
  flex-wrap: wrap;
}

.preview-item {
  margin-right: 10px;
  margin-bottom: 10px;
}

.preview-item img {
  width: 100px;
  height: auto;
}

.preview-item button {
  margin-top: 5px;
}

.preview-item-trash {
  top: 5px;
  right: 5px;
}
</style>
