<template>
  <template v-if="imageUrl.length > 0">
    <!-- <v-row class="mb-2"> -->
      <!-- <v-col cols="2" v-for="obj in imageUrl" :key="'gallery_image_' + obj.id"> -->
    <draggable :itemKey="'gallery_image'" v-model="imageUrl" tag="div" @change="onImageOrderChanged" class="d-flex flex-wrap ga-2">
      <template #item="{ element }">
        <v-sheet class="pa-2 position-relative cursor-grab">
          <v-avatar @click="removeImage(element.id)" density="compact" size="15" color="error" class="cursor-pointer position-absolute"
            style="top: 0px; right: 0px;">
            <v-icon icon="mdi-close" size="10"></v-icon>
          </v-avatar>
          <v-img v-if="isImageType(element, 'image')" :src="element.imageUrl" width="100" height="12  0" class="mb-0"/>
          <video v-if="isVideoType(element, 'video')" :src="element.imageUrl" width="50" height="70" class="mb-0" muted>
            <source :src="element?.imageUrl" :type="element?.imageMimeType"/>
          </video>
        </v-sheet>
      </template>
    </draggable>
  </template>
  <div class="mb-3">
    <ModalsUploadImageModal
        @image-selected="selectedImage"
        @image-folder-change="handleImageFolderChange" 
        :multiple="multiple"
        :image-upload-rules="imageRules"
        :modal-type="imageModelType"
        :max-size="maxSize"
        :required-width="requiredWidth"
        :required-height="requiredHeight"
        :image-folders="props?.imageFolders"
    />
  </div>
</template>
<script setup lang="ts">
import {ref, watchEffect, watch} from 'vue';
import {useProductImagesStore} from "~/stores/products/product-images";
import { useMediaType } from "~/composables/useMediaType";
import { componentNames } from '#build/components';

const { isImageType, isVideoType} = useMediaType();
const imageStore = useProductImagesStore();

const props = defineProps({
  imageIds: {
    type: Array,
    required: false
  },
  imageModel: {
    type: String
  },
  imageRules: {
    type: Array,
    required: false
  },
  requiredWidth: {
    type: Number
  },
  requiredHeight: {
    type: Number
  },
  maxSize: {
    type: Number
  },
  multiple: {
    type: Boolean,
    default: false
  },
  imageFolders:{
    type: Array,
    required: false
  },
  componentExecutedObject:{
    type: Object,
    required: false
  }
});

const emit = defineEmits(['selectedImageId', 'removeImageId', 'orderImageIds'])

const imageUrl = ref([]);
const imageModelType = ref();

const getImages = (imageIds: any) => {
  imageIds?.forEach((item: any) => {
    if(item){
      imageStore.getAnyImageById(item).then((res: any) => {
        if (res) {
          const exisitngIds = imageUrl?.value?.map((el)=>el?.id);
          if(!exisitngIds?.includes(res?.id)){
            imageUrl.value.push(res);
            imageUrl.value.sort((a, b) => {
              return imageIds.indexOf(a?.id) - imageIds.indexOf(b?.id);
            });
          }
        }
      });
    }
  });
}

const selectedImage = (val: any, id: any) => {
  imageUrl.value = []
  emit( "selectedImageId", val.values, id)
}

const removeImage = (id: any) => {
  // imageUrl.value = imageUrl.value.filter((obj)=> obj.id != id);
  imageUrl.value = [];
  emit("removeImageId", id)
}

const orderingImages = () => {
  emit("orderImageIds", imageUrl?.value, props?.componentExecutedObject);
}

watch( () => props.imageIds, (newVal: any, oldVal) => {
  getImages(newVal);

}, {immediate: true})

const handleImageFolderChange = (folderSlug:String)=>{
  imageModelType.value = folderSlug;
}

const onImageOrderChanged = ()=>{
  orderingImages();
}

onMounted(()=>{
  imageModelType.value = props?.imageModel;
})


</script>