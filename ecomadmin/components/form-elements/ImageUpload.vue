<template>
  <div>
    <v-img :src="imageUrl" v-if="imageUrl" width="200" class="mb-4" />
    <v-file-input
        v-model="selectedImage"
        accept="image/*"
        :rules="imageUploadRules"
        show-size
        @change="handleImageChange"
        @click:clear="clearSelectedImage"
    >
    </v-file-input>

    <v-alert
        v-if="error"
        color="error"
        dark
        dense
    >
      {{ error }}
    </v-alert>
  </div>
</template>

<script>

export default {
  props: {
    value: {
      type: Object,
      default: null,
    },
    requiredImg: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      selectedImage: null,
      imageUrl: null,
      error: null,
    };
  },
  watch: {
    value: {
      handler: 'updateSelectedImage',
      immediate: true,
    },
  },
  methods: {
    handleImageChange() {
      const file = this.selectedImage;

      this.error = null;

      // Read and display the selected image
      const reader = new FileReader();
      reader.onload = () => {
        this.imageUrl = reader.result;
        this.$emit('input', file[0]); // Emit the selected image to the parent component using v-model
      };
      reader.readAsDataURL(file[0]);
    },
    updateSelectedImage(newValue) {
      this.selectedImage = newValue;
      this.imageUrl = newValue ? URL.createObjectURL(newValue) : null;
    },
    clearSelectedImage() {
      this.imageUrl = null;
      this.$emit('clearImage');
      this.selectedImage = null
    }
  },
  computed: {
    imageUploadRules() {
      return [
        this.requiredImg ? (value) => !!value || 'Please upload an image.' : null,

        // (value) => {
        //   if (value) {
        //     const maxSizeInBytes = 1024 * 1024; // 1 MB
        //     return value[0].size <= maxSizeInBytes || 'Image size exceeds the maximum allowed limit (1 MB).';
        //   }
        // },
        //
        // (value) => {
        //   if (value) {
        //     const imgTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/svg+xml'];
        //     return !imgTypes.includes(value[0].type) && 'Image format is wrong! (png, jpg, jpeg & svg is supported only.)';
        //   }
        // }
      ];
    },
  },
};
</script>