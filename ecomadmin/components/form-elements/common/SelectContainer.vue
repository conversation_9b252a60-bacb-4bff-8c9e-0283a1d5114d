<template>
  <v-select
      :items="props.items"
      item-title="name"
      item-value="id"
      :model-value="props.modelValue"
      :label="props.label"
      @update:modelValue="updateValue"
      :error-messages="props.errorMessage"
      :multiple="props.multiple"
  ></v-select>
</template>
<script lang="ts" setup>

interface Props {
  errorMessage?: string | undefined
  modelValue: null,
  label ?: string,
  items ?: [],
  multiple ?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  errorMessage: undefined,
  modelValue: null,
  label: '',
  multiple: false
})

const emit = defineEmits(['update:modelValue'])
const updateValue = (event: any) => {
  emit('update:modelValue', event);
}
</script>