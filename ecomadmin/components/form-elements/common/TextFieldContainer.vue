<template>
  <v-text-field
     :model-value="props.modelValue"
     :label="label"
     :error-messages="props.errorMessage"
     @input="updateValue"
  ></v-text-field>
</template>
<script lang="ts" setup>

interface Props {
  errorMessage?: string | undefined
  modelValue: null,
  label ?: string,
}

const props = withDefaults(defineProps<Props>(), {
  errorMessage: undefined,
  modelValue: null,
  label: '',
})

const emit = defineEmits(['update:modelValue'])
const updateValue = (event: any) => {
  emit('update:modelValue', event.target.value);
}

</script>