<template>
    <div>
        <Editor
            :modelValue="modelValue"
            @update:modelValue="handleUpdate"
            :init="init"
            tinymce-script-src="/js/tinymce/tinymce.min.js"
            :placeholder="placeholder"
            :name="name"
            @init="initHandler"
        ></Editor>
    </div>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import Editor from "@tinymce/tinymce-vue";
import type {
    RawEditorOptions as EditorOptions,
    Editor as TinyMCEEditor,
    Ui,
} from "tinymce";

type NestedMenuItemContents = Ui.Menu.NestedMenuItemContents;
type MenuItemSpec = Ui.Menu.MenuItemSpec;

type FilePickerCallback = (value: string, meta?: any) => void;

const props = defineProps({
    label: {
        type: String,
        default: "Content",
    },
    name: {
        type: String,
        default: "content",
    },
    required: {
        type: Boolean,
        default: false,
    },
    min: {
        type: Number,
        default: 0,
    },
    height: {
        type: Number,
        default: 200,
    },
    max: {
        type: Number,
        default: 0,
    },
    plugins: {
        type: [String, Array<string>],
        // default: "quickbars emoticons table wordcount",
        default:
            "quickbars anchor autolink charmap codesample emoticons image link lists media searchreplace visualblocks wordcount code codesample table",
    },
    toolbar: {
        type: [String, Boolean, Array<string>],
        default:
            " bold italic underline strikethrough | fontsizeselect | forecolor backcolor | alignleft aligncenter alignright alignjustify | bullist numlist | outdent indent blockquote | undo redo | axupimgs | removeformat | link image media | emoticons | code | table",
    },
    placeholders: {
        type: Array<any>,
        default: () => [],
    },
    showLoading: {
        type: Boolean,
        default: false,
    },
    placeholder: {
        type: String,
        default: "",
    },
    labelContainerClass: {
        type: String,
        default: "",
    },
    hideLabel: {
        type: Boolean,
        default: false,
    },
    modelValue: {
      type: String,
      default: '',
    },
});

const emit = defineEmits(["init", "update:modelValue"]);
const isLoaded = ref(false);

const model = ref<string>(props.modelValue);

const handleUpdate = (value: string) => {
  emit("update:modelValue", value);
};


watch(() => props.modelValue, (newValue) => {
  if (newValue !== model.value) {
    model.value = newValue;
  }
});




const initHandler = () => {
    emit("init");
};

//Tinymce setup
const setupTinymce = (editor: TinyMCEEditor) => {
    /* Placeholder tag toolbar menu button */
    if (props.placeholders && props.placeholders.length) {
        for (const placeholder of props.placeholders) {
            editor.ui.registry.addMenuButton(`${placeholder.name}`, {
                text: `${placeholder.label}`,
                fetch: (callback) => {
                    const items: NestedMenuItemContents[] =
                        placeholder?.option?.map(
                            (item): MenuItemSpec => ({
                                type: "menuitem",
                                text: item.label.toString(),
                                onAction: () =>
                                    editor.insertContent(`${item.value}`),
                            }),
                        );
                    callback(items);
                },
            });
        }
    }

    editor.on("init", () => {
        charCount.value = editor.plugins.wordcount.body.getCharacterCount();
        isLoaded.value = true;
    });

    editor.on("input", () => {
        charCount.value = editor.plugins.wordcount.body.getCharacterCount();
        emit("update:modelValue", editor.getContent());
    });

    editor.on("change", () => {
        charCount.value = editor.plugins.wordcount.body.getCharacterCount();
        emit("update:modelValue", editor.getContent());
    });
};

const handleFileInput = (event: Event): File => {
    const target = event.target as HTMLInputElement;
    return (target.files as FileList)[0];
};

// File picker cb, value, meta
const filePickerCb = (cb: FilePickerCallback) => {
    const input = document.createElement("input");
    input.setAttribute("type", "file");
    input.setAttribute("accept", "image/*");

    input.addEventListener("change", (e: Event) => {
        const file = handleFileInput(e);

        const reader = new FileReader();
        reader.addEventListener("load", () => {
            /*
          Note: Now we need to register the blob in TinyMCEs image blob
          registry. In the next release this part hopefully won't be
          necessary, as we are looking to handle it internally.
        */
            const id = "blobid" + new Date().getTime();

            const blobCache =
                window.tinymce.activeEditor.editorUpload.blobCache;

            const base64 = (reader.result as string).split(",")[1];
            const blobInfo = blobCache.create(id, file, base64);
            blobCache.add(blobInfo);

            /* call the callback and populate the Title field with the file name */
            cb(blobInfo.blobUri(), { title: file.name });
        });
        reader.readAsDataURL(file);
    });

    input.click();
};

// Handle focus editor
const handleFocusEditor = () => {
    document.addEventListener("focusin", function (e: Event) {
        if (
            (e.target as HTMLElement).closest(
                ".tox-tinymce-aux, .moxman-window, .tam-assetmanager-root",
            ) !== null
        ) {
            e.stopImmediatePropagation();
        }
    });
};

const clear = () => {
    // document.removeEventListener("focusin", handleFocusEditor);
};

const focus = () => {
    // Prevent bootstrap dialog from blocking focusin
    // document.addEventListener("focusin", handleFocusEditor);
};

// onMounted(() => {
//     // focus();
// });

// onUnmounted(() => {
//     // clear();
// });

const init: EditorOptions = reactive({
    branding: false,
    promotion: false,
    license_key: 'gpl',

    height: props.height,
    menubar: false,
    // inline:true,
    // content_css: false,
    skin: "tinymce-5",
    plugins: props.plugins,
    toolbar: props.toolbar,
    quickbars_insert_toolbar: false,
    fontsize_formats: "8pt 10pt 12pt 14pt 18pt 24pt 36pt 48pt 72pt",
    language: 'en',

//     image_class_list: [
//     { title: 'Responsive', value: 'v-img__img v-img__img--cover' }
//   ],

    // enable title field in the Image dialog
    image_title: true,
    // enable automatic uploads of images represented by blob or data URIs
    automatic_uploads: false,
    convert_urls: false,
    relative_urls: false,
    file_picker_types: "image",
    file_picker_callback: filePickerCb,
    setup: setupTinymce,
});

const charCount = ref(0);
</script>

<style lang="scss">
.tox-tinymce-aux, .tox-tinymce, .tox-dialog-wrap {
  z-index: 9999 !important;
  *{
    z-index: 999999 !important;
  }
};
</style>
