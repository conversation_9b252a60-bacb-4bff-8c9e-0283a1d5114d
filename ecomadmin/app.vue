<template>
  <NuxtLoadingIndicator/>
  <NuxtLayout>
    <NuxtPage/>
  </NuxtLayout>
  <NuxtSnackbar/>
</template>

<script setup>
import { useRolePermissionsStore } from "./stores/administration/permissions";

const permissionStore = useRolePermissionsStore();

const handleFetchPermissionsByToken = ()=>{
  permissionStore.rolesFindByToken()
    .catch((e)=>{
      console.log("Plugin: init logged token permissions: ", e);
    })
}

onMounted(()=>{
  handleFetchPermissionsByToken();
})

</script>
