import type { ThemeTypes } from '@/types/themeTypes/ThemeType';

const AQUA_THEME: ThemeTypes = {
    name: 'AQUA_THEME',
    dark: false,
    variables: {
        'border-color': '#e5eaef'
    },
    colors: {
        primary: '#5D87FF',
        secondary: '#47D7BC',
        info: '#539BFF',
        success: '#28C76F',
        accent: '#FFAB91',
        warning: '#FFAE1F',
        error: '#FA896B',
        lightprimary: '#000',
        lightsecondary: '#EDFBF7',
        lightsuccess: '#E6FFFA',
        lighterror: '#FDEDE8',
        lightinfo: '#EBF3FE',
        lightwarning: '#FEF5E5',
        textPrimary: '#2A3547',
        textSecondary: '#2A3547',
        borderColor: '#e5eaef',
        inputBorder: '#DFE5EF',
        containerBg: '#ffffff',
        background: '#f6f6f6',
        hoverColor: '#f6f9fc',
        surface: '#fff',
        'on-surface-variant': '#fff',
        grey100: '#F2F6FA',
        grey200: '#EAEFF4',
        'sidebarBg': "#081226",
        Pending: "#FACC15",
        Confirmed: "#4CAF50",
        Picked_Up: "#2D9CDB",
        On_The_Way: "#0084FF",
        Delivered: "#00C853",
        Cancelled: "#FF3B30",
        Returned: "#FF9800",
        Failed_Delivery: "#9E9E9E",
    }
};



export { AQUA_THEME };
