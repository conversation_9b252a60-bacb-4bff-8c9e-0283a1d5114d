.authentication{
    &::before{
        content: "";
        position: absolute;
        height: 100%;
        width: 100%;
        opacity: 0.3;
        left: 0;
        top: 0;
        bottom: 0;
        background: radial-gradient(rgb(210, 241, 223), rgb(211, 215, 250), rgb(186, 216, 244)) 0% 0% / 400% 400%;
    }
    .auth-header{
        position: absolute;
        top: 0;
        left: 0;    
    }   
    @media screen and (max-width:1280px){
        .auth-header{
            position: unset;
        } 
    }
}
.verification{
    .v-field__input{
        text-align: center;
    }
} 
.auth-divider{
    span{
        z-index: 1;
    }
    &::before{
        position: absolute;
        width: 100%;
        border-top: thin solid rgb(229, 234, 239);
        top: 50%;
        content: "";
        transform: translateY(50%);
        left: 0;
    }
    &::after
    {
        position: absolute;
        width: 100%;
        border-top: thin solid rgb(229, 234, 239);
        top: 50%;
        content: "";
        transform: translateY(50%);
        right: 0;
    }
}

@media (min-width: 1536px){
    .auth{
        .v-col-lg-7{
            flex: 0 0 66.66%;
            max-width: 66.66%;
        }
        .v-col-lg-5{
            flex: 0 0 33.33%;
            max-width: 33.33%;
        }
    }
   
}
@media screen and (max-width:1280px){
    .mh-100{
        height: 100% !important;
    } 
}

@media screen and (max-width:600px){
    .mw-100{
        width: 100%;
        padding: 0 15px;
    } 
}
