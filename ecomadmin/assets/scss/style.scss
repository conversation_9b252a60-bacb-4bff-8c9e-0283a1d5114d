@import url("https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:wght@400;500;600&display=swap");
@import 'variables';
@import 'vuetify/styles/main.sass';
@import './override';
@import './layout/text';
@import './layout/reboot';
@import './layout/container';
@import './layout/sidebar';
//@import './layout/rtl';
//@import './layout/topbar';
//@import './layout/horizontal';
//@import './layout/dark';
//@import './layout/customizer';

@import './components/VBreadcrumb';
@import './components/VAlert';
@import './components/VButtons';
@import './components/VCard';
@import './components/VCarousel';
@import './components/VField';
@import './components/VList';
@import './components/VInput';
@import './components/VNavigationDrawer';
@import './components/VShadow';
@import './components/VSwitch';
@import './components/VSelectionControl';
@import './components/VTextField';
@import './components/VTextarea';
@import './components/VTabs';
@import './components/VTable';
@import './components/VExpansionpanel';
@import './components/VTooltip';

/*Landing page css*/
//@import 'landingpage/header';
//@import 'landingpage/general';

//@import './pages/datatable';
//@import './pages/dashboards';
@import './pages/editor';
@import './pages/authentication';
//@import './pages/apps';

@import 'vue3-perfect-scrollbar/dist/vue3-perfect-scrollbar.css';

@import url('https://fonts.googleapis.com/css2?family=Roboto+Condensed:ital,wght@0,100..900;1,100..900&display=swap');

html {
  font-family: "Roboto Condensed", sans-serif !important;
  font-size: 20px !important;
}

.pnt-scrollbar::-webkit-scrollbar {
  width: 6px !important;
  height: 6px !important;
}
.pnt-scrollbar {
  scrollbar-color: #a0a0a0 #e0e0e0;
  scrollbar-width: thin;
}
.pnt-scrollbar::-webkit-scrollbar-track {
  background: #e0e0e0;
  border-radius: 10px;
}
.pnt-scrollbar::-webkit-scrollbar-thumb {
  background: #a0a0a0;
  border-radius: 10px;
}
.pnt-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #808080;
}