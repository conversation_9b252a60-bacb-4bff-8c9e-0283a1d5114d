@use 'sass:math';
@use 'sass:map';
@use 'sass:meta';
@use 'vuetify/styles/tools/functions' as *;

// Custom Variables
// colors
$white: #fff !default;

// cards
$card-title-size: 18px !default;

$body-font-family: 'Plus Jakarta Sans' !default;
$border-radius-root: 7px !default;
$btn-font-weight: 400 !default;
$btn-letter-spacing: 0 !default;

// Global Shadow
$box-shadow: rgba(145 158 171 / 30%) 0px 0px 2px 0px, rgba(145 158 171 / 12%) 0px 12px 24px -4px;

// Global Radius as per breakeven point

@forward 'vuetify/settings' with (
    $color-pack: false !default,
    // Global font size and border radius
    $font-size-root: 1rem,
    $border-radius-root: $border-radius-root !default,
    $body-font-family: $body-font-family,
    $heading-font-family: $body-font-family !default,
    // 👉 Typography
    $typography: ('h1': ('size': 2.25rem,
            'weight': 600,
            'line-height': 2.75rem,
            'font-family': inherit),
        'h2': ('size': 1.875rem,
            'weight': 600,
            'line-height': 2.25rem,
            'font-family': inherit),
        'h3': ('size': 1.5rem,
            'weight': 600,
            'line-height': 2rem,
            'font-family': inherit),
        'h4': ('size': 1.3125rem,
            'weight': 600,
            'line-height': 1.6rem,
            'font-family': inherit),
        'h5': ('size': 1.125rem,
            'weight': 600,
            'line-height': 1.6rem,
            'font-family': inherit),
        'h6': ('size': 1rem,
            'weight': 600,
            'line-height': 1.2rem,
            'font-family': inherit),
        'subtitle-1': ('size': 0.875rem,
            'weight': 400,
            'line-height': 1.1rem,
            'font-family': inherit),
        'subtitle-2': ('size': 0.75rem,
            'weight': 400,
            'line-height': 1rem,
            'font-family': inherit),
        'body-1': ('size': 0.875rem,
            'weight': 400,
            'font-family': inherit),
        'body-2': ('size': 0.75rem,
            'weight': 400,
            'font-family': inherit),
        'button': ('size': 0.875rem,
            'weight': 500,
            'font-family': inherit,
            'text-transform': capitalize),
        'caption': ('size': 0.75rem,
            'weight': 400,
            'font-family': inherit),
        'overline': ('size': 0.75rem,
            'weight': 500,
            'font-family': inherit,
            'text-transform': uppercase)) !default,
    
    $rounded: (0: 0,
        'sm': $border-radius-root * 0.5,
        null: $border-radius-root,
        'md': $border-radius-root * 1,
        'lg': $border-radius-root * 2,
        'xl': $border-radius-root * 6,
        'pill': 9999px,
        'circle': 50%),  
);