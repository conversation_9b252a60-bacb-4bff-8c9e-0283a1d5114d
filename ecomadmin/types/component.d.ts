interface currentUser {
    id ?: string | number | undefined | null,
    email ?: string | undefined,
    firstName ?: string | undefined,
    lastName ?: string | undefined,
    gender ?: string | null,
    token ?: string | null | undefined
}

interface StatePayload {
    url ?: string,
    id ?: number,
    name ?: string,
    short ?: string,
    code ?: string,
    countryId ?: number | null
}

interface AddEditApiItem{
    url ?: string,
    id ?: number | null | undefined,
    query ?: any,
    payload ?: any,
    itemList ?: any
}

interface PayLoadQuery {
    order ?: string,
    page ?: number,
    take ?: number
}

interface ApiResponse {
    isSuccess ?: Boolean,
    data ?: any,
    meta ?: Pagination,
    messasge ?: string,
    statusCode ?: number
}

interface Pagination {
    page ?: number,
    take ?: number,
    itemCount ?: number,
    pageCount ?: number,
    hasPreviousPage ?: Boolean,
    hasNextPage ?: Boolean
}

interface MainProduct {
    id ?: number | null | undefined,
    isActive ?: boolean | undefined,
    isPublish ?: boolean = false,
    isMultiVariantProduct ?: boolean | undefined = false,
    isComboPackProduct ?: boolean | undefined,
    name ?: null | string ,
    code ?: null | string,
    sku ?: null | string,
    description ?: null | string,
    materialCare ?: null | string,
    shippingReturn?: null | string,
    wowFactors ?: null | string,
    slug ?: null | string,
    isRefundable ?: boolean | undefined,
    brandId ?: number | null | undefined,
    categoryId ?: number | null | undefined,
    countryIds: [],
    featuredImageId ?: number | null | undefined,
    hoverImageId ?: number | null | undefined,
    sortOrder ?: number | null | undefined,
    sizeChart ?: number | null | undefined,
    tagLine1 ?: null | string,
    tagLine2 ?: null | string,
    imageGalleryIds: [],
    productPrice: [] = [],
}

interface ProductVariant {
    productId ?: number | null | undefined,
    selectedAttributes ?: SelectedAttributeValues[],
    countryWiseProductVariant ?: CountryVariantSet[],
    attributeImages ?: AttributeImageObject[]
}

interface AttributeImageObject {
    attributeValueId ?: number | null | undefined,
    attributeValueName ?: string,
    images ?: [],
}

interface SelectedAttributeValues {
    values ?: [],
    attributeId ?: number | null | undefined,
}

interface CountryVariantSet {
    countryId ?: number | null | undefined,
    productVariations ?: SizeVariation[],
    comboType ?: ComboType[]
}

interface SizeVariation {
    size ?: number | null | undefined,
    variantDetails ?: ProductVariation[]
}

interface ProductVariation {
    color ?: number | null | undefined,
    unitPrice ?: number | null | undefined,
    discountPrice ?: number | null | undefined,
    quantity ?: number | null | undefined,
    sku ?: string | null | undefined
    isActive ?: boolean | undefined,
}

interface ComboType {
    comboType ?: null | undefined,
    amount ?: number | null | undefined,
    selectedItem ?: number | null | undefined,
    isPercentage ?: boolean | undefined,
}

interface DefaultValuesAllVariant {
    countryId: number;
    productVariations ?: SizeVariation[]
}

interface VariantPriceObject {
    countryId ?: number | null | undefined,
    unitPrice ?: number,
    discountPrice ?: number,
    quantity ?: number
}

interface Country {
    id ?: number | null | undefined,
    name ?: string,
    description ?: string,
    imageGalleryId ?: number,
    currencyId ?: number,
    taxPercentage ?: number
}

interface CityPayload {
    id ?: number | null | undefined,
    name ?: string,
    short ?: string,
    code ?: string,
    stateId ?: number | null
}

interface CurrentAdminUser{
    id ?: number | null,
    firstName ?: string,
    lastName ?: string,
    gender ?: string | null,
    token ?: string | null
}

interface CommonApiResponse {
    data ?: object | null,
    isSuccess ?: boolean,
    messasge ?: string,
    statusCode ?: number
}


// # Sizechart types

interface _SizeChartDataValue {
    "cm": string,
    "in": string,
}

interface SizeChartDataValue {
    [key: string]: _SizeChartDataValue
}

interface _SizeChartData {
    name: string,
    values: SizeChartDataValue,
}

export interface SizeChartData {
    label: string,
    data: _SizeChartData,
    sizes: string[],
}