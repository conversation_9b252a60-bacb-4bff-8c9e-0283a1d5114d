
type BrandPayloadType = {
    id?: number | null | undefined,
    name?: string,
    description?: string,
    selectedImage?: File
};

type Brand = {
    id?: number | null | undefined,
    name?: string,
    description?: string,
    isActive?: boolean | string
};

type BrandStateProps =  {
    loading?: boolean,
    error?: boolean | any | string,
    brands?: Brand[] | [] | string,
    pagination: {
        page?: number | '' | string,
        take?: number,
        itemCount?: number | 0 ,
        pageCount?: number | 0,
        hasPreviousPage?: boolean,
        hasNextPage?: boolean
    },
    brandImages: []
};


export type { BrandPayloadType, Brand, BrandStateProps, }