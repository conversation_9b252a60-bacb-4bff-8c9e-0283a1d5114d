interface ProductLocalizeInput {
    name: string;
    description: string;
    materialCare: string;
    shippingReturn: string;
    wowFactors: string;
    tagLine1: string;
    metaTitle: string;
    metaDescription: string;
  }



export interface PayloadItem {
  name: string;
  cm: number;
  in: number;
}
export interface ChartImage {
  id: string | number | null,
  chartType: string;
  imageId: number | null;
}
export interface CreateSizeChartDto {
  title: string;
  note: string;
  payload: PayloadItem[];
  chartImages: ChartImage[];
}
