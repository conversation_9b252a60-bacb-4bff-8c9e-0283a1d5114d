export const useMediaType = () => {
    const isImageType = (mediaObj: any, type: string = 'image') => {
        return mediaObj?.imageMimeType?.startsWith('image/');
    }

    const isVideoType = (mediaObj: any, type: string = 'video') => {
        return mediaObj?.imageMimeType?.startsWith('video/');
    }

    const rawUploadedIsImageType = (mediaObj: any, type: string = 'video') => {
        return mediaObj?.type?.startsWith('image/');
    }

    const rawUploadedIsVideoType = (mediaObj: any, type: string = 'video') => {
        return mediaObj?.type?.startsWith('video/');
    }

    const getExtension = (media: any): string => {
        try {
            const url = media?.imageUrl || media; // allow raw string or object
            const cleanUrl = url.split(/[?#]/)[0]; // remove query/hash params
            const parts = cleanUrl.split('.');
            return parts.length > 1 ? parts.pop()?.toLowerCase() || '' : '';
        } catch {
            return '';
        }
    };
    const isImageExtension = (ext: string): boolean => {
        const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'svg'];
        return imageExts.includes(ext?.toLowerCase());
    };
    
    const isVideoExtension = (ext: string): boolean => {
        const videoExts = ['mp4', 'webm', 'mov', 'avi', 'mkv', 'flv'];
        console.log(ext.toLocaleLowerCase())
        return videoExts.includes(ext?.toLowerCase());
    };

    return {
        isImageType,
        isVideoType, 
        getExtension,
        rawUploadedIsImageType,
        rawUploadedIsVideoType,
        isImageExtension,
        isVideoExtension,
    }
}