import {CookieRef} from "#app";

export const useApiDataFetch: typeof useFetch = (request, options, key = '') => {

    const config = useRuntimeConfig();
    const user: CookieRef<CurrentAdminUser> = useCookie('PantonecloAdminUser');

    return useFetch(request, {
        baseURL: config.public.apiUrl, ...options, headers: {
            'Access-Control-Allow-Origin': '*',
            'Authorization': 'Bearer ' + user.value?.token
        },
    })
}