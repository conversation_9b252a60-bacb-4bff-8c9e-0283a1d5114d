export const PRODUCT_IMAGE_RULES = [
    {text: 'Maximum image size is 200KB', icon: 'mdi-check'},
    {text: 'Image dimension is 1800 X 2250', icon: 'mdi-check'},
    {text: 'Image type should be WEBP', icon: 'mdi-check'},
];

export const PRODUCT_SIZE_CHART_RULES = [
    {text: 'Maximum image size is 200KB', icon: 'mdi-check'},
    {text: 'Image dimension is 1700 X 1300', icon: 'mdi-check'},
    {text: 'Image type should be WEBP', icon: 'mdi-check'},
];

export const PRODUCT_SEO_META_IMAGE_RULES = [
    {text: 'Maximum image size is 200KB', icon: 'mdi-check'},
    {text: 'Image dimension is 1200 X 630', icon: 'mdi-check'},
    {text: 'Image type should be WEBP', icon: 'mdi-check'},
];


export const SLIDER_IMAGE_RULES = [
    {text: 'Maximum image size is 200KB', icon: 'mdi-check'},
    {text: 'Image dimension is 1800 X 2250', icon: 'mdi-check'},
    {text: 'Image type should be WEBP', icon: 'mdi-check'},
];

export const SLIDER_VIDEO_RULES = [
    {text: 'Maximum image size is 200KB', icon: 'mdi-check'},
    {text: 'Image dimension is 1800 X 2250', icon: 'mdi-check'},
    {text: 'Image type should be WEBP', icon: 'mdi-check'},
];