export const STATIC_MODULES = [
    'address', 'shippingAddress', 'attribute', 'attributeValue', 'brand',
    'cart', 'cart-item', 'category', 'categoryProductRelated', 'city', 'country', 'coupon', 'courier', 'courierServices', 'currency',
    'event', 'faq', 'generate-xml', 'image', 'mail', 'mail-integration', 'newsSubscription',
    'payment', 'permission', 'product', 'productMeta', 'productLocalization', 'productCollection', 'productOrder', 'productOrderDetails', 'productRefund', 'productReview', 'product-related', 'productTags',
    'quickLinks', 'roles', 'seller', 'seoMeta', 'setup', 'setupHomepage', 'homePartnerInstagram', 'stat-logs', 'state', 'subscriptionPopup',
    'user', 'userType', 'wishlist', 'product-collection', 'product-feed', 'size-chart', 'product-suggestion'
];

export const STATIC_DEFAULT_PERMISSIONS = ['create', 'readOne', 'readMany', 'update', 'softDelete', 'hardDelete'];

// Additional permissions for specific modules
export const STATIC_ADDITIONAL_PERMISSIONS: Record<string, string[]> = {
    'product': ['publishUnpublish', 'exportStockReport', 'activeDeactive'],
    'productOrder': ['changeStatus', 'restore', 'activeDeactive', 'sendInvoice', 'report'],
    'payment': ['refund', 'report'],
    'seller': ['approve', 'reject'],
    'permission': ['assign', 'generate'],
    'roles': ['assign', 'activeDeactive'],
    'user': ['activeDeactive'],
    'categoryProductRelated': ['syncSuggestion'],
    'category': ['tag'],
};

const mergedPermissions = [
    ...STATIC_DEFAULT_PERMISSIONS, 
    ...Object.entries(STATIC_ADDITIONAL_PERMISSIONS).flatMap(([module, actions]) =>
        actions.map(action => action)
    )
];

// Convert module names into an enum-like object
export const DynamicModuleEnum = Object.freeze(
    Object.fromEntries(
        STATIC_MODULES.map(m => [
            m.replace(/([a-z])([A-Z])/g, '$1_$2') // Convert camelCase to snake_case
                .replace(/[-]/g, '_') // Replace hyphens with underscores
                .toUpperCase(), // Convert to UPPER_CASE
            m
        ])
    )
);

export const DynamicPermissionEnum = Object.freeze(
    Object.fromEntries(
        mergedPermissions.map(m => [
            m.replace(/([a-z])([A-Z])/g, '$1_$2') // Convert camelCase to snake_case
                .replace(/[-]/g, '_') // Replace hyphens with underscores
                .toUpperCase(), // Convert to UPPER_CASE
            m
        ])
    )
);

export const beautifyPermissionName = (permissionName: String)=>{
    return permissionName?.replace(/([a-z])([A-Z])/g, '$1 $2')
      .replace(/[-]/g, ' ') 
      .toUpperCase();
}