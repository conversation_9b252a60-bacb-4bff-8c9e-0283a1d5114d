import {CookieRef} from "#app";

export const editPayloadItem = (items: any, mainItems: any) => {
    for (const [key, value] of Object.entries(mainItems)) {
        mainItems[key] = items[key];
        mainItems[value] = items[value];
    }
};

export const getApiItems = async (url: string, query: any, key: any = '') => {
    const {pending, data, error, refresh} = await useApiDataFetch(url, {
        query: query,
        key
    });

    if (data.value) {
        return {
            items: data?.value?.data,
            pagination: data?.value?.meta,
            loading: pending.value,
        };
    }

    if (error.value) {
        console.log(error.value)
    }
};
export const getAllApiItem = async (url: string) => {
    const {pending, data, error, refresh} = await useApiDataFetch(
        url
    );

    if (data.value) {
        return {
            item: data.value.data,
            loading: pending.value,
        };
    }

    if (error.value) {
        console.log(error.value)
        // err = error.value;
    }
};
export const getApiItem = async (url: string, id: number) => {
    const {pending, data, error, refresh} = await useApiDataFetch(
        url + "/" + id
    );

    if (data.value) {
        return {
            item: data.value.data,
            loading: pending.value,
        };
    }

    if (error.value) {
        console.log(error.value)
        // err = error.value;
    }
};

export const addEditApiItem = async (url: string, id: number | null | undefined, query: any, payload: any, itemList: any) => {

    const options: any = {
        method: id ? "put" : "post",
    }

    if (payload) {
        options.body = JSON.stringify(payload)
    }

    if (query) {
        options.query = query
    }

    const {data, pending, error, refresh}: any = await useApiDataFetch(
        url + (id ? "/" + id : ""),
        options,
    );

    if (data.value) {
        if (data?.value?.isSuccess && data?.value?.data) {
            if (id === null) {
                if (data.value.data && data.value.data.length > 0) {
                    data.value.data.forEach((item: any) => {
                        itemList.push(item);
                    });
                } else {
                    itemList.push(data.value.data);
                }
            } else {
                const objIndex = itemList.findIndex((obj: any) => obj.id == id);
                itemList[objIndex] = data.value.data;
            }
        }

        return {
            data: data?.value?.data,
            message: data?.value?.messasge,
            success: true,
        };
    }

    if (error.value) {
        return {
            message: error.value,
            success: false,
        };
    }
};

export const addEditApiItemOther = async (url: string, id: number | null | undefined, query: any, payload: any, itemList: any) => {

    const options: any = {
        method: id ? "put" : "post",
    }

    if (payload) {
        options.body = JSON.stringify(payload)
    }

    if (query) {
        options.query = query
    }

    const {data, pending, error, refresh}: any = await useApiDataFetch(
        url + (id ? "/" + id : ""),
        options,
    );

    if (data.value) {
        if (data?.value?.isSuccess && data?.value?.data) {
            if (id === null) {
                if (data.value.data && data.value.data.length > 0) {
                    data.value.data.forEach((item: any) => {
                        itemList.push(item);
                    });
                } else {
                    itemList.push(data.value.data);
                }
            } else {
                const objIndex = itemList.findIndex((obj: any) => obj.id == id);
                itemList[objIndex] = data.value.data;
            }
        }

        return {
            data: data?.value,
        };
    }

    if (error.value) {
        return {
            message: error.value,
            success: false,
        };
    }
};

export const deleteApiItem = async (url: string, id: number, dataList: any) => {
    const confirmation = confirm("Are you sure?");

    if (confirmation) {
        const {data, pending, error, refresh} = await useApiDataFetch(url + id, {
            method: "delete",
        });
        if (data?.value?.isSuccess) {
            const itemList = dataList;
            const objIndex = itemList.findIndex((obj: any) => obj.id == id);
            if (objIndex > -1) {
                itemList.splice(objIndex, 1);
            }

            return data?.value?.messasge;
        }
    }
};

export const deleteItem = async (url: string, id: number, dataList: any) => {


    if (id) {
        const {data, pending, error, refresh} = await useApiDataFetch(url + id, {
            method: "delete",
        });
        if (data?.value?.isSuccess) {
            const itemList = dataList;
            const objIndex = itemList.findIndex((obj: any) => obj.id == id);
            if (objIndex > -1) {
                itemList.splice(objIndex, 1);
            }

            return data?.value?.messasge;
        }
    }
};

export const sendDeleteRequest = async (url: string, payload: any) => {
    const options: any = {
        method: "delete",
    }

    if (payload) {
        options.body = JSON.stringify(payload)
    }

    const {data, pending, error, refresh} = await useApiDataFetch(url, options);
    if (data?.value?.isSuccess) {
        return data?.value?.messasge;
    }
};

export const deleteNewApiItem = async (url: string, id: number, dataList: any) => {
    const confirmation = confirm("Are you sure?");

    if (confirmation) {
        const {data, pending, error, refresh} = await useApiDataFetch(url + id, {
            method: "delete",
        });
        if (data?.value?.isSuccess) {
            const itemList = dataList;
            const objIndex = itemList.findIndex((obj: any) => obj.id == id);
            if (objIndex > -1) {
                itemList.splice(objIndex, 1);
            }

            return {
                data: null,
                message: data?.value?.messasge,
                success: true,
            };
        }
    }
};

export const formDataManipulation = (items: Object) => {
    const formData = new FormData();

    for (const [key, value] of Object.entries(items)) {
        if (value) {
            formData.append(key, value);
        }
    }

    return formData;
};

export const cartesian = (...a: any) =>
    a.reduce((a: any, b: any) => a.flatMap((d: any) => b.map((e: any) => [d, e].flat())));

export const arrayToTree = (arrayItems: any) => {

    const map: any = {};
    const tree: any = [];

    arrayItems.forEach((item: any) => {
        map[item.id] = {...item, children: []}
    })

    arrayItems.forEach((item: any) => {
        if (item.parentId !== null && map[item.parentId]) {
            map[item.parentId].children.push(map[item.id]);
        } else {
            tree.push(map[item.id])
        }
    })
    console.log('tree', tree)
    return tree;

}

export const flatten = (items: any, level: any) => {

    let result: any = [];
    items.forEach((item: any) => {
        result.push({...item, level});
        if (item.children && item.children.length > 0) {
            result = result.concat(flatten(item.children, level + 1));
        }
    });
    return result;
}

export const uploadImages = async (url: string, queryString: any, payload: any, dataList: any) => {

    const options: any = {
        method: "post",
    }

    if (payload) {
        options.body = payload
    }

    if (queryString) {
        options.query = queryString
    }

    const {data, pending, error, refresh}: any = await useApiDataFetch(
        url,
        options,
    );

    if (data.value) {
        if (data.value.isSuccess) {

            if (data.value.data && data.value.data.length > 0) {
                data.value.data.forEach((item: any) => {
                    dataList.push(item);
                });
            }

            return {
                data: data.value.data,
                message: data.value.messasge,
                success: true,
            };
        }
    }

    if (error.value) {
        return {
            message: error.value,
            success: false,
        };
    }
}
export const uploadVideo = async (url: string, queryString: any, payload: any, dataList: any) => {

    const options: any = {
        method: "post",
    }

    if (payload) {
        options.body = payload
    }

    if (queryString) {
        options.query = queryString
    }

    const {data, pending, error, refresh}: any = await useApiDataFetch(
        url,
        options,
    );

    if (data.value) {
        if (data.value.isSuccess) {

            if (data.value.data) {
                dataList.push(data?.value?.data);
            }

            return {
                data: data.value.data,
                message: data.value.messasge,
                success: true,
            };
        }
    }

    if (error.value) {
        return {
            message: error.value,
            success: false,
        };
    }
}
export function numberWithCommas(x: any) {
    return parseFloat(x).toFixed(2).toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

export const formatLocalePrice = (
    price: any,
    locale: string,
    currency: any
) => {
    return currency + ' ' + numberWithCommas(price)
}

export const saveOrUpdateData = async( payload: any, url: string ) => {
    const config = useRuntimeConfig();
    const user: CookieRef<CurrentAdminUser> = useCookie('PantonecloAdminUser');

    return await $fetch(config.public.apiUrl + url + (payload?.id ? '/' + payload?.id : ''), {
        method: payload?.id ? 'PUT' : 'POST',
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Authorization': 'Bearer ' + user.value?.token
        },
        body: payload
    })
}


export const formatDate = (dateString) => {
    const date = new Date(dateString);
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();
    return `${day}-${month}-${year}`;
}
  
export const formatDateTime = (dateString) => {
    if(!dateString) return "";
    const date = new Date(dateString);
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();

    let hours = date.getHours();
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    const amPm = hours >= 12 ? 'PM' : 'AM';
    hours = hours % 12 || 12;  // Convert to 12-hour format (0 becomes 12)

    return `${day}-${month}-${year} ${String(hours).padStart(2, '0')}:${minutes}:${seconds} ${amPm}`;
};

export const getDateForDatetimeLocal = (dateObj: Date)=>{
    let now = new Date();
    if(dateObj){
        now = new Date(dateObj);
    }
    now.setMinutes(now.getMinutes() - now.getTimezoneOffset()); // Adjust for timezone
    return now.toISOString().slice(0, 16);
}

export const buildParams = (params)=>{
    let queryString = "";

    for (const [key, value] of Object.entries(params)) {
      if (value !== null && value !== "" && value !== undefined) {
        if (queryString) {
          queryString += "&";
        }
        queryString += `${encodeURIComponent(key)}=${encodeURIComponent(value)}`;
      }
    }
  
    return queryString ? `?${queryString}` : "";
}

export const getCountActiveInactive = (itemList)=>{
    return itemList?.reduce((acc, item)=>{
            item?.isActive ? acc.totalActive++ : acc.totalInactive++;
            return acc;
        },
        { totalActive: 0, totalInactive: 0}
    ) ||  { totalActive: 0, totalInactive: 0};
}


export const glsLabelPrint = (orderId: string, labels?: number [], name = 'label') => {

    if(!labels?.length) return;

    // Convert binary array to Uint8Array
    const byteArray = new Uint8Array(labels);

    // Create a Blob (PDF type)
    const blob = new Blob([byteArray], { type: "application/pdf" });

    // Create a URL and open in new tab
    const url = URL.createObjectURL(blob);
    window.open(url, "_blank");

    // OR: Trigger download automatically
    const a = document.createElement("a");
    a.href = url;
    a.download = `${name}_${orderId}.pdf`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);

    // Clean up the object URL
    URL.revokeObjectURL(url);
}

export const pacticLabelPrint = (orderId: string, labels?: string) => {
    // here base64Data is like labels
    if(!labels) return;

    // Decode base64 to binary string
    const binaryString = atob(labels);
    
    // Convert binary string to Uint8Array
    const byteArray = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
        byteArray[i] = binaryString.charCodeAt(i);
    }

    // Create a Blob (PDF type)
    const blob = new Blob([byteArray], { type: "application/pdf" });

    // Create a URL and open in new tab
    const url = URL.createObjectURL(blob);
    window.open(url, "_blank");

    // OR: Trigger download automatically
    const a = document.createElement("a");
    a.href = url;
    a.download = `pactic_label_${orderId}.pdf`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);

    // Clean up the object URL
    URL.revokeObjectURL(url);
}

export const putApiItem = async (url: string, payload: any)=>{
    try{
        const cookie = useCookie("PantonecloAdminUser");
        if(!cookie?.value) return { "error": "Token is invalid" };

        const {pending, data, error, refresh} = await useApiDataFetch(url,{
            method: 'put',
            body: payload,
            headers: {
                Authorization: `${cookie?.value?.token}`,
            }
        });
        if (data.value) {
            return data.value
        }
        if (error.value) {
            return error.value.data
        }
    }
    catch(e){
        console.log(e);
    }
}

export const normalizedForm = (form: any ) => {
  const cleaned:any = {};
  for (const [key, value] of Object.entries(form)) {
    cleaned[key] = value === '' ? null : value
  }
  return cleaned
}