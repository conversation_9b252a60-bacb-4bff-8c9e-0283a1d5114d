export const REQUIRED_RULE = ( value: any ) => {
    if (value) return true;
    return 'This is a required field';
}

export const PERCENTANGE_MAX_100_RULE = ( value: any ) => {
    if (Number(value) <= 100) return true;
    return 'Must equal or less than 100';
}

export const DEPENDENT_REQUIRED_OPTIONAL = ( value: any, secondVal: any, message: string ) => {
    if ((value && secondVal) || (!value && !secondVal)) return true;

    return message || 'This is a required field';
}

export const SELECT_REQUIRED_RULE = (value: any) => {
    if (value) return true;
    return 'You must select an option'
}

export const SELECT_REQUIRED_RULE_MULTIPLE = (value: any) => {
    if (value?.length) return true;
    return 'You must select an option'
}

export const NUMBER_NON_NEGATIVE_RULE = ( value: number ) => {

}