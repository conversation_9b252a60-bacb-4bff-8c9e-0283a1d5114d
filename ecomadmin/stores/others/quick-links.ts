import {addEditApiItem, deleteApiItem, getApiItems, getAllApiItem} from "~/utils/helpers/functions";

export const useQuickLinksStore = defineStore('quickLinks', {
    state: () => ({
        loading: false,
        error: {},
        quickLinks: [],
        pagination: {
            page: 1,
            take: 10,
            itemCount: 0,
            pageCount: 0,
            hasPreviousPage: false,
            hasNextPage: false
        }
    }),

    actions: {
        async getQuickLinks() {
            this.loading = true

            await getAllApiItem('quickLinks').then((res:any) => {
                console.log('quickLinks', res);
                
                this.quickLinks = res.item;
                this.loading = false
            })
        },

        async addUpdateQuickLinks(payload: Object) {

            return await addEditApiItem( 'quickLinks', payload?.id, '', payload, this.quickLinks);

        },

        async localizeQuickLink(id: number, payload: Object) {

            return await addEditApiItem( 'quickLinks/updateLocalize', id, '', payload, this.quickLinks);
        },

        // async deleteCountry(id: number) {
        //     return await deleteApiItem( 'country/delete/', id, this.countries );
        // },

        getQuickLink(id: number) {

            return this.quickLinks.filter(qLink => parseInt(qLink.id) === id);

        }
    }
})