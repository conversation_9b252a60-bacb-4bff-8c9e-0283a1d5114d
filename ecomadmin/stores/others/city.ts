import {addEditApiItem, deleteApiItem, getApiItems} from "~/utils/helpers/functions";

export const useCityStore = defineStore('cities', {
    state: () => ({
        loading: false,
        error: {},
        cities: [] as CityPayload[],
        pagination: {
            page: 1,
            take: 1,
            itemCount: 0,
            pageCount: 0,
            hasPreviousPage: false,
            hasNextPage: false
        }
    }),

    actions: {
        async getCities(query: Object) {
            this.loading = true

            await getApiItems( 'city/findAllDataByPagination', query).then((res: any) => {
                this.cities = res.items;
                this.pagination = res.pagination
                this.loading = false
            })
        },

        async addUpdateCity(payload: CityPayload) {
            return await addEditApiItem( 'city', payload?.id, '', payload, this.cities);
        },

        async deleteCity(id: number) {
            return await deleteApiItem( 'city/delete/', id, this.cities );
        },

        getCity(id: number) {
            return this.cities.filter((city: CityPayload) => city.id === id);
        }
    }
})