import {addEditApiItem, deleteApiItem, deleteItem, formDataManipulation, getApiItems} from "~/utils/helpers/functions";

export const useCurrencyStore = defineStore('currencies', {

    state: () => ({
        loading: false,
        currencies: [],
        pagination: {
            page: 1,
            take: 1,
            itemCount: 0,
            pageCount: 0,
            hasPreviousPage: false,
            hasNextPage: false
        }
    }),

    actions: {
        async getCurrencies(order = 'ASC', page = 1, take = 10) {
            this.loading = true
            const query = {order: order, page: page, take: take};

            await getApiItems( 'currency/findAllDataByPagination', query ).then((res) => {
                this.currencies = res.items;
                this.pagination = res.pagination
                this.loading = false
            })
        },

        async addUpdateCurrency( payload: Object ) {
            // const formData = formDataManipulation(payload);
            return await addEditApiItem( 'currency', payload.id, '',payload, this.currencies );
        },

        async deleteCurrency(id: number) {
            return await deleteItem('currency/delete/', id, this.currencies);
        }

    }

})
