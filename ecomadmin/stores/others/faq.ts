import {
    addEditApiItem,
    deleteApiItem,
    deleteItem,
    getAllApiItem,
    getApiItems
} from "~/utils/helpers/functions";

export const useFaqStore = defineStore('faq', {
    state: () => ({
        loading: false,
        error: {},
        faqList: [],
        pagination: {
            page: 1,
            take: 1,
            itemCount: 0,
            pageCount: 0,
            hasPreviousPage: false,
            hasNextPage: false
        }
    }),

    actions: {
        async getValues(order: string = "ASC", page: number = 1, take: number = 10) {

            this.loading = true;
            const {pending, data: faq, error, refresh}
                = await useApiDataFetch('faq/findAllDataByPagination', {
                query: {order: order, page: page, take: take},
            })

            if (faq.value) {
                this.faqList = faq.value.data;
                this.pagination = faq.value.meta
                this.loading = pending.value;
            }

            if (error.value) {
                this.error = error.value
            }
        },


        async getAllFaq() {
            this.loading = true;
            await getAllApiItem('faq/getAllActive').then((res: any) => {
                console.log(res)
                this.faqList = res.item;
                this.loading = false
            })
        },

        async getFaq(id: number) {
            const {data: attrValue} = await useApiDataFetch('faq/' + id);
            return attrValue.value
        },

        async addUpdateValue(payload: Object) {
            return await addEditApiItem('faq', payload.id, '', payload, this.faqList);
        },

        async localizeFaq(id: number, payload: Object) {
            return await addEditApiItem('faq/updateLocalize', id, '', payload, this.faqList);
        },

        async deleteValue(id: number) {
            return await deleteItem('faq/delete/', id, this.faqList);
        },
    }
})
