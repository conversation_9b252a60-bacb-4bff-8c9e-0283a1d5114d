import {addEditApiItem, deleteApiItem, deleteItem, getAllApiItem, getApiItems} from "~/utils/helpers/functions";
export const useSubscriptionPopupStore = defineStore('subscription-popup', {
    state: (): any => ({
        loading: false,
        error: {},
        subscriptionPopup: [],
        pagination: {
            page: 1,
            take: 1,
            itemCount: 0,
            pageCount: 0,
            hasPreviousPage: false,
            hasNextPage: false
        },
        subscriptionPopImages: []
    }),

    actions: {
        async getSubscriptionPopup(order = 'ASC', page = 1, take = 10, sort = '', search = '') {
            this.loading = true;
            const query = {order: order, page: page, take: take};
            // const query = {order: order, page: page, take: take, sort: sort, globalSearch: search};
            await getApiItems( 'subscriptionPopup/findAllDataByPagination', query).then((res: any) => {
                this.subscriptionPopup = res.items;
                this.pagination = res.pagination
                this.loading = false
            })
        },
        // async getAllActiveBrands() {
        //     this.loading = true;
        //     await getAllApiItem( 'brand/getAllActiveData').then((res: any) => {
        //         this.subscriptionPopup = res.item;
        //         this.loading = false
        //     })
        // },

        async addUpdateSubscriptionPopup( payload: Object ) {
            return await addEditApiItem( 'subscriptionPopup', payload.id,'', payload, this.subscriptionPopup );
        },

        async deleteSubscriptionPop(id: number) {
           return await deleteItem('subscriptionPopup/delete/', id, this.subscriptionPopup);
        },

        async onUpdateStatus(id: number, status ?: boolean) {
            return await addEditApiItem(
                'subscriptionPopup/activeOrInactive',
                id,
                {status: status},
                '',
                this.subscriptionPopup
            );
        },

    },
    persist: true
})
