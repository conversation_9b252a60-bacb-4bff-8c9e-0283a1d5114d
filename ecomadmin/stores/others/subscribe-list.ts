import { getApiItems} from "~/utils/helpers/functions";
import {CookieRef} from "#app";
export const useSubscribeListStore = defineStore('subscribe-list', {
    state: (): any => ({
        loading: false,
        error: {},
        subscribeList: [],
        pagination: {
            page: 1,
            take: 1,
            itemCount: 0,
            pageCount: 0,
            hasPreviousPage: false,
            hasNextPage: false
        },
    }),

    actions: {
        async getSubscribeList(order = 'ASC', page = 1, take = 10) {
            this.loading = true;
            const query = {order: order, page: page, take: take};
            await getApiItems( 'newsSubscription/findAllDataByPagination', query).then((res: any) => {
                this.subscribeList = res.items;
                this.pagination = res.pagination
                this.loading = false
            })
        },

        async downloadSubscribeExcel() {
            this.loading = true;
            try {
                const config = useRuntimeConfig();
                const user: CookieRef<CurrentAdminUser> = useCookie('PantonecloAdminUser');
                const response: any = await $fetch(config.public.apiUrl + 'newsSubscription/excel/export', {
                    method: 'GET',
                    headers: {
                        'Access-Control-Allow-Origin': '*',
                        'Authorization': 'Bearer ' + user.value?.token
                    },
                })
                const url = window.URL.createObjectURL(response as Blob);
                const link = document.createElement('a');
                link.href = url;
                link.setAttribute('download', 'subscriber list.xlsx');
                document.body.appendChild(link);
                link.click();
                link.remove();
                
                this.loading = false;
              } catch (error) {
                this.loading = false;
                console.error('Error downloading the Excel file', error);
              }
        },

    },
    persist: true
})
