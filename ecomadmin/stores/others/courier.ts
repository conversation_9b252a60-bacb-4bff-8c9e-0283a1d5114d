import {
  addEditApiItem,
  deleteApiItem,
  getApiItems,
  getAllApiItem,
  deleteItem,
} from "~/utils/helpers/functions";

export const useCourierStore = defineStore("couriers", {
  state: () => ({
    loading: false,
    error: {},
    couriers: [],
    pagination: {
      page: 1,
      take: 10,
      itemCount: 0,
      pageCount: 0,
      hasPreviousPage: false,
      hasNextPage: false,
    },
  }),

  actions: {
    async getCouriers(
      order = "ASC",
      page = 1,
      take = 10,
      sort = "",
      search = ""
    ) {
      this.loading = true;
      const query = {
        order: order,
        page: page,
        take: take,
        sort: sort,
        globalSearch: search,
      };

      await getApiItems("couriers/findAllDataByPagination", query).then(
        (res: any) => {
          this.couriers = res.items;
          this.pagination = res.pagination;
          this.loading = false;
        }
      );
    },

    async getAllCourier() {
      this.loading = true;

      await getAllApiItem("couriers").then((res: any) => {
        this.couriers = res?.item || [];
        this.loading = false;
      });
    },

    async addUpdateCourier(payload: Object) {
      return await addEditApiItem(
        "couriers",
        payload?.id,
        "",
        payload,
        this.couriers
      );
    },

    async deleteCourier(id: number) {
      return await deleteItem("couriers/delete/", id, this.couriers);
    },

    getCourier(id: number) {
      return this.couriers.filter((courier) => parseInt(courier.id) === id);
    },
  },
});
