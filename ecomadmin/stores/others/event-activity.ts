import {
    addEditApiItem,
    deleteApiItem,
    formDataManipulation,
    getApiItems,
    get,
    getAllApiItem, getApiItem, deleteItem
} from "~/utils/helpers/functions";

export const useEventActivityStore = defineStore('eventActivities', {
    state: () => ({
        loading: false,
        error: {},
        eventActivities: [],
        eventActivitiesHistory: [],
        eventTaggingProducts: [],
        pagination: {
            page: 1,
            take: 1,
            itemCount: 0,
            pageCount: 0,
            hasPreviousPage: false,
            hasNextPage: false
        },
        searchProducts: []
    }),
    actions: {
        async getEventActivities(order = 'ASC', page = 1, take = 10, sort = '', search = '') {
            this.loading = true
            const query = {order: order, page: page, take: take, sort: sort, globalSearch: search};

            await getApiItems('event/findAllDataByPagination', query).then((res: any) => {
                this.eventActivities = res.items;
                this.pagination = res.pagination
                this.loading = false
            })
        },

        async getAllEventActivities() {
            this.loading = true

            await getAllApiItem('event').then((res: any) => {
                this.eventActivities = res.item;
                this.loading = false
            })
        },

        async getEventActivityHistory(order = 'ASC', page = 1, take = 10, sort = '', search = '') {
            this.loading = true
            const query = {order: order, page: page, take: take, sort: sort, globalSearch: search};

            await getApiItems('event/history/findAllDataByPagination', query).then((res: any) => {
                this.eventActivitiesHistory = res.items;
                this.pagination = res.pagination
                this.loading = false
            })
        },

        async fetchTagProductWithEventActivity(query: Object) {
            this.loading = true

            await getApiItems('event/history/taggedProducts', query).then((res: any) => {
                this.eventTaggingProducts = res.items;
                this.pagination = res.pagination
                this.loading = false
            })
        },

        async addUpdateEventActivity(payload: Object) {
            return await addEditApiItem('event', payload?.id, '', payload, this.eventActivities);
        },

        async addUpdateEventActivityHistory(payload: Object) {
            return await addEditApiItem('event/history', payload?.id, '', payload, this.eventActivitiesHistory);
        },

        async addUpdateEventActivityTagProduct(payload: Object) {
            return await addEditApiItem('event/history/taggedProducts', null, '', payload, this.eventTaggingProducts);
        },

        async deleteEventActivity(id: number) {
            return await deleteItem('event/delete/', id, this.eventActivities);
        },

        async deleteEventActivityHistory(id: number) {
            return await deleteItem( 'event/history/delete/', id, this.eventActivitiesHistory );
        },

  async changeStatusEventActivityHistory(id: number, status ?: boolean) {
    return await addEditApiItem(
      'event/history/activeOrInactive',
      id,
      {status:status},
      '',
      this.eventActivitiesHistory
    );
  },

        async removeTaggingProduct(id: number) {
            return await deleteItem( 'event/eventActivityMeta/delete/', id, this.eventTaggingProducts );
        },

        getEventActivity(id: number) {
            return this.eventActivities.filter(eventActivity => parseInt(eventActivity.id) === id);
        },

        async searchProduct( name: string, countryCode: string ) {
            const response = await getApiItems('product/web/searchByProduct', { name: name, countryCode: countryCode } );

            this.searchProducts = response.items
        }
    }
})
