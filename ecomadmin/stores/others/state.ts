import {addEditApiItem, deleteApiItem, getApiItems} from "~/utils/helpers/functions";

export const useStateStore = defineStore('states', {

    state: () => ({
        loading: false,
        states: [],
        pagination: {
            page: 1,
            take: 1,
            itemCount: 0,
            pageCount: 0,
            hasPreviousPage: false,
            hasNextPage: false
        }
    }),

    actions: {
        async getStates(query: PayLoadQuery) {
            this.loading = true

            await getApiItems( 'state/findAllDataByPagination', query ).then((res: any) => {
                this.states = res.items;
                this.pagination = res.pagination
                this.loading = false
            })
        },

        async addUpdateState( payload: any ) {
            return await addEditApiItem('state', payload.id, '', payload, this.states);
        },

        async deleteState(id: number) {
            return await deleteApiItem('state/delete/', id, this.states);
        }

    }

})