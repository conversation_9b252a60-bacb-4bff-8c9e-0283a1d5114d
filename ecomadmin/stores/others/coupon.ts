import {addEditApiItem, deleteApiItem, getApiItems, getAllApiItem, deleteItem} from "~/utils/helpers/functions";

export const useCouponStore = defineStore('coupons', {
    state: () => ({
        loading: false,
        error: {},
        coupons: [],
        couponsDwn: [],
        countryCoupons: [],
        pagination: {
            page: 1,
            take: 1,
            itemCount: 0,
            pageCount: 0,
            hasPreviousPage: false,
            hasNextPage: false
        }
    }),

    actions: {
        async getCoupons(order = 'ASC', page = 1, take = 10, sort = '', search = '') {
            this.loading = true
            const query = {order: order, page: page, take: take, sort: sort, globalSearch: search};

            await getApiItems( 'coupon/findAllDataByPagination', query).then((res:any) => {
                this.coupons = res.items;
                this.pagination = res.pagination
                this.loading = false
            })
        },

        async getAllActiveCoupons() {
            this.loading = true

            await getAllApiItem( 'coupon').then((res:any) => {
                this.couponsDwn = res.item;
                this.loading = false
            })
        },

        async fetchCouponsWithCountry(query: Object) {
            this.loading = true

            await getApiItems( 'coupon/couponMeta/findListWithCountry', query).then((res:any) => {
                this.countryCoupons = res.items;
                this.pagination = res.pagination
                this.loading = false
            })
        },


        async addUpdateCoupon(payload: Object) {

            return await addEditApiItem( 'coupon', payload?.id, '', payload, this.coupons);

        },

        async addUpdateTaggingCouponCountry(payload: Object) {
            return await addEditApiItem( 'coupon/couponMeta/createCouponCountryTagging', payload?.id, '', payload, this.countryCoupons);
        },

        async deleteCoupon(id: number) {
            return await deleteItem( 'coupon/delete/', id, this.coupons );
        },

        async deleteCouponMetaItem(id: number) {
            return await deleteApiItem( 'coupon/couponMeta/delete/', id, this.countryCoupons );
        },

        async changeStatusCoupon(id: number, status ?: boolean) {
            return await addEditApiItem(
            'coupon/activeOrInactive',
            id,
            {status:status},
            '',
            this.coupons
            );
        },

        getCoupon(id: number) {

            return this.coupons.filter(coupon => parseInt(coupon.id) === id);

        }
    }
})
