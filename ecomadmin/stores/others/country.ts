import {addEditApiItem, deleteApiItem, getApiItems, getAllApiItem, deleteItem} from "~/utils/helpers/functions";

export const useCountryStore = defineStore('countries', {
    state: () => ({
        loading: false,
        error: {},
        countries: [],
        pagination: {
            page: 1,
            take: 10,
            itemCount: 0,
            pageCount: 0,
            hasPreviousPage: false,
            hasNextPage: false
        }
    }),

    actions: {
        async getCountries(order = 'ASC', page = 1, take = 10, sort = '', search = '') {
            this.loading = true
            const query = {order: order, page: page, take: take, sort: sort, globalSearch: search};

            await getApiItems( 'country/findAllDataByPagination', query).then((res:any) => {
                this.countries = res.items;
                this.pagination = res.pagination
                this.loading = false
            })
        },


        async getAllCountries() {
            this.loading = true

            await getAllApiItem( 'country').then((res:any) => {
                this.countries = res?.item || [];
                this.loading = false
            })
        },

        async addUpdateCountry(payload: Object) {

            return await addEditApiItem( 'country', payload?.id, '', payload, this.countries);

        },

        async deleteCountry(id: number) {
            return await deleteItem( 'country/delete/', id, this.countries );
        },

        getCountry(id: number) {

            return this.countries.filter(country => parseInt(country.id) === id);

        }
    }
})
