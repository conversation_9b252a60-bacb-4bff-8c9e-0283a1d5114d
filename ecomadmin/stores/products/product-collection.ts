import {addEditApiItem, deleteApiItem, formDataManipulation, getAllApiItem, getApiItems} from "~/utils/helpers/functions";

export const useProductCollectionStore = defineStore('product-collection', {
    state: () => ({
        loading: false,
        error: {},
        collections: [],
        pagination: {
            page: 1,
            take: 1,
            itemCount: 0,
            pageCount: 0,
            hasPreviousPage: false,
            hasNextPage: false
        }
    }),

    actions: {
        async getCollections(order: string = "ASC", page: number = 1, take: number = 10, sort: '', search = '') {
            this.loading = true;
            const query = {order: order, page: page, take: take, sort: sort, globalSearch: search};
            await getApiItems('product-collection', query).then((res: any) => {
                this.collections = res.items;
                this.pagination = res.pagination;
                this.loading = false;
            });
        },
    }
})
