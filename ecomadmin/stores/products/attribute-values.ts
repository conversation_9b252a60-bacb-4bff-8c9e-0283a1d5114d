import {addEditApiItem, deleteApiItem, formDataManipulation, getAllApiItem, getApiItems} from "~/utils/helpers/functions";

export const useAttrValueStore = defineStore('attribute-values', {
    state: () => ({
        loading: false,
        error: {},
        attrValues: [],
        pagination: {
            page: 1,
            take: 1,
            itemCount: 0,
            pageCount: 0,
            hasPreviousPage: false,
            hasNextPage: false
        }
    }),

    actions: {
        // async getValues(query: Object) {
        //     this.loading = true;
        //     await getApiItems('attributeValue/findAllDataByPagination', query).then(res => {
        //         this.attrValues = res.items;
        //         this.pagination = res.pagination
        //         this.loading = false
        //     })
        // },

        async getValues(order: string = "ASC", page: number = 1, take: number = 10, sort: '', search = '') {

            this.loading = true;
            const {pending, data: categories, error, refresh}
                = await useApiDataFetch('attributeValue/findAllDataByPagination', {
                query: {order: order, page: page, take: take, sort: sort, globalSearch: search},
            })

            if (categories.value) {
                this.attrValues = categories.value.data;
                this.pagination = categories.value.meta
                this.loading = pending.value;
            }

            if (error.value) {
                this.error = error.value
            }
        },


        async getAllAttributeValues() {
            this.loading = true;
            await getAllApiItem('attributeValue/getAllActiveData').then(res => {
                this.attrValues = res.item;
                this.loading = false
            })
        },

        async getAttributeValue(id: number) {
            const {data: attrValue} = await useApiDataFetch('attributeValue/' + id);
            return attrValue.value
        },

        async addUpdateValue(payload: Object) {
            return await addEditApiItem('attributeValue', payload.id, '', payload, this.attrValues);
        },

        async localizeAttribute(id: number, payload: Object) {
            return await addEditApiItem('/attributeValue/localizeAttribute', id, '', payload, this.attrValues);
        },

        

        async deleteValue(id: number) {
            return await deleteApiItem('attributeValue/delete/', id, this.attrValues);
        }
    }
})
