import {getApiItems} from "~/utils/helpers/functions";

export const useProductCollectionItemStore = defineStore('product-collection-items', {
    state: () => ({
        loading: false,
        error: {},
        products: [],
        pagination: {
            page: 1,
            take: 1,
            itemCount: 0,
            pageCount: 0,
            hasPreviousPage: false,
            hasNextPage: false
        }
    }),

    actions: {
        async getProducts(feedId: number, order: string = "ASC", page: number = 1, take: number = 10, sort: '', search = '') {
            this.loading = true;
            const query = {order: order, page: page, take: take, sort: sort, globalSearch: search};
            await getApiItems(`product-collection/${feedId}/products`, query).then((res: any) => {
                this.products = res.items;
                this.pagination = res.pagination;
                this.loading = false;
            });
        },
    }
})
