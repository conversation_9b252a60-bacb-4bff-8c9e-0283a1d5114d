import {addEditApiItem, deleteApiItem, formDataManipulation, getAllApiItem, getApiItems} from "~/utils/helpers/functions";

export const useProductFeedStore = defineStore('product-feed', {
    state: () => ({
        loading: false,
        error: {},
        feeds: [],
        pagination: {
            page: 1,
            take: 1,
            itemCount: 0,
            pageCount: 0,
            hasPreviousPage: false,
            hasNextPage: false
        }
    }),

    actions: {
        async getFeeds(order: string = "ASC", page: number = 1, take: number = 10, sort: '', search = '') {
            this.loading = true;
            const query = {order: order, page: page, take: take, sort: sort, globalSearch: search};
            await getApiItems('product-feed', query).then((res: any) => {
                this.feeds = res.items;
                this.pagination = res.pagination;
                this.loading = false;
            });
        },
    }
})
