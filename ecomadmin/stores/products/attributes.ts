import {addEditApiItem, deleteApiItem, formDataManipulation, getApiItems} from "~/utils/helpers/functions";

export const useProductAttributesStore = defineStore('product-attributes', {
    state: () => ({
        loading: false,
        error: {},
        attributes: [],
        attributeValues : [],
        pagination: {
            page: 1,
            take: 1,
            itemCount: 0,
            pageCount: 0,
            hasPreviousPage: false,
            hasNextPage: false
        }
    }),
    actions: {
        async getAttributes( query: Object ) {

            this.loading = true

            await getApiItems( 'attribute/findAllDataByPagination', query).then(res => {
                this.attributes = res.items;
                this.pagination = res.pagination
                this.loading = false
            })
        },

        async addUpdateAttribute(payload: Object) {
            return await addEditApiItem( 'attribute', payload.id, '', payload, this.attributes );
        },

        async getAttributeValues( attributeId ?: number | undefined | null ) {

            const { data: attrValues } = await useApiDataFetch('attribute/' + attributeId);

            if( attrValues.value ) {
                this.attributeValues.push(attrValues.value.data)
            }
        },

        async deleteAttribute(id: number) {
            return await deleteApiItem('attribute/delete/', id, this.attributes);
        },

        removeFromAttrValues(id: number) {
            let index = this.attributeValues.map((item: any) => item.id).indexOf(id);
            this.attributeValues.splice(index, 1);
        }
    },

    persist: true
})