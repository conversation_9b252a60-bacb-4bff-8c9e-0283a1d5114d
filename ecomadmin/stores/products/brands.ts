import { BrandStateProps} from "~/types/admin/products/BrandTypes";
import {addEditApiItem, deleteApiItem, deleteItem,formDataManipulation, getAllApiItem, getApiItems} from "~/utils/helpers/functions";
export const useProductBrandsStore = defineStore('product-brands', {
    state: (): BrandStateProps => ({
        loading: false,
        error: {},
        brands: [],
        pagination: {
            page: 1,
            take: 1,
            itemCount: 0,
            pageCount: 0,
            hasPreviousPage: false,
            hasNextPage: false
        },
        brandImages: []
    }),

    actions: {
        async getBrands(order = 'ASC', page = 1, take = 10, sort = '', search = '') {
            this.loading = true;
            const query = {order: order, page: page, take: take, sort: sort, globalSearch: search};
            await getApiItems( 'brand/findAllDataByPagination', query).then((res: any) => {
                this.brands = res.items;
                this.pagination = res.pagination
                this.loading = false
            })
        },
        async getAllActiveBrands() {
            this.loading = true;
            await getAllApiItem( 'brand/getAllActiveData').then((res: any) => {
                this.brands = res.item;
                this.loading = false
            })
        },

        async addUpdateBrand( payload: Object ) {
            return await addEditApiItem( 'brand', payload.id,'', payload, this.brands );
        },

        async deleteBrand(id: number) {
           return await deleteItem('brand/delete/', id, this.brands);
        },

    },
    persist: true
})
