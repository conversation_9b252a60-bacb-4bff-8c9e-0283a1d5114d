import {addEditApiItem, arrayToTree, formDataManipulation, getAllApiItem, getApiItem} from "~/utils/helpers/functions";

interface Category{
    id ?: number,
    name ?: string,
    description ?: string,
    parentId ?: number,
    selectedImage?: string,
}

import { defineStore } from 'pinia';
export const useProductCategoriesStore = defineStore('product-categories', {
    state: () => ({
        loading: false,
        error: {},
        categories: [],
        parentCategories: [],
        pagination: {
            page: 1,
            take: 1,
            itemCount: 0,
            pageCount: 0,
            hasPreviousPage: false,
            hasNextPage: false
        }
    }),

    getters: {
      getCategoriesTree : (state) => arrayToTree(state.categories)
    },

    actions : {
        async getCategories(order: string = "ASC", page: number = 1, take: number = 10, sort: '', search = '') {

            this.loading = true;
            const {pending, data: categories, error, refresh}
                = await useApiDataFetch('category/findAllDataByPagination', {
                query: {order: order, page: page, take: take, sort: sort, globalSearch: search},
            })

            if (categories.value) {
                this.categories = categories.value.data;
                this.pagination = categories.value.meta
                this.loading = pending.value;
            }

            if (error.value) {
                this.error = error.value
            }
        },

        async getAllActiveCategories() {
            this.loading = true;
            await getAllApiItem( 'category/getAllAdminActiveData').then((res: any) => {
                this.categories = res.item;
                // this.categories = [];
                this.loading = false
            })
        },


        async addUpdateCategory( category: any ) {
            return await addEditApiItem( 'category', category.id, '', category, this.categories );
        },

        async localizeCategory( category: any, payload: any ) {
            return await addEditApiItem( '/category/categoryLocalize', category.id, '', payload, this.categories );
        },

        async deleteCategory(id: number) {

            // const confirmation = confirm('Are you sure?');

            if (id) {
                const {data, pending, error, refresh}
                    = await useApiDataFetch('category/delete/' + id, {
                    method: 'delete'
                });
                if (data.value.isSuccess) {
                    const itemList = this.categories;
                    const objIndex = itemList.findIndex((obj => obj.id == id));

                    if (objIndex > -1) {
                        itemList.splice(objIndex, 1)
                    }
                    console.log('data', data?.value)
                    return data?.value;
                }
            }

        },

        async getCategoryById( id: number ) {
            return await getApiItem( 'category', id );
        },

        async getParentCategories() {
            await getAllApiItem( 'category/getParentCategories').then((res: any) => {
                this.parentCategories = res?.item || [];
                this.loading = false
            })
        }
    },

    persist: true
})
