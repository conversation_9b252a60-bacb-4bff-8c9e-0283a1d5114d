import {
    addEditApiItem,
    deleteApiItem,
    deleteItem,
    formDataManipulation,
    getAllApiItem,
    getApiItems
} from "~/utils/helpers/functions";

export const useProductTagsStore = defineStore('product-tags', {
    state: () => ({
        loading: false,
        error: {},
        productTags: [],
        pagination: {
            page: 1,
            take: 1,
            itemCount: 0,
            pageCount: 0,
            hasPreviousPage: false,
            hasNextPage: false
        }
    }),

    actions: {
        async getValues(order: string = "ASC", page: number = 1, take: number = 10, sort: '', search = '') {

            this.loading = true;
            const {pending, data: tags, error, refresh}
                = await useApiDataFetch('productTags/findAllDataByPagination', {
                query: {order: order, page: page, take: take, sort: sort, globalSearch: search},
            })

            if (tags.value) {
                this.productTags = tags.value.data;
                this.pagination = tags.value.meta
                this.loading = pending.value;
            }

            if (error.value) {
                this.error = error.value
            }
        },


        async getAllProductTags() {
            this.loading = true;
            await getAllApiItem('productTags/getAllActive').then((res: any) => {
                console.log(res)
                this.productTags = res.item;
                this.loading = false
            })
        },

        async getProductTags(id: number) {
            const {data: attrValue} = await useApiDataFetch('productTags/' + id);
            return attrValue.value
        },

        async addUpdateValue(payload: Object) {
            return await addEditApiItem('productTags', payload.id, '', payload, this.productTags);
        },

        async localizeTag(id: number, payload: Object) {
            return await addEditApiItem('/productTags/updateLocalize', id, '', payload, this.productTags);
        },

        async tagWithProducts(payload: any) {
            return await addEditApiItem('productTags/tagsWithProduct', null, '', payload, this.productTags);
        },

        async deleteValue(id: number) {
            return await deleteItem('productTags/delete/', id, this.productTags);
        },
    }
})
