import {defineStore} from "pinia";
import {
    addEditApiItem, deleteApiItem,
    getApiItem,
    getApiItems,
    deleteItem,
} from "~/utils/helpers/functions";
import {CookieRef} from "#app";

const productListUrl: string = `product/findAllDataByPagination`;
const productListByCountryIdUrl: string = `product/getProductsByCountry`;
const getProductUrl: string = `product/getProductsByProductId`;
const addProductUrl: string = `product/addProduct`;
const addProductVariantUrl: string = `product/web/addProductVariant`;
const updateProductUrl: string = `product/updateProduct`;
const productPublishUrl: string = `product/publish`;
const countryWiseActiveOrInactiveUrl: string = `product/countryWiseActiveOrInactive`;


export const useProductsStore = defineStore("products", {
    state: () => ({
        loading: false,
        searchLoading: false,
        error: {},
        products: [],
        seoMeta: [],
        productObj: {},
        pagination: {
            page: 1,
            take: 10,
            itemCount: 0,
            pageCount: 0,
            hasPreviousPage: false,
            hasNextPage: false,
        },
    }),
    actions: {
        async getProducts(order: string = "ASC", page: number = 1, take: number = 10, sort: '', search = '', skuSearch = '', nameSearch = '', categoryIds="", append = false, isPublish: Boolean|any = null) {
            this.loading = true;
            const query: any = {order: order, page: page, take: take, sort: sort, globalSearch: search, skuSearch, nameSearch, categoryIds: categoryIds};
            if(isPublish){
                query['isPublish'] = isPublish
            }
            await getApiItems(productListUrl, query).then((res: any) => {

                const _data = res?.items || [];
                if(append) {
                    this.setInitialProducts(_data)
                } else {
                    this.products = _data;
                }

                this.pagination = res?.pagination;
                this.loading = false;
            });
        },

        setInitialProducts(_products) {
            if(!_products) return;

            let _data = _products || [];
            if(this.products) {
                _data = [..._data, ...this.products];
            }

            const dataMap = _data.reduce((map, obj) => {
                map.set(obj.id, obj);
                return map;
            }, new Map());

            this.products = [...dataMap.values()];
        },

        async getProduct(id: number) {
            this.loading = true;
            return getApiItem(getProductUrl, id);
        },

        async addUpdateProduct(payload: any) {
            return await addEditApiItem(
                payload?.id ? updateProductUrl : addProductUrl,
                payload.id,
                '',
                payload,
                this.products
            );
        },

        async updateProductLocalize(id: number, payload: any) {
            return await addEditApiItem(
                'product/updateLocalize',
                id,
                '',
                payload,
                this.products
            );
        },

        async addUpdateProductVariant(payload: any) {
            return await addEditApiItem(
                payload?.id ? addProductVariantUrl : addProductVariantUrl,
                payload.id,
                '',
                payload,
                this.products
            );
        },

        async publishProduct(id: number, isPublish ?: boolean) {
            return await addEditApiItem(
                productPublishUrl,
                id,
                {isPublish: isPublish},
                '',
                this.products
            );
        },

        async onCountryWiseActiveInactive(id: number,countryId: number, status: boolean) {
            return await addEditApiItem(
                countryWiseActiveOrInactiveUrl,
                id,
                {countryId:countryId, status: status},
                '',
                this.products
            );
        },

        async getCountryWiseAllProducts(query: any) {
            this.loading = true;
            await getApiItems(productListByCountryIdUrl, query).then((res: any) => {
                this.products = res.items;
                this.loading = false;
            });
        },

        async searchProduct(text: string, page = 1, order = 'ASC', take = 10) {
            this.searchLoading = true;

            await getApiItems('product/search/productList', {
                name: text,
                order,
                page,
                take
            }).then((res: any) => {
                this.products = res.items;
                this.pagination = res.pagination;
                this.searchLoading = false;
            });

        },

        async deleteProduct(productId: number) {
            return await deleteItem('product/delete/', productId, this.products);
        },

        async copyProduct(productId: number) {
            return await addEditApiItem('product/copyProduct', productId, '', '', []);
        },
        
        async saveSeoMetaData( payload: Object ) {
            return await addEditApiItem( 'seoMeta', payload?.id,'', payload, this.seoMeta );
        },
        
        // async getSeoMetaData( metaType = '', id:any) {

        //     const query = {metaType: metaType, referenceId: id};
        //     await getApiItems('seoMeta/getByKey', query).then((res: any) => {
        //         this.seoMeta = res.items;
        //         this.loading = false
        //     })
        // },
        async getSeoMetaData( metaType = '', id:any) {
            this.loading = true;
            const query = {metaType: metaType, referenceId: id};
            const user: CookieRef<CurrentAdminUser> = useCookie('PantonecloAdminUser');

            const config = useRuntimeConfig();
            return await $fetch(config.public.apiUrl + 'seoMeta/getByKey', {
                params: query,
                headers: {
                    'Access-Control-Allow-Origin': '*',
                    'Authorization': 'Bearer ' + user.value?.token
                },
            })
        },

    },

});
