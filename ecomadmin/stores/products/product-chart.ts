import {getApiItems, getAllApiItem} from "~/utils/helpers/functions";

export const useProductChartStore = defineStore('product-chart', {
    state: () => ({
        loading: false,
        error: {},
        charts: [],
        values: new Map<number, any>(),
        pagination: {
            page: 1,
            take: 1,
            itemCount: 0,
            pageCount: 0,
            hasPreviousPage: false,
            hasNextPage: false
        }
    }),

    actions: {
        async getCharts(order: string = "ASC", page: number = 1, take: number = 10, sort: '', search = '') {
            this.loading = true;
            const query = {order: order, page: page, take: take, sort: sort, globalSearch: search};
            await getApiItems('size-chart', query).then((res: any) => {
                this.charts = res.items;
                this.pagination = res.pagination;
                this.loading = false;
            });
        },

        async getChartValue(chartId: number) {
            const res = await getApiItems(`size-chart/chart-values/${chartId}`, {});

            let mapByCountryId = new Map();
            if(res?.items?.length) {
                mapByCountryId = new Map(res.items.map(item => [item.countryId, item]));
            }
            this.values = mapByCountryId;
        },

        async getAllActiveChart() {
            this.loading = true;
            await getAllApiItem( 'size-chart/getAllActiveData').then((res: any) => {
                this.charts = res.item;
                this.loading = false
            })
        },
    }
})