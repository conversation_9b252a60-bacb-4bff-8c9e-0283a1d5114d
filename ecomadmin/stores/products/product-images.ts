import {
    addEditApiItem,
    formDataManipulation, getApiItem,
    getApiItems,
} from "~/utils/helpers/functions";

export const useProductImagesStore = defineStore("product-images", {
    state: () => ({
        loading: false,
        error: {},
        images: [],
        pagination: {
            page: 1,
            take: 1,
            itemCount: 0,
            pageCount: 0,
            hasPreviousPage: false,
            hasNextPage: false,
        },
        featuredImage: null,
        hoverImage: null,
        productGalleryImages: []

    }),

    actions: {
        async getImages({order = "ASC", page = 1, take = 10}) {
            this.loading = true;
            const query = {order: order, page: page, take: take};
            await getApiItems("image/findAllDataByPagination", query).then((res) => {
                this.images = res.items;
                this.pagination = res.pagination;
                this.loading = false;
            });
        },

        async addImage(payload: File[], type: string) {
            let imageList: File[] = payload;

            this.loading = true;
            const formData = new FormData();

            for (let image of imageList) {
                formData.append("selectedImages", image);
            }

            // formData.append("imageType", type)

            return await addEditApiItem(
                "image/addImages",
                null,
                {imageType: type},
                formData,
                this.images
            );
        },

        async deleteImage() {
        },

        async getImage(id: number, type: string) {
            return await getApiItem("image", id).then((res: any) => {
                if (res) {
                    if( type === 'featured' ){
                        this.featuredImage = res.item
                    }
                    if( type === 'hover' ){
                        this.hoverImage = res.item
                    }
                }
            })
        },

        async getProductGalleryImages(productIds: any) {
            return await getApiItems('image/gallery/getImagesById', {
                ids: productIds
            }).then((res) => {
                this.productGalleryImages = res.items
            })
        },

        async getAnyImageById( imageId: number ) {
            return await getApiItem('image', imageId).then((res: any) => res.item)
        },
    },
});
