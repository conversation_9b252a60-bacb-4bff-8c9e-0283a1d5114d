import {defineStore} from "pinia";
import {
    addEditApiItem,
    getApiItem,
    getApiItems,
    deleteItem,
} from "~/utils/helpers/functions";

export const useFortunateWheelStore = defineStore("fortunate-wheel", {
    state: () => ({
        loading: false,
        searchLoading: false,
        error: {},
        rows: [],
        pagination: {
            page: 1,
            take: 10,
            itemCount: 0,
            pageCount: 0,
            hasPreviousPage: false,
            hasNextPage: false,
        },
    }),
    actions: {
        async createWheel(payload={}){
            return await addEditApiItem(
                '/wheels',
                null,
                '',
                payload,
                this.rows
            )
        },
        async fetchWheels(payload={}){
            await getApiItems(
                '/wheels',
                {}
            ).then((res: any) => {
                this.rows = res.items;
                this.loading = false;
            });
        },
        async updateWheel(payload: any){
            return await addEditApiItem(
                `/wheels`,
                payload.id,
                '',
                payload,
                this.rows
            )
        },
        async getSingleWheel(payload: any){
            return await getApiItem(
                `/wheels`,
                payload.id,
            )
        },
        async deleteWheel(payload: any){
            return await deleteItem(
                `/wheels/`,
                payload.id,
                this.rows
            )
        }
    }
});