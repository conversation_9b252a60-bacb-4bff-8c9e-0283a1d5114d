import {defineStore} from "pinia";
import {getApiItems} from "~/utils/helpers/functions";
import {CookieRef} from "#app";

const getFinancialReportUrl: string = `productOrder/report/financialReport`;
const getdemographicsReportUrl: string = `user/report/customerDemographics`;
const getpurchasesReportUrl: string = `productOrder/report/customerPurchaseHistory`;
const getBestsellingProductReportUrl: string = `productOrder/report/product/bestSelling`;
const getShowMovingInventoryReportUrl: string = `productOrder/report/product/slowMoving`;
const getVatReportUrl: string = ``;
const getSalesReportUrl: string = `productOrder/report/product/salesReport`;
const getProductPerformanceReportUrl: string = ``;
const getOrderFulfilmentReportUrl: string = ``;
const getProductReturnListUrl: string =`productRefund/report/productReturnList`;
const getAdminDashboardCardsUrl: string =`user/admin/dashboard`;


const config = useRuntimeConfig();

export const useReports = defineStore("reports", {
    state: () => ({
        loading: false,
        error: {},
        finances: [],
        demographics: [],
        purchases: [],
        best_selling_products: [],
        show_moving_inventory: [],
        sales: [],
        product_performance: [],
        return_list: [],
        cards: [],
        vat: [],
        order_fulfilment: [],
        reports: [],
        pagination: {
            page: 1,
            take: 10,
            itemCount: 0,
            pageCount: 0,
            hasPreviousPage: false,
            hasNextPage: false,
        },
    }),
    actions: {
        async getFinancialReport(order: string = "ASC", page: number = 1, take: number = 10, sort: '', search = '') {
            this.loading = true;
            const query = {order: order, page: page, take: take, sort: sort, globalSearch: search};
            await getApiItems(getFinancialReportUrl, query).then((res: any) => {
                this.finances = res.items;
                this.pagination = res.pagination;
                this.loading = false;
            });
        },
        async getCustomerDemographicsReport(order: string = "ASC", page: number = 1, take: number = 10, sort: '', search = '') {
            this.loading = true;
            const query = {order: order, page: page, take: take, sort: sort, globalSearch: search};
            await getApiItems(getdemographicsReportUrl, query).then((res: any) => {
                this.demographics = res.items;
                this.pagination = res.pagination;
                this.loading = false;
            });
        },

        async getPurchaseReport(order: string = "ASC", page: number = 1, take: number = 10, sort: '', search = '') {
            this.loading = true;
            const query = {order: order, page: page, take: take, sort: sort, globalSearch: search};
            await getApiItems(getpurchasesReportUrl, query).then((res: any) => {
                this.purchases = res.items;
                this.pagination = res.pagination;
                this.loading = false;
            });
        },

        async getBestsellingProductReport(order: string = "ASC", page: number = 1, take: number = 10, sort: '', search = '') {
            this.loading = true;
            const query = {order: order, page: page, take: take, sort: sort, globalSearch: search};
            await getApiItems(getBestsellingProductReportUrl, query).then((res: any) => {
                this.best_selling_products = res.items;
                this.pagination = res.pagination;
                this.loading = false;
            });
        },

        async getShowMovingInventoryReport(order: string = "ASC", page: number = 1, take: number = 10, sort: '', search = '') {
            this.loading = true;
            const query = {order: order, page: page, take: take, sort: sort, globalSearch: search};
            await getApiItems(getShowMovingInventoryReportUrl, query).then((res: any) => {
                this.show_moving_inventory = res.items;
                this.pagination = res.pagination;
                this.loading = false;
            });
        },

        async getVatReport(order: string = "ASC", page: number = 1, take: number = 10, sort: '', search = '') {
            this.loading = true;
            const query = {order: order, page: page, take: take, sort: sort, globalSearch: search};
            await getApiItems(getVatReportUrl, query).then((res: any) => {
                this.vat = res.items;
                this.pagination = res.pagination;
                this.loading = false;
            });
        },

        async getSalesReport(order: string = "ASC", page: number = 1, take: number = 10, sort: '', search = '') {
            this.loading = true;
            const query = {order: order, page: page, take: take, sort: sort, globalSearch: search};
            await getApiItems(getSalesReportUrl, query).then((res: any) => {
                this.sales = res.items;
                this.pagination = res.pagination;
                this.loading = false;
            });
        },

        async getProductPerformanceReport(order: string = "ASC", page: number = 1, take: number = 10, sort: '', search = '') {
            this.loading = true;
            const query = {order: order, page: page, take: take, sort: sort, globalSearch: search};
            await getApiItems(getProductPerformanceReportUrl, query).then((res: any) => {
                this.product_performance = res.items;
                this.pagination = res.pagination;
                this.loading = false;
            });
        },

        async getOrderFulfilmentReport(order: string = "ASC", page: number = 1, take: number = 10, sort: '', search = '') {
            this.loading = true;
            const query = {order: order, page: page, take: take, sort: sort, globalSearch: search};
            await getApiItems(getOrderFulfilmentReportUrl, query).then((res: any) => {
                this.order_fulfilment = res.items;
                this.pagination = res.pagination;
                this.loading = false;
            });
        },
        async getProductReturnListReport(order: string = "ASC", page: number = 1, take: number = 10, sort: '', search = '') {
            this.loading = true;
            const query = {order: order, page: page, take: take, sort: sort, globalSearch: search};
            await getApiItems(getProductReturnListUrl, query).then((res: any) => {
                this.return_list = res.items;
                this.pagination = res.pagination;
                this.loading = false;
            });
        },

        async fetchDashboardCards() {
            // this.loading = true;
            const query = {};
            await getApiItems(getAdminDashboardCardsUrl, query).then((res: any) => {
                this.cards = res?.items || [];
                this.loading = false;
            });
        },

        async downloadCustomerDemographicsReport() {
            this.downloadReportExcel('user/report/excelDownload/customerDemographics', 'Customer Demographics');
        },

        async downloadCustomerPurchaseHistoryReport() {
            this.downloadReportExcel('productOrder/report/excelDownload/customerPurchaseHistory', 'Customer Purchase History');
        },

        async downloadBestSellingProductReport() {
            this.downloadReportExcel('productOrder/report/excelDownload/bestSelling', 'Best Selling');
        },

        async downloadSalesReport() {
            this.downloadReportExcel('productOrder/report/excelDownload/product/salesReport', 'Sales Report');
        },
        async downloadSlowMovingProductReport() {
            this.downloadReportExcel('productOrder/report/excelDownload/productSlowMoving', 'Slow Moving Products');
        },

        async downloadProductReturnRateReport() {
            this.downloadReportExcel('productRefund/report/excelDownload/productReturnList', 'Product Return');
        },

        async downloadFinancialReport() {
            this.downloadReportExcel('productOrder/report/excelDownload/financialReport', 'Financial Report');
        },

        async downloadProductOrderReport(url:any) {
            this.downloadReportExcel(url || 'productOrder/report/excelDownload/product/orderReport', 'Product Order');
        },

        async downloadProductListReport(url:any) {
            this.downloadReportExcel(url || 'product/report/excelDownload/product/stockReport', 'Product List');
        },
        
        async downloadReportExcel(uri: string, reportName: string) {
            this.loading = true;
            try {
                const config = useRuntimeConfig();
                const user: CookieRef<CurrentAdminUser> = useCookie('PantonecloAdminUser');
                const response: any = await $fetch(config.public.apiUrl + `${uri}`, {
                    method: 'GET',
                    headers: {
                        'Access-Control-Allow-Origin': '*',
                        'Authorization': 'Bearer ' + user.value?.token
                    },
                })
                const url = window.URL.createObjectURL(response as Blob);
                const link = document.createElement('a');
                link.href = url;
                link.setAttribute('download', `${reportName}.xlsx`);
                document.body.appendChild(link);
                link.click();
                link.remove();
                
                this.loading = false;
              } catch (error) {
                this.loading = false;
                console.error('Error downloading the Excel file', error);
              }
        },

    }
});
