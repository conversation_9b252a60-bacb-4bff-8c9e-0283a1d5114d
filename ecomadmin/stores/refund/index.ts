import {defineStore} from "pinia";

const getRefundListUrl: string = `productRefund/findAllDataByPagination`;
const getRefundUrl: string = `productRefund`;

const config = useRuntimeConfig();

export const useRefundStore = defineStore("refund", {
    state: () => ({
        loading: false,
        error: {},
        refundList: [],
        refundDetails: {},
        pagination: {
            page: 1,
            take: 10,
            itemCount: 0,
            pageCount: 0,
            hasPreviousPage: false,
            hasNextPage: false,
        },
    }),
    actions: {
        async getRefundList(order: string = "ASC", page: number = 1, take: number = 10, sort = '', search = '') {
            this.loading = true;
            const query = {order: order, page: page, take: take, sort: sort, globalSearch: search};

            const {data, error} = await useFetch(config.public.apiUrl + getRefundListUrl, {
                query
            });

            if (data.value) {
                this.refundList = data.value?.data;
                this.pagination = data.value?.meta
                this.loading = false
            }

            if (error.value) {
                console.log(error.value)
            }
        },

        async getRefund(id: number) {
            this.loading = true;

            const {data, error} = await useFetch(config.public.apiUrl + getRefundUrl + '/' + id);

            if (data.value) {
                this.refundDetails = data.value?.data
                this.loading = false
                return toRaw(data.value)
            }

            if (error.value) {
                console.log(error.value)
            }

        },

        async updateRefundStatus(id: number, status: string) {
            this.loading = true;

            const {data, error} =
                await useFetch(config.public.apiUrl + getRefundUrl + '/refundStatus/' + id, {
                    method: "PUT",
                    query: {
                        status
                    }
                });

            if (data.value) {
                console.log(data.value)
                await this.getRefund(id)
                this.loading = false
                return data.value
            }

            if (error.value) {
                console.log(error.value)
            }
        },

    }
});
