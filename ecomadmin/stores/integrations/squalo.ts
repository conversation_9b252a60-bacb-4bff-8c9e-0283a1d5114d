import { addEditApiItem } from "~/utils/helpers/functions";

export const useSqualoStore = defineStore('squalo', {

    state: () => ({
        isSynchronizing: false,
        squaloStoreList: [],
        currentSqualoStore: null,
        totalSqualoStore: 0,
    }),
    actions: {
        squaloAuthenticateAuthUser(user: any, password: any) {
            let token = user + ":" + password;
            let hash = btoa(token);
            return `Basic ${hash}`;
        },
        calculateDelay(response: any) {
            const minutes = Number(response?.data?.min) ?? 0;
            const seconds = Number(response?.data?.sec) ?? 0;
            return (minutes * 60 + seconds) * 1000;
        },
        async fetchSqualoStores(payload: Object) {
            const { data, pending, error, refresh }: any = await useApiDataFetch(
                'mail-integration/squalo/store',
                {
                    method: "get",
                }
            );
            return data.value;
        },
        async addSqualoStore(payload: Object) {
            const { data, pending, error, refresh }: any = await useApiDataFetch(
                'mail-integration/squalo/store',
                {
                    method: "post",
                    body: payload
                }
            );
            return data.value;
        },
        async updateSqualoStore(payload: Object) {
            const { data, pending, error, refresh }: any = await useApiDataFetch(
                `mail-integration/squalo/store/${payload?.id}`,
                {
                    method: "patch",
                    body: payload
                }
            );
            return data.value;
        },
        async deleteSqualoStore(payload: Object) {
            const { data, pending, error, refresh }: any = await useApiDataFetch(
                `mail-integration/squalo/store/${payload?.id}`,
                {
                    method: "delete",
                    body: payload
                }
            );
            return data.value;
        },
        async getCategorySqualoStore(payload: Object) {
            const { data, pending, error, refresh }: any = await useApiDataFetch(
                `/mail-integration/squalo/category/findCategorySync/${payload?.storeId}/${payload?.countryId}`,
                {
                    method: "get"
                }
            );
            return data.value;
        },
        async deleteCategorySqualoStore(payload: Object) {
            const { data, pending, error, refresh }: any = await useApiDataFetch(
                `/mail-integration/squalo/category/findCategorySync/${payload?.storeId}/${payload?.countryId}`,
                {
                    method: "delete"
                }
            );
            return data.value;
        },
        async getProductSqualoStore(payload: Object) {
            const { data, pending, error, refresh }: any = await useApiDataFetch(
                `/mail-integration/squalo/product/findProductSync/${payload?.storeId}/${payload?.countryId}/${payload?.languageId}`,
                {
                    method: "get"
                }
            );
            return data.value;
        },
        async deleteProductSqualoStore(payload: Object) {
            const { data, pending, error, refresh }: any = await useApiDataFetch(
                `/mail-integration/squalo/product/findProductSync/${payload?.storeId}/${payload?.countryId}/${payload?.languageId}`,
                {
                    method: "delete"
                }
            );
            return data.value;
        },
        async getCustomerSqualoStore(payload: Object) {
            const { data, pending, error, refresh }: any = await useApiDataFetch(
                `/mail-integration/squalo/customer/findCustomerSync/${payload?.storeId}/${payload?.countryId}`,
                {
                    method: "get"
                }
            );
            return data.value;
        },
        async deleteCustomerSqualoStore(payload: Object) {
            const { data, pending, error, refresh }: any = await useApiDataFetch(
                `/mail-integration/squalo/customer/findCustomerSync/${payload?.storeId}/${payload?.countryId}`,
                {
                    method: "delete"
                }
            );
            return data.value;
        },
        async getCartSqualoStore(payload: Object) {
            const { data, pending, error, refresh }: any = await useApiDataFetch(
                `/mail-integration/squalo/cart/findCartSync/${payload?.storeId}/${payload?.countryId}`,
                {
                    method: "get"
                }
            );
            return data.value;
        },
        async deleteCartSqualoStore(payload: Object) {
            const { data, pending, error, refresh }: any = await useApiDataFetch(
                `/mail-integration/squalo/cart/findCartSync/${payload?.storeId}/${payload?.countryId}`,
                {
                    method: "delete"
                }
            );
            return data.value;
        },
        async getOrderSqualoStore(payload: Object) {
            const { data, pending, error, refresh }: any = await useApiDataFetch(
                `/mail-integration/squalo/order/findOrderSync/${payload?.storeId}/${payload?.countryId}`,
                {
                    method: "get"
                }
            );
            return data.value;
        },
        async deleteOrderSqualoStore(payload: Object) {
            const { data, pending, error, refresh }: any = await useApiDataFetch(
                `/mail-integration/squalo/order/findOrderSync/${payload?.storeId}/${payload?.countryId}`,
                {
                    method: "delete"
                }
            );
            return data.value;
        },
        async addSingleCartSqualoStore(payload: Object) {
            try {
                const storeId = payload?.storeId;
                const countryId = payload?.countryId;
                delete payload?.storeId;
                delete payload?.countryId;
                const { data, pending, error, refresh }: any = await useApiDataFetch(
                    `/mail-integration/squalo/single/cart/findCartSync/${storeId}/${countryId}`,
                    {
                        method: "post",
                        body: payload,
                    }
                );
                return data.value;
            }
            catch (e) {
                console.log("addSingleOrderSqualoStore failed: ", e);
            }
        },
        async addSingleOrderSqualoStore(payload: Object) {
            try {
                const storeId = payload?.storeId;
                const countryId = payload?.countryId;
                delete payload?.storeId;
                delete payload?.countryId;
                const { data, pending, error, refresh }: any = await useApiDataFetch(
                    `/mail-integration/squalo/single/order/findOrderSync/${storeId}/${countryId}`,
                    {
                        method: "post",
                        body: payload,
                    }
                );
                return data.value;
            }
            catch (e) {
                console.log("addSingleOrderSqualoStore failed: ", e);
            }
        },
        async fetchOrdersSqualo(payload: Object) {
            // Squalo self API
            try {
                const config = useRuntimeConfig();
                const storeId = payload?.storeId;
                const orderId = payload?.orderId;
                return new Promise((resolve, reject) => {
                    $fetch(`mc/v3/ecommerce/stores/${storeId}/orders/${orderId}`, {
                        method: 'get',
                        baseURL: config?.public?.squaloApiUrl,
                        headers: {
                            Authorization: this.squaloAuthenticateAuthUser(config?.public?.squaloMailApiKey, config?.public?.squaloApiKey)
                        }
                    }).then((response) => {
                        resolve(response);
                    }).catch((e) => {
                        reject(e);
                    })
                })
            } catch (e) {
                console.log(e);
            }
        }
    }
})