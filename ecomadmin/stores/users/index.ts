import {defineStore} from "pinia";
import {
    addEditApiItem, addEditApiItemOther,
    getApiItem,
    getApiItems,
} from "~/utils/helpers/functions";

const userListUrl: string = `user/userListByPagination`;


export const useUserStore = defineStore("users", {
    state: () => ({
        loading: false,
        searchLoading: false,
        error: {},
        users: [],
        userTypes: [],
        userObj: {},
        pagination: {
            page: 1,
            take: 10,
            itemCount: 0,
            pageCount: 0,
            hasPreviousPage: false,
            hasNextPage: false,
        },
    }),
    actions: {
        async getUsers(order: string = "ASC", page: number = 1, take: number = 10, search='') {
            this.loading = true;
            const query = {order: order, page: page, take: take, globalSearch:search};
            await getApiItems(userListUrl, query).then((res: any) => {
                this.users = res.items;
                this.pagination = res.pagination;
                this.loading = false;
            });
        },

        async createUser(payload: any) {

            return await addEditApiItemOther(
                "/auth/admin/register",
                null,
                null,
                payload,
                []
            );
        },
        async changePassword(payload: any) {

            return await addEditApiItemOther(
                "/auth/admin/changePassword",
                payload.id,
                null,
                payload,
                []
            );
        },
        async getUserTypes() {
            await getApiItems('/userType').then((res: any) => {

                this.userTypes = res.items;
            })
        },
        async updateUserProfile(payload={}) {
            try{
                const cookie = useCookie("PantonecloAdminUser");
                if(!cookie?.value) return { "error": "Token is invalid" };

                const { data, pending, error, refresh }: any = await useApiDataFetch(
                    `user/updateUserProfile/${payload?.id}`, {
                        method: "put",
                        body: payload,
                        headers: {
                            Authorization: `${cookie?.value?.token}`,
                        }
                    }
                )
                return data?.value;
            }
            catch(error){
                return { "error": error };
                
            }
        },
        async deleteUser(payload={}) {

            try{
                const cookie = useCookie("PantonecloAdminUser");
                if(!cookie?.value) return { "error": "Token is invalid" };

                let id = payload?.id;
                const {data, pending, error} = await useApiDataFetch(`user/delete/${id}`, {
                    method: 'delete',
                    headers: {
                        Authorization: `${cookie?.value?.token}`,
                    }
                });
                if (data.value) {
                    return data.value
                }
                if (error.value) {
                    return error.value.data
                }
            }
            catch(error){
                return { "error": error };
            }
        },
    },

});
