import { buildParams } from "~/utils/helpers/functions";

export const useRolePermissionsStore = defineStore('rolePermissions', {

    state: () => ({
        modules: [],
        defaultPermissions: [],
        additionalPermissions: [],
        roles: [],
        loggedUserPersmissions: [] as String[],
    }),
    getters: {
        getModules(state) {
            return state.modules;
        },
        getDefaultPermissions(state) {
            return state.defaultPermissions;
        },
        getAdditionalPermissions(state) {
            return state.additionalPermissions;
        },
        getRoles(state) {
            return state.roles;
        }
    },
    actions: {
        async fetchModulePermissions(payload: Object) {
            try {
                const { data, pending, error, refresh }: any = await useApiDataFetch(
                    `permissions/generate/module-permissions`,
                );
                if (data?.value?.isSuccess) {
                    this.modules = data?.value?.data?.data?.modules;
                    this.defaultPermissions = data?.value?.data?.data?.defaultPermissions;
                    this.additionalPermissions = data?.value?.data?.data?.additionalPermissions;
                }
                return data.value;
            }
            catch (error) {
                return { "error": error };
            }
        },
        async updatePermission(payload = {}) {
            try {
                const { data, pending, error, refresh }: any = await useApiDataFetch(
                    `permissions`, { method: "patch", body: payload }
                );
                return data.value;
            }
            catch (error) {
                return { "error": error };
            }
        },
        async updatePermissionByRole(payload = {}) {
            try {
                const { data, pending, error, refresh }: any = await useApiDataFetch(
                    `permissions/assign/by/role`, { method: "patch", body: payload }
                );
                return data.value;
            }
            catch (error) {
                return { "error": error };
            }
        },
        async fetchRoles(params = {}) {
            try {
                const { data, pending, error, refresh }: any = await useApiDataFetch(
                    `roles/findAllDataByPagination${buildParams(params)}`,
                );
                if (data?.value?.isSuccess) {
                    // this.roles = data?.value?.data;
                }
                return data.value;
            }
            catch (error) {
                return { "error": error };
            }
        },
        async fetchSingleRole(params = {}) {
            try {
                const { data, pending, error, refresh }: any = await useApiDataFetch(
                    `roles/${params?.id}`,
                );
                return data.value;
            }
            catch (error) {
                return { "error": error };
            }
        },
        async createRole(payload = {}) {
            try {
                const { data, pending, error, refresh }: any = await useApiDataFetch(
                    `roles`, { method: "post", body: payload }
                );
                return data.value;
            }
            catch (error) {
                return { "error": error };
            }
        },
        async updateRole(payload = {}) {
            try {
                let id = payload?.id;
                delete payload.id;
                const { data, pending, error, refresh }: any = await useApiDataFetch(
                    `roles/${id}`, { method: "patch", body: payload }
                );
                return data.value;
            }
            catch (error) {
                return { "error": error };
            }
        },
        async deleteRole(payload = {}) {
            try {
                let id = payload?.id;
                const { data, pending, error, refresh }: any = await useApiDataFetch(
                    `roles/${id}`, { method: "delete" }
                );
                return data.value;
            }
            catch (error) {
                return { "error": error };
            }
        },
        async activeInactiveRole(params = {}) {
            try {
                const { data, pending, error, refresh }: any = await useApiDataFetch(
                    `roles/activeOrInactive/${buildParams(params)}`, { method: "post" }
                );
                return data.value;
            }
            catch (error) {
                return { "error": error };
            }
        },
        async assignRoleUser(payload = {}) {
            try {
                const { data, pending, error, refresh }: any = await useApiDataFetch(
                    `roles/assignRole/${payload?.roleId}/toUser/${payload?.userId}`, { method: "put" }
                );
                return data.value;
            }
            catch (error) {
                return { "error": error };
            }
        },
        async rolesFindByToken(payload = {}) {
            try {
                const authStore = useAuthStore();
                const cookie = useCookie("PantonecloAdminUser");
                if (cookie?.value) {
                    const { data, pending, error, refresh }: any = await useApiDataFetch(
                        `roles/find/by/token`, {
                            method: "get",
                            headers: {
                                Authorization: `${cookie?.value?.token}`,
                            }
                        }
                    );
                    if(error?.value){
                        authStore.logUserOut();
                        return;
                    }
                    if (data?.value?.isSuccess) {
                        if (data?.value?.data?.permissions) {
                            this.loggedUserPersmissions = data?.value?.data?.permissions?.map((item: any) => `${item?.module}:${item?.action}`);
                        }
                    }
                }
            }
            catch (error) {
                return { "error": error };
            }
        },
        hasModulePermission(permissionKey: String) {
            if (!permissionKey) return false;
            
            return this.loggedUserPersmissions.includes(permissionKey);
        }
    },
    persist: true
})