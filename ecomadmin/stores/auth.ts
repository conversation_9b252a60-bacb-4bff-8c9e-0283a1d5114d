interface UserPayloadInterface {
    email ?: string,
    password ?: string,
    userTypeId ?: number
}

interface State {
    authenticated: Boolean,
    loading: Boolean,
    currentUser ?: CurrentAdminUser
}

export const useAuthStore = defineStore('pantonecloAdminAuth', {

    state: (): State => ({
        authenticated: false,
        loading: true,
    }),

    actions: {
        async authenticateUser({email, password, userTypeId}: UserPayloadInterface) {

            const config = useRuntimeConfig();

            const {data,pending, error} =
                await useFetch<CommonApiResponse>(config.public.authApiUrl + 'auth/login', {
                method: 'post',
                headers: {'Content-Type': 'application/json'},
                body: {
                    email,
                    password,
                    userTypeId
                },
            });
            this.loading = pending.value;

            if (data.value) {
                const pantonecloAdminUser: any = useCookie<CurrentAdminUser>('PantonecloAdminUser', {
                    maxAge: 60 * 60 * 24
                });
                pantonecloAdminUser.value = data.value?.data
                this.authenticated = true;

                return data.value
            }

            if (error.value) {
                return error.value.data
            }
        },

        logUserOut() {
            const pantonecloAdminUser: any = useCookie<CurrentAdminUser>('PantonecloAdminUser');
            this.authenticated = false;
            pantonecloAdminUser.value = null;
            localStorage.removeItem("rolePermissions");
        },
    },

})

// make sure to pass the right store definition, `useAuth` in this case.
if (import.meta.hot) {
    import.meta.hot.accept(acceptHMRUpdate(useAuthStore, import.meta.hot))
}