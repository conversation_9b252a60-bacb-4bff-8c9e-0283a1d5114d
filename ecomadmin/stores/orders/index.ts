import {CookieRef} from "#app";
import { defineStore } from "pinia";
import { addEditApiItem, getApiItems } from "~/utils/helpers/functions";

import jsPDF from "jspdf";
import html2canvas from "html2canvas";
import type { CurrentAdminUser } from "~/types/component";

const getOrderListUrl: string = `productOrder/findAllDataByPagination`;
const getOrderUrl: string = `productOrder`;

const config = useRuntimeConfig();

export const useOrdersStore = defineStore("orders", {
  state: () => ({
    loading: false,
    error: {},
    orderList: [],
    courierServices: [],
    redxAreas: [],
    redxLocation: [],
    productObj: {},
    orderDetails: {},
    serviceInfo: {},
    pagination: {
      page: 1,
      take: 10,
      itemCount: 0,
      pageCount: 0,
      hasPreviousPage: false,
      hasNextPage: false,
    },
  }),
  actions: {
    async getOrders(
      order: string = "ASC",
      page: number = 1,
      take: number = 10,
      sort: "",
      search = "",
      deliveryStatus = "",
      countrySearch = "",
      nameSearch = "",
      phoneSearch = "", 
      paymentSearch = "",
      startDate="",
      endDate="",
    ) {
      this.loading = true;
      const query = {
        order: order,
        page: page,
        take: take,
        sort: sort,
        globalSearch: search,
        deliveryStatus: deliveryStatus,
        countrySearch,
        nameSearch,
        phoneSearch, 
        paymentSearch,
        startDate,
        endDate,
        };
      await getApiItems(getOrderListUrl, query).then((res: any) => {
        this.orderList = res.items;
        this.pagination = res.pagination;
        this.loading = false;
      });
    },

    async getOrder(id: number) {
      this.loading = true;
      const user: CookieRef<CurrentAdminUser> = useCookie('PantonecloAdminUser');
      const { data, error } = await useFetch(
        config.public.apiUrl + getOrderUrl + "/" + id,
        {
          headers:{
            'Authorization': 'Bearer ' + user.value?.token
          }
        }
      );

      if (data.value) {
        this.orderDetails = data.value?.data;
        this.loading = false;
        return toRaw(data.value);
      }

      if (error.value) {
        console.log(error.value);
      }
    },
    async updateDeliveryStatus(invoiceNo: any, orderStatus: any, courier: any, remarks?: any, trackingId?: any) {
      this.loading = true;
      const user: CookieRef<CurrentAdminUser> = useCookie('PantonecloAdminUser');

      const { data, error } = await useFetch(
        config.public.apiUrl + "productOrder/web/updateOrderStatus",
        {
          method: "POST",
          query: {
            invoiceNo,
            orderStatus,
            courier,
            remarks,
            trackingId
          },
          headers:{
            'Authorization': 'Bearer ' + user.value?.token
          }
        }
      );

      if (data.value) {
        this.loading = false;
        return data.value;
      }

      if (error.value) {
        console.log(error.value);
      }
    },
    async updatePaymentStatus(invoiceNo: any, paymentStatus: any) {
      this.loading = true;
      const user: CookieRef<CurrentAdminUser> = useCookie('PantonecloAdminUser');

      const { data, error } = await useFetch(
        config.public.apiUrl + "productOrder/web/updateOrderStatus",
        {
          method: "POST",
          query: {
            invoiceNo,
            paymentStatus,
          },
          headers:{
            'Authorization': 'Bearer ' + user.value?.token
          }
        }
      );

      if (data.value) {
        this.loading = false;
        return data.value;
      }

      if (error.value) {
        console.log(error.value);
      }
    },

    async downloadOrderInvoice(invoiceNo: any, country: any=null) {
      this.loading = true;
      const user: CookieRef<CurrentAdminUser> = useCookie('PantonecloAdminUser');

      // const { data, error } = await useFetch(
      //   config.public.apiUrl +
      //     `productOrder/web/downloadInvoice?invoiceNo=${invoiceNo}`,
      //   {
      //     method: "GET",
      //     headers:{
      //       'Authorization': 'Bearer ' + user.value?.token
      //     }
      //   }
      // );
      
      // try {
      //   if (data?.value) {
      //     const element = document.createElement("div");

      //     element.style.position = "absolute";
      //     element.style.left = "-9999px";
      //     element.innerHTML = data?.value?.data;
      //     document.body.appendChild(element);
      //     const canvas = await html2canvas(element);

      //     const imgData = canvas.toDataURL("image/png", 0.5);
      //     const pdf = new jsPDF({
      //       compress: true,
      //     });

      //     // Get canvas dimensions
      //     const imgWidth = 200; // A4 size width in mm
      //     const imgHeight = (canvas.height * imgWidth) / canvas.width; // Maintain aspect ratio

      //     // Add canvas image to pdf
      //     pdf.addImage(imgData, "JPEG", 5, 5, imgWidth, imgHeight);
      //     pdf.save(`invoice_${invoiceNo}.pdf`);

      //     this.loading = false;
      //     document.body.removeChild(element);
      //   }

      //   if (error.value) {
      //     console.log(error.value);
      //   }
      // } catch (error) {
      //   console.error("Error generating PDF:", error);
      //   this.loading = false;
      // }

      try{
        const { data, error } = await useFetch(
          config.public.apiUrl +
            `productOrder/web/stream-invoice/${invoiceNo}`,
          {
            method: "GET",
            headers:{
              'Authorization': 'Bearer ' + user.value?.token
            }
          }
        );
        if (error?.value) {
          console.error("API Error:", error.value);
          this.loading = false;
        } else if (data?.value) {
          const blob = new Blob([data?.value], { type: "application/pdf" });
          const pdfUrl = URL.createObjectURL(blob);
          
          // download via anchor tag
          const link = document.createElement("a");
          link.href = pdfUrl;
          let fileName = `invoice_${invoiceNo}.pdf`;
          if(country?.code){
            fileName = `${country?.code}_invoice_${invoiceNo}.pdf`;
          }
          link.download = fileName;
          document.body.appendChild(link);
          link.click();

          // clean up this link
          document.body.removeChild(link);
          URL.revokeObjectURL(pdfUrl);

          this.loading = false;
        }
      }
      catch (error) {
        console.error("Error generating PDF:", error);
        this.loading = false;
      }
    },

    async manuallySendInvoiceToCustomer(invoiceNo: any) {
      this.loading = true;
      const user: CookieRef<CurrentAdminUser> = useCookie('PantonecloAdminUser');

      const { data, error } = await useFetch(
        config.public.apiUrl + "productOrder/sendInvoice/customer",
        {
          method: "POST",
          query: {
            invoiceNo,
          },
          headers:{
            'Authorization': 'Bearer ' + user.value?.token
          }
        }
      );

      if (data.value) {
        this.loading = false;
        return data.value;
      }

      if (error.value) {
        console.log(error.value);
      }
    },

    async getCourierService() {
      await getApiItems("couriers").then((res: any) => {
        this.courierServices = res.items;
      });
    },
    async getRedxArea() {
      await getApiItems("/courier/redx/areas").then((res: any) => {
        this.redxAreas = res.items.areas;
      });
    },
    async getRedxLocation() {
      await getApiItems("/courier/redx/pickup-stores").then((res: any) => {
        this.redxLocation = res.items.pickup_stores?.map(item => ({
          label: `${item.name} : ${item.address}`,
          ...item,
        }));
      });
    },

    async createRedXParcel(payload: any) {
      return await addEditApiItem(
        "/courier/redx/create-parcel",
        null,
        null,
        payload,
        []
      );
    },

    async createGSLParcel(payload: any) {
      return await addEditApiItem(
        "/courier/parcel/pickup-request", 
        null,
        null,
        payload,
        []
      );
    },
    async createGSLParcelReturnLabel(payload: any) {
      return await addEditApiItem(
        "/courier/parcel/gls-return-label-request", 
        null,
        null,
        payload,
        []
      );
    },

    async createPacketaParcel(payload: any) {
      return await addEditApiItem(
        "/courier/packeta/create-parcel",
        null,
        null,
        payload,
        []
      );
    },
    async createPacketaHomeDelivery(payload: any) {
      return await addEditApiItem(
        "/courier/packeta/home-delivery",
        null,
        null,
        payload,
        []
      );
    },
    async createPacticParcel(payload: any) {
      return await addEditApiItem(
        "/courier/pactic/create-parcel",
        null,
        null,
        payload,
        []
      );
    },

    async createPacticParcelManual(payload: any) {
      return await addEditApiItem(
        "/courier/pactic/create-parcel-manual",
        null,
        null,
        payload,
        []
      );
    },

    async getPacticParcelServiceList(payload: any) {
      return await addEditApiItem(
        "/courier/pactic/get-parcel-services",
        null,
        null,
        payload,
        []
      );
    },

    async createPacticParcelLabel(payload: any) {
      try {
        return await addEditApiItem(
          `/courier/pactic/get-label-by/${payload?.trackingNumber}/${payload?.countryCode}/${payload?.lang}`,
          null,
          null,
          {},
          []
        )
      }catch(error){
        console.log(error);
      }
    },

    async getPacketaArea(payload: any) {
      try {
        return await getApiItems(
        `/courier/packeta/list/by/${payload?.countryCode}`,
        )
      }catch(error){
        console.log(error);
      }
    },

    async getFanService() {
      try {
        return await getApiItems(
        `/courier/fan-courier-services`,
        )
      }catch(error){
        console.log(error);
      }
    },
    async createFanShippment(orderId: any, payload: any) {
      try {
        return await addEditApiItem(
          `/courier/fan-courier-create-shipment/${orderId}`,
          null,
          null,
          payload,
          []
        )
      }catch(error){
        console.log(error);
      }
    },

    async getServiceInfo(orderId: any, serviceId: any) {
      await getApiItems(
        "/courier/info?orderId=" + orderId + "&serviceProvider=" + serviceId
      ).then((res: any) => {
        this.serviceInfo = res.items;
      });
    },

    async manuallyUpdateCourierInfo(payload: Object) {
      // return await addEditApiItem( '/courier/manually-update-parcel', payload.id, '', payload, null );
      const user: CookieRef<CurrentAdminUser> = useCookie('PantonecloAdminUser');

      const { data, error } = await useFetch(
        config.public.apiUrl + `courier/manually-update-parcel/${payload.id}`,
        {
          method: "PUT",
          query: {
            serviceId: payload?.serviceId,
            trackingId: payload?.trackingId,
          },
          headers:{
            'Authorization': 'Bearer ' + user.value?.token
          }
        }
      );

      if (data.value) {
        this.loading = false;
        return data.value;
      }

      if (error.value) {
        console.log(error.value);
      }
    },

    async partialCancelOrder(orderId: number, productOrderDetailIds: number[]) {
      this.loading = true;
      const user: CookieRef<CurrentAdminUser> = useCookie('PantonecloAdminUser');
      const { data, error } = await useFetch(
        config.public.apiUrl + 'productOrder/partial-cancel-with-credit-note/' + orderId,
        {
          method: 'POST',
          body: {productOrderDetailIds },
          headers: {
            'Authorization': 'Bearer ' + user.value?.token,
          },
        }
      );
      this.loading = false;
      if (data.value) {
        return data.value;
      }
      if (error.value) {
        console.log(error.value);
      }
    }
  },
});
