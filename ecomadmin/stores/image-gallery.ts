import { deleteApiItem, getApiItems, uploadImages, uploadVideo} from "~/utils/helpers/functions";

export const useImageGalleryStore = defineStore("image-gallery", {
    state:() => ({
        loading: false,
        galleries: [],
        meta: {}
    }),

    actions: {
        async getImages(imageType: any, order = 'ASC', page = 1, take= 18, globalSearch = '') {

            await getApiItems('image/imageType/findAllDataByPagination', {
                imageType: imageType,
                order: order,
                page: page,
                take: take,
                globalSearch,
            }, 'images_' + imageType + '_page_' + page + '_' + globalSearch).then((response: any) => {
                this.galleries = response.items;
                this.meta = response.pagination;
            })

        },

        async addImage(payload: File[], type: string) {
            let imageList: File[] = payload;

            this.loading = true;
            const formData = new FormData();

            for (let image of imageList) {
                formData.append("selectedImages", image);
            }

            return await uploadImages("image/addImages", {imageType: type}, formData, this.galleries)
        },

        async removeImage( imageId: number ) {
            return await deleteApiItem("image/delete/", imageId, this.galleries)
        },

        async addVideo(payload: File[], type: string) {
            let imageList: File[] = payload;

            this.loading = true;
            const formData = new FormData();

            for (let image of imageList) {
                formData.append("videoFile", image);
            }

            return await uploadVideo("image/addVideo", {fileDirectory: type}, formData, this.galleries)
        },
    }
})