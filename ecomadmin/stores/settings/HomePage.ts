import {addEditApiItem, deleteApiItem, deleteItem, deleteNewApiItem, getApiItems} from "~/utils/helpers/functions";
import {CookieRef} from "#app";

export const homePageSettings = defineStore('settings', {
    state: () => ({
        loading: false,
        error: {},
        settings: [],
        returnPolicy: null,
        seoMeta: [],
        ins_partners: [],
        pagination: {
            page: 1,
            take: 1,
            itemCount: 0,
            pageCount: 0,
            hasPreviousPage: false,
            hasNextPage: false
        },
        brandImages: []
    }),

    actions: {

        async getSettingData(order = 'ASC', page = 1, take = 10, sort = '', search = '', type = '') {
            this.loading = true;
            const query = {order: order, page: page, take: take, sort: sort, globalSearch: search, type: type};
            await getApiItems('setupHomePage/getByType', query).then((res: any) => {

                this.settings = res.items;
                this.pagination = res.pagination
                this.loading = false
            })
        },

        async getInstagramPartner(order = 'ASC', page = 1, take = 10, sort = '', search = '', type = '') {
            this.loading = true;
            const query = {order: order, page: page, take: take, sort: sort, globalSearch: search, type: type};
            await getApiItems('homePartnerInstagram/getByType', query).then((res: any) => {

                this.ins_partners = res.items;
                this.pagination = res.pagination
                this.loading = false
            })
        },

        async addUpdateSetting( payload: Object ) {
            return await addEditApiItem( 'setupHomePage', payload.id,'', payload, this.settings );
        },

        async addUpdateInstagramPartner( payload: Object ) {
            return await addEditApiItem( 'homePartnerInstagram', payload.id,'', payload, this.settings );
        },

        async deletePartnerInstragram(id: number) {
            return await deleteItem('homePartnerInstagram/delete/', id, this.ins_partners);
        },
        
        async addUpdateReturnPolicy( payload: Object ) {
            return await addEditApiItem( 'setup', payload.id,'', payload, this.returnPolicy );
        },

        async saveSeoMetaData( payload: Object ) {
            return await addEditApiItem( 'seoMeta', payload?.id,'', payload, this.seoMeta );
        },
        
        async getReturnPolicyData( key = '') {
            this.loading = true;
            const query = {key: key};
            await getApiItems('setup/getByKey', query).then((res: any) => {
                this.returnPolicy = res.items;
                this.loading = false
            })
        },

        async getSeoMetaData( key = '') {
            this.loading = true;
            const query = {metaType: key};
            const user: CookieRef<CurrentAdminUser> = useCookie('PantonecloAdminUser');

            const config = useRuntimeConfig();
            return await $fetch(config.public.apiUrl + 'seoMeta/getByKey', {
                params: query,
                headers: {
                    'Access-Control-Allow-Origin': '*',
                    'Authorization': 'Bearer ' + user.value?.token
                },
            })
        },

    },
    persist: true
})
