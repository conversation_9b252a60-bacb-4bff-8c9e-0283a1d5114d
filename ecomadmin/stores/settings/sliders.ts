import { addEditApiItem, putApiItem, getApiItems, deleteApiItem } from "~/utils/helpers/functions";

export const useSliderStore = defineStore('sliders', {
	state: () => ({
		loading: false,
		sliders: [],
		pagination: {
			page: 1,
			take: 1,
			itemCount: 0,
			pageCount: 0,
			hasPreviousPage: false,
			hasNextPage: false
	}
	}),

	getters:{
		getSliders(state){
			return state?.sliders;
		}
	},
	actions: {
		async fetchAllSliders(order='ASC', page=1, take=10){
			this.loading = true;
			const query = {order: order, page: page, take: take};
			
			await getApiItems( 'media-slider/slider/findAllDataByPagination', query ).then((response) => {
				this.sliders = response?.items;
				this.pagination = response?.pagination;
				this.loading = false;
			})
		},
		async fetchSingleSlider(params:any={}){
			const id = params?.id;
			return await getApiItems(`media-slider/slider/findById/${id}`, {})
		},
		async saveSingleSlider(payload:any={}){
			let apiUrl = 'media-slider/slider/create';
			if(payload?.id){
				apiUrl = 'media-slider/slider/updateById';
			}
			return await addEditApiItem( apiUrl, payload?.id, '' , payload, [] );
		},
		async saveSingleSlideMedia(payload:any={}){
			let apiUrl = 'media-slider/slider-media/create';
			if(payload?.id){
				apiUrl = 'media-slider/slider-media/updateById';
			}
			return await addEditApiItem( apiUrl, payload?.id, '' , payload, [] );
		},
		async saveSingleCountrySlideMedia(payload:any={}){
			let apiUrl = 'media-slider/slider-country/create';
			if(payload?.id){
				apiUrl = 'media-slider/slider-country/updateById';
			}
			return await addEditApiItem( apiUrl, payload?.id, '' , payload, [] );
		},
		async saveSliderMediaSerial(payload:any={}){
			let apiUrl = 'media-slider/slider-media/updateMany';
			// below 3 paramter is a bug of generic API function
			return await putApiItem(apiUrl, payload);
		},
		async saveSingleCountrySlideMediaSerial(payload:any={}){
			let apiUrl = 'media-slider/slider-country/updateMany';
			// below 3 paramter is a bug of generic API function
			return await putApiItem(apiUrl, payload);
		},
		async deleteSliderMedia(itemId:any={}, itemList:[]){
			let apiUrl = '/media-slider/slider-media/delete/';
			// below 3 paramter is a bug of generic API function
			return await deleteApiItem(apiUrl, itemId, itemList);
		},
		async deleteSingleSlider(itemId:any={}, itemList: any){
			let apiUrl = '/media-slider/slider/delete/';
			// below 3 paramter is a bug of generic API function
			return await deleteApiItem(apiUrl, itemId, itemList);
		}
	}
})