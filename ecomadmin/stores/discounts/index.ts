import {defineStore} from "pinia";
import {
    addEditApiItem, deleteApiItem,
    getApiItem,
    getApiItems,
    deleteItem,
} from "~/utils/helpers/functions";

export const useDiscountsStore = defineStore("discounts", {
    state: () => ({
        loading: false,
        searchLoading: false,
        error: {},
        discounts: [],
        pagination: {
            page: 1,
            take: 10,
            itemCount: 0,
            pageCount: 0,
            hasPreviousPage: false,
            hasNextPage: false,
        },
    }),
    actions: {
        async createDiscount(payload={}){
            return await addEditApiItem(
                '/discounts',
                payload?.id,
                '',
                payload,
                this.discounts
            )
        },
        async fetchDiscounts(payload={}){
            await getApiItems(
                '/discounts',
                {},
            ).then((res: any) => {
                this.discounts = res.items;
                this.loading = false;
            });
        },
        async updateDiscount(payload={}){
            return await addEditApiItem(
                `/discounts`,
                payload?.id,
                '',
                payload,
                this.discounts
            )
        },
        async getSingleDiscount(payload={}){
            return await getApiItem(
                `/discounts`,
                payload?.id,
            )
        },
        async deleteDiscount(payload={}){
            return await deleteItem(
                `/discounts/soft-delete/`,
                payload?.id,
                this.discounts
            )
        }
    }
});