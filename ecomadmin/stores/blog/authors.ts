import {addEditApiItem, arrayToTree, formDataManipulation, getAllApiItem, getApiItem} from "~/utils/helpers/functions";

import { defineStore } from 'pinia';
export const useBlogAuthorsStore = defineStore('blog-authors', {
    state: () => ({
        loading: false,
        error: {},
        authors: [],
        parentAuthors: <AUTHORS>
        pagination: {
            page: 1,
            take: 1,
            itemCount: 0,
            pageCount: 0,
            hasPreviousPage: false,
            hasNextPage: false
        }
    }),

    getters: {
    },

    actions : {
        async getAuthors(order: string = "ASC", page: number = 1, take: number = 20, sort: '', search = '') {

            this.loading = true;
            const {pending, data: authors, error, refresh}
                = await useApiDataFetch('blog-author/findAllDataByPagination', {
                query: {order: order, page: page, take: take, sort: sort, globalSearch: search},
            })

            if (authors.value) {
                this.authors = authors.value.data;
                this.pagination = authors.value.meta
                this.loading = pending.value;
            }

            if (error.value) {
                this.error = error.value
            }
        },

        async getAllActiveAuthors() {
            this.loading = true;
            await getAllApiItem( 'blog-author/getAllAdminActiveData').then((res: any) => {
                this.authors = res.item;
                // this.authors = [];
                this.loading = false
            })
        },


        async addUpdateAuthor( author: any ) {
            return await addEditApiItem( 'blog-author', author.id, '', author, this.authors );
        },

        async localizeAuthor( author: any, payload: any ) {
            return await addEditApiItem( '/blog-author/authorLocalize', author.id, '', payload, this.authors );
        },

        async deleteAuthor(id: number) {

            // const confirmation = confirm('Are you sure?');

            if (id) {
                const {data, pending, error, refresh}
                    = await useApiDataFetch('blog-author/delete/' + id, {
                    method: 'delete'
                });
                if (data.value.isSuccess) {
                    const itemList = this.authors;
                    const objIndex = itemList.findIndex((obj => obj.id == id));

                    if (objIndex > -1) {
                        itemList.splice(objIndex, 1)
                    }
                    console.log('data', data?.value)
                    return data?.value;
                }
            }

        },

        async getAuthorById( id: number ) {
            return await getApiItem( 'blog-author', id );
        },

        async getParentAuthors() {
            await getAllApiItem( 'blog-author/getParentCategories').then((res: any) => {
                this.parentAuthors = res?.item || [];
                this.loading = false
            })
        }
    },

    persist: true
})
