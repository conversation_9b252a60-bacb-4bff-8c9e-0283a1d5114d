import {addEditApiItem, arrayToTree, buildParams, formDataManipulation, getAllApiItem, getApiItem} from "~/utils/helpers/functions";

import { defineStore } from 'pinia';
export const useBlogTagsStore = defineStore('blog-tags', {
    state: () => ({
        loading: false,
        error: {},
        tags: [],
        parentTags: [],
        pagination: {
            page: 1,
            take: 1,
            itemCount: 0,
            pageCount: 0,
            hasPreviousPage: false,
            hasNextPage: false
        }
    }),

    getters: {
    },

    actions : {
        async getTags(params={}) {

            this.loading = true;
            const {pending, data: tags, error, refresh}
                = await useApiDataFetch(`blog-tag/findAllDataByPagination${buildParams(params)}`)

            if (tags.value) {
                this.tags = tags.value.data;
                this.pagination = tags.value.meta
                this.loading = pending.value;
            }

            if (error.value) {
                this.error = error.value
            }
        },

        async getAllActiveTags() {
            this.loading = true;
            await getAllApiItem( 'blog-tag/getAllAdminActiveData').then((res: any) => {
                this.tags = res.item;
                // this.tags = [];
                this.loading = false
            })
        },


        async addUpdateTag( tag: any ) {
            return await addEditApiItem( 'blog-tag', tag.id, '', tag, this.tags );
        },

        async localizeTag( tag: any, payload: any ) {
            return await addEditApiItem( '/blog-tag/tagLocalize', tag.id, '', payload, this.tags );
        },

        async deleteTag(id: number) {

            // const confirmation = confirm('Are you sure?');

            if (id) {
                const {data, pending, error, refresh}
                    = await useApiDataFetch('blog-tag/delete/' + id, {
                    method: 'delete'
                });
                if (data.value.isSuccess) {
                    const itemList = this.tags;
                    const objIndex = itemList.findIndex((obj => obj.id == id));

                    if (objIndex > -1) {
                        itemList.splice(objIndex, 1)
                    }
                    console.log('data', data?.value)
                    return data?.value;
                }
            }

        },

        async getTagById( id: number ) {
            return await getApiItem( 'blog-tag', id );
        },

        async getParentTags() {
            await getAllApiItem( 'blog-tag/getParentCategories').then((res: any) => {
                this.parentTags = res?.item || [];
                this.loading = false
            })
        }
    },

    persist: true
})
