import { addEditApiItem, arrayToTree, formDataManipulation, getAllApiItem, getApiItem } from "~/utils/helpers/functions";
import { defineStore } from 'pinia';

export const useBlogPostsStore = defineStore('blog-posts', {
	state: () => ({
		loading: false,
		error: {},
		blogs: [],
		singleBlog: {},
		pagination: {
			page: 1,
			take: 1,
			itemCount: 0,
			pageCount: 0,
			hasPreviousPage: false,
			hasNextPage: false
		}
	}),

	getters: {
	},

	actions: {
		async getAllPosts(order: string = "ASC", page: number = 1, take: number = 10, sort: string = '', search = '') {

			this.loading = true;
			const { pending, data: blogs, error, refresh }= await useApiDataFetch('blog-post/findAllDataByPagination', {
				query: { order: order, page: page, take: take, sort: sort, globalSearch: search },
			});

			if (blogs.value) {
				this.blogs = blogs?.value?.data;
				this.pagination = blogs?.value?.meta;
				this.loading = pending?.value;
			}

			if (error.value) {
				this.error = error.value
			}
		},
		async getSinglePost(params={}) {

			if(!params?.id){
				alert("Id is null!");
				return;
			}

			this.loading = true;
			const { pending, data: blog, error, refresh }= await useApiDataFetch(`blog-post/${params?.id}`);

			if (blog.value) {
				this.singleBlog = blog?.value?.data;
				this.loading = pending?.value;
			}

			if (error.value) {
				this.error = error.value
			}
		},
		async createSinglePost(payload={}) {

			this.loading = true;
			const { pending, data: blog, error, refresh }= await useApiDataFetch(`blog-post/`,
				{
					method: 'POST',
					body: payload
				}
			);
			if (blog.value) {
				this.loading = pending?.value;
			}

			if (error.value) {
				this.error = error.value
			}
		},
		async createSinglePostLocalize(payload={}) {

			this.loading = true;
			const parentId = payload?.blogPost;
			delete payload.blogPost;
			const { pending, data: blog, error, refresh }= await useApiDataFetch(`blog-post/${parentId}/localize/create`,
				{
					method: 'POST',
					body: payload
				}
			);
			if (blog.value) {
				this.loading = pending?.value;
				return {
					data: blog?.value?.data,
					success: blog?.value?.isSuccess,
					message: blog?.value?.messasge,
				}
			}

			if (error.value) {
				this.error = error.value
			}
		},
		async updateSinglePostLocalize(payload={}) {

			this.loading = true;
			const id = payload?.id;
			delete payload.id;
			const { pending, data: blog, error, refresh }= await useApiDataFetch(`blog-post/${id}/localize/update`,
				{
					method: 'PUT',
					body: payload
				}
			);
			if (blog.value) {
				this.loading = pending?.value;
				return {
					data: blog?.value?.data,
					success: blog?.value?.isSuccess,
					message: blog?.value?.messasge,
				}	
			}

			if (error.value) {
				this.error = error.value
			}
		},
		async updateSinglePost(payload={}) {

			this.loading = true;
			const { pending, data: blog, error, refresh }= await useApiDataFetch(`blog-post/${payload?.id}`,
				{
					method: 'PUT',
					body: payload
				}
			);
			if (blog.value) {
				this.loading = pending?.value;
			}

			if (error.value) {
				this.error = error.value
			}
		},
		async deleteSinglePost(payload={}) {

			this.loading = true;
			const { pending, data: blog, error, refresh }= await useApiDataFetch(`blog-post/${payload?.id}`,
				{
					method: 'DELETE',
					body: payload
				}
			);
			if (blog.value) {
				this.loading = pending?.value;
			}

			if (error.value) {
				this.error = error.value
			}
		},
	}
});