<script setup lang="ts">
import { useCustomizerStore } from '@/stores/customizer';

const customizer = useCustomizerStore();

</script>


<template>

    <!-----LTR LAYOUT------->
    <v-locale-provider>
        <v-app
            :theme="customizer.actTheme"
            :class="[
                customizer.actTheme,
                customizer.mini_sidebar ? 'mini-sidebar' : '',
                customizer.setHorizontalLayout ? 'horizontalLayout' : 'verticalLayout',
                customizer.setBorderCard ? 'cardBordered' : ''
            ]"
        >

             <!---Customizer location right side--->
             <v-navigation-drawer app temporary elevation="10" location="right" v-model="customizer.Customizer_drawer" width="320">
             <LcFullCustomizer/>
             </v-navigation-drawer>
            <LcFullVerticalSidebar v-if="!customizer.setHorizontalLayout" />
            <LcFullVerticalHeader v-if="!customizer.setHorizontalLayout" />
            <v-main>
               <v-container fluid class="page-wrapper pb-sm-15 pb-10">
                    <div :class="customizer.boxed ? 'maxWidth' : ''">
                        <NuxtPage />
                    </div>
                </v-container>
            </v-main>
        </v-app>
    </v-locale-provider>
</template>
