name: Deploy to Server

on:
  push:
    branches:
      - main  # This triggers the workflow only on pushes to the main branch

jobs:
  deploy-aws:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v2

      - name: List files in repository to check for deploy.sh
        run: |
          ls -alh $GITHUB_WORKSPACE

      - name: Check if PANTONECL<PERSON> secret is set
        id: check_secret
        run: |
          if [[ -z "${{ secrets.PANTONECLO }}" ]]; then
            echo "PANTONECLO secret is not set. Skipping deployment."
            exit 0  # Exit without failure, skipping the deploy steps
          fi
          echo "PANTONECLO secret is set. Proceeding with deployment."

      - name: Set up SSH
        if: steps.check_secret.outputs.result != 'skipped'
        env:
          SSH_PRIVATE_KEY: ${{ secrets.PANTONECLO }}  # GitHub secret containing private key
          PANTONECLO_HOST: ${{ vars.PANTONECLO_HOST}}
          PANTONECLO_USERNAME: ${{ vars.PANTONECLO_USERNAME}}
        run: |
          mkdir -p ~/.ssh
          echo "$SSH_PRIVATE_KEY" | tr -d '\r' > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          # Add the server to the known hosts to prevent SSH prompt
          ssh-keyscan -H "$PANTONECLO_HOST" >> ~/.ssh/known_hosts

      - name: Debug SSH Connection
        if: steps.check_secret.outputs.result != 'skipped'
        env:
          PANTONECLO_HOST: ${{ vars.PANTONECLO_HOST}}
          PANTONECLO_USERNAME: ${{ vars.PANTONECLO_USERNAME}}
        run: |
          ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no "$PANTONECLO_USERNAME@$PANTONECLO_HOST" 'echo "SSH connection successful"'

      - name: Check for node_modules directory
        if: steps.check_secret.outputs.result != 'skipped'
        env:
          PANTONECLO_HOST: ${{ vars.PANTONECLO_HOST}}
          PANTONECLO_USERNAME: ${{ vars.PANTONECLO_USERNAME}}
        run: |
          ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no "$PANTONECLO_USERNAME@$PANTONECLO_HOST" << 'EOF'
            # Navigate to the project directory (adjust path as needed)
            cd /home/<USER>/api.pantoneclo.com
            
            # Check if node_modules exists
            if [ -d "node_modules" ]; then
              echo "node_modules directory exists. Skipping npm install."
            else
              echo "node_modules not found. Running npm install."
              npm install
            fi
          EOF

      - name: Deploy Application
        if: steps.check_secret.outputs.result != 'skipped'
        env:
          PANTONECLO_HOST: ${{ vars.PANTONECLO_HOST}}
          PANTONECLO_USERNAME: ${{ vars.PANTONECLO_USERNAME}}
        run: |
          # SSH into the server and perform necessary git operations
          ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no "$PANTONECLO_USERNAME@$PANTONECLO_HOST" << 'EOF'
            # Navigate to the project directory (adjust path as needed)
            cd /home/<USER>/adminpanel.pantoneclo.com
            
            # Reset git repository
            git reset --hard
            git clean -df
            git reset --hard .
            
            # Optionally, pull latest changes (if necessary)
            git pull origin main

            # Run npm build before restarting supervisor
            npm run build
            
            # Restart supervisor with specific job id
            sudo -S supervisorctl restart daemon-873215:*
          EOF

  deploy-hetzner:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v2

      - name: List files in repository to check for deploy.sh
        run: |
          ls -alh $GITHUB_WORKSPACE

      - name: Set up SSH
        env:
          SSH_PRIVATE_KEY: ${{ secrets.PANTONECLO }}  # GitHub secret containing private key
          PANTONECLO_HOST: ${{ vars.PANTONECLO_HETZNER_HOST}}
          PANTONECLO_USERNAME: ${{ vars.PANTONECLO_HETZNER_USERNAME}}
        run: |
          set -e
          mkdir -p ~/.ssh
          echo "$SSH_PRIVATE_KEY" | tr -d '\r' > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          # Add the server to the known hosts to prevent SSH prompt
          ssh-keyscan -H "$PANTONECLO_HOST" >> ~/.ssh/known_hosts

      - name: Debug SSH Connection
        env:
          PANTONECLO_HOST: ${{ vars.PANTONECLO_HETZNER_HOST }}
          PANTONECLO_USERNAME: ${{ vars.PANTONECLO_HETZNER_USERNAME}}
        run: |
          ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no "$PANTONECLO_USERNAME@$PANTONECLO_HOST" 'echo "SSH connection successful"'

      - name: Deploy Application
        env:
          PANTONECLO_HOST: ${{ vars.PANTONECLO_HETZNER_HOST}}
          PANTONECLO_USERNAME: ${{ vars.PANTONECLO_HETZNER_USERNAME}}
        run: |

          ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no "$PANTONECLO_USERNAME@$PANTONECLO_HOST" << 'EOF'

            cd /root/pantoneclo/ecomadmin
            
            git reset --hard
            git clean -df
            git reset --hard .
            
            GIT_SSH_COMMAND="ssh -i ~/.ssh/pantoneclo/id_rsa" git pull

            docker compose build && docker compose up -d
          EOF
