{"private": true, "scripts": {"build": "nuxt build", "dev": "nuxt dev -p 3001", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "live": "PORT=3001 node .output/server/index.mjs"}, "devDependencies": {"@nuxt/devtools": "latest", "@pinia-plugin-persistedstate/nuxt": "^1.2.0", "nuxt": "3.7.4", "ofetch": "^1.3.3"}, "dependencies": {"@mdi/font": "6.5.95", "@nuxt/vite-builder": "3.5.3", "@pinia/nuxt": "^0.5.0", "@tinymce/tinymce-vue": "^6.0.1", "@tiptap/extension-text-style": "^2.8.0", "@tiptap/pm": "^2.3.0", "@tiptap/starter-kit": "^2.3.0", "@tiptap/vue-3": "^2.3.0", "apexcharts": "^4.4.0", "dayjs": "^1.11.13", "html2canvas": "^1.4.1", "jspdf": "^2.5.1", "nuxt-snackbar": "^1.0.4", "pinia": "2.1.3", "remixicon": "^4.3.0", "sass": "1.56.1", "vee-validate": "4.6.7", "vite-plugin-vuetify": "1.0.2", "vue": "3.3.4", "vue-style-loader": "4.1.3", "vue-tabler-icons": "2.9.0", "vue3-apexcharts": "^1.8.0", "vue3-perfect-scrollbar": "1.6.0", "vue3-snackbar": "^2.3.4", "vuedraggable": "^4.1.0", "vuetify": "3.5.1", "yup": "0.32.11"}}