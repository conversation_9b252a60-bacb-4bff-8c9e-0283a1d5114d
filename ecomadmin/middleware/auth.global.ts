import {useAuthStore} from "~/stores/auth";

export default defineNuxtRouteMiddleware((to, from) => {

    const pantonecloAdminUser = useCookie<CurrentAdminUser>('PantonecloAdminUser');

    if( pantonecloAdminUser.value?.token && (to?.name === 'login' || to?.name === 'index') ) {
        return navigateTo( '/dashboard' )
    }

    if( !pantonecloAdminUser.value?.token && to?.name !== 'login' ) {
        abortNavigation();
        return navigateTo('/login')
    }
})