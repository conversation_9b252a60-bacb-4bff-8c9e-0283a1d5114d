import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { createProxyMiddleware } from 'http-proxy-middleware';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  const expressApp = app.getHttpAdapter().getInstance();

  expressApp.use(
    '/ecommerce',
    createProxyMiddleware({
      target: 'http://localhost:4000',
      pathRewrite: { '^/ecommerce': '/api' },
      changeOrigin: true,
    }),
  );

  expressApp.use(
    '/notification',
    createProxyMiddleware({
      target: 'http://localhost:4001',
      pathRewrite: { '^/notification': '/api' },
      changeOrigin: true,
    }),
  );

  await app.listen(3333);
}
bootstrap();
