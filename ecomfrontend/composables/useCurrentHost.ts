
import { getDomainDataList, getDomaCountryId, getDomainLang, getDomaCountryCurrency,  getDomaCountryCode, getDomaCountryCurrencySymbol, getDomaLanguageId, getDomaCountryDomain } from "../lib/domain-lang-map"

export const useCurrentHost = () => {
    const getCurrentHost = () => {
        if (import.meta?.server) {
            const headers = useRequestHeaders();
            const host = headers['host'];
            return host ? host.split(':')[0] : '';
        }

        return window.location.host.split(':')[0] || '';;
    }

    const _getDomaCountryId = () => {
        return getDomaCountryId(getCurrentHost());
    }

    const _getDomainLang = () => {
        return getDomainLang(getCurrentHost());
    }

    const _getDomaCountryCode = () => {
        return getDomaCountryCode(getCurrentHost());
    }
    const _getDomaCountryCurrencySymbol = () => {
        return getDomaCountryCurrencySymbol(getCurrentHost());
    }

    const _getDomaLanguageId = () => {
        return getDomaLanguageId(getCurrentHost());
    }

    const _getDomaCountryDomain = () => {
        return getDomaCountryDomain(getCurrentHost());
    }
    const _getDomaCountryCurrency = () =>{
        return getDomaCountryCurrency(getCurrentHost());
    }

    const _getDomaCountryList = () => {
        return getDomainDataList();
    }
    const _getDomaCountryListNonDuplicate = () => {
        const seen = new Set();
        const dataList = getDomainDataList();
        return dataList?.filter(item => {
            if (seen?.has(item?.id)) {
            return false;
            }
            seen.add(item?.id);
            return true;
        });
    }
    const _getDomaCountryListNonEuroCurrency = () => {
        const seen = new Set();
        const dataList = getDomainDataList();
        return dataList?.filter(item => {
            if (seen?.has(item?.id)) {
            return false;
            }
            seen.add(item?.id);
            return item?.currency != 'EUR';
        });
    }

    return {
        getCurrentHost,
        getDomaCountryId: _getDomaCountryId,
        getDomainLang: _getDomainLang,
        getDomaCountryCode: _getDomaCountryCode,
        getDomaCountryCurrencySymbol: _getDomaCountryCurrencySymbol,
        getDomaLanguageId: _getDomaLanguageId,
        getDomaCountryDomain: _getDomaCountryDomain,
        getDomaCountryList: _getDomaCountryList,
        getDomaCountryListNonDuplicate:_getDomaCountryListNonDuplicate,
        getDomaCountryListNonEuroCurrency:_getDomaCountryListNonEuroCurrency,
        getDomaCountryCurrency: _getDomaCountryCurrency
    }
}


