name: Deploy to Production Server

on:
  push:
    branches:
      - main  # Replace with the branch you want to trigger deployment from

jobs:
  # deploy-aws:
  #   runs-on: ubuntu-latest

  #   steps:
  #     - name: Checkout Repository
  #       uses: actions/checkout@v2

  #     - name: List files in repository to check for deploy.sh
  #       run: |
  #         ls -alh $GITHUB_WORKSPACE

  #     - name: Set up SSH
  #       env:
  #         SSH_PRIVATE_KEY: ${{ secrets.PANTONECLO }}  # GitHub secret containing private key
  #         PANTONECLO_HOST: ${{ vars.PANTONECLO_HOST}}
  #         PANTONECLO_USERNAME: ${{ vars.PANTONECLO_USERNAME}}
  #       run: |
  #         set -e
  #         mkdir -p ~/.ssh
  #         echo "$SSH_PRIVATE_KEY" | tr -d '\r' > ~/.ssh/id_rsa
  #         chmod 600 ~/.ssh/id_rsa
  #         # Add the server to the known hosts to prevent SSH prompt
  #         ssh-keyscan -H "$PANTONECLO_HOST" >> ~/.ssh/known_hosts

  #     - name: Debug SSH Connection
  #       env:
  #         PANTONECLO_HOST: ${{ vars.PANTONECLO_HOST}}
  #         PANTONECLO_USERNAME: ${{ vars.PANTONECLO_USERNAME}}
  #       run: |
  #         ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no "$PANTONECLO_USERNAME@$PANTONECLO_HOST" 'echo "SSH connection successful"'

  #     - name: Deploy Application
  #       env:
  #         PANTONECLO_HOST: ${{ vars.PANTONECLO_HOST}}
  #         PANTONECLO_USERNAME: ${{ vars.PANTONECLO_USERNAME}}
  #       run: |
  #         ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no "$PANTONECLO_USERNAME@$PANTONECLO_HOST" 'bash -x /home/<USER>/build/deploy.sh'

  deploy-hetzner:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v2

      - name: List files in repository to check for deploy.sh
        run: |
          ls -alh $GITHUB_WORKSPACE

      - name: Set up SSH
        env:
          SSH_PRIVATE_KEY: ${{ secrets.PANTONECLO }}  # GitHub secret containing private key
          PANTONECLO_HOST: ${{ vars.PANTONECLO_HETZNER_HOST}}
          PANTONECLO_USERNAME: ${{ vars.PANTONECLO_HETZNER_USERNAME}}
        run: |
          set -e
          mkdir -p ~/.ssh
          echo "$SSH_PRIVATE_KEY" | tr -d '\r' > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          # Add the server to the known hosts to prevent SSH prompt
          ssh-keyscan -H "$PANTONECLO_HOST" >> ~/.ssh/known_hosts

      - name: Debug SSH Connection
        env:
          PANTONECLO_HOST: ${{ vars.PANTONECLO_HETZNER_HOST }}
          PANTONECLO_USERNAME: ${{ vars.PANTONECLO_HETZNER_USERNAME}}
        run: |
          ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no "$PANTONECLO_USERNAME@$PANTONECLO_HOST" 'echo "SSH connection successful"'

      - name: Deploy Application
        env:
          PANTONECLO_HOST: ${{ vars.PANTONECLO_HETZNER_HOST}}
          PANTONECLO_USERNAME: ${{ vars.PANTONECLO_HETZNER_USERNAME}}
        run: |

          ssh -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no "$PANTONECLO_USERNAME@$PANTONECLO_HOST" << 'EOF'

            cd /root/pantoneclo/ecomfrontend
            
            git reset --hard
            git clean -df
            git reset --hard .
            
            GIT_SSH_COMMAND="ssh -i ~/.ssh/pantoneclo/id_rsa" git pull

            docker compose build --no-cache && docker compose up -d
          EOF
