<template>
  <div>
    <!-- <PERSON> Header -->
    <div class="mb-6">
      <h1 class="text-h4 font-weight-bold mb-2">
        {{ $t('Link Generator') }}
      </h1>
      <p class="text-body-1 text-medium-emphasis">
        {{ $t('Create trackable affiliate links for your marketing campaigns') }}
      </p>
    </div>

    <v-row>
      <!-- Link Generator Form -->
      <v-col cols="12" lg="8">
        <v-card elevation="2" rounded="lg">
          <v-card-title class="text-h6 font-weight-semibold">
            {{ $t('Generate New Link') }}
          </v-card-title>
          <v-card-text>
            <v-form @submit.prevent="generateLink" v-model="valid">
              <v-row>
                <v-col cols="12">
                  <v-label class="text-subtitle-1 font-weight-semibold pb-2">
                    {{ $t('Product/Page URL') }} <span class="text-error">*</span>
                  </v-label>
                  <v-text-field
                    v-model="form.baseUrl"
                    :rules="urlRules"
                    placeholder="https://store.com/products/example"
                    required
                    hide-details="auto"
                    class="mb-4"
                  />
                </v-col>

                <v-col cols="12" md="6">
                  <v-label class="text-subtitle-1 font-weight-semibold pb-2">
                    {{ $t('UTM Source') }}
                  </v-label>
                  <v-text-field
                    v-model="form.utmSource"
                    placeholder="instagram, facebook, email"
                    hide-details="auto"
                    class="mb-4"
                  />
                </v-col>

                <v-col cols="12" md="6">
                  <v-label class="text-subtitle-1 font-weight-semibold pb-2">
                    {{ $t('UTM Medium') }}
                  </v-label>
                  <v-text-field
                    v-model="form.utmMedium"
                    placeholder="social, email, cpc"
                    hide-details="auto"
                    class="mb-4"
                  />
                </v-col>

                <v-col cols="12" md="6">
                  <v-label class="text-subtitle-1 font-weight-semibold pb-2">
                    {{ $t('UTM Campaign') }}
                  </v-label>
                  <v-text-field
                    v-model="form.utmCampaign"
                    placeholder="summer_sale_2024"
                    hide-details="auto"
                    class="mb-4"
                  />
                </v-col>

                <v-col cols="12" md="6">
                  <v-label class="text-subtitle-1 font-weight-semibold pb-2">
                    {{ $t('UTM Content') }}
                  </v-label>
                  <v-text-field
                    v-model="form.utmContent"
                    placeholder="story_post, feed_post"
                    hide-details="auto"
                    class="mb-4"
                  />
                </v-col>

                <v-col cols="12">
                  <v-label class="text-subtitle-1 font-weight-semibold pb-2">
                    {{ $t('UTM Term') }}
                  </v-label>
                  <v-text-field
                    v-model="form.utmTerm"
                    placeholder="summer_fashion, shoes"
                    hide-details="auto"
                    class="mb-4"
                  />
                </v-col>
              </v-row>

              <v-btn
                type="submit"
                color="primary"
                size="large"
                :loading="generating"
                :disabled="!valid"
                prepend-icon="i-mdi:link-variant"
              >
                {{ $t('Generate Link') }}
              </v-btn>
            </v-form>
          </v-card-text>
        </v-card>

        <!-- Generated Link Result -->
        <v-card v-if="generatedLink" elevation="2" rounded="lg" class="mt-6">
          <v-card-title class="text-h6 font-weight-semibold text-success">
            {{ $t('Generated Link') }}
          </v-card-title>
          <v-card-text>
            <v-text-field
              :model-value="generatedLink"
              readonly
              variant="outlined"
              class="mb-4"
              append-inner-icon="i-mdi:content-copy"
              @click:append-inner="copyToClipboard(generatedLink)"
            />
            
            <div class="d-flex gap-2 flex-wrap">
              <v-btn
                color="primary"
                variant="outlined"
                @click="copyToClipboard(generatedLink)"
                prepend-icon="i-mdi:content-copy"
              >
                {{ $t('Copy Link') }}
              </v-btn>
              
              <v-btn
                color="info"
                variant="outlined"
                @click="previewLink"
                prepend-icon="i-mdi:eye"
              >
                {{ $t('Preview') }}
              </v-btn>
              
              <v-btn
                color="success"
                variant="outlined"
                @click="saveLink"
                prepend-icon="i-mdi:bookmark"
              >
                {{ $t('Save Link') }}
              </v-btn>
            </div>
          </v-card-text>
        </v-card>
      </v-col>

      <!-- Quick Tips & Saved Links -->
      <v-col cols="12" lg="4">
        <!-- Quick Tips -->
        <v-card elevation="2" rounded="lg" class="mb-6">
          <v-card-title class="text-h6 font-weight-semibold">
            {{ $t('Quick Tips') }}
          </v-card-title>
          <v-card-text>
            <div class="tips-list">
              <div class="tip-item d-flex align-start mb-3">
                <v-icon color="primary" size="20" class="me-2 mt-1">
                  i-mdi:lightbulb-outline
                </v-icon>
                <div>
                  <p class="text-body-2 mb-1 font-weight-medium">
                    {{ $t('Use descriptive UTM parameters') }}
                  </p>
                  <p class="text-caption text-medium-emphasis">
                    {{ $t('This helps you track which campaigns perform best') }}
                  </p>
                </div>
              </div>

              <div class="tip-item d-flex align-start mb-3">
                <v-icon color="primary" size="20" class="me-2 mt-1">
                  i-mdi:target
                </v-icon>
                <div>
                  <p class="text-body-2 mb-1 font-weight-medium">
                    {{ $t('Target specific products') }}
                  </p>
                  <p class="text-caption text-medium-emphasis">
                    {{ $t('Direct links to products convert better than homepage links') }}
                  </p>
                </div>
              </div>

              <div class="tip-item d-flex align-start">
                <v-icon color="primary" size="20" class="me-2 mt-1">
                  i-mdi:chart-line
                </v-icon>
                <div>
                  <p class="text-body-2 mb-1 font-weight-medium">
                    {{ $t('Monitor your analytics') }}
                  </p>
                  <p class="text-caption text-medium-emphasis">
                    {{ $t('Check your dashboard regularly to optimize performance') }}
                  </p>
                </div>
              </div>
            </div>
          </v-card-text>
        </v-card>

        <!-- Saved Links -->
        <v-card elevation="2" rounded="lg">
          <v-card-title class="d-flex align-center justify-space-between">
            <span class="text-h6 font-weight-semibold">
              {{ $t('Saved Links') }}
            </span>
            <v-btn
              icon="i-mdi:refresh"
              variant="text"
              size="small"
              @click="loadSavedLinks"
            />
          </v-card-title>
          <v-card-text>
            <div v-if="savedLinks.length === 0" class="text-center py-4">
              <v-icon size="48" color="grey-lighten-1" class="mb-2">
                i-mdi:bookmark-outline
              </v-icon>
              <p class="text-body-2 text-medium-emphasis">
                {{ $t('No saved links yet') }}
              </p>
            </div>

            <div v-else class="saved-links-list">
              <div
                v-for="link in savedLinks"
                :key="link.id"
                class="saved-link-item pa-3 mb-2 border rounded"
              >
                <div class="d-flex align-center justify-space-between mb-2">
                  <span class="text-body-2 font-weight-medium">
                    {{ link.name || $t('Untitled Link') }}
                  </span>
                  <v-menu>
                    <template #activator="{ props }">
                      <v-btn
                        v-bind="props"
                        icon="i-mdi:dots-vertical"
                        variant="text"
                        size="small"
                      />
                    </template>
                    <v-list>
                      <v-list-item @click="copyToClipboard(link.url)">
                        <v-list-item-title>{{ $t('Copy') }}</v-list-item-title>
                      </v-list-item>
                      <v-list-item @click="deleteSavedLink(link.id)">
                        <v-list-item-title>{{ $t('Delete') }}</v-list-item-title>
                      </v-list-item>
                    </v-list>
                  </v-menu>
                </div>
                <p class="text-caption text-medium-emphasis text-truncate">
                  {{ link.url }}
                </p>
                <div class="d-flex align-center justify-space-between mt-2">
                  <span class="text-caption">
                    {{ $t('Clicks') }}: {{ link.clicks || 0 }}
                  </span>
                  <span class="text-caption text-medium-emphasis">
                    {{ formatDate(link.createdAt) }}
                  </span>
                </div>
              </div>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </div>
</template>

<script setup lang="ts">
import { copyToClipboard } from "~/utils/functions";
definePageMeta({
  layout: 'affiliate',
  middleware: ['auth', 'affiliate']
});

const { t } = useI18n();
const affiliateStore = useAffiliateStore();

// Form state
const valid = ref(false);
const generating = ref(false);
const generatedLink = ref('');

const form = ref({
  baseUrl: '',
  utmSource: '',
  utmMedium: '',
  utmCampaign: '',
  utmContent: '',
  utmTerm: '',
});

// Saved links
const savedLinks = ref([
  {
    id: 1,
    name: 'Summer Collection',
    url: 'https://store.com/summer?ref=AFFILIATE123&utm_source=instagram',
    clicks: 45,
    createdAt: '2024-01-15T10:30:00Z',
  },
  {
    id: 2,
    name: 'New Arrivals',
    url: 'https://store.com/new-arrivals?ref=AFFILIATE123&utm_source=facebook',
    clicks: 23,
    createdAt: '2024-01-14T15:20:00Z',
  },
]);

// Validation rules
const urlRules = computed(() => [
  (v: string) => !!v || t('URL is required'),
  (v: string) => /^https?:\/\/.+/.test(v) || t('Must be a valid URL'),
]);

// Methods
const generateLink = async () => {
  if (!valid.value) return;

  generating.value = true;
  
  try {
    const response = await affiliateStore.generateTrackingUrl({
      baseUrl: form.value.baseUrl,
      affiliateCode: affiliateStore?.profile?.affiliateCode, // TODO: Get from profile
      utmSource: form.value.utmSource,
      utmMedium: form.value.utmMedium,
      utmCampaign: form.value.utmCampaign,
      utmContent: form.value.utmContent,
      utmTerm: form.value.utmTerm,
    });

    if (response.isSuccess) {
      generatedLink.value = response.data.trackingUrl;
    } else {
      // Handle error
      console.error('Failed to generate link:', response.message);
    }
  } catch (error) {
    console.error('Error generating link:', error);
  } finally {
    generating.value = false;
  }
};

const previewLink = () => {
  if (generatedLink.value) {
    window.open(generatedLink.value, '_blank');
  }
};

const saveLink = () => {
  // TODO: Implement save link functionality
  console.log('Save link:', generatedLink.value);
};

const loadSavedLinks = () => {
  // TODO: Load saved links from API
  console.log('Loading saved links...');
};

const deleteSavedLink = (id: number) => {
  // TODO: Delete saved link
  const index = savedLinks.value.findIndex(link => link.id === id);
  if (index > -1) {
    savedLinks.value.splice(index, 1);
  }
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString();
};

// SEO
useHead({
  title: 'Link Generator - Affiliate Dashboard',
  meta: [
    {
      name: 'description',
      content: 'Generate trackable affiliate links with UTM parameters for your marketing campaigns.'
    }
  ]
});
</script>

<style scoped>
.border {
  border: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
}

.gap-2 > * + * {
  margin-left: 8px;
}
</style>
