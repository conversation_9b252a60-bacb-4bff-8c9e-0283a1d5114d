<template>
  <div>
    <!-- <PERSON>er -->
    <div class="mb-6">
      <h1 class="text-h4 font-weight-bold mb-2">
        {{ $t('Commission Tracking') }}
      </h1>
      <p class="text-body-1 text-medium-emphasis">
        {{ $t('Track your earnings, pending payments, and commission history') }}
      </p>
    </div>

    <!-- Summary Cards -->
    <v-row class="mb-6">
      <v-col cols="12" sm="6" md="3">
        <LazyAffiliateStatsCard
          :title="$t('Total Earnings')"
          :value="formatCurrency(summaryStats.totalEarnings)"
          :change="summaryStats.earningsChange"
          icon="i-mdi:currency-usd"
          color="success"
          :loading="loading"
        />
      </v-col>
      
      <v-col cols="12" sm="6" md="3">
        <LazyAffiliateStatsCard
          :title="$t('Pending')"
          :value="formatCurrency(summaryStats.pendingEarnings)"
          icon="i-mdi:clock-outline"
          color="warning"
          :loading="loading"
        />
      </v-col>
      
      <v-col cols="12" sm="6" md="3">
        <LazyAffiliateStatsCard
          :title="$t('This Month')"
          :value="formatCurrency(summaryStats.monthlyEarnings)"
          :change="summaryStats.monthlyChange"
          icon="i-mdi:calendar-month"
          color="primary"
          :loading="loading"
        />
      </v-col>
      
      <v-col cols="12" sm="6" md="3">
        <LazyAffiliateStatsCard
          :title="$t('Paid Out')"
          :value="formatCurrency(summaryStats.paidEarnings)"
          icon="i-mdi:check-circle"
          color="info"
          :loading="loading"
        />
      </v-col>
    </v-row>

    <!-- Filters and Actions -->
    <v-card elevation="2" rounded="lg" class="mb-6">
      <v-card-text>
        <v-row align="center">
          <v-col cols="12" md="3">
            <v-select
              v-model="filters.status"
              :items="statusOptions"
              :label="$t('Status')"
              clearable
              hide-details
              @update:model-value="applyFilters"
            />
          </v-col>
          
          <v-col cols="12" md="3">
            <v-select
              v-model="filters.timeframe"
              :items="timeframeOptions"
              :label="$t('Time Period')"
              hide-details
              @update:model-value="applyFilters"
            />
          </v-col>
          
          <v-col cols="12" md="4">
            <v-row>
              <v-col cols="6">
                <v-text-field
                  v-model="filters.dateFrom"
                  type="date"
                  :label="$t('From Date')"
                  hide-details
                />
              </v-col>
              <v-col cols="6">
                <v-text-field
                  v-model="filters.dateTo"
                  type="date"
                  :label="$t('To Date')"
                  hide-details
                />
              </v-col>
            </v-row>
          </v-col>
          
          <v-col cols="12" md="2">
            <div class="d-flex gap-2">
              <v-btn
                color="primary"
                @click="applyFilters"
                prepend-icon="i-mdi:filter"
              >
                {{ $t('Filter') }}
              </v-btn>
              
              <v-btn
                variant="outlined"
                @click="exportCommissions"
                prepend-icon="i-mdi:download"
              >
                {{ $t('Export') }}
              </v-btn>
            </div>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>

    <!-- Commissions Table -->
    <v-card elevation="2" rounded="lg">
      <v-card-title class="d-flex align-center justify-space-between">
        <span class="text-h6 font-weight-semibold">
          {{ $t('Commission History') }}
        </span>
        <v-btn
          icon="i-mdi:refresh"
          variant="text"
          @click="loadCommissions"
        />
      </v-card-title>
      
      <v-data-table
        :headers="tableHeaders"
        :items="commissions"
        :loading="loading"
        :items-per-page="itemsPerPage"
        :page="currentPage"
        @update:page="currentPage = $event"
        @update:items-per-page="itemsPerPage = $event"
        class="commission-table"
      >
        <template #item.commissionAmount="{ item }">
          <span class="font-weight-bold text-success">
            {{ formatCurrency(item.commissionAmount) }}
          </span>
        </template>
        
        <template #item.status="{ item }">
          <v-chip
            :color="getStatusColor(item.status)"
            size="small"
            variant="tonal"
          >
            {{ $t(item.status) }}
          </v-chip>
        </template>
        
        <template #item.commissionType="{ item }">
          <v-chip
            size="small"
            variant="outlined"
          >
            {{ item.commissionRate }}{{ item.commissionType === 'percentage' ? '%' : '$' }}
          </v-chip>
        </template>
        
        <template #item.createdAt="{ item }">
          {{ formatDate(item.createdAt) }}
        </template>
        
        <template #item.actions="{ item }">
          <v-menu>
            <template #activator="{ props }">
              <v-btn
                v-bind="props"
                icon="i-mdi:dots-vertical"
                variant="text"
                size="small"
              />
            </template>
            
            <v-list>
              <v-list-item @click="viewCommissionDetails(item)">
                <v-list-item-title>{{ $t('View Details') }}</v-list-item-title>
              </v-list-item>
              
              <v-list-item 
                v-if="item.status === 'pending'"
                @click="requestPayment(item)"
              >
                <v-list-item-title>{{ $t('Request Payment') }}</v-list-item-title>
              </v-list-item>
            </v-list>
          </v-menu>
        </template>
        
        <template #no-data>
          <div class="text-center py-8">
            <v-icon size="48" color="grey-lighten-1" class="mb-2">
              i-mdi:currency-usd-off
            </v-icon>
            <p class="text-body-1 text-medium-emphasis">
              {{ $t('No commissions found') }}
            </p>
          </div>
        </template>
      </v-data-table>
    </v-card>

    <!-- Commission Details Dialog -->
    <v-dialog v-model="detailsDialog" max-width="600">
      <v-card v-if="selectedCommission">
        <v-card-title class="text-h6 font-weight-semibold">
          {{ $t('Commission Details') }}
        </v-card-title>
        
        <v-card-text>
          <v-row>
            <v-col cols="6">
              <div class="mb-3">
                <span class="text-body-2 text-medium-emphasis">{{ $t('Order ID') }}</span>
                <p class="text-body-1 font-weight-medium">#{{ selectedCommission.orderId }}</p>
              </div>
            </v-col>
            
            <v-col cols="6">
              <div class="mb-3">
                <span class="text-body-2 text-medium-emphasis">{{ $t('Commission Amount') }}</span>
                <p class="text-h6 font-weight-bold text-success">
                  {{ formatCurrency(selectedCommission.commissionAmount) }}
                </p>
              </div>
            </v-col>
            
            <v-col cols="6">
              <div class="mb-3">
                <span class="text-body-2 text-medium-emphasis">{{ $t('Order Amount') }}</span>
                <p class="text-body-1 font-weight-medium">
                  {{ formatCurrency(selectedCommission.orderAmount) }}
                </p>
              </div>
            </v-col>
            
            <v-col cols="6">
              <div class="mb-3">
                <span class="text-body-2 text-medium-emphasis">{{ $t('Commission Rate') }}</span>
                <p class="text-body-1 font-weight-medium">
                  {{ selectedCommission.commissionRate }}{{ selectedCommission.commissionType === 'percentage' ? '%' : '$' }}
                </p>
              </div>
            </v-col>
            
            <v-col cols="12">
              <div class="mb-3">
                <span class="text-body-2 text-medium-emphasis">{{ $t('Status') }}</span>
                <p>
                  <v-chip
                    :color="getStatusColor(selectedCommission.status)"
                    variant="tonal"
                  >
                    {{ $t(selectedCommission.status) }}
                  </v-chip>
                </p>
              </div>
            </v-col>
            
            <v-col cols="12" v-if="selectedCommission.notes">
              <div class="mb-3">
                <span class="text-body-2 text-medium-emphasis">{{ $t('Notes') }}</span>
                <p class="text-body-1">{{ selectedCommission.notes }}</p>
              </div>
            </v-col>
          </v-row>
        </v-card-text>
        
        <v-card-actions>
          <v-spacer />
          <v-btn @click="detailsDialog = false">
            {{ $t('Close') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script setup lang="ts">
import { fetchApiData } from '../../utils/apiHelpers';

definePageMeta({
  layout: 'affiliate',
  middleware: ['auth', 'affiliate']
});

const { t } = useI18n();
const affiliateStore = useAffiliateStore();

// State
const loading = ref(true);
const detailsDialog = ref(false);
const selectedCommission = ref(null);
const currentPage = ref(1);
const itemsPerPage = ref(10);

// Filters
const filters = ref({
  status: null,
  timeframe: 'all',
  dateFrom: '',
  dateTo: '',
});

// Mock data
const summaryStats = ref({
  totalEarnings: 2450.75,
  pendingEarnings: 485.20,
  monthlyEarnings: 485.20,
  paidEarnings: 1965.55,
  earningsChange: 12.5,
  monthlyChange: 8.3,
});

const commissions = ref([
]);

// Options
const statusOptions = computed(() => [
  { title: t('All'), value: null },
  { title: t('Pending'), value: 'pending' },
  { title: t('Approved'), value: 'approved' },
  { title: t('Paid'), value: 'paid' },
  { title: t('Cancelled'), value: 'cancelled' },
]);

const timeframeOptions = computed(() => [
  { title: t('All Time'), value: 'all' },
  { title: t('Last 7 Days'), value: '7d' },
  { title: t('Last 30 Days'), value: '30d' },
  { title: t('Last 90 Days'), value: '90d' },
  { title: t('This Year'), value: 'year' },
]);

// Table headers
const tableHeaders = computed(() => [
  { title: t('Order ID'), key: 'orderId', sortable: true },
  { title: t('Commission'), key: 'commissionAmount', sortable: true },
  { title: t('Rate'), key: 'commissionType', sortable: false },
  { title: t('Order Amount'), key: 'orderAmount', sortable: true },
  { title: t('Status'), key: 'status', sortable: true },
  { title: t('Date'), key: 'createdAt', sortable: true },
  { title: t('Actions'), key: 'actions', sortable: false },
]);

// Methods
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(amount);
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString();
};

const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    pending: 'warning',
    approved: 'info',
    paid: 'success',
    cancelled: 'error',
  };
  return colorMap[status] || 'grey';
};

const loadCommissions = async () => {
  loading.value = true;
  try {
    // TODO: Load from API
    // await affiliateStore.fetchCommissions(affiliateId, filters.value);
    console.log("affiliateStore?.profile:::", affiliateStore?.profile);
    if(!affiliateStore?.profile?.id){
      await affiliateStore.fetchProfile(useAuthStore()?.currentBasicUser?.id);
    }

    await fetchApiData(`affiliate/web/commissions/20/findDataByPagination?order=ASC&page=1&take=10`, {
      headers: {
        'Authorization': `Bearer ${useAuthStore().currentBasicUser?.token}`,
      },
    })?.then((response: any)=>{
      commissions.value = response.data;
    })
  } catch (error) {
    console.error('Error loading commissions:', error);
  } finally {
    loading.value = false;
  }
};

const applyFilters = () => {
  loadCommissions();
};

const exportCommissions = () => {
  // TODO: Implement export functionality
  console.log('Exporting commissions...');
};

const viewCommissionDetails = (commission: any) => {
  selectedCommission.value = commission;
  detailsDialog.value = true;
};

const requestPayment = (commission: any) => {
  // TODO: Implement payment request
  console.log('Requesting payment for commission:', commission.id);
};

// Load data on mount
onMounted(() => {
  loadCommissions();
});

// SEO
useHead({
  title: 'Commission Tracking - Affiliate Dashboard',
  meta: [
    {
      name: 'description',
      content: 'Track your affiliate commissions, earnings, and payment history.'
    }
  ]
});
</script>

<style scoped>
.commission-table :deep(.v-data-table__td) {
  padding: 12px 16px;
}

.gap-2 > * + * {
  margin-left: 8px;
}
</style>
