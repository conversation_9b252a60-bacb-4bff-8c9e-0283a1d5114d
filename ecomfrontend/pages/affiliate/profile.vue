<template>
  <div>
    <!-- <PERSON>er -->
    <div class="mb-6">
      <h1 class="text-h4 font-weight-bold mb-2">
        {{ $t('Affiliate Profile') }}
      </h1>
      <p class="text-body-1 text-medium-emphasis">
        {{ $t('Manage your affiliate information and settings') }}
      </p>
    </div>

    <v-row>
      <!-- Profile Form -->
      <v-col cols="12" lg="8">
                <v-card elevation="2" rounded="lg">
          <v-card-title class="text-h6 font-weight-semibold">
            {{ $t('Profile Information') }}
          </v-card-title>
          
          <v-card-text>
            <v-form @submit.prevent="updateUserProfile" v-model="userFormValid">
              <!-- Personal Information -->
              <h3 class="text-h6 font-weight-semibold mb-4">
                {{ $t('Personal Information') }}
              </h3>
              
              <v-row>
                <v-col cols="12" md="6">
                  <v-label class="text-subtitle-1 font-weight-semibold pb-2">
                    {{ $t('First Name') }} <span class="text-error">*</span>
                  </v-label>
                  <v-text-field
                    v-model="userForm.firstName"
                    :rules="nameRules"
                    required
                    hide-details="auto"
                    class="mb-4"
                  />
                </v-col>
                
                <v-col cols="12" md="6">
                  <v-label class="text-subtitle-1 font-weight-semibold pb-2">
                    {{ $t('Last Name') }} <span class="text-error">*</span>
                  </v-label>
                  <v-text-field
                    v-model="userForm.lastName"
                    :rules="nameRules"
                    required
                    hide-details="auto"
                    class="mb-4"
                  />
                </v-col>
              </v-row>

              <v-label class="text-subtitle-1 font-weight-semibold pb-2">
                {{ $t('Email Address') }} <span class="text-error">*</span>
              </v-label>
              <v-text-field
                v-model="userForm.email"
                :rules="emailRules"
                type="email"
                required
                hide-details="auto"
                class="mb-4"
              />

              <v-label class="text-subtitle-1 font-weight-semibold pb-2">
                {{ $t('Phone Number') }}
              </v-label>
              <v-text-field
                v-model="userForm.phone"
                hide-details="auto"
                class="mb-4"
              />

              <!-- Action Buttons -->
              <div class="d-flex gap-3">
                <v-btn
                  type="submit"
                  color="primary"
                  size="large"
                  :loading="updating"
                  :disabled="!userFormValid"
                  prepend-icon="i-mdi:content-save"
                >
                  {{ $t('Save Changes') }}
                </v-btn>
                
                <v-btn
                  variant="outlined"
                  size="large"
                  @click="resetForm"
                  :disabled="updating"
                >
                  {{ $t('Reset') }}
                </v-btn>
              </div>
            </v-form>
          </v-card-text>
        </v-card>

        <v-card elevation="2" rounded="lg">
          <v-card-title class="text-h6 font-weight-semibold">
            {{ $t('Profile Information') }}
          </v-card-title>
          
          <v-card-text>
            <v-form @submit.prevent="updateProfile" v-model="valid">
              <!-- Personal Information -->
              <h3 class="text-h6 font-weight-semibold mb-4">
                {{ $t('Personal Information') }}
              </h3>

              <v-label class="text-subtitle-1 font-weight-semibold pb-2">
                {{ $t('Website URL') }}
              </v-label>
              <v-text-field
                v-model="form.websiteUrl"
                :rules="urlRules"
                placeholder="https://your-website.com"
                hide-details="auto"
                class="mb-4"
              />

              <v-label class="text-subtitle-1 font-weight-semibold pb-2">
                {{ $t('Bio / About You') }}
              </v-label>
              <v-textarea
                v-model="form.bio"
                :rules="bioRules"
                rows="4"
                placeholder="Tell us about yourself and your audience..."
                hide-details="auto"
                class="mb-6"
              />

              <!-- Social Media Links -->
              <h3 class="text-h6 font-weight-semibold mb-4">
                {{ $t('Social Media Links') }}
              </h3>
              
              <v-row>
                <v-col cols="12" md="6">
                  <v-label class="text-subtitle-1 font-weight-semibold pb-2">
                    {{ $t('Instagram') }}
                  </v-label>
                  <v-text-field
                    v-model="form.socialMedia.instagram"
                    prepend-inner-icon="i-mdi:instagram"
                    placeholder="@username"
                    hide-details="auto"
                    class="mb-4"
                  />
                </v-col>
                
                <v-col cols="12" md="6">
                  <v-label class="text-subtitle-1 font-weight-semibold pb-2">
                    {{ $t('Facebook') }}
                  </v-label>
                  <v-text-field
                    v-model="form.socialMedia.facebook"
                    prepend-inner-icon="i-mdi:facebook"
                    placeholder="facebook.com/username"
                    hide-details="auto"
                    class="mb-4"
                  />
                </v-col>
                
                <v-col cols="12" md="6">
                  <v-label class="text-subtitle-1 font-weight-semibold pb-2">
                    {{ $t('Twitter') }}
                  </v-label>
                  <v-text-field
                    v-model="form.socialMedia.twitter"
                    prepend-inner-icon="i-mdi:twitter"
                    placeholder="@username"
                    hide-details="auto"
                    class="mb-4"
                  />
                </v-col>
                
                <v-col cols="12" md="6">
                  <v-label class="text-subtitle-1 font-weight-semibold pb-2">
                    {{ $t('YouTube') }}
                  </v-label>
                  <v-text-field
                    v-model="form.socialMedia.youtube"
                    prepend-inner-icon="i-mdi:youtube"
                    placeholder="youtube.com/channel/..."
                    hide-details="auto"
                    class="mb-4"
                  />
                </v-col>
                
                <v-col cols="12" md="6">
                  <v-label class="text-subtitle-1 font-weight-semibold pb-2">
                    {{ $t('TikTok') }}
                  </v-label>
                  <v-text-field
                    v-model="form.socialMedia.tiktok"
                    prepend-inner-icon="i-mdi:music-note"
                    placeholder="@username"
                    hide-details="auto"
                    class="mb-4"
                  />
                </v-col>
              </v-row>

              <!-- Payment Information -->
              <h3 class="text-h6 font-weight-semibold mb-4">
                {{ $t('Payment Information') }}
              </h3>
              
              <v-label class="text-subtitle-1 font-weight-semibold pb-2">
                {{ $t('Preferred Payment Method') }}
              </v-label>
              <v-select
                v-model="form.paymentMethod"
                :items="paymentMethodOptions"
                hide-details="auto"
                class="mb-4"
              />

              <v-label class="text-subtitle-1 font-weight-semibold pb-2">
                {{ $t('Payment Details') }}
              </v-label>
              <v-textarea
                v-model="form.paymentDetails"
                rows="3"
                placeholder="Enter your payment details (bank account, PayPal email, etc.)"
                hide-details="auto"
                class="mb-6"
              />

              <!-- Action Buttons -->
              <div class="d-flex gap-3">
                <v-btn
                  type="submit"
                  color="primary"
                  size="large"
                  :loading="updating"
                  :disabled="!valid"
                  prepend-icon="i-mdi:content-save"
                >
                  {{ $t('Save Changes') }}
                </v-btn>
                
                <v-btn
                  variant="outlined"
                  size="large"
                  @click="resetForm"
                  :disabled="updating"
                >
                  {{ $t('Reset') }}
                </v-btn>
              </div>
            </v-form>
          </v-card-text>
        </v-card>
      </v-col>

      <!-- Profile Summary & Status -->
      <v-col cols="12" lg="4">
        <!-- Profile Status -->
        <v-card elevation="2" rounded="lg" class="mb-6">
          <v-card-title class="text-h6 font-weight-semibold">
            {{ $t('Account Status') }}
          </v-card-title>
          <v-card-text>
            <div class="text-center mb-4">
              <v-chip
                :color="getStatusColor(profileStatus)"
                size="large"
                variant="tonal"
                :prepend-icon="getStatusIcon(profileStatus)"
              >
                {{ $t(profileStatus) }}
              </v-chip>
            </div>
            
            <div class="status-details">
              <div class="d-flex align-center justify-space-between mb-2">
                <span class="text-body-2">{{ $t('Member Since') }}</span>
                <span class="text-body-2 font-weight-medium">
                  {{ formatDate(memberSince) }}
                </span>
              </div>
              
              <div class="d-flex align-center justify-space-between mb-2">
                <span class="text-body-2">{{ $t('Commission Rate') }}</span>
                <span class="text-body-2 font-weight-medium">
                  {{ commissionRate }}%
                </span>
              </div>
              
              <div class="d-flex align-center justify-space-between">
                <span class="text-body-2">{{ $t('Total Orders') }}</span>
                <span class="text-body-2 font-weight-medium">
                  {{ totalOrders }}
                </span>
              </div>
            </div>
          </v-card-text>
        </v-card>

        <!-- Quick Stats -->
        <v-card elevation="2" rounded="lg" class="mb-6">
          <v-card-title class="text-h6 font-weight-semibold">
            {{ $t('Quick Stats') }}
          </v-card-title>
          <v-card-text>
            <div class="stats-grid">
              <div class="stat-item text-center mb-3">
                <h3 class="text-h5 font-weight-bold text-success">
                  {{ formatCurrency(totalEarnings) }}
                </h3>
                <p class="text-body-2 text-medium-emphasis">
                  {{ $t('Total Earnings') }}
                </p>
              </div>
              
              <div class="stat-item text-center mb-3">
                <h3 class="text-h5 font-weight-bold text-primary">
                  {{ totalClicks.toLocaleString() }}
                </h3>
                <p class="text-body-2 text-medium-emphasis">
                  {{ $t('Total Clicks') }}
                </p>
              </div>
              
              <div class="stat-item text-center">
                <h3 class="text-h5 font-weight-bold text-info">
                  {{ conversionRate }}%
                </h3>
                <p class="text-body-2 text-medium-emphasis">
                  {{ $t('Conversion Rate') }}
                </p>
              </div>
            </div>
          </v-card-text>
        </v-card>

        <!-- Help & Support -->
        <v-card elevation="2" rounded="lg">
          <v-card-title class="text-h6 font-weight-semibold">
            {{ $t('Need Help?') }}
          </v-card-title>
          <v-card-text>
            <p class="text-body-2 text-medium-emphasis mb-4">
              {{ $t('Contact our affiliate support team for assistance') }}
            </p>
            
            <div class="d-flex flex-column gap-2">
              <v-btn
                variant="outlined"
                color="primary"
                block
                prepend-icon="i-mdi:email"
                href="mailto:<EMAIL>"
              >
                {{ $t('Email Support') }}
              </v-btn>
              
              <v-btn
                variant="outlined"
                color="info"
                block
                prepend-icon="i-mdi:help-circle"
                :to="localePath('/help/affiliate-guide')"
              >
                {{ $t('Affiliate Guide') }}
              </v-btn>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </div>
</template>

<script setup lang="ts">
import { storeToRefs } from 'pinia';
import { computed } from 'vue';


definePageMeta({
  layout: 'affiliate',
  middleware: ['auth', 'affiliate']
});

const { t } = useI18n();
const localePath = useLocalePath();
const affiliateStore = useAffiliateStore();
const authStore = useAuthStore();

// State
const valid = ref(false);
const userFormValid = ref(false);
const updating = ref(false);
const { getAffiliateProfile } = storeToRefs(affiliateStore);
const { currentBasicUser } = storeToRefs(authStore);

// Mock profile data
const profileStatus = ref('active');
const memberSince = ref('2024-01-01T00:00:00Z');
const commissionRate = ref(10);
const totalOrders = ref(45);
const totalEarnings = ref(2450.75);
const totalClicks = ref(15420);
const conversionRate = ref(3.2);

// Form data

const form = ref({
  websiteUrl: '',
  bio: '',
  socialMedia: {
    instagram: '',
    facebook: '',
    twitter: '',
    youtube: '',
    tiktok: '',
  },
  paymentMethod: '',
  paymentDetails: '',
});

const userForm = ref({
  firstName: '',
  lastName: '',
  email: '',
  phone: '',
})

// Options
const paymentMethodOptions = computed(() => [
  { title: t('PayPal'), value: 'paypal' },
  { title: t('Bank Transfer'), value: 'bank_transfer' },
  { title: t('Stripe'), value: 'stripe' },
  { title: t('Other'), value: 'other' },
]);

// Validation rules
const nameRules = computed(() => [
  (v: string) => !!v || t('This field is required'),
  (v: string) => v.length >= 2 || t('Must be at least 2 characters'),
]);

const emailRules = computed(() => [
  (v: string) => !!v || t('Email is required'),
  (v: string) => /.+@.+\..+/.test(v) || t('Email must be valid'),
]);

const urlRules = computed(() => [
  (v: string) => !v || /^https?:\/\/.+/.test(v) || t('Must be a valid URL'),
]);

const bioRules = computed(() => [
  (v: string) => !v || v.length <= 500 || t('Bio must be less than 500 characters'),
]);

// Methods
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(amount);
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString();
};

const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    active: 'success',
    pending: 'warning',
    suspended: 'error',
    rejected: 'error',
  };
  return colorMap[status] || 'grey';
};

const getStatusIcon = (status: string) => {
  const iconMap: Record<string, string> = {
    active: 'i-mdi:check-circle',
    pending: 'i-mdi:clock-outline',
    suspended: 'i-mdi:pause-circle',
    rejected: 'i-mdi:close-circle',
  };
  return iconMap[status] || 'i-mdi:help-circle';
};

const updateProfile = async () => {
  if (!valid.value) return;

  updating.value = true;
  
  try {
    await affiliateStore
      ?.updateProfile(form.value)
      ?.then(() => {
        updateForm();
      })
    console.log('Profile updated successfully');
  } catch (error) {
    console.error('Error updating profile:', error);
  } finally {
    updating.value = false;
  }
};

const updateUserProfile = async () => {
  if(!userFormValid.value) return;

  updating.value = true;
  
  try {
    await authStore
      ?.updateUserProfile(authStore.currentBasicUser?.id, userForm.value)
      ?.then(() => {
        // updateForm();
      })
    console.log('Profile updated successfully');
  } catch (error) {
    console.error('Error updating profile:', error);
  } finally {
    updating.value = false;
  }
}

const resetForm = () => {
  // Reset form to original values
  // TODO: Load original values from store
};

const updateForm = () => 
{ 
  form.value.firstName = getAffiliateProfile?.value?.user?.firstName;
  form.value.lastName = getAffiliateProfile?.value?.user?.lastName;
  form.value.email = getAffiliateProfile?.value?.user?.email;
  form.value.phone = getAffiliateProfile?.value?.user?.phone;
  form.value.websiteUrl = getAffiliateProfile?.value?.websiteUrl;
  form.value.bio = getAffiliateProfile?.value?.bio;
  form.value.paymentMethod = getAffiliateProfile?.value?.paymentMethod;
  form.value.paymentDetails = getAffiliateProfile?.value?.paymentDetails;
  if(getAffiliateProfile?.value?.socialMedia){
    form.value.socialMedia.instagram = getAffiliateProfile?.value?.socialMedia?.instagram;
    form.value.socialMedia.facebook = getAffiliateProfile?.value?.socialMedia?.facebook;
    form.value.socialMedia.twitter = getAffiliateProfile?.value?.socialMedia?.twitter;
    form.value.socialMedia.youtube = getAffiliateProfile?.value?.socialMedia?.youtube;
    form.value.socialMedia.tiktok = getAffiliateProfile?.value?.socialMedia?.tiktok;
  }
}

const userUserForm = () => {
  userForm.value.firstName = currentBasicUser?.value?.firstName;
  userForm.value.lastName = currentBasicUser?.value?.lastName;
  userForm.value.email = currentBasicUser?.value?.email;
  userForm.value.phone = getAffiliateProfile?.value?.user?.phone; 
}

// Load profile data on mount
onMounted(async () => {
  // TODO: Load profile data from store
  await affiliateStore.fetchProfile(authStore?.currentUserProfile?.id)?.then(() => {
    updateForm();
  })
  userUserForm();
});

// SEO
useHead({
  title: 'Profile Management - Affiliate Dashboard',
  meta: [
    {
      name: 'description',
      content: 'Manage your affiliate profile information, social media links, and payment details.'
    }
  ]
});
</script>

<style scoped>
.gap-3 > * + * {
  margin-left: 12px;
}

.gap-2 > * + * {
  margin-left: 8px;
}

.status-details {
  border-top: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
  padding-top: 16px;
}
</style>
