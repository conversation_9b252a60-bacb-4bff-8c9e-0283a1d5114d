<template>
  <div>
    <v-container>
      <h1 class="text-center py-4">{{ data?.title }}</h1>
      <LazyGeneralPageBreadcrumb
        :breadcrumbs="breadcrumbs"
        style="min-height: 40px"
      />
    </v-container>
    <v-container v-if="data?.pageContent" class="mb-5">
      <span v-html="data?.pageContent"></span>
    </v-container>
  </div>
</template>

<script setup lang="ts">
import { fetchData } from "../composables/getApi";
import { getLocalizeName, getLocalizeDescription } from "~/utils/functions";
import { createError, useRoute } from "nuxt/app";
import { useCurrentHost } from "../composables/useCurrentHost";
import { computed, ref } from "vue";

const localePath = useLocalePath();
const { t } = useI18n();
const route = useRoute();
const { getDomaLanguageId, getCurrentHost } = useCurrentHost();

const languageId = getDomaLanguageId();
const host = getCurrentHost();

const breadcrumbs = computed(() => {
  return [
    {
      text: t("Home"),
      disabled: false,
      to: localePath("/"),
    },
    {
      text: data?.title,
      disabled: true,
      to: "",
    },
  ];
});

const pageLoading = ref(false);
const slug = computed(() => route.params?.slug);

const { data, error, refresh } = await useAsyncData(
  `quickLinkIndex-${slug.value}`,
  async () => {
    var title = null;
    var pageContent = null;
    const response = await fetchData(
      `quickLinks/findBySlug/${route.params?.slug}`,
      {}
    );
    if (response.isSuccess) {
      title = getLocalizeName(
        languageId,
        response?.data?.locale,
        response?.data?.name
      );
      pageContent = getLocalizeDescription(
        languageId,
        response.data?.locale,
        response.data?.details
      );
    }
    return {
      title,
      pageContent,
    };
  },
  {
    watch: [slug.value],
  }
);

onMounted(async () => {
  if (!data.value) {
    throw createError({
      statusCode: 404,
      statusMessage: "Page Not Found",
    });
  }
});

// SEO
useHead(() => ({
  title: data?.value?.title || "Page",
  meta: [
    {
      name: "description",
      content: data?.value?.pageContent?.replace(/<\/?[^>]+(>|$)/g, "").slice(0, 160) || "View our content page"
    },
    {
      name: "robots",
      content: "index, follow"
    },
    {
      property: "og:title",
      content: data?.value?.title || "Page"
    },
    {
      property: "og:description",
      content: data?.value?.pageContent?.replace(/<\/?[^>]+(>|$)/g, "").slice(0, 160)
    },
    {
      property: "og:type",
      content: "website"
    },
    {
      property: "og:url",
      content: `https://${host}${route.fullPath}`
    }
  ]
}));
</script>
<style>
ul:not(.v-breadcrumbs),
ol:not(.v-breadcrumbs) {
  margin-left: 1.5em; /* Base margin for the first level */
}

ul ul,
ul ol,
ol ul,
ol ol {
  margin-left: 2em; /* Increase margin for the second level */
}

ul ul ul,
ul ul ol,
ul ol ul,
ul ol ol,
ol ul ul,
ol ul ol,
ol ol ul,
ol ol ol {
  margin-left: 3em; /* Increase margin for the third level */
}

/* Add more levels if needed */
ul ul ul ul,
ul ul ul ol,
ul ul ol ul,
ul ul ol ol,
ul ol ul ul,
ul ol ul ol,
ul ol ol ul,
ul ol ol ol,
ol ul ul ul,
ol ul ul ol,
ol ul ol ul,
ol ul ol ol,
ol ol ul ul,
ol ol ul ol,
ol ol ol ul,
ol ol ol ol {
  margin-left: 4em; /* Increase margin for the fourth level */
}

p {
  margin-bottom: 10px;
}
</style>
