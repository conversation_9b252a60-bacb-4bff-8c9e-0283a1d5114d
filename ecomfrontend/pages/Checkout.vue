<template>
  <section>
    <template v-if="pageLoading"> {{ $t("Loading") }}... </template>
    <v-container
      v-else-if="cartItems?.length > 0"
      fluid
      class="bg-blue-grey-lighten-5 h-100"
    >
      <v-row class="h-100">
        <v-col cols="12" md="7">
          <v-form class="bg-white rounded-xl mb-2">
            <v-card
              variant="outlined"
              :rounded="0"
              class="border-0 pa-3 overflow-hidden"
            >
              <v-card-title class="d-flex flex-column flex-sm-row justify-space-between pa-0 mb-4">
                <h5 class="text-h6 mb-0 text-capitalize font-weight-bold">
                  {{ $t("Shipping information") }}
                </h5>
                <p
                  class="text-subtitle-1 font-weight-bold"
                  v-if="!authenticated"
                >
                  {{ $t("Have an account") }}
                  <router-link :to="localePath('/auth/login')">{{
                    $t("Login")
                  }}</router-link>
                </p>
              </v-card-title>
              <v-card-text class="pa-0">
                <v-row>
                  <v-col cols="12" lg="6" class="py-1">
                    <v-text-field
                      v-model="shippingAddress.firstName"
                      bg-color="#fff"
                      density="compact"
                      :error-messages="
                        v2$.firstName.$errors.map((e) => $t(e.$message))
                      "
                      :hide-details="v2$.firstName.$errors?.length === 0"
                      rounded
                    >
                      <template #label>
                        {{
                          countryCode === "BD"
                            ? $t("Name")
                            : $t("First Name")
                        }}<span class="text-error"><strong>*</strong></span>
                      </template>
                    </v-text-field>
                  </v-col>
                  <v-col
                    v-if="countryCode != 'BD'"
                    cols="12"
                    lg="6"
                    class="py-1"
                  >
                    <v-text-field
                      v-model="shippingAddress.lastName"
                      bg-color="#fff"
                      density="compact"
                      :error-messages="
                        v2$.lastName.$errors.map((e) => $t(e.$message))
                      "
                      :hide-details="v2$.lastName.$errors?.length === 0"
                      rounded
                    >
                      <template #label>
                        {{ $t("Last Name")
                        }}<span class="text-error"><strong>*</strong></span>
                      </template></v-text-field
                    >
                  </v-col>
                  <v-col cols="12" lg="6" class="py-1">
                    <v-text-field
                      v-model="shippingAddress.email"
                      @update:modelValue="updateBillingEmailFCoupon"
                      bg-color="#fff"
                      density="compact"
                      :error-messages="
                        v2$.email.$errors.map((e) => $t(e.$message))
                      "
                      :hide-details="v2$.email.$errors?.length === 0"
                      rounded
                      required
                      v-click-outside="handleSubscription"
                    >
                      <template #label>
                        {{ $t("Email")
                        }}<span class="text-error"><strong>*</strong></span>
                      </template></v-text-field
                    >
                  </v-col>
                  <v-col
                    v-if="countryCode != 'BD'"
                    cols="12"
                    lg="6"
                    class="py-1"
                  >
                    <v-select
                      :items="countries"
                      v-model="shippingAddress.countryId"
                      density="compact"
                      :hide-details="v2$.countryId.$errors?.length === 0"
                      item-title="name"
                      item-value="id"
                      @update:modelValue="selectedCountry($event, 'shipping')"
                      placeholder="Select Country"
                      bg-color="#fff"
                      :error-messages="
                        v2$.countryId.$errors.map((e) => $t(e.$message))
                      "
                      required
                      @input="v2$.countryId.$touch"
                      @blur="v2$.countryId.$touch"
                      rounded
                    />
                  </v-col>
                  <v-col cols="12" lg="6" class="py-1">
                    <v-row no-gutters>
                      <v-col cols="3" sm="3" class="d-flex align-center py-1">
                        <v-select
                          :items="countryMobileCodesArr"
                          density="compact"
                          item-title="countryCode"
                          item-value="code"
                          required
                          :error-messages="
                            v2$.countryCode.$errors.map((e) => $t(e.$message))
                          "
                          @input="v2$.countryCode.$touch"
                          @blur="v2$.countryCode.$touch"
                          :hide-details="v2$.phone.$errors?.length === 0"
                          v-model="shippingAddress.countryCode"
                          bg-color="#fff"
                          rounded
                          class="text-caption"
                          :disabled="countryCode === 'BD'"
                        >
                          <template #prepend-inner>
                            <span class="text-grey-darken-1 text-caption pl-2 pr-1"
                              >+{{ shippingAddress.countryCode }}</span
                            >
                          </template>
                          <template #selection="{ item }">

                          </template>
                        </v-select>
                        <h6 class="mx-2">-</h6>
                      </v-col>
                      <v-col cols="9" sm="9" class="py-1">
                        <v-text-field
                          required
                          :hide-details="v2$.phone.$errors?.length === 0"
                          bg-color="#fff"
                          v-model="shippingAddress.phone"
                          :error-messages="
                            v2$.phone.$errors.map((e) => $t(e.$message))
                          "
                          rounded
                          density="compact"
                          ><template #label>
                            {{ $t("Phone")
                            }}<span class="text-error"
                              ><strong>*</strong></span
                            >
                          </template>
                          <template #prepend-inner>
                            <!-- <span class="text-grey-darken-1 text-caption pl-2 pr-1">+{{ shippingAddress.countryCode }}</span> -->
                          </template>
                        </v-text-field>
                      </v-col>
                    </v-row>
                  </v-col>

                  <v-col v-if="countryCode === 'RO'" cols="12" lg="3" class="py-1">
                    <v-select
                      v-model="shippingAddress.state"
                      :items="countryAddressData?.ro?.state"
                      bg-color="#fff"
                      :hide-details="v2$.state.$errors?.length === 0"
                      density="compact"
                      :error-messages="
                        v2$.state.$errors.map((e) => $t(e.$message))
                      "
                      rounded
                      ><template #label>
                        {{ $t("State")
                        }}<span class="text-error"><strong>*</strong></span>
                      </template></v-select
                    >
                  </v-col>
                  <v-col cols="12" lg="3" class="py-1">
                    <v-text-field
                      v-model="shippingAddress.city"
                      bg-color="#fff"
                      :hide-details="v2$.city.$errors?.length === 0"
                      density="compact"
                      :error-messages="
                        v2$.city.$errors.map((e) => $t(e.$message))
                      "
                      rounded
                      ><template #label>
                        {{ $t("City")
                        }}<span class="text-error"><strong>*</strong></span>
                      </template></v-text-field
                    >
                  </v-col>
                  <v-col
                    v-if="countryCode != 'BD'"
                    cols="12"
                    lg="3"
                    class="py-1"
                  >
                    <v-text-field
                      v-model="shippingAddress.code"
                      bg-color="#fff"
                      :hide-details="v2$.code.$errors?.length === 0"
                      density="compact"
                      :error-messages="
                        v2$.code.$errors.map((e) => $t(e.$message))
                      "
                      rounded
                    >
                      <template #label>
                        {{ $t("Postal Code")
                        }}<span class="text-error"><strong>*</strong></span>
                      </template>
                    </v-text-field>
                  </v-col>
                  <v-col
                    cols="12"
                    :lg="countryCode === 'BD' ? '12' : '12'"
                    class="py-1"
                  >
                    <v-textarea
                      v-model="shippingAddress.address"
                      bg-color="#fff"
                      :hide-details="v2$.address.$errors?.length === 0"
                      density="compact"
                      rows="1"
                      :error-messages="
                        v2$.address.$errors.map((e) => $t(e.$message))
                      "
                      rounded
                    >
                      <template #label>
                        {{ $t("Address")
                        }}<span class="text-error"><strong>*</strong></span>
                      </template></v-textarea
                    >
                  </v-col>
                </v-row>

                <div>
                  <v-checkbox
                    v-model="hasBilling"
                    color="success"
                    class="ps-0"
                    @update:modelValue="hasShippingMethod"
                    hide-details
                  >
                    <template v-slot:label>
                      <div class="text-body-1">
                        {{ $t("Shipping and Billing addresses are different") }}
                      </div>
                    </template>
                  </v-checkbox>
                </div>
                <div v-if="hasBilling" class="pb-2">
                  <!-- <h5 class="text-h5 mb-3">{{ $t("Billing information") }}</h5> -->
                  <v-row>
                    <v-col cols="12" lg="6" class="py-1">
                      <v-text-field
                        v-model="billingState.firstName"
                        required
                        rounded
                        bg-color="#fff"
                        :error-messages="
                          v$.firstName.$errors.map((e) => $t(e.$message))
                        "
                        :hide-details="v$.firstName.$errors?.length === 0"
                        density="compact"
                      >
                        <template #label>
                          {{ countryCode === "BD" ? $t("Name") : $t("First Name")
                          }}<span class="text-error"><strong>*</strong></span>
                        </template>
                      </v-text-field>
                    </v-col>
                    <v-col
                      v-if="countryCode != 'BD'"
                      cols="12"
                      lg="6"
                      class="py-1"
                    >
                      <v-text-field
                        v-model="billingState.lastName"
                        required
                        :error-messages="
                          v$.lastName.$errors.map((e) => $t(e.$message))
                        "
                        :hide-details="v$.lastName.$errors?.length === 0"
                        rounded
                        bg-color="#fff"
                        density="compact"
                      >
                        <template #label>
                          {{ $t("Last Name")
                          }}<span class="text-error"><strong>*</strong></span>
                        </template></v-text-field
                      >
                    </v-col>
                    <v-col cols="12" lg="6" class="py-1">
                      <v-text-field
                        v-model="billingState.email"
                        bg-color="#fff"
                        density="compact"
                        :error-messages="
                          v$.email.$errors.map((e) => $t(e.$message))
                        "
                        required
                        :hide-details="v$.email.$errors?.length === 0"
                        @input="v$.email.$touch"
                        @blur="v$.email.$touch"
                        rounded
                      >
                        <template #label>
                          {{$t("Email")}}
                          <!-- <span class="text-error"><strong>*</strong></span> -->
                        </template></v-text-field
                      >
                    </v-col>
                    <v-col
                      v-if="countryCode != 'BD'"
                      cols="12"
                      lg="6"
                      class="py-1"
                    >
                      <!-- <v-label class="font-weight-medium">{{ $t("Country") }}<span class="text-error">*</span></v-label> -->
                      <v-select
                        :items="domaCountryListNonDuplicate"
                        item-title="name"
                        item-value="id"
                        density="compact"
                        @update:modelValue="selectedCountry($event, 'billing')"
                        v-model="billingState.countryId"
                        placeholder="Select Country"
                        bg-color="#fff"
                        :hide-details="v$.countryId.$errors?.length === 0"
                        :error-messages="
                          v$.countryId.$errors.map((e) => $t(e.$message))
                        "
                        required
                        @input="v$.countryId.$touch"
                        @blur="v$.countryId.$touch"
                        rounded
                      />
                    </v-col>

                    <v-col cols="12" lg="6" class="py-1">
                      <v-row no-gutters>
                        <v-col cols="3" sm="3" class="d-flex align-center py-1">
                          <v-select
                            :items="allCountryMobileCodesArr"
                            density="compact"
                            item-title="phoneCode"
                            item-value="phoneCode"
                            required
                            :error-messages="
                              v$.countryCode.$errors.map((e) => $t(e.$message))
                            "
                            @input="v$.countryCode.$touch"
                            @blur="v$.countryCode.$touch"
                            :hide-details="v$.phone.$errors?.length === 0"
                            v-model="billingState.countryCode"
                            bg-color="#fff"
                            rounded
                            class="text-caption"
                            :disabled="countryCode === 'BD'"
                          >
                            <template #prepend-inner>
                              <span class="text-grey-darken-1 text-caption pl-2 pr-1"
                                >{{ billingState.countryCode }}</span
                              >
                            </template>
                            <template #selection="{ item }">

                            </template>
                          </v-select>
                          <h6 class="mx-2">-</h6>
                        </v-col>
                        <v-col cols="9" sm="9" class="py-1">
                          <v-text-field
                            required
                            :hide-details="v$.phone.$errors?.length === 0"
                            bg-color="#fff"
                            v-model="billingState.phone"
                            :error-messages="
                              v$.phone.$errors.map((e) => $t(e.$message))
                            "
                            rounded
                            density="compact"
                          >
                            <template #label>
                              {{ $t("Phone")
                              }}
                              <!-- <span class="text-error"><strong>*</strong></span> -->
                            </template>

                            <template #prepend-inner>
                              <!-- <span class="text-grey-darken-1 text-caption pl-2 pr-1">+{{ billingState.countryCode }}</span> -->
                            </template>
                          </v-text-field>
                        </v-col>
                      </v-row>
                    </v-col>

                    <v-col
                      v-if="countryCode === 'RO'"
                      :lg="'3'"
                      cols="12"
                      class="py-1"
                    >
                      <v-select
                        bg-color="#fff"
                        v-model="billingState.state"
                        :items="countryAddressData?.ro?.state"
                        required
                        :hide-details="v$.state.$errors.length === 0"
                        density="compact"
                        :error-messages="
                          v$?.state?.$errors?.map((e) => $t(e.$message))
                        "
                        rounded
                      >
                        <template #label>
                          {{ $t("State")
                          }}<span class="text-error"><strong>*</strong></span>
                        </template></v-select
                      >
                    </v-col>
                    <v-col
                      :lg="countryCode === 'BD' ? '6' : '3'"
                      cols="12"
                      class="py-1"
                    >
                      <v-text-field
                        bg-color="#fff"
                        v-model="billingState.city"
                        required
                        :hide-details="v$.city.$errors?.length === 0"
                        density="compact"
                        :error-messages="
                          v$.city.$errors.map((e) => $t(e.$message))
                        "
                        rounded
                      >
                        <template #label>
                          {{ $t("City")
                          }}<span class="text-error"><strong>*</strong></span>
                        </template></v-text-field
                      >
                    </v-col>
                    <v-col
                      v-if="countryCode != 'BD'"
                      cols="12"
                      lg="3"
                      class="py-1"
                    >
                      <v-text-field
                        required
                        bg-color="#fff"
                        v-model="billingState.code"
                        density="compact"
                        :error-messages="
                          v$.code.$errors.map((e) => $t(e.$message))
                        "
                        :hide-details="v$.code.$errors?.length === 0"
                        rounded
                      >
                        <template #label>
                          {{ $t("Postal Code")
                          }}<span class="text-error"><strong>*</strong></span>
                        </template>
                      </v-text-field>
                    </v-col>
                    <v-col cols="12" lg="12" class="py-1">
                      <v-textarea
                        bg-color="#fff"
                        v-model="billingState.address"
                        :hide-details="v$.address.$errors?.length === 0"
                        density="compact"
                        :error-messages="
                          v$.address.$errors.map((e) => $t(e.$message))
                        "
                        rows="1"
                        rounded
                      >
                        <template #label>
                          {{ $t("Address")
                          }}<span class="text-error"><strong>*</strong></span>
                        </template></v-textarea
                      >
                    </v-col>
                  </v-row>
                </div>
              </v-card-text>
            </v-card>
          </v-form>
          <LazyCheckoutPayment
            ref="lazyCheckoutPayment"
            @confirmOrder="orderConfirm"
            @selectedStripe="selectedStripe"
            :country-code="globals?.code"
            @returnToShippingAndBilling="step = 'your-cart'"
          />
        </v-col>
        <v-col cols="12" md="5">
          <v-sheet class="rounded-xl py-2 px-1" color="blue-grey-lighten-4">
            <LazyCheckoutCart :items="cartItems" @reset="resetCartQtyHndler" />
            <LazyCheckoutOrderSummary
              :use-of-coupon="true"
              :money-back="true"
              :shopping-cart-store="cartStore"
              :showShipping="true"
            />
          </v-sheet>
          <div>
            <client-only>
              <h6></h6>
              <LazyCheckoutLowerCartItemRelatedProduct/>
            </client-only>
          </div>
        </v-col>
      </v-row>
    </v-container>
    <LazyCartEmpty v-else />
    <v-overlay class="align-center justify-center" :model-value="loading">
      <v-progress-circular
        color="primary"
        size="64"
        indeterminate
      ></v-progress-circular>
    </v-overlay>
    <client-only>
      <LazyOtpDialog v-model:modelValue="showOTPDialog" :phoneNumber="shippingAddress?.phone" :cartStore="cartStore" @triggerVerification="handleOTPVerification"/>
    </client-only>
    <v-dialog
      v-model="showRedirectModal"
      max-width="600"
      scrollable
    >
      <v-card rounded="xl">
        <v-card-title class="text-h5">
          {{ $t("Country") }}
        </v-card-title>
        <v-card-text>
          <h6 class="text-h6 font-weight-bold">Please visit for {{ requestShippingCountry?.name }}</h6>
          <a :href="`https://${ requestShippingCountry?.domain}/Checkout?cartUUID=${cartStore?.cartUUID}`">{{ requestShippingCountry?.domain }}</a> 
        </v-card-text>
      </v-card>
    </v-dialog>
  </section>
</template>
<script setup lang="ts">
import { encrypt } from "../utils/crypto-functions";
import { storeToRefs } from "pinia";
import { useTagManager } from "../composables/useTagManager";
import { fetchApiData } from "../utils/apiHelpers";
import { useSnackbar } from "../composables/useSnackbar";
import { useHead, useRuntimeConfig } from "nuxt/app";
import { useGlobalStore } from "../stores/global";

import { useVuelidate } from "@vuelidate/core";
import { email, required, helpers, numeric } from "@vuelidate/validators";
import {
  getSelectedCountryCode,
  handleClearLocalStorage,
  showSnackbarResponse,
} from "../utils/functions";
import { computed, ref, watch } from "vue";
import countryMobileCodes from "../utils/countryMobileCodes";
import { useSqualoStore } from "../stores/squalo";
import { isInvalidPostcodeByCountryId } from "../utils/zipcode-validator";
import RevolutCheckout, {type Mode} from "@revolut/checkout"
import { useWhatsappStore } from "../stores/whatsapp";
import debounce from 'lodash.debounce';
import { useAuthStore } from "../stores/auth";
import { countryAddressData } from "../utils/state-data";
import { useCurrentHost } from "../composables/useCurrentHost";

const squaloStore = useSqualoStore();
const whatsappStore = useWhatsappStore();

const route = useRoute();
const invoice = computed(() => route.query.invoiceNo);
const status = computed(() => route.query.status);
const { t, locale} = useI18n();

const { gtmCartViewEvent, gtmProductPurchaseEvent, gtmBeginCheckoutEvent } =
  useTagManager();
const stripeElements = ref({});
const pageLoading = ref(true);
const cartStore = useShoppingCartStore();
const { authenticated, currentUserProfile } = storeToRefs(useAuthStore());
const globalStore = useGlobalStore();
const { globals } = storeToRefs(globalStore);
const couponStore = useCouponStore();
const paymentStore = usePaymentStore();
const { showSnackbar } = useSnackbar();
const localePath = useLocalePath();
const countryStore = useCountryStore();

const cartItems = computed(() => cartStore.cart);

const loading = ref(false);
const step = ref("your-cart");
const router = useRouter();
const enableWhatsappInvoice = ref(true);

const {getDomaCountryCode, getDomaCountryCurrency, getDomaCountryId, getDomaLanguageId, getDomaCountryListNonDuplicate, getDomaCountryListNonEuroCurrency } = useCurrentHost();
const languageId = getDomaLanguageId();
const currnetCountryId = getDomaCountryId();
const domaCountryListNonDuplicate = getDomaCountryListNonDuplicate();
const domaCountryListNonEuroCurrency = getDomaCountryListNonEuroCurrency();
const domaCountryCurrency = getDomaCountryCurrency();
const domaCountryCode = getDomaCountryCode();
const lazyCheckoutPayment = ref();

const requestShippingCountry = ref<any>(null);


const config = useRuntimeConfig();

const isEmailFieldOutsideClick = ref(false);
const showRedirectModal = ref(false);

const showOTPDialog = ref(false);
const selectedStripe = (data: any) => {
  stripeElements.value = data;
};

const getGtmPayload = () => {
  const _carts = cartStore.cart;
  const currency = _carts.find((it) => !!it.currency)?.currency;

  return _carts.map((item) => ({
    id: item.productId?.id,
    name: item.name,
    unitPrice: item.unitPrice,
    quantity: item.quantity,
    color: item?.color?.name,
    size: item?.size?.name,
    currency: currency,
    brand: item?.brand,
    discountPrice: item?.discountPrice || 0,
    sku: item.sku,
    categoriesArr: item?.categoriesArr,
  }));
};

const triggerPurchaseEvent = (order: any) => {
  const mapItems = getGtmPayload();
  if (mapItems?.length) {
    gtmProductPurchaseEvent(
      {
        transaction_id: order?.invoiceNumber,
        tax: cartStore?.tax || globals.value.taxPercentage,
        shipping: cartStore?.shippingCharge || globals.value.shippingCharge,
      },
      mapItems,
      {
        email: cartStore?.shippingAddress?.email,
        phone: cartStore?.shippingAddress?.phone,
        address: {
          first_name: cartStore?.shippingAddress?.firstName,
          last_name: cartStore?.shippingAddress?.lastName,
          state: cartStore?.shippingAddress?.state,
          city: cartStore?.shippingAddress?.city,
          region: cartStore?.shippingAddress?.address,
          postal_code: cartStore?.shippingAddress?.code,
          country: 
            countryStore.countries.find(
              (el) => el.id === cartStore?.shippingAddress?.countryId
            ).name,
        },
      },
      globalStore.clientIP
    );
  }
};


const sendWhatsappDocument = (userNumber: any, documentFile: any)=>{
  // Invoke whatsapp message
  let formData = new FormData();
  formData.append('msisdn', userNumber);
  formData.append('document', documentFile);
  formData.append(
    'caption', 
    "Greetings from Pantoneclo! Your invoice is attached and has also been sent to your email. We’ll notify you once your package has been shipped."
  );

  whatsappStore
    ?.sendDocument(formData)
    ?.then((response)=>{
    })
    ?.catch((e)=>{
      console.log(e);
    })
}


const sendInvoiceToWhatsapp = async (phoneNumber: String, invoiceNumber: Number)=>{
  try{
    const response = await fetchApiData(`productOrder/web/stream-invoice/${invoiceNumber}`,
      {
        method: "GET",
      }
    )
    // const blob = new Blob([response?.data], { type: "application/pdf" });
    // Convert Blob to File
    const file = new File([response], `invoice-${invoiceNumber}.pdf`, {
      type: "application/pdf",
    });
    sendWhatsappDocument(phoneNumber, file);
  }
  catch(e){
    console.log("Whatsapp sending failed:: ", e);
  }
}

const moveToPaymentStep = () => {
  // cartStore.updateCartApi();

  // step.value = "payment";
  const mapItems = getGtmPayload();
  const userCheckoutData = {
    email: encrypt(cartStore?.shippingAddress?.email, config?.public?.secretKey),
    phone: encrypt(cartStore?.shippingAddress?.phone, config?.public?.secretKey),
    address: {
      first_name: encrypt(
        cartStore?.shippingAddress?.firstName,
        config?.public?.secretKey
      ),
      last_name: encrypt(
        cartStore?.shippingAddress?.lastName,
        config?.public?.secretKey
      ),
      state: encrypt(cartStore?.shippingAddress?.state, config?.public?.secretKey),
      city: encrypt(cartStore?.shippingAddress?.city, config?.public?.secretKey),
      region: encrypt(
        cartStore?.shippingAddress?.address,
        config?.public?.secretKey
      ),
      postal_code: encrypt(
        cartStore?.shippingAddress?.code,
        config?.public?.secretKey
      ),
      country: encrypt(
        countryStore.countries.find(
          (el) => el.id === cartStore?.shippingAddress?.countryId
        )?.name,
        config.public.secretKey
      ),
    },
  };
  if (mapItems?.length) {
    gtmBeginCheckoutEvent(mapItems, {}, null);
  }
};

const triggerCartViewEvent = () => {
  const mapItems = getGtmPayload();
  if (mapItems?.length) {
    gtmCartViewEvent(mapItems);
  }
};

const createOrder = async () => {
  let orderResponseData = {};
  const orderResponse: any = await cartStore
    .createOrder()
    .then((orderResponse) => {
      if (orderResponse?.isSuccess) {
        cartStore.billingAddress = {};
        cartStore.isBillingShippingSame = true;
        const invoiceNumber = orderResponse?.data?.invoiceNo;
        const orderId = orderResponse?.data?.id;
        const paymentStatus = orderResponse?.data?.paymentStatus;

        // cartStore.reset();
        orderResponseData = { invoiceNumber, orderId, paymentStatus };
        orderResponseData["isSuccess"] = true;

        // Sending invoice to ordered user whatsapp number
        if(enableWhatsappInvoice.value){
          const phoneNumber = "+" + orderResponse?.data?.shippingAddress?.phone;
          sendInvoiceToWhatsapp(phoneNumber, invoiceNumber);
        }
      }
    })
    .catch((e: any) => {
      orderResponseData = { isSuccess: false };
      showSnackbarResponse(e?.response?._data);
    });

  return orderResponseData;
};
const handleOTPValidationBeforeOrderCreate = async ()=>{
  if(!shippingAddress?.phone){
    showSnackbar("Please enter your phone number", "red");
    return;
  }
  showOTPDialog.value = true;
}
const handleCODPaymentCreateOrder = async () => { 
  let order: any = {};
  order = await createOrder();
  if (order?.isSuccess === false) {
    loading.value = false;
    return;
  }
  triggerPurchaseEvent(order);
  
  const { invoiceNumber } = order;
  await router.push(
    localePath(
      `/orderConfirmation?invoiceNumber=${invoiceNumber}&cod=yes`
    )
  );
  loading.value = false;
  cartStore.$reset();
  couponStore.$reset();
  return;
}
const handleOTPVerification = async (isVerified: boolean)=>{
  if(isVerified){
    await handleCODPaymentCreateOrder();
  }
}
const orderConfirm = async (paymentValues: any) => {
  const { bill, ship } = await checkOutNow();
  if (!bill || !ship) return;

  const _address = shippingAddress;

  if(isInvalidPostcodeByCountryId(_address.countryId, _address.code)) {
    showSnackbar("Invalid Post code", "red");
    loading.value = false;
    return;
  }

  if (!paymentValues?.paymentMethod) return;
  const _paymentMethod = paymentValues.paymentMethod;

  loading.value = true;
  cartStore.paymentMethod = _paymentMethod;
  cartStore.deliveryType = paymentValues.deliveryType;
  let order: any = {};

  // if (paymentValues.deliveryType === "Courier") {
  //   cartStore.isBillingShippingSame = false;
  // }

  if( countryCode.value == "BD" && _paymentMethod == "COD"){
    // OTP validation for BD because of fake order Reducing meta order placing ad charge
    // This validation only for admin's customer division order placing
    const hasCustomerDivisionTag = isPhoneNumberEnterCustomerDivision(shippingAddress?.phone);
    if(hasCustomerDivisionTag){
      storeCustomerDivisionOrderPlacement();
      await handleCODPaymentCreateOrder();
    }
    else{
      handleOTPValidationBeforeOrderCreate();
    }
    loading.value = false;
    return;
  }

  if (_paymentMethod == "COD" || _paymentMethod == "ssl-commerce") {
    order = await createOrder();
    if (order?.isSuccess === false) {
      loading.value = false;
      return;
    }
    triggerPurchaseEvent(order);
  }

  try {
    const { invoiceNumber } = order;
    switch (paymentValues.paymentMethod) {
      case "revolut":
        const { token: revolutToken, id: revolutId } = await paymentStore.revolutPayment({
          amount: parseFloat(cartStore?.total),
          currency: domaCountryCurrency?.toLowerCase(),
          email: cartStore.billingAddress?.email ? cartStore.billingAddress?.email : cartStore.shippingAddress?.email,
          cart_id: cartStore.id,
        });

        const revolut = await RevolutCheckout(revolutToken, config.public.revolutMode as Mode)
        revolut.setDefaultLocale(locale.value)

        revolut.payWithPopup({
          name: cartStore?.billingAddress?.firstName ? cartStore?.billingAddress?.firstName : cartStore?.shippingAddress?.firstName,
          email: cartStore?.billingAddress?.email ? cartStore?.billingAddress?.email : cartStore?.shippingAddress?.email,
          phone: cartStore?.billingAddress?.phone ? cartStore?.billingAddress?.phone : cartStore?.shippingAddress?.phone,
          async onSuccess () {
            const order = await createOrder();
            triggerPurchaseEvent(order);

            await fetchApiData("payment/revolut/update-order", {
              method: "POST",
              body: {
                paymentMethod: "revolut",
                invoice: order?.invoiceNumber,
                paymentIntent: revolutId,
              },
            });
            await router.push(
              localePath(
                `/orderConfirmation?invoiceNumber=${order?.invoiceNumber}`
              )
            );
            loading.value = false;
            cartStore.$reset();
            couponStore.$reset();
          },
          onError(error) {
            showSnackbar(
              error ||
                "Payment failed",
              "red"
            );
            loading.value = false;
          },
        });
        return
      case "COD":
        await router.push(
          localePath(
            `/orderConfirmation?invoiceNumber=${invoiceNumber}&cod=yes`
          )
        );
        loading.value = false;
        cartStore.$reset();
        couponStore.$reset();
        return;
      case "stripe":
        try {
          const stripeRes = await paymentStore.stripePayment(
            stripeElements.value.elements,
            stripeElements.value.stripe
          );
          const { status } = stripeRes;
          if (status === "succeeded") {
            const order = await createOrder();
            triggerPurchaseEvent(order);
            await fetchApiData("payment/stripe/testResponse", {
              method: "POST",
              body: {
                paymentMethod: "stripe",
                invoice: order?.invoiceNumber,
                paymentIntent: stripeRes?.id,
              },
            });
            await router.push(
              localePath(
                `/orderConfirmation?invoiceNumber=${order?.invoiceNumber}`
              )
            );
            loading.value = false;
            cartStore.$reset();
            couponStore.$reset();
            return;
          } else {
            showSnackbar(
              stripeRes?.error?.message ||
                stripeRes?.message ||
                "Payment failed",
              "red"
            );
            loading.value = false;
            return;
          }
        } catch (er) {
          showSnackbar("Payment failed", "red");
          loading.value = false;
          return;
        }

      case "ssl-commerce":
        await paymentStore.sslPayment(invoiceNumber);
        loading.value = false;
        return;
    }
  } catch (e) {
    loading.value = false;
  }
};

const showReturnMessage = (res: string) => {
  step.value = "payment";
  showSnackbar(t("Payment Failed"), "red");
  loading.value = false;
};


const resetCartQty = () => {

  cartStore.codCharge = 0;
  cartStore.shippingCharge = 0;
  cartStore.courierId = null;
  cartStore.paymentMethod = "";
  cartStore.deliveryType = "";
  cartStore.courierServiceId = null;

  cartStore.isBillingShippingSame = true;
  cartStore.shippingAddress = {};

  lazyCheckoutPayment.value?.reset()
}

const resetCartQtyHndler = () => {
  // resetCartQty()
}


onMounted(async () => {
  resetCartQty();
  handleFetchCartMounted();
  moveToPaymentStep();

  if (countryStore.countries?.length === 0) {
    await countryStore.getCountries();
  }

  billingState.countryCode = allCountryMobileCodesArr?.value?.find((item: any) => item.id === currnetCountryId)?.phoneCode;
  shippingAddress.countryCode = getSelectedCountryCode(globals?.value?.id);
  if (authenticated?.value) {
    shippingAddress.firstName = currentUserProfile?.value?.address?.firstName;
    shippingAddress.lastName = currentUserProfile?.value?.address?.lastName;
    shippingAddress.email = currentUserProfile?.value?.address?.email;
    shippingAddress.phone = currentUserProfile?.value?.address?.phone;
    shippingAddress.address = currentUserProfile?.value?.address?.address;
    shippingAddress.state = currentUserProfile?.value?.address?.state;
    shippingAddress.city = currentUserProfile?.value?.address?.city;
    shippingAddress.code = currentUserProfile?.value?.address?.code;
  }
  else{
    shippingAddress.firstName = cartStore?.shippingAddress?.firstName;
    shippingAddress.lastName = cartStore?.shippingAddress?.lastName;
    shippingAddress.email = cartStore?.shippingAddress?.email;
    shippingAddress.phone = cartStore?.shippingAddress?.phone;
    shippingAddress.address = cartStore?.shippingAddress?.address;
    shippingAddress.state = cartStore?.shippingAddress?.state;
    shippingAddress.city = cartStore?.shippingAddress?.city;
    shippingAddress.code = cartStore?.shippingAddress?.code;
  }

  pageLoading.value = false;

  if (cartItems.value?.length) {
    triggerCartViewEvent();
  }

  if (status.value && invoice.value) {
    showReturnMessage(status.value);
  }

  // billingState = {
  //   ...billingState,
  //   ...cartStore?.billingAddress,
  // }
});

const handleFetchCartMounted = ()=>{
  const query = route.query;
  if (query?.cartUUID) {
    // ?cartUUID=1754203325&redirect_from=pantoneclo.si
    cartStore.cartUUID = query?.cartUUID;
    cartStore.fetchCartItems(cartStore.cartUUID);
    cartStore.updateCartApi();
    return;
  }

  if (cartStore.id !=null && cartStore.cartUUID != null && cartStore.cartUUID != "") {
    cartStore.fetchCartItems(cartStore.cartUUID);
    cartStore.updateCartApi();
  } else {
    handleClearLocalStorage();
    showSnackbar("Cart items Failed", "red");
  }
}

const countryCode = computed(() => domaCountryCode);

const countryMobileCodesArr = computed(() => {
  const data = countryMobileCodes();
  if (countryCode.value == "BD") {
    return data.filter((item) => item.countryId == 1);
  }

  return data;
});

const allCountryMobileCodesArr = computed(() => {
  const data = domaCountryListNonDuplicate;
  if (countryCode.value == "BD") {
    return data.filter((item) => item.id == 1);
  }

  return data;
});


const countries = computed(() => countryStore.countries);
const initialBillingState = {
  id: null,
  address: null,
  countryId: currnetCountryId,
  state: null,
  city: null,
  firstName: null,
  lastName: " ",
  email: null,
  countryCode: null,
  phone: null,
  code: null,
  cs: null,
};
const hasShipping = ref(!cartStore?.isBillingShippingSame);
const hasBilling = ref(false);

// watch(
//   () => cartStore?.isBillingShippingSame,
//   (newVal, oldVal) => {
//     if (newVal != oldVal) {
//       hasBilling.value = !newVal;
//     }
//   }
// );

const initialShippingAddress = {
  id: null,
  countryId: currnetCountryId,
  state: null,
  city: null,
  firstName: null,
  lastName: " ",
  email: null,
  phone: null,
  countryCode: null,
  address: null,
  code: null,
  cs: null,
};

var shippingAddress = reactive({
  ...initialShippingAddress,
  ...cartStore.shippingAddress,
});

let billingState = reactive({
  ...initialBillingState,
  ...cartStore.billingAddress,
});

const shippingAddressRules = computed(() => {
  return {
    countryId: {
      required: helpers.withMessage("Country is required", required),
    },
    firstName: {
      required: helpers.withMessage("First Name is required", required),
    },
    lastName:
      countryCode.value != "BD"
        ? {
            required: helpers.withMessage("Last Name is required", required),
          }
        : {},
    email: {
      required: helpers.withMessage("Email is required", required),
      email: helpers.withMessage("E-mail must be valid", email),
    },
    address: {
      required: helpers.withMessage("Address is required", required),
    },
    countryCode:
      countryCode.value != "BD"
        ? {
            required: helpers.withMessage("required", required),
          }
        : {},
    phone: {
      required: helpers.withMessage("Phone Number is required", required),
      regex: helpers.withMessage("Phone number must be numeric", helpers.regex(/^\d+(-\{\{[a-zA-Z0-9_]+\}\})?$/))
    },
    state: 
        countryCode.value === "RO" ?
          {required: helpers.withMessage("State is required", required)}
          :
          {}
    ,
    city: {
      required: helpers.withMessage("City is required", required),
    },
    code:
      countryCode.value != "BD"
        ? {
            required: helpers.withMessage("Postal Code is required", required),
          }
        : {},
  };
});

const rules = {
  countryId: {
    required: helpers.withMessage("Country is required", required),
  },
  firstName: {
    required: helpers.withMessage("First Name is required", required),
  },
  lastName: 
    countryCode.value != "BD"
    ? {
        required: helpers.withMessage("Last Name is required", required),
      }
    : {},
  email: {
    // required: helpers.withMessage("Email is required", required),
    email: helpers.withMessage("E-mail must be valid", email),
  },
  address: {
    required: helpers.withMessage("Address is required", required),
  },
  countryCode: {
    required: helpers.withMessage("required", required),
  },
  phone: {
    // required: helpers.withMessage("Phone Number is required", required),
    regex: helpers.withMessage("Phone number must be numeric", helpers.regex(/^\d+(-\{\{[a-zA-Z0-9_]+\}\})?$/))
  },
  state:
    countryCode.value === "RO" ?
      {
        required: helpers.withMessage("State is required", required)
      }
      :
      {},
  city: {
    required: helpers.withMessage("City is required", required),
  },
  code:
    countryCode.value != "BD"
      ? {
          required: helpers.withMessage("Postal Code is required", required),
        }
      : {},
};

const v$ = useVuelidate(rules, billingState);
const v2$ = useVuelidate(shippingAddressRules, shippingAddress);

watch(
  () => cartStore.shippingAddress,
  (newVal, oldVal) => {
    if (newVal) {
      if (newVal?.address) {
        Object.assign(shippingAddress, newVal);
      }
    }
  },
  { deep: true }
);

const handleSqualoSubscription = (email: string)=>{

  fetchApiData("newsSubscription", {
    method: "POST",
    body: {
      email: email,
      countryId: cartStore?.countryId,
      languageId: languageId,
      countryCode: countryCode?.value?.toLowerCase(),
      isSubscribed: true,
      userId: authenticated?.value ? currentUserProfile?.value?.id : null,
      isMailSent: false,
    },
  })
    ?.then((response)=>{
      // const squaloPayload = {
      //   storeId: cartStore?.countryId,
      //   countryId: cartStore?.countryId,
      //   ...response?.data
      // }
      // squaloStore
      //   ?.addEmailSubscription(squaloPayload)
      //   ?.then((response)=>{
      //     localStorage.setItem("squaloUser", JSON.stringify(response?.data));
      //     cartStore.subscriberId = response?.data?.id;
      //     cartStore?.updateCartApi();
      //   })
    })
}

const fetchNewsSubscriptionGetByEmail = (payload:any = {})=>{
  // /newsSubscription/getByEmail/{email}
  const email = payload?.email;
  if(email){
    fetchApiData(`newsSubscription/getByEmail/${email}`)
    .then((response)=>{
      if(!response?.isSuccess){
        handleSqualoSubscription(email);
      }
    })
  }
}

const handleEmailValidator = (email: string): boolean =>{
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

const debounceFetchNewsSubscriptionGetByEmail = debounce(fetchNewsSubscriptionGetByEmail, 500);

const handleSubscription = ()=>{
  if(isEmailFieldOutsideClick?.value && handleEmailValidator(cartStore.shippingAddress.email)){
    isEmailFieldOutsideClick.value = false;
    // subscription is adding due to squalo authority feature
    const squaloUser = squaloStore.getSqualoUserLocalStorage;
    if(!authenticated?.value) {
      debounceFetchNewsSubscriptionGetByEmail({email: cartStore.shippingAddress.email})
    }

    // update the cart(api) as we have a complete email in billing address
    cartStore?.updateCartApi();
  }
}

watch(()=> shippingAddress.email, (newVal, oldVal)=>{
  if(newVal && newVal!=oldVal){
    isEmailFieldOutsideClick.value = true;
    cartStore.shippingAddress.email = newVal;
  }else{
    cartStore.shippingAddress.email = '';
  }
}, {deep:true})

watch(()=> billingState?.email, (newVal, oldVal)=>{
  if(newVal && newVal!=oldVal){
    cartStore.billingAddress.email = newVal;
  }
}, {deep:true})

watch(()=> shippingAddress.firstName, (newVal, oldVal)=>{
  if(newVal && newVal!=oldVal){
    cartStore.shippingAddress.firstName = newVal;
  }
}, {deep:true})

watch(()=> shippingAddress.lastName, (newVal, oldVal)=>{
  if(newVal && newVal!=oldVal){
    cartStore.shippingAddress.lastName = newVal;
  }
}, {deep:true})

watch(()=> shippingAddress?.phone, (newVal, oldVal)=>{
  if(newVal && newVal!=oldVal){
    cartStore.shippingAddress.phone = newVal;
  }
  else{
    cartStore.shippingAddress.phone = '';
  }
}, {deep:true})

watch(()=> shippingAddress.countryId, (newVal, oldVal)=>{
  if(newVal && newVal!=oldVal){
    shippingAddress.countryCode = countryMobileCodesArr?.value?.find((item: any) => item.countryId == newVal)?.code;
  }
})

watch(()=> billingState.countryId, (newVal, oldVal)=>{
  if(newVal && newVal!=oldVal){
    billingState.countryCode = allCountryMobileCodesArr?.value?.find((item: any) => item.id === newVal)?.phoneCode;
  }
})


const isPhoneNumberEnterCustomerDivision = (phone: string)=>{
  return phone.includes('-') && phone.includes('{{') && phone.includes('}}');
}
// If customer division placing order then store it in cart's billing address open key
// CS division place phone number as '018000000-{{office_name}}'
const storeCustomerDivisionOrderPlacement = ()=>{
  cartStore.shippingAddress.cs = shippingAddress?.phone;
  const [phone] = shippingAddress?.phone?.split('-');
  cartStore.shippingAddress.phone = phone.trim();
}

const checkOutNow = async () => {
  const isBillingFormValid = hasBilling.value ? await v$.value.$validate() : true;
  const isShippingFormValid = await v2$.value.$validate();

  if (isBillingFormValid && isShippingFormValid) {
    cartStore.billingAddress = billingState;
    cartStore.shippingAddress = shippingAddress;
  }
  return { bill: isBillingFormValid, ship: isShippingFormValid };
};

const isRedirectableCountry = (countryId: number) => {
  return domaCountryListNonEuroCurrency?.map((item) => item?.id)?.includes(countryId);
};


const selectedCountry = (countryId: number, type: string) => {
  cartStore.fetchStates(countryId, type);
  // if(type === 'shipping' && currnetCountryId != countryId) {
  //   requestShippingCountry.value = domaCountryListNonEuroCurrency?.find((item) => item?.id == countryId);
  //   showRedirectModal.value = isRedirectableCountry(countryId);
  // }
};

const hasShippingMethod = (val: any) => {
  cartStore.isBillingShippingSame = !val;
};


const updateBillingEmailFCoupon = (val: any) => {
  couponStore.email = val;
};

// SEO
useHead(() => ({
  title: 'Checkout | Pantoneclo',
  meta: [
    { name: 'description', content: 'Checkout' },
    { name: 'keywords', content: 'Checkout' },
  ],
}))

</script>


<style scoped>
@media (max-width: 575px) {
  .v-stepper-window {
    margin: 0 !important;
  }
}

.custom-img-box {
  width: 60px;
  height: 60px;
}
</style>
