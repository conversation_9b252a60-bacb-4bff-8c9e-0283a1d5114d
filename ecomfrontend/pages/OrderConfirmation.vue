<template>
  <div>
    <v-container>
      <v-stepper alt-labels show-actions flat v-model="step">
        <LazyCheckoutStepper>
          <template #confirmation>
            <v-sheet
              elevation="0"
              max-width="800"
              rounded="0"
              width="100% "
              class="px-6 px-md-16 py-4 py-md-10 text-center mx-auto bg-grey50"
            >
              <v-icon
                class="mb-5"
                color="success"
                icon="i-mdi:check-circle-outline"
                size="112"
              ></v-icon>

              <h2 class="text-h5 mb-1">{{ $t("Congratulations") }}</h2>
              <p class="mb-2 text-body-1">
                {{ $t("Order Placed") }} <span v-if="isCOD">({{ $t("Please pay upon Delivery") }})</span>
              </p>

              <div class="d-flex align-center justify-center text-h5">
                <h5 class="mr-5">{{ $t("Invoice No") }}: {{ invoice }}</h5>
                <NuxtLink
                  class="text-blue text-decoration-underline"
                  :to="localePath('/track/order')"
                  >{{ $t("Track your order") }}</NuxtLink
                >
              </div>

              <p class="mb-2 text-body-1">
                {{
                  $t("A confirmation email has been sent to your email address")
                }}
                <br />
                {{
                  $t("Thank you for your order! Estimated delivery will be")
                }}
                <b>{{ $t("3 to 6 working days") }}</b
                >.
              </p>

              <div class="text-center mt-8">
                <v-btn
                  class="text-uppercase"
                  color="primary"
                  variant="flat"
                  @click="cartStore.downloadOrderInvoice(invoice)"
                  :loading="cartStore?.loading"
                  download
                >
                  {{ $t("Download invoice") }}
                </v-btn>
              </div>
            </v-sheet>
            <client-only>
              <v-sheet v-if="userRelatedProductList?.length > 0" class="px-2 pb-2 mt-3 rounded-xl">
                <h5 class="text-left text-h5 py-1">Buy more recommed products!</h5>
                <v-row>
                  <v-col 
                    v-for="(item, index) in userRelatedProductList"
                    :key="index"
                    cols="6"
                    sm="4"
                    md="4"
                    lg="3"
                  >	
                    <LazyEcommerceProductSingleCard :product="item" fromPage="checkout" />
                  </v-col>
                </v-row>
              </v-sheet>
              <LazyCheckoutLowerCartItemRelatedProduct v-else fromPage="orderConfirmation"/>
            </client-only>
          </template>
        </LazyCheckoutStepper>
      </v-stepper>
    </v-container>
  </div>
</template>
<script setup lang="ts">
const config = useRuntimeConfig();
const step = ref("confirmation");
const route = useRoute();
const invoice = computed(() => route.query.invoiceNumber);
const isCOD = computed(() => route.query?.cod == 'yes');

const cartStore = useShoppingCartStore();
const couponStore = useCouponStore();

const localePath = useLocalePath();
const userRelatedProductList = ref<any>([]);

const handleGetUserRelatedProductsLocalStorage = ()=>{
  const userRelatedProducts = localStorage.getItem("userRelatedProducts");
  if (userRelatedProducts) {
    userRelatedProductList.value = JSON.parse(userRelatedProducts).slice(0,4);
  }
}

onMounted(() => {
  cartStore.$reset();
  couponStore.$reset();
  // localStorage.removeItem("billingAddress");
  handleGetUserRelatedProductsLocalStorage();
});
</script>
