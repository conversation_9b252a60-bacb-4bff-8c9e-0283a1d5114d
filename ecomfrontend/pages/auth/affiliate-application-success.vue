<template>
  <div class="authentication" style="min-height: 80dvh">
    <v-container fluid class="pa-3 h-100">
      <v-row class="d-flex justify-center align-center h-100">
        <v-col cols="12" md="6" lg="5" xl="4" class="d-flex align-center my-auto">
          <v-card rounded="md" elevation="2" class="px-sm-1 px-0 mx-auto" width="100%">
            <v-card-item class="pa-sm-6 text-center">
              <div class="d-flex justify-center py-4 mb-5">
                <LazyGeneralLogo/>
              </div>
              
              <!-- Success Icon -->
              <div class="mb-6">
                <v-icon 
                  size="80" 
                  color="success" 
                  class="mb-4"
                >
                  i-mdi:check-circle
                </v-icon>
              </div>

              <!-- Success Message -->
              <div class="mb-6">
                <h2 class="text-h4 font-weight-bold text-success mb-3">
                  {{ $t('Application Submitted!') }}
                </h2>
                <p class="text-body-1 text-medium-emphasis mb-4">
                  {{ $t('Thank you for applying to our affiliate program. Your application has been successfully submitted and is now under review.') }}
                </p>
              </div>

              <!-- What's Next Section -->
              <!-- <v-card variant="outlined" class="pa-4 mb-6 text-left">
                <h3 class="text-h6 font-weight-semibold mb-3 text-center">
                  {{ $t('What happens next?') }}
                </h3>
                
                <div class="d-flex align-start mb-3">
                  <v-icon color="primary" class="me-3 mt-1">i-mdi:clock-outline</v-icon>
                  <div>
                    <h4 class="text-subtitle-1 font-weight-semibold">{{ $t('Review Process') }}</h4>
                    <p class="text-body-2 text-medium-emphasis">
                      {{ $t('Our team will review your application within 2-3 business days.') }}
                    </p>
                  </div>
                </div>

                <div class="d-flex align-start mb-3">
                  <v-icon color="primary" class="me-3 mt-1">i-mdi:email-outline</v-icon>
                  <div>
                    <h4 class="text-subtitle-1 font-weight-semibold">{{ $t('Email Notification') }}</h4>
                    <p class="text-body-2 text-medium-emphasis">
                      {{ $t('You will receive an email notification once your application is approved or if we need additional information.') }}
                    </p>
                  </div>
                </div>

                <div class="d-flex align-start">
                  <v-icon color="primary" class="me-3 mt-1">i-mdi:account-check-outline</v-icon>
                  <div>
                    <h4 class="text-subtitle-1 font-weight-semibold">{{ $t('Account Activation') }}</h4>
                    <p class="text-body-2 text-medium-emphasis">
                      {{ $t('Once approved, your affiliate account will be activated and you can start earning commissions.') }}
                    </p>
                  </div>
                </div>
              </v-card> -->

              <!-- Application Details -->
              <!-- <v-card variant="tonal" color="info" class="pa-4 mb-6">
                <h4 class="text-subtitle-1 font-weight-semibold mb-2">
                  {{ $t('Application Reference') }}
                </h4>
                <p class="text-body-2">
                  {{ $t('Application ID') }}: <strong>{{ applicationId }}</strong>
                </p>
                <p class="text-body-2">
                  {{ $t('Submitted on') }}: <strong>{{ submissionDate }}</strong>
                </p>
              </v-card> -->

              <!-- Action Buttons -->
              <div class="d-flex flex-column gap-3">
                <v-btn
                  color="primary"
                  size="large"
                  block
                  :to="localePath('/')"
                  variant="flat"
                >
                  <v-icon left class="me-2">i-mdi:home</v-icon>
                  {{ $t('Return to Homepage') }}
                </v-btn>

                <v-btn
                  color="primary"
                  size="large"
                  block
                  :to="localePath('/auth/affiliate-login')"
                  variant="outlined"
                >
                  <v-icon left class="me-2">i-mdi:login</v-icon>
                  {{ $t('Affiliate Login') }}
                </v-btn>
              </div>

              <!-- Contact Information -->
              <div class="mt-6 pt-4 border-t">
                <p class="text-body-2 text-medium-emphasis">
                  {{ $t('Have questions about your application?') }}
                </p>
                <p class="text-body-2">
                  {{ $t('Contact us at') }} 
                  <a href="mailto:<EMAIL>" class="text-primary">
                    <EMAIL>
                  </a>
                </p>
              </div>
            </v-card-item>
          </v-card>
        </v-col>
      </v-row>
    </v-container>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  middleware: ["guest"]
});

const localePath = useLocalePath();
const { t } = useI18n();

// Generate a mock application ID (in real app, this would come from the API response)
const applicationId = ref(`AF-${Date.now().toString().slice(-6)}`);
const submissionDate = ref(new Date().toLocaleDateString());

// SEO Meta
useHead({
  title: 'Application Submitted - Affiliate Program',
  meta: [
    {
      name: 'description',
      content: 'Your affiliate application has been successfully submitted. We will review your application and contact you soon.'
    }
  ]
});
</script>

<style scoped>
.authentication {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.border-t {
  border-top: 1px solid rgba(0, 0, 0, 0.12);
}
</style>
