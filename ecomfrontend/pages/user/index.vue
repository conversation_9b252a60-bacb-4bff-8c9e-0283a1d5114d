<template>
  <div>
    <LazyUserLayout>
      <v-card elevation="0" rounded="md" class="bg-lightprimary border-0">
        <v-card-item class="pt-3 pb-0">
          <v-row>
            <v-col cols="12" sm="6" class="pt-sm-10 pt-5">
              <div class="d-flex align-center">
                <v-avatar size="40">
                  <NuxtImg :src="currentUserProfile.imageUrl ?? 'https://pantoneclos3bucket.s3.ap-south-1.amazonaws.com/pantoneclo-img/user-1.jpg'"
                           :alt="currentUserProfile?.firstName"
                           loading="lazy" sizes="xs:100vw sm:100vw md:100vw"
                           format="webp" quality="85" width="40" height="40" class="h-100 w-100" fit="cover"/>
                </v-avatar>
                <h5 class="text-h5 ml-4">{{ $t('Welcome back') }} {{
                    currentUserProfile?.firstName + ' ' + currentUserProfile.lastName
                  }}!</h5>
              </div>
              <div class="mt-8">
                <div class="d-flex">
                  <div>
                    <div class="d-flex">
                      <h2 class="text-h2 textPrimary">{{ orderStore.pagination.itemCount }}</h2>
                    </div>
                    <div class="text-subtitle-1 textPrimary mt-1">{{ $t('Total Orders') }}</div>
                  </div>
                  <v-divider vertical class="mx-5"></v-divider>
                  <div>
                    <div class="d-flex">
                      <h2 class="text-h2 textPrimary">35%</h2>
                    </div>
                    <div class="text-subtitle-1 textPrimary mt-1">{{$t('Profile Complete')}}</div>
                  </div>
                </div>
              </div>
            </v-col>
            <v-col cols="12" sm="6" class="text-sm-right">
              <div class="mb-n2 ">

                <NuxtImg src="https://pantoneclos3bucket.s3.ap-south-1.amazonaws.com/pantoneclo-img/welcome-bg.svg"
                          loading="lazy" sizes="xs:100vw sm:100vw md:100vw"
                         format="webp" quality="85" width="326" height="196" class="h-100 w-100" fit="cover"/>
              </div>
            </v-col>
          </v-row>
        </v-card-item>
      </v-card>
    </lazyUserLayout>
  </div>
</template>
<script setup lang="ts">
import {storeToRefs} from "pinia";

definePageMeta({
  middleware: ["auth"]
});

const {currentUserProfile} = storeToRefs(useAuthStore());
const orderStore = useOrderStore()
onMounted(() => {

  let params = {
    take: 1,
    page: 1,
    order: 'DESC'
  }
  if (orderStore.ordersList.length === 0) {
    orderStore.getOrdersData(params)
  }

})

</script>
