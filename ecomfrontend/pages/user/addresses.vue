<template>
  <div>
    <LazyUserLayout>
      <v-btn color="primary" class="mb-3" @click="addressAddPop = true">{{$t('Add New Address')}}</v-btn>
      <v-radio-group v-model="selectedOption">
        <v-row>
          <v-col cols="4" v-for="(option, index) in options" :key="index">
            <v-card elevation="2" class="h-100">
              <v-radio :value="option.id" class="d-none"></v-radio>
              <v-card-title>
                <div class="d-flex justify-space-between align-center">
                  <v-chip color="success" v-if="currentUser?.shippingAddressId === option.id" size="x-small" variant="flat">{{$t('Default')}}</v-chip>
                  <div v-else></div>
                  <div>
                    <v-btn icon size="x-small" variant="text" color="warning" @click="editAddress(option)">
                      <v-icon class="i-mdi:pencil"/>
                    </v-btn>
                    <v-btn icon size="x-small" variant="text" color="error" @click="deleteAddress(option.id)">
                      <v-icon class="i-mdi:trash-can"/>
                    </v-btn>
                  </div>
                </div>
              </v-card-title>
              <v-card-text @click="onChangeDefaultAddress(option)" class="cursor-pointer">
                <div>
                  <h3 class="px-0"> {{$t('Address')}} {{ index + 1 }}</h3>
                  <p class="mb-0"><b>{{$t('Name')}}: </b>{{ option?.firstName + ' ' + option?.lastName }}</p>
                  <p class="mb-0"><b>{{$t('Email')}}: </b>{{ option?.email }}</p>
                  <p class="mb-0"><b>{{$t('Phone')}}: </b>{{ option?.phone }}</p>
                  <p class="mb-0"><b>{{$t('Address')}}: </b>{{ option?.address }}, {{ option?.city }}, {{ option?.country.name }}
                  </p>
                </div>
              </v-card-text>
            </v-card>
          </v-col>
        </v-row>
      </v-radio-group>
      <v-dialog v-model="addressAddPop" max-width="600" @update:modelValue="onPopupChange">
        <v-card>
          <v-card-title class="pa-5">
            {{$t('Add New Address')}}
          </v-card-title>
          <v-card-text>
            <v-form @submit.prevent="saveNewAddress">
              <v-row>
                <v-col cols="12" md="6">
                  <LazyFormTextField :label="$t('First Name')" required :hide-details="v$.firstName.$errors?.length === 0"
                                     v-model="state.firstName"
                                     :error-message="v$.firstName.$errors.map(e => $t(e.$message))"/>
                </v-col>
                <v-col cols="12" md="6">
                  <LazyFormTextField :label="$t('Last Name')" hide-details v-model="state.lastName"/>
                </v-col>
                <v-col cols="12" md="5">
                  <LazyFormTextField :label="$t('Email')" required :hide-details="v$.email.$errors?.length === 0"
                                     v-model="state.email" :error-message="v$.email.$errors.map(e => $t(e.$message))"/>
                </v-col>
                <v-col cols="12" md="7">
                  <v-label class="mb-1 font-weight-medium">{{$t('Phone')}} <span v-if="required" class="text-error">*</span>
                  </v-label>
                  <v-row no-gutters>
                    <v-col cols="4">
                      <v-select :items="countryMobileCodes()" density="compact"
                                item-title="countryCode" item-value="code"
                                required
                                :error-messages="v$.countryCode.$errors.map(e => $t(e.$message))"
                                @input="v$.countryCode.$touch"
                                @blur="v$.countryCode.$touch"
                                v-model="state.countryCode" bg-color="#fff"></v-select>
                    </v-col>
                    <v-col cols="8">
                      <LazyFormTextField required :hide-details="v$.phone.$errors?.length === 0"
                                         v-model="state.phone" :error-message="v$.phone.$errors.map(e => $t(e.$message))"/>

                    </v-col>
                  </v-row>
                </v-col>
                <v-col cols="12">
                  <v-label class="mb-1 font-weight-medium">{{$t('Address')}}</v-label>
                  <VTextarea v-model="state.address" bg-color="#fff" hide-details rows="3" density="compact"/>
                </v-col>
                <v-col cols="12" lg="6">
                  <v-label class="mb-1 font-weight-medium">{{$t('Country')}}</v-label>

                  <v-select :items="countries" item-title="name" item-value="id" density="compact"
                            @update:modelValue="selectedCountry($event, 'billing')"
                            v-model="state.countryId" :placeholder="$t('Select Country')"
                            bg-color="#fff" :hide-details="v$.countryId.$errors?.length === 0"
                            :error-messages="v$.countryId.$errors.map(e => $t(e.$message))" required
                            @input="v$.countryId.$touch"
                            @blur="v$.countryId.$touch"/>
                </v-col>

                <v-col cols="12" lg="6">
                  <LazyFormTextField v-model="state.city" :label="$t('City')" hide-details/>
                </v-col>
                <v-col cols="12" lg="6">
                  <LazyFormTextField v-model="state.state" :label="$t('State')" hide-details/>
                </v-col>
                <v-col cols="12" lg="6">
                  <LazyFormTextField v-model="state.code" :label="$t('Postal Code')" hide-details/>
                </v-col>
              </v-row>
              <v-btn variant="tonal" class="mt-5 px-10" type="submit">{{$t('Save')}}</v-btn>
            </v-form>
          </v-card-text>
        </v-card>
      </v-dialog>
    </LazyUserLayout>
  </div>
</template>
<script setup lang="ts">
import {useVuelidate} from "@vuelidate/core";
import {email, helpers, numeric, required} from "@vuelidate/validators";
import {reactive} from "vue";
import {showSnackbarResponse} from "../../utils/functions";
import {fetchApiData} from "../../utils/apiHelpers";
import countryMobileCodes from "../../utils/countryMobileCodes";

definePageMeta({
  middleware: ["auth"]
});

const addressAddPop = ref(false);
const cart = useShoppingCartStore();
const countryStore = useCountryStore()
const countries = computed(() => countryStore.countries);
const authStore = useAuthStore();
const currentUser = computed(() => authStore.currentUserProfile)

const selectedOption = ref(null);
const options = ref();

const initialAddressFields = {
  id: null,
  address: '',
  state: '',
  city: '',
  firstName: '',
  lastName: '',
  email: '',
  countryId: '',
  countryCode: '',
  phone: '',
  code: '',
}

let state = reactive({
  ...initialAddressFields
})

const rules = {
  countryId: {
    required: helpers.withMessage('Select Country', required)
  },
  firstName: {
    required: helpers.withMessage('First Name is required', required),
  },
  email: {
    required: helpers.withMessage('Email is required', required),
    email: helpers.withMessage('E-mail must be valid', email)
  },
  countryCode: {
    required: helpers.withMessage('Select Country', required),
  },
  phone: {
    required: helpers.withMessage('Phone Number is required', required),
    numeric: helpers.withMessage('Phone Number is numeric', numeric),
  },
}

const v$ = useVuelidate(rules, state);

const selectedCountry = (countryId: number, type: string) => cart.fetchStates(countryId, type);

const saveNewAddress = async () => {

  const isFormValid = await v$.value.$validate();

  if (isFormValid) {

    if (!state.id) {
      const response = await fetchApiData('/address-shipping', {
        method: 'POST',
        body: {
          userId: currentUser.value.id,
          ...state
        }
      })

      if (response.isSuccess) {
        await refreshData()
        addressAddPop.value = false
      } else {
        addressAddPop.value = false
      }
      showSnackbarResponse(response);
    } else {
      const response = await fetchApiData('/address-shipping/' + state.id, {
        method: 'PUT',
        body: {
          userId: currentUser.value.id,
          ...state
        }
      })

      if (response.isSuccess) {
        await refreshData()
        addressAddPop.value = false
      } else {
        addressAddPop.value = false
      }
      showSnackbarResponse(response);
    }
    await refreshData();
  }
}

const onChangeDefaultAddress = async (option: any) => {

  const response = await fetchApiData(`user/web/setDefaultAddressShipping?userId=${authStore?.currentBasicUser?.id}&shippingAddressId=${option?.id}`, {
    method: 'POST',
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Authorization': 'Bearer ' + authStore.currentBasicUser?.token
    },
  })

  if (response.isSuccess) {
    selectedOption.value = option?.id;
    await refreshData()
  }

  showSnackbarResponse(response);
}

const refreshData = async () => {

  const response = await authStore.getUserAddresses();

  if( response.isSuccess ) {

    options.value = response.data;

    await authStore.getUser(authStore?.currentBasicUser?.id);
    selectedOption.value = currentUser.value?.addressId

  }

}

const editAddress = (address: any) => {
  for (const [key, value] of Object.entries(address)) {
    state[key] = value
  }
  state["countryCode"] = address?.country?.phoneCode;
  addressAddPop.value = true
}

const deleteAddress = async (id: number) => {

  const response = await fetchApiData('/address/delete/' + id, {
    method: 'DELETE',
  })

  if(response?.isSuccess){
    options.value = options?.value?.filter((item)=> item?.id != id);
  }
  showSnackbarResponse(response);

  await refreshData();

}

const onPopupChange = (open: boolean) => {
  if (!open) {
    v$.value.$reset();
    for (const [key, value] of Object.entries(initialAddressFields)) {
      state[key] = value
    }
  }
}

const setFirstAddressDefault = ()=>{
  let existId = options?.value?.find((el)=> el?.id === currentUser?.value?.addressId);
  if(!existId){
    selectedOption.value = options?.value[0]?.id;
    onChangeDefaultAddress(options?.value[0]);
  }
}

onMounted(async () => {
  await refreshData();
  // setFirstAddressDefault();

  if (countryStore.countries?.length === 0) {
    await countryStore.getCountries();
  }
});

</script>
