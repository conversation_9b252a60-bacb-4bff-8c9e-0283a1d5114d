<template>
  <div>
    <LazyUserLayout :items="items" :page-title="$t('Order details')">
      <template v-if="orderStore.loading">
        <v-skeleton-loader type="table" />
      </template>
      <v-card class="border" v-else>
        <v-card-title class="pa-4">
          <div>
            <v-chip :color="getOrderStatusColor(orderData)" class="me-2">
              <v-icon class="i-mdi:truck" start></v-icon>
              {{ orderData.deliveryStatus }}
            </v-chip>
            <v-chip :color="getOrderStatusColor(orderData.deliveryStatus)">
              <v-icon class="i-mdi:cash" start></v-icon>
              {{ orderData.paymentStatus }}
            </v-chip>
          </div>
          <h3 class="mt-3">{{$t('Order Details')}}</h3>
        </v-card-title>
        <v-card-text>
          <v-table>
            <thead>
            <tr>
              <th class="text-left text-h6">
                {{$t('Product')}}
              </th>
              <th class="text-left text-h6">
                {{$t('Total')}}
              </th>
            </tr>
            </thead>
            <tbody>
            <tr v-for="product in orderData.productOrderDetails" :key="product.id">
              <td class="text-sm-body-1">
               <div class="d-flex">
                 <v-avatar rounded="0">
                   <v-img :src="product.product?.featuredImage?.imageGalleryUrls?.thump" width="50" class="me-2" />
                 </v-avatar>
                 <div>
                   <h4>{{ product.product.name }} X {{ product.quantity }}</h4>
                   <h5 v-if="product?.size" class="mb-0">{{$t('Size')}}: {{ product.size?.name }}</h5>
                   <h5 v-if="product?.color">{{$t('Color')}}: {{ product.color?.name }}</h5>
                 </div>
               </div>
              </td>
              <td class="text-sm-body-1">
                <span v-if="orderCurrency" v-html="getPriceWithCurrency(product.discountPrice, product.unitPrice, product.quantity, orderCurrency)"></span>
              </td>
            </tr>
            </tbody>
            <tfoot>
            <UserOrderDetailsItem v-if="orderCurrency" :title="$t('Shipping Charge')" :value="parseInt(orderData.shippingCharge) > 0 ?  priceWithCurrency(orderData.shippingCharge, orderCurrency) : $t('Free')" bold />
            <UserOrderDetailsItem :title="$t('COD Charges')" :value="priceWithCurrency(orderData.codCharge, orderCurrency)"
                                  bold v-if="parseInt(orderData.codCharge) > 0 && orderCurrency" />
            <UserOrderDetailsItem :title="$t('Payment Method')" :value="orderData.paymentMethod" bold />
            <UserOrderDetailsItem v-if="orderCurrency" :title="`${$t('VAT/TAX/GST')}(${$t('included')})`" :value="priceWithCurrency(orderData.vatTax, orderCurrency)" bold />
            <UserOrderDetailsItem v-if="orderCurrency" :title="$t('Total')" :value="priceWithCurrency(orderData.amount, orderCurrency)" bold />
            </tfoot>
          </v-table>
          <v-row>    
            <v-col cols="6" v-if="orderData.shippingAddress">
              <h2 class="my-3">{{$t('Shipping Address')}}</h2>
                    <div class="text-sm-body-1" v-if="orderData?.shippingAddress?.firstName">{{$t('Name')}}: {{ orderData?.shippingAddress?.firstName + ' ' +  orderData?.shippingAddress?.lastName}}</div>
                    <div class="text-sm-body-1" v-if="orderData?.shippingAddress?.email">{{$t('Email')}}: {{ orderData?.shippingAddress?.email }}</div>
                    <div class="text-sm-body-1" v-if="orderData?.shippingAddress?.address">{{$t('Address')}}: {{ orderData?.shippingAddress?.address }}</div>
                    <!-- <div class="text-sm-body-1">State: {{ orderData?.shippingAddress?.state }}</div> -->
                    <div class="text-sm-body-1">{{$t('City')}}: {{ orderData?.shippingAddress?.city }}</div>
                    <div class="text-sm-body-1">{{$t('Country')}}: {{ orderData?.shippingAddress?.country?.name }}</div>
                    <div class="text-sm-body-1">{{$t('Postal Code')}}: {{ orderData?.shippingAddress?.code }}</div>
                    <div class="text-sm-body-1">{{$t('Phone')}}: {{ orderData?.shippingAddress?.phone }}</div>
            </v-col>
            <v-col v-else-if="orderData?.billingAddress" cols="12" md="6">
              <h2 class="my-3">{{$t('Billing Address')}}</h2>
                    <div class="text-sm-body-1" v-if="orderData?.billingAddress?.firstName">{{$t('Name')}}: {{ orderData?.billingAddress?.firstName + ' ' +  orderData?.billingAddress?.lastName}}</div>
                    <div class="text-sm-body-1" v-if="orderData?.billingAddress?.email">{{$t('Email')}}: {{ orderData?.billingAddress?.email }}</div>
                    <div class="text-sm-body-1" v-if="orderData?.billingAddress?.address">{{$t('Address')}}: {{ orderData?.billingAddress?.address }}</div>
                    <!-- <div class="text-sm-body-1">State: {{ orderData?.billingAddress?.state }}</div> -->
                    <div class="text-sm-body-1">{{$t('City')}}: {{ orderData?.billingAddress?.city }}</div>
                    <div class="text-sm-body-1">{{$t('Country')}}: {{ orderData?.billingAddress?.country?.name }}</div>
                    <div class="text-sm-body-1">{{$t('Postal Code')}}: {{ orderData?.billingAddress?.code }}</div>
                    <div class="text-sm-body-1">{{$t('Phone')}}: {{ orderData?.billingAddress?.phone }}</div>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>
    </LazyUserLayout>
  </div>
</template>
<script setup lang="ts">
import type {OrderDetails} from "~/types/comoponent";
import {formatLocalePrice, formatLocalePriceProvidedCurrency} from "~/utils/money";

definePageMeta({
  middleware: ["auth"]
});

const route = useRoute();
const orderStore = useOrderStore()

const orderData = computed<OrderDetails>(() => orderStore.orderDetails);
const orderCurrency = computed(() => orderStore.orderDetails.currency?.currencySymbol);

const getPriceWithCurrency = (discountPrice: any, unitPrice: any, quantity: number, currency: any) => {

  if (parseFloat(discountPrice) || parseFloat(discountPrice) > 0) {

    const discountPriceWithQuantity = discountPrice * quantity;
    const unitPriceWithQuantity = unitPrice * quantity;

    return `<span class='text-sm-body-1'>${formatLocalePriceProvidedCurrency(discountPriceWithQuantity, orderCurrency.value)}</span>
            <span class="text-decoration-line-through text-sm-body-1 text-error">${formatLocalePriceProvidedCurrency(unitPriceWithQuantity, orderCurrency.value)}</span>`

  } else {
    const priceWithQuantity = unitPrice * quantity;
    return `<span class="text-sm-body-1">${formatLocalePriceProvidedCurrency(priceWithQuantity, orderCurrency.value)}</span>`
  }
}

const priceWithCurrency = (amount: any, currency: any) => {
  return formatLocalePriceProvidedCurrency(amount, orderCurrency.value)
}

const items = [
  {
    text: 'Dashboard',
    disabled: false,
    to: '/user',
  },
  {
    text: 'Orders',
    disabled: false,
    to: '/user/orders',
  },
  {
    text: '#' + route.params.invoice,
    disabled: true,
    to: '',
  },

];

const {data: orderDetails, error, refresh} = await useAsyncData('orders-details-page-' + route.params?.invoice,
    () => {
      let params = {
        invoice: route?.params?.invoice,
      }
      orderStore.getOrderDetails(params);
    },
    {watch: false, immediate: false}
);

onMounted(async () => {
  await refresh();
  if( orderDetails ) {
    orderStore.orderDetails = orderDetails.value?.data;
  }

})

</script>
