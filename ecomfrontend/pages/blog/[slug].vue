<template>
  <div>
    <v-container class="py-4 py-md-10" max-width="1200">
      <LazyGeneralPageBreadcrumb :breadcrumbs="breadcrumbs" />
      <h1 class="text-h3 text-md-h1 font-weight-bold mb-4 title mt-2">
        {{ blog?.title }}
      </h1>

      <NuxtImg v-if="blog?.featuredImageInfo" :src="blog?.featuredImageInfo?.imageUrl" :alt="'feature' + blog?.name || blog?.title"
        loading="lazy" sizes="xs:100vw sm:100vw md:100vw" format="webp" quality="85"
        class="mt-1 w-100" style="object-fit: cover; aspect-ratio: 1200 / 628;" />
      <v-row justify="center" class="mt-6">
        <v-col cols="12" md="8">
          <v-card elevation="0" rounded="lg">
            <!-- <div class="mb-4 text-subtitle-2 text-grey">
              {{ getDateFormat(blog?.publishedAt) }}
            </div> -->
            <!-- <div class="text-subtitle-1 text-grey mb-4">
              {{ blog?.excerpt }}
            </div> -->

            <div v-if="blog?.content" v-html="blog.content" class="blog-content mb-6"></div>
          </v-card>
        </v-col>
        <!-- <v-col cols="12" md="4" class="position-relative">
          <LazyBlogLatestView v-if="blog" :blogStore="blogStore" :blog="blog" :countryId="countryId"
            :languageId="languageId" />
        </v-col> -->
      </v-row>
    </v-container>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { useRoute } from 'vue-router';
import { useAsyncData, useHead } from 'nuxt/app';
import { useBlogStore } from '../../stores/blog';
import { useCurrentHost } from '../../composables/useCurrentHost';
import { getDateFormat } from '../../utils/functions';

const route = useRoute();
const blogStore = useBlogStore();


const { getDomaCountryId, getDomaLanguageId, getDomainLang, getCurrentHost } = useCurrentHost();
const domain = getCurrentHost();
const host = `https://${domain}`;

const countryId = getDomaCountryId();
const languageId = getDomaLanguageId();
const domainLang = getDomainLang();

const { t } = useI18n();
const localePath = useLocalePath();
const parentName = ref("Blog")

const breadcrumbs = computed(() => {
  const _breadcrumbs = [
    {
      text: t("Home"),
      disabled: false,
      to: localePath("/"),
    },
  ];

  if (parentName.value) {
    _breadcrumbs.push({
      text: parentName.value,
      disabled: false,
      to: localePath("/blog"),
    });
  }

  if (route?.params?.slug) {
    _breadcrumbs.push({
      text: data?.value?.blog?.title,
      disabled: true,
      to: "",
    });
  }

  return _breadcrumbs;
});



const metaAuthors = computed(() => {
  const names: string[] = [];

  const primary = data.value?.authors?.primary;
  const secondary = data.value?.authors?.secondary;

  if (primary?.name) {
    names.push(primary.name);
  }

  if (Array.isArray(secondary)) {
    for (const author of secondary) {
      if (author?.name) names.push(author.name);
    }
  }

  return names.length > 0 ? names.join(', ') : 'Pantoneclo Team';
});

const { data, error } = await useAsyncData(`singleblogIndex-${route?.params?.slug}`, async () => {
  let blog = null;
  await blogStore.getSinglePost(
    { slug: route.params.slug, countryId: countryId, languageId: languageId}
  ).then((response) => {
    blog = response?.data || null;
  })
  return {
    blog: blog,
  };
});

const blog = computed(() => data?.value?.blog);

useHead(() => {
  const pageTitle = blog.value?.title;
  const metaTitle = blog.value?.metaTitle;
  const metaDescription = blog.value?.metaDescription;
  const ogImage = blog.value.featuredImageInfo.imageUrl;
  const updatedAt = blog?.value?.updatedAt;
  const publishedAt = blog?.value?.publishedAt;

  const canonicalUrl = host + route.fullPath;

  return {
    title: `${pageTitle || 'Blog'} | Pantoneclo`,
    meta: [
      { name: 'title', content: metaTitle || '' },
      { name: 'description', content: metaDescription || '' },
      { name: 'keywords', content: 'summer leggings, best leggings for heat, flattering leggings, breathable leggings, women\'s activewear, Pantoneclo' },
      { name: 'author', content: metaAuthors.value },
      { name: 'viewport', content: 'width=device-width, initial-scale=1' },
      { name: 'robots', content: 'index, follow' },
      { httpEquiv: 'X-UA-Compatible', content: 'IE=edge' },
      {
        name: 'robots',
        content: 'index, follow',
      },

      // Open Graph
      { property: 'og:type', content: 'article' },
      { property: 'og:url', content: canonicalUrl },
      { property: 'og:title', content: blog.value?.title },
      { property: 'og:description', content: metaDescription },
      blog.value?.featuredImageInfo?.imageUrl && { property: 'og:image', content: ogImage },

      // Twitter
      { name: 'twitter:card', content: 'summary_large_image' },
      { name: 'twitter:url', content: canonicalUrl },
      { name: 'twitter:title', content: blog.value?.title },
      { name: 'twitter:description', content: metaDescription },
      blog.value?.featuredImageInfo?.imageUrl && { name: 'twitter:image', content: ogImage },
    ].filter(Boolean),
    link: [
      {
        rel: 'preload',
        as: 'image',
        href: ogImage,
        type: 'image/webp'
      },
      { 
        rel: 'alternate', 
        hreflang: 'en', 
        href: canonicalUrl
      },
      { 
        rel: 'alternate', 
        hreflang: domainLang, 
        href: canonicalUrl, 
      },
      { 
        rel: 'alternate', 
        hreflang: 'x-default', 
        href: canonicalUrl
      },
    ],
    script: [
      {
        type: "application/ld+json",
        children: JSON.stringify({
          "@context": "https://schema.org",
          "@type": "BlogPosting",
          "mainEntityOfPage": {
            "@type": "WebPage",
            "@id": `${host + route.fullPath}`
          },
          "headline": metaTitle || '',
          "description": metaDescription || '',
          "image": `${ogImage}`,
          "author": {
            "@type": "Person",
            "name": "Pantoneclo Team"
          },
          "publisher": {
            "@type": "Organization",
            "name": "Pantoneclo",
            "logo": {
              "@type": "ImageObject",
              "url": `https://${domain}/logo.png`
            }
          },
          "datePublished": getDateFormat(publishedAt),
          "dateModified": getDateFormat(updatedAt)
        }),
      },

    ]
  }
});
</script>

<style scoped>
.blog-content :deep(p) {
  margin-bottom: 1rem;
  line-height: 1.6;
  font-size: 1.1rem;
  color: rgba(0, 0, 0, 0.87);
}

.blog-content :deep(h2) {
  margin-top: 2rem;
  margin-bottom: 1rem;
  font-size: 1.75rem;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.87);
}

.blog-content :deep(h3) {
  margin-top: 1.5rem;
  margin-bottom: 1rem;
  font-size: 1.4rem;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.87);
}

.blog-content :deep(ul),
.blog-content :deep(ol) {
  padding-left: 1.5rem;
  margin-bottom: 1rem;
}

.blog-content :deep(li) {
  margin-bottom: 0.5rem;
  line-height: 1.6;
}

.blog-content :deep(img) {
  display: block;
  max-width: 100%;
  height: auto;
  border-radius: 6px;
  margin: 1rem 0;
  object-fit: cover;
}

@media (max-width: 960px) {
  .blog-content :deep(p),
  .blog-content :deep(li) {
    font-size: 1rem;
  }

  .blog-content :deep(h2) {
    font-size: 1.5rem;
  }

  .blog-content :deep(h3) {
    font-size: 1.25rem;
  }
}

@media (max-width: 600px) {
  .blog-content :deep(p),
  .blog-content :deep(li) {
    font-size: 0.95rem;
  }

  .blog-content :deep(h2) {
    font-size: 1.3rem;
  }

  .blog-content :deep(h3) {
    font-size: 1.1rem;
  }
}

</style>
