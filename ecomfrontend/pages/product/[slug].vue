<template>
  <div>
    <Head>
      <Title key="product_seo_title"
        >{{
          data?.seoMetaLocalization?.metaTitle ||
          data?.seoMeta?.metaTitle ||
          data?.name ||
          productMetaTitle
        }} | Pantoneclo
      </Title>
      <Meta
        name="description"
        key="product_seo_description"
        :content="
          data?.seoMetaLocalization?.metaDescription ||
          data?.seoMeta?.metaDescription
        "
      />
      <Meta
        v-if="data?.seoMetaLocalization?.keywords"
        name="keywords"
        :content="data?.seoMetaLocalization?.keywords"
      />
      <Meta
        property="og:title"
        :content="`${
          data?.seoMetaLocalization?.metaTitle ||
          data?.seoMeta?.metaTitle ||
          data?.name ||
          productMetaTitle
        } | Pantoneclo`"
      />
      <Meta
        property="og:description"
        :content="
          data?.seoMetaLocalization?.metaDescription ||
          data?.seoMeta?.metaDescription
        "
      />
      <Meta property="og:url" :content="host + route?.fullPath" />
      <Meta property="og:type" content="product" />
      <Meta
        property="og:image"
        :content="data?.singleProduct?.featuredImage?.imageGalleryUrls?.medium"
      />
      <Meta
        property="og:image:secure_url"
        :content="data?.singleProduct?.featuredImage?.imageGalleryUrls?.medium"
      />
      <Meta property="og:image:width" content="500" />
      <Meta property="og:image:height" content="500" />
      <Meta
        property="product:brand"
        :content="data?.singleProduct?.brand_name"
      />
      <Meta
        property="product:price:currency"
        :content="data?.singleProduct?.currency"
      />
      <Meta property="product:availability" content="in stock" />
      <Meta property="product:condition" content="new" />
      <Meta
        property="product:price:amount"
        :content="`${data?.singleProduct?.variants[0]?.variantDetails[0].unitPrice}`"
      />
      <Meta
        property="product:retailer_item_id"
        :content="data?.targetSku || data?.singleProduct?.sku"
      />
      <Meta
        property="product:item_group_id"
        :content="`${data?.singleProduct?.id}`"
      />

      <Meta
        name="twitter:card"
        :content="data?.singleProduct?.featuredImage?.imageGalleryUrls?.large"
      />
      <Meta
        name="twitter:title"
        :content="
          data?.seoMetaLocalization?.metaTitle ||
          data?.seoMeta?.metaTitle ||
          data?.name ||
          productMetaTitle
        "
      />
      <Meta
        name="twitter:description"
        :content="
          data?.seoMetaLocalization?.metaDescription ||
          data?.seoMeta?.metaDescription
        "
      />
      <Meta
        name="twitter:image"
        :content="data?.singleProduct?.featuredImage?.imageGalleryUrls?.medium"
      />
      <Meta name="twitter:url" :content="host + route?.fullPath" />
      <Link
        rel="preload"
        as="image"
        :href="data?.preloadProductFirstImageObj?.imageUrl"
        type="image/webp"
        fetchpriority
      />
      <Link
        rel="preload"
        as="image"
        :href="data?.preloadProductFirstImageObj?.imageGalleryUrls?.large"
        type="image/webp"
        fetchpriority
      />

    </Head>
    <v-container fluid>
      <v-row>
        <v-col lg="10" offset-lg="1">
          <GeneralPageBreadcrumb
            v-if="data?.singleProduct?.categories?.length"
            :breadcrumbs="breadCrumbs"
            class="pb-2 pb-md-4"
          />

          <EcommerceProductDetails
            :slug="data?.slug"
            :productData="data?.singleProduct"
            :categoriesArrData="data?.categoriesArr"
            :targetSku="data?.targetSku"
            :matchingProductSkuObj="data?.matchingProductSkuObj"
            :matchSkuColorId="data?.matchSkuColorId"
            :matchSkuSizeId="data?.matchSkuSizeId"
            size-guide-enable
          />
        </v-col>
      </v-row>
    </v-container>
    <LazySingleProductRelated
      v-if="data?.singleProduct?.id && data?.relatedProductList"
      :productId="data?.singleProduct?.id"
      :products="data?.relatedProductList"
    />
  </div>
</template>
<script setup lang="ts">
import { createError, useAsyncData, useHead, useRoute } from "nuxt/app";
import { computed, onMounted, ref } from "vue";
import {
  useCurrentHost,
  useLocalePath,
  useProductCategoryStore,
} from "../../.nuxt/imports";
import { useProductStore } from "../../stores/product";
import { BreadCrumb, CategoryBreadCrumbItem } from "../../types/comoponent";
import { getLocalizeName } from "../../utils/functions";
import debounce from 'lodash/debounce';

const route = useRoute();

const {
  getDomaCountryId,
  getCurrentHost,
  getDomaLanguageId,
  getDomainLang,
} = useCurrentHost();
const countryId = getDomaCountryId();
const languageId = getDomaLanguageId();
const domainLang = getDomainLang();

const productStore = useProductStore();
const productCategoryStore = useProductCategoryStore();
const localePath = useLocalePath();

const domain = getCurrentHost();

const host = "https://" + domain;
const categoriesBreadCrumb: BreadCrumb[] = [];
const breadCrumbs = ref<BreadCrumb[]>([]);

const productMetaTitle = computed(() => {
  if (!route?.params?.slug) return "";
  return data?.value?.slug
    .replace(/-\d+$/, "")
    .replace(/-/g, " ")
    .replace(/\b\w/g, (char) => char.toUpperCase());
});

const handleGenerateBreadCrumb = () => {
  data?.value?.singleProduct?.categories?.forEach(
    (item: CategoryBreadCrumbItem) => {
      categoriesBreadCrumb.push({
        text: getLocalizeName(languageId, item?.locale, item?.name),
        disabled: false,
        to: localePath("/category/" + item.slug),
      });

      if (item?.children?.length) {
        item?.children?.forEach((child) => {
          categoriesBreadCrumb.push({
            text: getLocalizeName(
              languageId,
              child?.locale,
              child?.name
            ),
            disabled: false,
            to: localePath("/category/" + child.slug),
          });
        });
      }
    }
  );
  breadCrumbs.value = [
    ...categoriesBreadCrumb,
    {
      text: data?.value?.singleProduct?.name,
      disabled: true,
      to: "",
    },
  ];
};

const handleLocalStorageRelatedProducts = () => {
  try {
    const newItems = data?.value?.relatedProductList;
    if (!Array.isArray(newItems) || newItems.length === 0) return;

    let userRelatedProducts: any = localStorage.getItem("userRelatedProducts");

    if (userRelatedProducts) {
      userRelatedProducts = JSON.parse(userRelatedProducts);

      if (Array.isArray(userRelatedProducts)) {
        // Add new items to the beginning
        userRelatedProducts = [...newItems, ...userRelatedProducts];

        // Optionally: remove duplicates by product id
        userRelatedProducts = userRelatedProducts.filter(
          (item, index, self) =>
            index === self.findIndex(i => i.id === item.id)
        );

        // Keep only latest 6 items
        userRelatedProducts = userRelatedProducts.slice(0, 6);
      } else {
        userRelatedProducts = newItems.slice(0, 4);
      }
    } else {
      userRelatedProducts = newItems.slice(0, 4);
    }

    localStorage.setItem("userRelatedProducts", JSON.stringify(userRelatedProducts));
  } catch (e) {
    console.error("Failed to store related products:", e);
  }
};

const debounceHandleLocalStorageRelatedProducts = debounce(handleLocalStorageRelatedProducts, 600);

onMounted(() => {
  handleGenerateBreadCrumb();
  debounceHandleLocalStorageRelatedProducts();
});

const { data, error } = await useAsyncData(`productIndex-${route?.params?.slug}`, async () => {
  const slug = route?.params?.slug;
  const targetSku = route?.query?.sku || "";
  var singleProduct = null;
  var categoriesArr = [];
  var seoMeta = null;
  var seoMetaLocalization = null;

  const response = await productStore.getProductByCountrySlug(
    countryId,
    slug,
    languageId,
  );
  if (response?.isSuccess) {
    productStore.product = response?.data?.product;
    singleProduct = response?.data?.product;
    categoriesArr = response?.data?.categoriesArr;
    seoMeta = response?.data?.seoMeta;
    seoMetaLocalization = response?.data?.seoMetaLocalization;
  } else {
    throw new Error("Page not found");
  }

  // SSR related operations
  var domain = getCurrentHost();
  const matchingVariant = singleProduct?.variants?.find((item) =>
    item.variantDetails?.some((detail: any) => detail?.sku === targetSku)
  );
  const matchingProductSkuObj = matchingVariant?.variantDetails?.find(
    (detail: any) => detail?.sku === targetSku
  );
  var preloadProductFirstImageObj = null;
  if (matchingProductSkuObj?.color) {
    preloadProductFirstImageObj = singleProduct?.colors?.find(
      (colorItem: any) => colorItem?.id === matchingProductSkuObj?.color?.id
    )?.images[0];
  } else {
    preloadProductFirstImageObj = singleProduct?.featuredImage;
  }

  // Fetching related products
  var relatedProductList = null;
  const _oldRelatedFetchApi = async () => {
    await productCategoryStore
      .getRelatedProductsByIdOldApi(
        countryId,
        singleProduct?.id,
        1,
        8,
        null,
        languageId
      )
      .then((relatedResponse: any) => {
        if (relatedResponse?.isSuccess) {
          relatedProductList = relatedResponse?.data || [];
        }
      });
  };

  try {
    const relatedResponse = await productCategoryStore.getRelatedProductsById(
      countryId,
      singleProduct?.id,
      1,
      20,
      null,
      languageId
    );
    if (relatedResponse?.isSuccess && relatedResponse?.data?.length > 0) {
      relatedProductList = relatedResponse?.data || [];
    } else {
      await _oldRelatedFetchApi();
    }
  } catch (error) {
    await _oldRelatedFetchApi();
  }

  return {
    slug,
    singleProduct,
    targetSku,
    categoriesArr,
    seoMeta,
    seoMetaLocalization,
    domain,
    matchSkuColorId: matchingProductSkuObj?.color?.id,
    matchSkuSizeId: matchingVariant?.size?.id,
    preloadProductFirstImageObj,
    relatedProductList,
  };
},
);

if (error.value) {
  throw createError({ statusCode: 404, statusMessage: "Page Not Found" });
}

useHead(() => {
  const canonicalUrl = host + route.fullPath;
  
  return {
    link:[
      { 
        rel: 'alternate', 
        hreflang: 'en', 
        href: canonicalUrl
      },
      { 
        rel: 'alternate', 
        hreflang: domainLang, 
        href: canonicalUrl, 
      },
      { 
        rel: 'alternate', 
        hreflang: 'x-default',
        href: canonicalUrl
      },
    ],
    script: [
      {
        type: "application/ld+json",
        children: JSON.stringify({
            "@context": "http://schema.org",
            "@type": "Product",
            "offers": [{
                  "@type": "Offer",
                  "name": data?.value?.singleProduct?.name,
                  "availability":"https://schema.org/InStock",
                  "price": data?.value?.singleProduct?.variants[0]?.variantDetails[0].discountPrice ?? data?.value?.singleProduct?.variants[0]?.variantDetails[0].unitPrice,
                  "priceCurrency": data?.value?.singleProduct?.currency,
                  "priceValidUntil": new Date().toISOString().slice(0, 10), // "2024-03-31"
                  "url": route?.fullPath
                }
            ],
            "brand": {
              "name": data?.value?.singleProduct?.brand_name
            },
            "name": data?.value?.singleProduct?.name,
            "description": data?.value?.singleProduct?.description,
            "category": data?.value?.singleProduct?.categories[0]?.name,
            "url": route?.fullPath,
            "sku": data?.value?.singleProduct?.variants[0]?.variantDetails[0].sku,
            "image": {
              "@type": "ImageObject",
              "url": data?.value?.singleProduct?.featuredImage?.imageUrl,
              "image": data?.value?.singleProduct?.featuredImage?.imageUrl,
              "name": data?.value?.singleProduct?.name,
              "width": "1024",
              "height": "1024"
            }
          })
      },
    ],
  };
})
</script>
