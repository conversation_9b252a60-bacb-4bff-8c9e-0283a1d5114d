<template>
  <div>
    <v-container fluid class="px-16">
      <v-row>
        <v-col cols="12">
          <div class="text-center my-8">
            <h2 class="text-uppercase">{{$t('Return Policy')}}</h2>
            <p>{{$t('Your comfort matters')}}</p>
          </div>
        </v-col>
      </v-row>
      <v-row>
        <v-col cols="12" sm="6">
          <h4 class="text-uppercase">{{$t('Select items you would like to return')}}</h4>
          <v-table class="mb-10">
            <thead>
            <tr>
              <th class="text-left text-uppercase bg-grey-lighten-2">
                {{$t('Product')}}
              </th>
              <th class="text-center text-uppercase bg-grey-lighten-2">
                {{$t('Quantity')}}
              </th>
              <th class="text-center text-uppercase bg-grey-lighten-2">
                {{$t('Price')}}
              </th>
              <th class="text-center text-uppercase bg-grey-lighten-2">
                {{$t('Select for return')}}
              </th>
            </tr>
            </thead>
            <tbody>
            <tr v-for="product in orderDetails?.product" :key="product.id">
              <td class="text-left px-0 py-2">
                <div class="d-flex align-center">
                  <div class="me-2">
                    <NuxtImg :src="product.featuredImage?.imageGalleryUrls?.thump" alt="Pantoneclo partner"
                            loading="lazy" sizes="xs:100vw sm:100vw md:100vw"
                            format="webp" quality="85" width="70" height="80" class="h-100" fit="cover" />
                  </div>
                  <div>
                    <h3>{{ product.productName }}</h3>
                    <div class="d-flex justify-space-between align-center">
                      <p>{{$t('Unit Price')}}: {{ priceWithCurrency(product.unitPrice) }}</p>
                      <p v-if="product.sku" class="ms-2">SKU: {{ product?.sku }}</p>
                      <p v-if="product.sizeVariant" class="ms-2">{{$t('Size')}}: {{ product?.sizeVariant.name }}</p>
                      <p v-if="product.colorVariant" class="ms-2">{{$t('Color')}}: {{ product?.colorVariant?.name }}</p>
                    </div>
                  </div>
                </div>
              </td>
              <td class="text-center px-0">
                <div class="pa-3">
                  <h3 class="px-3 font-weight-bold">{{ product?.quantity }}</h3>
                </div>

              </td>
              <td class="text-center px-0">
                <h3 class="text-h4"
                    v-html="getPriceWithCurrency(product.discountPrice, product.unitPrice, product.quantity, orderDetails?.currencySymbol)"></h3>
              </td>
              <td class="text-end px-0">
                <v-btn variant="flat" color="secondary" @click="selectedProductForRefund(product)" class="text-uppercase"
                      v-if="!product?.refundStatus">{{$t('Return Request')}}
                </v-btn>
                <v-btn variant="flat" class="text-uppercase bg-grey-lighten-1" v-else>{{ $t(product?.refundStatus) }}</v-btn>
              </td>
            </tr>
            </tbody>
          </v-table>
          <!-- Return Form-->
          <v-row v-if="selectedProduct">
            <v-col cols="10" offset="1">
              <v-row class="bg-grey-lighten-4 pa-4 pa-md-8 mb-4 mb-md-8 align-center" no-gutters>
                <v-col cols="12" md="6">
                  <div class="d-flex align-center">
                    <div class="me-2">
                      <NuxtImg :src="selectedProduct.featuredImage?.imageGalleryUrls?.medium" alt="Pantoneclo partner"
                              loading="lazy" sizes="xs:100vw sm:100vw md:100vw"
                              format="webp" quality="85" width="90" height="100" class="h-100" fit="cover" />
                    </div>
                    <div>
                      <h3>{{ selectedProduct?.productName }}</h3>
                      <div class="">
                        <p>{{$t('Unit Price')}}: {{ priceWithCurrency(selectedProduct.unitPrice) }}</p>
                        <p v-if="selectedProduct.sizeVariant">{{$t('Size')}}: {{ selectedProduct.sizeVariant?.name }}</p>
                        <p v-if="selectedProduct.colorVariant">{{$t('Color')}}: {{ selectedProduct.colorVariant?.name }}</p>
                      </div>
                    </div>
                  </div>
                </v-col>
                <v-col cols="12" md="6">
                  <v-form @submit.prevent="onSubmit">
                    <v-select variant="outlined" bg-color="#fff" required :label="$t('Reason for return')" class="mb-3"
                              :error-messages="v$.refundTypeId.$errors.map(e => e.$message)" density="compact"
                              :items="refundTypes" item-title="type" item-value="id" v-model="form.refundTypeId">
                    </v-select>

                    <v-text-field type="number" variant="outlined" bg-color="#fff" :label="$t('Quantity')" min="1" :max="selectedQuantity"
                                  :error-messages="v$.quantityToRefund.$errors.map(e => e.$message)" density="compact"
                                  v-model="form.quantityToRefund"></v-text-field>

                    <v-textarea bg-color="#fff" variant="outlined" v-model="form.refundReason" density="compact" rows="4" :placeholder="$t('Comments')"></v-textarea>

                    <LazyGeneralImageUpload @imageUploaded="imageUploaded"/>

                    <v-checkbox color="success" class="ps-0 mb-3" v-model="form.agreementCheck" density="compact"
                                false-icon="i-mdi:circle-outline" true-icon="i-mdi:check-circle-outline"
                                :error-messages="v$.agreementCheck.$errors.map(e => e.$message)">
                      <template v-slot:label>
                        <div>
                          {{$t('I have read the')}}
                          <NuxtLink :to="localePath('/return-refund')" class="text-orange text-decoration-none" target="_blank">{{$t('Return & Refund')}}</NuxtLink>
                          {{$t('Policy')}}
                        </div>
                      </template>
                    </v-checkbox>

                    <div class="text-center">
                      <v-btn rounded="0" variant="flat" color="#000" class="px-4 px-md-8 text-uppercase" type="submit"
                            :loading="isBtnLoading">{{$t('Send Request')}}
                      </v-btn>
                      <v-btn rounded="0" class="ms-3 text-uppercase bg-grey-lighten-1" variant="flat"
                            @click="cancelRequest">{{$t('Cancel')}}
                      </v-btn>
                    </div>
                  </v-form>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-col>
        <v-col cols="12" sm="6">
          <v-sheet class="ps-0 ps-md-8">
            <h4 class="text-h5">{{ returnPolicyContent?.header?.title }}</h4>
            <ul>
              <li v-for="item in returnPolicyContent?.steps" :key="item">
                <div>
                  <h5>{{ item?.title }}</h5>
                  <p v-if="item?.descriptionOne" class="text-justify">{{ item?.descriptionOne }}</p>
                  <p v-if="item?.descriptionTwo" class="text-justify">{{ item?.descriptionTwo }}</p>
                  <ul v-if="item?.address">
                    <li v-if="item?.address?.returnCompany">{{ item?.address?.returnCompany }}</li>
                    <li v-if="item?.address?.phone">{{ item?.address?.phone }}</li>
                    <li v-if="item?.address?.email">{{ item?.address?.email }}</li>
                    <li v-if="item?.address?.returnCompany">{{ item?.address?.returnCompany }}</li>
                    <li v-if="item?.address?.baseCompany">{{ item?.address?.baseCompany }}</li>
                  </ul>
                  <p v-if="item?.note" class="text-justify">{{ item?.note }}</p>
                </div>
              </li>
            </ul>
          </v-sheet>
        </v-col>
      </v-row>
    </v-container>
  </div>
</template>
<script setup lang="ts">
import {useVuelidate} from "@vuelidate/core";
import {helpers, required, minValue, maxValue} from "@vuelidate/validators";
import {getPriceWithCurrency} from "~/utils/functions";
import {numberParse, scrollTop, showSnackbarResponse} from "../../utils/functions";
import { returnPolicy } from "../../utils/returnPolicy";
import { useCurrentHost } from "../../composables/useCurrentHost";
import { computed } from "vue";

const localePath = useLocalePath();

const { t } = useI18n();

const isBtnLoading = ref(false)
const route = useRoute();
const refundStore = useRefundStore();
const selectedProduct = ref();
const orderDetails = computed(() => refundStore?.orderDetails)
const refundTypes = computed(() => refundStore.refundTypes?.map(item => {

  item.type = t(item.type);
  return item;
}));
const { getDomaCountryCode } = useCurrentHost();

const countryCode = getDomaCountryCode();

const returnPolicyContent = computed(()=>{
  console.log("countryCode.toLocaleLowerCase()::", countryCode.toLocaleLowerCase());
  return returnPolicy[countryCode.toLocaleLowerCase()];
});

const selectedQuantity = ref(1)

const initialValues = {
  quantityToRefund: 1,
  refundReason: '',
  refundTypeId: null,
  agreementCheck: null,
  images: []
}
const form = reactive({...initialValues})

const formRules = {
  quantityToRefund: {
    minValue: helpers.withMessage(
      t('The minimum value allowed is :value').replace(':value', '1'),
      minValue(1)
    ),
    maxValue: helpers.withMessage(
      t('The maximum value allowed is :value').replace(':value', selectedQuantity.value),
      maxValue(selectedQuantity)
    ),
    required: helpers.withMessage(t('Quantity is required'), required), 
  },
  refundTypeId: {
    required: helpers.withMessage(t('Please select a refund reason'), required),
  },
  agreementCheck: {
    required: helpers.withMessage(t('Please check to continue'), required),
  }
}

const v$ = useVuelidate(formRules, form);

const priceWithCurrency = (price) => `${orderDetails.value?.currencySymbol} ${numberParse(price)}`
const selectedProductForRefund = (product: any) => {
  selectedProduct.value = toRaw(product)
  selectedQuantity.value = product?.quantity
}

const cancelRequest = () => selectedProduct.value = undefined;
const imageUploaded = (images: any) => form.images = images;

const onSubmit = async () => {

  const isFormValid = await v$.value.$validate();

  if (isFormValid && form.agreementCheck === true) {
    isBtnLoading.value = true

    const response = form.images?.length > 0 ? await refundStore.addImage(form.images) : [];
    const res = await refundStore.refundRequest({
      userId: orderDetails.value?.userId,
      orderId: orderDetails.value?.orderId,
      productId: selectedProduct.value?.id,
      countryId: orderDetails.value?.countryId,
      invoiceNo: route.params.invoice,
      refundReason: form.refundReason,
      refundTypeId: form.refundTypeId,
      images: response,
      size: selectedProduct.value?.sizeVariant?.name,
      color: selectedProduct.value?.colorVariant?.name,
      sku: selectedProduct.value?.sku,
      quantity: form.quantityToRefund
    })

    showSnackbarResponse(res)
    isBtnLoading.value = false
    window.location.reload();
    scrollTop();
  }
}

onMounted(async () => {

  await refundStore.getProductsForRefundLoggedIn(route.params.invoice);

  if (refundStore.refundTypes.length === 0) {
    await refundStore.findAllRefundReason();
  }

})


</script>