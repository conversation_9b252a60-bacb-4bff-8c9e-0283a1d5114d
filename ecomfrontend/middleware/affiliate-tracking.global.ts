import { useAffiliateStore } from '~/stores/affiliate';

export default defineNuxtRouteMiddleware(async (to, from) => {
  // 1. Check for affiliate referral code in the query parameters
  const affiliateCode = to.query.ref as string;

  if (affiliateCode) {
    const affiliateStore = useAffiliateStore();

    // 2. Store the affiliate code in session/cookie
    // Using a cookie to persist across sessions until the user clears them
    // or makes a purchase.
    const cookie = useCookie('affiliate_code', {
      maxAge: 60 * 60 * 1,  // 60 * 60 * 24 * 30, // 30 days
      path: '/',
    });
    cookie.value = affiliateCode;

    // 3. Track the affiliate click via API
    // This should be an asynchronous call that doesn't block navigation.
    
    try {
      await affiliateStore.trackClick({
        affiliateCode,
        landingPage: to.fullPath,
        source: 'website',  // need to pass dynamically from url query params
        countryId: 1, // need to pass dynamically
        // Additional details can be added here if needed,
        // like referrer, UTM params, etc. The backend can
        // also infer some details from the request headers.
      });
      console.log(`Affiliate click tracked for code: ${affiliateCode}`);
    } catch (error) {
      console.error('Failed to track affiliate click:', error);
    }
  }
});
