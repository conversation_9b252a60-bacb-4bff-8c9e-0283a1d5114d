import { getDomainLang } from "../lib/domain-lang-map";

export default async function ({ app }) {
  const serverLocale = useCookie('locale_lang', { maxAge: 365 * 24 * 60 * 60 }); // 1 year
  const localePrefer = useCookie('locale_prefer', { maxAge: 365 * 24 * 60 * 60 });

  const globalStore = useGlobalStore();
  const { getCurrentHost, getDomaCountryCode, getDomaCountryId } = useCurrentHost();

  const countryId = getDomaCountryId();

  const host = getCurrentHost();

  if (localePrefer.value?.length) {
    serverLocale.value = localePrefer.value;
  } else {
    serverLocale.value = getDomainLang(host);
  }

  if (countryId && globalStore?.globals?.id != countryId) {
    if (process.env.NODE_ENV === 'development') {
      await globalStore.updateCountry('BD', 'pantoneclo.com.bd');

    } else {
      await globalStore.updateCountry(getDomaCountryCode(), host);
    }
  }
}
