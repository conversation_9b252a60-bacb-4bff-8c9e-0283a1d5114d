<template>
  <v-app>
    <!-- Navigation Drawer -->
    <v-navigation-drawer
      v-model="drawer"
      :rail="rail"
      permanent
      class="affiliate-sidebar"
      color="surface"
      elevation="1"
    >
      <!-- Logo Section -->
      <div class="pa-4 d-flex align-center">
        <LazyGeneralLogo v-if="!rail" />
        <v-icon v-else size="32" color="primary">i-mdi:account-star</v-icon>
      </div>

      <v-divider />

      <!-- Navigation Menu -->
      <v-list nav density="compact" class="pa-2">
        <v-list-item
          v-for="item in navigationItems"
          :key="item.title"
          :to="localePath(item.to)"
          :prepend-icon="item.icon"
          :title="item.title"
          :value="item.value"
          color="primary"
          class="mb-1"
          rounded="lg"
        />
      </v-list>

      <v-divider class="my-4" />

      <!-- User Section -->
      <v-list nav density="compact" class="pa-2">
        <v-list-item
          :prepend-icon="'i-mdi:account-circle'"
          :title="userDisplayName"
          :subtitle="$t('Affiliate')"
          class="mb-1"
          rounded="lg"
        />
        
        <!-- <v-list-item
          :prepend-icon="'i-mdi:cog'"
          :title="$t('Settings')"
          :to="localePath('/affiliate/settings')"
          color="primary"
          class="mb-1"
          rounded="lg"
        /> -->
        
        <v-list-item
          :prepend-icon="'i-mdi:logout'"
          :title="$t('Logout')"
          @click="logout"
          color="error"
          class="mb-1"
          rounded="lg"
        />
      </v-list>

      <!-- Rail Toggle -->
      <template #append>
        <div class="pa-2">
          <v-btn
            :icon="rail ? 'i-mdi:chevron-right' : 'i-mdi:chevron-left'"
            variant="text"
            @click="rail = !rail"
            size="small"
          />
        </div>
      </template>
    </v-navigation-drawer>

    <!-- App Bar -->
    <v-app-bar
      :order="-1"
      color="surface"
      elevation="1"
      height="64"
    >
      <template #prepend>
        <v-app-bar-nav-icon
          @click="drawer = !drawer"
          class="d-lg-none"
        />
      </template>

      <v-app-bar-title class="font-weight-bold">
        {{ pageTitle }}
      </v-app-bar-title>

      <v-spacer />

      <!-- Notifications -->
      <v-btn
        icon="i-mdi:bell-outline"
        variant="text"
        @click="showNotifications = !showNotifications"
      >
        <v-badge
          v-if="notificationCount > 0"
          :content="notificationCount"
          color="error"
        >
          <v-icon>i-mdi:bell-outline</v-icon>
        </v-badge>
        <v-icon v-else>i-mdi:bell-outline</v-icon>
      </v-btn>

      <!-- User Menu -->
      <v-menu>
        <template #activator="{ props }">
          <v-btn
            v-bind="props"
            :prepend-icon="'i-mdi:account-circle'"
            variant="text"
            class="text-none"
          >
            {{ userDisplayName }}
          </v-btn>
        </template>
        
        <v-list>
          <v-list-item
            :to="localePath('/affiliate/profile')"
            prepend-icon="i-mdi:account"
          >
            <v-list-item-title>{{ $t('Profile') }}</v-list-item-title>
          </v-list-item>
          
          <v-list-item
            :to="localePath('/affiliate/settings')"
            prepend-icon="i-mdi:cog"
          >
            <v-list-item-title>{{ $t('Settings') }}</v-list-item-title>
          </v-list-item>
          
          <v-divider />
          
          <v-list-item
            @click="logout"
            prepend-icon="i-mdi:logout"
            class="text-error"
          >
            <v-list-item-title>{{ $t('Logout') }}</v-list-item-title>
          </v-list-item>
        </v-list>
      </v-menu>
    </v-app-bar>

    <!-- Main Content -->
    <v-main class="affiliate-main">
      <v-container fluid class="pa-6">
        <slot />
      </v-container>
    </v-main>

    <!-- Notifications Drawer -->
    <v-navigation-drawer
      v-model="showNotifications"
      location="right"
      temporary
      width="400"
    >
      <v-toolbar color="primary" dark>
        <v-toolbar-title>{{ $t('Notifications') }}</v-toolbar-title>
        <v-spacer />
        <v-btn
          icon="i-mdi:close"
          @click="showNotifications = false"
        />
      </v-toolbar>
      
      <v-list>
        <v-list-item
          v-for="notification in notifications"
          :key="notification.id"
          class="border-b"
        >
          <template #prepend>
            <v-icon :color="notification.type">
              {{ getNotificationIcon(notification.type) }}
            </v-icon>
          </template>
          
          <v-list-item-title>{{ notification.title }}</v-list-item-title>
          <v-list-item-subtitle>{{ notification.message }}</v-list-item-subtitle>
          
          <template #append>
            <v-list-item-action>
              <v-btn
                icon="i-mdi:close"
                size="small"
                variant="text"
                @click="dismissNotification(notification.id)"
              />
            </v-list-item-action>
          </template>
        </v-list-item>
        
        <v-list-item v-if="notifications.length === 0" class="text-center">
          <v-list-item-title class="text-medium-emphasis">
            {{ $t('No notifications') }}
          </v-list-item-title>
        </v-list-item>
      </v-list>
    </v-navigation-drawer>

    <!-- Global Snackbar -->
    <LazyGlobalSnackbar />
  </v-app>
</template>

<script setup lang="ts">
import { onMounted } from 'vue';

const localePath = useLocalePath();
const { t } = useI18n();
const authStore = useAuthStore();
const affiliateStore = useAffiliateStore();
const route = useRoute();

// Layout state
const drawer = ref(true);
const rail = ref(false);
const showNotifications = ref(false);

// User info
const userDisplayName = computed(() => {
  const user = authStore.currentUserProfile;
  return user?.firstName && user?.lastName 
    ? `${user.firstName} ${user.lastName}`
    : user?.email || t('Affiliate User');
});

// Page title
const pageTitle = computed(() => {
  const routeName = route.name as string;
  const titleMap: Record<string, string> = {
    'affiliate-dashboard': t('Dashboard'),
    'affiliate-analytics': t('Analytics'),
    'affiliate-commissions': t('Commissions'),
    'affiliate-links': t('Link Generator'),
    'affiliate-discounts': t('Discount Codes'),
    'affiliate-profile': t('Profile'),
    'affiliate-settings': t('Settings'),
  };
  return titleMap[routeName] || t('Affiliate Dashboard');
});

// Navigation items
const navigationItems = computed(() => [
  {
    title: t('Dashboard'),
    icon: 'i-mdi:view-dashboard',
    to: '/affiliate/dashboard',
    value: 'dashboard',
  },
  {
    title: t('Analytics'),
    icon: 'i-mdi:chart-line',
    to: '/affiliate/analytics',
    value: 'analytics',
  },
  {
    title: t('Commissions'),
    icon: 'i-mdi:currency-usd',
    to: '/affiliate/commissions',
    value: 'commissions',
  },
  {
    title: t('Link Generator'),
    icon: 'i-mdi:link-variant',
    to: '/affiliate/links',
    value: 'links',
  },
  {
    title: t('Discount Codes'),
    icon: 'i-mdi:ticket-percent',
    to: '/affiliate/discounts',
    value: 'discounts',
  },
  {
    title: t('Profile'),
    icon: 'i-mdi:account',
    to: '/affiliate/profile',
    value: 'profile',
  },
]);

// Notifications
const notifications = ref([
  {
    id: 1,
    type: 'success',
    title: t('Commission Earned'),
    message: t('You earned $25.50 from a recent sale'),
  },
  {
    id: 2,
    type: 'info',
    title: t('New Discount Code'),
    message: t('A new discount code has been assigned to you'),
  },
]);

const notificationCount = computed(() => notifications.value.length);

// Methods
const logout = async () => {
  authStore.logUserOut();
};

const getNotificationIcon = (type: string) => {
  const iconMap: Record<string, string> = {
    success: 'i-mdi:check-circle',
    error: 'i-mdi:alert-circle',
    warning: 'i-mdi:alert',
    info: 'i-mdi:information',
  };
  return iconMap[type] || 'i-mdi:information';
};

const dismissNotification = (id: number) => {
  const index = notifications.value.findIndex(n => n.id === id);
  if (index > -1) {
    notifications.value.splice(index, 1);
  }
};

// Responsive behavior
const { width } = useDisplay();
watch(width, (newWidth) => {
  if (newWidth < 1280) {
    drawer.value = false;
    rail.value = false;
  } else {
    drawer.value = true;
  }
}, { immediate: true });
</script>

<style scoped>
.affiliate-sidebar {
  border-right: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
}

.affiliate-main {
  background-color: rgb(var(--v-theme-background));
}

.border-b {
  border-bottom: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
}
</style>
