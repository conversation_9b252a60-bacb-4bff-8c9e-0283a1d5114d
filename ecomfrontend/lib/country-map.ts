// {
//   id: 3,
//   name: "Slovenia",
//   code: "SI",
//   domain: "pantoneclo.com",
//   lang: "sl",
//   langId: 16,
//   currency: "EUR",
//   currencySymbol: "€",
//   currencyId: 1,
//   languageId: 16,
//   isActive: true,
// },
// {
//   id: 3,
//   name: "Slovenia",
//   code: "SI",
//   domain: "localhost",
//   lang: "sl",
//   langId: 16,
//   currency: "EUR",
//   currencySymbol: "€",
//   currencyId: 1,
//   languageId: 16,
//   isActive: true,
// },
export const countryList = [
  {
    id: 3,
    name: "Slovenia",
    code: "SI",
    domain: "pantoneclo.com",
    lang: "sl",
    langId: 16,
    currency: "EUR",
    currencySymbol: "€",
    currencyId: 1,
    languageId: 16,
    isActive: true,
    phoneCode: "+386",
  },
  {
    id: 3,
    name: "Slovenia",
    code: "SI",
    domain: "localhost",
    lang: "sl",
    langId: 16,
    currency: "EUR",
    currencySymbol: "€",
    currencyId: 1,
    languageId: 16,
    isActive: true,
    phoneCode: "+386",
  },
  {
    id: 1,
    name: "Bangladesh",
    code: "BD",
    domain: "pantoneclo.com.bd",
    lang: "en",
    langId: 1,
    currency: "BDT",
    currencySymbol: "Tk.",
    currencyId: 1,
    languageId: 1,
    isActive: true,
    phoneCode: "+88",
  },
  {
    id: 2,
    name: "Greece",
    code: "GR",
    domain: "pantoneclo.gr",
    lang: "el",
    langId: 6,
    currency: "EUR",
    currencySymbol: "€",
    currencyId: 1,
    languageId: 6,
    isActive: true,
    phoneCode: "+30",
  },
  {
    id: 3,
    name: "Slovenia",
    code: "SI",
    domain: "pantoneclo.si",
    lang: "sl",
    langId: 16,
    currency: "EUR",
    currencySymbol: "€",
    currencyId: 1,
    languageId: 16,
    isActive: true,
    phoneCode: "+386",
  },
  {
    id: 4,
    name: "Slovakia",
    code: "SK",
    domain: "pantoneclo.sk",
    lang: "sk",
    langId: 15,
    currency: "EUR",
    currencySymbol: "€",
    currencyId: 1,
    languageId: 15,
    isActive: true,
    phoneCode: "+421",
  },
  {
    id: 5,
    name: "Romania",
    code: "RO",
    domain: "pantoneclo.ro",
    lang: "ro",
    langId: 14,
    currency: "RON",
    currencySymbol: "RON",
    currencyId: 1,
    languageId: 14,
    isActive: true,
    phoneCode: "+40",
  },
  {
    id: 6,
    name: "Portugal",
    code: "PT",
    domain: "pantoneclo.pt",
    lang: "pt",
    langId: 13,
    currency: "EUR",
    currencySymbol: "€",
    currencyId: 1,
    languageId: 13,
    isActive: true,
    phoneCode: "+351",
  },
  {
    id: 7,
    name: "Poland",
    code: "PL",
    domain: "pantoneclo.pl",
    lang: "pl",
    langId: 12,
    currency: "PLN",
    currencySymbol: "PLN",
    currencyId: 1,
    languageId: 12,
    isActive: true,
    phoneCode: "+48",
  },
  {
    id: 8,
    name: "Lithuania",
    code: "LT",
    domain: "pantoneclo.lt",
    lang: "lt",
    langId: 11,
    currency: "EUR",
    currencySymbol: "€",
    currencyId: 1,
    languageId: 11,
    isActive: true,
    phoneCode: "+370",
  },
  {
    id: 9,
    name: "Italy",
    code: "IT",
    domain: "pantoneclo.it",
    lang: "it",
    langId: 10,
    currency: "EUR",
    currencySymbol: "€",
    currencyId: 1,
    languageId: 10,
    isActive: true,
    phoneCode: "+39",
  },
  {
    id: 10,
    name: "Spain",
    code: "ES",
    domain: "pantoneclo.es",
    lang: "es",
    langId: 7,
    currency: "EUR",
    currencySymbol: "€",
    currencyId: 1,
    languageId: 7,
    isActive: true,
    phoneCode: "+34",
  },
  {
    id: 11,
    name: "Germany",
    code: "DE",
    domain: "pantoneclo.de",
    lang: "de",
    langId: 3,
    currency: "EUR",
    currencySymbol: "€",
    currencyId: 1,
    languageId: 3,
    isActive: true,
    phoneCode: "+49",
  },
  {
    id: 12,
    name: "Czech Republic",
    code: "CZ",
    domain: "pantoneclo.cz",
    lang: "cz",
    langId: 5,
    currency: "CZK",
    currencySymbol: "CZK",
    currencyId: 1,
    languageId: 5,
    isActive: true,
    phoneCode: "+420",
  },
  {
    id: 13,
    name: "Croatia",
    code: "HR",
    domain: "pantoneclo.hr",
    lang: "hr",
    langId: 8,
    currency: "EUR",
    currencySymbol: "€",
    currencyId: 1,
    languageId: 8,
    isActive: true,
    phoneCode: "+385",
  },
  {
    id: 14,
    name: "Bulgaria",
    code: "BG",
    domain: "pantoneclo.bg",
    lang: "bg",
    langId: 4,
    currency: "BGN",
    currencySymbol: "BGN",
    currencyId: 1,
    languageId: 4,
    isActive: true,
    phoneCode: "+359",
  },
  {
    id: 15,
    name: "Austria",
    code: "AT",
    domain: "pantoneclo.at",
    lang: "at",
    langId: 17,
    currency: "EUR",
    currencySymbol: "€",
    currencyId: 1,
    languageId: 17,
    isActive: true,
    phoneCode: "+43",
  },
  {
    id: 16,
    name: "Hungary",
    code: "HU",
    domain: "pantoneclo.hu",
    lang: "hu",
    langId: 9,
    currency: "HUF",
    currencySymbol: "HUF",
    currencyId: 1,
    languageId: 9,
    isActive: true,
    phoneCode: "+36",
  },
  {
    id: 17,
    name: "Azerbaijan",
    code: "AZ",
    domain: "pantoneclo.az",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+994",
  },
  {
    id: 18,
    name: "Burundi",
    code: "BI",
    domain: "pantoneclo.bi",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+257",
  },
  {
    id: 19,
    name: "India",
    code: "IN",
    domain: "pantoneclo.in",
    lang: "en",
    langId: 2,
    currency: "INR",
    currencySymbol: "₹",
    currencyId: 1,
    languageId: 2,
    isActive: false,
    phoneCode: "+91",
  },
  {
    id: 20,
    name: "Benin",
    code: "BJ",
    domain: "pantoneclo.bj",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+229",
  },
  {
    id: 21,
    name: "Bonaire, Sint Eustatius and Saba",
    code: "BQ",
    domain: "pantoneclo.bq",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+599",
  },
  {
    id: 22,
    name: "Burkina Faso",
    code: "BF",
    domain: "pantoneclo.bf",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+226",
  },
  {
    id: 23,
    name: "Aruba",
    code: "AW",
    domain: "pantoneclo.aw",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+297",
  },
  {
    id: 24,
    name: "Antigua and Barbuda",
    code: "AG",
    domain: "pantoneclo.ag",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+1",
  },
  {
    id: 25,
    name: "Bahrain",
    code: "BH",
    domain: "pantoneclo.bh",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+973",
  },
  {
    id: 26,
    name: "Bahamas",
    code: "BS",
    domain: "pantoneclo.bs",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+1",
  },
  {
    id: 27,
    name: "Bosnia and Herzegovina",
    code: "BA",
    domain: "pantoneclo.ba",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+387",
  },
  {
    id: 28,
    name: "Saint Barthélemy",
    code: "BL",
    domain: "pantoneclo.bl",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+590",
  },
  {
    id: 29,
    name: "Belarus",
    code: "BY",
    domain: "pantoneclo.by",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+375",
  },
  {
    id: 30,
    name: "Belize",
    code: "BZ",
    domain: "pantoneclo.bz",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+501",
  },
  {
    id: 31,
    name: "Bermuda",
    code: "BM",
    domain: "pantoneclo.bm",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+1",
  },
  {
    id: 32,
    name: "Bolivia, Plurinational State of",
    code: "BO",
    domain: "pantoneclo.bo",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+591",
  },
  {
    id: 33,
    name: "Brazil",
    code: "BR",
    domain: "pantoneclo.br",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+55",
  },
  {
    id: 34,
    name: "Barbados",
    code: "BB",
    domain: "pantoneclo.bb",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+1",
  },
  {
    id: 35,
    name: "Brunei Darussalam",
    code: "BN",
    domain: "pantoneclo.bn",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+673",
  },
  {
    id: 36,
    name: "Bhutan",
    code: "BT",
    domain: "pantoneclo.bt",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+975",
  },
  {
    id: 37,
    name: "Bouvet Island",
    code: "BV",
    domain: "pantoneclo.bv",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+47",
  },
  {
    id: 38,
    name: "Botswana",
    code: "BW",
    domain: "pantoneclo.bw",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+267",
  },
  {
    id: 39,
    name: "Central African Republic",
    code: "CF",
    domain: "pantoneclo.cf",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+236",
  },
  {
    id: 40,
    name: "Canada",
    code: "CA",
    domain: "pantoneclo.ca",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+1",
  },
  {
    id: 41,
    name: "Cocos (Keeling) Islands",
    code: "CC",
    domain: "pantoneclo.cc",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+61",
  },
  {
    id: 42,
    name: "Switzerland",
    code: "CH",
    domain: "pantoneclo.ch",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+41",
  },
  {
    id: 43,
    name: "Chile",
    code: "CL",
    domain: "pantoneclo.cl",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+56",
  },
  {
    id: 44,
    name: "China",
    code: "CN",
    domain: "pantoneclo.cn",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+86",
  },
  {
    id: 45,
    name: "Côte d'Ivoire",
    code: "CI",
    domain: "pantoneclo.ci",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+225",
  },
  {
    id: 46,
    name: "Cameroon",
    code: "CM",
    domain: "pantoneclo.cm",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+237",
  },
  {
    id: 47,
    name: "Congo, The Democratic Republic of the",
    code: "CD",
    domain: "pantoneclo.cd",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+243",
  },
  {
    id: 48,
    name: "Congo",
    code: "CG",
    domain: "pantoneclo.cg",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+242",
  },
  {
    id: 49,
    name: "Cook Islands",
    code: "CK",
    domain: "pantoneclo.ck",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+682",
  },
  {
    id: 50,
    name: "Colombia",
    code: "CO",
    domain: "pantoneclo.co",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+57",
  },
  {
    id: 51,
    name: "Comoros",
    code: "KM",
    domain: "pantoneclo.km",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+269",
  },
  {
    id: 52,
    name: "Cabo Verde",
    code: "CV",
    domain: "pantoneclo.cv",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+238",
  },
  {
    id: 53,
    name: "Costa Rica",
    code: "CR",
    domain: "pantoneclo.cr",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+506",
  },
  {
    id: 54,
    name: "Cuba",
    code: "CU",
    domain: "pantoneclo.cu",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+53",
  },
  {
    id: 55,
    name: "Curaçao",
    code: "CW",
    domain: "pantoneclo.cw",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+599",
  },
  {
    id: 56,
    name: "Christmas Island",
    code: "CX",
    domain: "pantoneclo.cx",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+61",
  },
  {
    id: 57,
    name: "Cayman Islands",
    code: "KY",
    domain: "pantoneclo.ky",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+1",
  },
  {
    id: 58,
    name: "Cyprus",
    code: "CY",
    domain: "pantoneclo.cy",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+357",
  },
  {
    id: 59,
    name: "Antarctica",
    code: "AQ",
    domain: "pantoneclo.aq",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+672",
  },
  {
    id: 60,
    name: "American Samoa",
    code: "AS",
    domain: "pantoneclo.as",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+1",
  },
  {
    id: 61,
    name: "Djibouti",
    code: "DJ",
    domain: "pantoneclo.dj",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+253",
  },
  {
    id: 62,
    name: "Dominica",
    code: "DM",
    domain: "pantoneclo.dm",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+1",
  },
  {
    id: 63,
    name: "Denmark",
    code: "DK",
    domain: "pantoneclo.dk",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+45",
  },
  {
    id: 64,
    name: "Dominican Republic",
    code: "DO",
    domain: "pantoneclo.do",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+1",
  },
  {
    id: 65,
    name: "Algeria",
    code: "DZ",
    domain: "pantoneclo.dz",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+213",
  },
  {
    id: 66,
    name: "Ecuador",
    code: "EC",
    domain: "pantoneclo.ec",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+593",
  },
  {
    id: 67,
    name: "Egypt",
    code: "EG",
    domain: "pantoneclo.eg",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+20",
  },
  {
    id: 68,
    name: "Eritrea",
    code: "ER",
    domain: "pantoneclo.er",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+291",
  },
  {
    id: 69,
    name: "Western Sahara",
    code: "EH",
    domain: "pantoneclo.eh",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+212",
  },
  {
    id: 70,
    name: "Armenia",
    code: "AM",
    domain: "pantoneclo.am",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+374",
  },
  {
    id: 71,
    name: "Estonia",
    code: "EE",
    domain: "pantoneclo.ee",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+372",
  },
  {
    id: 72,
    name: "Ethiopia",
    code: "ET",
    domain: "pantoneclo.et",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+251",
  },
  {
    id: 73,
    name: "Finland",
    code: "FI",
    domain: "pantoneclo.fi",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+358",
  },
  {
    id: 74,
    name: "Fiji",
    code: "FJ",
    domain: "pantoneclo.fj",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+679",
  },
  {
    id: 75,
    name: "Falkland Islands (Malvinas)",
    code: "FK",
    domain: "pantoneclo.fk",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+500",
  },
  {
    id: 76,
    name: "France",
    code: "FR",
    domain: "pantoneclo.fr",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+33",
  },
  {
    id: 77,
    name: "Faroe Islands",
    code: "FO",
    domain: "pantoneclo.fo",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+298",
  },
  {
    id: 78,
    name: "Micronesia, Federated States of",
    code: "FM",
    domain: "pantoneclo.fm",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+691",
  },
  {
    id: 79,
    name: "Gabon",
    code: "GA",
    domain: "pantoneclo.ga",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+241",
  },
  {
    id: 80,
    name: "United Kingdom",
    code: "GB",
    domain: "pantoneclo.gb",
    lang: "en",
    langId: 1,
    currency: "GBP",
    currencySymbol: "£",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+44",
  },
  {
    id: 81,
    name: "Georgia",
    code: "GE",
    domain: "pantoneclo.ge",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+995",
  },
  {
    id: 82,
    name: "Guernsey",
    code: "GG",
    domain: "pantoneclo.gg",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+44",
  },
  {
    id: 83,
    name: "Ghana",
    code: "GH",
    domain: "pantoneclo.gh",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+233",
  },
  {
    id: 84,
    name: "Gibraltar",
    code: "GI",
    domain: "pantoneclo.gi",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+350",
  },
  {
    id: 85,
    name: "Guinea",
    code: "GN",
    domain: "pantoneclo.gn",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+224",
  },
  {
    id: 86,
    name: "Guadeloupe",
    code: "GP",
    domain: "pantoneclo.gp",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+590",
  },
  {
    id: 87,
    name: "Gambia",
    code: "GM",
    domain: "pantoneclo.gm",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+220",
  },
  {
    id: 88,
    name: "Guinea-Bissau",
    code: "GW",
    domain: "pantoneclo.gw",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+245",
  },
  {
    id: 89,
    name: "Equatorial Guinea",
    code: "GQ",
    domain: "pantoneclo.gq",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+240",
  },
  {
    id: 90,
    name: "Afghanistan",
    code: "AF",
    domain: "pantoneclo.af",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+93",
  },
  {
    id: 91,
    name: "Grenada",
    code: "GD",
    domain: "pantoneclo.gd",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+1",
  },
  {
    id: 92,
    name: "Greenland",
    code: "GL",
    domain: "pantoneclo.gl",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+299",
  },
  {
    id: 93,
    name: "Guatemala",
    code: "GT",
    domain: "pantoneclo.gt",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+502",
  },
  {
    id: 94,
    name: "French Guiana",
    code: "GF",
    domain: "pantoneclo.gf",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+594",
  },
  {
    id: 95,
    name: "Guam",
    code: "GU",
    domain: "pantoneclo.gu",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+1",
  },
  {
    id: 96,
    name: "Guyana",
    code: "GY",
    domain: "pantoneclo.gy",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+592",
  },
  {
    id: 97,
    name: "Hong Kong",
    code: "HK",
    domain: "pantoneclo.hk",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+852",
  },
  {
    id: 98,
    name: "Heard Island and McDonald Islands",
    code: "HM",
    domain: "pantoneclo.hm",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+672",
  },
  {
    id: 99,
    name: "Honduras",
    code: "HN",
    domain: "pantoneclo.hn",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+504",
  },
  {
    id: 100,
    name: "French Southern Territories",
    code: "TF",
    domain: "pantoneclo.tf",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+262",
  },
  {
    id: 101,
    name: "Haiti",
    code: "HT",
    domain: "pantoneclo.ht",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+509",
  },
  {
    id: 102,
    name: "Australia",
    code: "AU",
    domain: "pantoneclo.au",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+61",
  },
  {
    id: 103,
    name: "Indonesia",
    code: "ID",
    domain: "pantoneclo.id",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+62",
  },
  {
    id: 104,
    name: "Isle of Man",
    code: "IM",
    domain: "pantoneclo.im",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+44",
  },
  {
    id: 105,
    name: "Belgium",
    code: "BE",
    domain: "pantoneclo.be",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+32",
  },
  {
    id: 106,
    name: "British Indian Ocean Territory",
    code: "IO",
    domain: "pantoneclo.io",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+246",
  },
  {
    id: 107,
    name: "Ireland",
    code: "IE",
    domain: "pantoneclo.ie",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+353",
  },
  {
    id: 108,
    name: "Iran, Islamic Republic of",
    code: "IR",
    domain: "pantoneclo.ir",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+98",
  },
  {
    id: 109,
    name: "Iraq",
    code: "IQ",
    domain: "pantoneclo.iq",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+964",
  },
  {
    id: 110,
    name: "Iceland",
    code: "IS",
    domain: "pantoneclo.is",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+354",
  },
  {
    id: 111,
    name: "Israel",
    code: "IL",
    domain: "pantoneclo.il",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+972",
  },
  {
    id: 112,
    name: "Argentina",
    code: "AR",
    domain: "pantoneclo.ar",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+54",
  },
  {
    id: 113,
    name: "Jamaica",
    code: "JM",
    domain: "pantoneclo.jm",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+1",
  },
  {
    id: 114,
    name: "Jersey",
    code: "JE",
    domain: "pantoneclo.je",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+44",
  },
  {
    id: 115,
    name: "Jordan",
    code: "JO",
    domain: "pantoneclo.jo",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+962",
  },
  {
    id: 116,
    name: "Japan",
    code: "JP",
    domain: "pantoneclo.jp",
    lang: "en",
    langId: 1,
    currency: "JPY",
    currencySymbol: "¥",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+81",
  },
  {
    id: 117,
    name: "Kazakhstan",
    code: "KZ",
    domain: "pantoneclo.kz",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+7",
  },
  {
    id: 118,
    name: "Kenya",
    code: "KE",
    domain: "pantoneclo.ke",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+254",
  },
  {
    id: 119,
    name: "Kyrgyzstan",
    code: "KG",
    domain: "pantoneclo.kg",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+996",
  },
  {
    id: 120,
    name: "Cambodia",
    code: "KH",
    domain: "pantoneclo.kh",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+855",
  },
  {
    id: 121,
    name: "Kiribati",
    code: "KI",
    domain: "pantoneclo.ki",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+686",
  },
  {
    id: 122,
    name: "Saint Kitts and Nevis",
    code: "KN",
    domain: "pantoneclo.kn",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+1",
  },
  {
    id: 123,
    name: "Korea, Republic of",
    code: "KR",
    domain: "pantoneclo.kr",
    lang: "en",
    langId: 1,
    currency: "KRW",
    currencySymbol: "₩",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+82",
  },
  {
    id: 124,
    name: "Kuwait",
    code: "KW",
    domain: "pantoneclo.kw",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+965",
  },
  {
    id: 125,
    name: "Lao People's Democratic Republic",
    code: "LA",
    domain: "pantoneclo.la",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+856",
  },
  {
    id: 126,
    name: "Lebanon",
    code: "LB",
    domain: "pantoneclo.lb",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+961",
  },
  {
    id: 127,
    name: "Liberia",
    code: "LR",
    domain: "pantoneclo.lr",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+231",
  },
  {
    id: 128,
    name: "Libya",
    code: "LY",
    domain: "pantoneclo.ly",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+218",
  },
  {
    id: 129,
    name: "Saint Lucia",
    code: "LC",
    domain: "pantoneclo.lc",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+1",
  },
  {
    id: 130,
    name: "Liechtenstein",
    code: "LI",
    domain: "pantoneclo.li",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+423",
  },
  {
    id: 131,
    name: "Sri Lanka",
    code: "LK",
    domain: "pantoneclo.lk",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+94",
  },
  {
    id: 132,
    name: "Lesotho",
    code: "LS",
    domain: "pantoneclo.ls",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+266",
  },
  {
    id: 133,
    name: "United Arab Emirates",
    code: "AE",
    domain: "pantoneclo.ae",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+971",
  },
  {
    id: 134,
    name: "Luxembourg",
    code: "LU",
    domain: "pantoneclo.lu",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+352",
  },
  {
    id: 135,
    name: "Latvia",
    code: "LV",
    domain: "pantoneclo.lv",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+371",
  },
  {
    id: 136,
    name: "Macao",
    code: "MO",
    domain: "pantoneclo.mo",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+853",
  },
  {
    id: 137,
    name: "Saint Martin (French part)",
    code: "MF",
    domain: "pantoneclo.mf",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+590",
  },
  {
    id: 138,
    name: "Morocco",
    code: "MA",
    domain: "pantoneclo.ma",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+212",
  },
  {
    id: 139,
    name: "Monaco",
    code: "MC",
    domain: "pantoneclo.mc",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+377",
  },
  {
    id: 140,
    name: "Moldova, Republic of",
    code: "MD",
    domain: "pantoneclo.md",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+373",
  },
  {
    id: 141,
    name: "Madagascar",
    code: "MG",
    domain: "pantoneclo.mg",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+261",
  },
  {
    id: 142,
    name: "Maldives",
    code: "MV",
    domain: "pantoneclo.mv",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+960",
  },
  {
    id: 143,
    name: "Mexico",
    code: "MX",
    domain: "pantoneclo.mx",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+52",
  },
  {
    id: 144,
    name: "Marshall Islands",
    code: "MH",
    domain: "pantoneclo.mh",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+692",
  },
  {
    id: 145,
    name: "North Macedonia",
    code: "MK",
    domain: "pantoneclo.mk",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+389",
  },
  {
    id: 146,
    name: "Mali",
    code: "ML",
    domain: "pantoneclo.ml",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+223",
  },
  {
    id: 147,
    name: "Malta",
    code: "MT",
    domain: "pantoneclo.mt",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+356",
  },
  {
    id: 148,
    name: "Myanmar",
    code: "MM",
    domain: "pantoneclo.mm",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+95",
  },
  {
    id: 149,
    name: "Montenegro",
    code: "ME",
    domain: "pantoneclo.me",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+382",
  },
  {
    id: 150,
    name: "Mongolia",
    code: "MN",
    domain: "pantoneclo.mn",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+976",
  },
  {
    id: 151,
    name: "Northern Mariana Islands",
    code: "MP",
    domain: "pantoneclo.mp",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+1",
  },
  {
    id: 152,
    name: "Mozambique",
    code: "MZ",
    domain: "pantoneclo.mz",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+258",
  },
  {
    id: 153,
    name: "Mauritania",
    code: "MR",
    domain: "pantoneclo.mr",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+222",
  },
  {
    id: 154,
    name: "Montserrat",
    code: "MS",
    domain: "pantoneclo.ms",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+1",
  },
  {
    id: 155,
    name: "Martinique",
    code: "MQ",
    domain: "pantoneclo.mq",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+596",
  },
  {
    id: 156,
    name: "Mauritius",
    code: "MU",
    domain: "pantoneclo.mu",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+230",
  },
  {
    id: 157,
    name: "Malawi",
    code: "MW",
    domain: "pantoneclo.mw",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+265",
  },
  {
    id: 158,
    name: "Malaysia",
    code: "MY",
    domain: "pantoneclo.my",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+60",
  },
  {
    id: 159,
    name: "Mayotte",
    code: "YT",
    domain: "pantoneclo.yt",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+262",
  },
  {
    id: 160,
    name: "Namibia",
    code: "NA",
    domain: "pantoneclo.na",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+264",
  },
  {
    id: 161,
    name: "New Caledonia",
    code: "NC",
    domain: "pantoneclo.nc",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+687",
  },
  {
    id: 162,
    name: "Niger",
    code: "NE",
    domain: "pantoneclo.ne",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+227",
  },
  {
    id: 163,
    name: "Norfolk Island",
    code: "NF",
    domain: "pantoneclo.nf",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+672",
  },
  {
    id: 164,
    name: "Nigeria",
    code: "NG",
    domain: "pantoneclo.ng",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+234",
  },
  {
    id: 165,
    name: "Nicaragua",
    code: "NI",
    domain: "pantoneclo.ni",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+505",
  },
  {
    id: 166,
    name: "Niue",
    code: "NU",
    domain: "pantoneclo.nu",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+683",
  },
  {
    id: 167,
    name: "Netherlands",
    code: "NL",
    domain: "pantoneclo.nl",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+31",
  },
  {
    id: 168,
    name: "Norway",
    code: "NO",
    domain: "pantoneclo.no",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+47",
  },
  {
    id: 169,
    name: "Nepal",
    code: "NP",
    domain: "pantoneclo.np",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+977",
  },
  {
    id: 170,
    name: "Nauru",
    code: "NR",
    domain: "pantoneclo.nr",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+674",
  },
  {
    id: 171,
    name: "New Zealand",
    code: "NZ",
    domain: "pantoneclo.nz",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+64",
  },
  {
    id: 172,
    name: "Oman",
    code: "OM",
    domain: "pantoneclo.om",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+968",
  },
  {
    id: 173,
    name: "Pakistan",
    code: "PK",
    domain: "pantoneclo.pk",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+92",
  },
  {
    id: 174,
    name: "Panama",
    code: "PA",
    domain: "pantoneclo.pa",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+507",
  },
  {
    id: 175,
    name: "Pitcairn",
    code: "PN",
    domain: "pantoneclo.pn",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+64",
  },
  {
    id: 176,
    name: "Peru",
    code: "PE",
    domain: "pantoneclo.pe",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+51",
  },
  {
    id: 177,
    name: "Philippines",
    code: "PH",
    domain: "pantoneclo.ph",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+63",
  },
  {
    id: 178,
    name: "Palau",
    code: "PW",
    domain: "pantoneclo.pw",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+680",
  },
  {
    id: 179,
    name: "Papua New Guinea",
    code: "PG",
    domain: "pantoneclo.pg",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+675",
  },
  {
    id: 180,
    name: "Andorra",
    code: "AD",
    domain: "pantoneclo.ad",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+376",
  },
  {
    id: 181,
    name: "Puerto Rico",
    code: "PR",
    domain: "pantoneclo.pr",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+1",
  },
  {
    id: 182,
    name: "Korea, Democratic People's Republic of",
    code: "KP",
    domain: "pantoneclo.kp",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+850",
  },
  {
    id: 183,
    name: "Albania",
    code: "AL",
    domain: "pantoneclo.al",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+355",
  },
  {
    id: 184,
    name: "Paraguay",
    code: "PY",
    domain: "pantoneclo.py",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+595",
  },
  {
    id: 185,
    name: "Palestine, State of",
    code: "PS",
    domain: "pantoneclo.ps",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+970",
  },
  {
    id: 186,
    name: "French Polynesia",
    code: "PF",
    domain: "pantoneclo.pf",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+689",
  },
  {
    id: 187,
    name: "Qatar",
    code: "QA",
    domain: "pantoneclo.qa",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+974",
  },
  {
    id: 188,
    name: "Réunion",
    code: "RE",
    domain: "pantoneclo.re",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+262",
  },
  {
    id: 189,
    name: "Åland Islands",
    code: "AX",
    domain: "pantoneclo.ax",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+358",
  },
  {
    id: 190,
    name: "Russian Federation",
    code: "RU",
    domain: "pantoneclo.ru",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+7",
  },
  {
    id: 191,
    name: "Rwanda",
    code: "RW",
    domain: "pantoneclo.rw",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+250",
  },
  {
    id: 192,
    name: "Saudi Arabia",
    code: "SA",
    domain: "pantoneclo.sa",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+966",
  },
  {
    id: 193,
    name: "Sudan",
    code: "SD",
    domain: "pantoneclo.sd",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+249",
  },
  {
    id: 194,
    name: "Senegal",
    code: "SN",
    domain: "pantoneclo.sn",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+221",
  },
  {
    id: 195,
    name: "Singapore",
    code: "SG",
    domain: "pantoneclo.sg",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+65",
  },
  {
    id: 196,
    name: "South Georgia and the South Sandwich Islands",
    code: "GS",
    domain: "pantoneclo.gs",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+500",
  },
  {
    id: 197,
    name: "Saint Helena, Ascension and Tristan da Cunha",
    code: "SH",
    domain: "pantoneclo.sh",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+290",
  },
  {
    id: 198,
    name: "Svalbard and Jan Mayen",
    code: "SJ",
    domain: "pantoneclo.sj",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+47",
  },
  {
    id: 199,
    name: "Solomon Islands",
    code: "SB",
    domain: "pantoneclo.sb",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+677",
  },
  {
    id: 200,
    name: "Sierra Leone",
    code: "SL",
    domain: "pantoneclo.sl",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+232",
  },
  {
    id: 201,
    name: "El Salvador",
    code: "SV",
    domain: "pantoneclo.sv",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+503",
  },
  {
    id: 202,
    name: "San Marino",
    code: "SM",
    domain: "pantoneclo.sm",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+378",
  },
  {
    id: 203,
    name: "Somalia",
    code: "SO",
    domain: "pantoneclo.so",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+252",
  },
  {
    id: 204,
    name: "Saint Pierre and Miquelon",
    code: "PM",
    domain: "pantoneclo.pm",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+508",
  },
  {
    id: 205,
    name: "Serbia",
    code: "RS",
    domain: "pantoneclo.rs",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+381",
  },
  {
    id: 206,
    name: "South Sudan",
    code: "SS",
    domain: "pantoneclo.ss",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+211",
  },
  {
    id: 207,
    name: "Sao Tome and Principe",
    code: "ST",
    domain: "pantoneclo.st",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+239",
  },
  {
    id: 208,
    name: "Suriname",
    code: "SR",
    domain: "pantoneclo.sr",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+597",
  },
  {
    id: 209,
    name: "Anguilla",
    code: "AI",
    domain: "pantoneclo.ai",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+1",
  },
  {
    id: 210,
    name: "Angola",
    code: "AO",
    domain: "pantoneclo.ao",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+244",
  },
  {
    id: 211,
    name: "Sweden",
    code: "SE",
    domain: "pantoneclo.se",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+46",
  },
  {
    id: 212,
    name: "Eswatini",
    code: "SZ",
    domain: "pantoneclo.sz",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+268",
  },
  {
    id: 213,
    name: "Sint Maarten (Dutch part)",
    code: "SX",
    domain: "pantoneclo.sx",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+1",
  },
  {
    id: 214,
    name: "Seychelles",
    code: "SC",
    domain: "pantoneclo.sc",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+248",
  },
  {
    id: 215,
    name: "Syrian Arab Republic",
    code: "SY",
    domain: "pantoneclo.sy",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+963",
  },
  {
    id: 216,
    name: "Turks and Caicos Islands",
    code: "TC",
    domain: "pantoneclo.tc",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+1",
  },
  {
    id: 217,
    name: "Chad",
    code: "TD",
    domain: "pantoneclo.td",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+235",
  },
  {
    id: 218,
    name: "Togo",
    code: "TG",
    domain: "pantoneclo.tg",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+228",
  },
  {
    id: 219,
    name: "Thailand",
    code: "TH",
    domain: "pantoneclo.th",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+66",
  },
  {
    id: 220,
    name: "Tajikistan",
    code: "TJ",
    domain: "pantoneclo.tj",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+992",
  },
  {
    id: 221,
    name: "Tokelau",
    code: "TK",
    domain: "pantoneclo.tk",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+690",
  },
  {
    id: 222,
    name: "Turkmenistan",
    code: "TM",
    domain: "pantoneclo.tm",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+993",
  },
  {
    id: 223,
    name: "Timor-Leste",
    code: "TL",
    domain: "pantoneclo.tl",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+670",
  },
  {
    id: 224,
    name: "Tonga",
    code: "TO",
    domain: "pantoneclo.to",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+676",
  },
  {
    id: 225,
    name: "Trinidad and Tobago",
    code: "TT",
    domain: "pantoneclo.tt",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+1",
  },
  {
    id: 226,
    name: "Tunisia",
    code: "TN",
    domain: "pantoneclo.tn",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+216",
  },
  {
    id: 227,
    name: "Turkey",
    code: "TR",
    domain: "pantoneclo.tr",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+90",
  },
  {
    id: 228,
    name: "Tuvalu",
    code: "TV",
    domain: "pantoneclo.tv",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+688",
  },
  {
    id: 229,
    name: "Taiwan, Province of China",
    code: "TW",
    domain: "pantoneclo.tw",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+886",
  },
  {
    id: 230,
    name: "Tanzania, United Republic of",
    code: "TZ",
    domain: "pantoneclo.tz",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+255",
  },
  {
    id: 231,
    name: "Uganda",
    code: "UG",
    domain: "pantoneclo.ug",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+256",
  },
  {
    id: 232,
    name: "Ukraine",
    code: "UA",
    domain: "pantoneclo.ua",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+380",
  },
  {
    id: 233,
    name: "United States Minor Outlying Islands",
    code: "UM",
    domain: "pantoneclo.um",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+1",
  },
  {
    id: 234,
    name: "Uruguay",
    code: "UY",
    domain: "pantoneclo.uy",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+598",
  },
  {
    id: 235,
    name: "United States",
    code: "US",
    domain: "pantoneclo.us",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+1",
  },
  {
    id: 236,
    name: "Uzbekistan",
    code: "UZ",
    domain: "pantoneclo.uz",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+998",
  },
  {
    id: 237,
    name: "Holy See (Vatican City State)",
    code: "VA",
    domain: "pantoneclo.va",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+379",
  },
  {
    id: 238,
    name: "Saint Vincent and the Grenadines",
    code: "VC",
    domain: "pantoneclo.vc",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+1",
  },
  {
    id: 239,
    name: "Venezuela, Bolivarian Republic of",
    code: "VE",
    domain: "pantoneclo.ve",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+58",
  },
  {
    id: 240,
    name: "Virgin Islands, British",
    code: "VG",
    domain: "pantoneclo.vg",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+1",
  },
  {
    id: 241,
    name: "Virgin Islands, U.S.",
    code: "VI",
    domain: "pantoneclo.vi",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+1",
  },
  {
    id: 242,
    name: "Viet Nam",
    code: "VN",
    domain: "pantoneclo.vn",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+84",
  },
  {
    id: 243,
    name: "Vanuatu",
    code: "VU",
    domain: "pantoneclo.vu",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+678",
  },
  {
    id: 244,
    name: "Wallis and Futuna",
    code: "WF",
    domain: "pantoneclo.wf",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+681",
  },
  {
    id: 245,
    name: "Samoa",
    code: "WS",
    domain: "pantoneclo.ws",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+685",
  },
  {
    id: 246,
    name: "Yemen",
    code: "YE",
    domain: "pantoneclo.ye",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+967",
  },
  {
    id: 247,
    name: "South Africa",
    code: "ZA",
    domain: "pantoneclo.za",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+27",
  },
  {
    id: 248,
    name: "Zambia",
    code: "ZM",
    domain: "pantoneclo.zm",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+260",
  },
  {
    id: 249,
    name: "Zimbabwe",
    code: "ZW",
    domain: "pantoneclo.zw",
    lang: "en",
    langId: 1,
    currency: "USD",
    currencySymbol: "$",
    currencyId: 1,
    languageId: 1,
    isActive: false,
    phoneCode: "+263",
  },
];
