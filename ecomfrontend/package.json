{"name": "pantoneclo", "version": "1.0.0", "private": true, "scripts": {"build": "nuxt build", "build-sw": "SW=true nuxi build", "dev": "nuxt dev -p 3002", "dev-sw": "SW=true nuxi dev -p 3002", "generate": "nuxt generate", "preview": "PORT=3002 nuxt preview", "postinstall": "nuxt prepare", "start": "PORT=3002 node .output/server/index.mjs"}, "devDependencies": {"@nuxt/image": "^1.8.0", "@nuxtjs/i18n": "^8.5.5", "@nuxtjs/seo": "^2.0.0-rc.19", "@unocss/nuxt": "^66.1.2", "@zadigetvoltaire/nuxt-gtm": "^0.0.13", "nuxt": "^3.13.0", "nuxt-module-hotjar": "^1.3.2", "sass": "^1.90.0", "terser": "^5.31.6", "vuetify-nuxt-module": "^0.16.1", "webpack-bundle-analyzer": "^4.10.2"}, "dependencies": {"@fancyapps/ui": "^5.0.36", "@iconify/json": "^2.2.243", "@nuxt/vite-builder": "^3.13.0", "@nuxtjs/critters": "^0.7.2", "@pinia-plugin-persistedstate/nuxt": "^1.2.1", "@pinia/nuxt": "^0.5.4", "@revolut/checkout": "^1.1.21", "@sentry/nuxt": "^9.20.0", "@stripe/stripe-js": "^4.4.0", "@vuelidate/core": "^2.0.3", "@vuelidate/validators": "^2.0.4", "crypto-js": "^4.2.0", "lodash": "^4.17.21", "lodash.debounce": "^4.0.8", "lru-cache": "^11.0.0", "nuxt-swiper": "^1.2.2", "ohash": "^1.1.3", "pinia": "^2.2.2", "sass-embedded": "^1.80.3", "unhead": "^1.9.3", "v3-infinite-loading": "^1.3.2", "vite": "^6.3.5", "vue": "^3.4.38", "vuetify": "^3.7.3"}}