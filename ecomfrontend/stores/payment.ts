import { defineStore, storeToRefs } from "pinia";
import { fetchApiData } from "../utils/apiHelpers";

export const usePaymentStore = defineStore("payment", {
  state: () => ({
    loading: false,
    errorResponseMessage: "",
  }),
  actions: {
    async stripePayment(elements: any, stripe: any) {
      const config = useRuntimeConfig();
      const { billingAddress, shippingAddress } = storeToRefs(useShoppingCartStore());
      const appUrl = config.public.appBdBaseUrl?.replace(/\/$/, "");
      const { error, paymentIntent } = await stripe.confirmPayment({
        elements,
        confirmParams: {
          //   return_url: `${appUrl}/str/return?invoice=${invoice}`,
          //   redirect: "if_required",
          payment_method_data: {
            billing_details: {
              email: billingAddress.value?.email ? billingAddress.value?.email : shippingAddress.value?.email,
              name:
                billingAddress.value?.firstName || shippingAddress.value?.firstName +
                " " +
                billingAddress.value?.lastName || shippingAddress.value?.lastName,
              phone: billingAddress.value?.phone ? billingAddress.value?.phone : shippingAddress.value?.phone,
            },
          },
        },
        redirect: "if_required",
      });

      if (error) {
        return error;
        throw new Error(error);
      } else {
        return paymentIntent;
      }
    },

    getProductNameStringFormat(productList){
      if(productList && productList.length > 0){
        return productList?.map((item)=> item?.name).join(', ');
      }
    },

    getProductCategoryStringFormat(productList){
      if(productList && productList.length > 0){
        return productList?.map((item)=> item?.categoriesArr[0]).join(', ');
      }
    },

    async sslPayment(invoice: any) {
      const config = useRuntimeConfig();
      const cartStore = useShoppingCartStore();
      const { billingAddress, shippingAddress, total } = storeToRefs(cartStore);

      // SSL required product name in string
      let productName = this.getProductNameStringFormat(cartStore?.cart);
      let categoryName = this.getProductCategoryStringFormat(cartStore?.cart);

      const paymentOptions = {
        total_amount: total.value,
        currency: "BDT",
        tran_id: invoice,
        success_url: `${config.public.appBdBaseUrl}api/ssl-commerze-payment-status?status=success&invoiceNo=${invoice}`,
        fail_url: `${config.public.appBdBaseUrl}api/ssl-commerze-payment-status?status=failed&invoiceNo=${invoice}`,
        cancel_url: `${config.public.appBdBaseUrl}api/ssl-commerze-payment-status?status=cancel&invoiceNo=${invoice}`,
        emi_option: 0,
        cus_name: billingAddress.value?.firstName || shippingAddress?.value?.firstName,
        cus_email: billingAddress.value?.email || shippingAddress?.value?.email,
        cus_phone: billingAddress.value?.phone || shippingAddress?.value?.phone,
        shipping_method: "NO",
        product_name: productName || "-",
        product_category: categoryName || "-",
        product_profile: "Clothing & Apparels",
      };

      try {
        const response = await fetchApiData(
          "payment/ssl-commerz/create-session",
          {
            method: "post",
            body: paymentOptions,
          }
        );

        if (response?.isSuccess) {
          window.location.href = response.data?.redirectUrl;
        }
      } catch (error) {
        console.log("SSlcommerze Error", error);
      }
    },

    async revolutPayment(body: any) {
      const config = useRuntimeConfig();
      const response = await fetchApiData("payment/revolut/create-order", {
        headers: {
          "Content-Type": "application/json",
        },
        method: "POST",
        body: body,
      });

      if (response.isSuccess && response.data?.data?.checkout_url
      ) {
        return response.data?.data
      }
    },

  },
});
