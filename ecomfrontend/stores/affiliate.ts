import { defineStore } from 'pinia';
import { fetchApiData } from '../utils/apiHelpers';

export interface AffiliateProfile {
  id: number;
  userId: number;
  user?: {
    id: number;
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
  };
  countryId?: number;
  country?: {
    id: number;
    name: string;
    code: string;
  };
  status: 'pending' | 'active' | 'suspended' | 'rejected';
  commissionType: 'percentage' | 'fixed';
  commissionRate: number;
  totalEarnings: number;
  pendingEarnings: number;
  paidEarnings: number;
  totalClicks: number;
  totalConversions: number;
  totalOrders: number;
  conversionRate: number;
  paymentMethod?: string;
  paymentDetails?: any;
  bio?: string;
  websiteUrl?: string;
  socialMedia?: {
    instagram?: string;
    facebook?: string;
    twitter?: string;
    youtube?: string;
    tiktok?: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface AffiliateDiscount {
  id: number;
  affiliateId: number;
  discountId: number;
  discount?: {
    id: number;
    title: string;
    coupon: string;
    discountType: 'percentage' | 'fixed';
    amountType: 'total' | 'product';
    isActive: boolean;
    startDate?: string;
    endDate?: string;
    usageLimit?: number;
    usageCount?: number;
  };
  status: 'active' | 'inactive' | 'expired';
  maxUsage?: number;
  currentUsage: number;
  startDate?: string;
  endDate?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface AffiliateCommission {
  id: number;
  affiliateId: number;
  orderId: number;
  commissionAmount: number;
  commissionRate: number;
  commissionType: 'percentage' | 'fixed';
  orderAmount: number;
  discountAmount: number;
  status: 'pending' | 'approved' | 'paid' | 'cancelled';
  source: 'order' | 'manual' | 'bonus';
  notes?: string;
  approvedAt?: string;
  paidAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface AffiliateStats {
  totalEarnings: number;
  monthlyEarnings: number;
  totalClicks: number;
  totalConversions: number;
  conversionRate: number;
  earningsChange: number;
  monthlyChange: number;
  clicksChange: number;
  conversionChange: number;
}

export interface TrafficSource {
  source: string;
  percentage: number;
  color: string;
  clicks: number;
}

export interface EarningsData {
  date: string;
  earnings: number;
}

interface AffiliateState {
  profile: AffiliateProfile | null;
  discounts: AffiliateDiscount[];
  commissions: AffiliateCommission[];
  stats: AffiliateStats | null;
  trafficSources: TrafficSource[];
  earningsData: EarningsData[];
  loading: {
    profile: boolean;
    discounts: boolean;
    commissions: boolean;
    stats: boolean;
    dashboard: boolean;
  };
  error: string | null;
}

export const useAffiliateStore = defineStore('affiliate', {
  state: (): AffiliateState => ({
    profile: null,
    discounts: [],
    commissions: [],
    stats: null,
    trafficSources: [],
    earningsData: [],
    loading: {
      profile: false,
      discounts: false,
      commissions: false,
      stats: false,
      dashboard: false,
    },
    error: null,
  }),

  getters: {
    isApproved: (state) => state.profile?.status === 'active',
    isPending: (state) => state.profile?.status === 'pending',
    isRejected: (state) => state.profile?.status === 'rejected',
    isSuspended: (state) => state.profile?.status === 'suspended',

    activeDiscounts: (state) =>
      state.discounts.filter(d => d.status === 'active'),

    pendingCommissions: (state) =>
      state.commissions.filter(c => c.status === 'pending'),

    approvedCommissions: (state) =>
      state.commissions.filter(c => c.status === 'approved'),

    paidCommissions: (state) =>
      state.commissions.filter(c => c.status === 'paid'),

    totalPendingEarnings: (state) =>
      state.commissions
        .filter(c => c.status === 'pending' || c.status === 'approved')
        .reduce((sum, c) => sum + c.commissionAmount, 0),
    getAffiliateProfile: (state)=> state.profile,
    
  },

  actions: {
    // Profile Management
    async fetchProfile(userId: number) {
      this.loading.profile = true;
      this.error = null;

      try {
        const data = await fetchApiData(`affiliate/profile/by/user/${userId}`, {
          headers: {
            'Authorization': `Bearer ${useAuthStore().currentBasicUser?.token}`,
          },
        });
        
        console.log('Affiliate Profile:', data);
        if (data?.isSuccess) {
          this.profile = data?.data;
        } else {
          this.error = data?.message || 'Failed to fetch profile';
        }
        return response;
      } catch (error: any) {
        this.error = error.message || 'An error occurred';
        console.error('Error fetching affiliate profile:', error);
      } finally {
        this.loading.profile = false;
      }
    },

    async updateProfile(profileData: Partial<AffiliateProfile>) {
      if (!this.profile) return;

      this.loading.profile = true;
      this.error = null;

      try {
        const response = await fetchApiData(`affiliate/${this.profile.id}`, {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${useAuthStore().currentBasicUser?.token}`,
          },
          body: profileData,
        });

        if (response.isSuccess) {
          this.profile = { ...this.profile, ...response.data };
          return { isSuccess: true, message: 'Profile updated successfully' };
        } else {
          this.error = response.message || 'Failed to update profile';
          return { isSuccess: false, message: this.error };
        }
      } catch (error: any) {
        this.error = error.message || 'An error occurred';
        console.error('Error updating affiliate profile:', error);
        return { isSuccess: false, message: this.error };
      } finally {
        this.loading.profile = false;
      }
    },

    // Dashboard Data
    async fetchDashboardData(affiliateId: number) {
      this.loading.dashboard = true;
      this.error = null;

      try {
        const response = await fetchApiData(`affiliate/dashboard/${affiliateId}`, {
          headers: {
            'Authorization': `Bearer ${useAuthStore().currentBasicUser?.token}`,
          },
        });

        if (response.isSuccess) {
          const data = response.data;
          this.stats = data.stats;
          this.trafficSources = data.trafficSources || [];
          this.earningsData = data.earningsData || [];
        } else {
          this.error = response.message || 'Failed to fetch dashboard data';
        }
      } catch (error: any) {
        this.error = error.message || 'An error occurred';
        console.error('Error fetching dashboard data:', error);
      } finally {
        this.loading.dashboard = false;
      }
    },

    // Discounts Management
    async fetchDiscounts(affiliateId: number) {
      this.loading.discounts = true;
      this.error = null;

      try {
        const response = await fetchApiData(`affiliate/discounts/${affiliateId}`, {
          headers: {
            'Authorization': `Bearer ${useAuthStore().currentBasicUser?.token}`,
          },
        });

        if (response.isSuccess) {
          this.discounts = response.data;
        } else {
          this.error = response.message || 'Failed to fetch discounts';
        }
      } catch (error: any) {
        this.error = error.message || 'An error occurred';
        console.error('Error fetching affiliate discounts:', error);
      } finally {
        this.loading.discounts = false;
      }
    },

    // Commissions Management
    async fetchCommissions(affiliateId: number, filters?: any) {
      this.loading.commissions = true;
      this.error = null;

      try {
        const queryParams = new URLSearchParams();
        queryParams.append('affiliateId', affiliateId.toString());

        if (filters) {
          Object.keys(filters).forEach(key => {
            if (filters[key] !== undefined && filters[key] !== null) {
              queryParams.append(key, filters[key].toString());
            }
          });
        }

        const response = await fetchApiData(`affiliate/commissions?${queryParams}`, {
          headers: {
            'Authorization': `Bearer ${useAuthStore().currentBasicUser?.token}`,
          },
        });

        if (response.isSuccess) {
          this.commissions = response.data.items || response.data;
        } else {
          this.error = response.message || 'Failed to fetch commissions';
        }
      } catch (error: any) {
        this.error = error.message || 'An error occurred';
        console.error('Error fetching affiliate commissions:', error);
      } finally {
        this.loading.commissions = false;
      }
    },

    // Link Generation
    async generateTrackingUrl(params: {
      baseUrl: string;
      affiliateCode: string;
      utmSource?: string;
      utmMedium?: string;
      utmCampaign?: string;
      utmContent?: string;
      utmTerm?: string;
    }) {
      try {
        const response = await fetchApiData('affiliate/generate-tracking-url', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${useAuthStore().currentBasicUser?.token}`,
          },
          body: params,
        });

        if (response.isSuccess) {
          return { isSuccess: true, data: response.data };
        } else {
          return { isSuccess: false, message: response.message || 'Failed to generate tracking URL' };
        }
      } catch (error: any) {
        console.error('Error generating tracking URL:', error);
        return { isSuccess: false, message: error.message || 'An error occurred' };
      }
    },

    async trackClick(payload: {
      affiliateCode: string;
      landingPage: string,
      source: string;
      countryId: number;
      couponCode?: string;
      sessionId?: string;
      referrerUrl?: string;
      utmSource?: string;
      utmMedium?: string;
      utmCampaign?: string;
      utmContent?: string;
      utmTerm?: string;
    }) {
      try {
        const response = await fetchApiData('affiliate/track-click', {
          method: 'POST',
          body: payload,
        });

        if (!response.isSuccess) {
          console.error('API error tracking affiliate click:', response.message);
        }
        return response;
      } catch (error) {
        console.error('Failed to track affiliate click:', error);
      }
    },

    // Analytics
    async fetchUTMAnalytics(affiliateId: number, dateFrom?: string, dateTo?: string) {
      try {
        const queryParams = new URLSearchParams();
        if (dateFrom) queryParams.append('dateFrom', dateFrom);
        if (dateTo) queryParams.append('dateTo', dateTo);

        const response = await fetchApiData(`affiliate/${affiliateId}/utm-analytics?${queryParams}`, {
          headers: {
            'Authorization': `Bearer ${useAuthStore().currentBasicUser?.token}`,
          },
        });

        if (response.isSuccess) {
          return { isSuccess: true, data: response.data };
        } else {
          return { isSuccess: false, message: response.message || 'Failed to fetch analytics' };
        }
      } catch (error: any) {
        console.error('Error fetching UTM analytics:', error);
        return { isSuccess: false, message: error.message || 'An error occurred' };
      }
    },

    // Clear store data
    clearData() {
      this.profile = null;
      this.discounts = [];
      this.commissions = [];
      this.stats = null;
      this.trafficSources = [];
      this.earningsData = [];
      this.error = null;

      // Reset loading states
      Object.keys(this.loading).forEach(key => {
        this.loading[key as keyof typeof this.loading] = false;
      });
    },
  },

  // Note: Persistence would be added here if needed
  // persist: {
  //   key: 'affiliate-store',
  //   storage: persistedState.localStorage,
  //   pick: ['profile'], // Only persist profile data
  // },
});
