import { defineStore, storeToRefs } from "pinia";
import { getItems } from "~/utils/apiHelpers";
import type { LineItem, CartSuggestion } from "~/types/stores/Cart";
import type { Address } from "~/types/comoponent";

import { useAuthStore } from "~/stores/auth";
import {
  findSimpleProductIndex,
  variableProductIndex,
  variableProductCartIndex,
  findSimpleProductCartIndex,
} from "~/utils/functions";
import { useCouponStore } from "~/stores/coupon";

import { fetchApiData } from "../utils/apiHelpers";
import { useGlobalStore } from "./global";
import { useSqualoStore } from "./squalo";
import { showSnackbarResponse } from "../utils/functions";

export const useShoppingCartStore = defineStore({
  id: "shopping-cart",
  state: () => ({
    isModalOpen: false,
    countryId: 1,
    activeLanguageId: 1,
    loading: false,
    cart: [] as LineItem[],
    suggestions: [] as CartSuggestion[],
    subtotal: 0,
    discount: 0,
    tax: 0,
    total: 0,
    discountGoals: [] as any[],
    showDiscountGoal: false,
    currentDiscountGoal: null as any,
    availableFreeProducts: [] as any[],
    showFreeProductsModal: false,
    showCelebration: false,
    celebrationMessage: '',
    totalAfterDiscount: 0,
    isBillingShippingSame: true,
    shippingAddress: {} as Address,
    billingAddress: {} as Address,
    deliveryType: "",
    paymentMethod: "",
    countries: [],
    billingStates: [],
    billingCities: [],
    shippingStates: [],
    shippingCities: [],
    subscriberId: null,
    userId: null,
    shippingCharge: 0,
    codCharge: 0 as any,
    courierServiceId: null,
    courierId: null,
    cartUUID: null,
    couponId: null,
    id: null,
    createdAt: null,
    insideOutside: "inside",
    orderDiscountAmount: 0,
    orderDiscountId: null,
    shippingDiscountAmount: 0,
    shippingDiscountId: null
  }),

  getters: {
    cartSubTotal: (state) => {
      // const cartTotal = state.cart?.map(
      //   (item: any) =>
      //     (item.discountPrice > 0 ? item.discountPrice : item.unitPrice) *
      //     item.quantity
      // );
      // const totalAmount = cartTotal.reduce(
      //   (acc: any, cur: any) => acc + cur,
      //   0
      // );
      // state.total = totalAmount;
      return formatLocalePrice(+state.subtotal);
    },

    getOrderData: (state) => {
      const orderObject: any = {};
      orderObject.orderDetails = state.cart?.map((item: any) => ({
        productId: item.productId,
        quantity: item.quantity,
        variantId: item.variantId,
        unitPrice: item.unitPrice,
        discountPrice: 0,
      }));

      orderObject.userId = state.userId;
      return orderObject;
    },

    getTaxTotal: (state) => {
      // const website = useGlobalStore();
      // const tax: any = percentage(website.globals?.taxPercentage, state.total);

      // state.tax = tax;
      return formatLocalePrice(+state.tax);
    },

    getCodTotal: (state) => {
      // const website = useGlobalStore();
      // const codCharge: any = state.paymentMethod == "COD" ? state.codCharge : 0;
      // state.codCharge = codCharge;
      return state.codCharge;
    },

    getTotal: (state) => {
      // const website = useGlobalStore();

      // const total =
      //   Number(state.subtotal || 0) -
      //   (Number(state.discount || 0)) +
      //   (Number(state.shippingCharge || 0) + Number(state.codCharge || 0));
      // state.total = total;
      return formatLocalePrice(+state.total);
    },

    getDiscount: (state) => {
      // const website = useGlobalStore();
      return formatLocalePrice(+state.discount);
    },

    getCartItemsForOrder: (state) => {
      const keysToExtract = [
        "name",
        "productId",
        "sku",
        "size",
        "color",
        "quantity",
        "unitPrice",
        "discountPrice",
        "isMultiVariant",
      ];

      return state.cart.map((obj: any) => {
        const extractedObj: any = {};

        keysToExtract.forEach((key) => {
          extractedObj[key] = obj[key];
        });
        return extractedObj;
      });
    },

    getBillingAddressForOrder: (state: any) => {
      const keysToExtract = [
        "id",
        "address",
        "firstName",
        "lastName",
        "email",
        "countryCode",
        "phone",
        "countryId",
        "state",
        "city",
        "code",
        "zip",
        "postalcode",
        "street",
        "country",
        "title",
        "addressId",
      ];
      const extractedObject: any = {};
      keysToExtract.forEach((key) => {
        extractedObject[key] = state.billingAddress[key];
      });

      return extractedObject;
    },

    getShippingAddressForOrder: (state: any) => {
      const keysToExtract = [
        "id",
        "address",
        "firstName",
        "lastName",
        "countryCode",
        "email",
        "phone",
        "countryId",
        "state",
        "city",
        "code",
        "zip",
        "postalcode",
        "street",
        "country",
        "title",
        "nameStreet",
        "addressId",
      ];

      const extractedObject: any = {};
      keysToExtract.forEach((key) => {
        extractedObject[key] = state.shippingAddress[key];
      });

      return extractedObject;
    },

    getShippingCharge: (state: any) => {
      // const website = useGlobalStore();

      // let shippingCharge = 0;
      // if (parseFloat(website.globals?.shippingChargeFree) > 0) {
      //   shippingCharge =
      //     state.subtotal >= parseFloat(website.globals?.shippingChargeFree)
      //       ? 0
      //       : state.shippingCharge;
      // } else {
      //   shippingCharge = state.shippingCharge;
      // }

      // state.shippingCharge = shippingCharge;
      // return shippingCharge;
      return state.shippingCharge;
    },
    getAvailableFreeProducts: (state: any) => {
      return state.availableFreeProducts;
    },
  },

  actions: {
    reset() {
      this.cartUUID = null;
      this.$reset();
    },
    fillLastName() {
      // For bangladesh lastname is not required.
      // it should not be sent as Null because of backend lastName field null issue
      let key = "lastName";
      if (!this.billingAddress[key]) {
        if (this.billingAddress["firstName"]) {
          let splitedFirstName = this.billingAddress["firstName"].trim().split(' ');
          if (splitedFirstName?.length > 1) {
            this.billingAddress["firstName"] = splitedFirstName[0];
            this.billingAddress[key] = splitedFirstName.splice(1).join(" ");
          }
          else {
            this.billingAddress[key] = " ";
          }
        }
      }
    },
    fillShippingLastName() {
      // For bangladesh lastname is not required.
      // it should not be sent as Null because of backend lastName field null issue
      let key = "lastName";
      if (!this.shippingAddress[key]) {
        if (this.shippingAddress["firstName"]) {
          let splitedFirstName = this.shippingAddress["firstName"].trim().split(' ');
          if (splitedFirstName?.length > 1) {
            this.shippingAddress["firstName"] = splitedFirstName[0];
            this.shippingAddress[key] = splitedFirstName.splice(1).join(" ");
          }
          else {
            this.shippingAddress[key] = " ";
          }
        }
      }
    },
    fillShippingFromBilling() {
      // Sometimes courier selected user not filling name, email, phone
      const keysToExtract = [
        "firstName",
        "lastName",
        "email",
        "phone",
        // "postalcode",
      ];
      keysToExtract.forEach((key) => {
        if (!this.shippingAddress[key] && this.billingAddress[key]) {
          this.shippingAddress[key] = this.billingAddress[key] || "";
        }
      });
    },
    fillAddressIdToBillingAddress() {
      this.billingAddress["addressId"] = this.shippingAddress?.addressId ? this.shippingAddress?.addressId : null;
    },
    async productAddToCart(item: LineItem, activeLanguageId: number, countryId: number) {
      this.activeLanguageId = activeLanguageId;
      this.countryId = countryId;
      const { authenticated, currentBasicUser } = storeToRefs(useAuthStore());
      this.userId = authenticated.value ? currentBasicUser.value?.id : null;

      const stockQuantity = item?.stock;
      if (item.isMultiVariant) {
        const cartItemIndexMulti = variableProductIndex(item, this.cart);
        // if (this.cart[cartItemIndexMulti]?.quantity >= stockQuantity) {
        //   return {
        //     isSuccess: false,
        //     messasge: `Available Stock is ${stockQuantity}. You cannot add more than the available stock quantity.`,
        //   };
        // }
        if (cartItemIndexMulti === -1) {
          this.cart?.push(item);
        } else {
          this.cart[cartItemIndexMulti].unitPrice = item.unitPrice;
          this.cart[cartItemIndexMulti].discountPrice = item.discountPrice;
          this.cart[cartItemIndexMulti].quantity = item.quantity;
        }
      } else {
        const cartItemIndex = findSimpleProductIndex(item.productId, this.cart);
        if (cartItemIndex === -1) {
          this.cart.push(item);
        } else {
          this.cart[cartItemIndex].discountPrice = item?.discountPrice;
          this.cart[cartItemIndex].unitPrice = item.unitPrice;
          this.cart[cartItemIndex].quantity = item.quantity;
        }
      }
      // this.subtotal = this.getCartItemSubTotal();
      if (this.id === null || this.cartUUID === null || this.cartUUID === "") {
        this.cartUUID = `${Math.floor(Date.now() / 1000)}`;
        await this.addCartApi();
        this.fetchDiscountGoals();
      }
      else {
        await this.updateCartApi();
        this.fetchDiscountGoals();
      }

      return {
        isSuccess: true,
        messasge: `${item?.quantity} X ${item?.name} Added to Cart`,
      };
    },
    openModal() {
      this.isModalOpen = true;
    },
    closeModal() {
      this.isModalOpen = false;
    },
    productRemoveFromCart(item: any) {
      const couponStore = useCouponStore();
      couponStore.$reset();
      this.removeCartItemApi(item)
      // Fetch discount goals after cart update
      this.fetchDiscountGoals();
    },
    async cartItemIncrement(item: any) {
      const couponStore = useCouponStore();
      couponStore.$reset();

      const response = await this.updateCartQuantityApi(item.id, { quantityChange: 1, couponId: couponStore.couponId, courierServiceId: this.courierServiceId, insideOutside: this.insideOutside, deliveryType: this.deliveryType, paymentMethod: this.paymentMethod })
      if (response?.isSuccess && response.data && response?.data?.cart) {
        this.cart = response.data?.cart;
        // Fetch discount goals after cart update
        this.fetchDiscountGoals();
      }
      return response;
    },
    async cartItemDecrement(item: any) {
      const couponStore = useCouponStore();
      couponStore.$reset();
      
      const response = await this.updateCartQuantityApi(item.id, { quantityChange: -1, couponId: couponStore.couponId, courierServiceId: this.courierServiceId, insideOutside: this.insideOutside, deliveryType: this.deliveryType, paymentMethod: this.paymentMethod })
      if (response?.isSuccess && response.data && response?.data?.cart) {
        this.cart = response.data?.cart;
        // Fetch discount goals after cart update
        this.fetchDiscountGoals();
      }
      return response;
    },
    getCartItemSubTotal() {
      // const cartTotal = this.cart?.map(
      //   (item: any) =>
      //     (item.discountPrice > 0 || item?.discountPrice
      //       ? item.discountPrice
      //       : item.unitPrice) * item.quantity
      // );
      // return cartTotal.reduce((acc: any, cur: any) => acc + cur, 0);
      return this.subtotal;
    },
    cartItemQuantity(item: any) {
      if (item.isMultiVariant) {
        const cartItemIndexMulti = variableProductCartIndex(item, this.cart);
        return this.cart[cartItemIndexMulti]?.quantity;
      } else {
        const cartItemIndex = findSimpleProductCartIndex(item, this.cart);
        return this.cart[cartItemIndex]?.quantity;
      }
    },
    async updateStates(newData) {
      if (newData?.cart) {
        this.cart = newData?.cart;
      }
      this.discount = newData?.discount;
      this.codCharge = newData?.codCharge;
      this.shippingCharge = newData?.shippingCharge;
      this.subtotal = newData?.subtotal;
      this.tax = newData?.tax;
      this.total = newData?.total;
      this.createdAt = newData?.createdAt;
      this.orderDiscountAmount = newData?.orderDiscountAmount;
      this.orderDiscountId = newData?.orderDiscountId;
      this.shippingDiscountAmount = newData?.shippingDiscountAmount;
      this.shippingDiscountId = newData?.shippingDiscountId;
    },
    async fetchStates(countryId: number, type: string) {
      const config = useRuntimeConfig();
      this.loading = true;
      const states = await getItems(
        config.public.apiUrl + `state/getByCountryId/${countryId}`
      );

      if (type === "billing") {
        if (states && states?.items?.length > 0) {
          this.billingStates = states?.items.map((item) => ({
            id: item.id,
            name: item.name,
          }));
          this.loading = false;
        } else {
          this.billingStates = [];
          this.loading = false;
        }
      }
      if (type === "shipping") {
        if (states && states?.items?.length > 0) {
          this.shippingStates = states?.items.map((item) => ({
            id: item.id,
            name: item.name,
          }));
          this.loading = false;
        } else {
          this.shippingStates = [];
          this.loading = false;
        }
      }
    },
    async getSuggestedProducts() {
      const website = useGlobalStore();
      const countryId = website.globals.id;

      const productIds = this.cart.map(item => item?.productId?.id);
      if (!productIds) return;

      const config = useRuntimeConfig();
      const data = await $fetch(config.public.apiUrl + `product-suggestion/suggestions/${countryId}`, {
        method: "POST",
        body: {
          productsId: productIds,
        },
        params: {
          languageId: this.activeLanguageId,
        }
      });
      if (data?.isSuccess && data.data?.length) {
        this.suggestions = data?.data?.map(item => ({
          id: item.id,
          name: item.name,
          slug: item.slug,
          featuredImage: item.featured_image_gallery_urls,
          image: item.featured_imageurl,
          unitPrice: Number(item.unitprice),
          discountprice: item.discountprice,
        })) as CartSuggestion[]
      } else {
        this.suggestions = [];
      }
    },
    async createOrder() {
      const config = useRuntimeConfig();
      const { globals } = storeToRefs(useGlobalStore());
      // const { discountAmount, couponId } = storeToRefs(useCouponStore());
      const { authenticated, currentBasicUser } = storeToRefs(useAuthStore());

      // this.fillLastName();
      // this.fillAddressIdToBillingAddress();
      // this.fillShippingFromBilling();


      // return $fetch(config.public.apiUrl + `productOrder/web/confirmOrder`, {
      //   method: "POST",
      //   body: {
      //     ...this.$state,
      //     userId: authenticated.value ? currentBasicUser.value?.id : null,
      //     currencyId: globals.value?.currencyId,
      //   },
      // });

      this.fillShippingLastName();
      const squaloStore = useSqualoStore();
      const squaloUser = squaloStore.getSqualoUserLocalStorage;

      return $fetch(config.public.apiUrl + `cart/web/confirmOrder/${this.id}`, {
        method: "POST",
        body: {
          ...this.$state,
          subscriberId: squaloUser?.id,
          userId: authenticated.value ? currentBasicUser.value?.id : null,
          currencyId: globals.value?.currencyId,
        },
      });

    },

    // async fetchClientSecret(body: any) {
    //   const config = useRuntimeConfig();
    //   const response = await fetchApiData("payment/stripe/intents", {
    //     headers: {
    //       Authorization: "Bearer " + config.public.stripeSecretKey,
    //       "Content-Type": "application/json",
    //     },
    //     method: "POST",
    //     body: body,
    //   });

    //   if (response.isSuccess) {
    //     return response.data;
    //   }
    // },
    async fetchClientSecret(body: any) {
      const config = useRuntimeConfig();

      try {
        const res = await $fetch("payment/stripe/intents", {
          baseURL: config.public.apiUrl,
          method: "POST",
          headers: {
            Authorization: "Bearer " + config.public.stripeSecretKey,
            "Content-Type": "application/json",
          },
          body,
        });

        console.log("✅ Stripe success:", res);

        if (res?.isSuccess) {
          return res.data;
        } else {
          console.error("❌ Stripe returned isSuccess false:", res);
          return null;
        }
      } catch (err: any) {
        console.error("🔥 Stripe API failed:", err?.data || err?.message || err);
        showSnackbarResponse({ messasge: err?.data?.messasge});
        return null;
      }
    },

    async downloadOrderInvoice(invoiceNo: any) {
      const config = useRuntimeConfig();
      this.loading = true;

      // PDF with texts
      try{
        const { data, error } = await useFetch(
          config.public.apiUrl +
            `productOrder/web/stream-invoice/${invoiceNo}`,
          {
            method: "GET",
          }
        );
        if (error?.value) {
          console.error("API Error:", error.value);
          this.loading = false;
        } else if (data?.value) {
          const blob = new Blob([data?.value], { type: "application/pdf" });
          const pdfUrl = URL.createObjectURL(blob);

          // download via anchor tag
          const link = document.createElement("a");
          link.href = pdfUrl;
          link.download = `invoice_${invoiceNo}.pdf`;
          document.body.appendChild(link);
          link.click();

          // clean up this link
          document.body.removeChild(link);
          URL.revokeObjectURL(pdfUrl);

          this.loading = false;
        }
      }
      catch (error) {
        console.error("Error generating PDF:", error);
        this.loading = false;
      }
    },

    singleCartSqualoTrigger(response) {
      try {
        // Initial Cart adding to squalo
        const squaloStore = useSqualoStore();
        const squaloUser = JSON.parse(localStorage.getItem("squaloUser") ?? "{}") || {};
        // if (squaloUser?.id) {
          let payload = {
            storeId: response?.data?.countryId,
            countryId: response?.data?.countryId,
            cartResponse: { ...response?.data, subscriberId: squaloUser?.["id"] }
          }
          // if(!this.subscriberId){
          this.subscriberId = squaloUser?.["id"];
          // }
          // squaloStore.addSingleCartSqualo(payload);
        // }
      } catch (e) {
        console.log("Cart squalo trigger sync failed: ", e);
      }
    },
    async addCartApi() {
      const response = await fetchApiData('/cart', {
        method: 'POST',
        body: { ... this.$state }
      })
      if (response?.isSuccess && response.data) {
        this.id = response.data.id;
        this.updateStates(response?.data);
        this.singleCartSqualoTrigger(response);
        this.getSuggestedProducts();
      }
    },
    async updateCartApi() {
      const response = await fetchApiData(`/cart/${this.id}`, {
        method: 'PATCH',
        body: { ... this.$state }
      })
      if (response?.isSuccess && response.data) {
        this.updateStates(response?.data);
        this.singleCartSqualoTrigger(response);
        this.getSuggestedProducts();
      }
    },
    oldCartReinitialize() {
      try {
        if (!this.hasOwnProperty('createdAt') || this.createdAt === null) {
          this.addCartApi().catch((e) => {
            console.log("error in cart re initialize")
            this.reset();
          });
          console.log("Cart new version initialization successfull!")
        }
      }
      catch (e) {
        console.log("Cart new version initialization failed!", e)
      }
    },
    async updateCartQuantityApi(cartItemId: number, payload: any) {
      this.oldCartReinitialize();
      const response = await fetchApiData(`/cart/item/${this.countryId}/${cartItemId}/${this.cartUUID}`, {
        method: 'PATCH',
        body: payload,
      })
      if (response?.isSuccess && response.data) {
        this.updateStates(response?.data);
        this.singleCartSqualoTrigger(response);
      }
      return response;
    },
    async removeCartItemApi(cartObj: any) {
      const couponStore = useCouponStore();
      couponStore.$reset();

      this.oldCartReinitialize();
      const response = await fetchApiData(`/cart/cart-item/${cartObj.id}/${cartObj?.cartUUID}/${this.countryId}/${this.insideOutside}`, {
        method: 'DELETE',
      })
      if (response?.isSuccess && response.data) {
        this.updateStates(response?.data);

        // Fetch discount goals after cart update
        this.fetchDiscountGoals();

        this.singleCartSqualoTrigger(response);
        this.getSuggestedProducts();
      }
      return response;
    },
    async fetchCartItems(cartUUID: String) {
      const couponStore = useCouponStore();
      couponStore.$reset();

      this.oldCartReinitialize();
      const response = await fetchApiData(`/cart/by/cartUUID/${cartUUID}`, {
        method: 'GET',
        // headers:{
        //   "ngrok-skip-browser-warning": "true"
        // }
      })
      if (response?.isSuccess && response.data) {
        this.updateStates(response?.data);
      }
    },
    
    // Discount Goals Methods
    async fetchDiscountGoals() {
      if (!this.cartUUID || !this.countryId) return;
      
      try {
        const response = await fetchApiData(`/discounts/goals/${this.cartUUID}/${this.countryId}?languageId=${this.activeLanguageId}`, {
          method: 'GET',
          // headers: {
          //   "ngrok-skip-browser-warning": "true"
          // }
        });
        
        if (response?.isSuccess && response.data) {
          const previousFreeProducts = this.availableFreeProducts.length;
          this.discountGoals = response.data.nextGoals || [];
          this.availableFreeProducts = response.data.availableFreeProducts || [];
          
          // Show celebration if new free products became available
          if (this.availableFreeProducts.length > previousFreeProducts) {
            const newCount = this.availableFreeProducts.length - previousFreeProducts;
            this.celebrationMessage = `You've unlocked ${newCount} free product${newCount > 1 ? 's' : ''}!`;
            this.showCelebration = true;
            this.checkForFreeProducts();
          }
          
          this.showBestDiscountGoal();
        }
      } catch (error) {
        console.error('Failed to fetch discount goals:', error);
      }
    },

    showBestDiscountGoal() {
      if (this.discountGoals.length > 0) {
        // Show the goal with highest progress or value
        const bestGoal = this.discountGoals.reduce((best, current) => {
          if (current.progress > best.progress) return current;
          if (current.progress === best.progress) {
            const currentValue = current.goalAmount || current.goalQuantity || 0;
            const bestValue = best.goalAmount || best.goalQuantity || 0;
            return currentValue > bestValue ? current : best;
          }
          return best;
        });
        
        this.currentDiscountGoal = bestGoal;
        this.showDiscountGoal = true;
      } else {
        this.currentDiscountGoal = null;
        this.showDiscountGoal = false;
      }
    },

    hideDiscountGoal() {
      this.showDiscountGoal = false;
    },

    getAllDiscountGoals() {
      return this.discountGoals;
    },

    checkForFreeProducts() {
      if (this.availableFreeProducts.length > 0) {
        this.showFreeProductsModal = true;
      }
    },

    hideFreeProductsModal() {
      this.showFreeProductsModal = false;
    },

    async addFreeProductsToCart(selections: any[]) {
      // Add selected free products to cart
      for (const selection of selections) {
        for (const product of selection.products) {
          try {
            // Add product with discount applied
            await this.addToCartApi({
              productId: product.id,
              quantity: 1,
              discountId: selection.discountId,
              isFreeItem: true,
            });
          } catch (error) {
            console.error('Failed to add free product to cart:', error);
          }
        }
      }
      
      this.hideFreeProductsModal();
      // Refresh cart and discount goals
      this.fetchDiscountGoals();
    },
    getCartItem(id: number) {
      return this.cart?.find((item:any) => item?.id === id) || null;
    },
    isProductInsideCart(productId: number) {
      return this.cart?.find((item:any) => item?.productId?.id === productId) || false;
    },
    isProductInsideMultipleDiscount(productId: number) {
      return this.availableFreeProducts?.map((offer: any) => offer?.availableProducts?.some((product: any) => product?.id === productId)) || false;
    },
    getItemsWithSpecificDiscount(discountId: number) {
      return this.cart?.filter((item: any) => item?.discount?.id === discountId) || [];
    },
    getItemsWithSpecificDiscountTotalQuantity(discountId: number) {
      let items = this.cart?.filter((item: any) => item?.discount?.id === discountId)
      if(items?.length > 0){
        return items?.reduce((total:number, item: any)=> total + item?.quantity, 0) || 1;
      }
      return 1;
    },
    findDiscountFromAvailableFreeProducts(discountId: number) {
      return this.availableFreeProducts.find((offer: any) => offer?.discountId === discountId) || null;
    },
    findBestDiscountForProduct(productId) {
      let matchedOffers = <Array<any>>[];

      for (const offer of this.availableFreeProducts) {
        for (const product of offer.availableProducts) {
          if (product.id === productId) {
            matchedOffers.push({
              discountId: offer?.discountId,
              discountTitle: offer?.discountTitle,
              freeQuantity: offer?.freeQuantity,
              discountAmount: product?.discountAmount,
              discountPrice: product?.discountPrice,
              discountType: product?.discountType,
              unitPrice: parseFloat(product?.unitPrice),
              product,
            });
          }
        }
      }

      if (matchedOffers.length === 0) return null;
      // Compute actual discounted price and find the lowest
      return matchedOffers.reduce((best, current) => {
        return current.discountPrice < best.discountPrice ? current : best;
      });
    },
    
    getPercentageValue(basePrice: any, discountAmount: any) {
      const final = basePrice - (basePrice * (discountAmount / 100));
      return Math.max(final, 0).toFixed(2);
    },
    getFixedValue(basePrice: any, discountAmount: any) {
      const final = basePrice - discountAmount;
      return Math.max(final, 0).toFixed(2);
    },
    getDiscountPriceValue(product: any, bestDiscount: any) {
      if (!bestDiscount) {
        return product?.discountPrice ? Number(product?.discountPrice) : Number(product?.unitPrice);
      }

      const basePrice = product?.discountPrice
        ? Number(product?.discountPrice)
        : Number(product?.unitPrice);
      
      // same product inside multiple discount
      // let cartItem = this.isProductInsideMultipleDiscount(product?.id);
      // if(cartItem){
      //   // check same product inside multiple discount then return price with cart discount object
      //   let cartItem = this.isProductInsideCart(product?.id);
      //   if(cartItem){
      //     if(cartItem?.discount && cartItem?.discount?.id !== bestDiscount?.discountId){
      //       // Get current discount object where product is now added
      //       let currentDiscount = this.findDiscountFromAvailableFreeProducts(cartItem?.discount?.id);
      //       let productObject = currentDiscount?.availableProducts.find((p: any) => p.id === product?.id);
      //       if(productObject){
      //         if (productObject?.discountType === 'percentage') {
      //           return this.getPercentageValue(basePrice, productObject?.discountAmount);
      //         } else if (productObject?.discountType === 'fixed') {
      //           return this.getFixedValue(basePrice, productObject?.discountAmount);
      //         }
      //       }
      //     }
      //     return basePrice;
      //   }
      // }

      // multiple product if inside same discount
      // let cartItems = this?.getItemsWithSpecificDiscount(bestDiscount?.discountId);
      // if(cartItems.length > 0){
      //   let totalQuantity = cartItems.reduce((total, item) => total + item?.quantity, 0);
      //   if(totalQuantity > bestDiscount?.freeQuantity){
      //     return basePrice;
      //   }
      // }

      if (bestDiscount?.discountType === 'percentage') {
        return this.getPercentageValue(basePrice, bestDiscount?.discountAmount);
      } else if (bestDiscount?.discountType === 'fixed') {
        return this.getFixedValue(basePrice, bestDiscount?.discountAmount);
      }

      // fallback if discountType is unknown
      return basePrice;
    },
    getDiscountPriceValueForCartItem(product: any, bestDiscount: any) {
      if (!bestDiscount) {
        return product?.discountPrice ? Number(product?.discountPrice) : Number(product?.unitPrice);
      }

      const basePrice = product?.discountPrice
        ? Number(product?.discountPrice)
        : Number(product?.unitPrice);
      
      // same product inside multiple discount
      let cartItem = this.getCartItem(product?.cartItemId);
      if(cartItem){
        // check same product inside multiple discount then return price with cart discount object
        if(cartItem?.discount){
          // Get current discount object where product is now added
          let currentDiscount = this.findDiscountFromAvailableFreeProducts(cartItem?.discount?.id);
          let productObject = currentDiscount?.availableProducts.find((p: any) => p.id === product?.id);
          if(productObject){
            if (productObject?.discountType === 'percentage') {
              return this.getPercentageValue(basePrice, productObject?.discountAmount);
            } else if (productObject?.discountType === 'fixed') {
              return this.getFixedValue(basePrice, productObject?.discountAmount);
            }
          }
        }
        return basePrice;
      }
      // fallback if discountType is unknown
      return basePrice;
    }

  },

  persist: {
    storage: persistedState.localStorage,
  },
});

