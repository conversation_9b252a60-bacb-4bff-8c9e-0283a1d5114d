import {useWishlistStore} from "~/stores/wishlist";
import {State} from '~/types/stores/auth'
import {fetchApiData} from "../utils/apiHelpers";
import {fetchData} from "../composables/getApi";

export const useAuthStore = defineStore({
    id: 'pantoneclo-client-auth',
    state: (): State => ({
        authenticated: false,
        loading: true,
        currentBasicUser: undefined,
        currentUserProfile: {},
    }),
    actions: {
        async authenticateUser({email, password, userTypeId = 2, languageId = 1}: any) {
            const wishlistStore = useWishlistStore();
            try {
                const response = await fetchApiData('auth/login', {
                    method: 'post',
                    body: {
                        email,
                        password,
                        userTypeId
                    },
                })

                if (response.isSuccess) {
                    this.authenticated = true;
                    this.currentBasicUser = response?.data;
                    await this.getUser(this.currentBasicUser?.id);

                    // Only load wishlist for customer users
                    if (userTypeId === 2) { // Customer
                        wishlistStore.wishListsGuest = []
                        await wishlistStore.getWishlists(1, languageId)
                    }

                    return {
                        isSuccess: true,
                        messasge: response.messasge,
                        userType: userTypeId
                    }
                }

            } catch (error) {
                return {
                    isSuccess: false,
                    messasge: error.response?._data?.messasge
                }
            }
        },

        async authenticateAffiliate({email, password, languageId = 1}: any) {
            const { getAffiliateUserTypeId } = useUserTypes();
            const affiliateUserTypeId = getAffiliateUserTypeId();

            return await this.authenticateUser({
                email,
                password,
                userTypeId: affiliateUserTypeId,
                languageId
            });
        },

        async registerUser(user: any) {
            try {
                // Making the API call
                const response = await fetchApiData('auth/register', {
                    method: 'post',
                    body: user
                });
                // Assuming the response contains a success status
                if (response.isSuccess) {
                    return {
                        data: { ...response?.data },
                        isSuccess: true,
                        messasge: response.messasge // Correct the typo
                    };
                }
                // Handle case where registration is not successful
                return {
                    isSuccess: false,
                    messasge: response.messasge || 'An error occurred' // Provide a fallback
                };
            } catch (error: any) {
                // Handle the error and extract the error message
                return {
                    isSuccess: false,
                    messasge: error.response?._data?.messasge
                }
            }
        },

        async getUser(id: any) {
            this.loading = true;
            const authStore = useAuthStore();
            const response = await fetchApiData('user/' + id, {
                headers: {
                    'Access-Control-Allow-Origin': '*',
                    'Authorization': 'Bearer ' + authStore.currentBasicUser?.token
                },
            })
            if (response.isSuccess) {
                this.currentUserProfile = response.data;
                this.loading = false
            }
        },
        async updateUserProfile(userId: any, userData: any) {
            const authStore = useAuthStore();

            return await fetchApiData('user/updateUserProfile/' + userId, {
                headers: {
                    'Access-Control-Allow-Origin': '*',
                    'Authorization': 'Bearer ' + authStore.currentBasicUser?.token
                },
                method: 'PUT',
                body: userData
            })
        },
        logUserOut() {

            const wishlistStore = useWishlistStore();
            wishlistStore.$reset();
            const localePath = useLocalePath();

            this.$reset();
            navigateTo(localePath('/auth/login'))
        },

        async updatePassword(userId: number, password: string, oldPassword: any) {

            const response = await fetchApiData('auth/changePassword/' + userId, {
                method: 'PUT',
                body: {
                    password,
                    oldPassword
                }
            })

            if (response?.isSuccess) {
                await this.getUser(userId);
                return response;
            }
        },

        async getUserAddresses(){
            return await fetchApiData('address-shipping/getByUserId/' + this.currentBasicUser?.id, {
                headers: {
                    'Access-Control-Allow-Origin': '*',
                    'Authorization': 'Bearer ' + this.currentBasicUser?.token
                },
            })
        }
    },
    persist: true
})
