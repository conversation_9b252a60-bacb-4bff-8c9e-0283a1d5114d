import { defineStore } from "pinia";
import type { Country } from "~/types/stores/Shop";
import { fetchData } from "../composables/getApi";
import { useCurrentHost } from "../composables/useCurrentHost";

export const useCountryStore = defineStore({
  id: "country",
  state: () => ({
    loading: false,
    countries: [] as Country[],
  }),

  actions: {
    async getCountries() {
      const {getCurrentHost} = useCurrentHost();
      const response = await fetchData(
        "country/getAllActiveData" + "?domain=" + getCurrentHost()
      );
      if (response?.isSuccess) {
        console.log("+========", response?.data);
        this.countries = response?.data;
      }
    },
  },
});
