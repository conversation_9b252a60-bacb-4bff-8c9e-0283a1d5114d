import { acceptHMRUpdate, defineStore } from 'pinia';
import { useWebsiteStore } from "./website";
import { fetchData } from "../composables/getApi";
import { buildParams, scrollTop } from "../utils/functions";

export const useProductCategoryStore = defineStore('product-category', {
    state: (): ProductCategory => ({
        loading: false,
        products: [],
        relatedProducts: [],
        currentCategoryId: null,
        meta: {
            itemCount: 0,
            pageCount: 0,
            page: 1,
            take: 10
        } as Meta,
    }),


    actions: {

        async getProductsByCategorySlug(countryId: number, categorySlug: any = '', page = 1, take = 12, orderBy: any = null,
            brandId = null, color = null, size = null, discountRange = null, languageId = 1, categoryId: any = null) {

            // if( process?.client ) {
            //     scrollTop()
            // }

            const websiteStore = useWebsiteStore();

            if (websiteStore.categories.length === 0) {
                await websiteStore.getCategoriesPlain(countryId)
            }

            const category = categorySlug ? websiteStore.getCategoryBySlug(categorySlug, languageId) : null;

            let params: any = {
                categoryId: categoryId || category?.id,
                countryId,
                page,
                take,
                languageId,
            }


            if (orderBy) {
                params.orderBy = orderBy;
            } else {
                params.orderBy = ''
            }

            if (brandId) {
                params.brandId = brandId
            }

            if (color) {
                params.color = color.join(',')
            }

            if (size) {
                params.size = size.join(',')
            }

            if (discountRange) {
                params.discountRange = discountRange
            }

            return await fetchData('product/web/countryCategoryWiseProductsPagination?', params)

        },

        async getProductsByCategorySlugTaggingApi(countryId: number, categorySlug: any = '', page = 1, take = 12, orderBy: any = null,
            brandId = null, color = null, size = null, discountRange = null, languageId = 1, categoryId: any = null) {
            const websiteStore = useWebsiteStore();

            if (websiteStore.categories.length === 0) {
                await websiteStore.getCategoriesPlain(countryId)
            }

            console.log('categorySlug', categorySlug, categoryId);
            const category = categorySlug ? websiteStore.getCategoryBySlug(categorySlug, languageId) : null; // fallback category ID
            if(!categoryId && !category?.id){
                return {
                    isSuccess: false,
                    slugWrong: true,
                    status: 500,
                }
            }
            let params: any = {
                categoryId: categoryId || category?.id,
                countryId,
                page,
                take,
                languageId,
            }
            if (orderBy) params.orderBy = orderBy;
            else params.orderBy = '';

            if (brandId) params.brandId = brandId;
            if (color) params.color = color.join(',');
            if (size) params.size = size.join(',');
            if (discountRange) params.discountRange = discountRange;

            console.log('params LOL', params, window?.location)

            return await fetchData('category-product-related/web/getRelatedProductsById?', params)
        },


        async getRelatedProductsByIdOldApi(countryId: number, productId: number, page = 1, take = 12, orderBy: any = null, languageId = 1) {

            // if (process?.client) {
            //     scrollTop()
            // }

            let params = {
                productId,
                countryId,
                page,
                take,
                languageId,
            }

            // return await fetchData('product/web/getRelatedProductsById?', params)
            return new Promise((resolve, reject) => {
                const config = useRuntimeConfig();
                $fetch(`product/web/getRelatedProductsById/${buildParams(params)}`, {
                    method: "get",
                    baseURL: config.public.apiUrl,
                }).then((data) => {
                    resolve(data);
                }).catch((e) => {
                    reject(e);
                })
            })
        },
        async getRelatedProductsById(countryId: number, productId: number, page = 1, take = 12, orderBy: any = null, languageId = 1) {
            let params = {
                productId,
                countryId,
                page,
                take,
                languageId,
            }
            return new Promise((resolve, reject) => {
                const config = useRuntimeConfig();
                $fetch(`product-related/web/getRelatedProductsById/${buildParams(params)}`, {
                    method: "get",
                    baseURL: config.public.apiUrl,
                }).then((data) => {
                    resolve(data);
                }).catch((e) => {
                    reject(e);
                })
            })
        },

        // async getProductsByProductTags(tags: [], page = 1, take = 8) {
        //
        //     const {globals} = storeToRefs(useWebsiteStore())
        //
        //     let params = `countryId=${globals.value?.id}&page=${page}&take=${take}`
        //
        //     tags.forEach(tag => {
        //         params = params.concat(`&productTags[]=${tag}`)
        //     })
        //
        //     try {
        //         // return await $fetch<ApiResponse>(config.public.apiUrl + 'productTags/web/countryTagWiseProducts?' + encodeURI(params))
        //
        //         return await fetchData('productTags/web/countryTagWiseProducts', {
        //             countryId: globals.value?.id,
        //             page,
        //             take
        //         })
        //     } catch (error) {
        //         console.log(error)
        //     }
        // }

    },
})


if (import.meta.hot) {
    import.meta.hot.accept(acceptHMRUpdate(useProductCategoryStore, import.meta.hot))
}
