<template>
  <div class="image-gallery-wrapper">
    <div class="d-block d-sm-none">
      <LazySwiper
        :modules="[SwiperPagination, SwiperNavigation]"
        navigation
        pagination
        :grab-cursor="true"
      >
        <LazySwiperSlide v-for="slide in images" :key="slide.id">
          <LazySingleProductFancybox :options="{ Hash: false }">
            <NuxtLink
              :to="slide?.imageUrl ? localePath(slide?.imageUrl) : ''"
              data-fancybox="gallery-mobile"
            >
              <NuxtImg
                v-if="isImageMimeType(slide)"
                :src="replaceMediaDomain(slide?.imageGalleryUrls?.large || slide?.imageUrl) || slide?.imageGalleryUrls?.large || slide?.imageUrl"
                :alt="slide?.name"
                preload
                format="webp"
                width="452"
                height="565"
                class="h-100 w-100"
                fit="cover"
                quality="85"
              />
              <div
                v-else-if="isVideoMimeType(slide)"
                class="bg-black video-player video-player--with-vertical-video"
              >
                <video
                  class="video-player__viewer"
                  autoplay
                  muted
                  controls
                  loop
                  playsinline
                  style="aspect-ratio:4/5;"
                >
                  <source 
                    :src="replaceMediaDomain(slide?.imageUrl) || slide?.imageUrl" 
                    type="video/mp4" 
                  />
                </video>
              </div>
            </NuxtLink>
          </LazySingleProductFancybox>
        </LazySwiperSlide>
      </LazySwiper>
    </div>

    <div class="d-none d-sm-block">
      <v-row class="">
        <v-col cols="2">
          <div class="position-relative py-7" style="height: 490px">
            <Swiper
              direction="vertical"
              :grab-cursor="false"
              :modules="[SwiperThumbs, SwiperNavigation]"
              navigation
              @swiper="setThumbsSwiper"
              lazy-preloader-class="swiper-lazy-preloader swiper-lazy-preloader-white"
              :breakpoints="{
                '600': {
                  slidesPerView: 5,
                  spaceBetween: 2,
                },
                '768': {
                  slidesPerView: 5,
                  spaceBetween: 2,
                },
                '1400': {
                  slidesPerView: 5,
                  spaceBetween: 2,
                },
              }"
            >
              <SwiperSlide v-for="(slide, index) in images" :key="slide.id" class="mt-1" @click="handleSlideClick(index)">
                <div class="d-flex justify-center">
                  <NuxtImg
                    v-if="isImageMimeType(slide)"
                    :src="replaceMediaDomain(slide?.imageGalleryUrls?.thump || slide?.imageGalleryUrls?.medium) || slide?.imageGalleryUrls?.medium"
                    :alt="slide?.name"
                    width="70"
                    height="85"
                    quality="50"
                    format="webp"
                    cover
                    :class="{'border border-sm border-opacity-50 border-primary': currentSelectedSlide === index}"
                  />
                  <div
                    v-else-if="isVideoMimeType(slide)"
                    style="width: 70; height: 85;"
                    class="bg-black"
                    :class="{'border border-sm border-opacity-50 border-primary': currentSelectedSlide === index}"
                  >
                   <video
                      muted
                      playsinline
                      autoplay
                      style="max-width: 70px; max-height: 80px;"
                      controlslist="nodownload"
                    >
                      <source :src="replaceMediaDomain(slide?.imageUrl) || slide?.imageUrl" type="video/mp4" />
                    </video>
                  </div>
                </div>
              </SwiperSlide>
            </Swiper>
          </div>
        </v-col>
        <v-col cols="10">
          <Swiper
            class="h-100"
            :centered-slides="true"
            :slides-per-view="1"
            :grab-cursor="true"
            :mousewheel="false"
            :modules="[SwiperThumbs]"
            :thumbs="{ swiper: thumbsSwiper }"
            :speed="1000"
            effect="fade"
            lazy-preloader-class="swiper-lazy-preloader swiper-lazy-preloader-white"
          >
            <SwiperSlide v-for="slide in images" :key="slide.id" class="">
              <LazySingleProductFancybox :options="{ Hash: false }">
                <NuxtLink
                  :to="localePath(slide?.imageUrl ? slide?.imageUrl : '')"
                  data-fancybox="desktop-gal"
                  class="d-block"
                >
                  <NuxtImg
                    v-if="isImageMimeType(slide)"
                    :src="replaceMediaDomain(slide?.imageGalleryUrls?.large || slide?.imageUrl) || slide?.imageUrl"
                    :alt="slide?.name"
                    preload
                    loading="eager"
                    format="webp"
                    width="672"
                    height="840"
                    class="h-100 w-100"
                    quality="85"
                    fit="cover"
                  />
                  <div
                    v-else-if="isVideoMimeType(slide)"
                    class="bg-black video-player video-player--with-vertical-video"
                  >
                    <video
                      class="video-player__viewer"
                      autoplay
                      muted
                      loop
                      controls
                      playsinline
                      style="aspect-ratio:4/5;"
                    >
                      <source :src="replaceMediaDomain(slide?.imageUrl) || slide?.imageUrl" type="video/mp4" />
                    </video>
                  </div>
                </NuxtLink>
              </LazySingleProductFancybox>
            </SwiperSlide>
          </Swiper>
        </v-col>
      </v-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { GlobalImage } from "~/types/comoponent";
import { useMediaType } from "../../composables/useMediaType";

const localePath = useLocalePath();

const { replaceMediaDomain } = useMediaType();
const props = withDefaults(
  defineProps<{ loading?: boolean; images?: GlobalImage[] }>(),
  {}
);
const currentSelectedSlide = ref(0);
const thumbsSwiper = ref(null);
const { isImageMimeType, isVideoMimeType } = useMediaType();

const setThumbsSwiper = (swiper: any) => {
  thumbsSwiper.value = swiper;
};

const handleSlideClick = (slideIndex: number)=>{

  let swiperActiveIndex = thumbsSwiper?.value?.activeIndex;
  const lastVisibleIndex = swiperActiveIndex + thumbsSwiper?.value.params?.slidesPerView - 1;
  currentSelectedSlide.value = slideIndex; // store current clicked slide

  if (slideIndex === lastVisibleIndex) {
    thumbsSwiper.value?.slideTo(swiperActiveIndex + 1);

    // Note: if slider loop is True below condition works for most bottom photo
    // if(slideIndex + 1 >= props?.images?.length){
    //   const currentTranslate = thumbsSwiper.value.getTranslate();
    //   thumbsSwiper.value.setTranslate(currentTranslate - 70);
    // }
  } else if(slideIndex === swiperActiveIndex - 1) {
    let moveIndex = swiperActiveIndex - 1 > 0 ? swiperActiveIndex - 1 : 0;
    thumbsSwiper.value?.slideTo(moveIndex);
  }
}
</script>
<style lang="scss">
.swiper-vertical {
  position: initial;
  height: 100%;
  .swiper-wrapper {
    .swiper-slide {
      height: auto !important;
      box-sizing: border-box;
    }
  }
  .swiper-button-next {
    background-color: transparent;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%) rotate(90deg);
    top: auto;
  }
  .swiper-button-prev {
    background-color: transparent;
    top: 3.5%;
    left: 50%;
    transform: translateX(-50%) rotate(90deg);
  }
}

.video-player--with-vertical-video, .video-player__viewer {
  // max-width: 400px;
  margin: 0 auto;
}
.video-player__viewer {
    display: block;
    width: 100%;
}
@media (min-width: 440px) {
  .video-player--with-vertical-video {
    // background-image: var(--background-image-url);
    // background-repeat: no-repeat;
    // background-size: cover;
    // background-position: center;
  }
}
.image-gallery-wrapper {
  min-height: 560px; /* extra-small / phones */
}
@media (min-width: 270px) { /* small phones */
  .image-gallery-wrapper {
    min-height: 350px;
  }
}

@media (min-width: 320px) { /* small phones */
  .image-gallery-wrapper {
    min-height: 420px;
  }
}

@media (min-width: 360px) { /* small phones */
  .image-gallery-wrapper {
    min-height: 450px;
  }
}

@media (min-width: 414px) { /* large phones like iPhone Plus */
  .image-gallery-wrapper {
    min-height: 540px;
  }
}
@media (min-width: 480px) { /* small phones / large phones */
  .image-gallery-wrapper {
    min-height: 600px;
  }
}

@media (min-width: 768px) { /* tablets */
  .image-gallery-wrapper {
    min-height: 720px;
  }
}

@media (min-width: 1024px) { /* small laptops */
  .image-gallery-wrapper {
    min-height: 800px;
  }
}

@media (min-width: 1440px) { /* large desktops */
  .image-gallery-wrapper {
    min-height: 840px;
  }
}
</style>
