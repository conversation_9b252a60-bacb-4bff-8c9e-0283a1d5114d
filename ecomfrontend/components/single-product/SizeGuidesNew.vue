<template>
  <v-card class="size-chart-component">
    <v-card-text class="px-0">
      <div class="rounded-xl pa-2 bg-grey-lighten-4">
        <v-sheet class="d-flex rounded-xl">
          <v-btn
            @click="activeTab = 'body'"
            class="w-50 rounded-s-xl text-body-1 border text-capitalize"
            :class="{'bg-primary': activeTab === 'body'}"
            flat
            >{{$t('Size Chart')}}</v-btn
          >
          <v-btn
            @click="activeTab = 'product'"
            class="w-50 rounded-e-xl text-body-1 border text-capitalize"
            :class="{'bg-primary': activeTab === 'product'}"
            flat
            >{{$t('Product Measurement')}}
          </v-btn>
          <!--  -->
        </v-sheet>
      </div>
      <v-sheet>
        <div id="sizeChart">
          <div class="mt-2 d-flex ga-2 flex-column-reverse flex-md-row justify-space-between align-center">
            <p class="text-body-1 text-capitalize">
              * {{$t('measurements in')}}
            {{ selectedUnit === "in" ? $t("inches") : $t("centimeter") }}
            </p>
            <div class="text-right pa-1 bg-grey-lighten-4 overflow-hidden">
              <div style="height: 28px">
                <v-btn
                  @click="selectedUnit = 'in'"
                  size="small"
                  class="text-lowercase text-h6"
                  :class="{
                    'bg-black': selectedUnit === 'in',
                  }"
                  flat
                  >inch</v-btn
                >
                <v-btn
                  @click="selectedUnit = 'cm'"
                  size="small"
                  class="text-lowercase text-h6"
                  :class="{
                    'bg-black': selectedUnit === 'cm',
                  }"
                  flat
                  >cm</v-btn
                >
              </div>
            </div>
          </div>

          <template v-if="activeTab === 'product'">
            <div
              class="size-guide-pnt-scrollbar overflow-x-auto"
              v-for="(row, i) in productChartPayloads"
              :key="i"
            >
              <table class="size-chart-table">
                <caption class="bg-grey-lighten-4">
                  <h4 class="text-body-1 text-md-h6 py-2 border">
                    {{ $t(`${row.label}`) }}
                  </h4>
                </caption>
                <thead>
                  <tr class="bg-grey-lighten-4">
                    <th class="text-left" style="width: 40px;">{{$t('No.')}}</th>
                    <th class="text-left text-capitalize" style="min-width: max-content;">{{$t('Size')}}</th>
                    <th v-for="size in row.sizes" :key="size" class="text-center measurement-title">
                      {{ size }}
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(measurement, i) in row.data" :key="i">
                    <td class="text-left">{{ i+1 }}</td>
                    <td class="text-left">{{ $t(`${measurement.name}`) }}</td>
                    <td
                      v-for="size in row.sizes"
                      :key="size"
                      class="text-center measurement-value"
                    >
                      {{ measurement.values[size][selectedUnit] }}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <!-- <div v-if="props?.sizeChartData?.remarks" class="mt-2">
              <div v-html="props?.sizeChartData?.remarks"></div>
            </div> -->
            <div v-if="productChartImages && productChartImages?.length > 0" class="mt-3">
              <div v-for="(imageObj, index) in productChartImages" :key="'product_size_image_'+index">
                <NuxtImg
                  :src="imageObj?.imageUrl"
                  :alt="'product-size-image-'+index"
                  loading="lazy"
                  sizes="xs:100vw sm:100vw md:100vw"
                  format="jpg"
                  width="1700"
                  height="1300"
                  class="w-100 h-100"
                />
              </div>
            </div>
            <div v-else class="mt-3">
              <NuxtImg
                :src="image"
                loading="lazy"
                sizes="xs:100vw sm:100vw md:100vw"
                format="jpg"
                width="1700"
                height="1300"
                class="w-100 h-100"
              />
            </div>
          </template>
          <template v-else-if="activeTab === 'body'">
            <div
              class="size-guide-pnt-scrollbar overflow-x-auto"
              v-for="(row, i) in bodyChartPayloads"
              :key="i"
            >
              <table class="size-chart-table">
                <caption class="bg-grey-lighten-4">
                  <h4 class="text-body-1 text-md-h6 py-2 border">
                    {{ $t(`${row.label}`) }}
                  </h4>
                </caption>
                <thead>
                  <tr class="bg-grey-lighten-4">
                    <th class="text-left" style="width: 40px;">{{$t('No.')}}</th>
                    <th class="text-left" style="min-width: 150px">{{$t('Size')}}</th>
                    <th v-for="size in row.sizes" :key="size" class="text-center measurement-title">
                      {{ size }}
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(measurement, i) in row.data" :key="i">
                    <td class="text-left">{{ i+1 }}</td>
                    <td class="text-left">{{ $t(`${measurement.name}`) }}</td>
                    <td
                      v-for="size in row.sizes"
                      :key="size"
                      class="text-center measurement-value"
                    >
                      {{ measurement.values[size][selectedUnit] }}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <p v-if="props?.sizeChartData?.note?.noteData" class="mt-2">
              <span class="text-capitalize font-weight-bold">*{{ $t('note') }}</span>: {{ $t(`${props?.sizeChartData?.note?.noteData}`) }}
            </p>
            <div v-if="bodyChartImages && bodyChartImages?.length > 0" class="mt-3">
              <div v-for="(imageObj, index) in bodyChartImages" :key="'body_size_image_'+index">
                <NuxtImg
                  :src="imageObj?.imageUrl"
                  :alt="'body-size-image-'+index"
                  loading="lazy"
                  sizes="xs:100vw sm:100vw md:100vw"
                  format="jpg"
                  width="1700"
                  height="1300"
                  class="w-100 h-100"
                />
              </div>
            </div>
            <div v-else class="mt-3">
              <!-- <NuxtImg
                :src="image"
                placeholder
                loading="lazy"
                sizes="xs:100vw sm:100vw md:100vw"
                format="jpg"
                width="1700"
                height="1300"
                class="w-100 h-100"
              /> -->
            </div>
          </template>
        </div>
        <!-- <div id="sizeImage" class="mt-2">
          <NuxtImg
            :src="image"
            placeholder
            loading="lazy"
            sizes="xs:100vw sm:100vw md:100vw"
            format="jpg"
            width="1700"
            height="1300"
            class="w-100 h-100"
          />
        </div> -->
      </v-sheet>
    </v-card-text>
  </v-card>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import type { SizeChartDto, DataTypePayload } from "~/types/comoponent";
import { useCurrentHost } from "../../composables/useCurrentHost";

type SizeUnit = "cm" | "in";

const { getDomaCountryCode } = useCurrentHost();
const countryCode = getDomaCountryCode();

const props = defineProps<{
  sizeChartData: SizeChartDto;
}>();

const activeTab = ref('body');
const selectedUnit = ref<SizeUnit>("cm");

const payload: DataTypePayload[] = props.sizeChartData?.payload?.filter(
  (_item) => _item?.data?.length
);
const image = props.sizeChartData?.src;

const productChartPayloads: DataTypePayload[] = props.sizeChartData?.payload?.filter(
  (_item: any) => _item?.data?.length && _item?.type === 'product'
);
const bodyChartPayloads: DataTypePayload[] = props.sizeChartData?.payload?.filter(
  (_item: any) => _item?.data?.length && _item?.type === 'body'
);

const productChartImages = props.sizeChartData?.chartImages?.filter(
  (item: any) => item?.chartType === 'product'
);
const bodyChartImages = props.sizeChartData?.chartImages?.filter(
  (item: any) => item?.chartType === 'body'
);

const scrollTo = (id: string) => {
  // This function is for scrolldown to a specific section
  // @id: params for the HTML element id where we need to scroll down
  const element = document.getElementById(id);
  if (element) {
    element.scrollIntoView({ behavior: "smooth", block: "start" });
  }
};

const preSelectedUnit = () => {
  countryCode === "BD"
    ? (selectedUnit.value = "in")
    : (selectedUnit.value = "cm");
};

onMounted(() => {
  preSelectedUnit();
});
</script>

<style scoped>
.size-chart-table {
  width: 100%;
  border-collapse: collapse;
  margin: 16px 0;
}
.size-chart-table th,
.size-chart-table td {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: left;
}
.measurement-title{
  white-space: nowrap;
  min-width: max-content;
}
.measurement-value {
  white-space: nowrap;
  min-width: max-content;
}

.size-guide-pnt-scrollbar::-webkit-scrollbar-track {
  background-color: #F5F5F5;
}

.size-guide-pnt-scrollbar::-webkit-scrollbar {
  width: 6px;
  background-color: #F5F5F5;
}

.size-guide-pnt-scrollbar::-webkit-scrollbar-thumb {
  background-color: #696969;
  border: none;
}

</style>
