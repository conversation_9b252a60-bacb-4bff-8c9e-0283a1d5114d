<template>
  <v-navigation-drawer
    :width="drawerWidth"
    temporary
    :model-value="props.modelValue"
    @update:modelValue="updateValue"
    location="right"
    class="pa-2 rounded-s-xl pnt-scrollbar"
    :class="{
      'd-none': props.modelValue === false
    }"
    style="top: 2%; height: calc(96% + 0px); z-index: 1048"
  >
    <div class="px-2">
      <div class="d-flex justify-end">
        <!-- <div class="d-flex ga-2 align-center">
          <h4 class="text-h5 text-md-h4 text-capitalize">
            {{ $t("Size Chart") }}
          </h4>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            aria-hidden="true"
            focusable="false"
            role="presentation"
            class="icon icon-size-chart icon--line"
            viewBox="0 0 64 64"
            width="35"
            height="35"
            style="stroke-width: 3px"
          >
            <path
              d="M22.39 33.53c-7.46 0-13.5-3.9-13.5-8.72s6-8.72 13.5-8.72 13.5 3.9 13.5 8.72a12 12 0 0 1-.22 1.73"
              fill="none"
              stroke="currentColor"
            ></path>
            <ellipse
              cx="22.39"
              cy="24.81"
              rx="3.28"
              ry="2.12"
              fill="none"
              stroke="currentColor"
            ></ellipse>
            <path
              d="M8.89 24.81V38.5c0 7.9 6.4 9.41 14.3 9.41h31.92V33.53H22.39m24.39 0v7.44m-8.13-7.44v7.44m-8.13-7.44v7.44m-8.13-7.44v7.44"
              fill="none"
              stroke="currentColor"
            ></path>
          </svg>
        </div> -->

        <v-avatar
          class="rounded-circle pa-1 cursor-pointer"
          @click="updateValue(false)"
          variant="outlined"
          size="x-small"
        >
          <v-icon class="i-mdi:close" size="13" />
        </v-avatar>
      </div>
      <LazySingleProductSizeGuidesNew
        v-if="props.sizeChartData"
        :size-chart-data="props.sizeChartData"
      />

      <NuxtImg
        v-else
        :src="sizeChartImage?.imageUrl"
        loading="lazy"
        sizes="xs:100vw sm:100vw md:100vw"
        format="jpg"
        width="1700"
        height="1300"
        class="mt-3 w-100 h-100"
      />
    </div>
  </v-navigation-drawer>
</template>

<script setup lang="ts">
import type { GlobalImage, SizeChartDto } from "~/types/comoponent";
import { useDisplay } from "vuetify";
type Props = {
  modelValue?: boolean;
  sizeChartImage?: GlobalImage;
  sizeChartData?: SizeChartDto;
};

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
});

const emit = defineEmits(["update:modelValue"]);

const { smAndDown, mdAndUp } = useDisplay();

const drawerWidth = computed(() => {
  if (smAndDown.value) return 370; // Width for small screens (e.g., tablets)
  if (mdAndUp.value) return 550; // Width for medium and larger screens
  return 550; // Default width
});

const updateValue = (event: any) => {
  emit("update:modelValue", event);
};
</script>

<style scoped lang="scss">
/* Target the scrollbar */
:deep(.v-navigation-drawer__content::-webkit-scrollbar) {
  width: 6px !important;
  height: 6px !important;
}

/* Scrollbar track (background) */
:deep(.v-navigation-drawer__content::-webkit-scrollbar-track) {
  background: #e0e0e0 !important;
  border-radius: 10px !important;
}

:deep(.v-navigation-drawer__content::-webkit-scrollbar-button) {
  display: none !important;
}

/* Scrollbar thumb (the draggable part) */
:deep(.v-navigation-drawer__content::-webkit-scrollbar-thumb) {
  background: #a0a0a0 !important;
  border-radius: 10px;
}

/* Optional: Hover effect for the scrollbar thumb */
:deep(.v-navigation-drawer__content::-webkit-scrollbar-thumb:hover) {
  background: #808080 !important;
}
</style>
