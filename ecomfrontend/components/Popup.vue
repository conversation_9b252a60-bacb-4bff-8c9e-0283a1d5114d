<template>
  <v-dialog v-model="isDialogOpen" persistent :max-width="dialogWidth" class="dialog-wrapper">
    <v-sheet elevation="10" class="pa-4 pa-md-5 popup-wrapper">
      <NuxtImg
        :src="popUpImage"
        loading="lazy"
        sizes="xs:100vw sm:100vw md:100vw"
        format="webp"
        width="452"
        height="505"
        class="h-100 w-100 d-block popup-image"
        quality="85"
        alt="popup-image"
      />
      <div class="mt-4 d-flex flex-column align-center ga-1">
        <v-text-field
          variant="outlined"
          v-model="state.email"
          required
          :error-messages="v$.email.$errors.map((e) => $t(e.$message))"
          @blur="v$.email.$touch"
          @input="v$.email.$touch"
          :hide-details="v$.email.$errors?.length === 0"
          :label="$t('Enter your email address')"
          class="w-100"
          height="80"
          rounded
        >
          <template #append-inner>
            <v-btn
              color="error"
              :loading="loading"
              @click="submit"
              density="compact"
              icon
              variant="text"
            >
              <v-icon icon="i-mdi:arrow-right" size="25"></v-icon>
            </v-btn>
          </template>
        </v-text-field>

        <v-checkbox
          v-model="isSubscribed"
          density="compact"
          class="text-grey d-flex align-center"
          hide-details
          color="error"
        >
          <template #label>
            <p class="mt-1 mb-0 text-subtitle-2 text-sm-caption text-md-body-2 text-xl-body-1">{{ $t("Subscribed for promotional newsletters email")}}</p>
          </template>
        </v-checkbox>

        <p class="text-subtitle-2 mb-0 text-grey">
          {{ $t("Unsubscribe anytime") }}
        </p>
      </div>
      <v-hover v-slot="{isHovering, props}">
        <v-btn
          class="position-absolute pa-0"
          style="right: 4px; top: 4px;"
          @click="isDialogOpen = false"
          icon
          density="compact"
          variant="flat"
          width="18"
          height="18"
          v-bind="props"
          :color="isHovering ? 'error' : ''"
        >
          <v-icon size="15" class="i-mdi:close" />
        </v-btn>
      </v-hover>
    </v-sheet>
  </v-dialog>
</template>

<script setup lang="ts">
import { email, helpers, required } from "@vuelidate/validators";
import { useVuelidate } from "@vuelidate/core";
import { fetchApiData } from "../utils/apiHelpers";
import { showSnackbarResponse } from "../utils/functions";
import { computed, nextTick, onMounted, reactive, ref } from "vue";
import { useFetch } from "nuxt/app";
import { useCurrentHost } from "../composables/useCurrentHost";
import { useAuthStore } from "../stores/auth";
import { useSqualoStore } from "../stores/squalo";
import { useDisplay } from "vuetify";
import { useI18n } from 'vue-i18n';

const { xs, sm, md, lg, xl, xxl } = useDisplay();
const { getDomaCountryId, getDomaCountryCode, getDomaLanguageId } = useCurrentHost();
const countryId = getDomaCountryId();
const countryCode = getDomaCountryCode();
const languageId = getDomaLanguageId();
const authStore = useAuthStore();
const squaloStore = useSqualoStore();
const { t } = useI18n();

const isDialogOpen = ref(false);
let loading = ref(false);
const popUpImage = ref("");
const popUpTitle = ref("");

const popUpDescription = ref("");
const isSubscribed = ref(true);

const config = useRuntimeConfig();
const cookie = useCookie("popupShown-" + countryId, {
  expires: new Date(new Date().getTime() + 60 * 60 * 3 * 1000),
});

const initialState = {
  email: "",
};

const state = reactive({
  ...initialState,
});

const rules = {
  email: {
    required: helpers.withMessage("Email is required", required),
    email: helpers.withMessage("E-mail must be valid", email),
  },
};
const v$ = useVuelidate(rules, state);

const dialogWidth = computed(() => {
  if (xs.value) {
    return 320;
  }
  else if (sm.value) {
    return 360;
  }
  else if (md.value) {
    return 380;
  } 
  else if (lg.value) {
    return 480;
  }
  else if (xl.value) {
    return 550;
  }
  else if (xxl.value) {
    return 580;
  }
  else{
    return 550;
  }
});

const popupType = ref("SUBSCRIPTION_POPUP");
const showBannerPopup = async () => {
  if (!cookie.value) {
    setTimeout(async () => {
      const { data } = await useFetch<ApiResponse>(
        config.public?.apiUrl + `subscriptionPopup/web/country/${countryId}?type=${popupType?.value}`
      );
      if (data.value) {
        if (data.value?.isSuccess) {
          isDialogOpen.value = true;
          popUpImage.value = data.value?.data?.imageGallery?.imageUrl;
          popUpTitle.value = data.value?.data?.title;
          popUpDescription.value = data.value?.data?.details;
          cookie.value = true;
        }
      }
    }, 4000);
  }
};

onMounted(async () => {
  await nextTick();
  showBannerPopup();
});

const submit = async () => {
  if (!state.email) return;
  loading.value = true;
  const response = await fetchApiData("newsSubscription", {
    method: "POST",
    body: {
      email: state.email,
      countryCode: countryCode?.toLowerCase(),
      countryId: countryId,
      languageId: languageId,
      isSubscribed: isSubscribed?.value,
      userId: authStore?.currentBasicUser?.id ?? null,
      isMailSent: true,
    },
  });
  let msg = {
    ...response,
    messasge: t(response?.messasge),  // Accessing the translation here
  };
  showSnackbarResponse(msg);
  if (response?.isSuccess) {
    v$.value.$reset();
    for (const [key, value] of Object.entries(initialState)) {
      state[key] = value;
    }
    isDialogOpen.value = false;

    if (isSubscribed?.value) {
      let storeId = countryId;
      let payload = {
        storeId: storeId,
        countryId: countryId,
        ...response?.data,
      };
      // squaloStore.addEmailSubscription(payload).then((response) => {
      //   localStorage.setItem("squaloUser", JSON.stringify(response?.data));
      // });
    }
  }
  loading.value = false;
};
</script>
<style scoped>
.dialog-wrapper{
  @media screen and (min-width: 1264px) and (max-width: 1440px) {
    max-width: 390px;
  }
}
.popup-wrapper{
  border-radius: 10px;
}
.popup-image{
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  object-fit: cover;
  aspect-ratio: 4/5;
}

:deep(.v-checkbox .v-selection-control){
  display: flex;
  align-items: start;
}
:deep(.v-selection-control > .v-label){
  height: auto;
}
</style>
