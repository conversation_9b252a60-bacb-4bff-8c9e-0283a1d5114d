<template>
  <v-row class="justify-center align-center" style="height: 80vh">
    <v-col class="" lg="6">
      <div class="text-center">
        <NuxtImg :src="
            replaceMediaDomain('https://pantoneclos3bucket.s3.ap-south-1.amazonaws.com/pantoneclo-img/empty-shopping-cart.svg') ||
            'https://pantoneclos3bucket.s3.ap-south-1.amazonaws.com/pantoneclo-img/empty-shopping-cart.svg'
          "
          :alt="$t('Cart is empty')" sizes="xs:100vw sm:100vw md:100vw" format="webp" width="150" quality="85"
          height="150" class="w-75" fit="cover" />

        <h3 class="text-h3">{{ $t('Cart is empty') }}</h3>
      </div>
    </v-col>
  </v-row>
</template>
<script setup lang="ts">
import { useMediaType } from "../../composables/useMediaType";

const emit = defineEmits(['onClickBackBtn', 'closeCart'])
const { replaceMediaDomain } = useMediaType();

</script>