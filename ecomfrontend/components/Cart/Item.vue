<template>
  <v-sheet class="py-1 px-2 d-flex ga-2 align-center border rounded-xl">
    <div>
      <RouterLink
        :to="localePath('/product/' + props.item.slug)"
        class="text-decoration-none"
      >
        <v-avatar size="50" rounded="0">
          <NuxtImg
            :src="image"
            alt="Pantoneclo partner"
            format="webp"
            quality="85"
            fit="cover"
            width="50"
            height="60"
          />
        </v-avatar>
      </RouterLink>
    </div>
    <div class="w-100">
      <RouterLink
        :to="localePath('/product/' + props.item.slug)"
        class="text-decoration-none"
      >
        <h2
          class="truncated-text text-black text-body-2 text-md-body-1 text-lg-h6 font-weight-regular"
        >
          {{ item?.name }}
        </h2>
      </RouterLink>
      <div v-if="bestDiscount">
        <p
          class="mb-0 text-caption text-md-body-2 font-weight-bold d-flex align-center ga-1"
        >
          <span :class="{'text-green': props?.item?.discount, 'text-black': !props?.item?.discount }">{{ Number(bestDiscountPrice) === 0 ? 'Free' :  formatLocalePrice(bestDiscountPrice) }}</span>
          <span class="text-red text-decoration-line-through">{{
            formattedPrice
          }}</span>
        </p>
      </div>
      <div v-else class="d-flex ga-1 align-center">
        <p
          class="mb-0 text-caption text-md-body-2 font-weight-bold"
          v-if="item?.discountPrice"
        >
          {{ formattedDiscountedPrice }}
          <span class="text-red text-decoration-line-through">{{
            formattedPrice
          }}</span>
        </p>
        <p class="mb-0 text-caption text-md-body-2 font-weight-bold" v-else>
          {{ formattedPrice }}
        </p>
      </div>

      <div
        v-if="props.item.isMultiVariant"
        class="d-flex ga-1 flex-wrap align-center"
      >
        <p class="mb-0 text-caption text-md-body-2">
          <!-- {{ $t("Size") }}:  -->
          {{ props.item.size?.name }}
        </p>
        <span v-if="props.item.color">&#x2022;</span>
        <p class="text-caption text-md-body-2 mb-0" v-if="props.item.color">
          <!-- <span>{{ $t("Color") }}:</span> -->
          {{ props.item.color?.name }}
        </p>

        <button class="ms-1">
          <v-icon
            @click="removeItem"
            @keyup="removeItem"
            class="i-mdi:trash-can-outline text-error"
            size="16"
          />
        </button>
      </div>
    </div>
    <div class="d-flex flex-column ga-2">
      <div
        class="pa-1 border rounded-xl overflow-hidden d-flex justify-center"
        style="width: max-content"
      >
        <v-btn-toggle
          variant="outlined"
          density="compact"
          divided
          color="success"
          style="border-color: white !important; height: 25px"
        >
          <v-btn
            class="px-1 rounded-xl"
            size="20"
            @click="quantityMinus"
            :disabled="quantity < 2"
          >
            <v-icon size="10" class="i-mdi:minus" />
          </v-btn>

          <v-btn size="x-small">
            {{ quantity }}
          </v-btn>

          <v-btn class="px-1 rounded-xl" size="20" @click="quantityPlus">
            <v-icon size="10" class="i-mdi:plus" />
          </v-btn>
        </v-btn-toggle>
      </div>
      <div class="d-flex ga-2 justify-center align-center">
        <h2 class="text-caption text-md-body-2 font-weight-bold" style="width: max-content;">
          <span v-if="bestDiscount && props?.item?.discount" class="text-green me-1">{{ Number(bestDiscountTotalPrice) === 0 ? 'Free' : formatLocalePrice(bestDiscountTotalPrice) }}</span> 
          {{ formattedTotalPrice }}
        </h2>
      </div>
    </div>
  </v-sheet>
</template>
<script setup lang="ts">
import { formatLocalePrice } from "~/utils/money";
import type { LineItem } from "~/types/stores/Cart";
import type { GlobalImage } from "~/types/comoponent";
import { showSnackbarResponse } from "../../utils/functions";
import { computed, ref, watch } from "vue";
import { storeToRefs } from "pinia";

const cartStore = useShoppingCartStore();
const localePath = useLocalePath();

const props = defineProps<{
  item: any
}>();


const emit = defineEmits(['reset'])

const image = computed<GlobalImage>(
  () => props.item?.featuredImage?.imageGalleryUrls?.thump ?? ""
);

const id = props.item?.productId;
const price = computed(() => {
  return props.item?.unitPrice;
});
const discountedPrice = computed(() => {
  return props.item?.discountPrice;
});

const formattedPrice = computed(() => formatLocalePrice(price.value));
const formattedDiscountedPrice = computed(() =>
  formatLocalePrice(discountedPrice.value)
);

const quantity = computed(() => cartStore.cartItemQuantity(props.item));
const formattedTotalPrice = computed(() =>
  formatLocalePrice(
    (discountedPrice.value > 0 ? discountedPrice.value : price.value) * quantity.value
  )
);

const bestDiscount = ref(cartStore?.findBestDiscountForProduct(props?.item?.productId?.id));
const bestDiscountPrice = computed(()=>{
  if(bestDiscount.value){
    let productPayload = {
      ...props?.item,
      cartItemId: props?.item?.id,
      id: props?.item?.productId?.id,
    }
    let price = cartStore?.getDiscountPriceValueForCartItem(productPayload, bestDiscount.value);
    return price;
  }
})

const bestDiscountTotalPrice = computed(()=>{
  if(bestDiscount.value){
    let productPayload = {
      ...props?.item,
      cartItemId: props?.item?.id,
      id: props?.item?.productId?.id,
    }
    let price = cartStore?.getDiscountPriceValueForCartItem(productPayload, bestDiscount.value);
    if(props?.item?.discount){
      // const freeQuantity = props?.item?.discount?.buy_y_min_qty;
      // const itemsTotalQuantityWithDiscount = cartStore?.getItemsWithSpecificDiscountTotalQuantity(props?.item?.discount?.id);
      
      // if(itemsTotalQuantityWithDiscount > freeQuantity){
      //   const extraQuanlity = Math.abs(quantity.value - freeQuantity);
      //   price = Number(price) + Number(discountedPrice.value * extraQuanlity);        
      // }

      let finalPrice = Number(props?.item?.discountPrice);
      if(finalPrice === 0){
        finalPrice = Number(props?.item?.unitPrice);
      }
      return (Math.abs((finalPrice * quantity.value) - Number(props?.item?.discountAmount))).toFixed(2);
    }
    else{
      price = Number(discountedPrice.value) * quantity.value;
    }
    return price;
  }
})

const { getAvailableFreeProducts } = storeToRefs(cartStore);

watch(
  ()=> getAvailableFreeProducts.value,
  async () => {
    bestDiscount.value = cartStore?.findBestDiscountForProduct(props?.item?.productId?.id);
  },
  {
    deep: true,
    immediate: true,
  }
)

const quantityPlus = () =>
  cartStore.cartItemIncrement(props.item).then((response) => {
    if (response?.isSuccess === false) {
      showSnackbarResponse(response);
    } else {
      emit('reset');
    }
  });
const quantityMinus = () =>
  cartStore.cartItemDecrement(props.item).then((response) => {
    if (response?.isSuccess === false) {
      showSnackbarResponse(response);
    } else {
      emit('reset');
    }
  });
const removeItem = () =>
  cartStore
    .removeCartItemApi(props.item)
    .then((response) => {
      if (response?.isSuccess === false) {
        showSnackbarResponse(response);
      } else {
        emit('reset');
      }
    })
    .catch((e) => {
      showSnackbarResponse("Failed to remove cart item!");
    });
</script>

<style scoped>
.truncated-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
  max-width: 150px; /* Default for small screens */
}

@media (min-width: 600px) {
  .truncated-text {
    max-width: 200px;
  }
}

@media (min-width: 960px) {
  .truncated-text {
    max-width: 250px;
  }
}

@media (min-width: 1280px) {
  .truncated-text {
    max-width: 320px;
  }
}
</style>
