<template>
  <div>
    <!-- <template v-if="isMountedFetch">
      <LazyCategoryLoader />
    </template> -->

    <!-- <template v-if="categoryProducts?.length === 0">
      <LazyEcommerceProductEmpty />
    </template>
    <template v-else> -->

      <!-- <LazyCategoryHeading
        :categoryName="categoryName"
        :bannerImage="bannerImage"
      /> -->

      <client-only>
        <v-container fluid>
          <LazyGeneralPageBreadcrumb :breadcrumbs="breadcrumbs" />
        </v-container>
      </client-only>

      <client-only>
        <LazyCategoryFoldingFilter
          class="d-block d-md-none container bg-grey50 py-2"
        >
          <LazyCategoryFilters
            @onApplyFilters="onFilter"
            @onResetFilters="onReset"
            @onUpdateBrand="(val) => (selectedBrandIdForFilter = val)"
            @onUpdateSize="(val) => (selectedSizeForFilter = val)"
            @onUpdateColor="(val) => (selectedColorForFilter = val)"
            @onUpdateOrderBy="(val) => (selectedOrderByForFilter = val)"
            @onUpdateDiscountRange="(val) => (selectedDiscountRangeForFilter = val)"
            :category-id="categoryId"
            :selected-brand-id="selectedBrandIdForFilter"
            :selected-size="selectedSizeForFilter"
            :selected-color="selectedColorForFilter"
            :selected-order-by="selectedOrderByForFilter"
            :selected-discount-range="selectedDiscountRangeForFilter"
          />
        </LazyCategoryFoldingFilter>
        <LazyCategoryFilters
          class="d-none d-md-block container"
          @onApplyFilters="onFilter"
          @onResetFilters="onReset"
          @onUpdateBrand="(val) => (selectedBrandIdForFilter = val)"
          @onUpdateSize="(val) => (selectedSizeForFilter = val)"
          @onUpdateColor="(val) => (selectedColorForFilter = val)"
          @onUpdateOrderBy="(val) => (selectedOrderByForFilter = val)"
          @onUpdateDiscountRange="(val) => (selectedDiscountRangeForFilter = val)"
          :category-id="categoryId"
          :selected-brand-id="selectedBrandIdForFilter"
          :selected-size="selectedSizeForFilter"
          :selected-color="selectedColorForFilter"
          :selected-order-by="selectedOrderByForFilter"
          :selected-discount-range="selectedDiscountRangeForFilter"
        />
      </client-only>

      <v-container fluid>
        <v-row>
          <v-col
            class="px-1 px-md-2"
            cols="6"
            lg="3"
            sm="4"
            v-for="(product, index) in categoryProducts"
            :key="product?.id"
            v-intersect="onItemViewed(product)"
          >
            <template v-if="index < 8">
              <EcommerceProductSingleCard
                :product="product"
                :category-slug="slug || parentSlug"
              />
            </template>
            <template v-if="index >= 8">
              <client-only>
                <LazyEcommerceProductSingleCard
                  :product="product"
                  :category-slug="slug || parentSlug"
                />
              </client-only>
            </template>

          </v-col>
        </v-row>
        <!-- <v-pagination
          :length="pageCount"
          v-model="myCurrentPage"
          :total-visible="8"
          class="mt-7"
        >
        </v-pagination> -->
      </v-container>
    <!-- </template> -->
    <client-only>
      <LoadersInfiniteLoadMore
        v-if="categoryStore.meta.hasNextPage"
        :infiniteId="infiniteId"
        :running="loading"
        :hasNext="categoryStore.meta.hasNextPage"
        @loadMore="handleLoadMore"
      />
    </client-only>
  </div>
</template>
<script setup lang="ts">
import { getLocalizeName } from "~/utils/functions";
import { useTrackItemView } from "~/composables/useTrackItemView";
import { generateUniqueId } from "~/utils/functions";

const props = defineProps<{
  parentSlug?: string;
  slug?: string;
}>();

const { onItemViewed } = useTrackItemView("category_products_view", props.slug);

const { t } = useI18n();
const categoryStore = useProductCategoryStore();
const websiteStore = useWebsiteStore();
const bannerImage = ref();
const categoryId = ref(null);
const localePath = useLocalePath();

const { getDomaCountryId, getDomaLanguageId } = useCurrentHost();
const countryId = getDomaCountryId();
const languageId = getDomaLanguageId();

const slugKey = props.slug || props.parentSlug;
const route = useRoute();
const router = useRouter();

const categoryName = ref("");
const parentName = ref("");



// Initialize filters from URL query parameters
const selectedBrandIdForFilter = ref(route.query.brand ? Number(route.query.brand) : null);
const selectedSizeForFilter = ref(route.query.size ? route.query.size.toString().split(',').map(Number) : []);
const selectedColorForFilter = ref(route.query.color ? route.query.color.toString().split(',').map(Number) : []);
const selectedOrderByForFilter = ref(route.query.sort || null);
const selectedDiscountRangeForFilter = ref(route.query.discount || null);

const myCurrentPage = ref(1);

const infiniteId = ref(generateUniqueId("productsInfinite"));
const isTagginProductCategoryExist = ref(true);

const breadcrumbs = computed(() => {
  const _breadcrumbs = [
    {
      text: t("Home"),
      disabled: false,
      to: localePath("/"),
    },
  ];

  if (parentName.value) {
    _breadcrumbs.push({
      text: parentName.value,
      disabled: false,
      to: localePath("/category/" + props.parentSlug),
    });
  }

  if (props.slug) {
    _breadcrumbs.push({
      text: categoryName.value,
      disabled: true,
      to: "",
    });
  }

  return _breadcrumbs;
});

const loading = ref(true);
const categoryProducts = computed(() => categoryStore.products);
const isMountedFetch = ref(true);

const fetchCategoryProducts = async () => {
  loading.value = true;


  console.log('selectedDiscountRangeForFilter', selectedDiscountRangeForFilter);

  // new api fetching products those are tagged inside category manuall drag and drop
  if(isTagginProductCategoryExist.value === true){
    var response = await categoryStore.getProductsByCategorySlugTaggingApi(
      countryId,
      props.slug ? props.slug : props.parentSlug,
      myCurrentPage.value,
      8,
      selectedOrderByForFilter.value,
      selectedBrandIdForFilter.value,
      selectedColorForFilter.value,
      selectedSizeForFilter.value,
      selectedDiscountRangeForFilter.value,
      languageId,
      null // categoryId - using slug-based lookup
    );
  }

  if(response?.slugWrong === true){
    // If user trying to fetch wrong category via browser url
    categoryStore.products = [];
    categoryStore.meta = {};
    loading.value = false;
    isMountedFetch.value = false;
    return;
  }

  if(response?.isSuccess === false || !response?.data || response?.data?.length === 0){
    isTagginProductCategoryExist.value = false;
    response = await categoryStore.getProductsByCategorySlug(
      countryId,
      props.slug ? props.slug : props.parentSlug,
      myCurrentPage.value,
      12,
      selectedOrderByForFilter.value,
      selectedBrandIdForFilter.value,
      selectedColorForFilter.value,
      selectedSizeForFilter.value,
      selectedDiscountRangeForFilter.value,
      languageId,
      null // categoryId - using slug-based lookup
    );
  }

  if (response?.isSuccess) {
    categoryStore.products = [...categoryStore.products, ...response?.data];
    categoryStore.meta = response?.meta;
    if (response.meta.hasNextPage) {
      myCurrentPage.value += 1;
    }
    loading.value = false;
    isMountedFetch.value = false;
  } else {
    categoryStore.products = [];
    categoryStore.meta = {};
    loading.value = false;
    isMountedFetch.value = false;
  }
};

// Function to update URL with current filter state
const updateURL = () => {
  const query = { ...route.query };

  // Update query parameters based on current filter values
  if (selectedBrandIdForFilter.value) {
    query.brand = selectedBrandIdForFilter.value.toString();
  } else {
    delete query.brand;
  }

  if (selectedSizeForFilter.value && selectedSizeForFilter.value.length > 0) {
    query.size = selectedSizeForFilter.value.join(',');
  } else {
    delete query.size;
  }

  if (selectedColorForFilter.value && selectedColorForFilter.value.length > 0) {
    query.color = selectedColorForFilter.value.join(',');
  } else {
    delete query.color;
  }

  if (selectedOrderByForFilter.value) {
    query.sort = selectedOrderByForFilter.value;
  } else {
    delete query.sort;
  }

  if (selectedDiscountRangeForFilter.value) {
    query.discount = selectedDiscountRangeForFilter.value;
  } else {
    delete query.discount;
  }

  // Update URL without triggering navigation
  router.replace({ query });
};

// Apply filters - update URL and fetch products
const onFilter = async () => {
  updateURL(); // Update URL with current filters
  // fetchCategoryProducts will be triggered by URL change watcher
};

// Reset filters - clear URL and fetch products
const onReset = async () => {
  selectedBrandIdForFilter.value = null;
  selectedColorForFilter.value = [];
  selectedSizeForFilter.value = [];
  selectedOrderByForFilter.value = null;
  selectedDiscountRangeForFilter.value = null;

  // Clear URL query parameters
  const query = { ...route.query };
  delete query.brand;
  delete query.size;
  delete query.color;
  delete query.sort;
  delete query.discount;

  router.replace({ query });
  // fetchCategoryProducts will be triggered by URL change watcher
};

// onMounted(async () => {
//   categoryStore.products = [];
//   await fetchCategoryProducts();

//   if (websiteStore.categories.length === 0) {
//     await websiteStore.getCategoriesPlain(countryId);
//   }

//   const parentDataNew = websiteStore.getCategoryBySlug(
//     props.parentSlug,
//     languageId
//   );
//   const childDataNew = websiteStore.getCategoryBySlug(
//     props.slug,
//     languageId
//   );

//   if (parentDataNew) {
//     parentName.value = getLocalizeName(
//       languageId,
//       parentDataNew?.locale,
//       parentDataNew?.name
//     );
//     categoryName.value = getLocalizeName(
//       languageId,
//       parentDataNew?.locale,
//       parentDataNew?.name
//     );
//     bannerImage.value = parentDataNew?.bannerImage?.imageUrl;
//     categoryId.value = parentDataNew?.id;
//   }

//   if (childDataNew) {
//     categoryName.value = getLocalizeName(
//       languageId,
//       childDataNew?.locale,
//       childDataNew?.name
//     );
//     bannerImage.value = childDataNew?.bannerImage?.imageUrl;
//     categoryId.value = childDataNew?.id;
//   }
// });

const { data: fetchedProducts, pending } = await useAsyncData(
  `category-products-${slugKey}-${countryId}-${languageId}`,
  async () => {
    categoryStore.products = [];
    categoryStore.meta = {};
    myCurrentPage.value = 1;
    let response = null;
    isMountedFetch.value = true;

    if (isTagginProductCategoryExist.value === true) {
      response = await categoryStore.getProductsByCategorySlugTaggingApi(
        countryId,
        slugKey,
        myCurrentPage.value,
        8,
        selectedOrderByForFilter.value,
        selectedBrandIdForFilter.value,
        selectedColorForFilter.value,
        selectedSizeForFilter.value,
        selectedDiscountRangeForFilter.value,
        languageId,
        null,
      );
    }

    if (response?.slugWrong === true) {
      categoryStore.products = [];
      categoryStore.meta = {};
      return null;
    }

    if (!response?.isSuccess || !response?.data?.length) {
      isTagginProductCategoryExist.value = false;
      response = await categoryStore.getProductsByCategorySlug(
        countryId,
        slugKey,
        myCurrentPage.value,
        12,
        selectedOrderByForFilter.value,
        selectedBrandIdForFilter.value,
        selectedColorForFilter.value,
        selectedSizeForFilter.value,
        selectedDiscountRangeForFilter.value,
        languageId,
        null
      );
    }

    if (response?.isSuccess) {
      categoryStore.products = [...response.data];
      categoryStore.meta = response.meta || {};
      if (response.meta?.hasNextPage) {
        myCurrentPage.value += 1;
      }
    } else {
      categoryStore.products = [];
      categoryStore.meta = {};
    }

    if (websiteStore.categories.length === 0) {
      await websiteStore.getCategoriesPlain(countryId);
    }

    const parentData = websiteStore.getCategoryBySlug(props.parentSlug, languageId);
    const childData = websiteStore.getCategoryBySlug(props.slug, languageId);

    if (parentData) {
      parentName.value = getLocalizeName(languageId, parentData?.locale, parentData?.name);
      categoryName.value = getLocalizeName(languageId, parentData?.locale, parentData?.name);
      bannerImage.value = parentData?.bannerImage?.imageUrl;
      categoryId.value = parentData?.id;
    }

    if (childData) {
      categoryName.value = getLocalizeName(languageId, childData?.locale, childData?.name);
      bannerImage.value = childData?.bannerImage?.imageUrl;
      categoryId.value = childData?.id;
    }

    isMountedFetch.value = false;
    loading.value = false;
    // return {
    //   itemList: response?.data
    // }
  },
  { server: true }
);

// watch(myCurrentPage, () => {
//   useRouter().push({ query: { page: myCurrentPage.value } });
// });

// watch(myCurrentPage, async (newVal) => {
//   if (newVal) {
//     await fetchCategoryProducts();
//   }
// });

const handleLoadMore = async () => {
  console.log("Load more called");
  await fetchCategoryProducts();
};

// Watch for URL query changes to update filters and fetch products
watch(() => route.query, async (newQuery, oldQuery) => {
  // Update filter values from URL
  selectedBrandIdForFilter.value = newQuery.brand ? Number(newQuery.brand) : null;
  selectedSizeForFilter.value = newQuery.size ? newQuery.size.toString().split(',').map(Number) : [];
  selectedColorForFilter.value = newQuery.color ? newQuery.color.toString().split(',').map(Number) : [];
  selectedOrderByForFilter.value = newQuery.sort || null;
  selectedDiscountRangeForFilter.value = newQuery.discount || null;

  // Only fetch products if query actually changed (not initial load)
  if (oldQuery && JSON.stringify(newQuery) !== JSON.stringify(oldQuery)) {
    myCurrentPage.value = 1;
    categoryStore.products = [];
    isMountedFetch.value = true;
    await fetchCategoryProducts();
  }
}, { deep: true, immediate: false });

// Handle initial load with URL parameters
onMounted(() => {
  // If there are filter parameters in URL on initial load,
  // the filters are already set from the initialization above
  // and useAsyncData will handle the initial fetch
});
</script>
