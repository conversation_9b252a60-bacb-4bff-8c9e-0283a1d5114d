<template>
  <v-card
    rounded="0"
    class="pa-3 position-relative money-back"
    color="blue-grey-lighten-4"
  >
    <!-- <div v-if="moneyBack" class="money-back-icon-wrapper">
      <h6 class="text-h6 text-capitalize text-center">
        30 Days <span class="font-weight-light">Moneyback Guarantee</span>
      </h6>
    </div> -->
    <v-card-item class="pa-0 mt-2">
      <h6 class="text-body-2 text-md-h6 mb-1 text-uppercase">
        {{ $t("Order Summary") }}
      </h6>

      <div class="d-flex ga-2 align-center my-2">
        <p class="mb-0 text-body-1 font-weight-light">
          {{ $t("Total Item") }}:
        </p>
        <h6 class="font-weight-semibold text-body-1">
          {{ cartStore.cart.length }}
        </h6>
      </div>

      <div class="d-flex align-center justify-space-between my-2">
        <h6 class="text-body-1 font-weight-regular">
          {{ $t("Total Amount") }}
        </h6>
        <h6 class="font-weight-semibold text-body-1">
          {{ cartStore.cartSubTotal }}
        </h6>
      </div>

      <div v-if="cartStore.courierId && showShipping">
        <div
          class="d-flex align-center justify-space-between"
          v-if="shippingCharge > 0"
        >
          <h6 class="text-body-1 font-weight-regular">
            {{ $t("Shipping Charge") }}
          </h6>
          <h6 class="font-weight-semibold text-body-1">
            {{ formatLocalePrice(cartStore.getShippingCharge) }}
          </h6>
        </div>

        <div class="d-flex align-center justify-space-between" v-else>
          <h6 class="text-body-1 font-weight-regular">
            {{ $t("Shipping Charge") }}
          </h6>
          <h6 class="font-weight-semibold text-h6">{{ $t("Free") }}</h6>
        </div>
      </div>
      <div
        class="d-flex align-center justify-space-between my-2"
        v-if="cartStore.getCodTotal > 0 && showShipping"
      >
        <h6 class="text-body-1 font-weight-regular">{{ $t("COD Charges") }}</h6>
        <h6 class="font-weight-semibold text-body-1">
          {{ formatLocalePrice(cartStore.getCodTotal) }}
        </h6>
      </div>


      <div v-if="cartStore.shippingDiscountAmount && Number(cartStore?.shippingDiscountAmount) > 0" class="my-2">
        <div class="d-flex align-center justify-space-between">
          <h6 class="text-body-1 font-weight-regular text-error">
            <span>Shipping Discount</span>
          </h6>
          <h6 class="font-weight-semibold text-h6 text-error">{{ formatLocalePrice(cartStore.shippingDiscountAmount) }}</h6>
        </div>
      </div>
      <div v-if="cartStore.orderDiscountAmount && Number(cartStore?.orderDiscountAmount) > 0" class="my-2">
        <div class="d-flex align-center justify-space-between">
          <h6 class="text-body-1 font-weight-regular text-error">
            <span>Order Discount</span>
          </h6>
          <h6 class="font-weight-semibold text-h6 text-error">{{ formatLocalePrice(cartStore.orderDiscountAmount) }}</h6>
        </div>
      </div>

      <div
        class="d-flex align-center justify-space-between my-2"
        v-if="cartStore.discount > 0"
      >
        <h6 class="text-body-1 font-weight-regular">
          <span>{{ $t("Total") }}</span> <span>{{ $t("Discount") }}</span>
        </h6>
        <h6 class="font-weight-semibold text-body-1">
          {{ formatLocalePrice(cartStore.discount) }}
        </h6>
      </div>

      <div
        class="d-flex align-center justify-space-between my-2"
        v-if="cartStore.discount > 0"
      >
        <h6 class="text-body-1 font-weight-regular">
          {{ $t("After Discount Price") }}
        </h6>
        <h6 class="font-weight-semibold text-body-1">
          {{ formatLocalePrice(cartStore.subtotal - cartStore.discount) }}
        </h6>
      </div>

      <div class="d-flex align-center justify-space-between my-2">
        <h6 class="text-body-1 font-weight-regular">
          <!-- {{ $t("VAT/TAX/GST") }}<small>({{ $t("included") }})</small> -->
          <span class="text-uppercase">{{ $t("VAT") }}</span
          ><small class="text-capitalize">({{ $t("included") }})</small>
        </h6>
        <h6 class="font-weight-semibold text-body-1">
          {{ cartStore.getTaxTotal }}
        </h6>
      </div>

      <div
        class="d-flex align-center justify-space-between my-2"
        v-if="cartStore.getDiscount > 0"
      >
        <h6 class="text-body-1 font-weight-regular">{{ $t("Discount") }}</h6>
        <h6 class="font-weight-semibold text-body-1">
          {{ cartStore.getDiscount }}
        </h6>
      </div>

      <v-divider color="success"></v-divider>
      <div class="d-flex align-center justify-space-between my-3">
        <h6 class="text-h6">{{ $t("Grand Total") }}</h6>
        <h6 class="font-weight-semibold text-h5">
          {{ cartStore.getTotal }}
        </h6>
      </div>
    </v-card-item>

    <div class="coupon-apply mt-2" v-if="useOfCoupon">
      <p class="text-sm-body-2 text-blue" v-if="couponStore.couponCode">
        {{ $t("Coupon code") }} “{{ couponStore.couponCode }}“
        {{ $t("successfully applied") }}
      </p>
      <!-- <p class="text-sm-body-2 text-blue" v-if="! couponStore.couponCode">Use “PANTONE” coupon to get 5% discount</p> -->
      <!-- <p class="text-sm-body-2 text-blue" v-else>Coupon code “{{ couponStore.couponCode }}“ applied to cart.</p> -->
      <v-text-field
        density="compact"
        variant="outlined"
        bg-color="rgba(255, 255, 255, 1)"
        v-model="couponCode"
        :error-messages="couponError ? $t(couponError) : ''"
        clearable
        @click:clear="clearCoupon"
        :hide-details="couponError?.length === 0"
        rounded
      >
        <template #label>
          <span>
            {{ $t("Coupon code") }}
          </span>
        </template>
        <template #append>
          <v-btn
            variant="flat"
            color="primary"
            :disabled="couponBtnLoading || !couponCode"
            :loading="couponBtnLoading"
            @click="onCouponCodeSubmit"
            rounded
            >{{ $t("Apply") }}
          </v-btn>
        </template>
      </v-text-field>
      <p v-if="emilError || phoneError" class="text-error mt-2 mb-0 text-body-1">
        {{
          $t("Add email in shipping information or sign in to apply the coupon")
        }} + {{ $t("Phone") }}
      </p>
    </div>
  </v-card>
</template>
<script setup lang="ts">
import { useSnackbar } from "../../composables/useSnackbar";

const cartStore = useShoppingCartStore();

const props = defineProps<{
  moneyBack?: boolean;
  useOfCoupon?: boolean;
  shoppingCartStore?: any;
  showShipping?: boolean;
}>();

const snackbar = useSnackbar();
const couponStore = useCouponStore();
const couponBtnLoading = ref(false);
const couponCode = ref("");
const couponError = computed(() => couponStore.error);

const couponStoreEmail = computed(() => couponStore.email);

const authStore = useAuthStore();

const emilError = ref(false);
const phoneError = ref(false);

const customerEmail = computed(
  () =>
    couponStoreEmail.value ||
    cartStore?.shippingAddress?.email ||
    (authStore?.authenticated ? authStore?.currentBasicUser?.email : null)
);

const shippingCharge = computed(() => cartStore.getShippingCharge);

const onCouponCodeSubmit = async () => {
  couponBtnLoading.value = true;
  if (!customerEmail.value) {
    emilError.value = true;
    couponBtnLoading.value = false;
    return;
  } 
  else if(!cartStore?.shippingAddress?.phone){
    phoneError.value = true;
    couponBtnLoading.value = false;
    return;
  }
  else {
    emilError.value = false;
    phoneError.value = false;
    try{
      couponStore.getCouponData(couponCode.value, customerEmail.value, cartStore?.shippingAddress?.phone)
      .then((response)=>{
        couponBtnLoading.value = false;
        couponCode.value = "";
      })
      .finally(() => {
        couponBtnLoading.value = false;
      })
    }
    catch(error){
      couponBtnLoading.value = false;
    }
  }
};

const clearCoupon = () => {
  couponStore.clearCouponData();
  emilError.value = false;
};

onMounted(() => {
  cartStore.getCartItemSubTotal();
  if (couponStore.couponCode) {
    couponCode.value = couponStore.couponCode;
  } else {
    couponStore.error = "";
  }

  cartStore.paymentMethod = null;
  cartStore.codCharge = 0;
  cartStore.shippingCharge = 0;
  cartStore.courierServiceId = null;
  cartStore.courierId = 0;
});

watch(
  () => couponStore.couponCode,
  (newVal) => {
    couponCode.value = newVal;
  }
);
</script>
<style lang="scss" scoped>
</style>
