<template>
  <v-card
    variant="outlined"
    :rounded="0"
    class="border-0 pa-3 bg-white rounded-xl"
  >
    <v-card-text class="pa-1">
      <v-container class="pa-0">
        <v-row>
          <v-col
            v-if="countryCode != 'BD'"
            cols="12"
            :sm="countryCode === 'BD' ? '6' : '12'"
          >
            <!-- @update:modelValue="onCourierChange" -->
            <h5 class="text-h5 text-capitalize">
              {{ $t("Delivery Type") }}
            </h5>
            <div v-if="deliveryTypeBusy" class="mt-3 d-flex justify-center">
              <v-progress-circular
                indeterminate
                color="primary"
                :size="40"
                :width="4"
              ></v-progress-circular>
            </div>
            <v-radio-group
              v-else
              v-model="selectedCourier"
              return-object
              class="label-op-1 mt-4"
              loading
              hide-details
            >
              <v-row>
                <v-col
                  v-for="courier in getCuriers"
                  :key="courier.id"
                  cols="12"
                  :md="countryCode === 'BD' ? '12' : '3'"
                  class="py-1"
                >
                  <v-radio
                    :value="courier.code"
                    @click.stop.prevent="
                      selectedCourierInputHandler(e, courier)
                    "
                    class="py-2 border border-primary rounded-xl cursor-pointer h-100"
                    :class="{
                      'bg-light-blue-lighten-5':
                        courier.code === selectedCourier,
                    }"
                  >
                    <template v-slot:label>
                      <div class="d-flex flex-column">
                        <p class="mb-0">
                          {{
                            courier?.additional[locale.toLowerCase()] ||
                            courier?.name
                          }}
                        </p>
                        <p class="mb-0 font-weight-bold">
                          {{
                            cartStore?.insideOutside === "inside"
                              ? courier.priceInsideCity
                                ? formatLocalePrice(courier.priceInsideCity)
                                : $t("Free")
                              : courier.priceOutsideCity
                              ? formatLocalePrice(courier.priceOutsideCity)
                              : $t("Free")
                          }}
                        </p>
                      </div>
                    </template>
                  </v-radio>
                </v-col>
              </v-row>
            </v-radio-group>
          </v-col>
          <v-col v-if="countryCode === 'BD'" cols="12" sm="12">
            <h5 class="text-h5 text-capitalize">
              {{ $t("Delivery Area") }}
            </h5>
            <v-radio-group
              v-model="cartStore.insideOutside"
              @update:modelValue="selectedInsideOutside"
              return-object
              class="label-op-1 mt-3"
              loading
              hide-details
            >
              <v-row>
                <v-col cols="12" sm="6" md="6" lg="4" class="py-1">
                  <v-radio
                    :value="'inside'"
                    class="py-0 border border-primary rounded-xl cursor-pointer h-100"
                  >
                    <template v-slot:label>
                      <p class="mb-0">Inside Dhaka City</p>
                      <p class="ms-2 mb-0 font-weight-bold">
                        {{
                          selectedCourierObject?.priceInsideCity
                            ? formatLocalePrice(
                                selectedCourierObject.priceInsideCity
                              )
                            : $t("Free")
                        }}
                      </p></template
                    >
                  </v-radio>
                </v-col>
                <v-col cols="12" sm="6" md="6" lg="4" class="py-1">
                  <v-radio
                    :value="'outside'"
                    class="py-0 border border-primary rounded-xl cursor-pointer h-100"
                  >
                    <template v-slot:label>
                      <p class="mb-0">Outside Dhaka City</p>
                      <p class="ms-2 mb-0 font-weight-bold">
                        {{
                          selectedCourierObject?.priceOutsideCity
                            ? formatLocalePrice(
                                selectedCourierObject.priceOutsideCity
                              )
                            : $t("Free")
                        }}
                      </p>
                    </template>
                  </v-radio>
                </v-col>
              </v-row>
            </v-radio-group>
          </v-col>
        </v-row>
      </v-container>
      <v-sheet
        v-if="selectedCourier === 'wp'"
        class="my-4 pa-2 rounded-xl"
        color="light-blue-lighten-5"
      >
        <span class="font-weight-bold">
          {{ $t("Warehouse address") }}
        </span>
        <p class="my-1">
          {{
            $t(
              "Orders placed today will be ready for pickup the next business day"
            )
          }}
        </p>
      </v-sheet>
      <v-sheet
        class="my-4 pa-2 rounded-xl"
        v-if="
          initialStates.deliveryType !== 'Warehouse Pickup' &&
          selectedPoint.country
        "
        color="light-blue-lighten-5"
      >
        <h4 class="text-body-1 font-weight-medium">
          {{ $t("Address") }}: {{ selectedPoint.address }},
          {{ selectedPoint.city }}, {{ selectedPoint.zip }},
          {{ selectedPoint.country }}
        </h4>
      </v-sheet>
      <!--PAYMENT METHOD Starts-->
      <h5 class="text-h5 mt-6 text-capitalize">
        {{ $t("Payment Method") }}
      </h5>
      <v-row class="mt-1">
        <v-col cols="12" lg="12" class="pt-1">
          <v-radio-group
            v-model="initialStates.paymentMethod"
            @update:modelValue="selectedPaymentMethod"
            :rules="[(value: any) => !!value]"
            :error="!initialStates.paymentMethod && submitted"
            hide-details
            :error-messages="
              !initialStates.paymentMethod && submitted
                ? [$t('Please select payment method')]
                : []
            "
          >
            <div class="mb-1" v-if="countryCode !== 'BD'">
              <!-- <v-radio
                value="stripe"
                color="primary"
                class="py-1 label-op-1 border rounded-xl"
              >
                <template v-slot:label>
                  <div class="d-flex align-center w-100">
                    <div>
                      <h5 class="text-body-1">
                        {{ $t("Debit/Credit/Master Card Payment") }}
                      </h5>
                    </div>
                  </div>
                </template>
              </v-radio> -->
              <v-radio
                v-if="config.public.revolutMode?.length > 2"
                value="revolut"
                color="primary"
                class="py-1 label-op-1 border rounded-xl mt-1"
              >
                <template v-slot:label>
                  <div class="d-flex align-center w-100">
                    <div>
                      <h5 class="text-body-1">
                        <!-- {{ $t("Revolut") }} -->
                        {{ $t("Card Payment") }}
                      </h5>
                    </div>
                  </div>
                </template>
              </v-radio>
            </div>

            <v-sheet
              elevation="0"
              class="mb-2 rounded-xl"
              color="light-blue-lighten-5"
              v-if="initialStates.paymentMethod == 'stripe'"
            >
              <CheckoutStripe @stripe-selected="stripeSelected" />
            </v-sheet>

            <div class="mb-2" v-if="countryCode === 'BD'">
              <v-radio
                value="ssl-commerce"
                color="primary"
                class="label-op-1 border rounded-xl"
              >
                <template v-slot:label>
                  <div class="d-flex align-center w-100">
                    <div>
                      <h5 class="text-body-1">
                        {{ $t("Card, Mobile & Net banking") }}
                      </h5>
                    </div>
                    <div class="ml-auto flex-shrink-0">
                      <v-img
                        src="https://pantoneclos3bucket.s3.ap-south-1.amazonaws.com/pantoneclo-img/ssl-payments.png"
                        height="50"
                        width="150"
                      />
                    </div>
                  </div>
                </template>
              </v-radio>
            </div>
            <v-card
              elevation="0"
              class="mb-2"
              variant="outlined"
              v-if="initialStates.paymentMethod == 'ssl-commerce'"
            >
              <CheckoutSSLCommerce />
            </v-card>

            <div class="mb-2" v-if="isCodShowing">
              <v-radio
                v-if="countryCode !== 'AT'"
                value="COD"
                color="primary"
                class="py-1 label-op-1 border rounded-xl"
              >
                <template v-slot:label>
                  <div class="d-flex align-center w-100">
                    <div>
                      <h5 class="text-body-1">
                        {{ $t("Cash on Delivery") }}
                      </h5>
                      <span class="d-block text-subtitle-2 text-wrap">{{
                        $t("Pay with cash when your order is delivered")
                      }}</span>
                    </div>
                  </div>
                </template>
              </v-radio>
            </div>
          </v-radio-group>
        </v-col>
      </v-row>
      <!--PAYMENT METHOD Ends-->

      <v-row no-gutters>
        <v-col cols="12" class="order-2 order-md-1">
          <p
            class="mb-0 px-3"
            style="color: #007eb9; font-size: 12px; font-weight: bold"
          >
            {{ $t("We take security very seriously") }}
          </p>

          <v-checkbox
            v-model="agreeToTerms"
            :rules="[rules]"
            :error="!agreeToTerms && submitted"
            :error-messages="
              !agreeToTerms && submitted
                ? ['You must accept the terms and conditions']
                : []
            "
            hide-details
          >
            <template v-slot:label>
              <div class="text-body-2">
                {{ $t("I agree to the") }}
                <NuxtLink
                  :to="localePath('/terms-conditions')"
                  target="_blank"
                  style="color: orange"
                  >{{ $t("Terms & Conditions") }}</NuxtLink
                >
                ,
                <NuxtLink
                  :to="localePath('/return-refund')"
                  target="_blank"
                  style="color: orange"
                  >{{ $t("Return Policy") }}</NuxtLink
                >
                &
                <NuxtLink
                  :to="localePath('/privacy-policy')"
                  target="_blank"
                  style="color: orange"
                  >{{ $t("Privacy Policy") }}</NuxtLink
                >
              </div>
            </template>
          </v-checkbox>
        </v-col>
        <!-- <v-col cols="12" md="3" class="order-1 order-md-2">
              <v-img
                src="https://pantoneclos3bucket.s3.ap-south-1.amazonaws.com/pantoneclo-img/mcafee-secure.jpg"
                width="70"
                class="float-md-end"
              />
            </v-col> -->
      </v-row>

      <div class="d-flex justify-space-between">
        <!-- <v-btn @click="returnToShipping">{{
              $t("Return to Shipping")
            }}</v-btn> -->
        <v-btn
          color="primary"
          :disabled="
            !agreeToTerms || !courierId || !initialStates.paymentMethod
          "
          @click="orderConfirm"
          class="w-100 text-h5"
          height="50"
          rounded
          >{{
            initialStates.paymentMethod !== "stripe" && initialStates.paymentMethod !== "revolut"
              ? $t("Confirm Order")
              : $t("Complete Order")
          }}</v-btn
        >
      </div>
    </v-card-text>
  </v-card>
  <CheckoutGlsPopup ref="glsCheckoutRef" @selected="onSelectedGlsPickup" />
  <CheckoutPaketaPickup
    ref="packetaCheckoutRef"
    @selected="onSelectedPacketaPickup"
    @busy="(val) => (deliveryTypeBusy = val)"
  />

  <CheckoutPacticPopup
    ref="pacticCheckoutRef"
    @selected="onSelectedPacticPickup"
    @busy="(val) => (deliveryTypeBusy = val)"
  />
</template>
<script setup lang="ts">
import { ref, onMounted, toRaw, watch } from "vue";

import { useI18n } from "vue-i18n";
import { scrollTop } from "../../utils/functions";
import { storeToRefs } from "pinia";
import { fetchApiData } from "../../utils/apiHelpers";

const globalStore = useGlobalStore();
const localePath = useLocalePath();

// Use i18n to get the current locale
const config = useRuntimeConfig();
const { globals } = storeToRefs(globalStore);

const { t, locale } = useI18n();
const isModalOpen = ref(false); // Modal open state
// Props and emits
const props = defineProps<{
  countryCode?: string;
}>();

const cartStore = useShoppingCartStore();

const emit = defineEmits([
  "returnToShippingAndBilling",
  "confirmOrder",
  "selectedStripe",
]);

const stripeSelected = (data: any) => {
  emit("selectedStripe", data);
};

// Validation rules
const rules = (value: any) =>
  !!value || t("You must accept the terms and conditions");
const deliveryRules = (value: any) =>
  !!value || t("Please select delivery type");
const paymentRules = (value: any) =>
  !!value || t("Please select payment method");

const wantToSubscribe = ref(false);
const agreeToTerms = ref(false);
const submitted = ref(false);
const getCuriers = ref([]);
const shippingFee = ref(0);
const courierId = ref(null);
const selectedCourier = ref("");
const selectedCourierObject = ref("");
const selectedCourierCOD = ref("");
const selectedPoint = ref({
  ...cartStore.shippingAddress,
  address: "",
  city: "",
  code: "",
  country: "",
  zip: "",
  countryCode: null,
});
const initialStates = ref({
  deliveryType: "",
  paymentMethod: "",
  selectedCourier: "",
});
const couponStore = useCouponStore();

// watch(
//   () => cartStore?.insideOutside,
//   (newVal, oldVal) => {
//     if (newVal != oldVal) {
//       selectedCourier.value = "";
//     }
//   }
// );

const isCodShowing = ref(true);
// Emit functions
const returnToShipping = () => {
  cartStore.codCharge = 0;
  cartStore.shippingCharge = 0;
  cartStore.courierId = null;
  emit("returnToShippingAndBilling");
};

const selectedPaymentMethod = (method: any) => {
  cartStore.paymentMethod = method;
  if(method != "" && method != null){
    cartStore.updateCartApi();
  }
  cartStore.getCodTotal;

  if (method == "COD" && selectedCourierCOD.value) {
    cartStore.codCharge = selectedCourierCOD.value;
  } else{
    cartStore.codCharge = 0;
  }
};

const selectedInsideOutside = (insideOutside: any) =>{
  if(insideOutside != "" && insideOutside != null){
    cartStore.updateCartApi();
  }
}

// Order confirmation function
const orderConfirm = async () => {
  submitted.value = true;
  if (
    !agreeToTerms.value ||
    !initialStates.value.deliveryType ||
    !initialStates.value.paymentMethod ||
    !courierId.value
  ) {
    return;
  } else {
    const paymentValues = toRaw(initialStates.value);
    if (wantToSubscribe.value === true) {
      const response = await fetchApiData("newsSubscription", {
        method: "POST",
        body: {
          email: cartStore?.shippingAddress?.email,
          countryCode: globalStore?.globals?.code?.toLowerCase(),
          countryId: globalStore?.globals?.id,
          isSubscribed: true,
          isMailSent: true,
        },
      });
      if (response?.isSuccess) {
        cartStore.subscriberId = response?.data?.id;
      }
    }
    emit("confirmOrder", paymentValues);
  }
};


const resetCartQty = () => {
  // cartStore.codCharge = 0;
  // cartStore.shippingCharge = 0;
  // cartStore.courierId = null;
  // cartStore.paymentMethod = null;
  // cartStore.courierServiceId = null;
  // shippingFee.value = 0;
  // courierId.value = null;
  // selectedCourier.value = "";
  // selectedCourierCOD.value = "";
  // cartStore.insideOutside = ""
  // initialStates.value = {
  //   deliveryType: "",
  //   paymentMethod: "",
  //   selectedCourier: "",
  // }
}

// Load Packeta Widget script dynamically
onMounted(() => {

  // resetCartQty()

  scrollTop();
});






const courierSelectedBackup = ref(null);
const applyCouriesSelectedFromBackup = () => {
  const courier = courierSelectedBackup.value;
  if(!courier) return;

  selectedPaymentMethod("");
  initialStates.value.paymentMethod = "";

  courierId.value = courier.id;
  shippingFee.value =
    cartStore?.insideOutside === "inside"
      ? courier?.priceInsideCity
      : courier?.priceOutsideCity;

  cartStore.courierServiceId = courier.id;

  cartStore.shippingCharge =
    cartStore?.insideOutside === "inside"
      ? courier?.priceInsideCity
      : courier?.priceOutsideCity;

  cartStore.courierId = courier.id;
}



const packetaCheckoutRef = ref(null);
const deliveryTypeBusy = ref(false);
// Open the Packeta widget
const openPacketaWidget = (packetaApiKey) => {
  packetaCheckoutRef.value?.open(
    packetaApiKey,
    globalStore?.globals?.code?.toString()?.toLowerCase() || "si"
  );
};

// Open pactic
const pacticCheckoutRef = ref(null);
const openPacticWidget = () => {
  pacticCheckoutRef.value?.open(globalStore?.globals?.code);
};

// Handle pactic pickup point
const onSelectedPacticPickup = (pickupPoint: any) => {
  if (pickupPoint) {

    applyCouriesSelectedFromBackup()


    selectedPoint.value = {
      address: pickupPoint.address || "",
      city: pickupPoint.city || "",
      zip: pickupPoint.zip || "",
      code: pickupPoint.zip || "",
      country: pickupPoint.country || "",
    };
    cartStore.shippingAddress = {
      ...cartStore.shippingAddress, // Keep other values unchanged
      address: pickupPoint?.address,
      city: pickupPoint.city,
      zip: pickupPoint.zip,
      code: pickupPoint.zip,
      country: pickupPoint.country,
      countryId: globals?.value?.id,
    };
    initialStates.value.deliveryType = "Courier";
    cartStore.deliveryType = initialStates.value.deliveryType;
    cartStore.updateCartApi();

    selectedCourier.value = "pactic";
    isCodShowing.value = false;
    cartStore.codCharge = 0;
    // cartStore.isBillingShippingSame = false;
  } else {
    console.log("No point selected");
  }
};

// Handle the selected Packeta pickup point
const onSelectedPacketaPickup = (pickupPoint: any) => {
  if (pickupPoint) {

    applyCouriesSelectedFromBackup();

    console.log("onSelectedPacketaPickup", pickupPoint);
    selectedPoint.value = {
      address: pickupPoint.address || "",
      city: pickupPoint.city || "",
      zip: pickupPoint.zip || "",
      code: pickupPoint.zip || "",
      country: pickupPoint.country || "",
    };
    cartStore.shippingAddress = {
      ...cartStore.shippingAddress,
      address: pickupPoint?.address,
      city: pickupPoint.city,
      zip: pickupPoint.zip,
      code: pickupPoint.zip,
      country: pickupPoint.country,
      addressId: pickupPoint?.addressId,
      countryId: globals?.value?.id,
    };
    cartStore.billingAddress = {
      ...cartStore.billingAddress,
      addressId: pickupPoint?.addressId,
    };
    initialStates.value.deliveryType = "Courier";
    cartStore.deliveryType = initialStates.value.deliveryType;
    cartStore.updateCartApi();
    selectedCourier.value = "packeta";
    isCodShowing.value = false;
    cartStore.codCharge = 0;
    // cartStore.isBillingShippingSame = false;
  } else {
    console.log("No point selected");
  }
};

const glsCheckoutRef = ref(null);
// Open Modal Function
const openGlsModal = () => {
  glsCheckoutRef.value?.open(globalStore?.globals?.code);
};

// Callback function when a GLS pickup point is selected
const onSelectedGlsPickup = (pickupPointDetails: any) => {

  applyCouriesSelectedFromBackup();

  selectedPoint.value = {
    address: pickupPointDetails.address || "",
    city: pickupPointDetails.city || "",
    zip: pickupPointDetails.zip || "",
    code: pickupPointDetails.zip || "",
    country: pickupPointDetails.country || "",
  };
  // Correct field mapping for cartStore.billingAddress
  cartStore.shippingAddress = {
    ...cartStore.shippingAddress,
    address: pickupPointDetails.address,
    city: pickupPointDetails.city,
    zip: pickupPointDetails.zip,
    code: pickupPointDetails.zip,
    country: pickupPointDetails.country,
    countryId: globals?.value?.id,
  };
  initialStates.value.deliveryType = "Courier";
  cartStore.deliveryType = initialStates.value.deliveryType;
  cartStore.updateCartApi();
  selectedCourier.value = "mygls";
  isCodShowing.value = false;
  cartStore.codCharge = 0;
  // cartStore.isBillingShippingSame = false;
};

onMounted(() => {
  fetchCouriers();
  selectedPaymentMethod("");
  cartStore.codCharge = 0;
  cartStore.shippingCharge = 0;
  cartStore.courierServiceId = null;
  cartStore.courierId = 0;
});

watch(
  () => cartStore?.insideOutside,
  (newVal, oldVal) => {
    if (newVal != oldVal)
      initialHomeDeliveryCourierForBangladesh(selectedCourierObject.value);
  }
);

const initialHomeDeliveryCourierForBangladesh = (courierObj: any) => {
  selectedCourierObject.value = courierObj;
  selectedCourier.value = courierObj?.code;

  selectedPaymentMethod("");
  courierId.value = courierObj?.id;
  initialStates.value.paymentMethod = "";
  // const prevBillingAddress = JSON.parse(
  //   localStorage.getItem("billingAddress") || "{}"
  // );
  courierId.value = courierObj.id;
  shippingFee.value =
    cartStore?.insideOutside === "inside"
      ? courierObj?.priceInsideCity
      : courierObj?.priceOutsideCity;

  cartStore.courierServiceId = courierObj?.id;

  cartStore.shippingCharge =
    cartStore?.insideOutside === "inside"
      ? courierObj?.priceInsideCity
      : courierObj?.priceOutsideCity;

  cartStore.courierId = courierObj?.id;

  selectedCourierCOD.value = courierObj?.codCharge;
  isCodShowing.value = true;
  selectedPoint.value = {};
  // cartStore.codCharge = courier.codCharge;
  initialStates.value.deliveryType = "Home Delivery";
  cartStore.deliveryType = initialStates.value.deliveryType;
  cartStore.updateCartApi();
  // cartStore.billingAddress = prevBillingAddress;
  // cartStore.shippingAddress = {
  //   ...prevBillingAddress,
  // };
};

// fetch quriar
const fetchCouriers = async () => {
  try {
    const response = await fetch(
      config.public?.apiUrl + "couriers/byLanguage/" + globalStore?.globals?.id
    );

    if (!response.ok) {
      throw new Error("Network response was not ok");
    }

    const data = await response.json();
    // Sort data: items with code "wp" first, then the rest in ascending order
    getCuriers.value = data.data.sort((a, b) => {
      if (a.code === "wp") return -1; // Place "wp" at the beginning
      if (b.code === "wp") return 1;
      return a.code.localeCompare(b.code); // Sort remaining items alphabetically
    });

    // Pre selection for Bangladesh domain. 'Home Delivery' pre selected
    if (props?.countryCode === "BD") {
      if (getCuriers?.value?.length > 0) {
        let homeDeliveryCourier = getCuriers?.value?.find(
          (el) => el.code === "hd"
        );
        initialHomeDeliveryCourierForBangladesh(homeDeliveryCourier);
      }
    }
  } catch (error) {
    console.error("Failed to fetch couriers:", error);
  }
};

const selectedCourierInputHandler = (e, courier) => {
  courierSelectedBackup.value = courier;

  if (courier.code === "mygls") {
    openGlsModal();
  } else if (courier.code === "packeta") {
    openPacketaWidget(courier.apiKey);
  } else if (courier.code === "pactic") {
    openPacticWidget();
  } else {

    applyCouriesSelectedFromBackup();

    const prevBillingAddress = JSON.parse(
      localStorage.getItem("billingAddress") || "{}"
    );

    selectedCourier.value = courier.code;
    selectedCourierCOD.value = courier.codCharge;
    isCodShowing.value = true;
    selectedPoint.value = {};
    // cartStore.codCharge = courier.codCharge;
    initialStates.value.deliveryType =
      courier.code === "wp" ? "Warehouse Pickup" : "Home Delivery";
    cartStore.deliveryType = initialStates.value.deliveryType;
    cartStore.updateCartApi();
    // cartStore.billingAddress = prevBillingAddress;
    // cartStore.isBillingShippingSame = true;
    cartStore.shippingAddress = {};
    couponStore.clearCouponData();
  }
};



defineExpose({reset: resetCartQty})
</script>
<style lang="scss" >
html .label-op-1 label {
  opacity: 1 !important;
}
.custom-font {
  font-size: 14px;
  font-weight: 600;
}
.v-radio .v-label {
  font-size: 14px;
}
.price-column {
  display: flex;
  flex-direction: column;
  height: 100%;
  // justify-content: space-around;
}

.price-item {
  text-align: right;
  padding: 9px 0;
  font-size: 14px;
}
</style>
