<template>
	<v-sheet v-if="relatedProductList?.length > 0" class="px-2 pb-2 mt-3 rounded-xl">
		<h5 class="text-left text-h5 py-1">Buy more recommed products!</h5>
		<v-row>
			<v-col 
				v-for="(item, index) in relatedProductList" 
				:key="index"
				cols="6"
				sm="4"
				md="6"
				lg="4"
			>	
				<LazyEcommerceProductSingleCard :product="item" fromPage="checkout" />
			</v-col>
		</v-row>
	</v-sheet>
</template>

<script setup>
import { computed, onMounted } from 'vue';

// Props
const props = defineProps({
	fromPage: null,
})

// Store
const cartStore = useShoppingCartStore();
const productCategoryStore = useProductCategoryStore();
const { getDomaCountryId, getDomaLanguageId } = useCurrentHost();

// State
const relatedProductList = ref([]);
const countryId = getDomaCountryId();
const languageId = getDomaLanguageId();

// Computed
const cartItemLowPriced = computed(()=>{
	if(props?.fromPage === "orderConfirmation"){
		return JSON.parse(localStorage?.getItem("cartItemLowPricedProduct"));
	}
	let obj = cartStore?.cart?.reduce((acc, curr)=>{
		return acc?.unitPrice > curr?.unitPrice ? curr : acc;
	})
	localStorage.setItem("cartItemLowPricedProduct", JSON.stringify(obj));
	return obj;
})

// Methods
const handleFetchRelatedProduct = ()=>{
	try {
		if(cartItemLowPriced?.value?.productId?.id){
			productCategoryStore.getRelatedProductsById(
				countryId,
				cartItemLowPriced?.value?.productId?.id,
				1,
				3,
				null,
				languageId
			)?.then((response)=>{
				relatedProductList.value = response?.data?.filter(item=>item?.id !== cartItemLowPriced?.value?.productId?.id) || [];
			})
		}
  } catch (error) {
  }
}

watch(()=> cartItemLowPriced?.value?.productId?.id, (newVal, oldVal)=>{
	if(newVal !== oldVal){
		handleFetchRelatedProduct();
	}
})

onMounted(()=>{
	handleFetchRelatedProduct();
})

</script>