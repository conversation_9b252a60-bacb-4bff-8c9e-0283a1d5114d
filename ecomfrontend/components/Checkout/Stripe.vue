<template>
  <v-container>
    <div id="checkoutStripe"></div>
  </v-container>
</template>

<script setup lang="ts">

import {loadStripe} from "@stripe/stripe-js";

const {locale} = useI18n();
const globalStore = useGlobalStore();

const emit = defineEmits(['stripeSelected'])

const config = useRuntimeConfig();
const stripe: any = await loadStripe(config.public.stripePublicKey, {
  locale: locale.value,
});
const cartStore = useShoppingCartStore();
const shopStore = useGlobalStore();

let elements: any;
let paymentElement: any;

const {clientSecret} = await cartStore.fetchClientSecret({
  amount: parseFloat(cartStore?.total),
  currency: shopStore.globals?.currency?.currency?.toLowerCase(),
  email: cartStore.billingAddress?.email ? cartStore?.billingAddress?.email : cartStore?.shippingAddress?.email,
  uuid: cartStore.cartUUID,
})

elements = stripe.elements({clientSecret});

const paymentElementOptions = {
  layout: "tabs",
  defaultValues: {
    billingDetails: {
      address: {
            country: globalStore?.globals?.code?.toString(),
        },
    },
  }
};

paymentElement = elements.create("payment", paymentElementOptions);

emit('stripeSelected', {
  elements: elements,
  stripe: stripe
});

onMounted(() => {
  paymentElement.mount('#checkoutStripe')
})

</script>
