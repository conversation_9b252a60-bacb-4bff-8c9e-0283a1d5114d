<template>
  <section class="slide-wrapper">
    <Swiper
      ref="sliderRef"
      :grab-cursor="false"
      :modules="modules"
      :loop="true"
      :autoplay="{
        delay: 1500,
        disableOnInteraction: false,
      }"
      navigation
      lazy-preloader-class="swiper-lazy-preloader swiper-lazy-preloader-white"
      :breakpoints="{
        '1400': {
          slidesPerView: 1,
          spaceBetween: 1,
        },
      }"
      @swiper="onSwiperInit"
      @mouseenter="pauseAutoplay"
      @mouseleave="resumeAutoplay"
    >
      <SwiperSlide v-for="slide in props?.websiteStore?.blogPageHeroSlider" :key="slide?.sliderId">
        <div v-if="slide?.sliderMediaType === 'image'" class="">
          <NuxtImg
            :src="replaceMediaDomain(slide?.localeMediaUrl ?? slide?.globalMediaUrl) || slide?.localeMediaUrl || slide?.globalMediaUrl"
            :alt="slide?.sliderName"
            loading="lazy"
            sizes="xs:100vw sm:100vw md:100vw"
            format="webp"
  					quality="85"
            class="h-100 w-100"
            width="1800"
            height="817"
          />
        </div>
        <div v-if="slide?.sliderMediaType === 'video'" class="">
          <video class="w-100 h-100" autoplay loop muted playsinline preload="auto">
            <source 
              :src="replaceMediaDomain(slide?.localeMediaUrl ?? slide?.globalMediaUrl) || slide?.localeMediaUrl || slide?.globalMediaUrl" 
              :type="slide?.localeMimeType ?? slide?.globalMimeType"
            />
          </video>
        </div>
      </SwiperSlide>
    </Swiper>
  </section>
</template>

<script setup lang="ts">
import type { Swiper as SwiperClass } from 'swiper';
import { Navigation, Autoplay } from "swiper/modules";
import { ref } from "vue";
import { useMediaType } from '../../composables/useMediaType';
const props = defineProps({
  websiteStore: null,
})

const { replaceMediaDomain } = useMediaType();
const modules = [Navigation, Autoplay];
const sliderRef = ref();

const onSwiperInit = (swiper: SwiperClass) => {
  sliderRef.value = swiper;
};

const pauseAutoplay = () => {
  // sliderRef.value?.autoplay?.stop();
};

const resumeAutoplay = () => {
  // sliderRef.value?.autoplay?.start();
};

</script>

<style scoped>
.slide-wrapper {
  overflow: hidden;
}
</style>
