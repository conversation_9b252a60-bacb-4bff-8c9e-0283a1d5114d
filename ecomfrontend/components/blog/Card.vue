<template>
	<v-card class="elevation-0 border rounded-xl pa-2 pa-md-5 d-flex flex-column h-100">
		<v-card-item>
			<nuxt-link :to="{ name: 'blog-slug', params: { slug: content?.slug } }" class="d-block h-100 text-decoration-none">
				<!-- Blog Image -->
				<NuxtImg
					:src="content?.featuredImageInfo?.imageUrl || 'https://via.placeholder.com/600x300?text=No+Image'"
					height="300"
					class="w-100 rounded-xl"
					:alt="`${content?.title || 'Blog'} - image`"
					format="webp"
					quality="85"
					fit="cover"
					style="object-fit: cover"
				/>
				<!-- Author & Title -->
				<v-card-title class="px-0 mt-3 d-block">
					<div v-if="content?.primaryAuthor?.name" class="text-subtitle-2 text-secondary text-truncate">
						{{ content.primaryAuthor.name }}
					</div>
					<div class="text-h5 text-primary font-weight-bold mt-1 text-truncate" :title="content?.title">
						{{ content?.title }}
					</div>
				</v-card-title>

				<!-- Excerpt -->
				<v-card-text class="px-0 py-2 flex-grow-1">
					<p class="text-body-2 text-grey line-clamp" :title="content?.excerpt">
						{{ content?.excerpt }}
					</p>
				</v-card-text>

				<!-- Read More -->
				<!-- <v-card-actions class="px-0 pt-0">
					<NuxtLink 
						class="text-primary font-weight-medium text-decoration-none" :title="`Read: ${content?.title}`">
						Read More →
					</NuxtLink>
				</v-card-actions> -->
			</nuxt-link>
		</v-card-item>
	</v-card>
</template>

<script setup>
const props = defineProps({
	content: {
		type: Object,
		required: true
	}
});
</script>

<style scoped>
.line-clamp {
	display: -webkit-box;
	-webkit-line-clamp: 3;
	-webkit-box-orient: vertical;
	overflow: hidden;
}
</style>
