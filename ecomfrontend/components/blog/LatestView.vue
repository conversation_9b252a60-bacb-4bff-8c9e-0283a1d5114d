<template>
	<v-sheet class="border rounded-xl elevation-3" style="position: sticky; top: 60px;">
		<div class="pa-2 pa-md-4 border-b-md border-primary">
			<h3 class="text-h3 font-weight-bold text-center">Reach Us</h3>
			<p class="mt-3 description text-center">In our social links you can find us</p>

			<!-- Social Media Icons -->
			<div class="mt-3 text-center d-flex d-md-block align-center justify-center">
				<template v-for="platform in socialPlatforms" :key="platform.name">
					<a v-if="socialMediaStore.socialLinks?.[platform.name]" :href="platform.shareLink + socialMediaStore.socialLinks[platform.name]"
						target="_blank" class="d-inline-flex">
						<NuxtImg :src="replaceMediaDomain(platform.icon) || platform.icon" :alt="'Share on ' + platform.name"
							loading="lazy" width="28" height="28" class="me-2" fit="cover" format="webp" quality="85" />
					</a>
				</template>
			</div>
		</div>

		<div v-if="itemList?.length > 0" class="pa-2 pa-md-4">
			<h3 class="text-h3 font-weight-bold">Latest Blog post</h3>

			<nuxt-link v-for="(item, index) in itemList" :key="index" class="mt-3 d-block pa-1 border" :to="`/blog/${item?.slug}`">
				<v-row>
					<v-col cols="3">
						<NuxtImg v-if="item?.featuredImageInfo" :src="item?.featuredImageInfo?.imageUrl"
							:alt="'latest-'+ blog?.name || blog?.title" loading="lazy" sizes="xs:100vw sm:100vw md:100vw"
							format="webp" quality="85" height="80" class="w-100" style="object-fit: cover; aspect-ratio: 16 / 9 ;" />
					</v-col>
					<v-col cols="9">
						<h4 class="text-h6">{{ item?.title }}</h4>
						<p>{{ item?.description?.slice(0, 100) }}</p>
					</v-col>
				</v-row>
			</nuxt-link>
		</div>
	</v-sheet>
</template>

<script setup lang="ts">
import { useRuntimeConfig } from 'nuxt/app';
import { onMounted } from 'vue';
import { useSocialMediaStore } from '../../stores/socialMedia';
import { useMediaType } from '../../composables/useMediaType';

const props = defineProps({
	blogStore: null,
	blog: null,
	languageId: null,
	countryId: null
});

const itemList = ref([]);

const config = useRuntimeConfig();
const staticImageUrl = config.public.pantonecloStaticImageUrl;
const getMediaImage = (title: string) => `${staticImageUrl}${title}.png`;
const socialMediaStore = useSocialMediaStore();
const { replaceMediaDomain } = useMediaType();

const socialPlatforms = [
  {
    name: 'facebook',
    icon: getMediaImage('fb'),
    shareLink: 'https://www.facebook.com/sharer/sharer.php?u=',
  },
  {
    name: 'linkedin',
    icon: getMediaImage('in'),
    shareLink: 'https://www.linkedin.com/sharing/share-offsite/?url=',
  },
  {
    name: 'instagram',
    icon: getMediaImage('insta'),
    shareLink: null, // Instagram does not support web sharing via link
  },
  {
    name: 'youtube',
    icon: getMediaImage('youtube'),
    shareLink: null, // YouTube is not a share-to platform
  },
  {
    name: 'tiktok',
    icon: getMediaImage('tiktok'),
    shareLink: null, // No public web-share URL format
  },
  {
    name: 'x', // formerly Twitter
    icon: getMediaImage('x'),
    shareLink: 'https://twitter.com/intent/tweet?url=', // X = Twitter
  },
  {
    name: 'whatsapp',
    icon: getMediaImage('whatsapp'),
    shareLink: 'https://wa.me/?text=',
  },
  {
    name: 'telegram',
    icon: getMediaImage('telegram'),
    shareLink: 'https://t.me/share/url?url=',
  },
];

const handleFetchLatestBlogs = async () => {
	const response = await props?.blogStore.getAllPosts({
		order: 'ASC',
		page: 1,
		take: 4,
		sort: '',
		search: '',
		languageId: props.languageId,
		countryId: props.countryId
	});
	itemList.value = response?.data || [];
}

onMounted(() => {
	handleFetchLatestBlogs();
});

</script>