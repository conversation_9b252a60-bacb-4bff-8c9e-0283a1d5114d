<template>
  <v-container class="py-3 py-md-8">
    <h3 class="text-grey300 text-h3 font-weight-medium text-center">{{ $t("Follow Us") }}</h3>
    <div class="mt-3 mt-md-5 d-flex ga-6 align-center justify-center">

      <a v-if="socialMediaStore.socialLinks?.facebook" :href="socialMediaStore.socialLinks.facebook" target="_blank"
        class="d-inline-flex">
        <NuxtImg :src="getMediaImage('facebook')" alt="Facebook" loading="lazy" sizes="xs:100vw sm:100vw md:100vw"
          format="webp" width="28" height="28" class="me-2" fit="cover" target="_blank" rel="noopener noreferrer" quality="85"/>
      </a>

      <a v-if="socialMediaStore.socialLinks?.linkedin" :href="socialMediaStore.socialLinks.linkedin" target="_blank"
        class="d-inline-flex">
        <NuxtImg :src="getMediaImage('linkedin')" alt="LinkedIn" loading="lazy" sizes="xs:100vw sm:100vw md:100vw"
          format="webp" width="28" height="28" class="me-2" fit="cover" target="_blank" rel="noopener noreferrer" quality="85"/>
      </a>

      <a v-if="socialMediaStore.socialLinks?.instagram" :href="socialMediaStore.socialLinks.instagram" target="_blank"
        class="d-inline-flex">
        <NuxtImg :src="getMediaImage('instagram')" alt="Instagram" loading="lazy" sizes="xs:100vw sm:100vw md:100vw"
          format="webp" width="28" height="28" class="me-2" fit="cover" target="_blank" rel="noopener noreferrer" quality="85"/>
      </a>

      <a v-if="socialMediaStore.socialLinks?.youtube" :href="socialMediaStore.socialLinks.youtube" target="_blank"
        class="d-inline-flex">
        <NuxtImg :src="getMediaImage('youtube')" alt="YouTube" loading="lazy" sizes="xs:100vw sm:100vw md:100vw"
          format="webp" width="28" height="28" class="me-2" fit="cover" target="_blank" rel="noopener noreferrer" quality="85"/>
      </a>

      <a v-if="socialMediaStore.socialLinks?.tiktok" :href="socialMediaStore.socialLinks.tiktok" target="_blank"
        class="d-inline-flex">
        <NuxtImg :src="getMediaImage('tiktok')" alt="TikTok" loading="lazy" sizes="xs:100vw sm:100vw md:100vw"
          format="webp" width="28" height="28" class="me-2" fit="cover" target="_blank" rel="noopener noreferrer" quality="85"/>
      </a>

      <a v-if="socialMediaStore.socialLinks?.x" :href="socialMediaStore.socialLinks.x" target="_blank"
        class="d-inline-flex">
        <NuxtImg :src="getMediaImage('twitter')" alt="X" loading="lazy" sizes="xs:100vw sm:100vw md:100vw"
          format="webp" width="28" height="28" class="me-2" fit="cover" target="_blank" rel="noopener noreferrer" quality="85"/>
      </a>
    </div>
  </v-container>
</template>

<script setup lang="ts">
import { useSocialMediaStore } from "../../stores/socialMedia";

const socialMediaStore = useSocialMediaStore();
const config = useRuntimeConfig();
const staticImageUrl = config.public.pantonecloStaticImageUrl;

const getMediaImage = (title: string) => {
  switch (title) {
    case "facebook":
      return `${staticImageUrl}fb.png`;
    case "twitter":
      return `${staticImageUrl}x.png`;
    case "instagram":
      return `${staticImageUrl}insta.png`;
    case "tiktok":
      return `${staticImageUrl}tiktok.png`;
    case "linkedin":
      return `${staticImageUrl}in.png`;
    case "youtube":
      return `${staticImageUrl}youtube.png`;
  }
};

</script>
