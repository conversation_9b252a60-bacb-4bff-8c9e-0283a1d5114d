<template>
  <v-container>
    <v-row>
      <v-col cols="12">
        <div class="d-flex align-center">
          <h3>{{$t('Attachment')}}</h3>
          <v-btn @click="openFileDialog" flat variant="text" class="pa-0 ms-2"
                 style="max-height: 50px; max-width: 50px; min-width: auto">
            <nuxt-picture src="https://pantoneclos3bucket.s3.ap-south-1.amazonaws.com/pantoneclo-img/img-upload.png" width="20"/>
          </v-btn>
        </div>
        <input
            type="file"
            multiple
            ref="fileInput"
            style="display: none"
            @change="onFileChange"
        />
      </v-col>
    </v-row>
    <v-row>
      <v-col v-for="(image, index) in images" :key="index" cols="12" md="4">
        <div class="position-relative">
          <NuxtImg :src="image" alt="Pantoneclo partner"
                   loading="lazy" sizes="xs:100vw sm:100vw md:100vw"
                   format="webp" width="357" height="20" class="h-100 w-100" fit="cover" quality="85" />

          <v-btn @click="deleteImage(index)" icon class="position-absolute" style="right: 0; top: 0" size="md" variant="tonal" color="error">
            <v-icon class="i-mdi:close" color="error"></v-icon>
          </v-btn>
        </div>
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup>
import {ref} from 'vue'
const emit = defineEmits(['imageUploaded'])

const fileInput = ref(null)
const images = ref([])

const openFileDialog = () => {
  fileInput.value.click()
}

const onFileChange = async (event) => {
  const files = event.target.files
  for (let i = 0; i < files.length; i++) {
    const reader = new FileReader()
    reader.onload = (e) => {
      images.value.push(e.target.result)
    }
    reader.readAsDataURL(files[i])
  }

  if (files) {
    emit('imageUploaded', files);
  }
}
const deleteImage = (index) => {
  images.value.splice(index, 1);
};

</script>

<style scoped>
.mb-4 {
  margin-bottom: 16px;
}
</style>
