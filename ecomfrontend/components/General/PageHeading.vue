<template>
  <div :class="{ 'page-heading-wrapper': props?.title }">
    <NuxtImg
      :src="backgroundImg"
      sizes="xs:100vw sm:100vw md:100vw"
      width="1800"
      height="450"
      class="h-100 w-100 d-block"
      fit="cover"
      quality="85"
      :alt="props?.title + '-cover'"
      preload
      fetchpriority="high"
      format="webp"
    />
    <div class="heading">
      <h1 class="text-uppercase">{{ title }}</h1>
      <div class="text-center">
        <slot name="subtitle" class="text-center"></slot>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  title: String,
  backgroundImg: {
    type: String,
    required: false,
    default() {
      return "https://pantoneclos3bucket.s3.ap-south-1.amazonaws.com/pantoneclo-img/final-bluer-ed-1.webp";
    },
  },
  minHeight: {
    type: String || Number,
    required: false,
  },
});
</script>
<style lang="scss" scoped>
.page-heading-wrapper {
  //min-height: 250px;
  position: relative;
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;

  &::after {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: #04070750;
  }

  .heading {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    z-index: 1;
    color: #fff;
  }
}
@media (max-width: 959px) {
  .page-heading-wrapper {
    .heading {
      width: 80%;
    }
  }
}
@media (max-width: 575px) {
  .page-heading-wrapper {
    .heading {
      width: 95%;
      h1 {
        font-size: 18px;
      }
    }
  }
}
</style>
