<template>
  <v-divider color="#000" :thickness="1" />
  <v-virtual-scroll
    :height="`${suggestions?.length > 0 ? 'calc(60vh - 125px)' : '60vh'}`"
    class="pa-3 mini-cart-items-wrapper px-3 px-md-8"
    :items="getCart"
  >
    <template v-slot:default="{ item, index }">
      <div class="d-flex gap-2 minicart-item">
        <NuxtLink :to="localePath(`/product/${item.slug}`)" class="d-block">
          <NuxtImg
            :src="item.featuredImage?.imageGalleryUrls?.thump"
            :alt="item?.name"
            loading="lazy"
            format="webp"
            width="60"
            height="80"
            class="me-2 rounded-xl"
            fit="cover"
            quality="85"
          />
        </NuxtLink>
        <div class="w-100">
          <div class="d-flex flex-column ga-1 ga-sm-0 flex-sm-row  justify-space-between">
            <h3
              class="text-subtitle-1"
            >
              {{
                item.name?.length > 24
                  ? item.name?.slice(0, 24) + "..."
                  : item.name
              }}
            </h3>
            <div
              v-html="
                getPriceWithCurrency(
                  item.discountPrice,
                  item.unitPrice,
                  item.quantity,
                  item
                )
              "
            ></div>
          </div>
          <template v-if="item.isMultiVariant">
            <div class="d-flex mb-0">
              <p
                class="me-1 mb-1 text-body-2 font-weight-light"
              >
                {{ $t("Size") }}: <span class="font-weight-bold">{{ item.size?.name }}</span>
              </p>
              <p
                class="me-1 mb-1 text-body-2 font-weight-light"
                v-if="item?.color?.name"
              >
                {{ $t("Color") }}: <span class="font-weight-bold">{{ item.color?.name }}</span>
              </p>
            </div>
          </template>
          <div class="d-flex align-center justify-space-between">
            <v-btn-toggle divided color="success" style="height: 25px">
              <v-btn
                size="x-small"
                color="primary"
                @click="shoppingCart.cartItemDecrement(item)"
                variant="tonal"
                class="py-1"
                :disabled="item?.quantity < 2"
              >
                <v-icon class="i-mdi:minus" size="18" />
              </v-btn>

              <v-btn size="x-small">
                {{ item?.quantity }}
              </v-btn>

              <v-btn
                size="x-small"
                color="primary"
                @click="handleCartItemIncrement(item)"
                variant="tonal"
                class="py-1"
              >
                <v-icon class="i-mdi:plus" size="18" />
              </v-btn>
            </v-btn-toggle>
            <div class="d-flex align-center justify-end mt-1">
              <v-btn
                icon
                flat
                elevation="0"
                size="x-small"
                @click="handleRemoveCartItem(item)"
              >
                <v-icon class="i-mdi:trash-can-outline text-error" size="18" />
              </v-btn>
            </div>
          </div>
        </div>
      </div>
    </template>
  </v-virtual-scroll>

  <!-- Available Discounts -->

  <v-sheet v-if="shoppingCart?.availableFreeProducts?.length > 0" class="px-3 px-md-12">
    <v-btn
      color="error"
      variant="flat"
      size="small"
      class="mb-2 text-h6 rounded-pill py-5 font-weight-bold"
      block
      @click="openFreeProductsModal"
    >
      <v-icon start class="i-mdi:gift-outline"></v-icon>
      View {{ shoppingCart?.availableFreeProducts?.length }} Achieved Discount{{ shoppingCart?.availableFreeProducts?.length > 1 ? 's' : '' }}!
    </v-btn>
  </v-sheet>

  <!-- Discount Goal Banner in MiniCart -->
  <div v-if="shoppingCart.showDiscountGoal && shoppingCart.currentDiscountGoal" class="px-3 px-md-6 py-3">
    <v-sheet class="border border-grey rounded-xl d-flex flex-column discount-goal-minicart mx-3 mb-3 pa-4">
      <div class="d-flex align-center justify-space-between mb-2">
        <div class="d-flex align-center gap-2">
          <v-icon size="18" color="primary" class="i-mdi:gift-outline"></v-icon>&nbsp;
          <span class="text-body-1 font-weight-medium text-primary">More discounts there!</span>
        </div>
        <v-btn
          icon
          color="error"
          size="20"
          variant="text"
          @click="shoppingCart.hideDiscountGoal()"
        >
         <v-icon size="18" color="error" class="i-mdi:close"></v-icon>
      </v-btn>

      </div>
      
      <p class="text-body-1 text-grey-darken-2 mb-2" v-html="getGoalText()">
      </p>
      
      <!-- Compact Progress Bar -->
      <div class="mb-2">
        <div class="d-flex justify-space-between align-center mb-1">
          <span class="text-body-1">{{ Math.round(shoppingCart.currentDiscountGoal.progress) }}% complete</span>
        </div>
        <v-progress-linear
          :model-value="shoppingCart.currentDiscountGoal.progress"
          color="error"
          bg-color="grey-lighten-3"
          height="6"
          rounded
        />
      </div>
      
      <!-- CTA -->
      <div class="d-flex justify-center">
        <v-btn
          size="small"
          color="error"
          variant="flat"
          class="text-none rounded-pill text-h6 font-weight-bold w-50"
          @click="openDiscountModal"
        >
          <v-icon start size="16" class="i-mdi:eye"></v-icon>
          {{ $t('View Details')}}
        </v-btn>
      </div>
    </v-sheet>
  </div>
  <template v-else>
    <v-btn
      v-if="shoppingCart.discountGoals.length > 0"
      color="secondary"
      variant="outlined"
      size="small"
      class="mb-2"
      block
      @click="openDiscountModal"
    >
      <v-icon start class="i-mdi:gift-outline"></v-icon>
      View {{ shoppingCart.discountGoals.length }} Available Discount{{ shoppingCart.discountGoals.length > 1 ? 's' : '' }}
    </v-btn>
    
    <!-- Free Products Button -->
    <v-btn
      v-if="shoppingCart.availableFreeProducts.length > 0"
      color="primary"
      variant="flat"
      size="small"
      class="mb-2"
      block
      @click="openFreeProductsModal"
    >
      <v-icon start class="i-mdi:gift"></v-icon>
      {{ getTotalFreeProducts() }} Discounted Product{{ getTotalFreeProducts() > 1 ? 's' : '' }} Available
    </v-btn>
  </template>



  <div class="mini-cart-footer-content px-3 px-md-8">
    <div
      class="d-flex justify-space-between subtotal-cart mb-4 bg-grey-lighten-4 px-4 py-3 rounded-xl"
    >
      <div class="text-uppercase font-weight-bold text-grey-darken-1">
        {{ $t("Subtotal") }}:
      </div>
      <div>{{ formatLocalePrice(shoppingCart.total) }}</div>
    </div>
    <div class="text-center">
      <v-btn
        color="primary"
        class="text-uppercase mb-3 rounded-xl"
        :to="localePath('/checkout')"
        size="large"
        block
        >{{ $t("Proceed to checkout") }}</v-btn
      >
      
      <p
        class="text-blue text-decoration-none text-sm-body-2"
        v-if="shippingChargeFree"
      >
        {{ $t("Free delivery on purchase over") }}
        {{ shippingChargeFree + " " + currencyName }}
      </p>
    </div>
  </div>

  <LazyEcommerceCartSuggestion
    v-if="suggestions?.length > 0"
    :suggestions="suggestions"
  />
</template>
<script setup lang="ts">
import type { LineItem, CartSuggestion } from "~/types/stores/Cart";
import { storeToRefs } from "pinia";
import { nextTick, onMounted } from "vue";
import { useTagManager } from "~/composables/useTagManager";
import { formatLocalePrice } from "~/utils/money";
import { showSnackbarResponse } from "../../utils/functions";

const props = defineProps<{
  open: boolean;
}>();

const { gtmCartViewEvent } = useTagManager();

const localePath = useLocalePath();
const shoppingCart = useShoppingCartStore();
const { globals } = storeToRefs(useGlobalStore());

// Inject the openDiscountModal function from the layout
const openDiscountModal = inject('openDiscountModal') as () => void;

// Open free products modal
const openFreeProductsModal = () => {
  shoppingCart.showFreeProductsModal = true;
};

// Calculate total free products available
const getTotalFreeProducts = () => {
  return shoppingCart.availableFreeProducts.reduce((total, offer) => {
    return total + offer.freeQuantity;
  }, 0);
};
const getCart = computed<LineItem[]>(() => shoppingCart.cart);
const suggestions = computed<CartSuggestion[]>(
  () => shoppingCart.suggestions || []
);
const currencyName = computed(() => globals.value?.currency?.currency);
const shippingChargeFree = computed(() => globals.value?.shippingChargeFree);
const subtotal = computed(() => shoppingCart.getCartItemSubTotal());

// Get goal text for display
const getGoalText = () => {
  const goal = shoppingCart.currentDiscountGoal;
  if (!goal) return '';
  
  if (goal.type === 'order_amount' && goal.amountNeeded) {
    return `Add ${formatLocalePrice(goal.amountNeeded)} more to get ${goal.title}`;
  } else if (goal.type === 'quantity' && goal.quantityNeeded) {
    return `Add ${goal.quantityNeeded} more item${goal.quantityNeeded > 1 ? 's' : ''} to get <span class='font-weight-bold'>${goal.title}</span>`;
  } else if (goal.type === 'product_specific') {
    return `Add required products to get ${goal.title}`;
  }
  
  return goal.title;
};

const getPriceWithCurrency = (
  discountPrice: any,
  unitPrice: any,
  quantity: number,
  item: any
) => {
  const bestDiscount = shoppingCart?.findBestDiscountForProduct(item?.productId?.id);
  if(bestDiscount){
    let miniProduct = {
      cartItemId: item?.id,
      id: item?.productId?.id,
      discountPrice: discountPrice,
      unitPrice: unitPrice,
    }
    let bestDiscountPrice = Number(discountPrice);
    if(item?.discount){
      // bestDiscountPrice = shoppingCart?.getDiscountPriceValueForCartItem(miniProduct, bestDiscount);
      // const freeQuantity = item?.discount?.buy_y_min_qty;
      // const itemsTotalQuantityWithDiscount = shoppingCart?.getItemsWithSpecificDiscountTotalQuantity(item?.discount?.id);
      // if(itemsTotalQuantityWithDiscount > freeQuantity){
      //   const extraQuanlity = Math.abs(quantity - freeQuantity);
      //   bestDiscountPrice = Number(bestDiscountPrice) + Number(discountPrice * extraQuanlity);
      // }
      let finalPrice = Number(item?.discountPrice);
      if(finalPrice === 0){
        finalPrice = Number(item?.unitPrice);
      }
      bestDiscountPrice = (Math.abs((finalPrice * quantity) - Number(item?.discountAmount))).toFixed(2);
    }else{
      bestDiscountPrice *= quantity;
    }

    const unitPriceWithQuantity = unitPrice * quantity;
    return `
      <span class="text-h6 font-weight-bold text-green">${ Number(bestDiscountPrice) === 0 ? 'Free' : formatLocalePrice(bestDiscountPrice)}</span> 
      <span class="text-body-1 text-decoration-line-through text-red">${formatLocalePrice(unitPriceWithQuantity)}</span>
      `;
  }
  else{
    if (parseInt(discountPrice) || parseInt(discountPrice) > 0) {
      const discountPriceWithQuantity = discountPrice * quantity;
      const unitPriceWithQuantity = unitPrice * quantity;

      return `<span class="h6 font-weight-bold">${formatLocalePrice(
        discountPriceWithQuantity
      )}</span>
              <span class="h6 text-decoration-line-through text-red">${formatLocalePrice(
                unitPriceWithQuantity
              )}</span>`;
    } else {
      const priceWithQuantity = unitPrice * quantity;
      return `<span class="h6 font-weight-bold">${formatLocalePrice(
        priceWithQuantity
      )}</span>`;
    }
  }
};

const sendCartViewEvent = () => {
  const payload = getCart.value?.map((_product: LineItem) => ({
    id: _product.productId,
    name: _product.name,
    unitPrice: _product.unitPrice,
    quantity: _product.quantity || 1,
    color: _product.color?.name,
    size: _product.size?.name,
    currency: _product.currency,
    brand: _product.brand,
    discountPrice: _product.discountPrice,
    sku: _product.sku,
    categoriesArr: _product?.categoriesArr,
  }));

  if (payload?.length) {
    gtmCartViewEvent(payload);
  }
};

const loadCart = () => {
  shoppingCart.getSuggestedProducts();
};

const handleCartItemIncrement = (cartItem) => {
  shoppingCart.cartItemIncrement(cartItem).then((response) => {
    showSnackbarResponse(response);
    // Discount goals are automatically fetched in the store method
  });
};

const handleRemoveCartItem = (cartItem) => {
  shoppingCart
    .removeCartItemApi(cartItem)
    .then((response) => {
      showSnackbarResponse(response);
    })
    .catch((e) => {
      showSnackbarResponse("Failed to remove cart item!");
    });
};

watch(
  () => props.open,
  async (newVal) => {
    if (newVal) {
      await nextTick();
      sendCartViewEvent();
    }
  }
);

onMounted(() => {
  console.log("slide, on before mount");
});
</script>
<style lang="scss" scoped>
.mini-cart-items-wrapper {
  .minicart-item {
    border-bottom: 1px solid #c2c2c2;
    margin-bottom: 10px;
  }

  .v-virtual-scroll__item {
    &:last-child {
      .minicart-item {
        border-bottom: 0;
      }
    }
  }
}

// Discount Goal Styles
.discount-goal-minicart {
  border-radius: 12px;
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
  border: 1px solid #e3e8f7;
}

.discount-goal-card {
  padding: 12px 16px;
  background: white;
  border-radius: 10px;
  border: 1px solid #f0f0f0;
  position: relative;
  overflow: hidden;
}

.discount-goal-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #1976d2 0%, #42a5f5 100%);
}
</style>
