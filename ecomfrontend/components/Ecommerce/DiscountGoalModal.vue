<template>
  <v-dialog
    v-model="dialog"
    max-width="800"
    scrollable
  >
    <v-card rounded="xl">
      <v-card-title class="d-flex align-center justify-space-between">
        <div class="d-flex align-center gap-2">
          <v-icon color="primary" class="i-mdi:target"></v-icon>
          <span class="text-capitalize">{{ $t('unlock discounts') }}</span>
        </div>
        <v-btn
        icon
          size="small"
          variant="text"
          @click="dialog = false"
        >
        <v-icon variant="text" color="grey" class="i-mdi:close"></v-icon>
      </v-btn>
      </v-card-title>

      <v-card-text class="py-0">
        <div v-if="!goals.length" class="text-center py-8">
          <v-icon size="64" color="grey-lighten-1" class="i-mdi:gift-outline"></v-icon>
          <h3 class="text-h6 mt-4 text-grey-darken-1">{{ $t('No discount goals available') }}</h3>
          <p class="text-body-2 text-grey-darken-1">{{ $t('All available discounts are already applied or no goals match your current cart')}}</p>
        </div>

        <div v-else>
          <p class="text-body-1 text-grey-darken-1 mb-4">
           {{ $t("You're close to unlocking these amazing discounts! Add the recommended items to your cart") }}.
          </p>

          <div class="discount-goals-list">
            <v-card
              v-for="goal in goals"
              :key="goal.id"
              class="mb-4"
              variant="outlined"
              :color="goal.progress > 75 ? 'error' : 'primary'"
            >
              <v-card-text>
                <div class="d-flex align-center justify-space-between mb-3">
                  <h4 class="text-h6">{{ goal.title }}</h4>
                  <v-chip
                    :color="goal.progress > 75 ? 'error' : 'primary'"
                    size="small"
                    variant="flat"
                  >
                    {{ goal.progress }}% {{ $t('complete') }}
                  </v-chip>
                </div>

                <p class="text-body-2 mb-3">{{ goal.benefit }}</p>

                <!-- Progress Bar -->
                <div class="mb-3">
                  <div class="d-flex justify-space-between align-center mb-1">
                    <span class="text-caption">{{ getProgressText(goal) }}</span>
                  </div>
                  <v-progress-linear
                    :model-value="goal.progress"
                    :color="goal.progress > 75 ? 'error' : 'primary'"
                    height="8"
                    rounded
                  />
                </div>

                <!-- Recommended Products -->
                <div v-if="goal.recommendedProducts?.length">
                  <h5 class="text-subtitle-2 mb-2 text-capitalize">{{ $t('recommended products') }}:</h5>
                  <v-row>
                    <v-col
                      v-for="product in goal.recommendedProducts"
                      :key="product.id"
                      cols="12"
                      sm="6"
                      md="4"
                    >
                      <v-card
                        class="product-recommendation rounded-xl"
                        variant="outlined"
                        elevation="0"
                        hover
                        @click="goToProduct(product.slug)"
                      >
                        <div class="d-flex align-center pa-2">
                          <NuxtImg
                            :src="product.image"
                            :alt="product.name"
                            width="50"
                            height="60"
                            class="rounded me-3"
                            fit="cover"
                            format="webp"
                            quality="85"
                          />
                          <div class="flex-grow-1">
                            <h6 class="text-subtitle-2 mb-1">
                              {{ product.name.slice(0, 30) }}{{ product.name.length > 30 ? '...' : '' }}
                            </h6>
                            <div class="d-flex align-center text-error gap-2">
                              <span class="text-h6 font-weight-bold">
                                {{ formatLocalePrice(product.discountPrice || product.unitPrice)}}
                              </span>
                              <span v-if="product.discountPrice && product.discountPrice < product.unitPrice" 
                                    class="text-caption text-decoration-line-through text-grey">
                                {{ formatLocalePrice(product.unitPrice) }}
                              </span>
                            </div>
                            <div v-if="product.needsQuantity > 1" class="text-caption text-primary">
                              {{ $t('need')}} {{ product.needsQuantity }} {{ $t('items')}}
                            </div>
                          </div>
                          <v-icon size="20" color="grey-lighten-1" class="i-mdi:chevron-right"></v-icon>
                        </div>
                      </v-card>
                    </v-col>
                  </v-row>
                </div>
              </v-card-text>
            </v-card>
          </div>
        </div>
      </v-card-text>

      <v-card-actions class="px-6 pb-4 py-6">
        <v-spacer />
        <v-btn
          color="error"
          variant="flat"
          rounded="xl"
          class="font-weight-bold text-capitalize text-h6"
          @click="dialog = false"
        >
          {{ $t('continue shopping') }}
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup lang="ts">
import { formatLocalePrice } from "~/utils/money";
interface DiscountGoal {
  id: number;
  discountId: number;
  title: string;
  type: string;
  goalAmount?: number;
  currentAmount?: number;
  amountNeeded?: number;
  goalQuantity?: number;
  currentQuantity?: number;
  quantityNeeded?: number;
  progress: number;
  benefit: string;
  recommendedProducts?: Array<{
    id: number;
    name: string;
    slug: string;
    image: string;
    unitPrice: number;
    discountPrice: number;
    needsQuantity: number;
  }>;
}

interface Props {
  modelValue: boolean;
  goals: DiscountGoal[];
}

const props = defineProps<Props>();
const emit = defineEmits<{
  'update:modelValue': [value: boolean];
}>();

const localePath = useLocalePath();
const dialog = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
});

const getProgressText = (goal: DiscountGoal): string => {
  if (goal.type === 'order_amount' && goal.amountNeeded) {
    return `Add $${goal.amountNeeded.toFixed(2)} more to your cart`;
  } else if (goal.type === 'quantity' && goal.quantityNeeded) {
    return `Add ${goal.quantityNeeded} more item${goal.quantityNeeded > 1 ? 's' : ''} to your cart`;
  } else if (goal.type === 'product_specific') {
    return 'Add the required products to your cart';
  }
  
  return 'You\'re almost there!';
};

const goToProduct = (slug: string) => {
  dialog.value = false;
  navigateTo(localePath(`/product/${slug}`));
};
</script>

<style scoped>
.discount-goals-list {
  max-height: 600px;
  overflow-y: auto;
}

.product-recommendation {
  cursor: pointer;
  transition: all 0.2s ease;
}

.product-recommendation:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}
</style>
