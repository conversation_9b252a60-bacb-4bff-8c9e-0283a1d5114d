<template>
  <v-dialog
    v-model="dialog"
    max-width="700"
    scrollable
    persistent
  >
    <v-card rounded="xl">
      <v-card-title class="d-flex align-center justify-space-between">
        <div class="d-flex align-center gap-2">
          <v-icon color="primary" class="i-mdi:gift"></v-icon>&nbsp;
          <span>{{ $t('discounted products available') }}</span>
        </div>
        <v-btn
          icon
          size="25"
          variant="flat"
          density="compact"
          color="error"
          @click="skipFreeItems"
        >
          <v-icon color="black" size="16" class="i-mdi:close"></v-icon>
        </v-btn>
      </v-card-title>

      <v-card-text class="pb-0">
        <div class="text-center mb-4">
          <v-icon size="48" color="primary" class="i-mdi:gift"></v-icon>
          <h3 class="text-h6 mt-2">🎉 {{ $t('Congratulations') }}!</h3>
          <p class="text-body-2 text-grey-darken-1">{{ $t("You've unlocked discounted products. Choose your items below") }}:</p>
        </div>

        <div v-for="freeOffer in availableFreeProducts" :key="freeOffer.discountId" class="mb-6">
          <v-card variant="outlined" class="mb-4">
            <v-card-text>
              <div class="d-flex align-center gap-2 mb-3">
                <v-icon color="primary" class="i-mdi:tag"></v-icon>&nbsp;
                <h4 class="text-h6">{{ freeOffer.discountTitle }}</h4>
              </div>
              
              <p class="text-body-2 text-grey-darken-2 mb-4">
                Choose {{ freeOffer.freeQuantity }} discounted item{{ freeOffer.freeQuantity > 1 ? 's' : '' }}:
              </p>

          <v-row>
            <v-col
              v-for="product in freeOffer.availableProducts"
              :key="product.id"
              cols="6"
              sm="6"
              md="4"
            >
              <v-card
                class="free-product-card"
                :class="{ 'selected': isProductSelected(product.id) }"
                variant="outlined"
                hover
              >
              <NuxtLink :to="localePath(`/product/${product.slug}`)" class="text-decoration-none">
                <div class="position-relative">
                  <NuxtImg
                    :src="product.image"
                    :alt="product.name"
                    width="200"
                    class="w-100"
                    fit="cover"
                    style="object-fit: cover;"
                    quality="85"
                    format="webp"
                  />
                  
                  <!-- Discount Badge -->
                  <v-chip
                    color="primary"
                    size="small"
                    class="free-badge"
                    variant="flat"
                  >
                    {{ getDiscountBadge(product) }}
                  </v-chip>
                </div>

                <v-card-text class="pa-3">
                  <h4 class="text-subtitle-2 mb-2">
                    {{ product.name.slice(0, 40) }}{{ product.name.length > 40 ? '...' : '' }}
                  </h4>
                  <!-- add to cart button -->
                  <v-btn
                    color="primary"
                    variant="flat"
                    size="30"
                    class="text-capitalize"
                    icon="i-mdi:cart-outline"
                    @click.stop.prevent="handleAddToCartView(product)"
                  >
                  </v-btn>
                  <div class="d-flex align-center justify-space-between">
                    <div>
                      <!-- <span class="text-caption text-decoration-line-through text-grey">
                        {{ formatLocalePrice(product.unitPrice) }}
                      </span> -->
                      <!-- <span class="text-subtitle-2 font-weight-bold text-primary ms-2">
                        {{ getDiscountLabel(product) }}
                      </span> -->
                    </div>
                  </div>
                </v-card-text>
                </NuxtLink>
              </v-card>
            </v-col>
          </v-row>
            </v-card-text>
          </v-card>
        </div>
          <v-dialog v-model="quickView" class="quick-view-wrapper" persistent max-width="1000">
            <v-card>
              <v-card-title class="ms-auto pa-0">
                <v-btn variant="text" @click="quickView = false" class="pe-0">
                  <v-icon class="i-mdi:close" color="red" size="30" />
                </v-btn>
              </v-card-title>
              <v-card-text class="pa-4 pt-0">
                <LazyEcommerceProductDetails
                  :slug="selectedDiscountProduct?.slug"
                  :productData="selectedDiscountProduct"
                  :categoriesArrData="selectedDiscountProduct?.categoriesArr"
                  :sizeGuideEnable="true"
                  @close="quickView = false"
                />
              </v-card-text>
            </v-card>
          </v-dialog>

      </v-card-text>

      <v-card-actions class="pa-6">
        <v-spacer />
        <v-btn
          variant="flat"
          color="primary"
          rounded="xl"
          class="text-capitalize text-h6"
          @click="skipFreeItems"
          prepend-icon="i-mdi:close"
        >
          {{$t('Skip for Now') }}
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup lang="ts">
import { computed, ref } from "vue";
import { formatLocalePrice } from "~/utils/money";
interface FreeProduct {
  id: number;
  name: string;
  slug: string;
  image: string;
  unitPrice: number;
  discountPrice: number;
  needsQuantity: number;
  discountType?: string;
  discountAmount?: number;
}

interface AvailableFreeProduct {
  discountId: number;
  discountTitle: string;
  freeQuantity: number;
  availableProducts: FreeProduct[];
}

interface Props {
  modelValue: boolean;
  availableFreeProducts: AvailableFreeProduct[];
}

const props = defineProps<Props>();
const emit = defineEmits<{
  'update:modelValue': [value: boolean];
  'itemsSelected': [selections: any[]];
}>();
const quickView = ref(false);
const selectedDiscountProduct = ref(null);

const dialog = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
});

// Track selected products for each discount
const selectedProducts = ref<Record<number, Set<number>>>({});

// Initialize selection tracking
watch(() => props.availableFreeProducts, (newProducts) => {
  newProducts.forEach(offer => {
    if (!selectedProducts.value[offer.discountId]) {
      selectedProducts.value[offer.discountId] = new Set();
    }
  });
}, { immediate: true });

const isProductSelected = (productId: number): boolean => {
  return Object.values(selectedProducts.value).some(set => set.has(productId));
};

const getSelectedCount = (discountId: number): number => {
  return selectedProducts.value[discountId]?.size || 0;
};

const toggleProductSelection = (discountId: number, product: FreeProduct, maxQuantity: number) => {
  if (!selectedProducts.value[discountId]) {
    selectedProducts.value[discountId] = new Set();
  }

  const productSet = selectedProducts.value[discountId];
  
  if (productSet.has(product.id)) {
    // Deselect
    productSet.delete(product.id);
  } else {
    // Select (if under limit)
    if (productSet.size < maxQuantity) {
      // Remove from other discount sets first (one product can only be selected once)
      Object.keys(selectedProducts.value).forEach(key => {
        const otherDiscountId = parseInt(key);
        if (otherDiscountId !== discountId) {
          selectedProducts.value[otherDiscountId].delete(product.id);
        }
      });
      
      productSet.add(product.id);
    }
  }
};

const hasValidSelections = computed(() => {
  return props.availableFreeProducts.every(offer => {
    const selectedCount = getSelectedCount(offer.discountId);
    return selectedCount > 0 && selectedCount <= offer.freeQuantity;
  });
});

const addSelectedItemsToCart = () => {
  const selections: any[] = [];
  
  props.availableFreeProducts.forEach(offer => {
    const selectedIds = Array.from(selectedProducts.value[offer.discountId] || []);
    const selectedItems = offer.availableProducts.filter(p => selectedIds.includes(p.id));
    
    if (selectedItems.length > 0) {
      selections.push({
        discountId: offer.discountId,
        discountTitle: offer.discountTitle,
        products: selectedItems,
      });
    }
  });

  emit('itemsSelected', selections);
  dialog.value = false;
};

const skipFreeItems = () => {
  dialog.value = false;
};

// Get discount label for product price display
const getDiscountLabel = (product: FreeProduct): string => {
  if (product.discountPrice === 0) {
    return 'FREE';
  }
  return formatLocalePrice(product.discountPrice);
};

// Get discount badge text
const getDiscountBadge = (product: FreeProduct): string => {
  if (product.discountPrice === 0) {
    return 'FREE';
  }
  
  if (product.discountType === 'percentage' && product.discountAmount) {
    return `${product.discountAmount}% OFF`;
  } else if (product.discountAmount) {
    return `${formatLocalePrice(product.discountAmount)} OFF`;
  }
  
  return 'DISCOUNTED';
};

const handleAddToCartView = (product)=>{
  quickView.value = true;
  selectedDiscountProduct.value = {
    ...product,
    isMultiVariantProduct: true,
    sizes: [],
  };
}

</script>

<style scoped>
.free-product-card {
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.free-product-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.free-product-card.selected {
  border-color: rgb(var(--v-theme-primary));
  border-width: 2px;
}

.free-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 2;
}

.selection-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(var(--v-theme-primary), 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}

/* Clean Vuetify-consistent design */
</style>
