<template>
  <v-hover v-slot="{ isHovering, props }">
    <v-card
      :class="{ 'on-hover': isHovering }"
      v-bind="props"
      class="product-single-card-wrapper bg-white"
      variant="outlined"
    >
      <div class="position-relative">
        <NuxtLink
          :to="localePath(`/product/${product.slug}`)"
          class="d-block position-relative overflow-hidden"
        >
          <NuxtImg
            :src="
              isHovering
                ? replaceMediaDomain(product.hoverImage?.imageGalleryUrls?.large) || product.hoverImage?.imageGalleryUrls?.large
                : replaceMediaDomain(product.featuredImage?.imageGalleryUrls?.large) || product.featuredImage?.imageGalleryUrls?.large
            "
            :alt="product.name"
            width="452"
            height="565"
            class="w-100 h-100 transition-image"
            fit="cover"
            style="aspect-ratio:4/5; object-fit:cover;"
            quality="85"
            format="webp"
          />

          <template v-if="product?.limitedEdition || product?.customerFavorite">
            <v-chip
              label
              class="position-absolute w-100 text-uppercase d-block text-center rounded-0"
              color="#0407078a"
              tag="div"
              variant="flat"
              style="bottom: 5px; left: 0; line-height: 2.3"
            >
              {{
                product?.limitedEdition
                  ? $t("Limited Edition")
                  : $t("Customer Favorite")
              }}
            </v-chip>
          </template>

          <div class="d-flex ga-1 flex-column flex-md-row align-start align-md-center w-100" style="position: absolute; top: 3px; left: 10px">
            <template v-if="product?.mostPopular || product?.specialOffer">
              <v-chip
                variant="flat"
                color="orange"
                label
                :size="chipSize"
              >
                <template #default>
                  <p class="font-weight-semibold text-white d-flex align-center mb-0">
                    <v-icon class="i-mdi:fire" />{{
                      product?.mostPopular ? $t("Popular") : $t("Special Offer")
                    }}
                  </p>
                </template>
              </v-chip>
            </template>
            <template v-if="product?.justArrived || product?.bestValue">
              <v-chip
                variant="flat"
                color="#32B3F0"
                label
                :size="chipSize"
                class="rounded-pill"
              >
                <template #default>
                  <p class="font-weight-semibold mb-0">
                    {{
                      product?.justArrived ? $t("Just Arrived") : $t("Best value")
                    }}
                  </p>
                </template>
              </v-chip>
            </template>
          </div>
        </NuxtLink>
        <div class="hover-add-cart d-none d-md-flex justify-center" :class="{'while-hovering': isHovering}">
          <v-btn
            density="compact"
            variant="flat"
            color="black"
            @click="addToCart"
            class="w-100 w-md-75"
            height="40"
            rounded="pill"
          >
            <v-avatar color="#fff" size="25" class="me-1">
              <v-icon icon="i-mdi:cart" size="15"></v-icon>
            </v-avatar> <span class="text-capitalize text-caption">{{$t("Add To Cart")}}</span>
          </v-btn>
        </div>
      </div>
      <v-card-actions class="d-block">
          <h2 class="text-h6 font-weight-regular">
            <NuxtLink
              :to="localePath(`/product/${product.slug}`)"
              class="text-decoration-none text-grey-darken-4 text-start d-block"
              style="overflow: hidden"
              :style="
                (fromPage === 'checkout' ? {height: 'auto'} : {height: '40px'})
              "
            >
                {{ fromPage === 'checkout' ? trimText(product.name, 10) : trimText(product.name, 80) }}
            </NuxtLink>
          </h2>
        <div class="d-flex align-center justify-space-between w-100 mt-3">
          <div v-if="bestDiscount" :key="bestDiscount?.discountId">
            <div class="single-card-price-wrapper d-flex flex-column flex-sm-row ga-0 ga-sm-1 align-start align-sm-center">
              <p class="ma-0 d-flex ga-1 align-center">
                <span class="text-body-2 text-sm-body-1 font-weight-medium font-weight-sm-bold">{{ Number(bestDiscountPrice) === 0 ? 'Free' : formatLocalePrice(bestDiscountPrice) }}</span>
                <span class="text-decoration-line-through text-grey text-caption text-sm-body-2 font-weight-semibold me-1">
                  {{ regularPrice }}
                </span>
                <span class="text-body-1 text-green font-weight-semibold">
                  {{ bestDiscount?.discountType === 'percentage'? `${bestDiscount?.discountAmount}%`: `${formatLocalePrice(bestDiscount?.discountAmount)}` }}{{$t('OFF')}}
                </span>
              </p>
            </div>
          </div>
          <div 
            v-else  
            class="single-card-price-wrapper d-flex flex-column flex-sm-row ga-0 ga-sm-1 align-start align-sm-center"
          >
            <template v-if="discountedPrice">
              <p class="ma-0">
                <span
                  class="text-body-2 text-sm-body-1 font-weight-medium font-weight-sm-bold me-1"
                  >{{ discountedPrice }}</span
                >
                <span
                  class="text-decoration-line-through text-grey text-caption text-sm-body-2 font-weight-semibold me-1"
                  >{{ regularPrice }}</span
                >
              </p>
              <span class="text-body-1 text-red font-weight-semibold">
                {{ discountPercentage }}%{{$t('OFF')}}
              </span>
            </template>
            <span
              v-else
              class="price font-weight-semibold"
              style="line-height: 1"
              >{{ regularPrice }}</span
            >
          </div>
          <div class="d-block d-md-none">
            <v-btn
              icon
              density="compact"
              variant="flat"
              color="black"
              @click="addToCart"
              style="z-index: 200; padding: 0;"
              size="30"
            >
              <v-icon icon="i-mdi:cart" size="18" />
            </v-btn>
          </div>
        </div>
      </v-card-actions>
      <LazyEcommerceWishlist :product="product" />
    </v-card>
  </v-hover>
  <v-dialog v-model="quickView" class="quick-view-wrapper" persistent>
    <v-card>
      <v-card-title class="ms-auto pa-0">
        <v-btn variant="text" @click="quickView = false" class="pe-0">
          <v-icon class="i-mdi:close" color="red" size="30" />
        </v-btn>
      </v-card-title>
      <v-card-text class="pa-4 pt-0">
        <LazyEcommerceProductDetails
          :slug="product.slug"
          :productData="product"
          :categoriesArrData="product.categoriesArr"
          :sizeGuideEnable="true"
          @close="quickView = false"
        />
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script setup lang="ts">
import type { ProductListSingleCard } from "~/types/comoponent";
import { trimText } from "../../utils/functions";
import { useMediaType } from "../../composables/useMediaType";
import { computed, ref, watch } from "vue";
import { storeToRefs } from "pinia";

const { getActiveLanguageId } = useLanguage();
const {replaceMediaDomain} = useMediaType();

const props = defineProps<{
  product: ProductListSingleCard;
  index?: number;
  categorySlug?: any;
  fromPage?: any,
}>();

const { gtmCartEvent } = useTagManager();

const localePath = useLocalePath();
const cartStore = useShoppingCartStore();

const regularPrice = computed(() => formatLocalePrice(props.product.unitPrice));
const discountedPrice =
  props.product?.discountPrice > 0
    ? computed(() => formatLocalePrice(props.product?.discountPrice))
    : null;

const discountPercentage = computed(() => {
  if (
    !props.product?.discountPrice ||
    Number(props.product.unitPrice) <= Number(props.product.discountPrice)
  ) {
    return 0;
  }

  const regular = Number(props.product.unitPrice);
  const discounted = Number(props.product.discountPrice);
  return Math.round(((regular - discounted) / regular) * 100);
});

const bestDiscount = ref(null);
const bestDiscountPrice = computed(()=>{
  if(bestDiscount.value){
    return cartStore?.getDiscountPriceValue(props?.product, bestDiscount.value);
  }
})
const { getAvailableFreeProducts } = storeToRefs(cartStore);

watch(
  ()=> getAvailableFreeProducts.value,
  async () => {
    bestDiscount.value = cartStore?.findBestDiscountForProduct(props?.product?.id);
  },
  {
    deep: true,
    immediate: true,
  }
)

const quickView = ref(false);

const { xs, sm, md, lg, xl } = useDisplay();

const chipSize = computed(() => {
  if (xs.value) return 'x-small';
  if (sm.value) return 'small';
  return 'small';
});

const addToCart = () => {
  if (!props.product.isMultiVariantProduct) {
    const item = {
      id: props.product.id,
      name: props.product.name,
      slug: props.product.slug,
      featuredImage: props.product.featuredImage,
      productId: props.product.id,
      unitPrice: props.product.unitPrice,
      discountPrice:
        props.product.discountPrice > 0 ? props.product.discountPrice : null,
      quantity: 1,
      isMultiVariant: false,
      sku: props.product.sku,
      currency: props.product.currency,
      brand: props.product.brand_name,
      categoriesArr: props.product?.categoriesArr,
    };

    const response = cartStore.productAddToCart(item, getActiveLanguageId());
    if (response?.isSuccess) {
      const gtagData = {
        id: item.id,
        name: item.name,
        unitPrice: item.unitPrice,
        quantity: item.quantity,
        color: null,
        size: null,
        currency: props.product.currency,
        brand: props.product.brand_name,
        discountPrice: item.discountPrice,
        sku: props.product.sku,
        categoriesArr: props.product?.categoriesArr,
      };

      gtmCartEvent([gtagData]);
    }
  } else {
    quickView.value = true;
  }
};
</script>

<style scoped lang="scss">
.v-card {
  transition: color 1s ease-in-out;
}

.show-btns {
  color: rgba(0, 0, 0, 1) !important;
}

.quick-view-wrapper {
  width: 75%;
}

// .block-ellipsis {
//   display: block;
//   display: -webkit-box;
//   max-width: 100%;
//   height: 43px;
//   margin: 0 auto;
//   font-size: 14px;
//   line-height: 1;
//   -webkit-line-clamp: 3;
//   -webkit-box-orient: vertical;
//   overflow: hidden;
//   text-overflow: ellipsis;
// }

@media (max-width: 575px) {
  .product-single-card-wrapper {
    .single-card-price-wrapper {
      .price {
        font-size: 0.85em;
      }
    }
  }

  .quick-view-wrapper {
    width: 100%;
  }
}

.hover-add-cart{
  position: absolute;
  bottom: 15px;
  left: 10px;
  right: 10px;
  z-index: 99999;
  opacity: 0;
  transform: translateY(10px);
  transition: opacity 1s ease, transform 1s ease;
}
.while-hovering{
  opacity: 1;
  transform: translateY(0);
}

.transition-image {
  transition: transform 1.2s ease;
}

.transition-image:hover {
  transform: scale(1.05);
  // opacity: 0.9;
}
</style>
