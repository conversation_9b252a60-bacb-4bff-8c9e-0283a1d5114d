<template>
	<v-container fluid>
		<v-sheet elevation="0" class="pt-2">
			<h2 class="text-h5 mb-2 pl-4">{{ $t('Complete the fit') }}</h2>

			<v-row>
				<v-col cols="6" v-for="item in suggestions" :key="item.id">

					<v-sheet elevation="1" class="product-single-card-wrapper">
						<NuxtLink :to="localePath(`/product/${item.slug}`)" class="d-block position-relative">
							<NuxtImg :src="item?.featuredImage?.medium || item?.image" :alt="item?.name" loading="lazy"
								sizes="xs:100vw sm:100vw md:100vw lg:100vw xl:100vw" format="webp" width="452" height="505"
								class="w-100 h-100" fit="cover" quality="85" style="aspect-ratio:4/5; object-fit:cover;" />
						</NuxtLink>

						<v-card-actions class="d-block">
							<NuxtLink :to="localePath(`/product/${item.slug}`)"
								class="text-decoration-none text-grey-darken-4 text-start d-block"
								style="height: 40px; overflow: hidden">
								<h6 class="text-h6 font-weight-regular">
									{{ item.name?.length > 60 ? item.name?.slice(0, 60) + '...' : item.name }}
								</h6>

							</NuxtLink>
							<div class="d-flex align-center justify-space-between w-100 mt-3">
								<div class="single-card-price-wrapper">
									<div v-html="getPriceWithCurrency(item.discountprice, item.unitPrice, 1)"></div>
								</div>
								<div>
									<NuxtLink :to="localePath(`/product/${item.slug}`)"
										class="v-btn v-btn--flat v-theme--light v-btn--density-default v-btn--size-x-small v-btn--variant-tonal py-1">
										<v-icon class="i-mdi:eye-outline text-error v-btn__content" size="18" />
									</NuxtLink>
								</div>
							</div>
						</v-card-actions>
					</v-sheet>

				</v-col>
			</v-row>
		</v-sheet>
	</v-container>
</template>

<script lang="ts" setup>
import { formatLocalePrice } from "~/utils/money";
const localePath = useLocalePath();

type Suggestion = {
	id: number;
	image: string;
	slug: string;
	name: string;
	isMultiVariant?: boolean;
	discountprice: number;
	unitPrice: number;
}
const props = defineProps<{
	suggestions: Suggestion[]
}>()

const getPriceWithCurrency = (discountPrice: any, unitPrice: any, quantity: number) => {

	if (parseInt(discountPrice) || parseInt(discountPrice) > 0) {
		const discountPriceWithQuantity = discountPrice * quantity;
		const unitPriceWithQuantity = unitPrice * quantity;

		return `<span class="h6 font-weight-bold">${formatLocalePrice(discountPriceWithQuantity)}</span>
          <span class="h6 text-decoration-line-through text-red">${formatLocalePrice(unitPriceWithQuantity)}</span>`

	} else {
		const priceWithQuantity = unitPrice * quantity;
		return `<span class="h6 font-weight-bold">${formatLocalePrice(priceWithQuantity)}</span>`
	}
}
</script>