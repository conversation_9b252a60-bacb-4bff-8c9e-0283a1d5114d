<template>
  <div>
    <template v-if="productPageLoading">
      <LazyLoadersSingleProduct />
    </template>
    <template v-else>
      <v-row>
        <v-col cols="12" md="7" xl="6">
          <SingleProductImageGallery :images="totalImages" />
        </v-col>
        <v-col cols="12" md="5" xl="6">
          <LazySingleProductTitle :title="props?.productData?.name" tag="h1" />
          <LazySingleProductSimpleProductPrice
            v-if="variantData || unitPrice"
            :unit-price="
              parseFloat(
                isMultiVariantProduct ? variantData?.unitPrice : unitPrice
              )
            "
            :discount-price="
              parseFloat(
                isMultiVariantProduct
                  ? variantData?.discountPrice
                  : discountedPrice
              )
            "
            :bestDiscount="bestDiscount"
            :cartStore="cartStore"
          />
          <LazySingleProductDiscountsForProduct
            class="mt-3"
            v-if="discountsForProduct"
            :discounts="discountsForProduct"
            :cartStore="cartStore"
          />
          <div v-if="props?.productData?.isMultiVariantProduct" class="py-3 py-md-6">
            <template v-if="availableColors && availableColors?.length > 0">
              <p class="mt-3 text-black font-weight-semibold opacity-1 mb-0">
                <span class="font-weight-light">{{ $t("Color") + ": "}}</span> <span class="font-weight-bold">{{selectedColor?.name }}</span>
              </p>
              <v-radio-group
                v-model="selectedColor"
                inline
                hide-details
                class="product-color-variations-wrapper mt-3"
                @update:modelValue="selectedColorByUser"
              >
                <v-radio
                  v-for="(option, index) in availableColors"
                  :value="option"
                  :key="index"
                  true-icon=""
                  false-icon=""
                  density="default"
                  class="rounded-xl overflow-hidden"
                >
                  <NuxtImg
                    :src="replaceMediaDomain(option?.images[0]?.imageGalleryUrls?.thump) || option?.images[0]?.imageGalleryUrls?.thump"
                    :alt="option?.name"
                    preload
                    format="webp"
                    width="108"
                    height="135"
                    class="h-100 w-100"
                    fit="cover"
                    quality="85"
                  />
                </v-radio>
              </v-radio-group>
            </template>
            <div
              class="class mt-6 d-flex align-center justify-space-between ga-2"
            >
              <p class="text-black mb-0">
                <span class="font-weight-light">{{ $t("Size") + ": "}}</span><span class="font-weight-bold">{{selectedSize?.name }}</span>
              </p>
              <div
                @click="sizeGuides = true"
                class="text-body-1 font-weight-bold animation-offset-underline cursor-pointer d-flex align-center"
                v-if="sizeGuideEnable"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  aria-hidden="true"
                  focusable="false"
                  role="presentation"
                  class="icon icon-size-chart icon--line"
                  viewBox="0 0 64 64"
                  width="20"
                  height="20"
                  style="stroke-width: 5px"
                >
                  <path
                    d="M22.39 33.53c-7.46 0-13.5-3.9-13.5-8.72s6-8.72 13.5-8.72 13.5 3.9 13.5 8.72a12 12 0 0 1-.22 1.73"
                    fill="none"
                    stroke="currentColor"
                  ></path>
                  <ellipse
                    cx="22.39"
                    cy="24.81"
                    rx="3.28"
                    ry="2.12"
                    fill="none"
                    stroke="currentColor"
                  ></ellipse>
                  <path
                    d="M8.89 24.81V38.5c0 7.9 6.4 9.41 14.3 9.41h31.92V33.53H22.39m24.39 0v7.44m-8.13-7.44v7.44m-8.13-7.44v7.44m-8.13-7.44v7.44"
                    fill="none"
                    stroke="currentColor"
                  ></path></svg>{{ $t("Size Guide") }}
              </div>
            </div>
            <!-- <v-radio-group
              v-model="selectedSize"
              inline
              hide-details
              class="mt-3 product-size-variations-wrapper"
              @update:modelValue="selectedSizeByUser"
              :key="reloadAvailableSizes"
            >
              <v-radio
                :value="option"
                density="default"
                v-for="option in availableSizes"
                :key="option"
                true-icon=""
                class="radio-type-text"
                false-icon=""
                :disabled="!isSizeAvailableForColor(option?.id)"
                :class="{ 'bg-black': selectedSize?.id === option?.id }"  
              >
                <p>{{ option?.name }}</p>
              </v-radio>
            </v-radio-group> -->
            <div class="product-size-variations-wrapper sizes-wrapper mt-3">
             <v-btn
               v-for="(option, index) in availableSizes"
               :key="option?.id + 'sizeOption' + index"
               :disabled="!isSizeAvailableForColor(option?.id)"
               :color="selectedSize?.id === option?.id ? 'black' : ''"
               :variant="selectedSize?.id === option?.id ? 'flat' : 'outlined'"
               class="size-btn text-capitalize"
               @click="selectSize(option)"
               :aria-pressed="selectedSize?.id === option?.id"
               :aria-label="option?.name"
               :name="'sizeButton_'+option?.id"
               :class="
                 {
                   'custom-disabled': !isSizeAvailableForColor(option?.id),
                   'cross-lines': !isSizeAvailableForColor(option?.id)
                 }
               "
                ref="sizeButtons"
                rounded="xl"
             >
               {{option?.name}}
             </v-btn>
           </div>
          </div>
          <LazySingleProductTaglines
            :tagLine1="tagLine1"
            :tagLine2="tagLine2"
            :tag-line2-type="tagLine2Type"
          />
          <LazySingleProductAddToCart
            @addProductToCart="addToCart"
            @buyNowProduct="addToCart"
            :productCategory="singleProduct?.categorySlug"
            :product-id="singleProduct?.id"
            :product-name="singleProduct?.name"
            :categories="singleProduct?.categories"
            :product="!isMultiVariantProduct ? singleProduct : variantData"
            :currency="singleProduct?.currency"
          >
            <template #wishlist>
              <LazySingleProductWishlist :product="productForWishlist" />
            </template>
          </LazySingleProductAddToCart>
          <LazySingleProductDescription
            :description="productDescription"
            :material-care="materialCare"
            :shipping-return="shippingReturn"
            :wow-factors="wowFactors"
          />
        </v-col>
      </v-row>
      <client-only>
        <LazySingleProductSizeGuides
          v-model="sizeGuides"
          v-if="sizeGuideEnable"
          :size-chart-image="singleProduct?.sizeChart"
          :size-chart-data="singleProduct?.sizeChartData"
        />
      </client-only>
    </template>
  </div>
</template>

<script lang="ts" setup>
import type { SingleProduct } from "~/types/comoponent";
import { nextTick, onMounted, ref, toRaw, watch } from "vue";
import { useLanguage } from "../../composables/useLanguage";
import { useMediaType } from "../../composables/useMediaType";
import {showSnackbarResponse} from "../../utils/functions";
import { useDiscountStore } from "../../stores/discounts";
import { storeToRefs } from "pinia";

const { getDomaCountryId, getDomaCountryCode, getCurrentHost, getDomaLanguageId } =
  useCurrentHost();
const countryId = getDomaCountryId();
const countryCode = getDomaCountryCode();
const languageId = getDomaLanguageId();

const props = defineProps<{
  slug: string;
  sizeGuideEnable?: boolean;
  productData: null;
  categoriesArrData: null;
  targetSku?: null;
  matchSkuColorId?: null;
  matchSkuSizeId?: null;
}>();

const { replaceMediaDomain } = useMediaType();
const { gtmCartEvent, gtmProductViewEvent } = useTagManager();
const router = useRouter();
const route = useRoute();
const emit = defineEmits(["findProductData", "singleProductMeta", "close"]);
const { getActiveLanguageId } = useLanguage();

const cartStore = useShoppingCartStore();

const productStore = useProductStore();
const singleProduct = ref<SingleProduct>();
const sizeGuides = ref(false);
const selectedSize: any = ref(null);
const selectedColor: any = ref(null);
const variantData: any = ref(null);
const totalImages = ref([]);

const productName = computed(() => singleProduct.value?.name);
const unitPrice = computed(() => singleProduct.value?.unitPrice);
const discountedPrice = computed(() => singleProduct.value?.discountPrice);

const productDescription = computed(() => singleProduct.value?.description);
const materialCare = computed(() => singleProduct.value?.materialCare);
const shippingReturn = computed(() => singleProduct.value?.shippingReturn);
const wowFactors = computed(() => singleProduct.value?.wowFactors);
const tagLine1 = computed(() => singleProduct.value?.tagLine1);
const tagLine2 = computed(() => singleProduct.value?.tagLine2);
const tagLine2Type = computed(() => singleProduct.value?.tagLine2Type);

const availableSizes: any = computed(() => singleProduct.value?.sizes);
const availableColors: any = computed(() => singleProduct.value?.colors);

const isMultiVariantProduct: any = computed(
  () => singleProduct.value?.isMultiVariantProduct
);
const productVariants = computed(() =>
  singleProduct.value?.isMultiVariantProduct
    ? singleProduct.value.variants
    : null
);

const stock = computed(() =>
  singleProduct.value?.isMultiVariantProduct
    ? variantData.value?.quantity
    : singleProduct.value?.quantity
);
const productTags = ref();

const targetSizeId = ref<number>();
const targetColorId = ref<number>();
const categoriesArr = ref<string[]>([]);

const triggerViewEvent = () => {
  if (!singleProduct) return;

  const gtagData = {
    id: singleProduct.value.id,
    name: productName.value,
    unitPrice:
      variantData.value?.unitPrice ||
      singleProduct.value?.variants[0]?.variantDetails[0]?.unitPrice ||
      0,
    quantity:
      variantData.value?.quantity ||
      singleProduct.value?.variants[0]?.variantDetails[0]?.quantity ||
      1,
    color:
      variantData.value?.color?.name ||
      singleProduct.value?.variants[0]?.variantDetails[0]?.color?.name ||
      null,
    size:
      variantData.value?.size?.name ||
      singleProduct.value?.variants[0]?.variantDetails[0]?.size?.name ||
      null,
    currency: singleProduct?.value?.currency,
    brand: singleProduct.value?.brand_name,
    discountPrice:
      variantData.value?.discountPrice ||
      singleProduct.value?.variants[0]?.variantDetails[0]?.discountPrice ||
      0,
    categoriesArr: categoriesArr.value,
    sku:
      variantData.value?.sku ||
      singleProduct.value?.variants[0]?.variantDetails[0]?.sku ||
      singleProduct.value?.sku,
  };

  gtmProductViewEvent([gtagData]);
};

const productForWishlist = computed(() => {
  return {
    bestValue: singleProduct.value?.bestValue,
    categoryId: singleProduct.value?.categoryId,
    categorySlug: singleProduct.value?.categorySlug,
    customerFavorite: singleProduct.value?.customerFavorite,
    discountPrice: parseFloat(
      isMultiVariantProduct
        ? variantData.value?.discountPrice
        : discountedPrice.value
    ),
    featuredImage: singleProduct.value?.featuredImage,
    hoverImage: singleProduct.value?.hoverImage,
    id: singleProduct.value?.id,
    isMultiVariantProduct: isMultiVariantProduct,
    justArrived: singleProduct.value?.justArrived,
    limitedEdition: singleProduct.value?.limitedEdition,
    mostPopular: singleProduct.value?.mostPopular,
    name: productName.value,
    quantity: stock.value,
    slug: props.slug,
    specialOffer: singleProduct.value?.specialOffer,
    unitPrice: parseFloat(
      isMultiVariantProduct ? variantData.value?.unitPrice : unitPrice.value
    ),
    color: isMultiVariantProduct.value ? variantData.value?.color?.name : null,
    size: isMultiVariantProduct.value ? variantData.value?.size?.name : null,
    currency: singleProduct?.value?.currency,
    brand: singleProduct.value?.brand_name,
    categoriesArr: categoriesArr.value,
    sku: isMultiVariantProduct.value
      ? variantData.value?.sku || singleProduct.value?.sku
      : singleProduct.value?.sku,
  };
});

const addToCart = async (id: number, quantity: number) => {
  const item = {
    name: productName.value,
    slug: singleProduct.value?.slug,
    featuredImage: !singleProduct.value?.isMultiVariantProduct
      ? singleProduct.value?.featuredImage
      : variantData?.value?.color?.id
      ? toRaw(await getColorImages(variantData.value.color.id, true))
      : singleProduct.value?.featuredImage,
    productId: singleProduct.value?.id,
    variantId: !singleProduct.value?.isMultiVariantProduct
      ? singleProduct.value?.id
      : variantData.id,
    unitPrice: !singleProduct.value?.isMultiVariantProduct
      ? unitPrice
      : variantData.value?.unitPrice,
    discountPrice: !singleProduct.value?.isMultiVariantProduct
      ? discountedPrice
      : variantData.value?.discountPrice,
    quantity: quantity,
    stock: parseInt(stock.value),
    isMultiVariant: isMultiVariantProduct.value,
    color: isMultiVariantProduct.value ? variantData.value.color : null,
    size: isMultiVariantProduct.value ? variantData.value.size : null,
    categories: singleProduct?.value?.categories,
    categoriesArr: categoriesArr.value,
    sku: isMultiVariantProduct.value
      ? variantData.value?.sku
      : singleProduct.value?.sku,
    currency: singleProduct?.value?.currency,
    brand: singleProduct.value?.brand_name,
  };

  cartStore.productAddToCart(
    item,
    getActiveLanguageId(),
    countryId
  )?.then((response: any) => {
    if (response?.isSuccess) {
      emit("close");
      const gtagData = {
        id: singleProduct.value.id,
        name: item.name,
        unitPrice: item.unitPrice,
        quantity: item.quantity,
        categories: item.categories,
        color: item.color?.name,
        size: item.size?.name,
        currency: item?.currency,
        brand: item?.brand,
        discountPrice: item.discountPrice,
        categoriesArr: categoriesArr.value,
        sku: item.sku,
      };

      gtmCartEvent([gtagData]);
    }    
    showSnackbarResponse(response);
  })

  cartStore.openModal();
}

const updateRouteSku = () => {


  nextTick(() => {
    if (variantData.value?.sku) {
      console.log(route.path);
      const newUrl = `${window.location.pathname}?${new URLSearchParams({
        ...route.query,
        sku: variantData.value.sku,
      }).toString()}`;

      window.history.replaceState({}, "", newUrl);
    }
  })


  /*
  if (variantData.value?.sku) {
    router.replace({
      query: {
        ...route.query,
        ...{ sku: variantData.value.sku },
      },
    });
  }
  */
};

const validateForm = () => {
  if (!selectedColor.value) {
    variantData.value = getSingleVariantData(selectedSize.value);
    variantData.value.color = {
      image: {
        originalImageUrl: singleProduct?.value?.featuredImage?.originalImageUrl,
      },
    };
  } else {
    variantData.value = getVariantData(selectedSize.value, selectedColor.value);
    variantData.value.color["image"] = selectedColor.value?.images[0];
  }

  updateRouteSku();
};

const getColorsBySize = (id: any) => {
  if (productVariants.value) {
    const elementPosition = productVariants.value
      ?.map((x: any) => x.size.id)
      .indexOf(parseInt(id));
    return productVariants.value[elementPosition];
  }
};

const getVariantData = (size: any, color: any) => {
  if (size) {
    const colorData: any = getColorsBySize(parseInt(size.id));
    const elementPosition = colorData.variantDetails
      .map((variant: any) => parseInt(variant.color.id))
      .indexOf(parseInt(color.id));
    const elm = colorData.variantDetails[elementPosition];
    elm.size = size;
    return elm;
  }
};

const getSingleVariantData = (size: any) => {
  const data = getColorsBySize(size.id);

  if (data) {
    const elm = data?.variantDetails?.[0];
    elm.size = size;
    return elm;
  }
};

const selectedSizeByUser = (value: any) => {
  validateForm();
  triggerViewEvent();
};

const selectSize = (option: any) => {
  selectedSize.value = option;
  selectedSizeByUser(option);
};

const selectedColorByUser = async () => {
  validateForm();
  const images = await getColorImages(selectedColor.value.id);
  if (images?.length > 0) {
    totalImages.value = images;
  }
  triggerViewEvent();
};

const getColorImages = async (colorId: number, single = false) => {
  const color = await availableColors.value.find(
    (item: any) => item.id === colorId
  );
  if (color && single) {
    return color.images[0];
  } else {
    return color.images;
  }
};

const getTargetSelectedSize = () => {
  if (targetSizeId.value && availableSizes.value?.length) {
    const data = availableSizes.value.find(
      (item) => item.id == targetSizeId.value
    );
    if (data) return data;
  }

  return availableSizes.value?.[0];
};

const getTargetSelectedColor = () => {
  if (targetColorId.value && availableColors.value?.length) {
    const data = availableColors.value.find(
      (item) => item.id == targetColorId.value
    );
    if (data) return data;
  }

  return availableColors.value?.[0];
};

const isColorAvailableForSize = (colorId: Number) => {
  if (!selectedSize.value) return true;

  const sizeVariant = productVariants.value.find((variant: any) => {
    return variant.size.id === selectedSize.value.id;
  });

  if (!sizeVariant) return false;

  const colorVariant = sizeVariant.variantDetails.find(
    (variant: any) => variant.color.id === colorId
  );

  if (!colorVariant) return false;

  return colorVariant.isActive && colorVariant.quantity > 0;
};

const isSizeAvailableForColor = (sizeId: Number) => {
  if (!selectedColor?.value) return true;
  const sizeVariant = productVariants?.value?.find((variant: any) => {
    return variant?.size?.id === sizeId;
  });
  if (!sizeVariant) return false;
  const colorVariant = sizeVariant?.variantDetails?.find(
    (variant: any) => variant?.color?.id === selectedColor?.value?.id
  );
  if (!colorVariant) return false;
  return colorVariant?.isActive && colorVariant?.quantity > 0;
};

/**WATCHERS**/

var reloadAvailableSizes = ref("new");

watch(
  () => selectedColor.value,
  () => {
    reloadAvailableSizes.value = JSON.stringify(new Date());
  }
);

watch(
  () => selectedSize.value,
  () => {
    if (!selectedColor.value && selectedSize.value) {
      variantData.value = getSingleVariantData(selectedSize.value);
      variantData.value.color = {
        image: {
          originalImageUrl:
            singleProduct?.value?.featuredImage?.originalImageUrl,
        },
      };
      updateRouteSku();
      return;
    }

    getColorsBySize(selectedSize.value?.id);
    const data = getVariantData(selectedSize.value, selectedColor.value);

    if (selectedColor.value) {
      variantData.value = data;
      variantData.value.color["image"] = selectedColor.value?.images[0];
      updateRouteSku();
    }
  }
);

watch(
  () =>
    (availableColors.value && availableColors.value.length > 0) ||
    availableSizes.value ||
    targetColorId.value ||
    targetSizeId.value,
  () => {
    if (availableSizes.value?.length > 0 && availableColors.value?.length > 0) {
      selectedSize.value = getTargetSelectedSize();
      selectedColor.value = getTargetSelectedColor();

      if (targetColorId.value) {
        selectedColorByUser();
      }
    } else if (availableSizes.value?.length > 0) {
      selectedSize.value = getTargetSelectedSize();
    }
  }
);

watch(
  [
    () => singleProduct.value?.featuredImage,
    () => singleProduct.value?.hoverImage,
    () => singleProduct.value?.imageGallery,
  ],
  ([newFeatured, newHover, newGallery]) => {
    // totalImages.value = [newA, newB].concat(newC);
    const images: any = [];

    if (newFeatured) images.push(newFeatured);
    if (newHover) images.push(newHover);
    if (Array.isArray(newGallery)) images.push(...newGallery);

    totalImages.value = images;
  }
);

const productPageLoading = ref(false);

const extractColorAndSizeFromTargetSKU = (targetSku: string) => {
  return {
    color: props?.matchSkuColorId || 0,
    size: props?.matchSkuSizeId || 0,
  };
};

const fetchData = async () => {
  productPageLoading.value = true;
  const response = await productStore.getProductByCountrySlug(
    countryId,
    props.slug,
    getActiveLanguageId()
  );
  const targetSku =
    route?.query && route?.query["sku"] ? route?.query["sku"] : "";

  if (response?.isSuccess) {
    productStore.product = response?.data?.product;
    singleProduct.value = response?.data?.product;
    productTags.value = response?.data?.productTags;
    categoriesArr.value = response?.data?.categoriesArr;

    if (targetSku) {
      const { color, size } = extractColorAndSizeFromTargetSKU(targetSku || "");
      targetColorId.value = color;
      targetSizeId.value = size;
    }

    emit("findProductData", {
      productId: singleProduct.value?.id,
      productName: productName.value,
      description: productDescription.value?.replace(/<[^>]*>/g, ""),
      image: singleProduct.value?.featuredImage?.imageUrl,
      categories: singleProduct.value?.categories,
      relatedTags: response?.data?.productTags,
    });

    emit("singleProductMeta", response?.data?.seoMeta);

    productPageLoading.value = false;

    nextTick(() => {
      triggerViewEvent();
    });
  }
};

const sizeButtons = ref([]);

const sizeButtonsWidth = async()=>{
  await nextTick(); // wait until buttons are rendered
  const buttons = sizeButtons.value;

  if (buttons?.length) {
    let maxWidth = 0;

    // Calculate widest button
    buttons.forEach((btn) => {
      const btnWidth = btn?.$el?.offsetWidth || btn?.offsetWidth;
      if (btnWidth > maxWidth) maxWidth = btnWidth;
    });

    // Apply max width to all
    buttons.forEach((btn) => {
      if (btn?.$el) btn.$el.style.width = `${maxWidth}px`;
      else if (btn) btn.style.width = `${maxWidth}px`;
    });
  }
}

const bestDiscount = ref(null);
const { getAvailableFreeProducts } = storeToRefs(cartStore);

watch(
  ()=> getAvailableFreeProducts.value,
  async () => {
    bestDiscount.value = cartStore?.findBestDiscountForProduct(props?.productData?.id);
  },
  {
    deep: true,
    immediate: true,
  }
);

const discountsForProduct = ref([]);
const discountStore = useDiscountStore();
const fetchGetAllDiscountsForSingleProduct = ()=>{
  let params = {
    productId: props?.productData?.id,
    countryId: countryId,
    languageId: languageId,
  }
  discountStore
    ?.getAllDiscountsForSingleProduct(params)
    ?.then((response)=>{
      discountsForProduct.value = response?.data;
    })
}

onMounted(async () => {
  if (props?.productData) {
    fetchGetAllDiscountsForSingleProduct();
    productPageLoading.value = false;
    singleProduct.value = props?.productData;
    // singleProduct.value?.imageGallery contains null sometimes
    singleProduct.value.imageGallery = singleProduct?.value?.imageGallery?.filter((el: any)=> el!=null);
    categoriesArr.value = props?.categoriesArrData;
    const { color, size } = extractColorAndSizeFromTargetSKU(
      props?.targetSku || ""
    );
    targetColorId.value = color;
    targetSizeId.value = size;
  }
  if (!props?.productData?.sizes || props?.productData?.sizes?.length === 0) {
    await fetchData();
  }
  else{
    cartStore.showFreeProductsModal = false;
  }

  triggerViewEvent();
  sizeButtonsWidth();
});
</script>

<style lang="scss">
.product-size-variations-wrapper {
  &.v-radio-group {
    .v-input__control {
      .v-selection-control {
        &.radio-type-text {
          border: 1px solid #040707;

          .v-selection-control__wrapper {
            p {
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              font-size: 0.8rem;
            }
          }
        }
      }
    }
  }
}

.product-size-variations-wrapper,
.product-color-variations-wrapper {
  .v-input__control {
    .v-label {
      color: black;
      font-weight: 600;
      margin-inline-start: 0 !important;
      font-size: 1rem;

      + .v-selection-control-group {
        padding-inline-start: 0 !important;
      }
    }
  }

  .v-selection-control-group {
    gap: 5px;

    .v-selection-control__wrapper {
      display: block;
      width: 40px;
      height: 40px;

      .v-selection-control__input {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        border-radius: 0;

        &::before {
          border-radius: 0;
        }
      }
    }

    .v-selection-control--dirty {
      .v-selection-control__input {
        border: 2px solid #2f3636;
        border-radius: 6px;
      }
    }

    .v-selection-control--disabled {
      border: 1px solid #000;
      position: relative;

      .v-selection-control__input {
        &::before {
          position: absolute;
          width: 142%;
          height: 1px;
          left: -20%;
          opacity: 1;
          transform: rotate(135deg);
          top: 50%;
        }
      }
    }
  }
}

.product-color-variations-wrapper {
  .v-selection-control-group {
    .v-selection-control__wrapper {
      width: 50px;
      height: 60px;
    }
  }
}

// .animation-offset-underline {
//   text-decoration: none;
//   display: block;
//   position: relative;
//   &::after {
//     content: "";
//     position: absolute;
//     left: 0;
//     bottom: 0;
//     width: 100%;
//     height: 0.1em;
//     background-color: #000;
//     transition: opacity 300ms, transform 300ms;
//     opacity: 1;
//     transform: scale(0);
//     transform-origin: center;
//   }
//   &:hover::after,
//   &:focus::after {
//     transform: scale(1);
//   }
// }

.cross-lines::before,
 .cross-lines::after {
   content: '';
   position: absolute;
   width: 100%;
   height: 1px;
   background-color: rgb(0, 0, 0, .4);
   top: 48%;
   left: 0%;
   transform: rotate(27deg);
 }

 .cross-lines::after {
   transform: rotate(-27deg);
 }

.size-btn {
  white-space: nowrap;
  text-align: center;
  margin-right: 8px;
  margin-bottom: 8px;
}

.blink-me {
  // font-family: "Patua One", serif;
  font-size: 16px !important;
  letter-spacing: 4px;
  animation: colorFade1 3s infinite;
  &:hover{
    animation: unset;
  }
}

@keyframes colorFade1 {
  0% {
    color: black;
    opacity: 1;
  }
  50% {
    color: black;
    opacity: 0;
  }
  100% {
    color: black;
    opacity: 1;
  }
}
</style>
