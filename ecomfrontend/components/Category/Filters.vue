<template>
  <v-form @submit.prevent="applyFilters" class="h-100">
    <v-card color="grey50" rounded="0" class="mb-0 h-100">
      <v-card-text>
        <v-row class="align-center">
          <!-- <v-col md="1">
            <h2 class="text-capitalize float-start float-md-end d-flex">
              <v-img
                src="https://pantoneclos3bucket.s3.ap-south-1.amazonaws.com/pantoneclo-img/filter-icon.png"
                class="me-2"
                width="15"
              />
              {{ $t("Filter") }}
            </h2>
          </v-col> -->
          <v-col cols="12" md="2">
            <v-select
              hide-details
              rounded
              color="primary"
              clearable
              ref="select1"
              :label="$t('Brand')"
              :items="brandStore.brands"
              item-title="name"
              item-value="id"
              variant="solo"
              :disabled="!!brandId"
              v-model="form.brandId"
              @update:menu="onUpdateBrandMenu"
            ></v-select>
          </v-col>
          <v-col cols="12" md="2">
            <v-select
              hide-details
              rounded
              color="primary"
              v-model="form.size"
              @update:menu="onUpdateAttributeValues"
              clearable
              :label="$t('Size')"
              :items="sizes"
              item-title="attribute_value_name"
              item-value="attribute_value_id"
              multiple
              chips
              variant="solo"
            >
            </v-select>
          </v-col>
          <v-col cols="12" md="2">
            <v-select
              hide-details
              rounded
              color="primary"
              v-model="form.color"
              @update:menu="onUpdateAttributeValues"
              chips
              clearable
              :label="$t('Color')"
              :items="colors"
              item-title="attribute_value_name"
              item-value="attribute_value_id"
              variant="solo"
              multiple
            >
            </v-select>
          </v-col>
          <v-col cols="12" md="2">
            <v-select
              hide-details
              rounded
              color="primary"
              clearable
              :label="$t('Discount')"
              :items="discountRangeOptions"
              item-title="name"
              item-value="value"
              variant="solo"
              v-model="form.discountRange"
            >
              <template v-slot:prepend-inner>
                <v-icon size="small" color="primary">mdi-percent</v-icon>
              </template>
            </v-select>
          </v-col>
          <v-col cols="12" md="2">
            <v-select
              hide-details
              rounded
              color="primary"
              clearable
              :label="$t('Order By')"
              :items="orderList"
              variant="solo"
              v-model="form.orderBy"
              item-title="name"
              item-value="id"
            ></v-select>
          </v-col>

          <v-col>
            <div class="d-flex ga-2 flex-column flex-md-row">
              <v-btn
                class="w-100 w-sm-auto text-uppercase py-6"
                color="primary"
                type="submit"
                style="line-height: 0"
                rounded
              >
                {{ $t("Apply") }}
              </v-btn>
              <v-btn
                variant="outlined"
                class="w-100 w-sm-auto bg-white text-uppercase py-6"
                style="line-height: 0"
                @click="resetFilters"
                rounded
              >
                {{ $t("Reset") }}
              </v-btn>
            </div>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
  </v-form>
</template>

<script setup lang="ts">
import { getLocalizeValue } from "~/utils/functions";

const { t } = useI18n();
const { getActiveLanguageId } = useLanguage();

const props = defineProps<{
  categoryId?: number;
  brandId?: number;
  selectedBrandId?: number | null;
  selectedSize?: number[] | null;
  selectedColor?: number[] | null;
  selectedOrderBy?: string | null;
  selectedDiscountRange?: string | null;
}>();

const emit = defineEmits([
  "onApplyFilters",
  "onResetFilters",
  "onUpdateBrand",
  "onUpdateSize",
  "onUpdateColor",
  "onUpdateOrderBy",
  "onUpdateDiscountRange",
]);

const categoryStore = useProductCategoryStore();
const brandStore = useBrandStore();
const websiteStore = useWebsiteStore();

const orderList = computed(() => [
  {
    name: t("Price Low to High"),
    id: "Price Low to High",
  },
  {
    name: t("Price High to Low"),
    id: "Price High to Low",
  },
]);

const discountRangeOptions = computed(() => [
  { name: t("5% - 15%"), value: "5-15" },
  { name: t("15% - 25%"), value: "15-25" },
  { name: t("25% - 35%"), value: "25-35" },
  { name: t("35% - 45%"), value: "35-45" },
  { name: t("45% - 55%"), value: "45-55" },
  { name: t("55% - 65%"), value: "55-65" },
  { name: t("65% - 75%"), value: "65-75" },
  { name: t("75% - 80%"), value: "75-80" },
  { name: `5% ${t("or more")}`, value: "5" },
  { name: `10% ${t("or more")}`, value: "10" },
  { name: `20% ${t("or more")}`, value: "20" },
  { name: `30% ${t("or more")}`, value: "30" },
  { name: `40% ${t("or more")}`, value: "40" },
  { name: `50% ${t("or more")}`, value: "50" },
  { name: `60% ${t("or more")}`, value: "60" },
  { name: `70% ${t("or more")}`, value: "70" },
]);

const form = ref({
  brandId: props.selectedBrandId || null,
  size: props.selectedSize || [],
  color: props.selectedColor || [],
  orderBy: props.selectedOrderBy || null,
  discountRange: props.selectedDiscountRange || null,
});

const applyFilters = () => {
  categoryStore.currentPage = 1;

  // Emit current form values to parent
  emit("onUpdateBrand", form.value.brandId);
  emit("onUpdateSize", form.value.size);
  emit("onUpdateColor", form.value.color);
  emit("onUpdateOrderBy", form.value.orderBy);
  emit("onUpdateDiscountRange", form.value.discountRange);

  // Trigger apply filters
  emit("onApplyFilters");
};

const colors = computed(() => {
  return websiteStore?.attributeValues
    .filter((item: any) => item?.attribute_slug === "color")
    ?.map((item) => {
      item.attribute_value_name = getLocalizeValue(
        getActiveLanguageId(),
        item.locale,
        item.attribute_value_name
      );
      return item;
    });
});

const sizes = computed(() => {
  return websiteStore.attributeValues
    .filter((item: any) => item?.attribute_slug === "size")
    ?.map((item) => {
      item.attribute_value_name = getLocalizeValue(
        getActiveLanguageId(),
        item.locale,
        item.attribute_value_name
      );
      return item;
    });
});

const resetFilters = () => {
  categoryStore.currentPage = 1;

  form.value.brandId = null;
  form.value.size = [];
  form.value.color = [];
  form.value.orderBy = null;
  form.value.discountRange = null;

  // Emit reset values to parent
  emit("onUpdateBrand", null);
  emit("onUpdateSize", []);
  emit("onUpdateColor", []);
  emit("onUpdateOrderBy", null);
  emit("onUpdateDiscountRange", null);

  emit("onResetFilters");
};

const onUpdateBrandMenu = async (open: any) => {
  if (open && brandStore.brands?.length === 0) {
    const brands = await brandStore.getBrands();
    if (brands.isSuccess) {
      brandStore.brands = brands.data;
    }
  }
};

const onUpdateAttributeValues = async (open: any) => {
  if (open) {
    let values = null;

    if (props.categoryId) {
      values = await websiteStore.getAttributeValues(props.categoryId);
    } else {
      values = await websiteStore.getAttributeValuesByBrand(props.brandId);
    }

    if (values.isSuccess) {
      websiteStore.attributeValues = values.data.sort((a, b) =>
        a?.attribute_value_name.localeCompare(b.attribute_value_name)
      );
    } else {
      websiteStore.attributeValues = [];
    }
  }
};



// Watch for prop changes to update form (when URL changes)
watch(() => props.selectedBrandId, (newVal) => {
  form.value.brandId = newVal || null;
});

watch(() => props.selectedSize, (newVal) => {
  form.value.size = newVal || [];
}, { deep: true });

watch(() => props.selectedColor, (newVal) => {
  form.value.color = newVal || [];
}, { deep: true });

watch(() => props.selectedOrderBy, (newVal) => {
  form.value.orderBy = newVal || null;
});

watch(() => props.selectedDiscountRange, (newVal) => {
  form.value.discountRange = newVal || null;
});

// Load brands and attribute values on component mount
onMounted(async () => {
  // Load brands if not already loaded
  if (brandStore.brands?.length === 0) {
    const brands = await brandStore.getBrands();
    if (brands.isSuccess) {
      brandStore.brands = brands.data;
    }
  }

  // Load attribute values if we have filters from URL
  if ((props.selectedSize && props.selectedSize.length > 0) ||
      (props.selectedColor && props.selectedColor.length > 0)) {
    let values: any = null;

    if (props.categoryId) {
      values = await websiteStore.getAttributeValues(props.categoryId);
    } else if (props.brandId) {
      values = await websiteStore.getAttributeValuesByBrand(props.brandId);
    }

    if (values?.isSuccess) {
      websiteStore.attributeValues = values.data.sort((a: any, b: any) =>
        a?.attribute_value_name.localeCompare(b.attribute_value_name)
      );
    }
  }
});

const closeAllSelects = () => {};

// Add scroll event listener on component mount
onMounted(() => {
  window.addEventListener("scroll", closeAllSelects);
});
</script>
