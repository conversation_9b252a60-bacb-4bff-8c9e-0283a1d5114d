<template>
  <div>
    <v-form @submit.prevent="submitApplication" v-model="valid">
      <!-- Step Indicator -->
      <v-stepper v-model="currentStep" class="mb-6" elevation="0">
        <v-stepper-header>
          <v-stepper-item 
            :complete="currentStep > 1" 
            :value="1" 
            title="Account Info"
          />
          <v-divider />
          <v-stepper-item 
            :complete="currentStep > 2" 
            :value="2" 
            title="Affiliate Details"
          />
          <v-divider />
          <v-stepper-item 
            :value="3" 
            title="Review & Submit"
          />
        </v-stepper-header>
      </v-stepper>

      <!-- Step 1: Account Information -->
      <div v-if="currentStep === 1">
        <h3 class="text-h6 font-weight-semibold mb-4">{{ $t('Account Information') }}</h3>
        
        <v-row>
          <v-col cols="12" md="6">
            <v-label class="text-subtitle-1 font-weight-semibold pb-2">
              {{ $t('First Name') }} <span class="text-error">*</span>
            </v-label>
            <v-text-field
              v-model="form.firstName"
              :rules="nameRules"
              required
              hide-details="auto"
              class="mb-4"
            />
          </v-col>
          <v-col cols="12" md="6">
            <v-label class="text-subtitle-1 font-weight-semibold pb-2">
              {{ $t('Last Name') }} <span class="text-error">*</span>
            </v-label>
            <v-text-field
              v-model="form.lastName"
              :rules="nameRules"
              required
              hide-details="auto"
              class="mb-4"
            />
          </v-col>
        </v-row>

        <v-label class="text-subtitle-1 font-weight-semibold pb-2">
          {{ $t('Email Address') }} <span class="text-error">*</span>
        </v-label>
        <v-text-field
          v-model="form.email"
          :rules="emailRules"
          type="email"
          required
          hide-details="auto"
          class="mb-4"
        />

        <v-label class="text-subtitle-1 font-weight-semibold pb-2">
          {{ $t('Phone Number') }} <span class="text-error">*</span>
        </v-label>
        <v-text-field
          v-model="form.phone"
          :rules="phoneRules"
          required
          hide-details="auto"
          class="mb-4"
        />

        <v-label class="text-subtitle-1 font-weight-semibold pb-2">
          {{ $t('Password') }} <span class="text-error">*</span>
        </v-label>
        <v-text-field
          v-model="form.password"
          :rules="passwordRules"
          :type="passwordVisible ? 'text' : 'password'"
          :append-inner-icon="passwordVisible ? 'i-mdi:eye-off' : 'i-mdi:eye'"
          @click:append-inner="passwordVisible = !passwordVisible"
          required
          hide-details="auto"
          class="mb-4"
        />

        <v-label class="text-subtitle-1 font-weight-semibold pb-2">
          {{ $t('Confirm Password') }} <span class="text-error">*</span>
        </v-label>
        <v-text-field
          v-model="form.confirmPassword"
          :rules="confirmPasswordRules"
          :type="confirmPasswordVisible ? 'text' : 'password'"
          :append-inner-icon="confirmPasswordVisible ? 'i-mdi:eye-off' : 'i-mdi:eye'"
          @click:append-inner="confirmPasswordVisible = !confirmPasswordVisible"
          required
          hide-details="auto"
          class="mb-6"
        />
      </div>

      <!-- Step 2: Affiliate Details -->
      <div v-if="currentStep === 2">
        <h3 class="text-h6 font-weight-semibold mb-4">{{ $t('Affiliate Information') }}</h3>
        <v-label class="text-subtitle-1 font-weight-semibold pb-2">
          {{ $t('Affiliate Code') }} <span class="text-error">*</span>
        </v-label>
        <v-text-field
          v-model="form.affiliateCode"
          required
          hide-details="auto"
          class="mb-4"
        />
        <v-label class="text-subtitle-1 font-weight-semibold pb-2">
          {{ $t('Commission Type') }} <span class="text-error">*</span>
        </v-label>
        <v-select
          v-model="form.commissionType"
          :items="commissionTypeOptions"
          :rules="requiredRules"
          required
          hide-details="auto"
          class="mb-4"
        />

        <v-label class="text-subtitle-1 font-weight-semibold pb-2">
          {{ $t('Requested Commission Rate') }} <span class="text-error">*</span>
        </v-label>
        <v-text-field
          v-model.number="form.commissionRate"
          :rules="commissionRateRules"
          type="number"
          :suffix="form.commissionType === 'percentage' ? '%' : '$'"
          required
          hide-details="auto"
          class="mb-4"
        />

        <v-label class="text-subtitle-1 font-weight-semibold pb-2">
          {{ $t('Payment Method') }}
        </v-label>
        <v-select
          v-model="form.paymentMethod"
          :items="paymentMethodOptions"
          hide-details="auto"
          placeholder="Select payment method"
          class="mb-4"
        />

        <v-label class="text-subtitle-1 font-weight-semibold pb-2">
          {{ $t('Website URL') }}
        </v-label>
        <v-text-field
          v-model="form.websiteUrl"
          :rules="urlRules"
          placeholder="https://your-website.com"
          hide-details="auto"
          class="mb-4"
        />

        <v-label class="text-subtitle-1 font-weight-semibold pb-2">
          {{ $t('Bio / About You') }}
        </v-label>
        <v-textarea
          v-model="form.bio"
          :rules="bioRules"
          rows="4"
          placeholder="Tell us about yourself, your audience, and how you plan to promote our products..."
          hide-details="auto"
          class="mb-6"
        />
      </div>

      <!-- Step 3: Review & Submit -->
      <div v-if="currentStep === 3">
        <h3 class="text-h6 font-weight-semibold mb-4">{{ $t('Review Your Application') }}</h3>
        
        <v-card variant="outlined" class="pa-4 mb-4">
          <h4 class="text-subtitle-1 font-weight-semibold mb-2">{{ $t('Personal Information') }}</h4>
          <p><strong>{{ $t('Name') }}:</strong> {{ form.firstName }} {{ form.lastName }}</p>
          <p><strong>{{ $t('Email') }}:</strong> {{ form.email }}</p>
          <p><strong>{{ $t('Phone') }}:</strong> {{ form.phone }}</p>
        </v-card>

        <v-card variant="outlined" class="pa-4 mb-4">
          <h4 class="text-subtitle-1 font-weight-semibold mb-2">{{ $t('Affiliate Details') }}</h4>
          <p><strong>{{ $t('Commission Type') }}:</strong> {{ form.commissionType }}</p>
          <p><strong>{{ $t('Commission Rate') }}:</strong> {{ form.commissionRate }}{{ form.commissionType === 'percentage' ? '%' : '$' }}</p>
          <p><strong>{{ $t('Payment Method') }}:</strong> {{ form.paymentMethod || 'Not specified' }}</p>
          <p v-if="form.websiteUrl"><strong>{{ $t('Website') }}:</strong> {{ form.websiteUrl }}</p>
          <p v-if="form.bio"><strong>{{ $t('Bio') }}:</strong> {{ form.bio }}</p>
        </v-card>

        <v-checkbox
          v-model="form.agreeToTerms"
          :rules="termsRules"
          required
          class="mb-4"
        >
          <template #label>
            <span>
              {{ $t('I agree to the') }}
              <a href="#" class="text-primary">{{ $t('Terms and Conditions') }}</a>
              {{ $t('and') }}
              <a href="#" class="text-primary">{{ $t('Affiliate Agreement') }}</a>
            </span>
          </template>
        </v-checkbox>
      </div>

      <!-- Navigation Buttons -->
      <div class="d-flex justify-space-between mt-6">
        <v-btn
          v-if="currentStep > 1"
          variant="outlined"
          @click="currentStep--"
          :disabled="loading"
        >
          {{ $t('Previous') }}
        </v-btn>
        <v-spacer v-else />

        <v-btn
          v-if="currentStep < 3"
          color="primary"
          @click="nextStep"
          :disabled="!isCurrentStepValid"
        >
          {{ $t('Next') }}
        </v-btn>
        
        <v-btn
          v-else
          color="primary"
          type="submit"
          :loading="loading"
          :disabled="!valid || !form.agreeToTerms"
        >
          {{ $t('Submit Application') }}
        </v-btn>
      </div>
    </v-form>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { showSnackbarResponse } from "../../utils/functions";
import { fetchApiData } from "../../utils/apiHelpers";

const { t } = useI18n();
const authStore = useAuthStore();
const localePath = useLocalePath();

const loading = ref(false);
const valid = ref(false);
const currentStep = ref(1);
const passwordVisible = ref(false);
const confirmPasswordVisible = ref(false);

const form = ref({
  firstName: '',
  lastName: '',
  email: '',
  phone: '',
  password: '',
  confirmPassword: '',
  affiliateCode: '',
  commissionType: 'percentage',
  commissionRate: 10,
  paymentMethod: '',
  websiteUrl: '',
  bio: '',
  agreeToTerms: false,
});

// Options
const commissionTypeOptions = [
  { title: t('Percentage'), value: 'percentage' },
  { title: t('Fixed Amount'), value: 'fixed' },
];

const paymentMethodOptions = [
  { title: t('Bank Transfer'), value: 'bank_transfer' },
  { title: t('PayPal'), value: 'paypal' },
  { title: t('Stripe'), value: 'stripe' },
  { title: t('Other'), value: 'other' },
];

// Validation Rules
const nameRules = computed(() => [
  (v: string) => !!v || t('This field is required'),
  (v: string) => v.length >= 2 || t('Must be at least 2 characters'),
]);

const emailRules = computed(() => [
  (v: string) => !!v || t('Email is required'),
  (v: string) => /.+@.+\..+/.test(v) || t('Email must be valid'),
]);

const phoneRules = computed(() => [
  (v: string) => !!v || t('Phone number is required'),
]);

const passwordRules = computed(() => [
  (v: string) => !!v || t('Password is required'),
  (v: string) => v.length >= 8 || t('Password must be at least 8 characters'),
]);

const confirmPasswordRules = computed(() => [
  (v: string) => !!v || t('Please confirm your password'),
  (v: string) => v === form.value.password || t('Passwords do not match'),
]);

const requiredRules = computed(() => [
  (v: any) => !!v || t('This field is required'),
]);

const commissionRateRules = computed(() => [
  (v: number) => !!v || t('Commission rate is required'),
  (v: number) => v > 0 || t('Commission rate must be greater than 0'),
  (v: number) => form.value.commissionType === 'percentage' ? v <= 50 : v <= 1000 || t('Invalid commission rate'),
]);

const urlRules = computed(() => [
  (v: string) => !v || /^https?:\/\/.+/.test(v) || t('Must be a valid URL'),
]);

const bioRules = computed(() => [
  (v: string) => !v || v.length <= 500 || t('Bio must be less than 500 characters'),
]);

const termsRules = computed(() => [
  (v: boolean) => !!v || t('You must agree to the terms and conditions'),
]);

const isCurrentStepValid = computed(() => {
  if (currentStep.value === 1) {
    return form.value.firstName && form.value.lastName && form.value.email && 
           form.value.phone && form.value.password && form.value.confirmPassword &&
           form.value.password === form.value.confirmPassword;
  }
  if (currentStep.value === 2) {
    return form.value.commissionType && form.value.commissionRate > 0;
  }
  return true;
});

const nextStep = () => {
  if (isCurrentStepValid.value) {
    currentStep.value++;
  }
};

const submitApplication = async () => {
  if (!valid.value || !form.value.agreeToTerms) return;

  loading.value = true;

  try {
    // First register the user
    const userRegistrationResponse = await authStore.registerUser({
      firstName: form.value.firstName,
      lastName: form.value.lastName,
      email: form.value.email,
      phone: form.value.phone,
      password: form.value.password,
      userTypeId: 2, // Register as customer first
    });

    if (!userRegistrationResponse.isSuccess) {
      showSnackbarResponse(userRegistrationResponse);
      return;
    }
    console.log("userRegistrationResponse:::", userRegistrationResponse);
    // Then create affiliate profile application
    const affiliateResponse = await fetchApiData('affiliate/register', {
      method: 'POST',
      body: {
        userId: userRegistrationResponse.data?.id,
        affiliateCode: form.value.affiliateCode,
        commissionType: form.value.commissionType,
        commissionRate: form.value.commissionRate,
        paymentMethod: form.value.paymentMethod,
        websiteUrl: form.value.websiteUrl,
        bio: form.value.bio,
      },
    });

    showSnackbarResponse({
      isSuccess: true,
      message: t('Application submitted successfully! We will review your application and contact you soon.'),
    });

    // Redirect to success page or login
    await navigateTo(localePath('/auth/affiliate-application-success'));

  } catch (error: any) {
    console.error('Affiliate registration error:', error);
    await showSnackbarResponse({
      isSuccess: false,
      message: error.response?._data?.message || t('Registration failed. Please try again.'),
    });
  } finally {
    loading.value = false;
  }
};

function onAffiliateCodeInput(val) {
  // Convert only letters a–z to uppercase
  console.log(val, "<<<,");
  form.affiliateCode = val.replace(/[a-z]/g, match => match.toUpperCase())
}
</script>

<style scoped>
.v-stepper {
  box-shadow: none !important;
}
</style>
