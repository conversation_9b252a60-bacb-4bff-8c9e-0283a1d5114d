<template>
  <IntersectWrapper :onIntersect="onIntersect">
    <section class="maxWidth py-7 py-md-5">
      <v-container>
        <v-row class="align-center">
          <v-col cols="12" md="2">
            <h3 class="text-center text-md-left">
              {{ $t("Also Available in") }}
            </h3>
          </v-col>
          <v-col cols="12" md="10">
            <div class="stores-slider-wrapper">
              <LazySwiper
                :slides-per-view="6"
                space-between="50"
                :loop="true"
                :modules="[SwiperAutoplay]"
                :breakpoints="breakPoints"
              >
                <LazySwiperSlide v-for="item in clients" :key="item.id">
                  <span
                    class="d-block"
                    style="cursor: default!important"
                  >
                    <NuxtImg
                      :src="item?.imageGallery?.imageUrl"
                      :alt="item?.title"
                      loading="lazy"
                      sizes="xs:100vw sm:100vw md:100vw lg:100vw xl:100vw"
                      format="webp"
                      width="150"
                      height="40"
                      class="h-100 mx-auto d-block"
                      fit="cover"
                      quality="85"
                    />
                </span>
                </LazySwiperSlide>
              </LazySwiper>
            </div>
          </v-col>
        </v-row>
      </v-container>
    </section>
  </IntersectWrapper>
</template>
<script setup lang="ts">
const localePath = useLocalePath();

const store = usePartnerInstagramStore();
const clients = computed(() => store.partners);
const breakPoints = ref({
  320: {
    slidesPerView: 1,
  },
  576: {
    slidesPerView: 3,
    centeredSlides: true,
  },
  1200: {
    slidesPerView: 5,
  },
});

const onIntersect = async () => {
  if (store.partners.length === 0) {
    await store.getHomePartnerInstagram("PARTNER");
  }
};
</script>

<style lang="scss" scoped>
.stores-slider-wrapper {
  .swiper-wrapper {
    align-items: center;
  }
}
</style>