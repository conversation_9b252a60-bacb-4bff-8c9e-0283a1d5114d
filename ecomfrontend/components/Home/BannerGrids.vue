<template>
  <v-container fluid>
    <div v-if="loading">
      <div class="home-banners-wrapper">
        <div class="banner-item">
          <v-skeleton-loader type="image, article"></v-skeleton-loader>
        </div>
        <div class="banner-item">
          <v-skeleton-loader type="image, article"></v-skeleton-loader>
        </div>
        <div class="banner-item">
          <v-skeleton-loader type="image, article"></v-skeleton-loader>
        </div>
        <div class="banner-item">
          <v-skeleton-loader type="image, article"></v-skeleton-loader>
        </div>
        <div class="banner-item">
          <v-skeleton-loader type="image, article"></v-skeleton-loader>
        </div>
      </div>
    </div>
    <section class="home-banners-wrapper" v-else>
      <v-card
        v-for="banner in bannersData"
        :key="banner.id"
        class="banner-item"
        :to="localePath(banner?.redirectLink)"
      >
        <NuxtImg
          :src="replaceMediaDomain(banner?.imageUrl ?? defaultBannerImage) || banner?.imageUrl || defaultBannerImage"
          :alt="banner?.title"
          loading="lazy"
          format="jpg"
          width="920"
          height="450"
          class="w-100 h-100"
          fit="cover"
          quality="85"
        />

        <v-card-text class="banner-content pa-0">
          <v-card-title
            class="text-white text-h4 text-md-h1 font-weight-bold banner-title px-0 pt-2"
            v-text="banner.title"
            style="line-height: 1"
          />
          <p class="text-white text-body-1 font-weight-normal banner-subtitle">
            {{ banner?.subTitle }}
          </p>
        </v-card-text>
      </v-card>
    </section>
  </v-container>
</template>
<script setup lang="ts">
import { computed, ref } from 'vue';
import { useMediaType } from '../../composables/useMediaType';

const props = defineProps<{
  websiteStore?: any;
}>();

const localePath = useLocalePath();
const {replaceMediaDomain} = useMediaType();
const defaultBannerImage = ref(
  "https://pantoneclos3bucket.s3.ap-south-1.amazonaws.com/pantoneclo-img/final-bluer-ed-1.webp"
);

const loading = ref(
  props.websiteStore.homeCategoryBanners?.length > 0 ? false : true
);
const bannersData = computed(() => props.websiteStore.homeCategoryBanners);
</script>
<style lang="scss" scoped>
.home-banners-wrapper {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(15%, 1fr));
  gap: 15px;
  .banner-item {
    position: relative;
    // &::after {
    //   content: "";
    //   position: absolute;
    //   bottom: 0;
    //   left: 0;
    //   width: 100%;
    //   height: 30%;
    //   background: linear-gradient(
    //     to bottom,
    //     rgba(0, 0, 0, 0) 10%,
    //     rgba(0, 0, 0, 0.5)
    //   );
    //   pointer-events: none;
    // }
    
    .banner-content {
      position: absolute;
      bottom: 4%;
      left: 5%;
      width: 90%;
      z-index: 1;
      .banner-title {
        padding-left: 1px !important;
        padding-right: 5px !important;
        text-shadow: 2px 2px 3px rgb(0, 0, 0, 0.6);
      }
      .banner-subtitle {
        text-shadow: 2px 2px 3px rgb(0, 0, 0, 0.7);
        letter-spacing: 2px !important;
      }
      // width: max-content;
      // background: linear-gradient(
      //   to left,
      //   rgba(0, 0, 0, 0) 10%,
      //   rgba(0, 0, 0, 0.5)
      // );
      // background: rgb(0, 0, 0, 0.3);
      // border-radius: 10px;
      // padding: 5px !important;
    }
  }
  .banner-item:nth-child(1),
  .banner-item:nth-child(2) {
    grid-column: span 3;
    img {
      aspect-ratio: 16 / 8;
      object-fit: cover;
    }
  }
  .banner-item:nth-child(3),
  .banner-item:nth-child(4),
  .banner-item:nth-child(5) {
    grid-column: span 2;
    img {
      aspect-ratio: 16 / 11.5;
      object-fit: cover;
    }
  }
}
@media (max-width: 767.98px) {
  .home-banners-wrapper {
    grid-template-columns: repeat(auto-fit, minmax(10%, 1fr));

    .banner-item {
      img {
        height: 100%;
      }
    }

    .banner-item:nth-child(1) {
      grid-column: span 6;
    }

    .banner-item:nth-child(2),
    .banner-item:nth-child(3),
    .banner-item:nth-child(4),
    .banner-item:nth-child(5) {
      grid-column: span 3;
    }

    .banner-title {
      padding: 0;
      line-height: 1.3rem !important;
    }
  }
}

</style>
