<template>
  <section class="hero-slider-wrapper">
    <div v-if="loading">
      <v-skeleton-loader type="image, image, image"></v-skeleton-loader>
    </div>
    <nuxt-link v-else to="/event/flash-deals">
      <NuxtImg
        :src="replaceMediaDomain(bannerData) || bannerData"
        preload
        sizes="xs:100vw sm:100vw md:100vw lg:100vw xl:100vw"
        fetchPriority="high"
        alt="Pantoneclo-Hero-Banner"
        format="webp"
        class="w-100 h-100"
        fit="cover"
        quality="85"
      />
      <span class="custom-button">
        <transition name="fade">{{$t('Shop Now')}}</transition>
      </span>
    </nuxt-link>
  </section>
</template>
<script lang="ts" setup>
import { computed, ref } from "vue";
import { useCurrentHost } from "../../composables/useCurrentHost";
import { useLanguage } from "../../composables/useLanguage";
import { useMediaType } from "../../composables/useMediaType";

const props = defineProps<{
  websiteStore?: any;
}>();

const { replaceMediaDomain } = useMediaType();
const { getActiveLanguageId } = useLanguage();
const { getDomaCountryId, getDomaCountryCode } = useCurrentHost();

const countryId = getDomaCountryId();
const countryCode = getDomaCountryCode();

const loading = ref(!props?.websiteStore?.mainBanner);
const bannerData = computed(() => props?.websiteStore?.mainBanner);

const shopNowLinkList: Record<string, string> = {
  SI: "/collection/valentine-special-offer-2025-si",
  AT: "/collection/valentine-special-offer-2025-at",
  BD: "/collection/valentine-special-offer-2025",
  BG: "/collection/valentine-special-offer-2025-bg",
  HR: "/collection/valentine-special-offer-2025-hr",
  CZ: "/collection/valentine-special-offer-2025-cz",
  DE: "/collection/valentine-special-offer-2025-de",
  GR: "/collection/valentine-special-offer-2025-gr",
  HU: "/collection/valentine-special-offer-2025-hu",
  IN: "/collection/valentine-special-offer-2025-in",
  IT: "/collection/valentine-special-offer-2025-it",
  LT: "/collection/valentine-special-offer-2025-lt",
  PL: "/collection/valentine-special-offer-2025-pl",
  PT: "/collection/valentine-special-offer-2025-pt",
  RO: "/collection/valentine-special-offer-2025-ro",
  SK: "/collection/valentine-special-offer-2025-sk",
};

const shopNowLink = computed(
  () =>
    shopNowLinkList[countryCode] || "/collection/valentine-special-offer-2025"
);
</script>
<style scoped>
.hero-slider-wrapper {
  position: relative;
  overflow: hidden;
}

.custom-button {
  font-family: "Patua One", sans-serif;
  position: absolute;
  bottom: 43%;
  left: 33%;
  background-color: rgba(0, 0, 0);
  color: #fff;
  padding: 10px 20px;
  border: none;
  border-radius: 25px;
  font-size: 1.2rem;
  cursor: pointer;
  transition: background-color 0.3s ease, transform 0.3s ease;
  text-decoration: none;
  text-transform: uppercase;
}
/* Media Queries for Responsive Design */
@media (min-width: 1904px) {
  .custom-button {
    bottom: 43%;
    left: 33%;
    font-size: 1.5rem;
    padding: 8px 20px;
  }
}
@media (max-width: 1904px) {
  .custom-button {
    bottom: 43%;
    left: 32%;
    font-size: 1.2rem;
    padding: 8px 20px;
  }
}
@media (max-width: 1264px) {
  .custom-button {
    bottom: 42%;
    left: 31%;
    font-size: 1.1rem;
    padding: 8px 18px;
  }
}

@media (max-width: 960px) {
  .custom-button {
    bottom: 40%;
    left: 30%;
    font-size: 1.2rem;
    padding: 4px 16px;
  }
}

@media (max-width: 768px) {
  .custom-button {
    bottom: 40%;
    left: 30%;
    font-size: 0.9rem;
    padding: 3px 12px;
  }
}

@media (max-width: 600px) {
  .custom-button {
    bottom: 42%;
    left: 32%;
    font-size: 8px;
    padding: 3px 6px;
  }
}
@media (max-width: 431px) {
  .custom-button {
    bottom: 42%;
    left: 30%;
    font-size: 8px;
    padding: 3px 6px;
  }
}
@media (max-width: 376px) {
  .custom-button {
    bottom: 38%;
    left: 29%;
    font-size: 8px;
    padding: 3px 6px;
  }
}
.custom-button:hover {
  background-color: rgba(0, 0, 0, 0.9);
  transform: translateY(-3px);
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s;
}

.fade-enter,
.fade-leave-to {
  opacity: 0;
}
</style>
