<template>
  <IntersectWrapper :onIntersect="onIntersect">
    <section class="instagram-section-wrapper py-0 py-md-4">
      <v-container fluid>
        <v-row class="align-center">
          <v-col cols="12" md="2" class="py-0">
            <div class="text-center">
              <div
                class="d-flex flex-row flex-md-column ga-2 align-center justify-center"
              >
                <a
                  :href="
                    instagramData[0]?.redirectLink ||
                    'https://www.instagram.com/pantoneclo'
                  "
                  class="d-block mt-2 insta-logo"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <!-- <NuxtImg
                    src="https://pantoneclos3bucket.s3.ap-south-1.amazonaws.com/pantoneclo-img/insta-logo.png"
                    alt="Pantoneclo Instagram"
                    placeholder
                    loading="lazy"
                    sizes="xs:100vw sm:100vw md:100vw lg:100vw xl:100vw"
                    format="avif"
                    width="256"
                    height="69"
                    class="h-100 w-100"
                    fit="cover"
                  /> -->
                  <v-img src="/pantoneclo-Follow-us.jpg" alt="instagram-logo" aspect-ratio="16/9" />
                </a>
                <!-- <h2 class="text-uppercase pantoneclo-text">Pantoneclo</h2> -->
              </div>
            </div>
          </v-col>
          <v-col cols="12" md="10">
            <div class="position-relative py-2">
              <LazySwiper
                :modules="modules"
                navigation
                :autoplay="{ delay: 3000}"
                class="position-static"
                :creative-effect="effect"
                :space-between="20"
                :breakpoints="breakPoints"
              >
                <LazySwiperSlide
                  v-for="(data, index) in instagramData"
                  :key="index"
                >
                  <v-hover>
                    <template #default="{ isHovering, props }">
                      <v-card
                        v-bind="props"
                        :class="isHovering ? 'insta-card-hover' : undefined"
                        link
                        :href="data?.redirectLink"
                        target="_blank"
                        class="position-relative border border-primary rounded-xl overflow-hidden"
                      >
                        <NuxtImg
                          :src="replaceMediaDomain(data?.imageGallery?.imageUrl) || data?.imageGallery?.imageUrl"
                          :alt="data?.title"
                          loading="lazy"
                          width="263"
                          height="300"
                          quality="50"
                          class="h-100 w-100 d-block"
                        />

                        <div class="position-absolute text-content">
                          <v-icon icon="i-mdi:magnify" size="40" color="#fff" />
                        </div>
                      </v-card>
                    </template>
                  </v-hover>
                </LazySwiperSlide>
              </LazySwiper>
            </div>
          </v-col>
        </v-row>
      </v-container>
    </section>
  </IntersectWrapper>
</template>
<script setup lang="ts">
import { Autoplay, Navigation, EffectCreative } from 'swiper/modules'
import { useMediaType } from '../../composables/useMediaType';
const store = usePartnerInstagramStore();
const instagramData = computed(() => store.instagramData);

const { replaceMediaDomain } = useMediaType();
const onIntersect = async () => {
  if (store.instagramData.length === 0) {
    await store.getHomePartnerInstagram("INSTAGRAM");
  }
};

const modules = [Autoplay, EffectCreative, Navigation];

const effect = {
  prev: {
    shadow: false,
    translate: ["-20%", 0, -1],
  },
  next: {
    translate: ["100%", 0, 0],
  },
};

const breakPoints = {
  320: {
    loop: true,
    spaceBetween: 5,
    watchSlidesProgress: true,
    slidesPerView: 2.5,
  },
  576: {
    loop: true,
    spaceBetween: 5,
    watchSlidesProgress: true,
    slidesPerView: 4.5,
  },
  1200: {
    slidesPerView: 5.5,
    spaceBetween: 7.5,
  },
};
</script>

<style lang="scss" scoped>
.instagram-section-wrapper {
  .insta-card-hover {
    &::before {
      position: absolute;
      content: "";
      width: 101%;
      left: 0;
      top: 0;
      right: 0;
      bottom: 0;
      background-color: rgb(0, 0, 0, 0.4);
      z-index: 1;
    }

    .text-content {
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 2;
    }
  }
}
.pantoneclo-text {
  font-family: "Patua One", sans-serif;
  letter-spacing: 4px;
}

.insta-logo {
  width: 100%;
  @media (max-width: 992px) {
    width: 40%;
  }
}
</style>
