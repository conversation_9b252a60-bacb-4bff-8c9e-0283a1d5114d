<template>
  <section class="slide-wrapper">
    <Swiper
      ref="sliderRef"
      :grab-cursor="false"
      :modules="modules"
      :loop="true"
      :autoplay="{
        delay: 8950,

        disableOnInteraction: false,
      }"
      navigation
      lazy-preloader-class="swiper-lazy-preloader swiper-lazy-preloader-white"
      :breakpoints="{
        '1400': {
          slidesPerView: 1,
          spaceBetween: 1,
        },
      }"
      @swiper="onSwiperInit"
      @mouseenter="pauseAutoplay"
      @mouseleave="resumeAutoplay"
    >
      <SwiperSlide 
        v-for="slide in props?.websiteStore?.homepageHeroSlider" 
        :key="slide?.sliderId"
        class="position-relative slide-box"
      >
        <nuxt-link class="d-block slide-link" :to="slide?.sliderMediaCustomUrl">
          <div v-if="slide?.sliderMediaType === 'image'">
            <NuxtImg
              :src="replaceMediaDomain(slide?.localeMediaUrl ?? slide?.globalMediaUrl) || slide?.localeMediaUrl || slide?.globalMediaUrl"
              :alt="slide?.sliderName"
              loading="lazy"
              sizes="xs:100vw sm:100vw md:100vw"
              format="webp"
              width="1800"
              height="817"
              class="h-100 w-100"
              quality="85"
            />
          </div>
          <div v-if="slide?.sliderMediaType === 'video'">
            <video class="w-100 h-100" autoplay muted loop playsinline preload="auto" ref="videoRef">
              <source 
                :src="replaceMediaDomain(slide?.localeMediaUrl ?? slide?.globalMediaUrl) || slide?.localeMediaUrl || slide?.globalMediaUrl" 
                :type="slide?.localeMimeType ?? slide?.globalMimeType"
              />
            </video>
          </div>
          <div class="content d-flex flex-column align-center ga-1 ga-md-3 w-75 w-md-25">
            <h1 v-if="slide?.sliderCountryLocale?.title || slide?.sliderMediaTitle" class="title text-h4 text-md-h1 text-center font-weight-bold">{{ slide?.sliderCountryLocale?.title || slide?.sliderMediaTitle }}</h1>
            <p v-if="slide?.sliderCountryLocale?.description || slide?.sliderMediaDescription" class="description text-center text-body-2 text-md-h6 font-weight-medium">{{ slide?.sliderCountryLocale?.description || slide?.sliderMediaDescription }}</p>
            <v-hover v-slot="{ isHovering, props }">
              <v-btn
                v-bind="props"
                v-if="slide?.sliderCountryLocale?.buttonText || slide?.sliderMediaButtonText"
                :to="slide?.sliderMediaButtonLink"
                color="#FA896B"
                variant="flat"
                rounded
                density="compact"
                class="button-link w-33 px-1 py-0 py-md-2 px-md-2 text-body-2 text-md-h5"
                :class="isHovering ? 'hover-shadow' : ''"
                style="width: max-content !important;"
              >
                {{ slide?.sliderCountryLocale?.buttonText || slide?.sliderMediaButtonText }}
              </v-btn>
            </v-hover>
          </div>
        </nuxt-link>
      </SwiperSlide>
    </Swiper>
  </section>
</template>

<script setup lang="ts">
import type { Swiper as SwiperClass } from 'swiper';
import { Navigation, Autoplay } from "swiper/modules";
import { onMounted, ref, watch } from "vue";
import { useMediaType } from '../../composables/useMediaType';
import { useRoute } from 'nuxt/app';

const props = defineProps({
  websiteStore: null,
})

const route = useRoute();
const {replaceMediaDomain} = useMediaType();
const modules = [Navigation, Autoplay];
const sliderRef = ref();
const videoRef = ref([]);

const onSwiperInit = (swiper: SwiperClass) => {
  sliderRef.value = swiper;
};

const playFirstVideo = () => {
  if (videoRef?.value?.[0]?.paused) {
    videoRef.value[0].play().catch((e) => {
      console.warn('Autoplay error:', e);
    });
  }
};

watch(
  () => route.path,
  (newVal, oldVal) => {
    playFirstVideo(); // Route change
  }
);

const pauseAutoplay = () => {
  // sliderRef.value?.autoplay?.stop();
};

const resumeAutoplay = () => {
  // sliderRef.value?.autoplay?.start();
};

onMounted(() => {
  playFirstVideo(); // Initial load
});
</script>

<style scoped lang="scss">
.slide-wrapper {
  overflow: hidden;
}
.content{
 position: absolute;
 bottom: 10%;
 left: 50%;
 transform: translateX(-50%);
 .title{
  color: #494949 !important;
 }
 .description{
  color: #2e2e2e;
 }
 .button-link{

 }
}
.slide-box{
  a{
    color: black;
    text-decoration: none;
  }
}

.hover-shadow {
  box-shadow: 0 4px 12px rgba(27, 27, 27, 0.45);
  transition: box-shadow 0.3s ease;
}
.slide-link{
  @media screen and (max-width: 600px) {
    img, video{
      height: 650px;
      object-fit: cover;
      aspect-ratio: 4/5;
    }
  }
}
</style>
