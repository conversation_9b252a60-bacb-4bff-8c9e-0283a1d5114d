<template>
  <div>
    <div class="position-relative main-header-wrapper">
      <VAppBar order="1">
        <template #extension>
          <span v-if="shippingChargeFree" style="font-size: 0.9rem">
            {{ $t("Free delivery on purchase over") }}
            {{
              `${shippingChargeFree + " " + globals?.currency?.currency}`
            }}</span
          >
          <span v-if="headerContent && headerContent?.title"
            >, {{ headerContent?.title }}</span
          >
        </template>
        <div class="v-toolbar__content px-0">
          <LazyLayoutHeaderNavigationSm class="d-block d-md-none" />
          <div class="header-logo-wrapper">
            <NuxtLink :to="localePath('/')" class="d-flex">
              <NuxtImg
                :src="replaceMediaDomain(`${staticImageUrl}logo-white.png`) || `${staticImageUrl}logo-white.png`"
                alt="Pantoneclo"
                preload
                sizes="xs:100vw sm:100vw md:100vw lg:100vw"
                format="webp"
                width="200"
                height="38"
                class="h-100 hidden-sm-and-down"
                fit="cover"
                quality="85"
              />

              <NuxtImg
                :src="replaceMediaDomain(`${staticImageUrl}logo.png`) || `${staticImageUrl}logo.png`"
                alt="Pantoneclo"
                preload
                sizes="sm:100vw"
                format="webp"
                width="150"
                height="29"
                class="h-100 hidden-md-and-up"
                fit="cover"
                quality="85"
              />
            </NuxtLink>
          </div>
          <v-spacer />
          <div class="hidden-sm-and-down">
            <LazyLayoutHeaderNavigationLg />
          </div>
          <v-spacer />
          <!-- <nuxt-link
            to="/event/flash-deals"
            class="text-decoration-none blink_me me-2 hidden-md-and-up"
            >{{ $t("Deals") }}🗲
          </nuxt-link> -->
          <v-sheet>
            <LazyLayoutHeaderSearchbar :country-code="globals?.code" />
          </v-sheet>
          <v-btn
            icon
            variant="elevated"
            :elevation="0"
            color="primary"
            @click.stop="cartDrawer = !cartDrawer"
            class="custom-hover-primary header-icon-bg ml-3"
            size="small"
            name="cart-btn"
            aria-label="Cart"
          >
            <v-badge
              v-if="cart?.length"
              color="error"
              :content="cart?.length"
              :floating="true"
            >
              <v-icon color="#fff" class="i-mdi:cart-outline" size="18" />
            </v-badge>
            <v-icon v-else color="#fff" class="i-mdi:cart-outline"></v-icon>
          </v-btn>
          <v-btn
            icon
            variant="elevated"
            :elevation="0"
            color="primary"
            :to="localePath('/wishlists')"
            class="custom-hover-primary header-icon-bg ml-3 hidden-sm-and-down"
            size="small"
            name="wishlist-btn"
            aria-label="Wishlist"
          >
            <v-badge
              v-if="wishlistCount"
              color="error"
              :content="wishlistCount"
              :floating="true"
            >
              <v-icon color="#fff" class="i-mdi:heart" size="18"></v-icon>
            </v-badge>
            <v-icon
              v-else
              color="#fff"
              class="i-mdi:heart-outline"
              size="18"
            ></v-icon>
          </v-btn>
          <v-sheet class="ml-3 mr-sm-0 mr-3 d-none d-md-block">
            <LazyLayoutHeaderProfileDD :authStore="authStore" />
          </v-sheet>
          <v-sheet class="d-flex align-center">
            <LazyLayoutHeaderCountrySwitcher :website-store="shopStore" />
          </v-sheet>
        </div>
      </VAppBar>
    </div>
    <client-only>
      <VNavigationDrawer
        location="right"
        v-model="cartDrawer"
        temporary
        width="520"

        touchless
        @update:modelValue="cartModelEventHandle"
        class="px-0 py-2 rounded-s-xl pnt-scrollbar"
        style="top: 2%; height: calc(96% + 0px); z-index: 1048"
      >
        <LazySharedAppDrawerHeader
          :title="$t('Your Cart')"
          @close-event="cartCloseHandler"
        >
          <template #content v-if="cart.length > 0">
            <h6 class="text-h5">{{ $t("Items") }}: {{ cart?.length }}</h6>
          </template>
        </LazySharedAppDrawerHeader>
        <div v-if="cart.length > 0">
          <LazyEcommerceMiniCart :open="cartDrawer" />
        </div>
        <div v-else>
          <LazyCartEmpty
            @onClickBackBtn="onClickEmptyCartBackBtn"
            @closeCart="cartCloseHandler"
          />
        </div>
      </VNavigationDrawer>
    </client-only>
  </div>
</template>
<script setup lang="ts">
import { storeToRefs } from "pinia";
import { computed, watchEffect } from "vue";
import { useMediaType } from "../../composables/useMediaType";

const { replaceMediaDomain } = useMediaType();
const localePath = useLocalePath();
const shoppingCart = useShoppingCartStore();
const cartDrawer = ref(false);
const globalStore = useGlobalStore();
const shopStore = useWebsiteStore();
const { wishlists, wishListsGuest } = storeToRefs(useWishlistStore());
const authStore = useAuthStore();
const config = useRuntimeConfig();
const staticImageUrl = config.public.pantonecloStaticImageUrl;

const { headerContent } = storeToRefs(shopStore);
const { globals } = storeToRefs(globalStore);
const { cart } = storeToRefs(shoppingCart);
const { getActiveLanguageId } = useLanguage();

const shippingChargeFree = computed(() => globals.value?.shippingChargeFree);
const wishlistCount = computed(() =>
  authStore.authenticated ? wishlists.value.length : wishListsGuest.value.length
);

const fetchData = (countryId) => {
  shopStore.getSetupDataByTypeWithCountryCode(
    "HEADER_STICKY",
    countryId,
    getActiveLanguageId()
  );
};

const cartModelEventHandle = (val) => {
  if (!val) {
    shoppingCart.closeModal();
  }
};

watchEffect(() => {
  if (!cartDrawer.value && shoppingCart.isModalOpen) {
    cartDrawer.value = true; // Open the cart drawer when modal opens
  }
});

const cartCloseHandler = () => {
  shoppingCart.closeModal();
  cartDrawer.value = false;
};

const onClickEmptyCartBackBtn = () => {
  cartDrawer.value = false;
  navigateTo(localePath("/"));
};

onMounted(() => {
  if (!shopStore?.mainBanner) {
    fetchData(globals.value?.id);
  }
});

watch(
  () => globals.value?.id,
  (newCountryId) => {
    if (newCountryId) {
      fetchData(newCountryId);
    }
  }
);
</script>
<style lang="scss">
.main-header-wrapper {
  .v-toolbar {
    box-shadow: 0 0 2px #919eab4d, 0 12px 24px -4px #919eab1f !important;
    &::before {
      content: "";
      background-image: url("https://cdn.pantoneclo.com/pantoneclo-img/header-logo-bg.png");
      height: 100%;
      position: absolute;
      left: 0;
      top: 0;
      z-index: 1;
      background-size: contain;
      width: 100%;
    }

    .v-toolbar__content {
      order: 2;
      z-index: 1;
      position: initial;

      .header-logo-wrapper {
        transform: translateY(-35%);
      }
    }

    .v-toolbar__extension {
      z-index: 0;
      order: 1;
      background-color: #3a3a3c;
      color: #fff;
      display: block;
      text-align: center;
      line-height: 30px;
      font-size: 13px;
      height: 30px;
    }
  }
}

@media (max-width: 960px) {
  .main-header-wrapper {
    .v-toolbar {
      &::before {
        content: none;
      }

      .header-logo-wrapper {
        position: initial !important;
        transform: none !important;
      }

      .v-toolbar__content {
        padding: 0;
      }

      .v-toolbar__extension {
        font-size: 11px;
      }
    }
  }
}

.blink_me {
  font-family: "Patua One", serif;
  letter-spacing: 2px;
  animation: colorFade 2s infinite;
}

@keyframes colorFade {
  0% {
    color: red;
    opacity: 1;
  }
  50% {
    color: black;
    opacity: 0;
  }
  100% {
    color: red;
    opacity: 1;
  }
}
</style>
