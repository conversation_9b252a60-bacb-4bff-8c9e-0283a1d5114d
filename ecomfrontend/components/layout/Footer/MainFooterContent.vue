<template>
  <section class="main-footer-content-wrapper">
    <v-container class="pt-5 pt-md-5">
      <div class="mb-3 text-center text-md-start">
        <NuxtLink :to="localePath('/')" class="text-decoration-none">
          <NuxtImg
            :src="`${
              useRuntimeConfig().public.pantonecloStaticImageUrl
            }logo.png`"
            alt="Pantoneclo"
            loading="lazy"
            sizes="xs:100vw sm:100vw md:100vw"
            format="webp"
            width="150"
            height="35"
            class=""
            fit="cover"
            quality="85"
          />
        </NuxtLink>
      </div>
      <v-row>
        <v-col cols="3" class="hidden-sm-and-down">
          <div>
            <h4 class="footer-widget-title">{{ $t("Quick Links") }}</h4>
            <v-list>
              <v-list-item
                v-for="(link, index) in quickLinks"
                :key="'quick-link-' + index"
                :title="$t(link.name)"
                :to="localePath(link.to)"
                max-height="30px"
                min-height="30px"
                class="ps-0"
              ></v-list-item>
            </v-list>
          </div>
        </v-col>
        <v-col cols="3" class="hidden-sm-and-down">
          <div>
            <h4 class="footer-widget-title">{{ $t("Contact") }}</h4>
            <v-list>
              <v-list-item
                class="ps-0"
                v-for="n in contact"
                :key="n.name"
                :title="$t(n.name)"
                :subtitle="n?.detail"
                :to="n?.to ? localePath(n.to) : undefined"
              ></v-list-item>
            </v-list>
          </div>
        </v-col>
        <v-col cols="3" class="hidden-sm-and-down">
          <div>
            <h4 class="footer-widget-title">{{ $t("My Account") }}</h4>
            <v-list>
              <v-list-item
                v-for="n in myAccount"
                :key="n.name"
                :title="$t(n.name)"
                :to="localePath(n.to)"
                max-height="30px"
                min-height="30px"
                class="ps-0"
              ></v-list-item>
            </v-list>
            <v-btn
              to="/become-a-seller"
              class="text-body-1 cursor-pointer rounded-xl text-capitalize"
              color="black"
              aria-label="Become a Seller"
              >{{$t('become a seller')}}</v-btn
            >
          </div>
        </v-col>

        <v-col
          cols="12"
          sm="6"
          offset-sm="3"
          offset-md="0"
          class="hidden-md-and-up"
        >
          <v-expansion-panels variant="accordion">
            <v-expansion-panel :elevation="0">
              <v-expansion-panel-title
                expand-icon="i-mdi:plus"
                collapse-icon="i-mdi:minus"
                class="px-0 py-0"
                >{{ $t("Quick Links") }}
              </v-expansion-panel-title>
              <v-expansion-panel-text>
                <v-list>
                  <v-list-item
                    v-for="link in quickLinks"
                    :key="link"
                    :title="$t(link.name)"
                    :to="localePath(link.to)"
                    max-height="30px"
                    min-height="30px"
                    class="pa-0"
                  >
                  </v-list-item>
                </v-list>
              </v-expansion-panel-text>
            </v-expansion-panel>
            <v-expansion-panel :elevation="0">
              <v-expansion-panel-title
                expand-icon="i-mdi:plus"
                collapse-icon="i-mdi:minus"
                class="px-0 py-0"
                >{{ $t("Contact") }}
              </v-expansion-panel-title>
              <v-expansion-panel-text>
                <v-list>
                  <v-list-item
                    class="ps-0"
                    v-for="n in contact"
                    :key="n.name"
                    :title="$t(n.name)"
                    :subtitle="n.detail"
                    :to="n?.to ? localePath(n.to) : undefined"
                  ></v-list-item>
                </v-list>
              </v-expansion-panel-text>
            </v-expansion-panel>
            <v-expansion-panel :elevation="0">
              <v-expansion-panel-title
                expand-icon="i-mdi:plus"
                collapse-icon="i-mdi:minus"
                class="px-0 py-0"
                >{{ $t("My Account") }}
              </v-expansion-panel-title>
              <v-expansion-panel-text>
                <v-list>
                  <v-list-item
                    v-for="n in myAccount"
                    :key="n.name"
                    :title="$t(n.name)"
                    :to="localePath(n.to)"
                    max-height="30px"
                    min-height="30px"
                    class="ps-0"
                  ></v-list-item>
                </v-list>
              </v-expansion-panel-text>
            </v-expansion-panel>
          </v-expansion-panels>
        </v-col>

        <v-col cols="12" sm="6" md="3" offset-sm="3" offset-md="0">
          <div class="d-flex justify-center">
            <v-btn
              to="/become-a-seller"
              class="mb-3 w-50 d-flex d-md-none text-body-1 cursor-pointer rounded-xl"
              color="black"
              aria-label="Become a Seller"
              >{{$t('become a seller')}}</v-btn
            >
          </div>
          <div v-if="false" class="platform-wrapper mb-5">
            <h4
              class="footer-widget-title text-center text-md-left mb-3 text-uppercase"
            >
              {{ $t("Platforms") }}
            </h4>
            <div class="d-flex flex-row ga-2 justify-center justify-md-start">
              <div style="width: 110px; height: 30px">
                <svg
                  width="100%"
                  viewBox="0 0 6137.76 6137.76"
                  xmlns="http://www.w3.org/2000/svg"
                  xmlns:xlink="http://www.w3.org/1999/xlink"
                  fill="#000000"
                >
                  <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                  <g
                    id="SVGRepo_tracerCarrier"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  ></g>
                  <g id="SVGRepo_iconCarrier">
                    <path
                      d="M6140.621 1737.202c0 149.832-121.453 271.344-271.261 271.344H274.18c-149.82.012-271.32-121.5-271.32-271.344V272.134C2.86 122.314 124.36.814 274.18.814h5595.168c149.808 0 271.261 121.5 271.261 271.32v1465.068h.012z"
                    ></path>
                    <defs>
                      <path
                        id="a"
                        d="M1332.988 884.566l-276.756-159.78S552.041 433.654 522.4 416.542c-29.64-17.112-59.172-6.744-59.172 29.016v1255.813c0 26.808 21.107 38.364 46.859 23.508 25.74-14.88 546.145-315.348 546.145-315.348l276.744-159.769s234.816-135.563 266.027-153.576c31.213-18.036 28.213-42.756 1.873-56.939-26.328-14.197-267.888-154.681-267.888-154.681z"
                      ></path>
                    </defs>
                    <clipPath id="b">
                      <use xlink:href="#a" overflow="visible"></use>
                    </clipPath>
                    <linearGradient
                      id="c"
                      gradientUnits="userSpaceOnUse"
                      x1="1451.479"
                      y1="2452.512"
                      x2="1451.479"
                      y2="2339.209"
                      gradientTransform="matrix(12 0 0 -12 -16611.705 29817.492)"
                    >
                      <stop offset="0" stop-color="#257cb0"></stop>
                      <stop offset=".286" stop-color="#4a93b5"></stop>
                      <stop offset=".767" stop-color="#78bcbb"></stop>
                      <stop offset="1" stop-color="#89cfbd"></stop>
                    </linearGradient>
                    <path
                      clip-path="url(#b)"
                      fill="url(#c)"
                      d="M463.24 387.346v1359.636l685.608-679.812z"
                    ></path>
                    <defs>
                      <path
                        id="d"
                        d="M1332.988 884.566l-276.756-159.78S552.041 433.654 522.4 416.542c-29.64-17.112-59.172-6.744-59.172 29.016v1255.813c0 26.808 21.107 38.364 46.859 23.508 25.74-14.88 546.145-315.348 546.145-315.348l276.744-159.769s234.816-135.563 266.027-153.576c31.213-18.036 28.213-42.756 1.873-56.939-26.328-14.197-267.888-154.681-267.888-154.681z"
                      ></path>
                    </defs>
                    <clipPath id="e">
                      <use xlink:href="#d" overflow="visible"></use>
                    </clipPath>
                    <linearGradient
                      id="f"
                      gradientUnits="userSpaceOnUse"
                      x1="1422.913"
                      y1="2424.393"
                      x2="1495.392"
                      y2="2424.393"
                      gradientTransform="matrix(12 0 0 -12 -16611.705 29817.492)"
                    >
                      <stop offset="0" stop-color="#52c1ad"></stop>
                      <stop offset="1" stop-color="#dee89a"></stop>
                    </linearGradient>
                    <path
                      clip-path="url(#e)"
                      fill="url(#f)"
                      d="M1332.988 884.566l-276.756-159.78L463.24 382.402v4.944l685.608 679.824z"
                    ></path>
                    <defs>
                      <path
                        id="g"
                        d="M1332.988 884.566l-276.756-159.78S552.041 433.654 522.4 416.542c-29.64-17.112-59.172-6.744-59.172 29.016v1255.813c0 26.808 21.107 38.364 46.859 23.508 25.74-14.88 546.145-315.348 546.145-315.348l276.744-159.769s234.816-135.563 266.027-153.576c31.213-18.036 28.213-42.756 1.873-56.939-26.328-14.197-267.888-154.681-267.888-154.681z"
                      ></path>
                    </defs>
                    <clipPath id="h">
                      <use xlink:href="#g" overflow="visible"></use>
                    </clipPath>
                    <linearGradient
                      id="i"
                      gradientUnits="userSpaceOnUse"
                      x1="1459.151"
                      y1="2395.86"
                      x2="1459.151"
                      y2="2338.797"
                      gradientTransform="matrix(12 0 0 -12 -16611.705 29817.492)"
                    >
                      <stop offset="0" stop-color="#ec413d"></stop>
                      <stop offset=".167" stop-color="#da4452"></stop>
                      <stop offset=".575" stop-color="#b0487a"></stop>
                      <stop offset=".862" stop-color="#954a92"></stop>
                      <stop offset="1" stop-color="#8a4a9d"></stop>
                    </linearGradient>
                    <path
                      clip-path="url(#h)"
                      fill="url(#i)"
                      d="M463.24 1746.982v4.944l592.992-342.396 276.744-159.78-184.128-182.58z"
                    ></path>
                    <defs>
                      <path
                        id="j"
                        d="M1332.988 884.566l-276.756-159.78S552.041 433.654 522.4 416.542c-29.64-17.112-59.172-6.744-59.172 29.016v1255.813c0 26.808 21.107 38.364 46.859 23.508 25.74-14.88 546.145-315.348 546.145-315.348l276.744-159.769s234.816-135.563 266.027-153.576c31.213-18.036 28.213-42.756 1.873-56.939-26.328-14.197-267.888-154.681-267.888-154.681z"
                      ></path>
                    </defs>
                    <clipPath id="k">
                      <use xlink:href="#j" overflow="visible"></use>
                    </clipPath>
                    <linearGradient
                      id="l"
                      gradientUnits="userSpaceOnUse"
                      x1="1500.897"
                      y1="2411.077"
                      x2="1500.897"
                      y2="2380.646"
                      gradientTransform="matrix(12 0 0 -12 -16611.705 29817.492)"
                    >
                      <stop offset="0" stop-color="#f58879"></stop>
                      <stop offset=".119" stop-color="#f69079"></stop>
                      <stop offset=".713" stop-color="#fcb877"></stop>
                      <stop offset="1" stop-color="#fec874"></stop>
                    </linearGradient>
                    <path
                      clip-path="url(#k)"
                      fill="url(#l)"
                      d="M1332.988 884.566l-184.14 182.604 184.128 182.58 316.283-182.592z"
                    ></path>
                    <path
                      d="M4779.556 1332.946c-25.752 0-49.248-4.752-70.524-14.244-21.276-9.504-39.155-24.12-53.592-43.824h-4.319c2.879 23.088 4.319 44.977 4.319 65.652v162.708h-59.856V930.322h48.696l8.28 54.12h2.88c15.407-21.66 33.336-37.272 53.771-46.896 20.437-9.624 43.896-14.436 70.332-14.436 52.44 0 92.916 17.928 121.404 53.76 28.5 35.832 42.756 86.112 42.756 150.792 0 64.944-14.496 115.38-43.452 151.368-28.979 35.951-69.203 53.916-120.695 53.916zm-8.664-358.968c-40.404 0-69.624 11.196-87.66 33.553-18.036 22.367-27.3 57.972-27.792 106.8v13.332c0 55.571 9.264 95.315 27.792 119.256s48.24 35.88 89.1 35.88c34.176 0 60.937-13.812 80.305-41.472 19.344-27.648 29.027-65.772 29.027-114.384 0-49.284-9.684-87.133-29.027-113.461-19.357-26.352-46.621-39.504-81.745-39.504zM5082.376 1325.722h-59.881v-561.36h59.881v561.36zM5426.692 1325.722l-11.916-56.279h-2.88c-19.729 24.791-39.385 41.579-58.992 50.327-19.596 8.796-44.076 13.177-73.404 13.177-39.203 0-69.924-10.104-92.184-30.301-22.26-20.195-33.372-48.924-33.372-86.22 0-79.836 63.864-121.692 191.567-125.556l67.129-2.172v-24.528c0-31.032-6.685-53.952-20.041-68.724-13.332-14.809-34.716-22.2-64.031-22.2-32.965 0-70.248 10.104-111.841 30.3l-18.407-45.816c19.5-10.584 40.824-18.875 64.056-24.888 23.208-6.012 46.464-9.023 69.804-9.023 47.137 0 82.068 10.452 104.809 31.38 22.716 20.928 34.08 54.479 34.08 100.668v269.844h-44.376v.011zm-135.288-42.192c37.26 0 66.54-10.225 87.84-30.66 21.265-20.437 31.933-49.068 31.933-85.884v-35.712l-59.916 2.508c-47.616 1.716-81.925 9.084-102.996 22.224-21.036 13.104-31.561 33.492-31.561 61.152 0 21.636 6.54 38.124 19.669 49.403 13.115 11.317 31.439 16.969 55.031 16.969zM5505.832 930.322h64.2l86.593 225.491c18.996 51.48 30.791 88.633 35.375 111.492h2.881c3.131-12.288 9.695-33.252 19.655-62.987 9.984-29.712 42.647-121.021 97.944-273.996h64.248l-169.944 450.252c-16.824 44.496-36.491 76.067-59.004 94.739-22.476 18.624-50.088 27.925-82.764 27.925-18.288 0-36.324-2.04-54.144-6.12v-47.977c13.223 2.88 28.031 4.332 44.388 4.332 41.136 0 70.452-23.088 88.032-69.275l21.996-56.269-159.456-397.607zM3670.167 967.066c19.009 15.731 58.705 48.779 58.705 111.672 0 61.164-34.74 90.168-69.48 117.42-10.752 10.739-23.16 22.355-23.16 40.571 0 18.156 12.396 28.093 21.528 35.545l29.784 23.111c36.384 30.612 69.432 58.752 69.432 115.836 0 77.736-75.264 156.276-217.488 156.276-119.939 0-177.803-57.048-177.803-118.284 0-29.76 14.819-71.916 63.684-100.896 51.24-31.428 120.756-35.52 157.955-38.063-11.615-14.868-24.815-30.588-24.815-56.184 0-14.04 4.152-22.32 8.269-32.292-9.133.84-18.217 1.668-26.496 1.668-87.637 0-137.256-65.34-137.256-129.816 0-38.052 17.388-80.256 52.908-110.82 47.159-38.867 103.391-45.504 148.092-45.504h170.328l-52.933 29.772h-51.253v-.012zm-58.74 367.224c-6.611-.864-10.764-.864-18.996-.864-7.439 0-52.163 1.692-86.855 13.296-18.191 6.564-71.137 26.412-71.137 85.164 0 58.692 57.097 100.933 145.597 100.933 79.344 0 121.571-38.124 121.571-89.353.001-42.264-27.299-64.5-90.18-109.176zm24-157.164c18.996-19.044 20.629-45.48 20.629-60.384 0-59.532-35.545-152.16-104.172-152.16-21.529 0-44.664 10.74-57.9 27.3-14.041 17.364-18.217 39.672-18.217 61.2 0 55.404 32.256 147.216 103.416 147.216 20.677 0 42.961-9.924 56.244-23.172zM3149.5 1321.054c-131.268 0-201.455-102.396-201.455-194.892 0-108.181 88.332-200.616 213.888-200.616 121.319 0 197.292 94.92 197.292 194.855.011 97.393-75.097 200.653-209.725 200.653zm103.201-66.876c19.848-26.448 24.803-59.46 24.803-91.691 0-72.685-34.68-211.38-137.076-211.38-27.239 0-54.479 10.752-74.291 28.128-32.209 28.859-38.016 65.208-38.016 100.752 0 81.684 40.463 216.239 140.399 216.239 32.196 0 65.231-15.66 84.181-42.048zM2701.96 1321.054c-131.293 0-201.492-102.396-201.492-194.892 0-108.181 88.367-200.616 213.888-200.616 121.356 0 197.315 94.92 197.315 194.855.013 97.393-75.107 200.653-209.711 200.653zm103.224-66.876c19.812-26.448 24.78-59.46 24.78-91.691 0-72.685-34.705-211.38-137.101-211.38-27.228 0-54.491 10.752-74.292 28.128-32.208 28.859-37.98 65.208-37.98 100.752 0 81.684 40.44 216.239 140.389 216.239 32.208 0 65.221-15.66 84.204-42.048zM2437.396 1308.106l-118.729 27.396c-48.168 7.488-91.319 14.076-136.955 14.076-229.152 0-316.284-168.528-316.284-300.528 0-161.027 123.672-310.416 335.388-310.416 44.844 0 87.973 6.624 126.984 17.424 62.256 17.437 91.309 39 109.584 51.469l-68.928 65.579-29.04 6.612 20.748-33.216c-28.212-27.372-79.728-78-177.648-78-131.172 0-229.979 99.66-229.979 244.92 0 156.024 112.92 302.964 293.855 302.964 53.184 0 80.521-10.775 105.444-20.76v-133.62l-125.328 6.636 66.384-35.735h176.004l-21.564 20.748c-5.855 5.004-6.659 6.684-8.315 13.26-.864 7.488-1.644 31.584-1.644 39.876v101.316h.023z"
                      fill="#f9f9f9"
                    ></path>
                    <path
                      d="M3932.548 1284.202c-27.408-2.496-33.145-7.452-33.145-39.864V774.49c.168-1.788.276-3.624.443-5.353 3.349-29.04 11.641-34.02 37.404-48.983H3818.5l-62.279 29.892h63.468v.372l-.048-.3v512.508c0 16.572-3.276 19.092-22.393 43.968h146.953l30.719-18.216c-14.1-1.716-28.236-2.52-42.372-4.176zM4270.347 1291.834c-9.035 4.956-18.107 10.752-27.144 14.844-27.252 12.36-55.271 15.672-80.052 15.672-26.316 0-67.572-1.704-109.656-32.172-58.487-41.256-84.047-112.151-84.047-173.964 0-127.764 103.871-190.416 188.783-190.416 29.688 0 60.229 7.404 84.96 23.101 41.185 27.155 51.899 62.604 57.636 81.575l-193.691 78.349-63.492 4.943c20.58 104.7 91.477 165.66 169.824 165.66 42.036 0 72.552-14.808 100.572-28.8l-43.693 41.208zm-77.471-245.712c15.636-5.748 23.879-10.716 23.879-22.248 0-32.952-37.08-70.896-81.636-70.896-33.013 0-94.8 25.572-94.8 114.589 0 14.016 1.656 28.848 2.508 43.739l150.049-65.184zM4349.919 931.438v54.396h-6.611v-54.396h-18v-5.641h42.6v5.641h-17.989zM4424.08 985.834v-54.9h-.3l-16.752 54.9h-5.172l-16.896-54.9h-.217v54.9h-6v-60.036h10.284l15.288 48.528h.216l15.06-48.528h10.429v60.036h-5.94z"
                      fill="#f9f9f9"
                    ></path>
                    <path
                      d="M2030.308 576.994h-41.256c0-5.676-3.912-21.744-11.736-48.216h-59.557c-8.075 26.04-12.119 42.107-12.119 48.216h-38.796c0-3.349 10.032-30.853 30.096-82.5 20.063-51.648 30.096-80.101 30.096-85.332h48.732c0 4.896 9.084 32.928 27.264 84.108 18.18 51.18 27.276 79.08 27.276 83.724zm-59.557-72.84c-14.699-44.341-22.043-68.316-22.043-71.929h-2.064c0 3.349-7.608 27.324-22.812 71.929h46.919zM2196.532 576.994h-31.836c0-3.528-11.819-22.128-35.448-55.812-24.744-35.496-38.844-59.76-42.275-72.827h-2.064c2.148 18.815 3.229 35.831 3.229 51.048 0 21.228.6 47.1 1.8 77.604h-31.716c1.283-26.64 1.932-55.896 1.932-87.78 0-31.367-.648-58.056-1.932-80.052h38.279c0 4.56 10.608 22.74 31.836 54.528 22.597 33.768 35.58 56.676 38.929 68.712h1.932c-2.063-19.164-3.096-36.349-3.096-51.564 0-23.279-.601-47.184-1.8-71.676h32.232c-1.213 21.312-1.801 48-1.801 80.052-.013 32.291.587 61.547 1.799 87.767zM2383.36 487.271c0 24.407-7.823 45.72-23.46 63.936-15.648 18.228-37.561 27.324-65.748 27.324-12.72 0-30.252-.517-52.596-1.549 1.115-25.955 1.68-55.224 1.68-87.779 0-31.272-.564-57.96-1.68-80.053h22.561c3.695 0 9.191-.132 16.5-.384 7.295-.252 12.071-.384 14.303-.384 31.021 0 53.509 7.74 67.488 23.208 13.968 15.468 20.952 34.02 20.952 55.681zm-35.185 5.542c0-16.5-4.812-30.396-14.436-41.712-9.624-11.292-24.324-16.943-44.088-16.943-3.695 0-8.76.384-15.216 1.164.852 17.195 1.284 35.147 1.284 53.88 0 20.544.432 40.908 1.283 61.104a85.294 85.294 0 0 0 16.752 1.68c18.48 0 32.16-5.747 41.053-17.22 8.893-11.472 13.368-25.453 13.368-41.953zM2555.644 576.994h-38.16c-9.972-30.42-17.615-49.2-22.943-56.328-5.328-7.14-13.92-10.704-25.776-10.704-4.992 0-9.757.048-14.304.132 0 22.093.383 44.388 1.164 66.9h-35.832c1.115-25.956 1.68-55.224 1.68-87.78 0-31.271-.564-57.96-1.68-80.052h22.295c2.58 0 7.969-.132 16.177-.384s15.792-.385 22.752-.385c39.444 0 59.172 13.488 59.172 40.477 0 21.396-11.256 36.3-33.768 44.736v1.932c8.676 2.4 16.284 9.18 22.812 20.292 6.515 11.124 15.323 31.512 26.411 61.164zm-48.336-118.597c0-17.1-11.004-25.655-33-25.655-8.076 0-15.217.6-21.396 1.8.948 13.32 1.416 30.684 1.416 52.08 4.729.18 8.725.252 11.988.252 27.324.012 40.992-9.48 40.992-28.477zM2740.419 489.981c0 26.377-8.076 48.252-24.239 65.616-16.164 17.353-36.144 26.04-59.94 26.04-22.691 0-41.688-7.968-56.976-23.916-15.3-15.936-22.944-36.203-22.944-60.768 0-26.376 8.076-48.252 24.24-65.616 16.152-17.352 36.132-26.04 59.939-26.04 22.692 0 41.677 7.944 56.977 23.844 15.3 15.901 22.943 36.169 22.943 60.84zm-35.567 3.733c0-17.964-4.597-32.532-13.788-43.704-9.204-11.172-20.544-16.752-34.032-16.752-12.552 0-23.208 5.628-31.968 16.884s-13.164 25.477-13.164 42.672c0 17.868 4.62 32.412 13.859 43.633 9.24 11.22 20.557 16.824 33.973 16.824 12.54 0 23.208-5.652 31.968-16.957 8.76-11.292 13.152-25.501 13.152-42.6zM2813.308 576.994h-36.48c1.116-26.64 1.68-55.896 1.68-87.78 0-31.368-.563-58.056-1.68-80.052h36.48c-1.116 21.66-1.68 48.336-1.68 80.052 0 32.388.564 61.656 1.68 87.78zM3000.148 487.271c0 24.407-7.824 45.72-23.46 63.936-15.647 18.228-37.56 27.324-65.748 27.324-12.72 0-30.252-.517-52.597-1.549 1.117-25.955 1.681-55.224 1.681-87.779 0-31.272-.563-57.96-1.681-80.053h22.561c3.696 0 9.192-.132 16.5-.384 7.297-.252 12.072-.384 14.305-.384 31.02 0 53.508 7.74 67.487 23.208 13.968 15.468 20.952 34.02 20.952 55.681zm-35.184 5.542c0-16.5-4.812-30.396-14.437-41.712-9.624-11.292-24.323-16.943-44.088-16.943-3.696 0-8.76.384-15.216 1.164.852 17.195 1.283 35.147 1.283 53.88 0 20.544.433 40.908 1.284 61.104a85.301 85.301 0 0 0 16.752 1.68c18.479 0 32.16-5.747 41.052-17.22 8.894-11.472 13.37-25.453 13.37-41.953zM3248.02 576.994h-41.256c0-5.676-3.912-21.744-11.748-48.216h-59.556c-8.076 26.04-12.121 42.107-12.121 48.216h-38.783c0-3.349 10.031-30.853 30.096-82.5 20.064-51.648 30.096-80.101 30.096-85.332h48.732c0 4.896 9.096 32.928 27.264 84.108 18.168 51.18 27.276 79.08 27.276 83.724zm-59.556-72.84c-14.7-44.341-22.057-68.316-22.057-71.929h-2.064c0 3.349-7.596 27.324-22.799 71.929h46.92zM3398.38 453.106c0 20.544-7.633 35.712-22.885 45.504s-33.96 14.699-56.137 14.699c-4.043 0-6.911-.084-8.639-.252 0 16.164.467 37.465 1.416 63.937h-36.229c1.115-24.828 1.68-54.097 1.68-87.78 0-30.936-.563-57.624-1.68-80.052h22.428c3.18 0 9.275-.132 18.3-.384s16.837-.385 23.46-.385c16.32 0 30.12 3.696 41.376 11.088 11.257 7.393 16.91 18.589 16.91 33.625zm-33.252 7.092c0-18.385-12.205-27.588-36.612-27.588-6.108 0-12.552.647-19.332 1.932.948 16.248 1.416 34.632 1.416 55.176 2.147.18 4.644.252 7.476.252 31.368.011 47.052-9.912 47.052-29.772zM3552.988 453.106c0 20.544-7.632 35.712-22.872 45.504-15.252 9.792-33.972 14.699-56.147 14.699-4.045 0-6.912-.084-8.641-.252 0 16.164.469 37.465 1.416 63.937h-36.228c1.116-24.828 1.681-54.097 1.681-87.78 0-30.936-.564-57.624-1.681-80.052h22.428c3.181 0 9.276-.132 18.3-.384s16.836-.385 23.46-.385c16.319 0 30.12 3.696 41.376 11.088 11.255 7.393 16.908 18.589 16.908 33.625zm-33.252 7.092c0-18.385-12.204-27.588-36.611-27.588-6.108 0-12.553.647-19.332 1.932.947 16.248 1.416 34.632 1.416 55.176 2.147.18 4.644.252 7.476.252 31.367.011 47.051-9.912 47.051-29.772zM3805.768 489.981c0 26.377-8.076 48.252-24.24 65.616-16.163 17.364-36.144 26.04-59.94 26.04-22.691 0-41.688-7.968-56.975-23.916-15.289-15.936-22.944-36.203-22.944-60.768 0-26.376 8.076-48.252 24.239-65.616 16.164-17.364 36.145-26.04 59.94-26.04 22.692 0 41.688 7.944 56.976 23.844 15.288 15.901 22.944 36.169 22.944 60.84zm-35.58 3.733c0-17.964-4.597-32.532-13.788-43.704s-20.544-16.752-34.031-16.752c-12.553 0-23.209 5.628-31.969 16.884s-13.152 25.477-13.152 42.672c0 17.868 4.621 32.412 13.86 43.633 9.239 11.22 20.556 16.824 33.96 16.824 12.552 0 23.208-5.652 31.968-16.956 8.772-11.293 13.152-25.502 13.152-42.601zM3980.488 576.994h-31.836c0-3.528-11.809-22.128-35.448-55.812-24.744-35.496-38.845-59.76-42.276-72.827h-2.064c2.148 18.815 3.229 35.831 3.229 51.048 0 21.228.6 47.1 1.801 77.604h-31.717c1.284-26.64 1.932-55.896 1.932-87.78 0-31.367-.646-58.056-1.932-80.052h38.28c0 4.56 10.62 22.74 31.836 54.528 22.608 33.768 35.58 56.676 38.929 68.712h1.932c-2.064-19.164-3.096-36.349-3.096-51.564 0-23.279-.6-47.184-1.801-71.676h32.232c-1.212 21.312-1.8 48-1.8 80.052-.001 32.291.599 61.547 1.799 87.767z"
                      fill="#f9f9fa"
                    ></path>
                  </g>
                </svg>
              </div>
              <div style="width: 120px; height: 30px">
                <svg
                  width="100%"
                  viewBox="0 0 539.86 539.86"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="#000000"
                >
                  <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                  <g
                    id="SVGRepo_tracerCarrier"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke="#CCCCCC"
                    stroke-width="10.797120000000001"
                  ></g>
                  <g id="SVGRepo_iconCarrier">
                    <g transform="scale(4.00216 4.0011)">
                      <path
                        fill="#FFF"
                        d="M134.032 35.268a3.83 3.83 0 0 1-3.834 3.83H4.729a3.835 3.835 0 0 1-3.839-3.83V4.725A3.84 3.84 0 0 1 4.729.89h125.468a3.834 3.834 0 0 1 3.834 3.835l.001 30.543z"
                      ></path>
                      <path
                        fill="#A6A6A6"
                        d="M130.198 39.989H4.729A4.73 4.73 0 0 1 0 35.268V4.726A4.733 4.733 0 0 1 4.729 0h125.468a4.735 4.735 0 0 1 4.729 4.726v30.542c.002 2.604-2.123 4.721-4.728 4.721z"
                      ></path>
                      <path
                        d="M134.032 35.268a3.83 3.83 0 0 1-3.834 3.83H4.729a3.835 3.835 0 0 1-3.839-3.83V4.725A3.84 3.84 0 0 1 4.729.89h125.468a3.834 3.834 0 0 1 3.834 3.835l.001 30.543z"
                      ></path>
                      <path
                        fill="#FFF"
                        d="M30.128 19.784c-.029-3.223 2.639-4.791 2.761-4.864-1.511-2.203-3.853-2.504-4.676-2.528-1.967-.207-3.875 1.177-4.877 1.177-1.022 0-2.565-1.157-4.228-1.123-2.14.033-4.142 1.272-5.24 3.196-2.266 3.923-.576 9.688 1.595 12.859 1.086 1.554 2.355 3.287 4.016 3.226 1.625-.066 2.232-1.035 4.193-1.035 1.943 0 2.513 1.035 4.207.996 1.744-.027 2.842-1.56 3.89-3.127 1.255-1.779 1.759-3.533 1.779-3.623-.04-.014-3.386-1.292-3.42-5.154zM26.928 10.306c.874-1.093 1.472-2.58 1.306-4.089-1.265.056-2.847.875-3.758 1.944-.806.942-1.526 2.486-1.34 3.938 1.421.106 2.88-.717 3.792-1.793z"
                      ></path>
                      <linearGradient
                        id="a"
                        gradientUnits="userSpaceOnUse"
                        x1="-23.235"
                        y1="97.431"
                        x2="-23.235"
                        y2="61.386"
                        gradientTransform="matrix(4.0022 0 0 4.0011 191.95 -349.736)"
                      >
                        <stop
                          offset="0"
                          stop-color="#1a1a1a"
                          stop-opacity=".1"
                        ></stop>
                        <stop
                          offset=".123"
                          stop-color="#212121"
                          stop-opacity=".151"
                        ></stop>
                        <stop
                          offset=".308"
                          stop-color="#353535"
                          stop-opacity=".227"
                        ></stop>
                        <stop
                          offset=".532"
                          stop-color="#575757"
                          stop-opacity=".318"
                        ></stop>
                        <stop
                          offset=".783"
                          stop-color="#858585"
                          stop-opacity=".421"
                        ></stop>
                        <stop
                          offset="1"
                          stop-color="#b3b3b3"
                          stop-opacity=".51"
                        ></stop>
                      </linearGradient>
                      <path
                        fill="url(#a)"
                        d="M130.198 0H62.993l26.323 39.989h40.882a4.733 4.733 0 0 0 4.729-4.724V4.726A4.734 4.734 0 0 0 130.198 0z"
                      ></path>
                      <g fill="#FFF">
                        <path
                          d="M53.665 31.504h-2.271l-1.244-3.909h-4.324l-1.185 3.909H42.43l4.285-13.308h2.646l4.304 13.308zm-3.89-5.549L48.65 22.48c-.119-.355-.343-1.191-.671-2.507h-.04c-.132.566-.343 1.402-.632 2.507l-1.106 3.475h3.574zM64.663 26.588c0 1.632-.443 2.922-1.33 3.869-.794.843-1.781 1.264-2.958 1.264-1.271 0-2.185-.453-2.74-1.361v5.035h-2.132V25.062c0-1.025-.027-2.076-.079-3.154h1.875l.119 1.521h.04c.711-1.146 1.79-1.719 3.238-1.719 1.132 0 2.077.447 2.833 1.342.755.897 1.134 2.075 1.134 3.536zm-2.172.078c0-.934-.21-1.704-.632-2.311-.461-.631-1.08-.947-1.856-.947-.526 0-1.004.176-1.431.523-.428.35-.708.807-.839 1.373a2.784 2.784 0 0 0-.099.649v1.601c0 .697.214 1.286.642 1.768.428.48.984.721 1.668.721.803 0 1.428-.311 1.875-.928.448-.619.672-1.435.672-2.449zM75.7 26.588c0 1.632-.443 2.922-1.33 3.869-.795.843-1.781 1.264-2.959 1.264-1.271 0-2.185-.453-2.74-1.361v5.035h-2.132V25.062c0-1.025-.027-2.076-.079-3.154h1.875l.119 1.521h.04c.71-1.146 1.789-1.719 3.238-1.719 1.131 0 2.076.447 2.834 1.342.754.897 1.134 2.075 1.134 3.536zm-2.173.078c0-.934-.211-1.704-.633-2.311-.461-.631-1.078-.947-1.854-.947-.526 0-1.004.176-1.433.523-.428.35-.707.807-.838 1.373-.065.264-.1.479-.1.649v1.601c0 .697.215 1.286.641 1.768.428.479.984.721 1.67.721.804 0 1.429-.311 1.875-.928.448-.619.672-1.435.672-2.449zM88.04 27.771c0 1.133-.396 2.054-1.183 2.765-.866.776-2.075 1.165-3.625 1.165-1.432 0-2.58-.276-3.446-.829l.493-1.777c.935.554 1.962.83 3.08.83.804 0 1.429-.182 1.875-.543.447-.362.673-.846.673-1.45 0-.541-.187-.994-.554-1.363-.369-.368-.979-.711-1.836-1.026-2.33-.869-3.496-2.14-3.496-3.812 0-1.092.412-1.986 1.234-2.685.822-.698 1.912-1.047 3.268-1.047 1.211 0 2.22.211 3.021.632l-.535 1.738c-.754-.408-1.605-.612-2.557-.612-.752 0-1.342.185-1.764.553-.355.329-.535.73-.535 1.206 0 .525.205.961.613 1.303.354.315 1 .658 1.934 1.026 1.146.462 1.988 1 2.527 1.618.543.618.813 1.389.813 2.308zM95.107 23.508h-2.35v4.659c0 1.185.414 1.776 1.244 1.776.381 0 .697-.032.947-.099l.059 1.619c-.42.157-.973.236-1.658.236-.842 0-1.5-.257-1.975-.771-.473-.514-.711-1.375-.711-2.587v-4.837h-1.4v-1.6h1.4v-1.757l2.094-.632v2.389h2.35v1.604zM105.689 26.627c0 1.475-.422 2.686-1.264 3.633-.881.975-2.053 1.461-3.514 1.461-1.41 0-2.531-.467-3.367-1.4-.836-.935-1.254-2.113-1.254-3.534 0-1.487.432-2.705 1.293-3.652.863-.948 2.025-1.422 3.486-1.422 1.408 0 2.539.468 3.395 1.402.818.906 1.225 2.076 1.225 3.512zm-2.21.049c0-.879-.19-1.633-.571-2.264-.447-.762-1.087-1.143-1.916-1.143-.854 0-1.509.381-1.955 1.143-.382.631-.572 1.398-.572 2.304 0 .88.19 1.636.572 2.265.461.762 1.104 1.143 1.937 1.143.815 0 1.454-.389 1.916-1.162.392-.646.589-1.405.589-2.286zM112.622 23.783a3.71 3.71 0 0 0-.672-.059c-.75 0-1.33.282-1.738.85-.354.5-.532 1.132-.532 1.895v5.035h-2.132V24.93a67.43 67.43 0 0 0-.062-3.021h1.857l.078 1.836h.059c.226-.631.58-1.14 1.066-1.521a2.578 2.578 0 0 1 1.541-.514c.197 0 .375.014.533.039l.002 2.034zM122.157 26.252a5 5 0 0 1-.078.967h-6.396c.024.948.334 1.674.928 2.174.539.446 1.236.67 2.092.67.947 0 1.811-.15 2.588-.453l.334 1.479c-.908.396-1.98.593-3.217.593-1.488 0-2.656-.438-3.506-1.312-.848-.875-1.273-2.051-1.273-3.524 0-1.446.395-2.651 1.186-3.612.828-1.026 1.947-1.539 3.355-1.539 1.383 0 2.43.513 3.141 1.539.563.813.846 1.821.846 3.018zm-2.033-.553c.015-.633-.125-1.178-.414-1.639-.369-.594-.937-.89-1.698-.89-.697 0-1.265.289-1.697.869-.355.461-.566 1.015-.631 1.658l4.44.002z"
                        ></path>
                      </g>
                      <g fill="#FFF">
                        <path
                          d="M45.211 13.491c-.593 0-1.106-.029-1.533-.078V6.979a11.606 11.606 0 0 1 1.805-.136c2.445 0 3.571 1.203 3.571 3.164 0 2.262-1.33 3.484-3.843 3.484zm.358-5.823c-.33 0-.611.02-.844.068v4.891c.126.02.368.029.708.029 1.602 0 2.514-.912 2.514-2.62 0-1.523-.825-2.368-2.378-2.368zM52.563 13.54c-1.378 0-2.271-1.029-2.271-2.426 0-1.456.912-2.494 2.349-2.494 1.358 0 2.271.98 2.271 2.417 0 1.474-.941 2.503-2.349 2.503zm.04-4.154c-.757 0-1.242.708-1.242 1.698 0 .971.495 1.679 1.232 1.679s1.232-.757 1.232-1.699c0-.96-.485-1.678-1.222-1.678zM62.77 8.717l-1.475 4.716h-.961l-.611-2.048a15.53 15.53 0 0 1-.379-1.523h-.02c-.077.514-.223 1.029-.378 1.523l-.65 2.048h-.971l-1.388-4.716h1.077l.534 2.242c.126.534.232 1.038.32 1.514h.02c.077-.397.203-.893.388-1.504l.67-2.251h.854l.641 2.203c.155.534.281 1.058.379 1.553h.028c.068-.485.175-1 .32-1.553l.573-2.203 1.029-.001zM68.2 13.433h-1.048v-2.708c0-.834-.32-1.252-.951-1.252-.621 0-1.048.534-1.048 1.155v2.805h-1.048v-3.368c0-.417-.01-.864-.039-1.349h.922l.049.728h.029c.282-.504.854-.824 1.495-.824.99 0 1.64.757 1.64 1.989l-.001 2.824zM71.09 13.433h-1.049v-6.88h1.049v6.88zM74.911 13.54c-1.377 0-2.271-1.029-2.271-2.426 0-1.456.912-2.494 2.348-2.494 1.359 0 2.271.98 2.271 2.417.001 1.474-.941 2.503-2.348 2.503zm.039-4.154c-.757 0-1.242.708-1.242 1.698 0 .971.496 1.679 1.231 1.679.738 0 1.232-.757 1.232-1.699.001-.96-.483-1.678-1.221-1.678zM81.391 13.433l-.076-.543h-.028c-.32.437-.787.65-1.379.65-.845 0-1.445-.592-1.445-1.388 0-1.164 1.009-1.766 2.756-1.766v-.087c0-.621-.329-.932-.979-.932-.465 0-.873.117-1.232.35l-.213-.689c.436-.272.98-.408 1.619-.408 1.232 0 1.854.65 1.854 1.951v1.737c0 .476.021.845.068 1.126l-.945-.001zm-.144-2.349c-1.164 0-1.748.282-1.748.951 0 .495.301.737.719.737.533 0 1.029-.407 1.029-.96v-.728zM87.357 13.433l-.049-.757h-.029c-.301.572-.807.864-1.514.864-1.137 0-1.979-1-1.979-2.407 0-1.475.873-2.514 2.065-2.514.631 0 1.078.213 1.33.641h.021V6.553h1.049v5.609c0 .456.011.883.039 1.271h-.933zm-.155-2.775c0-.66-.437-1.223-1.104-1.223-.777 0-1.252.689-1.252 1.659 0 .951.493 1.602 1.231 1.602.659 0 1.125-.573 1.125-1.252v-.786zM94.902 13.54c-1.377 0-2.27-1.029-2.27-2.426 0-1.456.912-2.494 2.348-2.494 1.359 0 2.271.98 2.271 2.417.001 1.474-.94 2.503-2.349 2.503zm.039-4.154c-.756 0-1.241.708-1.241 1.698 0 .971.495 1.679 1.231 1.679.738 0 1.232-.757 1.232-1.699.002-.96-.483-1.678-1.222-1.678zM102.887 13.433h-1.049v-2.708c0-.834-.32-1.252-.951-1.252-.621 0-1.047.534-1.047 1.155v2.805h-1.049v-3.368c0-.417-.01-.864-.039-1.349h.922l.049.728h.029c.281-.504.854-.825 1.494-.825.99 0 1.641.757 1.641 1.989v2.825zM109.938 9.503h-1.153v2.29c0 .583.202.874.61.874.185 0 .34-.02.465-.049l.029.796c-.203.078-.475.117-.813.117-.826 0-1.32-.456-1.32-1.65V9.503h-.688v-.786h.688v-.864l1.029-.311v1.174h1.153v.787zM115.486 13.433h-1.047v-2.688c0-.844-.319-1.271-.951-1.271-.543 0-1.049.369-1.049 1.116v2.843h-1.047v-6.88h1.047v2.833h.021c.33-.514.808-.767 1.418-.767.998 0 1.608.776 1.608 2.009v2.805zM121.17 11.327h-3.145c.02.893.611 1.397 1.486 1.397.465 0 .893-.078 1.271-.223l.163.728c-.446.194-.971.291-1.582.291-1.475 0-2.348-.932-2.348-2.377 0-1.446.894-2.533 2.23-2.533 1.205 0 1.961.893 1.961 2.242a2.02 2.02 0 0 1-.036.475zm-.961-.747c0-.728-.367-1.242-1.037-1.242-.602 0-1.078.524-1.146 1.242h2.183z"
                        ></path>
                      </g>
                    </g>
                  </g>
                </svg>
              </div>
            </div>
          </div>

          <div class="security-payment-wrapper mb-7 text-center text-md-start">
            <h4
              class="footer-widget-title text-center text-md-left mb-3 text-uppercase"
            >
              {{ $t("Security & Payment partner") }}
            </h4>
            <div
              v-if="countryCode != 'BD'"
              class="d-flex ga-3 flex-wrap justify-center justify-md-start"
            >
              <div
                class="border rounded-sm border-black"
                style="width: 50px; height: 30px"
              >
                <svg
                  width="100%"
                  viewBox="0 0 780 780"
                  enable-background="new 0 0 780 500"
                  version="1.1"
                  xml:space="preserve"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="#000000"
                >
                  <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                  <g
                    id="SVGRepo_tracerCarrier"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  ></g>
                  <g id="SVGRepo_iconCarrier">
                    <rect width="780" height="460" fill="#0E4595"></rect>
                    <path
                      d="m293.2 348.73l33.361-195.76h53.36l-33.385 195.76h-53.336zm246.11-191.54c-10.57-3.966-27.137-8.222-47.822-8.222-52.725 0-89.865 26.55-90.18 64.603-0.299 28.13 26.514 43.822 46.752 53.186 20.771 9.595 27.752 15.714 27.654 24.283-0.131 13.121-16.586 19.116-31.922 19.116-21.357 0-32.703-2.967-50.227-10.276l-6.876-3.11-7.489 43.823c12.463 5.464 35.51 10.198 59.438 10.443 56.09 0 92.5-26.246 92.916-66.882 0.199-22.269-14.016-39.216-44.801-53.188-18.65-9.055-30.072-15.099-29.951-24.268 0-8.137 9.668-16.839 30.557-16.839 17.449-0.27 30.09 3.535 39.938 7.5l4.781 2.26 7.232-42.429m137.31-4.223h-41.232c-12.773 0-22.332 3.487-27.941 16.234l-79.244 179.4h56.031s9.16-24.123 11.232-29.418c6.125 0 60.555 0.084 68.338 0.084 1.596 6.853 6.49 29.334 6.49 29.334h49.514l-43.188-195.64zm-65.418 126.41c4.412-11.279 21.26-54.723 21.26-54.723-0.316 0.522 4.379-11.334 7.074-18.684l3.605 16.879s10.219 46.729 12.354 56.528h-44.293zm-363.3-126.41l-52.24 133.5-5.567-27.13c-9.725-31.273-40.025-65.155-73.898-82.118l47.766 171.2 56.456-0.064 84.004-195.39h-56.521"
                      fill="#ffffff"
                    ></path>
                    <path
                      d="m146.92 152.96h-86.041l-0.681 4.073c66.938 16.204 111.23 55.363 129.62 102.41l-18.71-89.96c-3.23-12.395-12.597-16.094-24.186-16.527"
                      fill="#F2AE14"
                    ></path>
                  </g>
                </svg>
              </div>
              <div
                class="border rounded-sm border-black"
                style="width: 50px; height: 30px"
              >
                <svg
                  version="1.1"
                  id="Layer_1"
                  xmlns:sketch="http://www.bohemiancoding.com/sketch/ns"
                  xmlns="http://www.w3.org/2000/svg"
                  xmlns:xlink="http://www.w3.org/1999/xlink"
                  width="100%"
                  viewBox="0 0 750 471"
                  enable-background="new 0 0 750 471"
                  xml:space="preserve"
                  fill="#000000"
                >
                  <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                  <g
                    id="SVGRepo_tracerCarrier"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  ></g>
                  <g id="SVGRepo_iconCarrier">
                    <title>Slice 1</title>
                    <desc>Created with Sketch.</desc>
                    <g id="Page-1" sketch:type="MSPage">
                      <g id="mastercard" sketch:type="MSLayerGroup">
                        <path
                          id="Fill-1"
                          sketch:type="MSShapeGroup"
                          fill="#D9222A"
                          d="M434.008,235.5c0,99.142-80.371,179.504-179.508,179.504 C155.363,415.004,75,334.642,75,235.5c0-99.133,80.362-179.504,179.5-179.504C353.637,55.996,434.008,136.367,434.008,235.5"
                        ></path>
                        <path
                          id="Fill-2"
                          sketch:type="MSShapeGroup"
                          fill="#EE9F2D"
                          d="M495.491,55.996c-46.379,0-88.642,17.596-120.5,46.467 c-6.487,5.883-12.546,12.238-18.125,18.996h36.267c4.958,6.029,9.525,12.371,13.684,19.012h-63.634 c-3.813,6.104-7.274,12.446-10.342,19.008h84.313c2.88,6.159,5.421,12.496,7.601,19.004h-99.513 c-2.075,6.191-3.821,12.529-5.217,19.008h109.941c2.638,12.25,4.042,24.967,4.042,38.008c0,19.934-3.254,39.112-9.254,57.021 h-99.513c2.175,6.512,4.717,12.854,7.596,19.008h84.316c-3.074,6.563-6.528,12.904-10.346,19.012h-63.625 c4.154,6.63,8.729,12.98,13.684,18.996h36.259c-5.571,6.771-11.634,13.134-18.13,19.013 c31.858,28.866,74.117,46.454,120.496,46.454C594.629,415.004,675,334.642,675,235.5C675,136.371,594.629,55.996,495.491,55.996"
                        ></path>
                        <path
                          id="Fill-3"
                          sketch:type="MSShapeGroup"
                          d="M651.074,335.559c0-3.2,2.592-5.801,5.796-5.801s5.796,2.601,5.796,5.801 c0,3.199-2.592,5.8-5.796,5.8S651.074,338.758,651.074,335.559L651.074,335.559L651.074,335.559z M656.87,339.967 c2.434,0,4.408-1.975,4.408-4.408c0-2.438-1.975-4.404-4.408-4.404c-2.438,0-4.404,1.967-4.404,4.404 C652.466,337.992,654.433,339.967,656.87,339.967L656.87,339.967L656.87,339.967z M656.087,338.108H654.9v-5.096h2.15 c0.45,0,0.908,0,1.304,0.254c0.413,0.279,0.646,0.771,0.646,1.279c0,0.571-0.338,1.104-0.884,1.313l0.938,2.25h-1.316 l-0.779-2.017h-0.871V338.108L656.087,338.108z M656.087,335.217h0.658c0.246,0,0.505,0.021,0.726-0.1 c0.195-0.125,0.296-0.359,0.296-0.584c0-0.195-0.121-0.421-0.288-0.517c-0.208-0.129-0.537-0.101-0.758-0.101h-0.634V335.217 L656.087,335.217z"
                        ></path>
                        <path
                          id="Fill-4"
                          sketch:type="MSShapeGroup"
                          d="M212.587,255.154c-2.046-0.238-2.945-0.301-4.35-0.301 c-11.046,0-16.638,3.788-16.638,11.268c0,4.612,2.729,7.545,6.987,7.545C206.525,273.666,212.245,266.108,212.587,255.154 L212.587,255.154L212.587,255.154z M226.758,288.15h-16.146l0.371-7.676c-4.926,6.066-11.496,8.95-20.426,8.95 c-10.563,0-17.804-8.25-17.804-20.229c0-18.024,12.596-28.541,34.217-28.541c2.208,0,5.042,0.199,7.941,0.57 c0.604-2.441,0.763-3.487,0.763-4.8c0-4.908-3.396-6.737-12.5-6.737c-9.533-0.108-17.396,2.271-20.625,3.333 c0.204-1.229,2.7-16.659,2.7-16.659c9.712-2.846,16.116-3.917,23.325-3.917c16.732,0,25.596,7.513,25.579,21.712 c0.033,3.805-0.597,8.5-1.579,14.671C230.883,259.559,227.254,282.546,226.758,288.15L226.758,288.15L226.758,288.15z"
                        ></path>
                        <path
                          id="Fill-5"
                          sketch:type="MSShapeGroup"
                          d="M164.6,288.15h-19.487l11.162-69.996L131.35,288.15h-13.279l-1.642-69.596 l-11.733,69.596H86.454l15.237-91.055h28.021l1.7,50.967l17.092-50.967h31.167L164.6,288.15"
                        ></path>
                        <path
                          id="Fill-6"
                          sketch:type="MSShapeGroup"
                          d="M519.574,255.154c-2.037-0.238-2.941-0.301-4.342-0.301 c-11.041,0-16.633,3.788-16.633,11.268c0,4.612,2.725,7.545,6.983,7.545C513.521,273.666,519.245,266.108,519.574,255.154 L519.574,255.154L519.574,255.154z M533.758,288.15h-16.146l0.366-7.676c-4.925,6.066-11.5,8.95-20.421,8.95 c-10.566,0-17.8-8.25-17.8-20.229c0-18.024,12.588-28.541,34.213-28.541c2.208,0,5.037,0.199,7.933,0.57 c0.604-2.441,0.763-3.487,0.763-4.8c0-4.908-3.392-6.737-12.496-6.737c-9.533-0.108-17.387,2.271-20.629,3.333 c0.204-1.229,2.709-16.659,2.709-16.659c9.712-2.846,16.112-3.917,23.313-3.917c16.741,0,25.604,7.513,25.587,21.712 c0.033,3.805-0.596,8.5-1.579,14.671C537.887,259.559,534.25,282.546,533.758,288.15L533.758,288.15L533.758,288.15z"
                        ></path>
                        <path
                          id="Fill-7"
                          sketch:type="MSShapeGroup"
                          d="M313.366,287.025c-5.333,1.679-9.491,2.399-14,2.399 c-9.962,0-15.399-5.725-15.399-16.267c-0.142-3.271,1.433-11.879,2.671-19.737c1.125-6.917,8.449-50.529,8.449-50.529h19.371 l-2.263,11.208h11.7l-2.642,17.796h-11.742c-2.25,14.083-5.454,31.625-5.491,33.95c0,3.816,2.037,5.483,6.671,5.483 c2.221,0,3.941-0.226,5.254-0.7L313.366,287.025"
                        ></path>
                        <path
                          id="Fill-8"
                          sketch:type="MSShapeGroup"
                          d="M372.758,286.425c-6.654,2.034-13.075,3.017-19.879,3 c-21.684-0.021-32.987-11.346-32.987-33.033c0-25.313,14.379-43.946,33.899-43.946c15.971,0,26.171,10.433,26.171,26.796 c0,5.429-0.7,10.729-2.388,18.212H339c-1.305,10.741,5.57,15.217,16.837,15.217c6.934,0,13.188-1.429,20.142-4.663 L372.758,286.425L372.758,286.425z M361.87,242.525c0.108-1.542,2.055-13.217-9.013-13.217c-6.17,0-10.583,4.704-12.379,13.217 H361.87L361.87,242.525z"
                        ></path>
                        <path
                          id="Fill-9"
                          sketch:type="MSShapeGroup"
                          d="M238.446,237.508c0,9.367,4.542,15.826,14.842,20.676 c7.892,3.708,9.112,4.809,9.112,8.17c0,4.617-3.479,6.701-11.191,6.701c-5.813,0-11.221-0.908-17.458-2.922 c0,0-2.563,16.321-2.68,17.101c4.43,0.967,8.38,1.862,20.279,2.191c20.563,0,30.059-7.829,30.059-24.75 c0-10.175-3.976-16.146-13.737-20.634c-8.171-3.75-9.108-4.587-9.108-8.045c0-4.004,3.237-6.046,9.537-6.046 c3.825,0,9.05,0.408,14,1.112l2.775-17.175c-5.046-0.8-12.696-1.442-17.15-1.442C245.925,212.446,238.379,223.833,238.446,237.508 "
                        ></path>
                        <path
                          id="Fill-10"
                          sketch:type="MSShapeGroup"
                          d="M467.533,214.392c5.412,0,10.458,1.421,17.412,4.921l3.188-19.763 c-2.854-1.121-12.904-7.7-21.417-7.7c-13.041,0-24.066,6.471-31.82,17.15c-11.309-3.746-15.958,3.825-21.658,11.367l-5.063,1.179 c0.383-2.483,0.729-4.95,0.612-7.446h-17.896c-2.446,22.917-6.779,46.128-10.171,69.075l-0.884,4.976h19.496 c3.254-21.143,5.037-34.68,6.121-43.842l7.341-4.084c1.097-4.078,4.529-5.458,11.417-5.291c-0.899,4.833-1.383,9.916-1.383,15.184 c0,24.225,13.07,39.308,34.05,39.308c5.404,0,10.041-0.712,17.221-2.658l3.429-20.759c-6.458,3.18-11.758,4.676-16.558,4.676 c-11.329,0-18.184-8.363-18.184-22.184C442.787,228.45,452.983,214.392,467.533,214.392"
                        ></path>
                        <path
                          id="Fill-12"
                          sketch:type="MSShapeGroup"
                          fill="#FFFFFF"
                          d="M170.208,282.742h-19.491l11.171-69.988l-24.926,69.988h-13.283 l-1.642-69.588l-11.733,69.588H92.063L107.3,191.7h28.021l0.788,56.362l18.904-56.362h30.267L170.208,282.742"
                        ></path>
                        <path
                          id="Fill-11"
                          sketch:type="MSShapeGroup"
                          d="M632.521,197.096l-4.321,26.309c-5.329-7.013-11.054-12.088-18.612-12.088 c-9.833,0-18.783,7.455-24.642,18.425c-8.158-1.692-16.596-4.563-16.596-4.563l-0.004,0.067c0.658-6.134,0.921-9.875,0.862-11.146 h-17.9c-2.438,22.917-6.771,46.128-10.158,69.075l-0.892,4.976h19.492c2.633-17.096,4.649-31.292,6.133-42.551 c6.658-6.016,9.992-11.266,16.721-10.916c-2.979,7.204-4.725,15.504-4.725,24.017c0,18.513,9.366,30.725,23.533,30.725 c7.142,0,12.621-2.462,17.967-8.171l-0.913,6.884H636.9l14.842-91.042H632.521L632.521,197.096z M608.15,271.037 c-6.633,0-9.983-4.908-9.983-14.596c0-14.554,6.271-24.875,15.112-24.875c6.696,0,10.321,5.104,10.321,14.509 C623.6,260.754,617.229,271.037,608.15,271.037L608.15,271.037L608.15,271.037z"
                        ></path>
                        <path
                          id="Fill-13"
                          sketch:type="MSShapeGroup"
                          fill="#FFFFFF"
                          d="M218.192,249.758c-2.042-0.236-2.946-0.299-4.346-0.299 c-11.046,0-16.634,3.787-16.634,11.266c0,4.604,2.729,7.547,6.979,7.547C212.138,268.271,217.859,260.713,218.192,249.758 L218.192,249.758L218.192,249.758z M232.37,282.742h-16.146l0.367-7.663c-4.921,6.054-11.5,8.95-20.421,8.95 c-10.567,0-17.805-8.25-17.805-20.229c0-18.033,12.592-28.542,34.217-28.542c2.208,0,5.042,0.2,7.938,0.571 c0.604-2.441,0.763-3.487,0.763-4.808c0-4.909-3.392-6.729-12.496-6.729c-9.537-0.108-17.396,2.271-20.629,3.321 c0.204-1.225,2.7-16.637,2.7-16.637c9.708-2.858,16.12-3.929,23.32-3.929c16.737,0,25.604,7.517,25.588,21.704 c0.029,3.821-0.604,8.513-1.584,14.675C236.495,254.15,232.863,277.15,232.37,282.742L232.37,282.742L232.37,282.742z"
                        ></path>
                        <path
                          id="Fill-14"
                          sketch:type="MSShapeGroup"
                          fill="#FFFFFF"
                          d="M493.745,194.15l-3.191,19.767c-6.95-3.496-12-4.92-17.408-4.92 c-14.55,0-24.75,14.058-24.75,34.107c0,13.821,6.858,22.18,18.184,22.18c4.8,0,10.096-1.492,16.554-4.675l-3.421,20.75 c-7.184,1.958-11.816,2.671-17.225,2.671c-20.976,0-34.05-15.084-34.05-39.309c0-32.55,18.058-55.3,43.887-55.3 C480.833,189.421,490.887,193.029,493.745,194.15"
                        ></path>
                        <path
                          id="Fill-15"
                          sketch:type="MSShapeGroup"
                          fill="#FFFFFF"
                          d="M525.188,249.758c-2.042-0.236-2.942-0.299-4.347-0.299 c-11.041,0-16.633,3.787-16.633,11.266c0,4.604,2.729,7.547,6.983,7.547C519.129,268.271,524.854,260.713,525.188,249.758 L525.188,249.758L525.188,249.758z M539.366,282.742h-16.15l0.371-7.663c-4.925,6.054-11.5,8.95-20.421,8.95 c-10.563,0-17.804-8.25-17.804-20.229c0-18.033,12.596-28.542,34.212-28.542c2.213,0,5.042,0.2,7.942,0.571 c0.6-2.441,0.762-3.487,0.762-4.808c0-4.909-3.392-6.729-12.495-6.729c-9.533-0.108-17.396,2.271-20.63,3.321 c0.204-1.225,2.704-16.637,2.704-16.637c9.709-2.858,16.117-3.929,23.317-3.929c16.741,0,25.604,7.517,25.583,21.704 c0.033,3.821-0.596,8.513-1.579,14.675C543.495,254.15,539.854,277.15,539.366,282.742L539.366,282.742L539.366,282.742z"
                        ></path>
                        <path
                          id="Fill-16"
                          sketch:type="MSShapeGroup"
                          fill="#FFFFFF"
                          d="M318.975,281.621c-5.338,1.679-9.496,2.408-14,2.408 c-9.962,0-15.399-5.725-15.399-16.267c-0.138-3.279,1.438-11.88,2.675-19.737c1.12-6.926,8.445-50.534,8.445-50.534h19.367 l-2.259,11.212h9.941l-2.646,17.788h-9.975c-2.25,14.092-5.463,31.621-5.496,33.95c0,3.83,2.041,5.483,6.671,5.483 c2.221,0,3.938-0.216,5.254-0.691L318.975,281.621"
                        ></path>
                        <path
                          id="Fill-17"
                          sketch:type="MSShapeGroup"
                          fill="#FFFFFF"
                          d="M378.366,281.029c-6.65,2.033-13.079,3.012-19.879,3 c-21.684-0.021-32.987-11.346-32.987-33.033c0-25.321,14.379-43.95,33.899-43.95c15.971,0,26.171,10.429,26.171,26.8 c0,5.434-0.7,10.733-2.383,18.213h-38.575c-1.305,10.741,5.57,15.221,16.837,15.221c6.93,0,13.188-1.434,20.138-4.676 L378.366,281.029L378.366,281.029z M367.475,237.117c0.116-1.538,2.059-13.217-9.013-13.217c-6.167,0-10.579,4.717-12.375,13.217 H367.475L367.475,237.117z"
                        ></path>
                        <path
                          id="Fill-18"
                          sketch:type="MSShapeGroup"
                          fill="#FFFFFF"
                          d="M244.054,232.112c0,9.366,4.542,15.817,14.842,20.675 c7.892,3.709,9.112,4.813,9.112,8.172c0,4.616-3.483,6.699-11.188,6.699c-5.816,0-11.225-0.908-17.467-2.921 c0,0-2.554,16.321-2.671,17.101c4.421,0.967,8.375,1.85,20.275,2.191c20.566,0,30.059-7.829,30.059-24.746 c0-10.18-3.971-16.15-13.737-20.637c-8.167-3.759-9.113-4.584-9.113-8.046c0-4,3.246-6.059,9.542-6.059 c3.821,0,9.046,0.421,14.004,1.125l2.771-17.179c-5.042-0.8-12.692-1.441-17.146-1.441 C251.533,207.046,243.991,218.425,244.054,232.112"
                        ></path>
                        <path
                          id="Fill-19"
                          sketch:type="MSShapeGroup"
                          fill="#FFFFFF"
                          d="M642.508,282.742h-18.438l0.917-6.893 c-5.346,5.717-10.825,8.18-17.967,8.18c-14.167,0-23.529-12.213-23.529-30.725c0-24.63,14.521-45.392,31.708-45.392 c7.559,0,13.279,3.087,18.604,10.096l4.325-26.308h19.221L642.508,282.742L642.508,282.742z M613.762,265.633 c9.075,0,15.45-10.283,15.45-24.953c0-9.405-3.629-14.509-10.325-14.509c-8.837,0-15.116,10.316-15.116,24.875 C603.771,260.733,607.129,265.633,613.762,265.633L613.762,265.633L613.762,265.633z"
                        ></path>
                        <path
                          id="Fill-20"
                          sketch:type="MSShapeGroup"
                          fill="#FFFFFF"
                          d="M556.92,208.704c-2.441,22.917-6.774,46.13-10.162,69.063 l-0.892,4.976h19.491c6.972-45.275,8.659-54.117,19.588-53.009c1.742-9.267,4.983-17.383,7.4-21.479 c-8.163-1.7-12.721,2.913-18.688,11.675c0.471-3.788,1.333-7.467,1.162-11.225H556.92"
                        ></path>
                        <path
                          id="Fill-21"
                          sketch:type="MSShapeGroup"
                          fill="#FFFFFF"
                          d="M396.5,208.704c-2.446,22.917-6.779,46.13-10.167,69.063 l-0.888,4.976h19.5c6.963-45.275,8.646-54.117,19.571-53.009c1.75-9.267,4.991-17.383,7.399-21.479 c-8.154-1.7-12.717,2.913-18.679,11.675c0.471-3.788,1.325-7.467,1.162-11.225H396.5"
                        ></path>
                        <path
                          id="Fill-22"
                          sketch:type="MSShapeGroup"
                          fill="#FFFFFF"
                          d="M651.067,276.945c0-3.212,2.596-5.799,5.796-5.799 c3.204,0,5.796,2.587,5.796,5.799c0,3.196-2.592,5.797-5.796,5.797C653.662,282.742,651.067,280.142,651.067,276.945 L651.067,276.945L651.067,276.945z M656.863,281.35c2.438,0,4.404-1.975,4.404-4.404c0-2.433-1.967-4.408-4.404-4.408 c-2.434,0-4.408,1.976-4.408,4.408C652.454,279.375,654.429,281.35,656.863,281.35L656.863,281.35L656.863,281.35z M656.079,279.479h-1.188v-5.082h2.153c0.446,0,0.909,0.008,1.296,0.254c0.417,0.283,0.654,0.766,0.654,1.274 c0,0.575-0.337,1.112-0.888,1.317l0.942,2.236h-1.321l-0.779-2.008h-0.87V279.479L656.079,279.479z M656.079,276.6h0.653 c0.246,0,0.513,0.018,0.729-0.1c0.196-0.125,0.296-0.362,0.296-0.588c0-0.188-0.116-0.412-0.287-0.524 c-0.204-0.116-0.542-0.083-0.763-0.083h-0.629V276.6L656.079,276.6z"
                        ></path>
                      </g>
                    </g>
                  </g>
                </svg>
              </div>
              <div
                class="border rounded-sm border-black pa-1"
                style="width: 50px; height: 30px"
              >
                <svg
                  width="100%"
                  viewBox="0 -49 512 512"
                  version="1.1"
                  xmlns="http://www.w3.org/2000/svg"
                  xmlns:xlink="http://www.w3.org/1999/xlink"
                  preserveAspectRatio="xMidYMid"
                  fill="#000000"
                >
                  <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                  <g
                    id="SVGRepo_tracerCarrier"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  ></g>
                  <g id="SVGRepo_iconCarrier">
                    <g>
                      <path
                        d="M35.9822222,83.4844444 C35.9822222,77.9377778 40.5333333,75.8044444 48.0711111,75.8044444 C58.88,75.8044444 72.5333333,79.0755556 83.3422222,84.9066667 L83.3422222,51.4844444 C71.5377778,46.7911111 59.8755556,44.9422222 48.0711111,44.9422222 C19.2,44.9422222 0,60.0177778 0,85.1911111 C0,124.444444 54.0444444,118.186667 54.0444444,135.111111 C54.0444444,141.653333 48.3555556,143.786667 40.3911111,143.786667 C28.5866667,143.786667 13.5111111,138.951111 1.56444444,132.408889 L1.56444444,166.257778 C14.7911111,171.946667 28.16,174.364444 40.3911111,174.364444 C69.9733333,174.364444 90.3111111,159.715556 90.3111111,134.257778 C90.1688889,91.8755556 35.9822222,99.4133333 35.9822222,83.4844444 Z M132.124444,16.4977778 L97.4222222,23.8933333 L97.28,137.813333 C97.28,158.862222 113.066667,174.364444 134.115556,174.364444 C145.777778,174.364444 154.311111,172.231111 159.004444,169.671111 L159.004444,140.8 C154.453333,142.648889 131.982222,149.191111 131.982222,128.142222 L131.982222,77.6533333 L159.004444,77.6533333 L159.004444,47.36 L131.982222,47.36 L132.124444,16.4977778 Z M203.235556,57.8844444 L200.96,47.36 L170.24,47.36 L170.24,171.804444 L205.795556,171.804444 L205.795556,87.4666667 C214.186667,76.5155556 228.408889,78.5066667 232.817778,80.0711111 L232.817778,47.36 C228.266667,45.6533333 211.626667,42.5244444 203.235556,57.8844444 Z M241.493333,47.36 L277.191111,47.36 L277.191111,171.804444 L241.493333,171.804444 L241.493333,47.36 Z M241.493333,36.5511111 L277.191111,28.8711111 L277.191111,0 L241.493333,7.53777778 L241.493333,36.5511111 Z M351.431111,44.9422222 C337.493333,44.9422222 328.533333,51.4844444 323.555556,56.0355556 L321.706667,47.2177778 L290.417778,47.2177778 L290.417778,213.048889 L325.973333,205.511111 L326.115556,165.262222 C331.235556,168.96 338.773333,174.222222 351.288889,174.222222 C376.746667,174.222222 399.928889,153.742222 399.928889,108.657778 C399.786667,67.4133333 376.32,44.9422222 351.431111,44.9422222 Z M342.897778,142.933333 C334.506667,142.933333 329.528889,139.946667 326.115556,136.248889 L325.973333,83.4844444 C329.671111,79.36 334.791111,76.5155556 342.897778,76.5155556 C355.84,76.5155556 364.8,91.0222222 364.8,109.653333 C364.8,128.711111 355.982222,142.933333 342.897778,142.933333 Z M512,110.08 C512,73.6711111 494.364444,44.9422222 460.657778,44.9422222 C426.808889,44.9422222 406.328889,73.6711111 406.328889,109.795556 C406.328889,152.604444 430.506667,174.222222 465.208889,174.222222 C482.133333,174.222222 494.933333,170.382222 504.604444,164.977778 L504.604444,136.533333 C494.933333,141.368889 483.84,144.355556 469.76,144.355556 C455.964444,144.355556 443.733333,139.52 442.168889,122.737778 L511.715556,122.737778 C511.715556,120.888889 512,113.493333 512,110.08 Z M441.742222,96.5688889 C441.742222,80.4977778 451.555556,73.8133333 460.515556,73.8133333 C469.191111,73.8133333 478.435556,80.4977778 478.435556,96.5688889 L441.742222,96.5688889 L441.742222,96.5688889 Z"
                        fill="#6772E5"
                      ></path>
                    </g>
                  </g>
                </svg>
              </div>
              <div
                class="border rounded-sm border-black pa-1"
                style="width: 50px; height: 30px"
              >
                <svg
                  width="100%"
                  viewBox="0 9 750 750"
                  version="1.1"
                  xmlns="http://www.w3.org/2000/svg"
                  xmlns:xlink="http://www.w3.org/1999/xlink"
                  fill="#000000"
                >
                  <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                  <g
                    id="SVGRepo_tracerCarrier"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  ></g>
                  <g id="SVGRepo_iconCarrier">
                    <desc>Created with Sketch.</desc>
                    <defs></defs>
                    <g
                      id="Page-1"
                      stroke="none"
                      stroke-width="1"
                      fill="none"
                      fill-rule="evenodd"
                    >
                      <g id="paypal" fill-rule="nonzero">
                        <path
                          d="M697.115385,0 L52.8846154,0 C23.7240385,0 0,23.1955749 0,51.7065868 L0,419.293413 C0,447.804425 23.7240385,471 52.8846154,471 L697.115385,471 C726.274038,471 750,447.804425 750,419.293413 L750,51.7065868 C750,23.1955749 726.274038,0 697.115385,0 Z"
                          id="Shape"
                          fill="#FFFFFF"
                        ></path>
                        <g
                          id="Group"
                          transform="translate(54.000000, 150.000000)"
                        >
                          <path
                            d="M109.272795,8.45777679 C101.24875,2.94154464 90.7780357,0.176741071 77.8606518,0.176741071 L27.8515268,0.176741071 C23.8915714,0.176741071 21.7038036,2.15719643 21.2882232,6.11333036 L0.972553571,133.638223 C0.761419643,134.890696 1.07477679,136.03617 1.90975893,137.077509 C2.73996429,138.120759 3.78416964,138.639518 5.03473214,138.639518 L28.7887321,138.639518 C32.9550446,138.639518 35.2450357,136.663839 35.6653929,132.701973 L41.2905357,98.3224911 C41.4959375,96.6563482 42.2286964,95.3016518 43.4792589,94.2584018 C44.7288661,93.2170625 46.2918304,92.5358929 48.1671964,92.2234911 C50.0425625,91.9139554 51.8109286,91.7582321 53.4808929,91.7582321 C55.1460804,91.7582321 57.124625,91.8633214 59.4203482,92.0706339 C61.7103393,92.2789018 63.170125,92.3801696 63.7958839,92.3801696 C81.7145625,92.3801696 95.7793304,87.3311071 105.991143,77.2224732 C116.198179,67.1176607 121.307429,53.1054375 121.307429,35.1829375 C121.307429,22.8903571 117.293018,13.9826071 109.272795,8.45777679 Z M83.4877054,46.7484911 C82.4425446,54.0426429 79.7369732,58.8328036 75.3614375,61.1256607 C70.9849464,63.4213839 64.7340446,64.5620804 56.6087321,64.5620804 L46.2937411,64.8754375 L51.6083929,31.43125 C52.0230179,29.1412589 53.3767589,27.9948304 55.6705714,27.9948304 L61.6109821,27.9948304 C69.9416964,27.9948304 75.9881518,29.1957143 79.7388839,31.5879286 C83.4877054,33.985875 84.7382679,39.041625 83.4877054,46.7484911 Z"
                            id="Shape"
                            fill="#003087"
                          ></path>
                          <path
                            d="M637.026411,0.176741071 L613.899125,0.176741071 C611.601491,0.176741071 610.248705,1.32316964 609.835991,3.61507143 L589.518411,133.638223 L589.205054,134.263027 C589.205054,135.310098 589.622545,136.295071 590.457527,137.233232 C591.286777,138.169482 592.332893,138.638562 593.581545,138.638562 L614.212482,138.638562 C618.16575,138.638562 620.354473,136.662884 620.776741,132.701018 L641.092411,4.86276786 L641.092411,4.55227679 C641.091455,1.63557143 639.732938,0.176741071 637.026411,0.176741071 Z"
                            id="Shape"
                            fill="#009CDE"
                          ></path>
                          <path
                            d="M357.599732,50.4973125 C357.599732,49.4578839 357.18033,48.4662232 356.352036,47.5299732 C355.516098,46.5927679 354.576982,46.1217768 353.538509,46.1217768 L329.471152,46.1217768 C327.174473,46.1217768 325.300063,47.1688482 323.845054,49.24675 L290.714223,98.0081786 L276.962812,51.1240268 C275.916696,47.7917411 273.62575,46.1217768 270.086152,46.1217768 L246.641687,46.1217768 C245.597482,46.1217768 244.659321,46.5918125 243.831027,47.5299732 C242.995089,48.4662232 242.580464,49.4588393 242.580464,50.4973125 C242.580464,50.9176696 244.612509,57.0615714 248.674687,68.9385714 C252.736866,80.8174821 257.113357,93.6326429 261.80225,107.38692 C266.491143,121.137375 268.936857,128.434393 269.147036,129.262688 C252.059518,152.602063 243.51767,165.104821 243.51767,166.769054 C243.51767,169.480357 244.871411,170.833143 247.580804,170.833143 L271.648161,170.833143 C273.940062,170.833143 275.814473,169.793714 277.274259,167.709125 L356.976839,52.6850804 C357.391464,52.2704554 357.599732,51.5443839 357.599732,50.4973125 Z"
                            id="Shape"
                            fill="#003087"
                          ></path>
                          <path
                            d="M581.704545,46.1217768 L557.948634,46.1217768 C555.030018,46.1217768 553.263562,49.5601071 552.638759,56.4367679 C547.215196,48.1060536 537.323429,43.9330536 522.943393,43.9330536 C507.940464,43.9330536 495.174982,49.5601071 484.655545,60.8123036 C474.13133,72.0654554 468.872089,85.2990625 468.872089,100.508348 C468.872089,112.80475 472.465187,122.597161 479.653295,129.887491 C486.842357,137.185464 496.479045,140.827286 508.568134,140.827286 C514.608857,140.827286 520.755625,139.574813 527.006527,137.076554 C533.258384,134.576384 538.150768,131.244098 541.698964,127.07492 C541.698964,127.284143 541.486875,128.220393 541.073205,129.886536 C540.652848,131.5565 540.447446,132.808973 540.447446,133.637268 C540.447446,136.975286 541.798321,138.637607 544.511536,138.637607 L566.079679,138.637607 C570.032946,138.637607 572.32867,136.661929 572.952518,132.700063 L585.768634,51.1230714 C585.974036,49.8725089 585.661634,48.7279911 584.830473,47.6847411 C583.994536,46.6443571 582.955107,46.1217768 581.704545,46.1217768 Z M540.916527,107.696455 C535.60283,112.906018 529.196205,115.509366 521.694741,115.509366 C515.649241,115.509366 510.756857,113.845134 507.004214,110.509027 C503.252527,107.180563 501.377161,102.595804 501.377161,96.7566607 C501.377161,89.0517054 503.981464,82.5361696 509.191982,77.2224732 C514.395812,71.9087768 520.860714,69.2519286 528.571402,69.2519286 C534.400036,69.2519286 539.245607,70.9715714 543.104295,74.4089464 C546.956295,77.8472768 548.888027,82.5896696 548.888027,88.6323036 C548.887071,96.1328125 546.229268,102.489759 540.916527,107.696455 Z"
                            id="Shape"
                            fill="#009CDE"
                          ></path>
                          <path
                            d="M226.639375,46.1217768 L202.885375,46.1217768 C199.963893,46.1217768 198.196482,49.5601071 197.570723,56.4367679 C191.944625,48.1060536 182.04617,43.9330536 167.877268,43.9330536 C152.874339,43.9330536 140.109813,49.5601071 129.588464,60.8123036 C119.06425,72.0654554 113.805009,85.2990625 113.805009,100.508348 C113.805009,112.80475 117.400018,122.597161 124.58908,129.887491 C131.778143,137.185464 141.41292,140.827286 153.500098,140.827286 C159.331598,140.827286 165.378054,139.574813 171.628,137.076554 C177.878902,134.576384 182.880196,131.244098 186.630929,127.07492 C185.794991,129.575089 185.380366,131.763813 185.380366,133.637268 C185.380366,136.975286 186.734107,138.637607 189.4435,138.637607 L211.009732,138.637607 C214.965866,138.637607 217.260634,136.661929 217.886393,132.700063 L230.700598,51.1230714 C230.906,49.8725089 230.593598,48.7279911 229.763393,47.6847411 C228.929366,46.6443571 227.888982,46.1217768 226.639375,46.1217768 Z M185.850402,107.851223 C180.53575,112.962384 174.02117,115.509366 166.316214,115.509366 C160.269759,115.509366 155.425143,113.845134 151.781411,110.509027 C148.132902,107.180563 146.311036,102.595804 146.311036,96.7566607 C146.311036,89.0517054 148.914384,82.5361696 154.125857,77.2224732 C159.331598,71.9087768 165.791723,69.2519286 173.504321,69.2519286 C179.335821,69.2519286 184.180437,70.9715714 188.039125,74.4089464 C191.891125,77.8472768 193.820946,82.5896696 193.820946,88.6323036 C193.820946,96.3420357 191.164098,102.751527 185.850402,107.851223 Z"
                            id="Shape"
                            fill="#003087"
                          ></path>
                          <path
                            d="M464.337964,8.45777679 C456.314875,2.94154464 445.846071,0.176741071 432.926777,0.176741071 L383.230054,0.176741071 C379.05992,0.176741071 376.767062,2.15719643 376.353393,6.11333036 L356.037723,133.637268 C355.826589,134.889741 356.138991,136.035214 356.974929,137.076554 C357.802268,138.119804 358.849339,138.638563 360.099902,138.638563 L385.728312,138.638563 C388.228482,138.638563 389.894625,137.285777 390.729607,134.576384 L396.356661,98.3215357 C396.563018,96.6553929 397.292911,95.3006964 398.544429,94.2574464 C399.794991,93.2161071 401.356045,92.5349375 403.233321,92.2225357 C405.107732,91.913 406.876098,91.7572768 408.547018,91.7572768 C410.212205,91.7572768 412.19075,91.8623661 414.483607,92.0696786 C416.775509,92.2779464 418.238161,92.3792143 418.859143,92.3792143 C436.780687,92.3792143 450.843545,87.3301518 461.055357,77.2215179 C471.265259,67.1167054 476.370687,53.1044821 476.370687,35.1819821 C476.371643,22.8903571 472.358187,13.9826071 464.337964,8.45777679 Z M432.301018,59.8750982 C427.716259,63.0000714 420.839598,64.5620804 411.672946,64.5620804 L401.670357,64.8754375 L406.985009,31.43125 C407.397723,29.1412589 408.751464,27.9948304 411.047187,27.9948304 L416.671375,27.9948304 C421.254223,27.9948304 424.900821,28.2030982 427.614036,28.6186786 C430.318652,29.0390357 432.926777,30.3373661 435.426946,32.5251339 C437.929027,34.7138571 439.177679,37.8923304 439.177679,42.0595982 C439.177679,50.8106696 436.882911,56.7482143 432.301018,59.8750982 Z"
                            id="Shape"
                            fill="#009CDE"
                          ></path>
                        </g>
                      </g>
                    </g>
                  </g>
                </svg>
              </div>
            </div>
            <NuxtImg
              v-if="countryCode == 'BD'"
              src="/ssl_banks.webp"
              alt="ssl commerce"
              loading="lazy"
              sizes="xs:100vw sm:100vw md:100vw"
              format="webp"
              class="mt-3 w-100"
              fit="cover"
              quality="85"
            />
          </div>
          <div
            v-if="props?.countryCode != 'BD'"
            class="courier-partner-wrapper text-center text-md-start"
          >
            <h4
              class="footer-widget-title text-center text-md-left mb-3 text-uppercase"
            >
              {{ $t("Courier partner") }}
            </h4>

            <NuxtImg
              :src="
                replaceMediaDomain('https://pantoneclos3bucket.s3.ap-south-1.amazonaws.com/pantoneclo-img/courier.png') ||
                'https://pantoneclos3bucket.s3.ap-south-1.amazonaws.com/pantoneclo-img/courier.png'
              "
              alt="Pantoneclo partner"
              loading="lazy"
              sizes="xs:100vw sm:100vw md:100vw"
              format="webp"
              width="391"
              height="32"
              class="h-100"
              style="max-width: 100%"
              fit="cover"
              quality="85"
            />
          </div>
        </v-col>
      </v-row>
    </v-container>
  </section>
</template>
<script setup lang="ts">
import { storeToRefs } from "pinia";
import { computed } from "vue";
import { useMediaType } from "../../../composables/useMediaType";

const props = defineProps({
  countryCode: null,
});
const { replaceMediaDomain } = useMediaType();
const localePath = useLocalePath();
const { authenticated } = storeToRefs(useAuthStore());

const quickLinks = [
  // {name: 'Support policy', to: "/support-policy"},
  { name: "Return & Refund Policy", to: "/return-refund" },
  { name: "Terms & Conditions", to: "/terms-conditions" },
  { name: "Privacy & Cookies Policy", to: "/privacy-policy" },
  // {name: 'Legal Documents', to: "/legal-documents"},
  { name: "About us", to: "/about-us" },
  { name: "Frequently asked questions", to: "/faq" },
  { name: "100 Days Quality Guarantee", to: "/100-day-quality-guarantee" },
  { name: "Blog", to: "/blog" },
];

const contact = [
  // {name: 'Addresses', detail: "Ipavčeva 22, 3000 Celje, Slovenia"},
  // {name: 'Bangladesh', detail: "55/1, Barua, Raj Para, Khilkhet, Dhaka-1229"},
  { name: "Email", detail: "<EMAIL>" },
  { name: "Company", to: "/our-company" },
];

const myAccount = computed(() => {
  return [
    {
      name: authenticated.value ? "My Account" : "Login",
      to: authenticated.value ? "/user" : "/auth/login",
    },
    { name: "Order History", to: "/user/orders" },
  ];
});
</script>
<style lang="scss">
.main-footer-content-wrapper {
  .v-expansion-panel-text__wrapper {
    padding: 0;
  }
}
</style>
