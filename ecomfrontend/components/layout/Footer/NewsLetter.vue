<template>
  <div>
    <IntersectWrapper :onIntersect="onIntersect">
      <section class="news-letter-wrapper">
        <v-container>
          <v-row class="align-center">
            <v-col cols="12" md="4" class="text-center text-md-left hidden-sm-and-down">
              <h3>{{ $t("Subscribe to our Newsletter") }}</h3>
              <p class="text-body-2">
                {{ $t("We handpick the very best deals") }}
              </p>
            </v-col>
            <v-col cols="12" md="5">
              <div class="mx-auto w-100 subscribe-form">
                <client-only>
                  <v-text-field
                    variant="solo-filled"
                    :placeholder="$t('Email Address')"
                    single-line
                    prepend-inner-icon="i-mdi:email-outline"
                    rounded="xl"
                    v-model="state.email"
                    required
                    :error-messages="
                      v$.email.$errors.map((e) =>
                        e.$message ? $t(e.$message) : ''
                      )
                    "
                    :hide-details="v$.email.$errors?.length === 0"
                    density="compact"
                  >
                    <template #append-inner>
                      <v-btn
                        color="blue"
                        rounded="xl"
                        :loading="loading"
                        @click="submit"
                        density="compact"
                        >{{ $t("Subscribe") }}</v-btn
                      >
                    </template>
                  </v-text-field>
                </client-only>
              </div>
            </v-col>
            <v-col cols="12" md="3">
              <div
                class="text-center d-flex d-md-block align-center justify-center"
              >
                <h3 class="mr-2 mr-md-0 mb-0 mb-md-3">{{ $t("Follow Us") }}</h3>
                <a
                  v-if="socialMediaStore.socialLinks?.facebook"
                  :href="socialMediaStore.socialLinks.facebook"
                  target="_blank"
                  class="d-inline-flex"
                >
                  <NuxtImg
                    :src="replaceMediaDomain(getMediaImage('facebook')) || getMediaImage('facebook')"
                    alt="Facebook"
                    loading="lazy"
                    format="webp"
                    width="28"
                    height="28"
                    class="me-2"
                    fit="cover"
                    target="_blank"
                    rel="noopener noreferrer"
                    quality="85"
                  />
                </a>

                <a
                  v-if="socialMediaStore.socialLinks?.linkedin"
                  :href="socialMediaStore.socialLinks.linkedin"
                  target="_blank"
                  class="d-inline-flex"
                >
                  <NuxtImg
                    :src="replaceMediaDomain(getMediaImage('linkedin')) || getMediaImage('linkedin')"
                    alt="LinkedIn"
                    loading="lazy"
                    format="webp"
                    width="28"
                    height="28"
                    class="me-2"
                    fit="cover"
                    target="_blank"
                    rel="noopener noreferrer"
                    quality="85"
                  />
                </a>

                <a
                  v-if="socialMediaStore.socialLinks?.instagram"
                  :href="socialMediaStore.socialLinks.instagram"
                  target="_blank"
                  class="d-inline-flex"
                >
                  <NuxtImg
                    :src="replaceMediaDomain(getMediaImage('instagram')) || getMediaImage('instagram')"
                    alt="Instagram"
                    loading="lazy"
                    format="webp"
                    width="28"
                    height="28"
                    class="me-2"
                    fit="cover"
                    target="_blank"
                    rel="noopener noreferrer"
                    quality="85"
                  />
                </a>

                <a
                  v-if="socialMediaStore.socialLinks?.youtube"
                  :href="socialMediaStore.socialLinks.youtube"
                  target="_blank"
                  class="d-inline-flex"
                >
                  <NuxtImg
                    :src="replaceMediaDomain(getMediaImage('youtube')) || getMediaImage('youtube')"
                    alt="YouTube"
                    loading="lazy"
                    format="webp"
                    width="28"
                    height="28"
                    class="me-2"
                    fit="cover"
                    target="_blank"
                    rel="noopener noreferrer"
                    quality="85"
                  />
                </a>

                <a
                  v-if="socialMediaStore.socialLinks?.tiktok"
                  :href="socialMediaStore.socialLinks.tiktok"
                  target="_blank"
                  class="d-inline-flex"
                >
                  <NuxtImg
                    :src="replaceMediaDomain(getMediaImage('tiktok')) || getMediaImage('tiktok')"
                    alt="TikTok"
                    loading="lazy"
                    format="webp"
                    width="28"
                    height="28"
                    class="me-2"
                    fit="cover"
                    target="_blank"
                    rel="noopener noreferrer"
                    quality="85"
                  />
                </a>

                <a
                  v-if="socialMediaStore.socialLinks?.x"
                  :href="socialMediaStore.socialLinks.x"
                  target="_blank"
                  class="d-inline-flex"
                >
                  <NuxtImg
                    :src="replaceMediaDomain(getMediaImage('twitter')) || getMediaImage('twitter')"
                    alt="X"
                    loading="lazy"
                    format="webp"
                    width="28"
                    height="28"
                    class="me-2"
                    fit="cover"
                    target="_blank"
                    rel="noopener noreferrer"
                    quality="85"
                  />
                </a>
              </div>
            </v-col>
          </v-row>
        </v-container>
      </section>
    </IntersectWrapper>
  </div>
</template>
<script setup lang="ts">
import { email, helpers, required } from "@vuelidate/validators";
import { useVuelidate } from "@vuelidate/core";
import { fetchApiData } from "../../../utils/apiHelpers";
import { showSnackbarResponse } from "../../../utils/functions";
import { useSocialMediaStore } from "../../../stores/socialMedia";
import { useCurrentHost } from "../../../composables/useCurrentHost";
import { useAuthStore } from "../../../stores/auth";
import { useI18n } from 'vue-i18n';

const { replaceMediaDomain } = useMediaType();
const { getActiveLanguageId } = useLanguage();
const config = useRuntimeConfig();
const store = usePartnerInstagramStore();
const socialMediaStore = useSocialMediaStore();
const staticImageUrl = config.public.pantonecloStaticImageUrl;
let loading = ref(false);
const { t } = useI18n();  // This gives you the `t` function

const socialMedias = computed(() => store?.socialMedias);
const { getDomaCountryId, getDomaCountryCode, getDomaLanguageId } = useCurrentHost();
const countryId = getDomaCountryId();
const languageId = getDomaLanguageId();
const countryCode = getDomaCountryCode();
const authStore = useAuthStore();

const getMediaImage = (title: string) => {
  switch (title) {
    case "facebook":
      return `${staticImageUrl}fb.png`;
    case "twitter":
      return `${staticImageUrl}x.png`;
    case "instagram":
      return `${staticImageUrl}insta.png`;
    case "tiktok":
      return `${staticImageUrl}tiktok.png`;
    case "linkedin":
      return `${staticImageUrl}in.png`;
    case "youtube":
      return `${staticImageUrl}youtube.png`;
  }
};

const initialState = {
  email: "",
};

const state = reactive({
  ...initialState,
});

const rules = {
  email: {
    required: helpers.withMessage("Email is required", required),
    email: helpers.withMessage("E-mail must be valid", email),
  },
};

const v$ = useVuelidate(rules, state);

const submit = async () => {
  await v$.value?.$validate();

  if (!state.email || v$.value?.$errors.length > 0) {
    return;
  }

  loading.value = true;
  const response = await fetchApiData("newsSubscription", {
    method: "POST",
    body: {
      email: state.email,
      countryCode: countryCode?.toLowerCase(),
      countryId: countryId,
      languageId: languageId,
      isSubscribed: true,
      userId: authStore?.currentBasicUser?.id ?? null,
      isMailSent: true,
    },
  });
  let msg = {
    ...response,
    messasge: t(response?.messasge),  // Accessing the translation here
  };

  showSnackbarResponse(msg);
  loading.value = false;
};

const onIntersect = async () => {
  if (store.socialMedias.length === 0) {
    await store.getSetupDataByType("SOCIAL", getActiveLanguageId());
  }
};
</script>
<style lang="scss">
.news-letter-wrapper {
  background-color: #000;
  color: #fff;

  .v-field {
    height: 50px;
    padding-right: 0;
    border-radius: 30px !important;

    input {
      padding: 2px 6px;
      min-height: 50px;
    }

    .v-btn {
      border-radius: 0 20px 20px 0 !important;
      height: 50px;
    }
  }

  .social-icon-border {
    border: 1px solid #fff;
    border-radius: 50%;
  }
}

@media (min-width: 576px) {
  .subscribe-form {
    width: 75% !important;
  }
}
</style>
