<template>
  <section class="border slogan-section-wrapper py-0 py-md-5">
    <v-container class="hidden-sm-and-down">
      <v-row no-gutters>
        <v-col cols="12">
          <v-row>
            <v-col
              cols="3"
              class="text-center pa-0"
              v-for="tag in tags"
              :key="tag.id"
            >
              <div class="d-flex justify-center">
                <NuxtLink
                  :to="localePath(tag.to)"
                  class="text-decoration-none text-black d-flex ga-2 justify-center align-center"
                >
                  <!-- <NuxtImg
                      :src="tag?.img"
                      :alt="tag.name"
                      loading="lazy"
                      sizes="xs:100vw sm:100vw md:100vw"
                      format="avif"
                      width="40"
                      height="40"
                      class="h-100"
                      fit="cover"
                    /> -->
                  <v-icon :icon="tag.icon" size="40"></v-icon>
                  <h2 class="text-h5">
                    {{ $t(tag.name) }}
                  </h2>
                </NuxtLink>
              </div>
            </v-col>
          </v-row>
        </v-col>
      </v-row>
    </v-container>
    <div class="slogan-swiper-wrapper hidden-md-and-up">
      <Swiper
        space-between="20"
        :loop="true"
        :modules="[SwiperAutoplay]"
        :autoplay="autoplay"
        :breakpoints="breakPoints"
      >
        <SwiperSlide v-for="tag in tags" :key="tag.id">
          <v-card>
            <v-card-text class="d-flex justify-center align-center text-center py-1">
              <v-avatar rounded="0">
                <v-icon :icon="tag.icon" size="25"></v-icon>
              </v-avatar>
              <NuxtLink
                class="ms-1 mb-0 tag-title text-body-1 text-decoration-none text-black font-weight-bold"
                :to="localePath(tag.to)"
              >
                {{ tag.name }}
              </NuxtLink>
            </v-card-text>
          </v-card>
        </SwiperSlide>
      </Swiper>
    </div>
  </section>
</template>
<script setup lang="ts">
import { storeToRefs } from "pinia";

const localePath = useLocalePath();

const { authenticated } = storeToRefs(useAuthStore());
const config = useRuntimeConfig();
const staticImageUrl = config.public.pantonecloStaticImageUrl;

const breakPoints = ref({
  320: {
    slidesPerView: 1,
  },
  575: {
    slidesPerView: 2,
  },
});

const autoplay = ref({
  delay: 2000,
  disableOnInteraction: true,
});

const tags = ref([
  {
    id: 1,
    name: "Free Shipping",
    img: `${staticImageUrl}free-shipping.png`,
    icon: "i-mdi:truck-delivery",
    to: "/free-shipping-policy",
  },
  {
    id: 2,
    name: "Return",
    img: `${staticImageUrl}easy-return.png`,
    icon: "i-mdi:restore",
    to: authenticated.value ? "/user/orders" : "/guest/return",
  },
  {
    id: 3,
    name: "100 Days Quality Guarantee",
    img: `${staticImageUrl}money-back.png`,
    icon: "i-mdi:wallet-outline",
    to: "/100-day-quality-guarantee",
  },
  {
    id: 4,
    name: "Track Order",
    img: `${staticImageUrl}tracking.png`,
    icon: "i-mdi:map-marker-outline",
    to: "/track/order",
  },
]);
</script>

<style lang="scss" scoped>
.slogan-swiper-wrapper {
  .swiper-wrapper {
    align-items: center;

    .swiper-slide {
      width: 80%;
    }
  }
}
</style>
