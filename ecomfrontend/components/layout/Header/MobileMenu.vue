<template>
  <div>
    <div class="d-flex align-center pa-4" v-if="!authenticated">
      <v-icon size="20" class="i-mdi:account me-2" />
      <NuxtLink
        :to="localePath('/auth/login')"
        @click="emit('linkClicked')"
        class="text-black"
        >{{ $t("Login") }}</NuxtLink
      >
      <div class="mx-2">|</div>
      <NuxtLink
        :to="localePath('/auth/register')"
        @click="emit('linkClicked')"
        class="text-black"
        >{{ $t("Register") }}</NuxtLink
      >
    </div>
    <div v-else class="px-4 py-2">
      <v-btn
        class="custom-hover-primary px-0"
        variant="text"
        :to="localePath('/user')"
        @click="emit('linkClicked')"
      >
        <v-avatar size="32">
          <NuxtImg
            :src="
              replaceMediaDomain('https://pantoneclos3bucket.s3.ap-south-1.amazonaws.com/pantoneclo-img/user-1.jpg') || 
              'https://pantoneclos3bucket.s3.ap-south-1.amazonaws.com/pantoneclo-img/user-1.jpg'
            "
            :alt="currentBasicUser?.firstName"
            loading="lazy"
            sizes="xs:100vw sm:100vw md:100vw"
            format="webp"
            width="32"
            height="32"
            class="h-100 w-100"
            fit="cover"
            quality="20"
          />
        </v-avatar>
        <span class="ms-2">{{
          currentBasicUser?.firstName + " " + currentBasicUser?.lastName
        }}</span>
      </v-btn>
    </div>

    <div class="d-flex justify-space-between">
      <NuxtLink
        :to="localePath('/wishlists')"
        class="pa-4 text-decoration-none text-black d-block font-weight-bold"
        @click="ClickedTheLink"
      >
        <v-icon class="i-mdi:heart-outline" size="20" />
        {{ $t("My Wishlists") }}
      </NuxtLink>
      <nuxt-link
        to="/event/flash-deals"
        class="pa-4 text-h6 text-decoration-none font-weight-light blink_me me-2"
        >{{ $t("Deals") }}🗲
      </nuxt-link>
    </div>

    <v-expansion-panels
      class="d-block mobile-menu-expansion-panel"
      @update:modelValue="onUpdateMobileMenuItem"
    >
      <NuxtLink
        class="text-h6 text-start d-block px-4 font-weight-bold text-decoration-none py-4 text-black"
        :to="localePath('/categories')"
        @click="ClickedTheLink"
        >{{ $t("All Categories") }}
      </NuxtLink>
      <v-expansion-panel elevation="0">
        <template #title>
          <v-btn
            @click="goToCat('men')"
            variant="text"
            class="ps-0 pe-15 text-body-1"
            >{{ $t("Men") }}</v-btn
          >
        </template>
        <template #text>
          <v-list>
            <v-list-item
              v-for="subcat in menItems"
              :key="subcat.id"
              class="px-0"
              :to="localePath(`/category/men/${subcat.slug}/`)"
              @click="emit('linkClicked')"
            >
              <template #title>
                <h3 class="font-weight-light">
                  {{
                    getLocalizeName(
                      getActiveLanguageId(),
                      subcat?.locale,
                      subcat?.name
                    )
                  }}
                </h3>
              </template>
              <template #prepend>
                <v-avatar :image="subcat?.imageGallery?.imageUrl" size="30" />
              </template>
            </v-list-item>
          </v-list>
        </template>
      </v-expansion-panel>
      <v-expansion-panel elevation="0">
        <template #title>
          <v-btn
            @click="goToCat('women')"
            variant="text"
            class="ps-0 pe-15 text-body-1"
            >{{ $t("Women") }}</v-btn
          >
        </template>
        <template #text>
          <v-list>
            <v-list-item
              v-for="subcat in womenItems"
              :key="subcat.id"
              class="px-0"
              :to="localePath(`/category/women/${subcat.slug}/`)"
              @click="emit('linkClicked')"
            >
              <template #title>
                <h3 class="font-weight-light">
                  {{
                    getLocalizeName(
                      getActiveLanguageId(),
                      subcat?.locale,
                      subcat?.name
                    )
                  }}
                </h3>
              </template>
              <template #prepend>
                <v-avatar :image="subcat?.imageGallery?.imageUrl" size="30" />
              </template>
            </v-list-item>
          </v-list>
        </template>
      </v-expansion-panel>
      <v-expansion-panel elevation="0">
        <template #title>
          <v-btn
            @click="goToCat('kids')"
            variant="text"
            class="ps-0 pe-15 text-body-1"
            >{{ $t("Kids") }}</v-btn
          >
        </template>
        <template #text>
          <v-list>
            <v-list-item
              v-for="subcat in kidsItems"
              :key="subcat.id"
              class="px-0"
              :to="localePath(`/category/kids/${subcat.slug}/`)"
              @click="emit('linkClicked')"
            >
              <template #title>
                <h3 class="font-weight-light">
                  {{
                    getLocalizeName(
                      getActiveLanguageId(),
                      subcat?.locale,
                      subcat?.name
                    )
                  }}
                </h3>
              </template>
              <template #prepend>
                <v-avatar :image="subcat?.imageGallery?.imageUrl" size="30" />
              </template>
            </v-list-item>
          </v-list>
        </template>
      </v-expansion-panel>
    </v-expansion-panels>
  </div>
</template>

<script lang="ts" setup>
import { getLocalizeName } from "~/utils/functions";
import { storeToRefs } from "pinia";
import { useMediaType } from "../../../composables/useMediaType";

const globalStore = useGlobalStore();
const countryId = computed(() => globalStore?.globals?.id);

const emit = defineEmits(["linkClicked"]);

const { replaceMediaDomain } = useMediaType();
const localePath = useLocalePath();
const { getActiveLanguageId } = useLanguage();

const authStore = useAuthStore();
const websiteStore = useWebsiteStore();
const { authenticated } = storeToRefs(authStore);
const { currentBasicUser } = storeToRefs(authStore);

const menItems = computed(() => websiteStore.getCategoriesByParentId(1));
const womenItems = computed(() => websiteStore.getCategoriesByParentId(2));
const kidsItems = computed(() => websiteStore.getCategoriesByParentId(3));

const ClickedTheLink = () => {
  emit("linkClicked", true);
};

const goToCat = (slug: any) => {
  navigateTo(localePath("/category/" + slug));
  emit("linkClicked");
};

const onUpdateMobileMenuItem = async (val) => {
  if (val !== "undefined" && !websiteStore?.categories?.length) {
    await websiteStore.getCategoriesPlain(countryId?.value);
  }
};
</script>


<style scoped lang="scss">
.blink_me {
  font-family: "Patua One", serif;
  letter-spacing: 2px !important;
  animation: colorFade 2s infinite;
  &:hover{
    animation: unset;
  }
}

@keyframes colorFade {
  0% {
    color: red;
    opacity: 1;
  }
  50% {
    color: black;
    opacity: 0;
  }
  100% {
    color: red;
    opacity: 1;
  }
}
</style>
