<template>
  <v-menu
    :close-on-content-click="closeOnSearchedProductClick"
    origin="auto"
    offset="10"
    location-strategy="connected"
    target="parent"
    @update:modelValue="menuModelUpdate"
    v-model="searchMenu"
  >
    <template v-slot:activator="{ props }">
      <v-btn
        icon
        class="custom-hover-primary header-icon-bg"
        size="small"
        variant="elevated"
        :elevation="0"
        color="primary"
        v-bind="props"
        name="search-btn"
        aria-label="Search"
      >
        <v-icon color="#fff" class="i-mdi:magnify" size="18"></v-icon>
      </v-btn>
    </template>
    <v-sheet min-width="380" elevation="10" rounded="md">
      <div class="pa-5">
        <v-form @submit.prevent="searchSubmit">
          <div class="d-flex position-relative">
            <v-text-field
              :placeholder="$t('Search for products')"
              color="primary"
              density="compact"
              variant="outlined"
              hide-details
              autofocus
              v-model="searchTerm"
              @update:modelValue="searchProduct"
              @focus="showSearchHistory = searchTerm.length === 0 && searchHistory.length > 0"
            ></v-text-field>

            <v-btn
              variant="text"
              class="position-absolute"
              style="right: 0; top: 50%; transform: translateY(-50%)"
              type="submit"
            >
              <v-icon class="i-mdi:magnify" size="18"></v-icon>
            </v-btn>
          </div>
        </v-form>
      </div>

      <!-- Search History Section -->
      <div v-if="showSearchHistory && searchHistory.length > 0" class="px-4 pb-3">
        <div class="d-flex align-center justify-space-between mb-2">
          <v-subheader class="pa-0 text-caption">{{ $t("Recent Searches") }}</v-subheader>
          <v-btn
            size="x-small"
            variant="text"
            color="error"
            @click="clearSearchHistory"
          >
            {{ $t("Clear") }}
          </v-btn>
        </div>
        <div class="d-flex flex-wrap ga-2">
          <v-chip
            v-for="(term, index) in searchHistory.slice(0, 6)"
            :key="index"
            size="small"
            variant="outlined"
            color="primary"
            @click="selectSearchHistory(term)"
            closable
            @click:close="removeSearchHistory(term)"
          >
            <v-icon start size="14" class="i-mdi:history"></v-icon>
            {{ term }}
          </v-chip>
        </div>
        <v-divider class="mt-3"></v-divider>
      </div>

      <template v-if="loading">
        <div class="pa-4 pt-0 d-flex align-center">
          <v-progress-circular size="16" width="2" indeterminate class="me-2"></v-progress-circular>
          {{ $t("Searching") }}...
        </div>
      </template>
      <template v-else-if="searchTerm.length > 3 && products.length === 0">
        <div class="pa-4 pt-0 text-center">
          <v-icon class="i-mdi:magnify-close mb-2" size="32" color="grey"></v-icon>
          <div>{{ $t("Sorry no Product Found") }}</div>
          <div class="text-caption text-grey">{{ $t("Try different keywords") }}</div>
        </div>
      </template>
      <template v-else>
        <div class="px-4">
          <!-- ${product?.categorySlug ? product?.categorySlug + '/' : '' } -->
          <NuxtLink
            :to="localePath(`/product/${product.slug}`)"
            v-for="product in products"
            :key="product.id"
            @click="addToSearchHistory(searchTerm); searchMenu = false"
            class="mb-4 d-flex text-decoration-none"
            style="color: #000"
          >
            <v-avatar size="60" rounded="0" class="me-2">
              <NuxtImg
                :src="product.featuredImage?.imageGalleryUrls?.thump"
                loading="lazy"
                sizes="xs:100vw sm:100vw md:100vw"
                format="avif"
                width="107"
                height="126"
                class="h-100 w-100"
                fit="cover"
                quality="20"
              />
            </v-avatar>
            <div class="single-card-price-wrapper">
              <h4 class="font-weight-semibold">{{ product.name }}</h4>
              <template v-if="product.discountPrice">
                <span class="price font-weight-semibold me-2">{{
                  formatLocalePrice(product.discountPrice)
                }}</span>
                <span class="text-decoration-line-through text-red price">
                  {{ formatLocalePrice(product.unitPrice) }}</span
                >
                <!-- <span class="text-decoration-line-through me-2 text-grey price">
                  {{ formatLocalePrice(product.unitPrice) }}</span>
                <span class="price">{{ formatLocalePrice(product.discountPrice) }}</span> -->
              </template>
              <span v-else class="price">{{
                formatLocalePrice(product.unitPrice)
              }}</span>
            </div>
          </NuxtLink>

          <v-btn
            block
            color="primary"
            variant="tonal"
            @click="searchViewAll"
            v-if="searchTerm.length > 3"
            class="mb-3 font-weight-bold"
            >{{ $t("View All") }}</v-btn
          >
        </div>
      </template>
    </v-sheet>
  </v-menu>
</template>
<script setup lang="ts">
const props = defineProps<{
  countryCode?: string;
}>();

const { getActiveLanguageId } = useLanguage();

const localePath = useLocalePath();
const productStore = useProductStore();

const searchTerm = ref("");
const searchMenu = ref(false);
const loading = ref(false);
const products = ref();

const closeOnSearchedProductClick = ref(false);
const timeout = ref<number | null>(null);

// Search History Management with localStorage
const searchHistory = ref<string[]>([]);
const showSearchHistory = ref(false);
const SEARCH_HISTORY_KEY = 'ecom_search_history';
const MAX_HISTORY_ITEMS = 8;

// Load search history on component mount
onMounted(() => {
  loadSearchHistory();
});

// localStorage functions
const loadSearchHistory = () => {
  if (process.client) {
    try {
      const stored = localStorage.getItem(SEARCH_HISTORY_KEY);
      if (stored) {
        searchHistory.value = JSON.parse(stored);
      }
    } catch (error) {
      console.error('Error loading search history:', error);
      searchHistory.value = [];
    }
  }
};

const saveSearchHistory = () => {
  if (process.client) {
    try {
      localStorage.setItem(SEARCH_HISTORY_KEY, JSON.stringify(searchHistory.value));
    } catch (error) {
      console.error('Error saving search history:', error);
    }
  }
};

const addToSearchHistory = (term: string) => {
  if (!term || term.trim().length < 2) return;

  const cleanTerm = term.trim();

  // Remove if already exists
  const index = searchHistory.value.indexOf(cleanTerm);
  if (index > -1) {
    searchHistory.value.splice(index, 1);
  }

  // Add to beginning
  searchHistory.value.unshift(cleanTerm);

  // Limit to MAX_HISTORY_ITEMS
  if (searchHistory.value.length > MAX_HISTORY_ITEMS) {
    searchHistory.value = searchHistory.value.slice(0, MAX_HISTORY_ITEMS);
  }

  saveSearchHistory();
};

const removeSearchHistory = (term: string) => {
  const index = searchHistory.value.indexOf(term);
  if (index > -1) {
    searchHistory.value.splice(index, 1);
    saveSearchHistory();
  }
};

const clearSearchHistory = () => {
  searchHistory.value = [];
  saveSearchHistory();
  showSearchHistory.value = false;
};

const selectSearchHistory = (term: string) => {
  searchTerm.value = term;
  showSearchHistory.value = false;
  debounceSearchProduct(term);
};

// Custom debounce function
const debounceSearchProduct = (value: string) => {
  console.log("+===checker===", value);
  if (timeout.value) {
    clearTimeout(timeout.value);
  }

  timeout.value = setTimeout(async () => {
    if (value.length > 0) {
      loading.value = true;
      productStore.searchedProduct = [];
      products.value = [];


      const response = await productStore.searchProduct(
        value,
        props.countryCode,
        6,
        1,
        getActiveLanguageId()
      );

      if (response.isSuccess) {
        products.value = response.data;
        loading.value = false;
      } else {
        loading.value = false;
      }
    }
    if (value.length === 0) {
      productStore.searchedProduct = [];
      products.value = [];
      loading.value = false;
    }
  }, 500);
};

const searchProduct = (value: string) => {
  // Show search history when input is empty or very short
  if (value.length === 0) {
    showSearchHistory.value = searchHistory.value.length > 0;
    products.value = [];
  } else {
    showSearchHistory.value = false;
  }

  debounceSearchProduct(value); // Call custom debounce function
};

const searchViewAll = () => {
  if (searchTerm.value.trim()) {
    addToSearchHistory(searchTerm.value.trim());
  }
  searchMenu.value = false;
  productStore.searchedProduct = [];
  navigateTo(localePath(`/search?query=${searchTerm.value}`));
};

const menuModelUpdate = (val: any) => {
  if (!val) {
    productStore.searchedProduct = [];
    searchTerm.value = "";
    products.value = [];
    showSearchHistory.value = false;
  } else {
    // Show search history when menu opens if search is empty
    showSearchHistory.value = searchTerm.value.length === 0 && searchHistory.value.length > 0;
  }
};

const searchSubmit = () => {
  if (!searchTerm.value) return;

  addToSearchHistory(searchTerm.value.trim());
  searchMenu.value = false;
  navigateTo(localePath(`/search?query=${searchTerm.value}`));
};
</script>
