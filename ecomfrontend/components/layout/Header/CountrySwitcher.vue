<template>
  <div :class="{ 'ml-md-10 ml-1': country.code != 'BD' }">
    <v-menu
      :close-on-content-click="true"
      location="bottom"
      @update:modelValue="onCountryMenuOpen"
      open-on-click
      v-if="country.code == 'BD'"
    >
      <!-- <template v-slot:activator="{ props }">
        <v-btn variant="text" color="primary" v-bind="props">
          <NuxtImg
            :src="activeLocale?.flag"
            placeholder
            loading="lazy"
            sizes="xs:100vw sm:100vw md:100vw"
            format="avif"
            width="28"
            height="22"
            class="me-2"
            fit="cover"
          />
          <span class="d-none d-md-inline">{{ activeLocale?.name }}</span>
        </v-btn>
      </template> -->

      <!-- <v-sheet
        rounded="md"
        width="200"
        elevation="10"
        height="auto"
        max-height="200"
        v-if="hasMultipleLang"
      >
        <v-list class="theme-list">
          <v-list-item
            v-for="(item, index) in languageArray"
            :key="index"
            class="d-flex align-center"
            @click="changelanguage(item)"
            :active="locale == item.code"
          >
            <template v-slot:prepend>
              <NuxtImg
                :src="item?.flag"
                :alt="item.name"
                placeholder
                loading="lazy"
                sizes="xs:100vw sm:100vw md:100vw"
                format="avif"
                width="28"
                height="22"
                fit="cover"
                class="me-2"
              />
            </template>
            <v-list-item-title class="text-subtitle-1 font-weight-regular">
              {{ item.name }}
            </v-list-item-title>
          </v-list-item>
        </v-list>
      </v-sheet> -->
    </v-menu>

    <v-menu
      :close-on-content-click="true"
      location="bottom"
      @update:modelValue="onCountryMenuOpen"
      open-on-click
      v-else
    >
      <template v-slot:activator="{ props }">
        <v-btn variant="text" color="primary" v-bind="props">
          <NuxtImg
            :src="replaceMediaDomain(country?.imageGallery?.imageUrl) || country?.imageGallery?.imageUrl"
            :alt="country?.name"
            loading="lazy"
            sizes="xs:100vw sm:100vw md:100vw"
            format="webp"
            width="28"
            height="22"
            class="me-2"
            fit="cover"
            quality="85"
          />

          <span class="d-none d-md-inline">{{
            country?.code ? country?.code : country?.name
          }}</span>
        </v-btn>
      </template>

      <v-sheet
        rounded="md"
        width="200"
        class="d-flex align-center"
        v-if="hasMultipleLang"
      >
        <div class="mx-auto mt-2">
          <v-tabs v-model="tab" color="primary">
            <v-tab value="language" @click.prevent.stop>{{
              $t("Language")
            }}</v-tab>
            <v-tab value="country" @click.prevent.stop>{{
              $t("Country")
            }}</v-tab>
          </v-tabs>
        </div>
      </v-sheet>

      <v-sheet
        rounded="md"
        width="200"
        elevation="10"
        height="auto"
        max-height="200"
      >
        <v-list class="theme-list" v-if="tab == 'country'">
          <v-list-item
            v-for="(item, index) in languageDD"
            :key="index"
            class="d-flex align-center"
            @click="changeCountry(item.code, item.domain)"
            :active="country?.code && country?.code == item.code"
          >
            <template v-slot:prepend>
              <NuxtImg
                :src="replaceMediaDomain(item?.imageGallery?.imageUrl) || item?.imageGallery?.imageUrl"
                :alt="item.avatar"
                loading="lazy"
                sizes="xs:100vw sm:100vw md:100vw"
                format="webp"
                width="28"
                height="22"
                fit="cover"
                class="me-2"
                quality="85"
              />
            </template>
            <v-list-item-title class="text-subtitle-1 font-weight-regular">
              {{ item.name }}
            </v-list-item-title>
          </v-list-item>
        </v-list>

        <v-list class="theme-list" v-else-if="hasMultipleLang">
          <v-list-item
            v-for="(item, index) in languageArray"
            :key="index"
            class="d-flex align-center"
            @click="changelanguage(item)"
            :active="locale == item.code"
          >
            <template v-slot:prepend>
              <NuxtImg
                :src="replaceMediaDomain(item?.flag) || item?.flag"
                :alt="item.name"
                loading="lazy"
                sizes="xs:100vw sm:100vw md:100vw"
                format="webp"
                width="28"
                height="22"
                fit="cover"
                class="me-2"
                quality="85"
              />
            </template>
            <v-list-item-title class="text-subtitle-1 font-weight-regular">
              {{ item.name }}
            </v-list-item-title>
          </v-list-item>
        </v-list>
      </v-sheet>
    </v-menu>
  </div>
</template>

<script setup lang="ts">
import { useRouter, useRoute } from "vue-router";
const { setLocale, locale } = useI18n();
import { scrollTop } from "../../../utils/functions";
import { computed, ref } from "vue";
import { useMediaType } from "../../../composables/useMediaType";
const { getCurrentHost } = useCurrentHost();
const _websiteStore = useWebsiteStore();

const { replaceMediaDomain } = useMediaType();
const localeLang = useCookie("locale_lang", { maxAge: 365 * 24 * 60 * 60 });
const localePrefer = useCookie("locale_prefer", { maxAge: 365 * 24 * 60 * 60 });
const coyuntryPrefer = useCookie("country_prefer", {
  maxAge: 365 * 24 * 60 * 60,
});

const props = defineProps<{
  websiteStore?: any;
}>();

const tab = ref("country");

const _languagesOptionArr = [
  {
    id: 1,
    name: "English",
    flag: "/united-kingdom.png",
    code: "en",
  },
  {
    id: 2,
    name: "বাংলা",
    flag: "https://pantoneclos3bucket.s3.ap-south-1.amazonaws.com/country/bd_1715510188186.webp",
    code: "bn",
  },

  {
    id: 3,
    name: "Deutsch",
    flag: "https://pantoneclos3bucket.s3.ap-south-1.amazonaws.com/country/flag_of_germany.svg_1715512154201.webp",
    code: "de",
  },
  {
    id: 4,
    name: "Български",
    flag: "https://pantoneclos3bucket.s3.ap-south-1.amazonaws.com/country/flag_of_bulgaria.svg_1715512263642.webp",
    code: "bg",
  },
  {
    id: 5,
    name: "Čeština",
    flag: "https://pantoneclos3bucket.s3.ap-south-1.amazonaws.com/country/flag_of_the_czech_republic.svg_1715512192923.webp",
    code: "cz",
  },
  {
    id: 6,
    name: "Ελληνικά",
    flag: "https://pantoneclos3bucket.s3.ap-south-1.amazonaws.com/country/gr_1715510004473.webp",
    code: "el",
  },
  {
    id: 7,
    name: "Español",
    flag: "https://pantoneclos3bucket.s3.ap-south-1.amazonaws.com/country/flag_of_spain.svg_1715511962103.webp",
    code: "es",
  },
  {
    id: 8,
    name: "Hrvatski",
    flag: "https://pantoneclos3bucket.s3.ap-south-1.amazonaws.com/country/flag_of_croatia.svg_1715512238300.webp",
    code: "hr",
  },
  {
    id: 9,
    name: "Magyar",
    flag: "https://pantoneclos3bucket.s3.ap-south-1.amazonaws.com/country/hungary_1715512489902.webp",
    code: "hu",
  },
  {
    id: 10,
    name: "Italiano",
    flag: "https://pantoneclos3bucket.s3.ap-south-1.amazonaws.com/country/flag_of_italy.svg_1715511925519.webp",
    code: "it",
  },
  {
    id: 11,
    name: "Lietuvių",
    flag: "https://pantoneclos3bucket.s3.ap-south-1.amazonaws.com/country/flag_of_lithuania.svg_1715511875726.webp",
    code: "lt",
  },
  {
    id: 12,
    name: "Polski",
    flag: "https://pantoneclos3bucket.s3.ap-south-1.amazonaws.com/country/flag_of_poland.svg_1715511853680.webp",
    code: "pl",
  },
  {
    id: 13,
    name: "Português",
    flag: "https://pantoneclos3bucket.s3.ap-south-1.amazonaws.com/country/flag_of_portugal.svg_1715511830003.webp",
    code: "pt",
  },
  {
    id: 14,
    name: "Română",
    flag: "https://pantoneclos3bucket.s3.ap-south-1.amazonaws.com/country/flag_of_romania.svg_1715511766244.webp",
    code: "ro",
  },
  {
    id: 15,
    name: "Slovenčina",
    flag: "https://pantoneclos3bucket.s3.ap-south-1.amazonaws.com/country/flag_of_slovakia.svg_1715511796176.webp",
    code: "sk",
  },
  {
    id: 16,
    name: "Slovenščina",
    flag: "https://pantoneclos3bucket.s3.ap-south-1.amazonaws.com/country/flag_of_slovenia.svg_1715511646614.webp",
    code: "sl",
  },
  {
    id: 17,
    name: "Deutsch (Österreich)",
    flag: "https://pantoneclos3bucket.s3.ap-south-1.amazonaws.com/country/flag_of_austria.svg_1715512284289.webp",
    code: "at",
  },
];

const activeLocale = computed(() => {
  return _languagesOptionArr.find((item) => item.code == locale.value);
});

const countryStore = useCountryStore();
const globalStore = useGlobalStore();
const { globals } = globalStore;

const languageDD = computed(() =>
  countryStore.countries?.filter(
    (item) => item.code != "BD" && item.code != "IN"
  )
);
const country = computed(() => globalStore.globals);
//
const languageArray = computed(() => {
  const all_lang: number[] = [];

  if (country.value?.languageId) {
    all_lang.push(Number(country.value.languageId));
  }

  if (country.value?.languagesId?.length) {
    country.value.languagesId.forEach((item) => {
      all_lang.push(Number(item));
    });
  }

  const _all_lang = new Set(all_lang);

  return _languagesOptionArr.filter((lang) => _all_lang.has(Number(lang.id)));
});

const hasMultipleLang = computed(() => {
  const all_lang = languageArray.value?.map((item) => Number(item.id)) || [];
  const _all_lang_set = new Set(all_lang);

  if (country.value?.languageId) {
    _all_lang_set.delete(Number(country.value.languageId));
  }

  return _all_lang_set.size > 0;
});

//
const changeCountry = async (code: string, domain?: string) => {
  if (code == country.value.code) return;

  const response = await globalStore.fetchCountry(code, getCurrentHost());

  console.log("response", response);
  if (response.isSuccess) {
    globalStore.$state.countryCode = code;
    globalStore.$state.globals = response.data;
    coyuntryPrefer.value = code; // Set country prefer

    useShoppingCartStore().$reset();
    useWishlistStore().$reset();

    _websiteStore.getCategoriesPlain(response.data.id);

    if (domain) {
      window.location.href = `https://${domain}`;
    } else {
      window.location.reload();
    }
  }
};

const changelanguage = (item) => {
  localeLang.value = item.code;
  localePrefer.value = item.code;
  setLocale(item.code);

  window.location.reload();

  // router.push({
  //   path: route.path,
  //   query: { ...route.query, refresh: new Date().getTime() },
  // });

  // scrollTop();
};

onMounted(async () => {
  if (countryStore.countries?.length === 0 && open) {
    await countryStore.getCountries();
  }
});

const onCountryMenuOpen = async (open: any) => {
  tab.value = country.value?.code != "BD" ? "country" : "language";
  if (countryStore.countries?.length === 0 && open) {
    await countryStore.getCountries();
  }
};
</script>
