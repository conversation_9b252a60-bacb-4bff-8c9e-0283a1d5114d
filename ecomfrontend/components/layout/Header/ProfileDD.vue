<template>
  <div>
    <template v-if="!authStore.authenticated ">
      <v-menu :close-on-content-click="true">
        <template v-slot:activator="{ props }">
          <v-btn v-bind="props" icon class="custom-hover-primary header-icon-bg" size="small" variant="elevated" :elevation="0"
            color="primary" aria-label="Account" name="account" >
            <v-icon class="i-mdi-account-outline" size="18" color="white"/>
          </v-btn>
        </template>
        <v-sheet rounded="md" width="320" elevation="10" class="pa-2">
          <v-list class="py-0 theme-list" lines="two">
            <v-list-item class="py-4 px-8 custom-text-primary" :to="localePath('/auth/login')">
              <template v-slot:prepend>
                <v-icon icon="i-mdi-account-outline"/>
              </template>
              <div>
                <h6 class="text-h6 font-weight-bold custom-title mb-0">
                  {{ $t("Login") }}
                </h6>
              </div>
            </v-list-item>
            <!-- <v-list-item class="py-4 px-8 custom-text-primary" :to="localePath('/helpcenter')">
              <template v-slot:prepend>
                <v-icon icon="i-mdi-handshake"/>
              </template>
              <div>
                <h6 class="text-h6 font-weight-bold custom-title mb-0 text-capitalize">
                  {{ $t("help center") }}
                </h6>
              </div>
            </v-list-item> -->
          </v-list>
        </v-sheet>
      </v-menu>
    </template>

    <v-menu :close-on-content-click="true" v-else>
      <template v-slot:activator="{ props }">
        <v-btn class="custom-hover-primary header-icon-bg" variant="text" v-bind="props" icon>
          <v-avatar size="32">
            <NuxtImg 
              :src="
                replaceMediaDomain(user.imageUrl ?? 'https://pantoneclos3bucket.s3.ap-south-1.amazonaws.com/pantoneclo-img/user-1.jpg') ||
                user.imageUrl || 'https://pantoneclos3bucket.s3.ap-south-1.amazonaws.com/pantoneclo-img/user-1.jpg'
              "
              :alt="user?.firstName"
              loading="lazy" sizes="xs:100vw sm:100vw md:100vw"
              format="avif" width="32" height="32" class="h-100 w-100" fit="cover" quality="20"/>
          </v-avatar>
        </v-btn>
      </template>
      <v-sheet rounded="md" width="320" elevation="10">
        <div class="px-3">
          <div class="d-flex align-center mt-4 pb-3">
            <v-avatar size="45">
              <NuxtImg 
                :src="
                  replaceMediaDomain(user.imageUrl ?? 'https://pantoneclos3bucket.s3.ap-south-1.amazonaws.com/pantoneclo-img/user-1.jpg') ||
                  user.imageUrl || 'https://pantoneclos3bucket.s3.ap-south-1.amazonaws.com/pantoneclo-img/user-1.jpg'
                "
                :alt="user?.firstName"
                loading="lazy" sizes="xs:100vw sm:100vw md:100vw"
                format="avif" width="45" height="45" class="h-100 w-100" fit="cover" quality="20"/>
            </v-avatar>
            <div class="ml-3">
              <h6 class="text-h6 mb-n1">{{ user?.firstName + ' ' + user?.lastName }}</h6>

              <div class="d-flex align-center mt-1">
                <v-icon color="#000" size="18" class="i-mdi:email-outline"/>
                <span
                    class="text-subtitle-2 font-weight-regular textSecondary ml-2"
                > {{ user.email }}</span
                >
              </div>
            </div>
          </div>
          <v-divider></v-divider>
        </div>
        <v-list class="py-0 theme-list" lines="two">
          <v-list-item v-for="item in profileDD" :key="item.title"
                       class="py-4 px-8 custom-text-primary" :to="localePath(item.href)">
            <template v-slot:prepend>
              <v-icon :icon="item.icon"/>
            </template>
            <div>
              <h6 class="text-h6 font-weight-bold custom-title mb-0">
                {{ $t(item.title) }}
              </h6>
            </div>
          </v-list-item>
        </v-list>

        <div class="pt-4 pb-6 px-8 text-center">
          <v-btn color="primary" variant="outlined" block @click="logOut">
            <v-icon class="i-mdi:log-out"/>
            {{$t('Logout')}}
          </v-btn>
        </div>
      </v-sheet>
    </v-menu>
  </div>
</template>

<script setup lang="ts">
import { useMediaType } from '../../../composables/useMediaType';


const localePath = useLocalePath();

const props = defineProps<{
  authStore?: any
}>()
const { replaceMediaDomain } = useMediaType();

const profileDD = ref([
  {icon: 'i-mdi:monitor-dashboard', title: 'Dashboard', href: '/user'},
  {icon: 'i-mdi:view-list', title: 'Orders', href: '/user/orders'},
  {icon: 'i-mdi:account-cog', title: 'Settings', href: '/user/settings'},
])

const user = computed(() => props.authStore.currentUserProfile)
const logOut = () => {
  props.authStore.logUserOut()
}
</script>
