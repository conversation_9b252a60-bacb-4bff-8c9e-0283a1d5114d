import type { LineItem } from "~/types/stores/Cart";
import { useSnackbar } from '~/composables/useSnackbar';
import countryMobileCodes from "./countryMobileCodes";

const { showSnackbar } = useSnackbar();


export const getLocalizeValue = (languageId?: number, locale?: any, value?: any) => {
    if (!locale || !languageId) return value;

    const _localeTitle = Object.hasOwn(locale, languageId) ? locale[languageId] : undefined;

    return _localeTitle || value;
};

export const getLocalizeName = (languageId?: number, locale?: any, value?: any) => {
    if (!locale || !languageId) return value;
    const key = String(languageId); // 🔥 Convert to string
    const _localeTitle = Object.hasOwn(locale, key) ? locale[key]?.name : undefined;

    return _localeTitle || value;
};

export const getLocalizeDescription = (languageId?: number, locale?: any, value?: any) => {
    if (!locale || !languageId) return value;
    const key = String(languageId); // 🔥 Convert to string
    const _localeTitle = Object.hasOwn(locale, key) ? locale[key]?.description : undefined;

    return _localeTitle || value;
};

export const getOrderStatusColor = (status: any) => {

    let color = '';

    switch (status) {
        case 'PENDING':
            color = 'warning';
            break;
        case 'COMPLETED':
            color = 'success';
            break;
        default:
            color = 'primary'
    }

    return color;
}

export const findSimpleProductIndex = (productId: number, cart: LineItem[]) => {
    return cart?.findIndex((item: any) => item.productId === productId);
}
export const findSimpleProductCartIndex = (cartItem: number, cart: LineItem[]) => {
    return cart?.findIndex((item: any) => item.productId === cartItem?.productId?.id);
}

export const variableProductIndex = (item: LineItem, cart: LineItem[]) => {

    return cart?.findIndex((x: any) => {

        if (!x.color) {
            return (x.size?.id === item.size?.id) && (x.productId.id === item.productId)
        } else {
            return (x.color?.id === item.color?.id) && (x.size?.id === item.size?.id) && (x.productId.id === item.productId)
        }

    });
}

export const variableProductCartIndex = (item: LineItem, cart: LineItem[]) => {

    return cart?.findIndex((x: any) => {

        if (!x.color) {
            return (x.size?.id === item.size?.id) && (x.productId.id === item.productId.id)
        } else {
            return (x.color?.id === item.color?.id) && (x.size?.id === item.size?.id) && (x.productId.id === item.productId.id)
        }

    });
}

export const arrayToChunks = (array: any, chunkSize: number) => {
    const chunks = []
    for (let i = 0; i < array.length; i += chunkSize) {
        chunks.push(array.slice(i, i + chunkSize))
    }

    return chunks;
}

export const percentage = (partialValue: any, totalValue: any) => {
    return parseFloat(((totalValue * partialValue) / (100 + partialValue)).toFixed(2));
}

export const getPriceWithCurrency = (discountPrice: any, unitPrice: any, quantity: number, currency: any) => {

    if (parseInt(discountPrice) || parseInt(discountPrice) > 0) {

        const discountPriceWithQuantity = discountPrice * quantity;
        const unitPriceWithQuantity = unitPrice * quantity;

        return `<span class=''>${currency + numberParse(discountPriceWithQuantity)}</span>
            <span class="text-decoration-line-through text-error">${currency + numberParse(unitPriceWithQuantity)}</span>`

    } else {
        const priceWithQuantity = unitPrice * quantity;
        return `<span class="">${currency + numberParse(priceWithQuantity)}</span>`
    }

}

export function scrollTop() {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
}

export const tagLine2Image = (type: string) => {

    let image = '';

    switch (type) {
        case 'Special':
            image = `${useRuntimeConfig().public.pantonecloStaticImageUrl}special.jpg`;
            break;
        case 'Promotional':
            image = `${useRuntimeConfig().public.pantonecloStaticImageUrl}promotional.jpg`;
            // image = '/images/tagline2/promotionalss.jpg';
            break;
        case 'Fire':
            image = `${useRuntimeConfig().public.pantonecloStaticImageUrl}fire.jpg`;
            break;
        case 'Low':
            image = `${useRuntimeConfig().public.pantonecloStaticImageUrl}low.jpg`;
            break;

    }

    return image;

}

export const showSnackbarResponse = (response) => {
    if (response.isSuccess) {
        showSnackbar(response?.messasge, 'green');

    } else {
        showSnackbar(response?.messasge, 'red');
    }
}

export const numberParse = (number) => {
    return parseFloat(number).toFixed(2).toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",")
}
export const numberParseWithoutFixedDecimal = (number) => {
    // return parseFloat(number).toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",")
    const floatNumber = parseFloat(number);
    const parts = floatNumber.toString().split(".");
    
    // If decimal part exists and has only 1 digit (e.g., "59.6"), add trailing zero
    if (parts.length === 2 && parts[1].length === 1) {
        parts[1] += "0";
    }

    const final = parts.join(".");

    return final.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

export const getSelectedCountryCode = (countryId) => {
    const country = countryMobileCodes().find((country) => country.countryId === countryId)
    return country?.code
}

export const dbDateToString = (date) => {
    if (date) {
        const [day, month, year] = date?.split('/');
        const isoDateString = `${year}-${month}-${day}T00:00:00`;
        return new Date(isoDateString)?.getTime()
    }
}

export const trimText = (text, maxLength) => {
    if (text.length > maxLength) {
        return text.slice(0, maxLength) + '...';
    }
    return text;
}

export const stripHtml = (html) => {
    let tmp = document.createElement("DIV");
    tmp.innerHTML = html;
    return tmp.textContent || tmp.innerText || "";
}


export const formatChildCategoriesArr = (categories) => {
    return categories.reduce((acc, category) => {
        // Add parent category name
        const parentCategory = category.name;

        // Check if there are child categories and format them
        if (category.children && category.children.length > 0) {
            category.children.forEach(child => {
                acc.push(`${parentCategory} > ${child.name}`);
            });
        } else {
            // If no children, just add the parent category
            acc.push(parentCategory);
        }

        return acc;
    }, []);
}

export const formatCategoriesForGA = (categoryArray) => {
    if (!categoryArray?.length) return {};

    const categories = {};
    categoryArray.forEach((category, index) => {

        if (index > 4) return;

        if (index == 0) {
            categories[`item_category`] = category?.split('>')[0];
        } else {
            categories[`item_category${index + 1}`] = category?.split('>')[1];
        }
    });

    return categories;
};


export function generateRandomString(length) {
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';

    for (let i = 0; i < length; i++) {
        const randomIndex = Math.floor(Math.random() * characters.length);
        result += characters.charAt(randomIndex);
    }

    return result;
}

export function generateUniqueId(prefix = "id") {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 10000);
    return `${prefix}-${timestamp}-${random}`;
}

export const buildParams = (params) => {
    const keys = Object.keys(params);
    if (keys.length == 0) return "";
    return (
        "?" +
        keys
            .map((key) => {
                return `${key}=${params[key]}`;
            })
            .join("&")
    );
};

export const handleClearLocalStorage = () => {
    const STORAGE_CLEAR_INTERVAL = 30 * 24 * 60 * 60 * 1000;
    const STORAGE_TIMESTAMP_KEY = "storageLastClearedCartForm";    
    let lastCleared = Number(localStorage.getItem(STORAGE_TIMESTAMP_KEY));
    const currentTime = Date.now();
    if (!lastCleared || currentTime - lastCleared > STORAGE_CLEAR_INTERVAL) {
        localStorage.clear();
        localStorage.setItem(STORAGE_TIMESTAMP_KEY, String(currentTime));
    }
};

export const getDateFormat = (date: string) => {
    const dateObj = new Date(date);
    if (isNaN(dateObj.getTime())) return "";

    const year = dateObj.getFullYear();
    const month = String(dateObj.getMonth() + 1).padStart(2, '0');
    const day = String(dateObj.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
}

export async function copyToClipboard(textToCopy: any) {
  // Navigator clipboard api needs a secure context (https)
  if (navigator.clipboard && window.isSecureContext) {
    await navigator.clipboard.writeText(textToCopy);
  } else {
    // Use the 'out of viewport hidden text area' trick
    const textArea = document.createElement('textarea');
    textArea.value = textToCopy;

    // Move textarea out of the viewport so it's not visible
    textArea.style.position = 'absolute';
    textArea.style.left = '-999999px';

    document.body.prepend(textArea);
    textArea.select();

    try {
      document.execCommand('copy');
    } catch (error) {
      console.error(error);
    } finally {
      textArea.remove();
    }
  }
}
