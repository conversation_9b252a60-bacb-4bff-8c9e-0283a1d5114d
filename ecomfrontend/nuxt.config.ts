import process from "node:process";
import site from "./site";
import { Mode } from "@revolut/checkout/types/types";
const isDev = process.env.NODE_ENV === "development";

const allDomains = [
  "pantoneclo.cz",
  "pantoneclo.com",
  "pantoneclo.at",
  "pantoneclo.de",
  "pantoneclo.es",
  "pantoneclo.gr",
  "pantoneclo.hr",
  "pantoneclo.it",
  "pantoneclo.lt",
  "pantoneclo.pl",
  "pantoneclo.pt",
  "pantoneclo.ro",
  "pantoneclo.sk",
  "pantoneclo.si",
  "pantoneclo.in",
  "pantoneclo.hu",
  "pantoneclo.bg",
  "pantoneclo.com.bd",
];

const { name, description, logo, trailingSlash, titleSeparator } = site;

const defaultLocale = process.env.NUXT_PUBLIC_LOCALE || "en";

console.log("defaultLocale", defaultLocale);

export default defineNuxtConfig({
  server: {
    port: 3002,
  },

  analyze: {
    enabled: true,
    analyzerMode: 'static'
  },

  ssr: true,

  app: {
    head: {
      title: "PANTONECLO | The Tone of Trends",
      charset: "utf-8",
      viewport: "width=device-width, initial-scale=1",
      htmlAttrs: {
        lang: "en",
      },
      meta: [
        // General meta tags
        { name: "description", content: description },
        { name: "viewport", content: "width=device-width, initial-scale=1" },

        // Open Graph meta tags
        { property: "og:type", content: "website" },
        { property: "og:title", content: "PANTONECLO | Design made to fit you" },
        { property: "og:description", content: description },
        { property: "og:image", content: logo },
        { property: "og:image:width", content: "1280" },
        { property: "og:image:height", content: "572" },
        { property: "og:site_name", content: name },

        // Twitter card tags
        { name: "twitter:card", content: "summary_large_image" },
        { name: "twitter:title", content: "PANTONECLO | Design made to fit you" },
        { name: "twitter:description", content: description },
        { name: "twitter:image", content: logo },
      ],
      link: [{ rel: "icon", type: "image/x-icon", href: "/favicon_pantoneclo.jpg" }],
    },
    pageTransition: { name: "page", mode: "out-in" },
    layoutTransition: {
      name: "slide",
      mode: "out-in",
    },
  },

  css: ["~/assets/vuetify/main.scss"],

  runtimeConfig: {
    public: {
      secretKey: process.env.SECRET_KEY,
      apiUrl: process.env.API_URL,
      authApiUrl: process.env.API_AUTH_URL,
      apiBaseUrl: process.env.API_BASE_URL,
      appBaseUrl: process.env.APP_BASE_URL,
      pantonecloStaticImageUrl: process.env.PANTONECLO_STATIC_IMAGE_URL,
      stripePublicKey: process.env.STRIPE_PUBLIC_KEY,
      stripeSecretKey: process.env.STRIPE_SECRET_KEY,
      revolutMode: process.env.NUXT_PUBLIC_REVOLUT_MODE as Mode,
      // applicationDomain: process.env.NUXT_PUBLIC_APPLICATION_DOMAIN,
      appBdBaseUrl: process.env.APP_BD_BASE_URL,
      gtm_enable: process.env.GTM_ENABLE,
      gtm: {
        id: "GTM-ID",
        queryParams: {
          gtm_auth: "",
          gtm_preview: "env-1",
          gtm_cookies_win: "x",
        },
        devtools: true,
        enabled: false,
      },
      globalGtm: process.env.GLOBAL_GTM,
      globalGtmAuth: process.env.GLOBAL_GTM_AUTH,
      mediaUrl: process.env.MEDIA_URL,
      whatsappApiUrl: process.env.BASE_URL,
      whatsappApiUsername: process.env.USER_NAME,
      whatsappApiPassword: process.env.PASSWORD,
      sentryDSN: process.env.SENTRY_DSN_PUBLIC || 'https://<EMAIL>/4509353619619920',
    },
  },

  vite: {
    clearScreen: false,
    define: {
      "process.env.DEBUG": false,
    },
    vue: {
      // template: { transformAssetUrls },
      script: {
        propsDestructure: true,
      },
    },
    build: {
      target: 'esnext',
      rollupOptions: {
        output: {
          experimentalMinChunkSize: 50 * 1024,
          manualChunks(id) {
            if (id.includes('node_modules')) {
              if (id.includes('vuetify')) return 'vendor-vuetify';
              if (id.includes('vue')) return 'vendor-vue';
              if (id.includes('pinia')) return 'vendor-pinia';
              if (id.includes('swiper')) return 'vendor-swiper';
              if (id.includes('@fancyapps')) return 'vendor-fancyapps';
              if (id.includes('lodash')) return 'vendor-lodash';
              return 'vendor-others'; // fallback for other libs
            }

            if (id.includes('/pages/')) {
              const parts = id.split('/');
              const pageGroup = parts[parts.indexOf('pages') + 1];
              return `page-${pageGroup}`;
            }
          },
        },
      },
      terserOptions: {
        compress: {
          drop_console: true,
          drop_debugger: true,
        },
        output: {
          comments: true,
        },
      },
      minify: "terser",
      transpile: [],
      sourcemap: false,
    },
  },

  build: {
    transpile: ["nuxt-swiper", "@stripe/stripe-js"],
    minify: true,
    extractCSS: true,
    // terser: {
    //   // terserOptions: {
    //   //   compress: {
    //   //     drop_console: true,
    //   //   },
    //   // },
    // },
    extend(config, ctx) {
      // Run ESLint on save
      if (ctx.isDev && ctx.isClient) {
        config.module.rules.push({
          enforce: "pre",
          test: /\.(js|vue)$/,
          loader: "eslint-loader",
          exclude: /(node_modules)/,
        });
      }
    },
  },

  hooks: {
    // prevent some prefetch behaviour
    "build:manifest": (manifest) => {
      for (const key in manifest) {
        manifest[key].dynamicImports = [];

        const file = manifest[key];
        if (file.assets) {
          file.assets = file.assets.filter(
            (assetName) => !/.+\.(gif|jpe?g|png|svg|webp|avif)$/.test(assetName)
          );
        }
      }
    },
  },

  nitro: {
    compressPublicAssets: true,
    minify: true,
    preset: "node-server",
    routeRules: {
      '/event-check/**': {
        isr: 120,
        headers: {
          'Cache-Control': 's-maxage=600, stale-while-revalidate=600'
        }
      },
      '/category/**': {
        headers: {
          'Cache-Control': 's-maxage=600, stale-while-revalidate=600'
        }
      },
      '/_nuxt/**': {
        headers: {
          'Cache-Control': 's-maxage=2592000, stale-while-revalidate=2592000, immutable'
        }
      },
    },
  },

  modules: [
    "@unocss/nuxt",
    "@nuxtjs/critters",
    // "@productdevbook/chatwoot",
    "@nuxt/image",
    "@pinia/nuxt",
    "nuxt-swiper",
    "@pinia-plugin-persistedstate/nuxt",
    "vuetify-nuxt-module",
    // "@nuxtjs/seo",
    "@zadigetvoltaire/nuxt-gtm",
    "@nuxtjs/i18n",
    "nuxt-module-hotjar",
    "@sentry/nuxt/module",
  ],
  hotjar: {
    hotjarId: 5234357,
    scriptVersion: 6,
  },
  vuetify: {
    moduleOptions: {
      styles: { configFile: "assets/vuetify/settings.scss" },
      ssrClientHints: {
        viewportSize: true,
        reloadOnFirstRequest: false,
      },
    },
    vuetifyOptions: "./vuetify.config.ts",
  },
  sentry: {
    dsn: process.env.SENTRY_DSN_PUBLIC || 'https://<EMAIL>/4509353619619920',
    enabled: process.env.NODE_ENV === "offline",
    tracesSampleRate: 1.0,
    browserTracing: true,
    logErrors: true,
  },

  swiper: {
    modules: [
      "navigation",
      "pagination",
      "autoplay",
      "thumbs",
      "effect-creative",
    ],
  },

  pinia: {
    autoImports: [
      "defineStore",
      ["defineStore", "definePiniaStore", "acceptHMRUpdate"],
    ],
  },

  // piniaPersistedstate: {
  //     cookieOptions: {
  //         maxAge: 60 * 60 * 3 * 1000,
  //     },
  //     storage: 'cookies',
  // },

  imports: {
    autoImport: true,
    dirs: ["stores"],
  },

  sourcemap: { server: false, client: false },

  devtools: {
    enabled: isDev,
    timeline: {
      enabled: true,
    },
  },

  // chatwoot: {
  //   init: {
  //     websiteToken: "JRYxGqi4nf7zTaAJQ3xSK5dY",
  //   },
  //   settings: {
  //     locale: "en",
  //     position: "right",
  //     launcherTitle: "Get Help",
  //     hideMessageBubble: true,
  //     showPopoutButton: false,
  //   },
  // },

  image: {
    // format: ["webp", "jpg", "jpeg"],
    provider: "ipx",
    domains: ["cdn.pantoneclo.com"],

    presets: {
      avatar: {
        modifiers: {
          format: "webp",
          width: 80,
          height: 80,
        },
      },
    },
    placeholder: false,
  },

  compatibilityDate: "2024-07-28",

  features: {
    inlineStyles: false,
  },

  critters: {
    config: {
      // Default: 'media'
      preload: "swap",
    },
  },

  experimental: {
    cookieStore: true,
    asyncContext: true,
    sharedPrerenderData: true,
    viewTransition: true,
    renderJsonPayloads: true,
    treeshakeClientOnly: true, // removes unused client-only code
  },

  site: {
    name,
    description,
    defaultLocale,
    trailingSlash,
    titleSeparator,
  },

  robots: {
    blockNonSeoBots: true,
  },

  sitemap: {
    // https://nuxtseo.com/sitemap/guides/i18n#debugging-hreflang
    // Open {{site.url}}/sitemap.xml
    xslColumns: [
      { label: "URL", width: "50%" },
      { label: "Last Modified", select: "sitemap:lastmod", width: "12.5%" },
      { label: "Priority", select: "sitemap:priority", width: "12.5%" },
      {
        label: "Change Frequency",
        select: "sitemap:changefreq",
        width: "12.5%",
      },
      { label: "Hreflangs", select: "count(xhtml:link)", width: "12.5%" },
    ],
    // To turn off xls file when viewing sitemap.xml
    // xsl: false,
    // Remove strictNuxtContentPaths if using nuxt-content in documentDriven mode
    strictNuxtContentPaths: true,
  },

  // ogImage: {
  //   // OG images and nuxtseo features can be previewed with nuxt-devtools during development. OG images can also be viewed using URL in this form - `/__og-image__/image/<path>/og.<extension>. For eg, {{site.url}}/__og-image__/image/og.png
  //   fonts: ["Inter:400", "Inter:700"],
  //   //
  //   defaults: {
  //     width: 1200,
  //     height: 600,
  //     emojis: "noto",
  //     renderer: "satori",
  //     component: "NuxtSeo",
  //     cacheMaxAgeSeconds: 60 * 60 * 24 * 3,
  //   },
  //   //
  //   // disable at a global level
  //   runtimeCacheStorage: false,
  // },

  linkChecker: {
    enabled: false,
    report: {
      html: true,
      markdown: true,
    },
  },
  i18n: {
    // detectBrowserLanguage: {
    //   useCookie: true,
    //   cookieKey: "i18n_redirected",
    //   redirectOn: "root",
    // },
    multiDomainLocales: true,
    detectBrowserLanguage: false,
    strategy: "no_prefix",
    locales: [
      {
        name: "English",
        code: "en",
        file: "en.json",
        language: "en-US",
        languageId: 1,
        domains: allDomains,
        defaultForDomains: ["pantoneclo.com", "pantoneclo.com.bd"],
      },
      {
        name: "Bengali",
        code: "bn",
        file: "bn.json",
        language: "bn-BD",
        languageId: 2,
        domains: allDomains,
      },

      {
        name: "German",
        code: "de",
        file: "de.json",
        language: "de-DE",
        languageId: 3,
        domains: allDomains,
        defaultForDomains: ["pantoneclo.de"],
      },
      {
        name: "Bulgarian",
        code: "bg",
        file: "bg.json",
        language: "bg-BG",
        languageId: 4,
        domains: allDomains,
        defaultForDomains: ['pantoneclo.bg']
      },
      {
        name: "Czech",
        code: "cz",
        file: "cz.json",
        language: "cs-CZ",
        languageId: 5,
        domains: allDomains,
        defaultForDomains: ["pantoneclo.cz"],
      },
      {
        name: "Greek",
        code: "el",
        file: "el.json",
        language: "el-GR",
        languageId: 6,
        domains: allDomains,
        defaultForDomains: ["pantoneclo.gr"],
      },
      {
        name: "Spanish",
        code: "es",
        file: "es.json",
        language: "es-ES",
        languageId: 7,
        domains: allDomains,
        defaultForDomains: ["pantoneclo.es"],
      },
      {
        name: "Croatian",
        code: "hr",
        file: "hr.json",
        language: "hr-HR",
        languageId: 8,
        domains: allDomains,
        defaultForDomains: ["pantoneclo.hr"],
      },
      {
        name: "Hungarian",
        code: "hu",
        file: "hu.json",
        language: "hu-HU",
        languageId: 9,
        domains: allDomains,
        defaultForDomains: ["pantoneclo.hu"],
      },
      {
        name: "Italian",
        code: "it",
        file: "it.json",
        language: "it-IT",
        languageId: 10,
        domains: allDomains,
        defaultForDomains: ["pantoneclo.it"],
      },
      {
        name: "Lithuanian",
        code: "lt",
        file: "lt.json",
        language: "lt-LT",
        languageId: 11,
        domains: allDomains,
        defaultForDomains: ["pantoneclo.lt"],
      },
      {
        name: "Polish",
        code: "pl",
        file: "pl.json",
        language: "pl-PL",
        languageId: 12,
        domains: allDomains,
        defaultForDomains: ["pantoneclo.pl"],
      },
      {
        name: "Portuguese",
        code: "pt",
        file: "pt.json",
        language: "pt-PT",
        languageId: 13,
        domains: allDomains,
        defaultForDomains: ["pantoneclo.pt"],
      },
      {
        name: "Romanian",
        code: "ro",
        file: "ro.json",
        language: "ro-RO",
        languageId: 14,
        domains: allDomains,
        defaultForDomains: ["pantoneclo.ro"],
      },
      {
        name: "Slovak",
        code: "sk",
        file: "sk.json",
        language: "sk-SK",
        languageId: 15,
        domains: allDomains,
        defaultForDomains: ["pantoneclo.sk"],
      },
      {
        name: "Slovenian",
        code: "sl",
        file: "sl.json",
        language: "sl-SI",
        languageId: 16,
        domains: allDomains,
        defaultForDomains: ["pantoneclo.si"],
      },
      {
        name: "Austrian",
        code: "at",
        file: "at.json",
        language: "at",
        languageId: 17,
        domains: allDomains,
        defaultForDomains: ["pantoneclo.at"],
      },
    ],
    fallbackLocale: defaultLocale,
    lazy: true,
    langDir: "lang",
  },
});
